<?php
/**
 * Comprehensive Guzzle Diagnostic Script
 * This will help identify why Guz<PERSON> is not working on the server
 */

echo "<h1>Comprehensive Guzzle Diagnostic</h1>";
echo "<p>Diagnosing Guzzle HTTP client issues on the server...</p>";

// Test 1: Environment Information
echo "<h2>1. Environment Information</h2>";
echo "PHP Version: " . PHP_VERSION . "<br>";
echo "Operating System: " . PHP_OS . "<br>";
echo "Server Software: " . ($_SERVER['SERVER_SOFTWARE'] ?? 'Unknown') . "<br>";
echo "Document Root: " . ($_SERVER['DOCUMENT_ROOT'] ?? 'Unknown') . "<br>";
echo "Script Path: " . __FILE__ . "<br>";
echo "Current Working Directory: " . getcwd() . "<br>";

// Test 2: Check PHP Extensions
echo "<h2>2. PHP Extensions Check</h2>";
$required_extensions = ['curl', 'json', 'mbstring', 'openssl'];
foreach ($required_extensions as $ext) {
    if (extension_loaded($ext)) {
        echo "✅ $ext extension loaded<br>";
    } else {
        echo "❌ $ext extension NOT loaded<br>";
    }
}

// Test 3: File System Check
echo "<h2>3. File System Check</h2>";
$files_to_check = [
    'composer.json',
    'vendor/autoload.php',
    'vendor/composer/autoload_real.php',
    'vendor/composer/autoload_psr4.php',
    'vendor/guzzlehttp/guzzle/composer.json',
    'vendor/guzzlehttp/guzzle/src/Client.php'
];

foreach ($files_to_check as $file) {
    if (file_exists($file)) {
        $size = filesize($file);
        $perms = substr(sprintf('%o', fileperms($file)), -4);
        echo "✅ $file exists (size: {$size}B, perms: $perms)<br>";
    } else {
        echo "❌ $file missing<br>";
    }
}

// Test 4: Composer Configuration Check
echo "<h2>4. Composer Configuration</h2>";
if (file_exists('composer.json')) {
    $composer_content = file_get_contents('composer.json');
    $composer_data = json_decode($composer_content, true);
    
    if ($composer_data) {
        echo "✅ composer.json is valid<br>";
        echo "Project name: " . ($composer_data['name'] ?? 'Not set') . "<br>";
        
        if (isset($composer_data['require']['guzzlehttp/guzzle'])) {
            echo "✅ Guzzle requirement: " . $composer_data['require']['guzzlehttp/guzzle'] . "<br>";
        } else {
            echo "❌ Guzzle not in requirements<br>";
        }
    } else {
        echo "❌ composer.json is invalid JSON<br>";
    }
}

// Test 5: Autoloader Test (Step by Step)
echo "<h2>5. Autoloader Test (Step by Step)</h2>";

// Step 5a: Check if autoload.php exists and is readable
if (file_exists('vendor/autoload.php') && is_readable('vendor/autoload.php')) {
    echo "✅ vendor/autoload.php exists and is readable<br>";
    
    // Step 5b: Try to include it
    try {
        ob_start();
        $autoloader = require_once 'vendor/autoload.php';
        $output = ob_get_clean();
        
        if ($output) {
            echo "⚠️ Autoloader produced output: " . htmlspecialchars($output) . "<br>";
        } else {
            echo "✅ Autoloader included without errors<br>";
        }
        
        if ($autoloader) {
            echo "✅ Autoloader returned object: " . get_class($autoloader) . "<br>";
        } else {
            echo "❌ Autoloader did not return object<br>";
        }
        
    } catch (Exception $e) {
        echo "❌ Exception including autoloader: " . $e->getMessage() . "<br>";
    } catch (Error $e) {
        echo "❌ Fatal error including autoloader: " . $e->getMessage() . "<br>";
    }
} else {
    echo "❌ vendor/autoload.php not accessible<br>";
}

// Test 6: Class Loading Test
echo "<h2>6. Class Loading Test</h2>";

// Check if class exists
if (class_exists('GuzzleHttp\Client')) {
    echo "✅ GuzzleHttp\Client class exists<br>";
} else {
    echo "❌ GuzzleHttp\Client class does not exist<br>";
    
    // Try to manually load the class
    $client_file = 'vendor/guzzlehttp/guzzle/src/Client.php';
    if (file_exists($client_file)) {
        echo "Attempting manual class loading...<br>";
        try {
            require_once $client_file;
            echo "✅ Manually loaded Client.php<br>";
        } catch (Exception $e) {
            echo "❌ Error manually loading Client.php: " . $e->getMessage() . "<br>";
        }
    }
}

// Test 7: PSR-4 Autoloading Check
echo "<h2>7. PSR-4 Autoloading Check</h2>";
if (file_exists('vendor/composer/autoload_psr4.php')) {
    $psr4_map = require 'vendor/composer/autoload_psr4.php';
    
    if (isset($psr4_map['GuzzleHttp\\'])) {
        echo "✅ GuzzleHttp namespace mapped to: " . implode(', ', $psr4_map['GuzzleHttp\\']) . "<br>";
    } else {
        echo "❌ GuzzleHttp namespace not found in PSR-4 map<br>";
        echo "Available namespaces: " . implode(', ', array_keys($psr4_map)) . "<br>";
    }
} else {
    echo "❌ autoload_psr4.php not found<br>";
}

// Test 8: Try Creating Guzzle Client
echo "<h2>8. Guzzle Client Creation Test</h2>";
if (class_exists('GuzzleHttp\Client')) {
    try {
        $client = new GuzzleHttp\Client(['timeout' => 10]);
        echo "✅ Successfully created GuzzleHttp\Client instance<br>";
        echo "Client class: " . get_class($client) . "<br>";
        
        // Test a simple request
        try {
            $response = $client->request('GET', 'https://httpbin.org/get', [
                'timeout' => 5,
                'http_errors' => false
            ]);
            echo "✅ Test HTTP request successful (status: " . $response->getStatusCode() . ")<br>";
        } catch (Exception $e) {
            echo "⚠️ Test HTTP request failed: " . $e->getMessage() . "<br>";
        }
        
    } catch (Exception $e) {
        echo "❌ Error creating Guzzle client: " . $e->getMessage() . "<br>";
    } catch (Error $e) {
        echo "❌ Fatal error creating Guzzle client: " . $e->getMessage() . "<br>";
    }
} else {
    echo "❌ Cannot test client creation - class not available<br>";
}

// Test 9: Memory and Resource Limits
echo "<h2>9. PHP Configuration</h2>";
echo "Memory Limit: " . ini_get('memory_limit') . "<br>";
echo "Max Execution Time: " . ini_get('max_execution_time') . "<br>";
echo "Error Reporting: " . ini_get('error_reporting') . "<br>";
echo "Display Errors: " . (ini_get('display_errors') ? 'On' : 'Off') . "<br>";
echo "Log Errors: " . (ini_get('log_errors') ? 'On' : 'Off') . "<br>";
echo "Error Log: " . ini_get('error_log') . "<br>";

// Test 10: Check for any PHP errors
echo "<h2>10. Error Log Check</h2>";
$error_log = ini_get('error_log');
if ($error_log && file_exists($error_log)) {
    $lines = file($error_log);
    $recent_lines = array_slice($lines, -10);
    echo "Recent PHP errors:<br>";
    echo "<pre style='background: #f5f5f5; padding: 10px; max-height: 200px; overflow-y: auto;'>";
    foreach ($recent_lines as $line) {
        echo htmlspecialchars($line);
    }
    echo "</pre>";
} else {
    echo "No error log found or accessible<br>";
}

echo "<h2>Summary</h2>";
echo "<p>This diagnostic should help identify why Guzzle is not working. Look for any ❌ marks above.</p>";
echo "<p>Common issues:</p>";
echo "<ul>";
echo "<li>Missing PHP extensions (curl, openssl)</li>";
echo "<li>File permission issues</li>";
echo "<li>Incomplete vendor directory</li>";
echo "<li>PHP version compatibility</li>";
echo "<li>Memory or execution time limits</li>";
echo "</ul>";
?>
