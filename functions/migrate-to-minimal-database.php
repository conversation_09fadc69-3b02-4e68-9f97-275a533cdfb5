<?php
/**
 * Database Migration Script: Transition to Minimal Customer Data Structure
 * 
 * This script helps migrate from the current database structure to the minimal approach:
 * - Keeps only essential columns in user table
 * - Backs up data that will be moved to Appika API
 * - Provides rollback capability
 */

echo "<h1>🔄 Database Migration: Minimal Customer Data Structure</h1>";

include_once(__DIR__ . '/server.php');

$action = $_GET['action'] ?? 'preview';

// Define the minimal columns to keep
$minimal_columns = [
    'id',
    'appika_id', 
    'appika_customer_id',
    'username',
    'email',
    'password',
    'premium_tickets',
    'ultimate_tickets', 
    'registration_time',
    'timezone',
    'stripe_customer_id'
];

// Get current table structure
function getCurrentTableStructure($conn) {
    $stmt = $conn->prepare("SHOW COLUMNS FROM user");
    $stmt->execute();
    $result = $stmt->get_result();
    
    $columns = [];
    while ($row = $result->fetch_assoc()) {
        $columns[] = [
            'Field' => $row['Field'],
            'Type' => $row['Type'],
            'Null' => $row['Null'],
            'Key' => $row['Key'],
            'Default' => $row['Default'],
            'Extra' => $row['Extra']
        ];
    }
    return $columns;
}

// Get columns that will be removed
function getColumnsToRemove($currentColumns, $minimalColumns) {
    $currentColumnNames = array_column($currentColumns, 'Field');
    return array_diff($currentColumnNames, $minimalColumns);
}

// Create backup table
function createBackupTable($conn) {
    $timestamp = date('Y_m_d_H_i_s');
    $backupTableName = "user_backup_" . $timestamp;
    
    $sql = "CREATE TABLE $backupTableName AS SELECT * FROM user";
    if ($conn->query($sql)) {
        return $backupTableName;
    }
    return false;
}

// Export data that will be removed
function exportRemovedData($conn, $columnsToRemove) {
    if (empty($columnsToRemove)) {
        return null;
    }
    
    $selectColumns = array_merge(['id', 'username'], $columnsToRemove);
    $sql = "SELECT " . implode(', ', $selectColumns) . " FROM user";
    
    $result = $conn->query($sql);
    $data = [];
    
    while ($row = $result->fetch_assoc()) {
        $data[] = $row;
    }
    
    return $data;
}

// Current table analysis
$currentColumns = getCurrentTableStructure($conn);
$columnsToRemove = getColumnsToRemove($currentColumns, $minimal_columns);

echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
echo "<h2>📊 Current Database Analysis</h2>";

echo "<h3>Current Columns (" . count($currentColumns) . "):</h3>";
echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px;'>";
foreach ($currentColumns as $column) {
    $isMinimal = in_array($column['Field'], $minimal_columns);
    $bgColor = $isMinimal ? '#d4edda' : '#f8d7da';
    $textColor = $isMinimal ? '#155724' : '#721c24';
    
    echo "<div style='background: $bgColor; color: $textColor; padding: 8px; border-radius: 3px; font-size: 0.9em;'>";
    echo "<strong>" . htmlspecialchars($column['Field']) . "</strong><br>";
    echo "<small>" . htmlspecialchars($column['Type']) . "</small>";
    if ($isMinimal) {
        echo "<br><span style='font-size: 0.8em;'>✅ Keep</span>";
    } else {
        echo "<br><span style='font-size: 0.8em;'>❌ Remove</span>";
    }
    echo "</div>";
}
echo "</div>";

echo "<h3>Migration Summary:</h3>";
echo "<ul>";
echo "<li><strong>Columns to keep:</strong> " . count($minimal_columns) . " (" . implode(', ', $minimal_columns) . ")</li>";
echo "<li><strong>Columns to remove:</strong> " . count($columnsToRemove) . " (" . implode(', ', $columnsToRemove) . ")</li>";
echo "</ul>";

if (!empty($columnsToRemove)) {
    echo "<div style='background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    echo "<strong>⚠️ Warning:</strong> The following columns will be removed and their data will be lost from the local database:";
    echo "<ul>";
    foreach ($columnsToRemove as $column) {
        echo "<li>" . htmlspecialchars($column) . "</li>";
    }
    echo "</ul>";
    echo "<p>This data should be available through Appika API instead.</p>";
    echo "</div>";
}

echo "</div>";

// Action buttons
echo "<div style='margin: 20px 0;'>";
echo "<h2>🔧 Migration Actions</h2>";

if ($action === 'preview') {
    echo "<p>Choose an action below:</p>";
    echo "<a href='?action=backup' class='btn' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>1. Create Backup</a>";
    echo "<a href='?action=export' class='btn' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>2. Export Data</a>";
    echo "<a href='?action=migrate' class='btn' style='background: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>3. Migrate (Remove Columns)</a>";
    echo "<a href='?action=verify' class='btn' style='background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>4. Verify Migration</a>";
}

// Handle actions
switch ($action) {
    case 'backup':
        echo "<h3>📦 Creating Backup</h3>";
        $backupTable = createBackupTable($conn);
        if ($backupTable) {
            echo "<div style='color: green; padding: 10px; background: #d4edda; border-radius: 5px;'>";
            echo "✅ <strong>Backup created successfully!</strong><br>";
            echo "Backup table name: <code>$backupTable</code><br>";
            echo "You can restore data from this table if needed.";
            echo "</div>";
        } else {
            echo "<div style='color: red; padding: 10px; background: #f8d7da; border-radius: 5px;'>";
            echo "❌ <strong>Failed to create backup!</strong><br>";
            echo "Error: " . htmlspecialchars($conn->error);
            echo "</div>";
        }
        break;
        
    case 'export':
        echo "<h3>📤 Exporting Data</h3>";
        if (!empty($columnsToRemove)) {
            $exportData = exportRemovedData($conn, $columnsToRemove);
            
            // Save to JSON file
            $filename = 'user_data_export_' . date('Y_m_d_H_i_s') . '.json';
            $filepath = __DIR__ . '/../exports/' . $filename;
            
            // Create exports directory if it doesn't exist
            if (!file_exists(__DIR__ . '/../exports/')) {
                mkdir(__DIR__ . '/../exports/', 0755, true);
            }
            
            if (file_put_contents($filepath, json_encode($exportData, JSON_PRETTY_PRINT))) {
                echo "<div style='color: green; padding: 10px; background: #d4edda; border-radius: 5px;'>";
                echo "✅ <strong>Data exported successfully!</strong><br>";
                echo "Export file: <code>$filename</code><br>";
                echo "Records exported: " . count($exportData) . "<br>";
                echo "Columns exported: " . implode(', ', $columnsToRemove);
                echo "</div>";
                
                // Show sample data
                if (!empty($exportData)) {
                    echo "<h4>Sample Exported Data (first 3 records):</h4>";
                    echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto;'>";
                    echo htmlspecialchars(json_encode(array_slice($exportData, 0, 3), JSON_PRETTY_PRINT));
                    echo "</pre>";
                }
            } else {
                echo "<div style='color: red; padding: 10px; background: #f8d7da; border-radius: 5px;'>";
                echo "❌ <strong>Failed to export data!</strong>";
                echo "</div>";
            }
        } else {
            echo "<div style='color: blue; padding: 10px; background: #cce7ff; border-radius: 5px;'>";
            echo "ℹ️ <strong>No data to export.</strong><br>";
            echo "All current columns are part of the minimal structure.";
            echo "</div>";
        }
        break;
        
    case 'migrate':
        echo "<h3>🚀 Performing Migration</h3>";
        
        if (empty($columnsToRemove)) {
            echo "<div style='color: blue; padding: 10px; background: #cce7ff; border-radius: 5px;'>";
            echo "ℹ️ <strong>No migration needed.</strong><br>";
            echo "Database already has the minimal structure.";
            echo "</div>";
        } else {
            echo "<div style='color: red; padding: 10px; background: #f8d7da; border-radius: 5px; margin-bottom: 15px;'>";
            echo "⚠️ <strong>WARNING: This will permanently remove columns and their data!</strong><br>";
            echo "Make sure you have created a backup and exported the data first.";
            echo "</div>";
            
            $confirm = $_GET['confirm'] ?? '';
            if ($confirm !== 'yes') {
                echo "<p>To proceed with the migration, click the button below:</p>";
                echo "<a href='?action=migrate&confirm=yes' style='background: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;' onclick='return confirm(\"Are you sure you want to remove these columns? This cannot be undone!\")'>⚠️ Confirm Migration</a>";
            } else {
                // Perform the actual migration
                $errors = [];
                foreach ($columnsToRemove as $column) {
                    $sql = "ALTER TABLE user DROP COLUMN `$column`";
                    if (!$conn->query($sql)) {
                        $errors[] = "Failed to drop column '$column': " . $conn->error;
                    }
                }
                
                if (empty($errors)) {
                    echo "<div style='color: green; padding: 10px; background: #d4edda; border-radius: 5px;'>";
                    echo "✅ <strong>Migration completed successfully!</strong><br>";
                    echo "Removed columns: " . implode(', ', $columnsToRemove) . "<br>";
                    echo "Your database now has the minimal structure.";
                    echo "</div>";
                } else {
                    echo "<div style='color: red; padding: 10px; background: #f8d7da; border-radius: 5px;'>";
                    echo "❌ <strong>Migration failed!</strong><br>";
                    echo "<ul>";
                    foreach ($errors as $error) {
                        echo "<li>" . htmlspecialchars($error) . "</li>";
                    }
                    echo "</ul>";
                    echo "</div>";
                }
            }
        }
        break;
        
    case 'verify':
        echo "<h3>✅ Verifying Migration</h3>";
        $newColumns = getCurrentTableStructure($conn);
        $newColumnNames = array_column($newColumns, 'Field');
        
        $missing = array_diff($minimal_columns, $newColumnNames);
        $extra = array_diff($newColumnNames, $minimal_columns);
        
        if (empty($missing) && empty($extra)) {
            echo "<div style='color: green; padding: 10px; background: #d4edda; border-radius: 5px;'>";
            echo "✅ <strong>Migration verification successful!</strong><br>";
            echo "Database structure matches the minimal requirements perfectly.";
            echo "</div>";
        } else {
            echo "<div style='color: orange; padding: 10px; background: #fff3cd; border-radius: 5px;'>";
            echo "⚠️ <strong>Migration verification issues found:</strong><br>";
            if (!empty($missing)) {
                echo "Missing required columns: " . implode(', ', $missing) . "<br>";
            }
            if (!empty($extra)) {
                echo "Extra columns (not in minimal spec): " . implode(', ', $extra) . "<br>";
            }
            echo "</div>";
        }
        
        echo "<h4>Current Table Structure:</h4>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f0f0f0;'><th>Column</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
        foreach ($newColumns as $column) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($column['Field']) . "</td>";
            echo "<td>" . htmlspecialchars($column['Type']) . "</td>";
            echo "<td>" . htmlspecialchars($column['Null']) . "</td>";
            echo "<td>" . htmlspecialchars($column['Key']) . "</td>";
            echo "<td>" . htmlspecialchars($column['Default']) . "</td>";
            echo "<td>" . htmlspecialchars($column['Extra']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        break;
}

echo "</div>";

echo "<hr>";
echo "<h3>📚 Migration Guide:</h3>";
echo "<ol>";
echo "<li><strong>Create Backup:</strong> Always create a backup before making changes</li>";
echo "<li><strong>Export Data:</strong> Export data from columns that will be removed</li>";
echo "<li><strong>Test Integration:</strong> Test the new customer data service functions</li>";
echo "<li><strong>Migrate:</strong> Remove unnecessary columns from the database</li>";
echo "<li><strong>Verify:</strong> Confirm the migration was successful</li>";
echo "<li><strong>Update Code:</strong> Update your application to use the new minimal approach</li>";
echo "</ol>";

echo "<style>";
echo "body { font-family: Arial, sans-serif; margin: 20px; }";
echo "h1, h2, h3 { color: #473BF0; }";
echo "code { background: #f8f9fa; padding: 2px 4px; border-radius: 3px; }";
echo "table { margin: 10px 0; }";
echo "th, td { padding: 8px; text-align: left; }";
echo "</style>";
?>
