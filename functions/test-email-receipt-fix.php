<?php
/**
 * Test Email Receipt Function After last_name Column Fix
 * This script tests if the email receipt function works after removing last_name column
 */

echo "<h2>📧 Email Receipt Function Test (After last_name Fix)</h2>";

// Include required files
include_once(__DIR__ . '/server.php');
include_once(__DIR__ . '/send-purchase-receipt.php');

// Test email address
$test_email = isset($_GET['email']) ? $_GET['email'] : '<EMAIL>';
$transaction_id = isset($_GET['transaction_id']) ? $_GET['transaction_id'] : '';

echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin-bottom: 20px;'>";
echo "<h3>🔧 Test Configuration</h3>";
echo "<p><strong>Test Email:</strong> " . htmlspecialchars($test_email) . "</p>";
echo "<p><strong>Transaction ID:</strong> " . htmlspecialchars($transaction_id) . "</p>";
echo "</div>";

// Test 1: Check if send-purchase-receipt.php loads without errors
echo "<h3>✅ Test 1: Function Loading</h3>";
try {
    if (function_exists('sendPurchaseReceiptEmail')) {
        echo "<div style='color: green; padding: 10px; border: 1px solid green; background: #f0fff0;'>";
        echo "✅ <strong>sendPurchaseReceiptEmail function is available</strong>";
        echo "</div>";
    } else {
        echo "<div style='color: red; padding: 10px; border: 1px solid red; background: #fff0f0;'>";
        echo "❌ <strong>sendPurchaseReceiptEmail function is NOT available</strong>";
        echo "</div>";
    }
} catch (Exception $e) {
    echo "<div style='color: red; padding: 10px; border: 1px solid red; background: #fff0f0;'>";
    echo "❌ <strong>Error loading function:</strong> " . htmlspecialchars($e->getMessage());
    echo "</div>";
}

// Test 2: Check database query (without last_name)
echo "<h3>✅ Test 2: Database Query Test</h3>";
try {
    $stmt = $conn->prepare("
        SELECT pt.*, u.first_name, u.email, u.company_name, u.address, u.city, u.state, u.country
        FROM purchasetickets pt
        LEFT JOIN user u ON pt.username = u.username
        WHERE pt.transactionid IS NOT NULL
        LIMIT 1
    ");
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        echo "<div style='color: green; padding: 10px; border: 1px solid green; background: #f0fff0;'>";
        echo "✅ <strong>Database query works correctly (no last_name column)</strong>";
        echo "</div>";
        
        $row = $result->fetch_assoc();
        echo "<p><strong>Sample data:</strong></p>";
        echo "<ul>";
        echo "<li>Transaction ID: " . htmlspecialchars($row['transactionid'] ?? 'N/A') . "</li>";
        echo "<li>Customer Name: " . htmlspecialchars($row['first_name'] ?? 'N/A') . "</li>";
        echo "<li>Email: " . htmlspecialchars($row['email'] ?? 'N/A') . "</li>";
        echo "</ul>";
    } else {
        echo "<div style='color: orange; padding: 10px; border: 1px solid orange; background: #fff8e1;'>";
        echo "⚠️ <strong>No purchase data found in database</strong>";
        echo "</div>";
    }
} catch (Exception $e) {
    echo "<div style='color: red; padding: 10px; border: 1px solid red; background: #fff0f0;'>";
    echo "❌ <strong>Database query error:</strong> " . htmlspecialchars($e->getMessage());
    echo "</div>";
}

// Test 3: Test actual email sending (if transaction ID provided)
if (!empty($transaction_id)) {
    echo "<h3>✅ Test 3: Email Sending Test</h3>";
    try {
        $result = sendPurchaseReceiptEmail($transaction_id, $test_email, false, null);
        
        if ($result) {
            echo "<div style='color: green; padding: 10px; border: 1px solid green; background: #f0fff0;'>";
            echo "✅ <strong>Email sent successfully!</strong>";
            echo "<p>Check your email inbox for the receipt.</p>";
            echo "</div>";
        } else {
            echo "<div style='color: red; padding: 10px; border: 1px solid red; background: #fff0f0;'>";
            echo "❌ <strong>Email sending failed</strong>";
            echo "<p>Check server logs for more details.</p>";
            echo "</div>";
        }
    } catch (Exception $e) {
        echo "<div style='color: red; padding: 10px; border: 1px solid red; background: #fff0f0;'>";
        echo "❌ <strong>Email sending error:</strong> " . htmlspecialchars($e->getMessage());
        echo "</div>";
    }
}

// Show recent transactions for testing
echo "<h3>📋 Available Transactions for Testing</h3>";

try {
    $stmt = $conn->prepare("
        SELECT DISTINCT pt.transactionid, pt.purchase_time, u.email, u.first_name
        FROM purchasetickets pt 
        LEFT JOIN user u ON pt.username = u.username 
        WHERE pt.transactionid IS NOT NULL 
        ORDER BY pt.purchase_time DESC 
        LIMIT 5
    ");
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f0f0f0;'>";
        echo "<th style='padding: 8px;'>Transaction ID</th>";
        echo "<th style='padding: 8px;'>Date</th>";
        echo "<th style='padding: 8px;'>Customer</th>";
        echo "<th style='padding: 8px;'>Email</th>";
        echo "<th style='padding: 8px;'>Test</th>";
        echo "</tr>";
        
        while ($row = $result->fetch_assoc()) {
            $customer_name = trim($row['first_name'] ?? '');
            if (empty($customer_name)) {
                $customer_name = 'N/A';
            }
            
            echo "<tr>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($row['transactionid']) . "</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($row['purchase_time']) . "</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($customer_name) . "</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($row['email'] ?? 'N/A') . "</td>";
            echo "<td style='padding: 8px;'>";
            echo "<a href='?transaction_id=" . urlencode($row['transactionid']) . "&email=" . urlencode($row['email'] ?? $test_email) . "' style='color: blue;'>Test This</a>";
            echo "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>No transactions found in database.</p>";
    }
} catch (Exception $e) {
    echo "<div style='color: red; padding: 10px; border: 1px solid red; background: #fff0f0;'>";
    echo "❌ <strong>Error fetching transactions:</strong> " . htmlspecialchars($e->getMessage());
    echo "</div>";
}

echo "<hr>";
echo "<h3>🔧 Manual Test Form</h3>";
echo "<form method='GET'>";
echo "<p>";
echo "<label>Email Address: </label>";
echo "<input type='email' name='email' value='" . htmlspecialchars($test_email) . "' required>";
echo "</p>";
echo "<p>";
echo "<label>Transaction ID: </label>";
echo "<input type='text' name='transaction_id' value='" . htmlspecialchars($transaction_id) . "' placeholder='Enter transaction ID'>";
echo "</p>";
echo "<p>";
echo "<button type='submit'>Test Email Receipt</button>";
echo "</p>";
echo "</form>";

echo "<style>";
echo "body { font-family: Arial, sans-serif; margin: 20px; }";
echo "h1, h2, h3 { color: #473BF0; }";
echo "table { margin: 10px 0; }";
echo "form { background: #f8f9fa; padding: 15px; border-radius: 5px; }";
echo "input, button { padding: 8px; margin: 5px; }";
echo "</style>";
?>
