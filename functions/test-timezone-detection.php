<?php
/**
 * Test Timezone Detection
 * Tests the timezone detection functionality for different countries
 */

require_once('timezone-detection.php');

echo "<h1>🌍 Test Timezone Detection</h1>";

// Test countries
$testCountries = [
    'TH' => 'Thailand',
    'JP' => 'Japan', 
    'US' => 'United States',
    'GB' => 'United Kingdom',
    'DE' => 'Germany',
    'SG' => 'Singapore',
    'AU' => 'Australia',
    'IN' => 'India',
    'BR' => 'Brazil',
    'CA' => 'Canada',
    'FR' => 'France',
    'IT' => 'Italy',
    'KR' => 'South Korea',
    'CN' => 'China',
    'MY' => 'Malaysia',
    'XX' => 'Unknown Country' // Test fallback
];

echo "<h2>🔍 Timezone Detection Results</h2>";

echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 20px 0;'>";
echo "<tr style='background: #f8f9fa;'>";
echo "<th style='padding: 12px; text-align: left;'>Country Code</th>";
echo "<th style='padding: 12px; text-align: left;'>Country Name</th>";
echo "<th style='padding: 12px; text-align: left;'>Detected Timezone</th>";
echo "<th style='padding: 12px; text-align: left;'>Display Name</th>";
echo "<th style='padding: 12px; text-align: left;'>Current Time</th>";
echo "<th style='padding: 12px; text-align: left;'>Valid?</th>";
echo "</tr>";

foreach ($testCountries as $countryCode => $expectedName) {
    $detectedTimezone = getTimezoneByCountry($countryCode);
    $displayName = getTimezoneDisplayName($detectedTimezone);
    $countryName = getCountryName($countryCode);
    $currentTime = getCurrentTimeInTimezone($detectedTimezone);
    $isValid = isValidTimezone($detectedTimezone);
    
    $validIcon = $isValid ? '✅' : '❌';
    $rowColor = $isValid ? '#d4edda' : '#f8d7da';
    
    echo "<tr style='background: $rowColor;'>";
    echo "<td style='padding: 8px;'><strong>$countryCode</strong></td>";
    echo "<td style='padding: 8px;'>" . htmlspecialchars($countryName) . "</td>";
    echo "<td style='padding: 8px;'><code>" . htmlspecialchars($detectedTimezone) . "</code></td>";
    echo "<td style='padding: 8px;'>" . htmlspecialchars($displayName) . "</td>";
    echo "<td style='padding: 8px;'>" . htmlspecialchars($currentTime) . "</td>";
    echo "<td style='padding: 8px;'>$validIcon $isValid</td>";
    echo "</tr>";
}

echo "</table>";

// Test specific scenarios
echo "<h2>🧪 Specific Test Scenarios</h2>";

echo "<div style='background: #e3f2fd; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
echo "<h3>Test Case 1: Japan Purchase</h3>";
echo "<p><strong>Country:</strong> JP (Japan)</p>";
echo "<p><strong>Expected Timezone:</strong> Asia/Tokyo</p>";
echo "<p><strong>Detected Timezone:</strong> " . getTimezoneByCountry('JP') . "</p>";
echo "<p><strong>Current Time in Japan:</strong> " . getCurrentTimeInTimezone(getTimezoneByCountry('JP')) . "</p>";
echo "<p><strong>Status:</strong> " . (getTimezoneByCountry('JP') === 'Asia/Tokyo' ? '✅ Correct' : '❌ Incorrect') . "</p>";
echo "</div>";

echo "<div style='background: #e8f5e8; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
echo "<h3>Test Case 2: Thailand Purchase</h3>";
echo "<p><strong>Country:</strong> TH (Thailand)</p>";
echo "<p><strong>Expected Timezone:</strong> Asia/Bangkok</p>";
echo "<p><strong>Detected Timezone:</strong> " . getTimezoneByCountry('TH') . "</p>";
echo "<p><strong>Current Time in Thailand:</strong> " . getCurrentTimeInTimezone(getTimezoneByCountry('TH')) . "</p>";
echo "<p><strong>Status:</strong> " . (getTimezoneByCountry('TH') === 'Asia/Bangkok' ? '✅ Correct' : '❌ Incorrect') . "</p>";
echo "</div>";

echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
echo "<h3>Test Case 3: Unknown Country</h3>";
echo "<p><strong>Country:</strong> XX (Unknown)</p>";
echo "<p><strong>Expected Timezone:</strong> UTC (fallback)</p>";
echo "<p><strong>Detected Timezone:</strong> " . getTimezoneByCountry('XX') . "</p>";
echo "<p><strong>Current Time in UTC:</strong> " . getCurrentTimeInTimezone(getTimezoneByCountry('XX')) . "</p>";
echo "<p><strong>Status:</strong> " . (getTimezoneByCountry('XX') === 'UTC' ? '✅ Correct Fallback' : '❌ Incorrect Fallback') . "</p>";
echo "</div>";

// Test payment simulation
echo "<h2>💳 Payment Success Simulation</h2>";

echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
echo "<h3>Simulating Payment from Different Countries</h3>";

$simulationCountries = ['TH', 'JP', 'US', 'GB', 'DE'];

foreach ($simulationCountries as $country) {
    $timezone = getTimezoneByCountry($country);
    $countryName = getCountryName($country);
    $displayName = getTimezoneDisplayName($timezone);
    
    echo "<div style='border: 1px solid #ddd; padding: 10px; margin: 10px 0; border-radius: 4px;'>";
    echo "<h4>🛒 Purchase from $countryName ($country)</h4>";
    echo "<p><strong>Customer Data:</strong></p>";
    echo "<ul>";
    echo "<li>Country: $country</li>";
    echo "<li>Detected Timezone: $timezone</li>";
    echo "<li>Display Name: $displayName</li>";
    echo "<li>Registration Time: " . getCurrentTimeInTimezone($timezone) . "</li>";
    echo "</ul>";
    echo "</div>";
}
echo "</div>";

echo "<h2>📋 Summary</h2>";
echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
echo "<h3>✅ Timezone Detection Features</h3>";
echo "<ul>";
echo "<li><strong>Auto-Detection:</strong> Automatically detects timezone based on country code</li>";
echo "<li><strong>Comprehensive Coverage:</strong> Supports " . count($testCountries) . "+ countries</li>";
echo "<li><strong>Fallback Support:</strong> Defaults to UTC for unknown countries</li>";
echo "<li><strong>Validation:</strong> Validates timezone identifiers</li>";
echo "<li><strong>Display Names:</strong> User-friendly timezone names with GMT offsets</li>";
echo "</ul>";

echo "<h3>🔧 How It Works</h3>";
echo "<ol>";
echo "<li>User completes purchase from any country</li>";
echo "<li>System detects country from Stripe payment data</li>";
echo "<li>Timezone is automatically determined based on country</li>";
echo "<li>User account is created with correct timezone</li>";
echo "<li>All times are displayed in user's local timezone</li>";
echo "</ol>";
echo "</div>";

echo "<h3>🔗 Next Steps</h3>";
echo "<p>1. <strong>Test a purchase from Japan:</strong> Complete a purchase and verify timezone is set to Asia/Tokyo</p>";
echo "<p>2. <strong>Check database:</strong> Verify that both country and timezone are saved correctly</p>";
echo "<p>3. <strong>Test other countries:</strong> Try purchases from different countries to verify detection</p>";

echo "<style>";
echo "body { font-family: Arial, sans-serif; margin: 20px; }";
echo "h1, h2, h3 { color: #473BF0; }";
echo "table { margin: 10px 0; }";
echo "th, td { padding: 8px; text-align: left; }";
echo "code { background: #f8f9fa; padding: 2px 4px; border-radius: 3px; }";
echo "</style>";
?>
