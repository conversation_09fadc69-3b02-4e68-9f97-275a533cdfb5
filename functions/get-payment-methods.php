<?php
session_start();

// Auto-detect environment for file paths
$is_localhost = (strpos($_SERVER['HTTP_HOST'], 'localhost') !== false || strpos($_SERVER['HTTP_HOST'], '127.0.0.1') !== false);
$file_base_path = $is_localhost ? '/helloit' : '';

require_once $_SERVER['DOCUMENT_ROOT'] . $file_base_path . '/vendor/autoload.php';
include($_SERVER['DOCUMENT_ROOT'] . $file_base_path . '/functions/server.php');

// Set your Stripe API keys (same as cart.php)
\Stripe\Stripe::setApiKey('sk_test_51ROUofEJRyUMDOj5Op3iqq0gpje1mQqwhINGCYVwVRvmOixzZh1IUmfwArcDt7FtJkN9aszopPP6dyxVWyOZeC8F00gwZtB0ju');

header('Content-Type: application/json');

// Check if user is logged in
if (!isset($_SESSION['username'])) {
    echo json_encode(['success' => false, 'message' => 'User not logged in']);
    exit;
}

$username = $_SESSION['username'];

// Get user's Stripe customer ID (same approach as cart.php)
$user_query = "SELECT id, stripe_customer_id FROM user WHERE username = '$username'";
$user_result = mysqli_query($conn, $user_query);

if (!$user_result || mysqli_num_rows($user_result) == 0) {
    echo json_encode(['success' => false, 'message' => 'User not found']);
    exit;
}

$user = mysqli_fetch_assoc($user_result);
$user_id = $user['id'];
$stripe_customer_id = $user['stripe_customer_id'];

// If no customer ID, return empty list
if (empty($stripe_customer_id)) {
    echo json_encode(['success' => true, 'payment_methods' => []]);
    exit;
}

try {
    // Get payment methods from Stripe API (same as cart.php)
    $payment_methods = \Stripe\PaymentMethod::all([
        'customer' => $stripe_customer_id,
        'type' => 'card',
    ]);

    $saved_payment_methods = $payment_methods->data;

    // Convert Stripe payment methods to our format
    $formatted_payment_methods = [];
    foreach ($saved_payment_methods as $index => $pm) {
        $formatted_payment_methods[] = [
            'id' => $pm->id,
            'payment_method_id' => $pm->id,
            'card_brand' => $pm->card->brand,
            'card_last4' => $pm->card->last4,
            'card_exp_month' => $pm->card->exp_month,
            'card_exp_year' => $pm->card->exp_year,
            'is_default' => $index === 0 // First card is considered default
        ];
    }

    echo json_encode(['success' => true, 'payment_methods' => $formatted_payment_methods]);
} catch (Exception $e) {
    // If error fetching payment methods, return empty list (same as cart.php)
    echo json_encode(['success' => true, 'payment_methods' => [], 'error' => $e->getMessage()]);
}
?>
