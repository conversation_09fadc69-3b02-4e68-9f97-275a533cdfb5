<?php
include('server.php');

echo "<h2>🔧 Update payment_temp Table Structure</h2>";

// Check current table structure
$check_query = "DESCRIBE payment_temp";
$check_result = mysqli_query($conn, $check_query);

echo "<h3>Current Table Structure:</h3>";
echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr style='background: #f8f9fa;'>";
echo "<th style='padding: 10px;'>Field</th>";
echo "<th style='padding: 10px;'>Type</th>";
echo "<th style='padding: 10px;'>Null</th>";
echo "<th style='padding: 10px;'>Key</th>";
echo "<th style='padding: 10px;'>Default</th>";
echo "<th style='padding: 10px;'>Extra</th>";
echo "</tr>";

$existing_columns = [];
while ($row = mysqli_fetch_assoc($check_result)) {
    $existing_columns[] = $row['Field'];
    echo "<tr>";
    echo "<td style='padding: 10px;'>" . $row['Field'] . "</td>";
    echo "<td style='padding: 10px;'>" . $row['Type'] . "</td>";
    echo "<td style='padding: 10px;'>" . $row['Null'] . "</td>";
    echo "<td style='padding: 10px;'>" . $row['Key'] . "</td>";
    echo "<td style='padding: 10px;'>" . ($row['Default'] ?? 'NULL') . "</td>";
    echo "<td style='padding: 10px;'>" . $row['Extra'] . "</td>";
    echo "</tr>";
}
echo "</table>";

// Check which columns need to be added
$required_columns = [
    'user_created' => 'TINYINT(1) DEFAULT 0',
    'processed_at' => 'TIMESTAMP NULL DEFAULT NULL'
];

$columns_to_add = [];
foreach ($required_columns as $column => $definition) {
    if (!in_array($column, $existing_columns)) {
        $columns_to_add[$column] = $definition;
    }
}

if (!empty($columns_to_add)) {
    echo "<h3>⚠️ Missing Columns Detected:</h3>";
    echo "<ul>";
    foreach ($columns_to_add as $column => $definition) {
        echo "<li><strong>$column:</strong> $definition</li>";
    }
    echo "</ul>";
    
    if (isset($_POST['update_table'])) {
        echo "<h3>🔧 Updating Table Structure:</h3>";
        
        $success = true;
        foreach ($columns_to_add as $column => $definition) {
            $alter_query = "ALTER TABLE payment_temp ADD COLUMN $column $definition";
            echo "<p><strong>Executing:</strong> $alter_query</p>";
            
            if (mysqli_query($conn, $alter_query)) {
                echo "<p style='color: green;'>✅ Successfully added column: $column</p>";
            } else {
                echo "<p style='color: red;'>❌ Failed to add column $column: " . mysqli_error($conn) . "</p>";
                $success = false;
            }
        }
        
        if ($success) {
            echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
            echo "<h4>✅ Table Update Successful!</h4>";
            echo "<p>The payment_temp table has been updated with the required columns.</p>";
            echo "</div>";
            
            // Show updated structure
            echo "<h3>Updated Table Structure:</h3>";
            $updated_check_query = "DESCRIBE payment_temp";
            $updated_check_result = mysqli_query($conn, $updated_check_query);
            
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
            echo "<tr style='background: #f8f9fa;'>";
            echo "<th style='padding: 10px;'>Field</th>";
            echo "<th style='padding: 10px;'>Type</th>";
            echo "<th style='padding: 10px;'>Null</th>";
            echo "<th style='padding: 10px;'>Key</th>";
            echo "<th style='padding: 10px;'>Default</th>";
            echo "<th style='padding: 10px;'>Extra</th>";
            echo "</tr>";
            
            while ($row = mysqli_fetch_assoc($updated_check_result)) {
                $is_new = in_array($row['Field'], array_keys($columns_to_add));
                $row_style = $is_new ? 'background: #d4edda;' : '';
                
                echo "<tr style='$row_style'>";
                echo "<td style='padding: 10px;'>" . $row['Field'] . ($is_new ? ' <strong>(NEW)</strong>' : '') . "</td>";
                echo "<td style='padding: 10px;'>" . $row['Type'] . "</td>";
                echo "<td style='padding: 10px;'>" . $row['Null'] . "</td>";
                echo "<td style='padding: 10px;'>" . $row['Key'] . "</td>";
                echo "<td style='padding: 10px;'>" . ($row['Default'] ?? 'NULL') . "</td>";
                echo "<td style='padding: 10px;'>" . $row['Extra'] . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
    } else {
        echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
        echo "<h4>⚠️ Table Update Required</h4>";
        echo "<p>The payment_temp table needs to be updated to support the improved webhook functionality.</p>";
        echo "<p><strong>New columns will be added:</strong></p>";
        echo "<ul>";
        foreach ($columns_to_add as $column => $definition) {
            echo "<li><strong>$column:</strong> $definition</li>";
        }
        echo "</ul>";
        echo "<form method='POST'>";
        echo "<input type='hidden' name='update_table' value='1'>";
        echo "<button type='submit' style='background: #28a745; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; font-size: 16px;'>Update Table Structure</button>";
        echo "</form>";
        echo "</div>";
    }
} else {
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
    echo "<h4>✅ Table Structure is Up to Date</h4>";
    echo "<p>The payment_temp table already has all required columns.</p>";
    echo "</div>";
}

// Show sample data
echo "<h3>📋 Sample Data in payment_temp:</h3>";
$sample_query = "SELECT * FROM payment_temp ORDER BY id DESC LIMIT 5";
$sample_result = mysqli_query($conn, $sample_query);

if ($sample_result && mysqli_num_rows($sample_result) > 0) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%; font-size: 12px;'>";
    echo "<tr style='background: #f8f9fa;'>";
    
    // Get column names
    $columns = [];
    while ($field = mysqli_fetch_field($sample_result)) {
        $columns[] = $field->name;
        echo "<th style='padding: 8px;'>" . $field->name . "</th>";
    }
    echo "</tr>";
    
    // Reset result pointer
    mysqli_data_seek($sample_result, 0);
    
    while ($row = mysqli_fetch_assoc($sample_result)) {
        echo "<tr>";
        foreach ($columns as $column) {
            $value = $row[$column] ?? 'NULL';
            if ($column === 'session_id' && strlen($value) > 20) {
                $value = substr($value, 0, 20) . '...';
            }
            echo "<td style='padding: 8px;'>" . htmlspecialchars($value) . "</td>";
        }
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>No data found in payment_temp table.</p>";
}
?>

<h3>📝 What These Changes Do:</h3>
<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0;'>
    <h4>New Columns:</h4>
    <ul>
        <li><strong>user_created:</strong> Tracks whether the user has been successfully created (0 = pending, 1 = created)</li>
        <li><strong>processed_at:</strong> Timestamp when the webhook finished processing</li>
    </ul>
    
    <h4>Benefits:</h4>
    <ul>
        <li>Better tracking of webhook processing status</li>
        <li>Allows payment success page to show appropriate data source</li>
        <li>Enables cleanup of old payment_temp records</li>
        <li>Improves debugging and monitoring</li>
    </ul>
</div>

<h3>🔗 Next Steps:</h3>
<ol>
    <li><strong>Update table structure</strong> (if needed)</li>
    <li><strong>Test new purchase</strong> to verify improved webhook</li>
    <li><strong>Update payment success page</strong> to use appropriate data source</li>
    <li><strong>Verify sign-in works</strong> with new credentials</li>
</ol>

<style>
body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
h2, h3, h4 { color: #333; }
table { margin: 20px 0; }
th { background: #f8f9fa; }
</style>
