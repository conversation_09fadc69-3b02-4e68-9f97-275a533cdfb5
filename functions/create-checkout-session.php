<?php
require_once '../vendor/autoload.php';
include('../functions/server.php');

// Set your Stripe API keys
\Stripe\Stripe::setApiKey('sk_test_51ROUofEJRyUMDOj5Op3iqq0gpje1mQqwhINGCYVwVRvmOixzZh1IUmfwArcDt7FtJkN9aszopPP6dyxVWyOZeC8F00gwZtB0ju');

header('Content-Type: application/json');

// Get data from POST request
$data = json_decode(file_get_contents('php://input'), true);
$packagesize = $data['packagesize'];
$tickettype = $data['tickettype'];
$save_payment_method = isset($data['save_payment_method']) ? $data['save_payment_method'] : false;
// Sanitize inputs to prevent SQL injection
$packagesize = mysqli_real_escape_string($conn, $packagesize);
$tickettype = mysqli_real_escape_string($conn, $tickettype);
$script_name = $_SERVER['SCRIPT_NAME'];
$base_path = str_replace('/functions/create-checkout-session.php', '', $script_name);


$sql = "SELECT dollar_price_per_package, numbers_per_package FROM tickets WHERE package_size = '$packagesize' AND ticket_type = '$tickettype'";
$result = mysqli_query($conn, $sql);

if ($result && mysqli_num_rows($result) > 0) {
    $ticket = mysqli_fetch_assoc($result);
    $price = $ticket['dollar_price_per_package'] * 100; // Convert to cents for Stripe

    // Create a Stripe Checkout session
    try {
        // Get user information if logged in
        $user_id = null;
        $customer_id = null;

        if (session_status() == PHP_SESSION_NONE) {
            session_start();
        }

        if (isset($_SESSION['username'])) {
            $username = $_SESSION['username'];
            $user_query = "SELECT id, email FROM user WHERE username = '$username'";
            $user_result = mysqli_query($conn, $user_query);

            if ($user_result && mysqli_num_rows($user_result) > 0) {
                $user = mysqli_fetch_assoc($user_result);
                $user_id = $user['id'];
                $user_email = $user['email'];

                // Check if user already has a Stripe customer ID
                $customer_query = "SELECT stripe_customer_id FROM user WHERE id = $user_id";
                $customer_result = mysqli_query($conn, $customer_query);

                if ($customer_result && mysqli_num_rows($customer_result) > 0) {
                    $customer_row = mysqli_fetch_assoc($customer_result);
                    if (!empty($customer_row['stripe_customer_id'])) {
                        $customer_id = $customer_row['stripe_customer_id'];
                    }
                }

                // If no customer ID exists, create a new Stripe customer
                if (!$customer_id) {
                    $customer = \Stripe\Customer::create([
                        'email' => $user_email,
                        'metadata' => [
                            'user_id' => $user_id
                        ]
                    ]);

                    $customer_id = $customer->id;

                    // Save the customer ID to the user record
                    $update_sql = "UPDATE user SET stripe_customer_id = '$customer_id' WHERE id = $user_id";
                    mysqli_query($conn, $update_sql);
                }
            }
        }

        $session_params = [
            'payment_method_types' => ['card'],
            'line_items' => [[
                'price_data' => [
                    'currency' => 'usd',
                    'unit_amount' => $price,
                    'product_data' => [
                        'name' => 'HelloIT Ticket',
                        'description' => 'Packages Size'.$packagesize,
                        'metadata' => [
                            'ticket_type' => $tickettype,
                            'packagesize' => $packagesize
                        ]
                    ]
                ],
                'quantity' => 1
            ]],
            'mode' => 'payment',
            'success_url' => (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://$_SERVER[HTTP_HOST]{$base_path}/payment-success.php?session_id={CHECKOUT_SESSION_ID}",
            'cancel_url' => (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://$_SERVER[HTTP_HOST]{$base_path}/cancel.php?canceled=true",
            'metadata' => [
                'ticket_type' => $tickettype,
                'packagesize' => $packagesize,
                'save_payment_method' => $save_payment_method ? 'true' : 'false',
                'username' => isset($_SESSION['username']) ? $_SESSION['username'] : '',
                'cart' => json_encode([[
                    'ticket_type' => $tickettype,
                    'package_size' => $packagesize,
                    'numbers_per_package' => $ticket['numbers_per_package'],
                    'dollar_price_per_package' => $ticket['dollar_price_per_package'],
                    'quantity' => 1
                ]])
            ]
        ];

        // If we have a customer ID and user wants to save payment method
        if ($customer_id) {
            $session_params['customer'] = $customer_id;

            // If user wants to save payment method
            if ($save_payment_method) {
                $session_params['payment_intent_data'] = [
                    'setup_future_usage' => 'off_session',
                ];
            }
        }

        $checkout_session = \Stripe\Checkout\Session::create($session_params);

        // Return session ID as JSON response
        echo json_encode(['sessionId' => $checkout_session->id]);
    } catch (Exception $e) {
        // Handle exception
        echo json_encode(['error' => $e->getMessage()]);
    }
} else {
    echo json_encode(['error' => 'No tickets found for the specified package size and ticket type.']);
}
?>