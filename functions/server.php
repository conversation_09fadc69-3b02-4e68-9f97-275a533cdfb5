<?php
$servername = "localhost";
$username = "helloit88_wat";
$password = "Thailand@2020";
$dbname = "helloit88_db";

//Create Connection
$conn = mysqli_connect($servername, $username, $password, $dbname);

//Check connection
if (!$conn) {
    die("Connect failed" . mysqli_connect_errno());
} else {
    //echo "Connected successfully // server.php";

    // Include timezone helper for GMT+0 storage and user timezone display
    include_once __DIR__ . '/timezone-helper.php';

    // Set database session to UTC (this will override the MST server timezone)
    setDatabaseUTC($conn);
}

class DBController
{
    private $conn;

    function __construct()
    {
        global $conn;
        $this->conn = $conn;
    }

    function runQuery($query)
    {
        $result = mysqli_query($this->conn, $query);
        $data = [];
        while ($row = mysqli_fetch_assoc($result)) {
            $data[] = $row;
        }
        return $data;
    }
}