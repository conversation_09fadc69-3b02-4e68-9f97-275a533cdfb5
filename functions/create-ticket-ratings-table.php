<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Create Ticket Ratings Table</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; background-color: #f8f9fa; }
        .container { background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        h2 { color: #473BF0; text-align: center; margin-bottom: 30px; }
        .success { color: green; font-weight: bold; background: #d4edda; padding: 15px; border-radius: 5px; border: 1px solid #c3e6cb; }
        .error { color: red; font-weight: bold; background: #f8d7da; padding: 15px; border-radius: 5px; border: 1px solid #f5c6cb; }
        .info { background: #e7f3ff; padding: 15px; border-radius: 5px; border: 1px solid #007bff; margin: 15px 0; }
        ul { line-height: 1.6; }
        .btn { background: #473BF0; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 10px 5px; }
        .btn:hover { background: #3d32d9; color: white; }
    </style>
</head>
<body>
    <div class="container">
        <h2>🌟 Create Ticket Ratings Table</h2>

<?php
include('server.php');

// Create ticket_ratings table to store user ratings and feedback
$create_table_sql = "
CREATE TABLE IF NOT EXISTS ticket_ratings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    ticket_id INT NOT NULL,
    user_id INT NOT NULL,
    rating INT NOT NULL CHECK (rating >= 1 AND rating <= 5),
    comment TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_ticket_user (ticket_id, user_id),
    FOREIGN KEY (ticket_id) REFERENCES support_tickets(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES user(id) ON DELETE CASCADE,
    INDEX idx_ticket_id (ticket_id),
    INDEX idx_user_id (user_id),
    INDEX idx_rating (rating),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;
";

if ($conn->query($create_table_sql) === TRUE) {
    echo "<div class='success'>✅ Ticket ratings table created successfully!</div>";

    // Show table structure
    echo "<h3>📋 Table Structure:</h3>";
    echo "<ul>";
    echo "<li><strong>id:</strong> Primary key (auto increment)</li>";
    echo "<li><strong>ticket_id:</strong> Foreign key to support_tickets table</li>";
    echo "<li><strong>user_id:</strong> Foreign key to user table</li>";
    echo "<li><strong>rating:</strong> Integer 1-5 stars (with CHECK constraint)</li>";
    echo "<li><strong>comment:</strong> Optional text feedback</li>";
    echo "<li><strong>created_at:</strong> When rating was first created</li>";
    echo "<li><strong>updated_at:</strong> When rating was last updated</li>";
    echo "</ul>";

    echo "<h3>🔒 Constraints & Indexes:</h3>";
    echo "<ul>";
    echo "<li><strong>Unique constraint:</strong> One rating per ticket per user</li>";
    echo "<li><strong>Foreign keys:</strong> Cascade delete when ticket/user is deleted</li>";
    echo "<li><strong>Check constraint:</strong> Rating must be between 1-5</li>";
    echo "<li><strong>Indexes:</strong> Optimized for ticket_id, user_id, rating, and created_at queries</li>";
    echo "</ul>";

} else {
    echo "<div class='error'>❌ Error creating table: " . $conn->error . "</div>";
}

// Test if we can insert a sample rating (optional)
echo "<div class='info'>";
echo "<h3>🧪 Table Created Successfully!</h3>";
echo "<p>You can now use this table to store ticket ratings. Each user can rate each ticket only once.</p>";
echo "<p><strong>✅ All rating system components are ready:</strong></p>";
echo "<ol>";
echo "<li>✅ Database table created</li>";
echo "<li>✅ Rating submission form (rate-ticket.php)</li>";
echo "<li>✅ Rating display in ticket details</li>";
echo "<li>✅ Rating links in ticket lists</li>";
echo "<li>✅ Account Menu updated with rating link</li>";
echo "</ol>";
echo "</div>";

echo "<div style='text-align: center; margin-top: 30px;'>";
echo "<a href='../front-end/my-ticket.php' class='btn'>📋 View My Tickets</a>";
echo "<a href='../front-end/my-ratings.php' class='btn'>⭐ View My Ratings</a>";
echo "<a href='../test-rating-system.php' class='btn'>🧪 Test Overview</a>";
echo "</div>";

$conn->close();
?>
    </div>
</body>
</html>
