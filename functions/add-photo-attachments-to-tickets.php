<?php
include('server.php');

echo "<h2>📸 Add Photo Attachments to Support Tickets</h2>";

echo "<h3>🔍 **Current Database Analysis**</h3>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0;'>";

// Check current support_tickets table structure
$table_info_query = "DESCRIBE support_tickets";
$table_info_result = mysqli_query($conn, $table_info_query);

echo "<h4>Current support_tickets Table Structure:</h4>";
echo "<table border='1' style='border-collapse: collapse; width: 100%; font-size: 12px;'>";
echo "<tr style='background: #f8f9fa;'>";
echo "<th style='padding: 6px;'>Field</th>";
echo "<th style='padding: 6px;'>Type</th>";
echo "<th style='padding: 6px;'>Null</th>";
echo "<th style='padding: 6px;'>Key</th>";
echo "<th style='padding: 6px;'>Default</th>";
echo "</tr>";

$has_attachments_column = false;
while ($row = mysqli_fetch_assoc($table_info_result)) {
    echo "<tr>";
    echo "<td style='padding: 6px;'>" . $row['Field'] . "</td>";
    echo "<td style='padding: 6px;'>" . $row['Type'] . "</td>";
    echo "<td style='padding: 6px;'>" . $row['Null'] . "</td>";
    echo "<td style='padding: 6px;'>" . $row['Key'] . "</td>";
    echo "<td style='padding: 6px;'>" . $row['Default'] . "</td>";
    echo "</tr>";
    
    if ($row['Field'] === 'attachments') {
        $has_attachments_column = true;
    }
}
echo "</table>";
echo "</div>";

echo "<h3>📋 **Photo Upload Recommendations**</h3>";
echo "<div style='background: #e3f2fd; padding: 15px; border-radius: 5px; margin: 15px 0; border: 1px solid #bbdefb;'>";

echo "<h4>🎯 Recommended Photo Upload Limits:</h4>";
echo "<ul style='color: #0c5460;'>";
echo "<li><strong>Maximum Photos:</strong> 5 photos per ticket</li>";
echo "<li><strong>File Size Limit:</strong> 5MB per photo (25MB total)</li>";
echo "<li><strong>Supported Formats:</strong> JPG, JPEG, PNG, GIF, WebP</li>";
echo "<li><strong>Storage Method:</strong> Files stored in uploads/tickets/ folder</li>";
echo "<li><strong>Database Storage:</strong> JSON array of file paths in attachments column</li>";
echo "</ul>";

echo "<h4>💡 Why These Limits?</h4>";
echo "<ul style='color: #0c5460;'>";
echo "<li><strong>5 Photos:</strong> Enough for most support issues without overwhelming</li>";
echo "<li><strong>5MB per photo:</strong> High quality but reasonable file size</li>";
echo "<li><strong>Multiple formats:</strong> Supports all common image types</li>";
echo "<li><strong>JSON storage:</strong> Flexible and easy to manage</li>";
echo "</ul>";
echo "</div>";

echo "<h3>🛠️ **Database Modifications Needed**</h3>";
echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 15px 0; border: 1px solid #ffeaa7;'>";

if (!$has_attachments_column) {
    echo "<h4>⚠️ Missing Column: attachments</h4>";
    echo "<p style='color: #856404;'>The support_tickets table needs an 'attachments' column to store photo file paths.</p>";
    
    if (isset($_POST['action']) && $_POST['action'] === 'add_attachments_column') {
        $alter_query = "ALTER TABLE support_tickets ADD COLUMN attachments JSON NULL COMMENT 'JSON array of attachment file paths'";
        
        if (mysqli_query($conn, $alter_query)) {
            echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0; color: #155724;'>";
            echo "<h4>✅ Column Added Successfully!</h4>";
            echo "<p>The 'attachments' column has been added to the support_tickets table.</p>";
            echo "</div>";
            $has_attachments_column = true;
        } else {
            echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0; color: #721c24;'>";
            echo "<h4>❌ Error Adding Column</h4>";
            echo "<p>Error: " . mysqli_error($conn) . "</p>";
            echo "</div>";
        }
    }
    
    if (!$has_attachments_column) {
        echo "<form method='POST' style='margin: 10px 0;'>";
        echo "<input type='hidden' name='action' value='add_attachments_column'>";
        echo "<button type='submit' style='background: #28a745; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;'>Add attachments Column</button>";
        echo "</form>";
    }
} else {
    echo "<h4 style='color: #155724;'>✅ Database Ready</h4>";
    echo "<p style='color: #155724;'>The support_tickets table already has the 'attachments' column.</p>";
}
echo "</div>";

echo "<h3>📁 **Upload Directory Setup**</h3>";
echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 5px; margin: 15px 0; border: 1px solid #bee5eb;'>";

$upload_dir = '../uploads/tickets';
$upload_dir_exists = is_dir($upload_dir);
$upload_dir_writable = $upload_dir_exists && is_writable($upload_dir);

echo "<h4>Directory Status:</h4>";
echo "<ul>";
echo "<li><strong>Upload Directory:</strong> uploads/tickets/</li>";
echo "<li><strong>Exists:</strong> " . ($upload_dir_exists ? '✅ Yes' : '❌ No') . "</li>";
echo "<li><strong>Writable:</strong> " . ($upload_dir_writable ? '✅ Yes' : '❌ No') . "</li>";
echo "</ul>";

if (!$upload_dir_exists) {
    if (isset($_POST['action']) && $_POST['action'] === 'create_upload_dir') {
        if (mkdir($upload_dir, 0755, true)) {
            echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0; color: #155724;'>";
            echo "<h4>✅ Directory Created!</h4>";
            echo "<p>Upload directory created successfully.</p>";
            echo "</div>";
            $upload_dir_exists = true;
            $upload_dir_writable = true;
        } else {
            echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0; color: #721c24;'>";
            echo "<h4>❌ Failed to Create Directory</h4>";
            echo "<p>Could not create upload directory. Check permissions.</p>";
            echo "</div>";
        }
    }
    
    if (!$upload_dir_exists) {
        echo "<form method='POST' style='margin: 10px 0;'>";
        echo "<input type='hidden' name='action' value='create_upload_dir'>";
        echo "<button type='submit' style='background: #007cba; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;'>Create Upload Directory</button>";
        echo "</form>";
    }
}

if ($upload_dir_exists && $upload_dir_writable) {
    echo "<p style='color: #0c5460;'><strong>✅ Ready for file uploads!</strong></p>";
}
echo "</div>";

echo "<h3>🔧 **Form Modifications Needed**</h3>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0;'>";

echo "<h4>Changes to create-ticket.php:</h4>";
echo "<ol>";
echo "<li><strong>Remove Severity and Priority fields</strong> from the form</li>";
echo "<li><strong>Set default values:</strong> severity = 'Normal', priority = 'Normal'</li>";
echo "<li><strong>Add photo upload field</strong> with multiple file selection</li>";
echo "<li><strong>Add file validation</strong> for size and type</li>";
echo "<li><strong>Add photo preview</strong> functionality</li>";
echo "<li><strong>Update form processing</strong> to handle file uploads</li>";
echo "</ol>";

echo "<h4>Photo Upload Features:</h4>";
echo "<ul>";
echo "<li><strong>Drag & Drop:</strong> Modern file upload interface</li>";
echo "<li><strong>Preview:</strong> Show selected photos before upload</li>";
echo "<li><strong>Validation:</strong> Check file size and type</li>";
echo "<li><strong>Progress:</strong> Upload progress indicator</li>";
echo "<li><strong>Remove:</strong> Remove photos before submitting</li>";
echo "</ul>";
echo "</div>";

echo "<h3>📊 **Implementation Status**</h3>";
echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 15px 0; border: 1px solid #c3e6cb;'>";

$database_ready = $has_attachments_column;
$directory_ready = $upload_dir_exists && $upload_dir_writable;
$ready_for_implementation = $database_ready && $directory_ready;

echo "<h4>Readiness Checklist:</h4>";
echo "<ul>";
echo "<li>" . ($database_ready ? '✅' : '❌') . " <strong>Database:</strong> attachments column " . ($database_ready ? 'exists' : 'missing') . "</li>";
echo "<li>" . ($directory_ready ? '✅' : '❌') . " <strong>Upload Directory:</strong> " . ($directory_ready ? 'ready' : 'not ready') . "</li>";
echo "<li>" . ($ready_for_implementation ? '✅' : '❌') . " <strong>Ready for Implementation:</strong> " . ($ready_for_implementation ? 'Yes' : 'No') . "</li>";
echo "</ul>";

if ($ready_for_implementation) {
    echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0; color: #155724;'>";
    echo "<h4>🚀 Ready to Update create-ticket.php!</h4>";
    echo "<p>All prerequisites are met. You can now proceed with updating the form.</p>";
    echo "<div style='display: flex; gap: 10px; margin: 10px 0;'>";
    echo "<a href='update-create-ticket-form.php' style='background: #28a745; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>Update Form Now</a>";
    echo "<a href='../front-end/create-ticket.php' style='background: #007cba; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>View Current Form</a>";
    echo "</div>";
    echo "</div>";
} else {
    echo "<div style='background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0; color: #856404;'>";
    echo "<h4>⚠️ Prerequisites Required</h4>";
    echo "<p>Complete the database and directory setup above before proceeding.</p>";
    echo "</div>";
}
echo "</div>";

echo "<h3>📋 **Proposed Form Layout**</h3>";
echo "<div style='background: #e3f2fd; padding: 15px; border-radius: 5px; margin: 15px 0; border: 1px solid #bbdefb;'>";

echo "<h4>New Form Structure:</h4>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<pre style='margin: 0; font-size: 12px;'>";
echo "┌─────────────────────────────────────────┐
│ Create New Support Ticket               │
├─────────────────────────────────────────┤
│ Subject: [________________]             │
│                                         │
│ Ticket Type: [Starter (1 remaining) ▼] │
│                                         │
│ Problem Type: [Select problem type... ▼]│
│                                         │
│ Problem Description:                    │
│ [_________________________________]    │
│ [_________________________________]    │
│ [_________________________________]    │
│                                         │
│ Attach Photos (Optional):               │
│ ┌─────────────────────────────────────┐ │
│ │  📷 Drag & drop photos here        │ │
│ │     or click to browse              │ │
│ │  (Max 5 photos, 5MB each)          │ │
│ └─────────────────────────────────────┘ │
│                                         │
│ [📎 Selected Photos Preview Area]       │
│                                         │
│ [Create Ticket]                         │
└─────────────────────────────────────────┘";
echo "</pre>";
echo "</div>";

echo "<h4>Hidden/Default Values:</h4>";
echo "<ul style='color: #0c5460;'>";
echo "<li><strong>Severity:</strong> Automatically set to 'Normal'</li>";
echo "<li><strong>Priority:</strong> Automatically set to 'Normal'</li>";
echo "<li><strong>Status:</strong> Automatically set to 'open'</li>";
echo "</ul>";
echo "</div>";

echo "<h3>🎯 **Benefits of This Approach**</h3>";
echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 5px; margin: 15px 0; border: 1px solid #bee5eb;'>";

echo "<h4>User Experience Benefits:</h4>";
echo "<ul style='color: #0c5460;'>";
echo "<li><strong>Simplified Form:</strong> Fewer fields to fill out</li>";
echo "<li><strong>Visual Support:</strong> Photos help explain issues better</li>";
echo "<li><strong>Faster Submission:</strong> No need to think about severity/priority</li>";
echo "<li><strong>Better Communication:</strong> Images worth a thousand words</li>";
echo "</ul>";

echo "<h4>Admin Benefits:</h4>";
echo "<ul style='color: #0c5460;'>";
echo "<li><strong>Consistent Defaults:</strong> All tickets start with 'Normal' priority</li>";
echo "<li><strong>Visual Context:</strong> Photos help understand issues quickly</li>";
echo "<li><strong>Better Triage:</strong> Admins can set priority based on actual issue</li>";
echo "<li><strong>Reduced Confusion:</strong> Users don't misclassify severity</li>";
echo "</ul>";
echo "</div>";
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
h2, h3, h4, h5 { color: #333; }
table { margin: 20px 0; }
th { background: #f8f9fa; }
button:hover { opacity: 0.9; }
pre { font-family: 'Courier New', monospace; }
</style>
