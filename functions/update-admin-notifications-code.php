<?php
include('server.php');

echo "<h2>🔧 Update Code Files - Remove admin_notifications References</h2>";

echo "<h3>📋 **Code Update Progress**</h3>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0;'>";

$updates_made = [];
$errors = [];

// Function to update file content
function updateFile($file_path, $old_content, $new_content, $description) {
    global $updates_made, $errors;
    
    if (!file_exists($file_path)) {
        $errors[] = "File not found: $file_path";
        return false;
    }
    
    $current_content = file_get_contents($file_path);
    if (strpos($current_content, $old_content) !== false) {
        $updated_content = str_replace($old_content, $new_content, $current_content);
        if (file_put_contents($file_path, $updated_content)) {
            $updates_made[] = "$file_path - $description";
            return true;
        } else {
            $errors[] = "Failed to write to: $file_path";
            return false;
        }
    } else {
        $updates_made[] = "$file_path - Already updated (no changes needed)";
        return true;
    }
}

// 1. Remove admin_notifications table creation from admin-notifications.php
$admin_notifications_old = '// Create admin_notifications table if it doesn\'t exist
$create_table_sql = "CREATE TABLE IF NOT EXISTS admin_notifications (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    type ENUM(\'new_ticket\', \'new_message\', \'ticket_update\') NOT NULL,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    ticket_id INT(11) NULL,
    user_id INT(11) NULL,
    admin_id INT(11) NULL,
    is_read TINYINT(1) DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    read_at TIMESTAMP NULL,
    INDEX idx_type (type),
    INDEX idx_is_read (is_read),
    INDEX idx_created_at (created_at)
)";
mysqli_query($conn, $create_table_sql);';

$admin_notifications_new = '// admin_notifications table removed - using existing notification systems';

updateFile('../merlion/admin-notifications.php', $admin_notifications_old, $admin_notifications_new, 'Removed table creation');

// 2. Remove admin_notifications INSERT from send-message.php
$send_message_old = '    // Create admin_notifications table if it doesn\'t exist
    $create_notifications_table = "CREATE TABLE IF NOT EXISTS admin_notifications (
        id INT(11) AUTO_INCREMENT PRIMARY KEY,
        type ENUM(\'new_ticket\', \'new_message\', \'ticket_update\') NOT NULL,
        title VARCHAR(255) NOT NULL,
        message TEXT NOT NULL,
        ticket_id INT(11) NULL,
        user_id INT(11) NULL,
        admin_id INT(11) NULL,
        is_read TINYINT(1) DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        read_at TIMESTAMP NULL
    )";
    mysqli_query($conn, $create_notifications_table);

    // Create admin notification for new message
    $message_escaped = mysqli_real_escape_string($conn, $message);
    $notificationInsert = "INSERT INTO admin_notifications (type, title, message, ticket_id, user_id, created_at)
                          VALUES (\'new_message\', \'New Chat Message\',
                          \'User $username sent a new message: $message_escaped\',
                          $ticket_id, $user_id, NOW())";
    mysqli_query($conn, $notificationInsert);';

$send_message_new = '    // admin_notifications table removed - notifications handled by chat_messages.is_read system';

updateFile('../front-end/send-message.php', $send_message_old, $send_message_new, 'Removed admin_notifications INSERT');

// 3. Remove admin_notifications INSERT from create-ticket.php
$create_ticket_old = '                // Create admin_notifications table if it doesn\'t exist
                $create_notifications_table = "CREATE TABLE IF NOT EXISTS admin_notifications (
                    id INT(11) AUTO_INCREMENT PRIMARY KEY,
                    type ENUM(\'new_ticket\', \'new_message\', \'ticket_update\') NOT NULL,
                    title VARCHAR(255) NOT NULL,
                    message TEXT NOT NULL,
                    ticket_id INT(11) NULL,
                    user_id INT(11) NULL,
                    admin_id INT(11) NULL,
                    is_read TINYINT(1) DEFAULT 0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    read_at TIMESTAMP NULL
                )";
                mysqli_query($conn, $create_notifications_table);

                // Create admin notification for new ticket
                $username = mysqli_real_escape_string($conn, $user[\'username\']);
                $subject_escaped = mysqli_real_escape_string($conn, $subject);
                $notificationInsert = "INSERT INTO admin_notifications (type, title, message, ticket_id, user_id, created_at)
                                      VALUES (\'new_ticket\', \'New Support Ticket Created\',
                                      \'User $username created a new $ticketType ticket: $subject_escaped\',
                                      $ticketId, $userId, NOW())";
                mysqli_query($conn, $notificationInsert);';

$create_ticket_new = '                // admin_notifications table removed - notifications handled by support_tickets.is_seen_by_admin system';

updateFile('../merlion/create-ticket.php', $create_ticket_old, $create_ticket_new, 'Removed admin_notifications INSERT');

// 4. Remove admin_notifications INSERT from chat-support.php
$chat_support_old = '        // Create admin_notifications table if it doesn\'t exist
        $create_notifications_table = "CREATE TABLE IF NOT EXISTS admin_notifications (
            id INT(11) AUTO_INCREMENT PRIMARY KEY,
            type ENUM(\'new_ticket\', \'new_message\', \'ticket_update\') NOT NULL,
            title VARCHAR(255) NOT NULL,
            message TEXT NOT NULL,
            ticket_id INT(11) NULL,
            user_id INT(11) NULL,
            admin_id INT(11) NULL,
            is_read TINYINT(1) DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            read_at TIMESTAMP NULL
        )";
        mysqli_query($conn, $create_notifications_table);

        // Create admin notification for new message
        $message_escaped = mysqli_real_escape_string($conn, $message);
        $username_escaped = mysqli_real_escape_string($conn, $username);
        $notificationInsert = "INSERT INTO admin_notifications (type, title, message, ticket_id, user_id, created_at)
                              VALUES (\'new_message\', \'New Chat Message\',
                              \'User $username_escaped sent a new message: $message_escaped\',
                              $ticket_id, $user_id, NOW())";
        mysqli_query($conn, $notificationInsert);';

$chat_support_new = '        // admin_notifications table removed - notifications handled by chat_messages.is_read system';

updateFile('../front-end/chat-support.php', $chat_support_old, $chat_support_new, 'Removed admin_notifications INSERT');

// 5. Remove admin_notifications queries from admin-chat.php
$admin_chat_old1 = '    // Also mark admin notifications as read for this user\'s messages
    $mark_notifications_read = "UPDATE admin_notifications
                               SET is_read = 1, read_at = NOW()
                               WHERE type = \'new_message\'
                               AND user_id = $chat_user_id
                               AND is_read = 0";
    mysqli_query($conn, $mark_notifications_read);';

$admin_chat_new1 = '    // admin_notifications table removed - using chat_messages.is_read system';

updateFile('../merlion/admin-chat.php', $admin_chat_old1, $admin_chat_new1, 'Removed admin_notifications UPDATE');

$admin_chat_old2 = '    // Count unread notifications for this admin (or all, depending on your logic)
    $notif_sql = "SELECT COUNT(*) as unread_count FROM admin_notifications WHERE is_read = 0 AND type = \'new_message\'";
    $notif_result = mysqli_query($conn, $notif_sql);
    $notif_row = mysqli_fetch_assoc($notif_result);
    $unread_count = intval($notif_row[\'unread_count\']);';

$admin_chat_new2 = '    // Count unread messages directly from chat_messages table
    $notif_sql = "SELECT COUNT(*) as unread_count FROM chat_messages WHERE sender_type = \'user\' AND is_read = 0";
    $notif_result = mysqli_query($conn, $notif_sql);
    $notif_row = mysqli_fetch_assoc($notif_result);
    $unread_count = intval($notif_row[\'unread_count\']);';

updateFile('../merlion/admin-chat.php', $admin_chat_old2, $admin_chat_new2, 'Updated notification count query');

echo "<h4>✅ Updates Completed:</h4>";
echo "<ul>";
foreach ($updates_made as $update) {
    echo "<li style='color: #155724;'>✅ $update</li>";
}
echo "</ul>";

if (!empty($errors)) {
    echo "<h4>❌ Errors:</h4>";
    echo "<ul>";
    foreach ($errors as $error) {
        echo "<li style='color: #721c24;'>❌ $error</li>";
    }
    echo "</ul>";
}
echo "</div>";

// Delete the table creation file
if (file_exists('../merlion/create-admin-notifications-table.php')) {
    if (unlink('../merlion/create-admin-notifications-table.php')) {
        echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0; color: #155724;'>";
        echo "<h4>🗑️ Deleted Unnecessary File</h4>";
        echo "<p>Removed: merlion/create-admin-notifications-table.php</p>";
        echo "</div>";
    }
}

// Delete the test notifications file
if (file_exists('../merlion/test-notifications.php')) {
    if (unlink('../merlion/test-notifications.php')) {
        echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0; color: #155724;'>";
        echo "<h4>🗑️ Deleted Test File</h4>";
        echo "<p>Removed: merlion/test-notifications.php</p>";
        echo "</div>";
    }
}

echo "<h3>🧪 **Test Current Notification System**</h3>";
echo "<div style='background: #e3f2fd; padding: 15px; border-radius: 5px; margin: 15px 0; border: 1px solid #bbdefb;'>";

// Test notification counts
$new_tickets_query = "SELECT COUNT(*) as count FROM support_tickets WHERE is_seen_by_admin = 0";
$new_tickets_result = mysqli_query($conn, $new_tickets_query);
$new_tickets_count = mysqli_fetch_assoc($new_tickets_result)['count'];

$unread_messages_query = "SELECT COUNT(*) as count FROM chat_messages WHERE sender_type = 'user' AND is_read = 0";
$unread_messages_result = mysqli_query($conn, $unread_messages_query);
$unread_messages_count = mysqli_fetch_assoc($unread_messages_result)['count'];

echo "<h4>✅ Notification System Test Results:</h4>";
echo "<ul style='color: #0c5460;'>";
echo "<li><strong>New Tickets (unseen by admin):</strong> $new_tickets_count</li>";
echo "<li><strong>Unread Messages from Users:</strong> $unread_messages_count</li>";
echo "<li><strong>Total Notifications:</strong> " . ($new_tickets_count + $unread_messages_count) . "</li>";
echo "</ul>";

echo "<h4>🔧 Test Admin Interface:</h4>";
echo "<div style='display: flex; gap: 10px; margin: 10px 0;'>";
echo "<a href='../merlion/admin-notifications' style='background: #007cba; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>Test Admin Notifications</a>";
echo "<a href='../merlion/admin-chat' style='background: #28a745; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>Test Admin Chat</a>";
echo "<a href='../merlion/admin-tickets' style='background: #6f42c1; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>Test Admin Tickets</a>";
echo "</div>";
echo "</div>";

echo "<h3>📊 **Summary of Changes**</h3>";
echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 15px 0; border: 1px solid #c3e6cb;'>";
echo "<h4 style='color: #155724;'>✅ Successfully Removed admin_notifications Table References!</h4>";

echo "<h4 style='color: #155724;'>🔧 Files Updated:</h4>";
echo "<ul style='color: #155724;'>";
echo "<li><strong>merlion/admin-notifications.php</strong> - Removed table creation</li>";
echo "<li><strong>front-end/send-message.php</strong> - Removed INSERT operations</li>";
echo "<li><strong>merlion/create-ticket.php</strong> - Removed INSERT operations</li>";
echo "<li><strong>front-end/chat-support.php</strong> - Removed INSERT operations</li>";
echo "<li><strong>merlion/admin-chat.php</strong> - Updated to use chat_messages table</li>";
echo "</ul>";

echo "<h4 style='color: #155724;'>🗑️ Files Deleted:</h4>";
echo "<ul style='color: #155724;'>";
echo "<li><strong>merlion/create-admin-notifications-table.php</strong> - No longer needed</li>";
echo "<li><strong>merlion/test-notifications.php</strong> - No longer needed</li>";
echo "</ul>";

echo "<h4 style='color: #155724;'>🚀 Benefits Achieved:</h4>";
echo "<ul style='color: #155724;'>";
echo "<li><strong>Performance:</strong> No more redundant table creation on every request</li>";
echo "<li><strong>Storage:</strong> No more unnecessary notification records</li>";
echo "<li><strong>Maintenance:</strong> Simplified notification system</li>";
echo "<li><strong>Consistency:</strong> Single source of truth for notifications</li>";
echo "</ul>";

echo "<p style='color: #155724; font-weight: bold;'>🎉 Your notification system is now optimized and will perform much better!</p>";
echo "</div>";

echo "<h3>🔗 **Next Steps**</h3>";
echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 15px 0; border: 1px solid #ffeaa7;'>";
echo "<h4>Recommended Actions:</h4>";
echo "<ol style='color: #856404;'>";
echo "<li><strong>Test the admin interface</strong> to ensure notifications still work</li>";
echo "<li><strong>Check notification counts</strong> in admin menu</li>";
echo "<li><strong>Test message notifications</strong> in admin chat</li>";
echo "<li><strong>Verify ticket notifications</strong> in admin tickets</li>";
echo "<li><strong>Monitor performance</strong> - should be noticeably faster</li>";
echo "</ol>";

echo "<div style='display: flex; gap: 10px; margin: 15px 0;'>";
echo "<a href='remove-admin-notifications-table.php' style='background: #dc3545; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>Drop Table (if not done)</a>";
echo "<a href='../merlion/admin-notifications' style='background: #28a745; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>Test Notifications</a>";
echo "</div>";
echo "</div>";
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
h2, h3, h4, h5 { color: #333; }
</style>
