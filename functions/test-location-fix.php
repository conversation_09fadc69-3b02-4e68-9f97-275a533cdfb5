<?php
/**
 * Simple Test for Location Update Fix
 * Tests only the location update functionality
 */

// Check if session is already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

echo "<h1>🧪 Test Location Update Fix</h1>";

// Check if user is logged in
if (!isset($_SESSION['username'])) {
    echo "<p style='color: red;'>❌ User not logged in. Please log in first.</p>";
    echo "<p><a href='../front-end/sign-in.php'>Go to Sign In</a></p>";
    exit();
}

$username = $_SESSION['username'];
echo "<h2>👤 Testing for User: " . htmlspecialchars($username) . "</h2>";

// Include required files safely
try {
    if (!class_exists('DBController')) {
        include('server.php');
    }
    if (!function_exists('getCompleteCustomerData')) {
        include('customer-data-service.php');
    }
    require_once '../vendor/autoload.php';
    require_once '../config/api-config.php';
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error loading required files: " . htmlspecialchars($e->getMessage()) . "</p>";
    exit();
}

try {
    // Get user data
    $user = getCompleteCustomerData($username);
    
    if (!$user) {
        echo "<p style='color: red;'>❌ Failed to get user data</p>";
        exit();
    }
    
    if (!$user['has_appika_data'] || empty($user['appika_customer_id'])) {
        echo "<p style='color: orange;'>⚠️ User doesn't have Appika data. Cannot test location update.</p>";
        echo "<p><strong>User needs to be linked to Appika API first.</strong></p>";
        exit();
    }
    
    echo "<h3>✅ User has Appika data</h3>";
    echo "<p><strong>Appika Customer ID:</strong> " . htmlspecialchars($user['appika_customer_id']) . "</p>";
    
    // Test location update directly
    echo "<h3>🔧 Testing Location Update Function</h3>";
    
    // Get API configuration
    $apiConfig = getCustomerApiConfig();
    $apiEndpoint = $apiConfig['endpoint'];
    $apiPath = $apiConfig['path'];
    $apiKey = $apiConfig['key'];
    
    // Create Guzzle client
    $client = new \GuzzleHttp\Client([
        'base_uri' => $apiEndpoint,
        'timeout' => 30,
        'http_errors' => false,
    ]);
    
    // First, get existing locations
    echo "<h4>📍 Step 1: Getting existing locations...</h4>";
    $locationsResponse = $client->request('GET', $apiPath . '/' . $user['appika_customer_id'] . '/locations', [
        'headers' => [
            'X-api-key' => "{$apiKey}",
            'Accept' => 'application/json',
        ]
    ]);
    
    $locationsStatusCode = $locationsResponse->getStatusCode();
    $locationsBody = $locationsResponse->getBody()->getContents();
    
    if ($locationsStatusCode >= 200 && $locationsStatusCode < 300) {
        $locationsData = json_decode($locationsBody, true);
        $locations = $locationsData['items'] ?? [];
        
        echo "<p>✅ Found " . count($locations) . " locations</p>";
        
        // Find primary location
        $primaryLocation = null;
        foreach ($locations as $location) {
            if ($location['is_primary_loc'] === 'y') {
                $primaryLocation = $location;
                break;
            }
        }
        
        if ($primaryLocation) {
            echo "<p>✅ Found primary location: " . htmlspecialchars($primaryLocation['loc_name']) . "</p>";
            echo "<p><strong>Location Code:</strong> " . htmlspecialchars($primaryLocation['loc_code']) . "</p>";
            
            // Test location update with proper fields
            echo "<h4>🔄 Step 2: Testing location update...</h4>";
            
            $updateLocationData = [
                'loc_code' => $primaryLocation['loc_code'], // Required field - now included!
                'loc_name' => $primaryLocation['loc_name'], // Required field - now included!
                'add1' => $user['address'] ?? 'Test Address Updated',
                'add2' => $user['address2'] ?? 'Test Address 2 Updated',
                'city' => $user['city'] ?? 'Bangkok',
                'state_code' => $user['state'] ?? 'Bangkok',
                'ccode' => $user['country'] ?? 'TH',
                'zip' => $user['postal_code'] ?? '10110',
                'tel_work' => $user['tel_work'] ?? '1234567890',
                'is_primary_loc' => 'y',
                'status' => 'a'
            ];
            
            echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
            echo "<h5>Update Data (with required fields):</h5>";
            echo "<pre>" . json_encode($updateLocationData, JSON_PRETTY_PRINT) . "</pre>";
            echo "</div>";
            
            $locationId = $primaryLocation['id'];
            $updateResponse = $client->request('PUT', $apiPath . '/' . $user['appika_customer_id'] . '/locations/' . $locationId, [
                'headers' => [
                    'X-api-key' => "{$apiKey}",
                    'Accept' => 'application/json',
                    'Content-Type' => 'application/json',
                ],
                'json' => $updateLocationData
            ]);
            
            $updateStatusCode = $updateResponse->getStatusCode();
            $updateBody = $updateResponse->getBody()->getContents();
            
            echo "<div style='background: " . ($updateStatusCode >= 200 && $updateStatusCode < 300 ? '#d4edda' : '#f8d7da') . "; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
            echo "<h4>" . ($updateStatusCode >= 200 && $updateStatusCode < 300 ? '✅ Location Update Successful!' : '❌ Location Update Failed!') . "</h4>";
            echo "<p><strong>Status Code:</strong> " . $updateStatusCode . "</p>";
            echo "<p><strong>Response:</strong> " . htmlspecialchars($updateBody) . "</p>";
            echo "</div>";
            
            if ($updateStatusCode >= 200 && $updateStatusCode < 300) {
                echo "<h3>🎉 Location Update Fix Successful!</h3>";
                echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 5px; color: #0c5460;'>";
                echo "<h4>What was fixed:</h4>";
                echo "<ul>";
                echo "<li>✅ Added required <code>loc_code</code> field</li>";
                echo "<li>✅ Added required <code>loc_name</code> field</li>";
                echo "<li>✅ Preserved existing location data</li>";
                echo "<li>✅ Fixed the HTTP 400 error</li>";
                echo "</ul>";
                echo "</div>";
                
                echo "<h4>🔗 Next Steps:</h4>";
                echo "<p>1. <a href='../front-end/profile.php' style='background: #473BF0; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px;'>Try updating your profile now</a></p>";
                echo "<p>2. The profile update should now work completely!</p>";
            }
            
        } else {
            echo "<p style='color: orange;'>⚠️ No primary location found. This might be why the update is failing.</p>";
        }
        
    } else {
        echo "<p style='color: red;'>❌ Failed to get locations: HTTP $locationsStatusCode - $locationsBody</p>";
    }
    
} catch (Exception $e) {
    echo "<h3>💥 Error Occurred:</h3>";
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; color: #721c24;'>";
    echo "<p><strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><strong>File:</strong> " . htmlspecialchars($e->getFile()) . "</p>";
    echo "<p><strong>Line:</strong> " . $e->getLine() . "</p>";
    echo "</div>";
}

echo "<hr>";
echo "<h3>🔗 Quick Links:</h3>";
echo "<p>";
echo "<a href='../front-end/profile.php' style='background: #473BF0; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px; margin-right: 10px;'>👤 Back to Profile</a>";
echo "<a href='show-profile-errors.php' style='background: #ffc107; color: black; padding: 8px 16px; text-decoration: none; border-radius: 4px;'>📋 Show Errors</a>";
echo "</p>";

echo "<style>";
echo "body { font-family: Arial, sans-serif; margin: 20px; }";
echo "h1, h2, h3 { color: #473BF0; }";
echo "pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; font-size: 12px; }";
echo "code { background: #f8f9fa; padding: 2px 4px; border-radius: 3px; }";
echo "</style>";
?>
