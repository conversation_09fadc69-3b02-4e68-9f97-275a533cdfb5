<?php
/**
 * Test script to demonstrate timezone functionality in receipts
 */

// Include required files
include_once(__DIR__ . '/server.php');
include_once(__DIR__ . '/send-purchase-receipt.php');

echo "<h2>🕐 Timezone Receipt Testing</h2>";

// Test timezone conversion function
function testTimezoneConversion() {
    echo "<h3>📅 Timezone Conversion Examples</h3>";
    
    // Sample UTC datetime (like what's stored in database)
    $utcDateTime = '2025-01-15 14:30:00'; // 2:30 PM UTC
    
    // Test different customer scenarios
    $testCustomers = [
        [
            'name' => 'Customer in Singapore',
            'timezone' => 'Asia/Singapore',
            'country' => 'Singapore'
        ],
        [
            'name' => 'Customer in New York',
            'timezone' => 'America/New_York',
            'country' => 'United States'
        ],
        [
            'name' => 'Customer in London',
            'timezone' => 'Europe/London',
            'country' => 'United Kingdom'
        ],
        [
            'name' => 'Customer in Tokyo',
            'timezone' => 'Asia/Tokyo',
            'country' => 'Japan'
        ],
        [
            'name' => 'Customer with no timezone (fallback)',
            'timezone' => null,
            'country' => null
        ]
    ];
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 20px 0;'>";
    echo "<tr style='background: #473BF0; color: white;'>";
    echo "<th style='padding: 10px;'>Customer</th>";
    echo "<th style='padding: 10px;'>Timezone</th>";
    echo "<th style='padding: 10px;'>UTC Time</th>";
    echo "<th style='padding: 10px;'>Local Time on Receipt</th>";
    echo "</tr>";
    
    foreach ($testCustomers as $customer) {
        $customerInfo = [
            'timezone' => $customer['timezone'],
            'country' => $customer['country'],
            'user_id' => null
        ];
        
        $localTime = formatPurchaseDateInUserTimezone($utcDateTime, $customerInfo);
        
        echo "<tr>";
        echo "<td style='padding: 10px;'>" . htmlspecialchars($customer['name']) . "</td>";
        echo "<td style='padding: 10px;'>" . htmlspecialchars($customer['timezone'] ?: 'Default (Asia/Singapore)') . "</td>";
        echo "<td style='padding: 10px;'>" . htmlspecialchars($utcDateTime . ' UTC') . "</td>";
        echo "<td style='padding: 10px; font-weight: bold; color: #473BF0;'>" . htmlspecialchars($localTime) . "</td>";
        echo "</tr>";
    }
    
    echo "</table>";
}

// Test with real database data
function testWithRealData() {
    global $conn;
    
    echo "<h3>📊 Real Customer Data Test</h3>";
    
    try {
        // Get a sample of real customers with their timezone info
        $stmt = $conn->prepare("
            SELECT u.first_name, u.email, u.timezone, u.country, u.id as user_id
            FROM user u
            WHERE u.email IS NOT NULL
            LIMIT 5
        ");
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result->num_rows > 0) {
            echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 20px 0;'>";
            echo "<tr style='background: #f0f0f0;'>";
            echo "<th style='padding: 10px;'>Customer</th>";
            echo "<th style='padding: 10px;'>Email</th>";
            echo "<th style='padding: 10px;'>Saved Timezone</th>";
            echo "<th style='padding: 10px;'>Country</th>";
            echo "<th style='padding: 10px;'>Receipt Date Example</th>";
            echo "</tr>";
            
            $sampleUTC = '2025-01-15 14:30:00';
            
            while ($row = $result->fetch_assoc()) {
                $customerName = trim($row['first_name'] ?? '');
                if (empty($customerName)) {
                    $customerName = 'N/A';
                }
                
                $localTime = formatPurchaseDateInUserTimezone($sampleUTC, $row);
                
                echo "<tr>";
                echo "<td style='padding: 10px;'>" . htmlspecialchars($customerName) . "</td>";
                echo "<td style='padding: 10px;'>" . htmlspecialchars($row['email']) . "</td>";
                echo "<td style='padding: 10px;'>" . htmlspecialchars($row['timezone'] ?: 'Not set') . "</td>";
                echo "<td style='padding: 10px;'>" . htmlspecialchars($row['country'] ?: 'Not set') . "</td>";
                echo "<td style='padding: 10px; font-weight: bold; color: #473BF0;'>" . htmlspecialchars($localTime) . "</td>";
                echo "</tr>";
            }
            
            echo "</table>";
        } else {
            echo "<p>No customer data found for testing.</p>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    }
}

// Run tests
testTimezoneConversion();
testWithRealData();

echo "<h3>✨ Key Benefits</h3>";
echo "<ul>";
echo "<li><strong>Automatic Detection:</strong> System automatically detects customer's timezone</li>";
echo "<li><strong>User-Friendly:</strong> Customers see purchase time in their local timezone</li>";
echo "<li><strong>Professional:</strong> Includes timezone abbreviation for clarity</li>";
echo "<li><strong>Fallback Safe:</strong> Uses default timezone if detection fails</li>";
echo "</ul>";

echo "<h3>🔧 How It Works</h3>";
echo "<ol>";
echo "<li>Purchase time is stored in UTC in the database</li>";
echo "<li>When generating receipt, system checks user's timezone preference</li>";
echo "<li>If no timezone saved, it detects from user's country</li>";
echo "<li>UTC time is converted to user's local time for display</li>";
echo "<li>Receipt shows local time with timezone abbreviation</li>";
echo "</ol>";

echo "<p><a href='test-receipt-email.php' style='color: #473BF0;'>← Back to Email Receipt Testing</a></p>";
?>
