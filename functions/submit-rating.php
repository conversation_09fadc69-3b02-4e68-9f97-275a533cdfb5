<?php
session_start();
include('server.php');

// Set JSON response header
header('Content-Type: application/json');

// Check if user is logged in
if (!isset($_SESSION['username'])) {
    echo json_encode(['success' => false, 'error' => 'User not logged in']);
    exit();
}

// Check if it's a POST request
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'error' => 'Invalid request method']);
    exit();
}

// Get user ID from session
$username = $_SESSION['username'];
$userQuery = "SELECT id FROM user WHERE username = ?";
$userStmt = $conn->prepare($userQuery);
$userStmt->bind_param("s", $username);
$userStmt->execute();
$userResult = $userStmt->get_result();
$user = $userResult->fetch_assoc();
$userStmt->close();

if (!$user) {
    echo json_encode(['success' => false, 'error' => 'User not found']);
    exit();
}

$user_id = $user['id'];

// Get and validate input data
$ticket_id = isset($_POST['ticket_id']) ? intval($_POST['ticket_id']) : 0;
$appika_ticket_id = isset($_POST['appika_ticket_id']) ? intval($_POST['appika_ticket_id']) : 0;
$is_appika_ticket = isset($_POST['is_appika_ticket']) ? intval($_POST['is_appika_ticket']) : 0;
$rating = isset($_POST['rating']) ? intval($_POST['rating']) : 0;
$comment = isset($_POST['comment']) ? trim($_POST['comment']) : '';

// Validate ticket ID (at least one must be present)
if ($ticket_id <= 0 && $appika_ticket_id <= 0) {
    echo json_encode(['success' => false, 'error' => 'Invalid ticket ID']);
    exit();
}

// Validate rating (1-5 stars)
if ($rating < 1 || $rating > 5) {
    echo json_encode(['success' => false, 'error' => 'Rating must be between 1 and 5 stars']);
    exit();
}

// Validate comment length (optional but if provided, should be reasonable)
if (strlen($comment) > 1000) {
    echo json_encode(['success' => false, 'error' => 'Comment is too long (maximum 1000 characters)']);
    exit();
}

if ($is_appika_ticket) {
    // Appika ticket: check if ticket exists via Appika API (skip local DB check)
    // Only allow rating if status is closed/resolved (should be checked on frontend, but double-check here if needed)
    // Check if user has already rated this Appika ticket
    $existingRatingQuery = "SELECT id FROM ticket_ratings WHERE appika_ticket_id = ? AND user_id = ? AND is_appika_ticket = 1";
    $existingStmt = $conn->prepare($existingRatingQuery);
    $existingStmt->bind_param("ii", $appika_ticket_id, $user_id);
    $existingStmt->execute();
    $existingResult = $existingStmt->get_result();
    $existingRating = $existingResult->fetch_assoc();
    $existingStmt->close();

    try {
        if ($existingRating) {
            // Prevent updates - rating is one-time only
            echo json_encode([
                'success' => false,
                'error' => 'You have already rated this ticket. Ratings cannot be changed once submitted.'
            ]);
        } else {
            // Insert new rating for Appika ticket
            $insertQuery = "INSERT INTO ticket_ratings (appika_ticket_id, user_id, rating, comment, is_appika_ticket) VALUES (?, ?, ?, ?, 1)";
            $insertStmt = $conn->prepare($insertQuery);
            $insertStmt->bind_param("iiis", $appika_ticket_id, $user_id, $rating, $comment);
            $success = $insertStmt->execute();
            $insertStmt->close();

            if ($success) {
                echo json_encode([
                    'success' => true,
                    'message' => 'Rating submitted successfully! Thank you for your feedback.',
                    'action' => 'created'
                ]);
            } else {
                echo json_encode(['success' => false, 'error' => 'Failed to submit rating']);
            }
        }
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'error' => 'Database error: ' . $e->getMessage()]);
    }
    $conn->close();
    exit();
} else {
    // Local ticket logic (existing code)
    // Check if ticket exists and belongs to user
    $ticketQuery = "SELECT id, status FROM support_tickets WHERE id = ? AND user_id = ?";
    $ticketStmt = $conn->prepare($ticketQuery);
    $ticketStmt->bind_param("ii", $ticket_id, $user_id);
    $ticketStmt->execute();
    $ticketResult = $ticketStmt->get_result();
    $ticket = $ticketResult->fetch_assoc();
    $ticketStmt->close();

    if (!$ticket) {
        echo json_encode(['success' => false, 'error' => 'Ticket not found or access denied']);
        exit();
    }

    // Check if ticket status allows rating (only resolved or closed tickets)
    if (!in_array($ticket['status'], ['resolved', 'closed'])) {
        echo json_encode(['success' => false, 'error' => 'Only resolved or closed tickets can be rated']);
        exit();
    }

    // Check if user has already rated this ticket
    $existingRatingQuery = "SELECT id FROM ticket_ratings WHERE ticket_id = ? AND user_id = ?";
    $existingStmt = $conn->prepare($existingRatingQuery);
    $existingStmt->bind_param("ii", $ticket_id, $user_id);
    $existingStmt->execute();
    $existingResult = $existingStmt->get_result();
    $existingRating = $existingResult->fetch_assoc();
    $existingStmt->close();

    try {
        if ($existingRating) {
            // Prevent updates - rating is one-time only
            echo json_encode([
                'success' => false,
                'error' => 'You have already rated this ticket. Ratings cannot be changed once submitted.'
            ]);
        } else {
            // Insert new rating
            $insertQuery = "INSERT INTO ticket_ratings (ticket_id, user_id, rating, comment) VALUES (?, ?, ?, ?)";
            $insertStmt = $conn->prepare($insertQuery);
            $insertStmt->bind_param("iiis", $ticket_id, $user_id, $rating, $comment);
            $success = $insertStmt->execute();
            $insertStmt->close();

            if ($success) {
                echo json_encode([
                    'success' => true,
                    'message' => 'Rating submitted successfully! Thank you for your feedback.',
                    'action' => 'created'
                ]);
            } else {
                echo json_encode(['success' => false, 'error' => 'Failed to submit rating']);
            }
        }
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'error' => 'Database error: ' . $e->getMessage()]);
    }
}

$conn->close();
?>
