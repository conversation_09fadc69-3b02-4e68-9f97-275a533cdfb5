<?php
/**
 * Quick test to verify the email receipt fix is working
 */

include_once(__DIR__ . '/server.php');
include_once(__DIR__ . '/send-purchase-receipt.php');

echo "<h2>🔧 Email Receipt Fix Test</h2>";

// Test parameters
$test_transaction_id = isset($_GET['transaction_id']) ? $_GET['transaction_id'] : 'test_transaction_123';
$test_email = isset($_GET['email']) ? $_GET['email'] : '<EMAIL>';

echo "<div style='background: #f0f8ff; padding: 15px; border: 1px solid #0066cc; margin: 20px 0;'>";
echo "<h3>🧪 Testing Email Receipt Function</h3>";
echo "<p><strong>Transaction ID:</strong> " . htmlspecialchars($test_transaction_id) . "</p>";
echo "<p><strong>Email:</strong> " . htmlspecialchars($test_email) . "</p>";
echo "</div>";

// Test the function
echo "<h3>📧 Function Test Result:</h3>";

try {
    // Test if the function can be called without errors
    $result = sendPurchaseReceiptEmail($test_transaction_id, $test_email);
    
    if ($result === false) {
        echo "<div style='color: orange; padding: 10px; border: 1px solid orange; background: #fff8e1;'>";
        echo "⚠️ <strong>Function executed but returned false</strong><br>";
        echo "This is expected if the transaction ID doesn't exist in the database.<br>";
        echo "The important thing is that no PHP errors occurred.";
        echo "</div>";
    } else {
        echo "<div style='color: green; padding: 10px; border: 1px solid green; background: #f0fff0;'>";
        echo "✅ <strong>SUCCESS!</strong> Function executed and returned true.<br>";
        echo "Email should have been sent successfully.";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div style='color: red; padding: 10px; border: 1px solid red; background: #fff0f0;'>";
    echo "❌ <strong>ERROR!</strong> " . htmlspecialchars($e->getMessage());
    echo "</div>";
}

// Test timezone function specifically
echo "<h3>🕐 Timezone Function Test:</h3>";

try {
    $test_utc_time = '2025-01-15 14:30:00';
    $formatted_time = showCustomerTime($test_utc_time, 'F j, Y \a\t g:i A T');
    
    echo "<div style='color: green; padding: 10px; border: 1px solid green; background: #f0fff0;'>";
    echo "✅ <strong>Timezone function working!</strong><br>";
    echo "UTC Time: " . htmlspecialchars($test_utc_time) . "<br>";
    echo "Formatted: " . htmlspecialchars($formatted_time);
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='color: red; padding: 10px; border: 1px solid red; background: #fff0f0;'>";
    echo "❌ <strong>Timezone Error!</strong> " . htmlspecialchars($e->getMessage());
    echo "</div>";
}

// Show recent transactions for real testing
echo "<h3>📋 Real Transaction Testing</h3>";

try {
    $stmt = $conn->prepare("
        SELECT DISTINCT pt.transactionid, pt.purchase_time, u.email, u.first_name
        FROM purchasetickets pt 
        LEFT JOIN user u ON pt.username = u.username 
        WHERE pt.transactionid IS NOT NULL AND u.email IS NOT NULL
        ORDER BY pt.purchase_time DESC 
        LIMIT 5
    ");
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        echo "<p>Click on any transaction below to test with real data:</p>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f0f0f0;'>";
        echo "<th style='padding: 8px;'>Transaction ID</th>";
        echo "<th style='padding: 8px;'>Date</th>";
        echo "<th style='padding: 8px;'>Customer</th>";
        echo "<th style='padding: 8px;'>Email</th>";
        echo "<th style='padding: 8px;'>Test</th>";
        echo "</tr>";
        
        while ($row = $result->fetch_assoc()) {
            $customer_name = trim($row['first_name'] ?? '');
            if (empty($customer_name)) {
                $customer_name = 'N/A';
            }
            
            echo "<tr>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars(substr($row['transactionid'], -12)) . "</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($row['purchase_time']) . "</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($customer_name) . "</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($row['email']) . "</td>";
            echo "<td style='padding: 8px;'>";
            echo "<a href='?transaction_id=" . urlencode($row['transactionid']) . "&email=" . urlencode($row['email']) . "' style='color: blue;'>Test This</a>";
            echo "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>No transactions with email addresses found.</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>Error fetching transactions: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "<h3>✅ What Was Fixed:</h3>";
echo "<div style='background: #f8f9fa; padding: 15px; border: 1px solid #ddd;'>";
echo "<p><strong>Problem:</strong> Custom timezone function was causing PHP errors that prevented emails from being sent.</p>";
echo "<p><strong>Solution:</strong> Replaced custom function with your existing <code>showCustomerTime()</code> function.</p>";
echo "<p><strong>Result:</strong> Email receipts should now work properly with timezone support!</p>";
echo "</div>";

echo "<h3>🎯 Next Steps:</h3>";
echo "<ol>";
echo "<li>Test with a real purchase to confirm emails are being sent</li>";
echo "<li>Check your email inbox (and spam folder)</li>";
echo "<li>Monitor server error logs for any remaining issues</li>";
echo "</ol>";

echo "<p><a href='test-receipt-email.php' style='color: blue;'>← Back to Main Testing</a></p>";
?>
