<?php
session_start();
include('server.php');

// Set content type to JSON
header('Content-Type: application/json');

// Initialize response
$response = array('success' => false, 'message' => '');

// Check if request is POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    $response['message'] = 'Invalid request method.';
    echo json_encode($response);
    exit();
}

$action = isset($_POST['action']) ? $_POST['action'] : '';

if ($action === 'send_2fa') {
$email = isset($_POST['email']) ? trim($_POST['email']) : '';
    if (empty($email) || !filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $response['message'] = 'Please enter a valid email address.';
        echo json_encode($response);
        exit();
    }

    // Check for 60-second cooldown
    $current_time = time();
    $cooldown_duration = 60; // 60 seconds

    if (isset($_SESSION['last_2fa_send_time'])) {
        $time_since_last_send = $current_time - $_SESSION['last_2fa_send_time'];
        if ($time_since_last_send < $cooldown_duration) {
            $remaining_time = $cooldown_duration - $time_since_last_send;
            $response['message'] = "Please wait {$remaining_time} seconds before requesting another 2FA code.";
            $response['cooldown_remaining'] = $remaining_time;
            echo json_encode($response);
            exit();
        }
    }

    $email_escaped = mysqli_real_escape_string($conn, $email);
    $check_query = "SELECT id FROM user WHERE email = '$email_escaped' LIMIT 1";
    $result = mysqli_query($conn, $check_query);
    if (!$result || mysqli_num_rows($result) === 0) {
        $response['message'] = 'No account found with this email address.';
        echo json_encode($response);
        exit();
    }
    $code = rand(100000, 999999);
    $_SESSION['reset_2fa_code'] = $code;
    $_SESSION['reset_2fa_email'] = $email;
    $_SESSION['last_2fa_send_time'] = $current_time; // Store the send time

    $subject = 'Your HelloIT Password Reset 2FA Code';
    $message = "Your HelloIT password reset verification code is: <b>$code</b><br><br>If you did not request this, please ignore this email.";
    $headers = "From: <EMAIL>\r\n";
    $headers .= "MIME-Version: 1.0\r\nContent-type: text/html; charset=UTF-8\r\n";
    if (mail($email, $subject, $message, $headers)) {
        $response['success'] = true;
        $response['message'] = 'A 2FA code has been sent to your email.';
    } else {
        $response['message'] = 'Failed to send 2FA code. Please try again.';
    }
    echo json_encode($response);
    exit();
}

if ($action === 'reset_password') {
    $email = isset($_POST['email']) ? trim($_POST['email']) : '';
    $code = isset($_POST['code']) ? trim($_POST['code']) : '';
    $new_password = isset($_POST['new_password']) ? $_POST['new_password'] : '';
    if (empty($email) || !filter_var($email, FILTER_VALIDATE_EMAIL)) {
    $response['message'] = 'Please enter a valid email address.';
    echo json_encode($response);
    exit();
}
    if (empty($code) || !isset($_SESSION['reset_2fa_code']) || !isset($_SESSION['reset_2fa_email']) || $email !== $_SESSION['reset_2fa_email'] || $code !== strval($_SESSION['reset_2fa_code'])) {
        $response['message'] = 'Invalid or expired 2FA code.';
    echo json_encode($response);
    exit();
}
    if (empty($new_password) || strlen($new_password) < 6) {
    $response['message'] = 'Password must be at least 6 characters long.';
    echo json_encode($response);
    exit();
}
    $email_escaped = mysqli_real_escape_string($conn, $email);
    $check_query = "SELECT id, username, password FROM user WHERE email = '$email_escaped' LIMIT 1";
    $result = mysqli_query($conn, $check_query);
    if (!$result || mysqli_num_rows($result) === 0) {
        $response['message'] = 'No account found with this email address.';
        echo json_encode($response);
        exit();
    }
    $user = mysqli_fetch_assoc($result);
    $user_id = $user['id'];
    $username = $user['username'];
    $current_password = $user['password'];
    $new_password_hash = (strpos($current_password, '$2y$') === 0 || strpos($current_password, '$2a$') === 0)
        ? password_hash($new_password, PASSWORD_DEFAULT)
        : md5($new_password);
    $new_password_escaped = mysqli_real_escape_string($conn, $new_password_hash);
    $update_query = "UPDATE user SET password = '$new_password_escaped' WHERE id = $user_id";
    if (mysqli_query($conn, $update_query)) {
        unset($_SESSION['reset_2fa_code'], $_SESSION['reset_2fa_email']);
        $log_message = date('Y-m-d H:i:s') . " - Password reset for user: $username (ID: $user_id, Email: $email) via 2FA" . PHP_EOL;
        file_put_contents(__DIR__ . '/password_reset.log', $log_message, FILE_APPEND | LOCK_EX);
        $response['success'] = true;
        $response['message'] = 'Password has been successfully reset! You can now sign in with your new password.';
    } else {
        $response['message'] = 'Failed to update password. Please try again.';
    }
    echo json_encode($response);
    exit();
}

$response['message'] = 'Invalid action.';
echo json_encode($response);
exit();
?>
