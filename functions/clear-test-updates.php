<?php
/**
 * Clear Test Updates - Remove test sync entries from database
 */

include('server.php');

echo "<h2>🧹 Clear Test Updates</h2>";
echo "<p>This will remove test sync entries from the database to clean up the 'Recent Appika Updates' widget.</p>";

if (isset($_POST['clear_updates'])) {
    $cleared = 0;
    
    // Clear appika_updated_at for recent test entries (last 2 hours)
    $query1 = "UPDATE support_tickets 
               SET appika_updated_at = NULL, 
                   appika_update_source = NULL 
               WHERE appika_updated_at >= DATE_SUB(NOW(), INTERVAL 2 HOUR)";
    
    if (mysqli_query($conn, $query1)) {
        $cleared += mysqli_affected_rows($conn);
        echo "<div style='background: #d4edda; color: #155724; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
        echo "✅ Cleared $cleared test update entries from support_tickets table";
        echo "</div>";
    }
    
    // Clear recent sync log entries (last 2 hours)
    $query2 = "DELETE FROM appika_sync_log 
               WHERE created_at >= DATE_SUB(NOW(), INTERVAL 2 HOUR)
               AND operation_type LIKE '%test%'";
    
    if (mysqli_query($conn, $query2)) {
        $logCleared = mysqli_affected_rows($conn);
        echo "<div style='background: #d4edda; color: #155724; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
        echo "✅ Cleared $logCleared test entries from appika_sync_log table";
        echo "</div>";
    }
    
    echo "<div style='background: #d1ecf1; color: #0c5460; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
    echo "<h4>✅ Cleanup Complete!</h4>";
    echo "<p>The 'Recent Appika Updates' widget should now be clean.</p>";
    echo "<a href='../merlion/admin-tickets.php' style='background: #28a745; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>Back to Admin Tickets</a>";
    echo "</div>";
    
} else {
    // Show current test entries
    $query = "SELECT id, subject, appika_updated_at, appika_update_source 
              FROM support_tickets 
              WHERE appika_updated_at >= DATE_SUB(NOW(), INTERVAL 2 HOUR)
              ORDER BY appika_updated_at DESC";
    
    $result = mysqli_query($conn, $query);
    $testEntries = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $testEntries[] = $row;
    }
    
    if (empty($testEntries)) {
        echo "<div style='background: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
        echo "<h4>✅ No Test Entries Found</h4>";
        echo "<p>The database is already clean. No recent test updates found.</p>";
        echo "<a href='../merlion/admin-tickets.php' style='background: #28a745; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>Back to Admin Tickets</a>";
        echo "</div>";
    } else {
        echo "<div style='background: #fff3cd; color: #856404; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
        echo "<h4>⚠️ Test Entries Found</h4>";
        echo "<p>Found " . count($testEntries) . " recent test update entries:</p>";
        echo "<ul>";
        foreach ($testEntries as $entry) {
            echo "<li>Ticket #{$entry['id']}: {$entry['subject']} (Updated: {$entry['appika_updated_at']})</li>";
        }
        echo "</ul>";
        echo "</div>";
        
        echo "<form method='POST' style='margin: 20px 0;'>";
        echo "<button type='submit' name='clear_updates' style='background: #dc3545; color: white; padding: 12px 20px; border: none; border-radius: 5px; cursor: pointer; font-size: 16px;'>";
        echo "🧹 Clear Test Updates";
        echo "</button>";
        echo "</form>";
        
        echo "<div style='background: #f8d7da; color: #721c24; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
        echo "<strong>Note:</strong> This will only clear entries from the last 2 hours (test entries).";
        echo "</div>";
    }
}
?>

<style>
body { 
    font-family: Arial, sans-serif; 
    margin: 20px; 
    line-height: 1.6; 
    background-color: #f8f9fa;
}
h2, h4 { color: #333; }
</style>
