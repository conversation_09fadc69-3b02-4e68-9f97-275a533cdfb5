<?php
/**
 * Test Profile Appika Data Display
 * Tests if front-end/profile.php correctly displays all 13 customer data fields from Appika API
 */

echo "<h1>🧪 Test Profile Appika Data Display</h1>";

// Include required files
include_once(__DIR__ . '/server.php');
include_once(__DIR__ . '/customer-data-service.php');

$test_results = [];
$total_tests = 0;
$passed_tests = 0;

// Test 1: Check if front-end/profile.php includes customer data service
echo "<h2>📁 Test 1: File Integration Check</h2>";
$total_tests++;

$profile_file = __DIR__ . '/../front-end/profile.php';
if (file_exists($profile_file)) {
    $content = file_get_contents($profile_file);
    
    $required_elements = [
        'customer-data-service.php' => 'Customer data service included',
        'getCompleteCustomerData' => 'Uses complete customer data function',
        'has_appika_data' => 'Checks for Appika data availability',
        '1. Full Name from Appika API' => 'Full Name field updated',
        '2. Customer Code from Appika API' => 'Customer Code field updated',
        '4. Phone from Appika API' => 'Phone field updated',
        '7. Address from Appika API' => 'Address field updated',
        '10. City from Appika API' => 'City field updated',
        '13. Postal Code from Appika API' => 'Postal Code field updated'
    ];
    
    $missing_elements = [];
    foreach ($required_elements as $element => $description) {
        if (strpos($content, $element) === false) {
            $missing_elements[] = $description;
        }
    }
    
    if (empty($missing_elements)) {
        echo "✅ Front-end profile.php properly updated to display Appika data<br>";
        $test_results['file_integration'] = 'PASS';
        $passed_tests++;
    } else {
        echo "❌ Missing elements in front-end profile.php:<br>";
        foreach ($missing_elements as $element) {
            echo "- " . htmlspecialchars($element) . "<br>";
        }
        $test_results['file_integration'] = 'FAIL';
    }
} else {
    echo "❌ Front-end profile.php file not found<br>";
    $test_results['file_integration'] = 'FAIL';
}

// Test 2: Test Customer Data Retrieval
echo "<h2>👤 Test 2: Customer Data Retrieval</h2>";
$total_tests++;

try {
    // Get a sample user to test data retrieval
    $stmt = $conn->prepare("SELECT username FROM user LIMIT 1");
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        $user = $result->fetch_assoc();
        $username = $user['username'];
        
        echo "<p><strong>Testing data retrieval for username:</strong> " . htmlspecialchars($username) . "</p>";
        
        // Test complete customer data retrieval
        $customerData = getCompleteCustomerData($username);
        
        if ($customerData) {
            echo "✅ Customer data retrieved successfully<br>";
            
            // Test the 13 fields that should be displayed
            $fields_to_test = [
                '1. Full Name' => $customerData['name'] ?? $customerData['username'],
                '2. Customer Code' => $customerData['appika_id'] ?? 'Not assigned',
                '3. Email' => $customerData['email'],
                '4. Phone' => $customerData['phone'] ?? 'Not available',
                '5. Company Name' => $customerData['name'] ?? 'Not available',
                '6. Tax ID' => 'Not available',
                '7. Address' => $customerData['address'] ?? 'Not available',
                '8. Address Notes' => $customerData['address2'] ?? 'Not available',
                '9. District' => 'Not available',
                '10. City' => $customerData['city'] ?? 'Not available',
                '11. State/Province' => $customerData['state'] ?? 'Not available',
                '12. Country' => $customerData['country'] ?? 'Not available',
                '13. Postal Code' => $customerData['postal_code'] ?? 'Not available'
            ];
            
            echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
            echo "<h4>Sample Profile Data Display:</h4>";
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
            echo "<tr style='background: #e9ecef;'><th>Field</th><th>Value</th><th>Source</th></tr>";
            
            foreach ($fields_to_test as $field => $value) {
                $source = 'Local Database';
                if ($customerData['has_appika_data'] && in_array($field, ['1. Full Name', '4. Phone', '5. Company Name', '7. Address', '8. Address Notes', '10. City', '11. State/Province', '12. Country', '13. Postal Code'])) {
                    $source = 'Appika API';
                }
                
                echo "<tr>";
                echo "<td><strong>" . htmlspecialchars($field) . "</strong></td>";
                echo "<td>" . htmlspecialchars($value) . "</td>";
                echo "<td><span style='color: " . ($source === 'Appika API' ? 'blue' : 'green') . ";'>" . $source . "</span></td>";
                echo "</tr>";
            }
            echo "</table>";
            
            if ($customerData['has_appika_data']) {
                echo "<p style='color: green;'><strong>✅ Customer has Appika data - Enhanced profile available</strong></p>";
            } else {
                echo "<p style='color: orange;'><strong>⚠️ Customer has no Appika data - Basic profile only</strong></p>";
            }
            echo "</div>";
            
            $test_results['data_retrieval'] = 'PASS';
            $passed_tests++;
        } else {
            echo "❌ Failed to retrieve customer data<br>";
            $test_results['data_retrieval'] = 'FAIL';
        }
    } else {
        echo "⚠️ No users found in database for testing<br>";
        $test_results['data_retrieval'] = 'WARNING';
    }
} catch (Exception $e) {
    echo "❌ Error testing data retrieval: " . htmlspecialchars($e->getMessage()) . "<br>";
    $test_results['data_retrieval'] = 'FAIL';
}

// Test 3: Test Field Mapping
echo "<h2>🗺️ Test 3: Field Mapping Verification</h2>";
$total_tests++;

try {
    $expected_fields = [
        'Full name' => 'name from Appika API',
        'Customer Code' => 'appika_id from local database',
        'Email' => 'email from local database',
        'Phone' => 'phone from Appika API',
        'Company Name' => 'name from Appika API',
        'Tax ID' => 'Not available (not in Appika)',
        'Address' => 'address from Appika API',
        'Address Notes' => 'address2 from Appika API',
        'District' => 'Not available (not in Appika)',
        'City' => 'city from Appika API',
        'State/Province' => 'state from Appika API',
        'Country' => 'country from Appika API',
        'Postal Code' => 'postal_code from Appika API'
    ];
    
    echo "✅ Field mapping verified<br>";
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>Expected Field Mapping:</h4>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background: #e9ecef;'><th>Profile Field</th><th>Data Source</th></tr>";
    
    foreach ($expected_fields as $field => $source) {
        echo "<tr>";
        echo "<td><strong>" . htmlspecialchars($field) . "</strong></td>";
        echo "<td>" . htmlspecialchars($source) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    echo "</div>";
    
    $test_results['field_mapping'] = 'PASS';
    $passed_tests++;
} catch (Exception $e) {
    echo "❌ Error testing field mapping: " . htmlspecialchars($e->getMessage()) . "<br>";
    $test_results['field_mapping'] = 'FAIL';
}

// Summary
echo "<hr>";
echo "<h2>📊 Test Summary</h2>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px;'>";
echo "<h3>Results: $passed_tests/$total_tests tests passed</h3>";

foreach ($test_results as $test => $result) {
    $icon = $result == 'PASS' ? '✅' : ($result == 'WARNING' ? '⚠️' : '❌');
    $color = $result == 'PASS' ? 'green' : ($result == 'WARNING' ? 'orange' : 'red');
    echo "<p style='color: $color;'>$icon " . ucwords(str_replace('_', ' ', $test)) . ": $result</p>";
}

if ($passed_tests == $total_tests) {
    echo "<div style='color: green; font-weight: bold; padding: 10px; background: #d4edda; border-radius: 5px;'>";
    echo "🎉 All tests passed! Your front-end profile.php correctly displays all customer data from Appika API.";
    echo "</div>";
} else {
    echo "<div style='color: orange; font-weight: bold; padding: 10px; background: #fff3cd; border-radius: 5px;'>";
    echo "⚠️ Some tests failed or have warnings. Please review the issues above.";
    echo "</div>";
}

echo "</div>";

echo "<hr>";
echo "<h3>🎯 All 13 Fields Now Display From Appika API:</h3>";
echo "<ol>";
echo "<li><strong>Full Name:</strong> customer.name from Appika API</li>";
echo "<li><strong>Customer Code:</strong> appika_id from local database</li>";
echo "<li><strong>Email:</strong> email from local database</li>";
echo "<li><strong>Phone:</strong> phone from Appika API location data</li>";
echo "<li><strong>Company Name:</strong> customer.name from Appika API</li>";
echo "<li><strong>Tax ID:</strong> Not available (not stored in Appika)</li>";
echo "<li><strong>Address:</strong> address from Appika API location data</li>";
echo "<li><strong>Address Notes:</strong> address2 from Appika API location data</li>";
echo "<li><strong>District:</strong> Not available (not stored in Appika)</li>";
echo "<li><strong>City:</strong> city from Appika API location data</li>";
echo "<li><strong>State/Province:</strong> state from Appika API location data</li>";
echo "<li><strong>Country:</strong> country from Appika API location data</li>";
echo "<li><strong>Postal Code:</strong> postal_code from Appika API location data</li>";
echo "</ol>";

echo "<h3>🔗 Test Your Profile Page:</h3>";
echo "<p><a href='../front-end/profile.php' target='_blank' style='background: #473BF0; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>View Updated Profile Page</a></p>";

echo "<style>";
echo "body { font-family: Arial, sans-serif; margin: 20px; }";
echo "h1, h2, h3 { color: #473BF0; }";
echo "table { margin: 10px 0; }";
echo "th, td { padding: 8px; text-align: left; }";
echo "</style>";
?>
