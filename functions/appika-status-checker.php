<?php
/**
 * Appika Status Checker
 * Quick function to check if Appika APIs are accessible
 */

function checkAppikaStatus() {
    $endpoints = [
        'ticket' => 'https://dev-sgsg-tktapi.appika.com/graphql',
        'customer' => 'https://dev-api-pooh-sgsg.appika.com/contact/customers'
    ];

    $results = [];

    foreach ($endpoints as $type => $url) {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10); // Increased timeout
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 5); // Increased connection timeout
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_USERAGENT, 'HelloIT Status Checker');

        // For GraphQL endpoint, send a simple query
        if ($type === 'ticket') {
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Content-Type: application/json',
                'Accept: application/json'
            ]);
            // Simple introspection query
            $query = json_encode(['query' => '{ __schema { queryType { name } } }']);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $query);
        }

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        // More lenient online detection
        $isOnline = false;
        if (!$error) {
            if ($httpCode >= 200 && $httpCode < 500) {
                // Accept any response from 200-499 as "online"
                // Even 400-499 errors mean the server is responding
                $isOnline = true;
            }
        }

        $results[$type] = [
            'online' => $isOnline,
            'error' => $error,
            'http_code' => $httpCode,
            'response_length' => strlen($response)
        ];
    }

    return $results;
}

function isAppikaOnline() {
    // Auto-detect server status - no manual override needed

    $status = checkAppikaStatus();

    // Count how many APIs are working
    $onlineCount = 0;
    $totalCount = count($status);

    foreach ($status as $type => $api) {
        if ($api['online']) {
            $onlineCount++;
        }
    }

    // Consider system online if at least one critical API is working
    // But be more strict - if both are down, definitely show maintenance
    $isOnline = $onlineCount > 0;

    // Log the decision
    error_log("APPIKA_STATUS: $onlineCount/$totalCount APIs online - System status: " . ($isOnline ? 'ONLINE' : 'OFFLINE'));

    // If no APIs are working, definitely offline
    if ($onlineCount === 0) {
        error_log("APPIKA_STATUS: All APIs down - MAINTENANCE MODE ACTIVATED");
        return false;
    }

    return $isOnline;
}

function getAppikaStatusMessage() {
    $status = checkAppikaStatus();

    $onlineCount = 0;
    $totalCount = count($status);

    foreach ($status as $api) {
        if ($api['online']) {
            $onlineCount++;
        }
    }

    if ($onlineCount === $totalCount) {
        return "All systems operational";
    } elseif ($onlineCount > 0) {
        return "Partial service disruption";
    } else {
        return "Service temporarily unavailable";
    }
}

// If this file is called directly, return JSON status
if (basename($_SERVER['PHP_SELF']) === 'appika-status-checker.php') {
    header('Content-Type: application/json');
    echo json_encode([
        'online' => isAppikaOnline(),
        'message' => getAppikaStatusMessage(),
        'details' => checkAppikaStatus(),
        'timestamp' => date('c')
    ]);
    exit();
}
?>
