<?php
/**
 * Auto Cleanup Script
 * Automatically deletes unused files and clears log files (keeps empty log files)
 * This runs immediately without user interaction
 */

echo "🗑️ Starting Auto Cleanup...\n\n";

// Files to delete completely
$filesToDelete = [
    // Root test files
    'test-admin-phone-update.php',
    'test-admin-user-creation.php',
    'test-api-status.php',
    'test-appika-api.php',
    'test-appika-send-message.php',
    'test-composer-installation.php',
    'test-create-customer-minimal-fix.php',
    'test-guzzle-working.php',
    'test-maintenance-toggle.php',
    'test-maintenance.php',
    'test-name-display.php',
    'test-path-fix.php',
    'test-profile-maintenance.php',
    'test-server-environment.php',
    'test-ticket-update.php',
    'test-user-creation-context.php',
    'test-user-creation.php',
    
    // Root debug files
    'debug-appika-detailed.php',
    'debug-status.php',
    'debug-user-creation.php',
    
    // Debug directory
    'debug/check-table-structure.php',
    'debug/insert-expired-tickets.php',
    'debug/test-expiration-logs.php',
    'debug/test-expiration-process.php',
    
    // Functions test files
    'functions/debug-multiple-items.php',
    'functions/debug-profile-update.php',
    'functions/test-appika-debug.php',
    'functions/test-appika-status.php',
    'functions/test-email-fix.php',
    'functions/test-email-receipt-fix.php',
    'functions/test-email-server.php',
    'functions/test-location-fix.php',
    'functions/test-minimal-customer-integration.php',
    'functions/test-payment-success-fix.php',
    'functions/test-profile-appika-display.php',
    'functions/test-profile-edit-appika.php',
    'functions/test-profile-fix.php',
    'functions/test-profile-updates.php',
    'functions/test-receipt-email.php',
    'functions/test-timezone-detection.php',
    'functions/test-timezone-receipt.php',
    'functions/test-updated-customer-creation.php',
    
    // Front-end debug/test files
    'front-end/debug-cart.php',
    'front-end/debug-expiration-logs.php',
    'front-end/debug-message-api.php',
    'front-end/debug-notifications.php',
    'front-end/test-cleanup.php',
    'front-end/test-direct-simple.php',
    'front-end/test-expiration.php',
    'front-end/test-login.php',
    'front-end/test-payment-methods.php',
    'front-end/test-payment-redirect.php',
    'front-end/test-session.php',
    
    // Merlion debug/test files
    'merlion/chat-diagnostic.php',
    'merlion/create-expired-test-tickets.php',
    'merlion/debug-expiration.php',
    'merlion/debug-ticket-issue.php',
    'merlion/debug-ticket-notifications.php',
    'merlion/test-appika-api.php',
    'merlion/test-sync-disabled.php',
    'merlion/test-ticket-expiration.php',
    'merlion/test-ticket-update.php',
    'merlion/real-time-ticket-monitor.php',
    
    // Fix/Setup files (one-time use)
    'fix-composer.bat',
    'fix-composer.sh',
    'fix-customer-locations.php',
    'fix-guzzle-loading.php',
    'diagnose-guzzle-issue.php',
    'reinstall-dependencies.php',
    'server-diagnostic.php',
    'setup-company-tax-fields.php',
    'migrate_chat_direct.php',
    'check-all-users-sync.php',
    'check-appika-status.php',
    'merlion/fix-cleanup-config.php',
    'merlion/fix-missing-locations.php',
    'merlion/fix-ticket-expiration.php',
    'merlion/fix-units.php',
    'merlion/force-update-expiration.php',
    'functions/clear-test-updates.php',
    'functions/migrate-to-minimal-database.php',
    'functions/process-pending-users.php',
    'functions/remove-admin-notifications-table.php',
    'functions/update-admin-notifications-code.php',
    'functions/update-guest-credentials.php',
    'functions/update-payment-temp-table.php',
    'database/setup-ticket-expiration.php',
    'database/update-ticket-expiration-units.php',
    
    // Documentation files
    'EMAIL_RECEIPT_IMPLEMENTATION.md',
    'GUZZLE-ISSUE-SOLUTION.md',
    'front-end/EMAIL_SETUP_GUIDE.md',
    'docs/ticket-expiration-system.md',
    'graphql-ticket/README.md',
    'graphql-ticket/ticket_schema_example.md',
    'api/README.md',
    
    // Unused/Legacy files
    'front-end/index.html',
    'front-end/profile-minimal.php',
    'front-end/ticket-detail-new.php',
    'front-end/ticket-chat-local-api.php',
    'front-end/ticket-messages-direct-api.php',
    'front-end/ticket-messages-direct.php',
    'merlion/admin-chat-simple.php',
    'merlion/admin-chat.php',
    'merlion/admin-tickets-new.php',
    'header-footer/newnavtest.php',
    'form_submission_log.txt',
    'functions/password_reset.log',
    'merlion/ticket_status_update.log'
];

// Log files to clear (keep file but make empty)
$logFilesToClear = [
    'logs/api_key_changes.log',
    'logs/api_key_updates.log',
    'logs/appika_api.log',
    'logs/appika_api_admin.log',
    'logs/appika_api_update.log',
    'logs/appika_debug.log',
    'logs/appika_sync.log',
    'logs/email-fallback.log',
    'logs/ticket_api.log',
    'logs/ticket_consumption.log',
    'logs/ticket_logs_delete.log',
    'logs/ticket_update_debug.log'
];

$deletedCount = 0;
$deletedSize = 0;
$clearedCount = 0;
$clearedSize = 0;
$errors = [];

echo "📁 Deleting unused files...\n";

// Delete files completely
foreach ($filesToDelete as $file) {
    if (file_exists($file)) {
        $size = filesize($file);
        if (unlink($file)) {
            $deletedCount++;
            $deletedSize += $size;
            echo "✅ Deleted: $file (" . formatBytes($size) . ")\n";
        } else {
            $errors[] = "Failed to delete: $file";
            echo "❌ Failed: $file\n";
        }
    } else {
        echo "⚪ Not found: $file\n";
    }
}

echo "\n📝 Clearing log files (keeping empty files)...\n";

// Clear log files but keep them as empty files
foreach ($logFilesToClear as $file) {
    if (file_exists($file)) {
        $size = filesize($file);
        if (file_put_contents($file, '') !== false) {
            $clearedCount++;
            $clearedSize += $size;
            echo "✅ Cleared: $file (" . formatBytes($size) . ")\n";
        } else {
            $errors[] = "Failed to clear: $file";
            echo "❌ Failed: $file\n";
        }
    } else {
        echo "⚪ Not found: $file\n";
    }
}

echo "\n🎉 AUTO CLEANUP COMPLETE!\n";
echo "==========================================\n";
echo "📊 SUMMARY:\n";
echo "• Files deleted: $deletedCount\n";
echo "• Log files cleared: $clearedCount\n";
echo "• Total space freed: " . formatBytes($deletedSize + $clearedSize) . "\n";

if (!empty($errors)) {
    echo "\n⚠️ ERRORS:\n";
    foreach ($errors as $error) {
        echo "• $error\n";
    }
}

echo "\n✨ Your project is now cleaner and has more free space!\n";

// Also delete the cleanup scripts themselves
echo "\n🗑️ Cleaning up cleanup scripts...\n";
if (file_exists('cleanup-unused-files.php')) {
    if (unlink('cleanup-unused-files.php')) {
        echo "✅ Deleted: cleanup-unused-files.php\n";
    }
}

// Self-delete this script
echo "✅ Auto-cleanup complete - script will self-delete\n";
if (file_exists(__FILE__)) {
    unlink(__FILE__);
}

function formatBytes($size, $precision = 2) {
    $units = array('B', 'KB', 'MB', 'GB', 'TB');
    
    for ($i = 0; $size > 1024 && $i < count($units) - 1; $i++) {
        $size /= 1024;
    }
    
    return round($size, $precision) . ' ' . $units[$i];
}
?>