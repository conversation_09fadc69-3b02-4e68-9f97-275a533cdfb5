<?php
session_start();
include('server.php');

header('Content-Type: application/json');

// Check if this is a POST request
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
    exit;
}

// Get input data
$email = $_POST['email'] ?? '';
$new_username = $_POST['new_username'] ?? '';
$new_password = $_POST['new_password'] ?? '';

// Validate input
if (empty($email) || empty($new_username) || empty($new_password)) {
    echo json_encode(['success' => false, 'message' => 'All fields are required']);
    exit;
}

// Validate username format
if (!preg_match('/^[a-zA-Z0-9_]{3,50}$/', $new_username)) {
    echo json_encode(['success' => false, 'message' => 'Username must be 3-50 characters and contain only letters, numbers, and underscores']);
    exit;
}

// Validate password length
if (strlen($new_password) < 6) {
    echo json_encode(['success' => false, 'message' => 'Password must be at least 6 characters long']);
    exit;
}

try {
    // Check if user exists with this email
    $check_stmt = $conn->prepare("SELECT id, username FROM user WHERE email = ?");
    $check_stmt->bind_param("s", $email);
    $check_stmt->execute();
    $result = $check_stmt->get_result();
    
    if ($result->num_rows === 0) {
        echo json_encode(['success' => false, 'message' => 'User not found']);
        exit;
    }
    
    $user = $result->fetch_assoc();
    $user_id = $user['id'];
    $current_username = $user['username'];
    
    // Check if new username is already taken (by another user)
    if ($new_username !== $current_username) {
        $username_check_stmt = $conn->prepare("SELECT id FROM user WHERE username = ? AND id != ?");
        $username_check_stmt->bind_param("si", $new_username, $user_id);
        $username_check_stmt->execute();
        $username_result = $username_check_stmt->get_result();
        
        if ($username_result->num_rows > 0) {
            echo json_encode(['success' => false, 'message' => 'Username is already taken. Please choose a different one.']);
            exit;
        }
    }
    
    // Hash the new password
    $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
    
    // Start transaction for atomic updates
    $conn->begin_transaction();

    try {
        // Update user credentials
        $update_stmt = $conn->prepare("UPDATE user SET username = ?, password = ? WHERE id = ?");
        $update_stmt->bind_param("ssi", $new_username, $hashed_password, $user_id);

        if (!$update_stmt->execute()) {
            throw new Exception("Failed to update user table: " . $update_stmt->error);
        }

        // If username changed, also update purchasetickets table to maintain consistency
        if ($new_username !== $current_username) {
            $update_purchases_stmt = $conn->prepare("UPDATE purchasetickets SET username = ? WHERE username = ?");
            $update_purchases_stmt->bind_param("ss", $new_username, $current_username);

            if (!$update_purchases_stmt->execute()) {
                throw new Exception("Failed to update purchasetickets table: " . $update_purchases_stmt->error);
            }

            $updated_purchases = $update_purchases_stmt->affected_rows;
            error_log("Updated $updated_purchases purchase records from username '$current_username' to '$new_username'");
        }

        // Commit transaction
        $conn->commit();

        // Log the successful update
        error_log("Guest credentials updated successfully for user ID: $user_id, email: $email, new username: $new_username");

        echo json_encode([
            'success' => true,
            'message' => 'Credentials updated successfully',
            'new_username' => $new_username
        ]);

    } catch (Exception $e) {
        // Rollback transaction on error
        $conn->rollback();
        error_log("Failed to update guest credentials for user ID: $user_id, error: " . $e->getMessage());
        echo json_encode(['success' => false, 'message' => 'Failed to update credentials. Please try again.']);
    }
    
} catch (Exception $e) {
    error_log("Error updating guest credentials: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'An error occurred. Please try again.']);
}
?>
