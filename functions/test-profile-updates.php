<?php
/**
 * Test Profile Updates - District Removal, Tax ID & Phone (tel_work) Integration
 * Tests the updated profile functionality with correct Appika API field mapping
 */

echo "<h1>🧪 Test Profile Updates - District, Tax ID & Phone</h1>";

// Include required files
include_once(__DIR__ . '/server.php');
include_once(__DIR__ . '/customer-data-service.php');

$test_results = [];
$total_tests = 0;
$passed_tests = 0;

// Test 1: Check District Removal
echo "<h2>🗑️ Test 1: District Field Removal</h2>";
$total_tests++;

$profile_file = __DIR__ . '/../front-end/profile.php';
if (file_exists($profile_file)) {
    $content = file_get_contents($profile_file);
    
    // Check that District is removed from profile display
    $district_references = [
        '<p class="mb-0">District</p>',
        'District (Optional)',
        'col-md-4.*District'
    ];
    
    $found_district = false;
    foreach ($district_references as $pattern) {
        if (preg_match('/' . $pattern . '/i', $content)) {
            $found_district = true;
            break;
        }
    }
    
    if (!$found_district) {
        echo "✅ District field successfully removed from profile display and edit modal<br>";
        $test_results['district_removal'] = 'PASS';
        $passed_tests++;
    } else {
        echo "❌ District field still found in profile.php<br>";
        $test_results['district_removal'] = 'FAIL';
    }
} else {
    echo "❌ Profile file not found<br>";
    $test_results['district_removal'] = 'FAIL';
}

// Test 2: Check Tax ID Integration
echo "<h2>💰 Test 2: Tax ID from Appika API</h2>";
$total_tests++;

try {
    // Check if customer data service extracts tax_id
    $service_file = __DIR__ . '/customer-data-service.php';
    $service_content = file_get_contents($service_file);
    
    $tax_id_checks = [
        "customer_ext.*tax_id" => "Extracts tax_id from customer_ext",
        "mergedData.*tax_id" => "Adds tax_id to merged data"
    ];
    
    $tax_id_issues = [];
    foreach ($tax_id_checks as $pattern => $description) {
        if (!preg_match('/' . $pattern . '/i', $service_content)) {
            $tax_id_issues[] = $description;
        }
    }
    
    // Check profile display
    $profile_content = file_get_contents($profile_file);
    if (!preg_match('/user.*tax_id.*htmlspecialchars/i', $profile_content)) {
        $tax_id_issues[] = "Profile doesn't display tax_id from Appika";
    }
    
    if (empty($tax_id_issues)) {
        echo "✅ Tax ID properly integrated from Appika API<br>";
        
        // Test with sample data
        $stmt = $conn->prepare("SELECT username FROM user LIMIT 1");
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result->num_rows > 0) {
            $user = $result->fetch_assoc();
            $customerData = getCompleteCustomerData($user['username']);
            
            echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
            echo "<h4>Tax ID Test Result:</h4>";
            echo "<p><strong>Username:</strong> " . htmlspecialchars($user['username']) . "</p>";
            echo "<p><strong>Has Appika Data:</strong> " . ($customerData['has_appika_data'] ? 'Yes' : 'No') . "</p>";
            echo "<p><strong>Tax ID Value:</strong> " . htmlspecialchars($customerData['tax_id'] ?? 'Not set') . "</p>";
            echo "</div>";
        }
        
        $test_results['tax_id_integration'] = 'PASS';
        $passed_tests++;
    } else {
        echo "❌ Tax ID integration issues:<br>";
        foreach ($tax_id_issues as $issue) {
            echo "- " . htmlspecialchars($issue) . "<br>";
        }
        $test_results['tax_id_integration'] = 'FAIL';
    }
} catch (Exception $e) {
    echo "❌ Error testing tax ID integration: " . htmlspecialchars($e->getMessage()) . "<br>";
    $test_results['tax_id_integration'] = 'FAIL';
}

// Test 3: Check Phone (tel_work) Integration
echo "<h2>📞 Test 3: Phone from tel_work Field</h2>";
$total_tests++;

try {
    // Check if customer data service extracts tel_work
    $tel_work_checks = [
        "tel_work.*primaryLocation" => "Extracts tel_work from location",
        "mergedData.*tel_work" => "Adds tel_work to merged data"
    ];
    
    $tel_work_issues = [];
    foreach ($tel_work_checks as $pattern => $description) {
        if (!preg_match('/' . $pattern . '/i', $service_content)) {
            $tel_work_issues[] = $description;
        }
    }
    
    // Check profile display uses tel_work
    if (!preg_match('/user.*tel_work.*htmlspecialchars/i', $profile_content)) {
        $tel_work_issues[] = "Profile doesn't display tel_work from Appika";
    }
    
    // Check edit modal uses tel_work
    if (!preg_match('/tel_work.*user.*tel_work/i', $profile_content)) {
        $tel_work_issues[] = "Edit modal doesn't use tel_work field";
    }
    
    if (empty($tel_work_issues)) {
        echo "✅ Phone (tel_work) properly integrated from Appika API<br>";
        
        // Test with sample data
        if (isset($customerData)) {
            echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
            echo "<h4>Phone (tel_work) Test Result:</h4>";
            echo "<p><strong>tel_work Value:</strong> " . htmlspecialchars($customerData['tel_work'] ?? 'Not set') . "</p>";
            echo "<p><strong>phone Value (backward compatibility):</strong> " . htmlspecialchars($customerData['phone'] ?? 'Not set') . "</p>";
            echo "</div>";
        }
        
        $test_results['tel_work_integration'] = 'PASS';
        $passed_tests++;
    } else {
        echo "❌ Phone (tel_work) integration issues:<br>";
        foreach ($tel_work_issues as $issue) {
            echo "- " . htmlspecialchars($issue) . "<br>";
        }
        $test_results['tel_work_integration'] = 'FAIL';
    }
} catch (Exception $e) {
    echo "❌ Error testing tel_work integration: " . htmlspecialchars($e->getMessage()) . "<br>";
    $test_results['tel_work_integration'] = 'FAIL';
}

// Test 4: Check Edit Profile Handler Updates
echo "<h2>🔧 Test 4: Edit Profile Handler Updates</h2>";
$total_tests++;

try {
    $edit_handler_file = __DIR__ . '/edit_profile_appika.php';
    if (file_exists($edit_handler_file)) {
        $handler_content = file_get_contents($edit_handler_file);
        
        $handler_checks = [
            'tax_id.*POST' => 'Handles tax_id form input',
            'tel_work.*tell' => 'Uses tel_work for phone updates',
            'updateCustomerTaxIdInAppika' => 'Has tax_id update function',
            'customer_ext.*tax_id' => 'Updates customer_ext with tax_id'
        ];
        
        $handler_issues = [];
        foreach ($handler_checks as $pattern => $description) {
            if (!preg_match('/' . $pattern . '/i', $handler_content)) {
                $handler_issues[] = $description;
            }
        }
        
        if (empty($handler_issues)) {
            echo "✅ Edit profile handler properly updated for tax_id and tel_work<br>";
            echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
            echo "<h4>Handler Features:</h4>";
            echo "<ul>";
            echo "<li>✅ Processes tax_id from form</li>";
            echo "<li>✅ Uses tel_work for phone updates</li>";
            echo "<li>✅ Updates customer_ext.tax_id in Appika API</li>";
            echo "<li>✅ Updates location.tel_work in Appika API</li>";
            echo "</ul>";
            echo "</div>";
            
            $test_results['edit_handler_updates'] = 'PASS';
            $passed_tests++;
        } else {
            echo "❌ Edit handler issues:<br>";
            foreach ($handler_issues as $issue) {
                echo "- " . htmlspecialchars($issue) . "<br>";
            }
            $test_results['edit_handler_updates'] = 'FAIL';
        }
    } else {
        echo "❌ Edit profile handler file not found<br>";
        $test_results['edit_handler_updates'] = 'FAIL';
    }
} catch (Exception $e) {
    echo "❌ Error testing edit handler: " . htmlspecialchars($e->getMessage()) . "<br>";
    $test_results['edit_handler_updates'] = 'FAIL';
}

// Summary
echo "<hr>";
echo "<h2>📊 Test Summary</h2>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px;'>";
echo "<h3>Results: $passed_tests/$total_tests tests passed</h3>";

foreach ($test_results as $test => $result) {
    $icon = $result == 'PASS' ? '✅' : '❌';
    $color = $result == 'PASS' ? 'green' : 'red';
    echo "<p style='color: $color;'>$icon " . ucwords(str_replace('_', ' ', $test)) . ": $result</p>";
}

if ($passed_tests == $total_tests) {
    echo "<div style='color: green; font-weight: bold; padding: 10px; background: #d4edda; border-radius: 5px;'>";
    echo "🎉 All tests passed! Your profile updates are correctly implemented.";
    echo "</div>";
} else {
    echo "<div style='color: red; font-weight: bold; padding: 10px; background: #f8d7da; border-radius: 5px;'>";
    echo "❌ Some tests failed. Please review the issues above.";
    echo "</div>";
}

echo "</div>";

echo "<hr>";
echo "<h3>🎯 Profile Updates Summary:</h3>";
echo "<div style='background: #e3f2fd; padding: 15px; border-radius: 5px;'>";
echo "<h4>✅ Changes Made:</h4>";
echo "<ol>";
echo "<li><strong>District Removed:</strong> No longer displayed in profile or edit modal</li>";
echo "<li><strong>Tax ID Added:</strong> Now displays from customer_ext.tax_id in Appika API</li>";
echo "<li><strong>Phone Updated:</strong> Now uses tel_work field from Appika location data</li>";
echo "<li><strong>Edit Handler:</strong> Updated to handle tax_id and tel_work updates</li>";
echo "</ol>";

echo "<h4>🗂️ Field Mapping (Updated):</h4>";
echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
echo "<tr style='background: #e9ecef;'><th>Profile Field</th><th>Appika API Source</th><th>Status</th></tr>";
echo "<tr><td>Full Name</td><td>customer.name</td><td>✅ Active</td></tr>";
echo "<tr><td>Customer Code</td><td>appika_id (local)</td><td>✅ Read-only</td></tr>";
echo "<tr><td>Email</td><td>email (local)</td><td>✅ Read-only</td></tr>";
echo "<tr><td>Phone</td><td>location.tel_work</td><td>✅ Updated</td></tr>";
echo "<tr><td>Company Name</td><td>customer.name</td><td>✅ Active</td></tr>";
echo "<tr><td>Tax ID</td><td>customer_ext.tax_id</td><td>✅ Added</td></tr>";
echo "<tr><td>Address</td><td>location.add1</td><td>✅ Active</td></tr>";
echo "<tr><td>Address Notes</td><td>location.add2</td><td>✅ Active</td></tr>";
echo "<tr><td>District</td><td>-</td><td>❌ Removed</td></tr>";
echo "<tr><td>City</td><td>location.city</td><td>✅ Active</td></tr>";
echo "<tr><td>State/Province</td><td>location.state_code</td><td>✅ Active</td></tr>";
echo "<tr><td>Country</td><td>location.ccode</td><td>✅ Active</td></tr>";
echo "<tr><td>Postal Code</td><td>location.zip</td><td>✅ Active</td></tr>";
echo "</table>";
echo "</div>";

echo "<h3>🔗 Test Your Updated Profile:</h3>";
echo "<p><a href='../front-end/profile.php' target='_blank' style='background: #473BF0; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>View Updated Profile Page</a></p>";

echo "<style>";
echo "body { font-family: Arial, sans-serif; margin: 20px; }";
echo "h1, h2, h3 { color: #473BF0; }";
echo "table { margin: 10px 0; }";
echo "th, td { padding: 8px; text-align: left; }";
echo "</style>";
?>
