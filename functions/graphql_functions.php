<?php
// GraphQL Functions for Appika API Integration
require_once __DIR__ . '/../vendor/autoload.php'; // Include Composer autoloader for Guzzle

// Load centralized API configuration
require_once __DIR__ . '/../config/api-config.php';

// Get GraphQL API configuration
$graphqlConfig = getGraphqlApiConfig();
$graphqlEndpoint = $graphqlConfig['endpoint'];
$apiKey = $graphqlConfig['key'];

/**
 * Make GraphQL requests to Appika API
 *
 * @param string $query The GraphQL query or mutation
 * @param array $variables Variables for the GraphQL query
 * @return array Response array with success status, data, and error information
 */
function makeGraphQLRequest($query, $variables = []) {
    global $graphqlEndpoint, $apiKey;

    try {
        // Create a Guzzle HTTP client with shorter timeout for faster maintenance detection
        $client = new \GuzzleHttp\Client([
            'timeout' => 8, // Match the status checker timeout
            'connect_timeout' => 5, // Connection timeout
            'http_errors' => false, // Don't throw exceptions for 4xx/5xx responses
        ]);

        // Prepare GraphQL request body
        $requestBody = [
            'query' => $query
        ];

        if (!empty($variables)) {
            $requestBody['variables'] = $variables;
        }

        // Send the request
        $response = $client->request('POST', $graphqlEndpoint, [
            'headers' => [
                'X-api-key' => "{$apiKey}",
                // 'Authorization' => "Bearer {$apiKey}",
                'Content-Type' => 'application/json',
                'Accept' => 'application/json',
            ],
            'json' => $requestBody
        ]);

        $statusCode = $response->getStatusCode();
        $body = $response->getBody()->getContents();
        $data = json_decode($body, true);

        // Check for HTTP errors
        if ($statusCode >= 400) {
            return [
                'success' => false,
                'status' => $statusCode,
                'data' => $data,
                'error' => "HTTP Error {$statusCode}: " . ($data['message'] ?? 'Unknown error'),
                'raw_response' => $body
            ];
        }

        // Check for GraphQL errors
        if (isset($data['errors']) && !empty($data['errors'])) {
            $errorMessages = [];
            foreach ($data['errors'] as $error) {
                $errorMessages[] = $error['message'] ?? 'Unknown GraphQL error';
            }

            return [
                'success' => false,
                'status' => $statusCode,
                'data' => $data,
                'error' => 'GraphQL Errors: ' . implode(', ', $errorMessages),
                'raw_response' => $body
            ];
        }

        // Success response
        return [
            'success' => true,
            'status' => $statusCode,
            'data' => $data,
            'error' => null,
            'raw_response' => $body
        ];

    } catch (\GuzzleHttp\Exception\ConnectException $e) {
        $error = 'Connection Error: ' . $e->getMessage();
        error_log("GRAPHQL_CONNECTION_ERROR: $error");

        // Check if this is being called from a front-end page that should trigger maintenance
        $current_page = basename($_SERVER['PHP_SELF']);
        $maintenance_pages = ['my-ticket.php', 'create-ticket.php', 'ticket-detail.php', 'profile.php'];

        if (in_array($current_page, $maintenance_pages)) {
            error_log("MAINTENANCE_TRIGGER: Connection error on $current_page - redirecting to maintenance");

            $maintenance_url = 'server-down.php';
            if (strpos($_SERVER['REQUEST_URI'], '/front-end/') === false) {
                $maintenance_url = '../front-end/server-down.php';
            }

            header("Location: $maintenance_url");
            exit();
        }

        return [
            'success' => false,
            'status' => 0,
            'data' => null,
            'error' => $error,
            'raw_response' => null
        ];
    } catch (\GuzzleHttp\Exception\RequestException $e) {
        $error = 'Request Error: ' . $e->getMessage();

        // Check for timeout errors
        if (strpos($error, 'timeout') !== false || strpos($error, 'timed out') !== false) {
            $current_page = basename($_SERVER['PHP_SELF']);
            $maintenance_pages = ['my-ticket.php', 'create-ticket.php', 'ticket-detail.php', 'profile.php'];

            if (in_array($current_page, $maintenance_pages)) {
                error_log("MAINTENANCE_TRIGGER: Timeout error on $current_page - redirecting to maintenance");

                $maintenance_url = 'server-down.php';
                if (strpos($_SERVER['REQUEST_URI'], '/front-end/') === false) {
                    $maintenance_url = '../front-end/server-down.php';
                }

                header("Location: $maintenance_url");
                exit();
            }
        }

        return [
            'success' => false,
            'status' => $e->getCode(),
            'data' => null,
            'error' => $error,
            'raw_response' => null
        ];
    } catch (\Exception $e) {
        return [
            'success' => false,
            'status' => 0,
            'data' => null,
            'error' => 'Unexpected Error: ' . $e->getMessage(),
            'raw_response' => null
        ];
    }
}

/**
 * Get all tickets from Appika API
 *
 * @return array Response array with tickets data
 */
function getAppikaTickets() {
    $query = '
    query GetTickets {
        getTickets {
            id
            ticket_no
            contact_id
            agent_id
            req_email
            subject
            type
            type_name
            priority
            status
            created
            updated
        }
    }';

    return makeGraphQLRequest($query, []);
}

/**
 * Get a specific ticket by ID from Appika API
 *
 * @param int $ticketId The ticket ID
 * @return array Response array with ticket data
 */
function getAppikaTicket($ticketId) {
    $query = '
    query GetTicket($id: Int!) {
        getTicket(id: $id) {
            id
            ticket_no
            contact_id
            agent_id
            req_email
            subject
            type
            type_name
            priority
            status
            created
            updated
            contacts {
                id
                email
                fname
                status
            }
            ticketMsg {
                id
                message
            }
        }
    }';

    $variables = [
        'id' => (int)$ticketId
    ];

    return makeGraphQLRequest($query, $variables);
}

/**
 * Create a new ticket in Appika API using createTicketByContact (recommended)
 *
 * @param array $contactData Contact data array with keys: name, email, subject, reply_msg
 * @return array Response array with created ticket data
 */
function createAppikaTicket($contactData) {
    $mutation = '
    mutation CreateTicketByContact(
        $name: String!,
        $email: String!,
        $subject: String!,
        $reply_msg: String!
    ) {
        createTicketByContact(
            name: $name,
            email: $email,
            subject: $subject,
            reply_msg: $reply_msg
        ) {
            id
            subject
            ticketMsg {
                id
                message
            }
        }
    }';

    return makeGraphQLRequest($mutation, $contactData);
}

/**
 * Create a new ticket in Appika API using createTicketByContact (recommended for descriptions)
 *
 * @param array $contactData Contact and ticket data array with keys: name, email, subject, reply_msg
 * @return array Response array with created ticket data including ticketMsg
 */
function createAppikaTicketByContact($contactData) {
    $mutation = '
    mutation CreateTicketByContact(
        $name: String!,
        $email: String!,
        $subject: String!,
        $reply_msg: String!
    ) {
        createTicketByContact(
            name: $name,
            email: $email,
            subject: $subject,
            reply_msg: $reply_msg
        ) {
            id
            subject
            ticketMsg {
                message
            }
        }
    }';

    return makeGraphQLRequest($mutation, $contactData);
}

/**
 * Update an existing ticket in Appika API
 *
 * @param int $ticketId The ticket ID
 * @param array $updateData Update data array
 * @return array Response array with updated ticket data
 */
function updateAppikaTicket($ticketId, $updateData) {
    $mutation = '
    mutation UpdateTicket(
        $id: Int!,
        $contact_id: Int,
        $agent_id: Int,
        $subject: String!,
        $type: Int!,
        $type_name: String,
        $priority: String!,
        $status: String!,
        $req_email: String,
        $time_track: String!,
        $reply_msg: String,
        $tags: String
    ) {
        updateTicket(
            id: $id,
            contact_id: $contact_id,
            agent_id: $agent_id,
            subject: $subject,
            type: $type,
            type_name: $type_name,
            priority: $priority,
            status: $status,
            req_email: $req_email,
            time_track: $time_track,
            reply_msg: $reply_msg,
            tags: $tags
        ) {
            id
            ticket_no
            contact_id
            agent_id
            req_email
            subject
            type
            type_name
            priority
            status
            created
            updated
        }
    }';

    $variables = array_merge(['id' => (int)$ticketId], $updateData);

    return makeGraphQLRequest($mutation, $variables);
}

/**
 * Log GraphQL API requests for debugging
 *
 * @param string $operation The operation name
 * @param array $result The API result
 * @param string $logFile The log file path (optional)
 */
function logGraphQLRequest($operation, $result, $logFile = null) {
    if (!$logFile) {
        $logFile = __DIR__ . '/../logs/graphql_api.log';
    }

    // Create logs directory if it doesn't exist
    $logDir = dirname($logFile);
    if (!file_exists($logDir)) {
        mkdir($logDir, 0755, true);
    }

    $logEntry = [
        'timestamp' => date('Y-m-d H:i:s'),
        'operation' => $operation,
        'success' => $result['success'],
        'status' => $result['status'],
        'error' => $result['error']
    ];

    $logLine = json_encode($logEntry) . "\n";
    file_put_contents($logFile, $logLine, FILE_APPEND | LOCK_EX);
}
?>