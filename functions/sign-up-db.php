<?php
session_start();
include('../functions/server.php');
require_once '../vendor/autoload.php'; // Include Composer autoloader for Guzzle
require_once '../functions/create-customer-minimal.php'; // Include new minimal customer creation

$errors = array();

// Get API configuration
$apiConfig = getCustomerApiConfig();
$apiEndpoint = $apiConfig['endpoint'];
$apiPath = $apiConfig['path'];
$apiKey = $apiConfig['key'];

// Function to send user data to Appika API
function sendToAppikaAPI($customerData, $method = 'POST', $path = '') {
    global $apiEndpoint, $apiPath, $apiKey;

    // Create a Guzzle HTTP client
    $client = new \GuzzleHttp\Client([
        'base_uri' => $apiEndpoint,
        'timeout' => 30,
        'http_errors' => false, // Don't throw exceptions for 4xx/5xx responses
    ]);

    // Determine the full path
    $fullPath = empty($path) ? $apiPath : $path;

    try {
        // Send the request
        $response = $client->request($method, $fullPath, [
            'headers' => [
                'X-api-key' => "{$apiKey}",
                'Accept' => 'application/json',
                'Content-Type' => 'application/json',
            ],
            'json' => $customerData
        ]);

        // Get status code and response body
        $statusCode = $response->getStatusCode();
        $body = $response->getBody()->getContents();

        // Parse JSON if the response is JSON
        if (strpos($response->getHeaderLine('Content-Type'), 'application/json') !== false) {
            $data = json_decode($body, true);
            return [
                'success' => ($statusCode >= 200 && $statusCode < 300),
                'status' => $statusCode,
                'data' => $data
            ];
        } else {
            return [
                'success' => false,
                'status' => $statusCode,
                'data' => $body
            ];
        }
    } catch (\Exception $e) {
        return [
            'success' => false,
            'status' => 500,
            'error' => $e->getMessage()
        ];
    }
}

// Function to add location to a customer in Appika
function addLocationToAppika($customerDbId, $locationData) {
    global $apiEndpoint, $apiPath, $apiKey;

    // Create the path for adding a location
    $locationPath = $apiPath . '/' . $customerDbId . '/locations';

    // Send the location data to Appika
    return sendToAppikaAPI($locationData, 'POST', $locationPath);
}

if (isset($_POST['reg_user'])) {
    $first_name = mysqli_real_escape_string($conn, $_POST['first_name']);
    $username = mysqli_real_escape_string($conn, $_POST['username']);
    $email = mysqli_real_escape_string($conn, $_POST['email']);
    $password_1 = mysqli_real_escape_string($conn, $_POST['password_1']);
    $password_2 = mysqli_real_escape_string($conn, $_POST['password_2']);

    $tell = mysqli_real_escape_string($conn, $_POST['tell']);
    $company_name = mysqli_real_escape_string($conn, $_POST['company_name']);
    $tax_id = mysqli_real_escape_string($conn, $_POST['tax_id']);
    $address = mysqli_real_escape_string($conn, $_POST['address']);
    $address2 = mysqli_real_escape_string($conn, $_POST['address2']);
    $district = mysqli_real_escape_string($conn, $_POST['district']);
    $city = mysqli_real_escape_string($conn, $_POST['city']);
    $state = mysqli_real_escape_string($conn, $_POST['state']);
    $country = mysqli_real_escape_string($conn, $_POST['country']);
    $postal_code = mysqli_real_escape_string($conn, $_POST['postal_code']);

    if (empty($first_name)) {
        array_push($errors, "Full name is required");
    }
    if (empty($username)) {
        array_push($errors, "Username is required");
    }
    if (empty($email)) {
        array_push($errors, "Email is required");
    }
    if (empty($password_1)) {
        array_push($errors, "Password is required");
    }
    if ($password_1 != $password_2) {
        array_push($errors, "The two password do not match");
    }
    if (empty($tell)) {
        array_push($errors, "Tell number is required");
    }
    if (empty($district)) {
        array_push($errors, "District is required");
    }
    if (empty($city)) {
        array_push($errors, "City is required");
    }
    if (empty($country)) {
        array_push($errors, "Country is required");
    }
    if (empty($postal_code)) {
        array_push($errors, "Postal Code is required");
    }
    if (empty($address)) {
        array_push($errors, "Address is required");
    }

    $user_check_query = "SELECT * FROM user WHERE username = '$username' or email = '$email' LIMIT 1";
    $query = mysqli_query($conn, $user_check_query);
    $result = mysqli_fetch_assoc($query);

    if ($result) {
        //if user exists
        if ($result['username'] === $username) {
            array_push($errors, "Username already exists");
        }
        if ($result['email'] === $email) {
            array_push($errors, "Email already exists");
        }
    }

    if (count($errors) == 0) {
        // Use new minimal customer creation approach
        $formData = [
            'username' => $username,
            'email' => $email,
            'password_1' => $password_1,
            'first_name' => $first_name, // Contains full name
            'address' => $address,
            'address2' => $address2,
            'city' => $city,
            'state' => $state,
            'country' => $country,
            'postal_code' => $postal_code,
            'tell' => $tell,
            'company_name' => $company_name,
            'tax_id' => $tax_id,
            'district' => $district,
            'timezone' => 'UTC'
        ];

        $creationResult = createCustomerFromRegistration($formData);

        if ($creationResult['success']) {
            // Customer created successfully
            error_log("Registration successful - User ID: " . $creationResult['user_id'] . ", Message: " . $creationResult['message']);
        } else {
            // Customer creation failed
            $errors = array_merge($errors, $creationResult['errors']);
            error_log("Registration failed: " . $creationResult['message']);
        }

        // Set registration success flag
        $_SESSION['registration_success'] = true;
        header('location: ../front-end/sign-in.php');
        exit();
    } else {
        // Store form data in session to repopulate the form
        $_SESSION['form_data'] = array(
            'first_name' => $first_name,
            'username' => $username,
            'email' => $email,
            'tell' => $tell,
            'company_name' => $company_name,
            'tax_id' => $tax_id,
            'address' => $address,
            'address2' => $address2,
            'district' => $district,
            'city' => $city,
            'state' => $state,
            'country' => $country,
            'postal_code' => $postal_code
        );

        $_SESSION['error'] = $errors;
        header("location: ../front-end/sign-up.php");
    }
}