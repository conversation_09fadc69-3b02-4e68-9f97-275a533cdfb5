<?php
/**
 * Email Server Test & Diagnostic Tool
 * Use this to test email functionality on your server
 */

echo "<h2>📧 Email Server Diagnostic Tool</h2>";

// Test 1: Check if mail function exists
echo "<h3>🔧 Test 1: PHP Mail Function</h3>";
if (function_exists('mail')) {
    echo "<div style='color: green; padding: 10px; border: 1px solid green; background: #f0fff0;'>";
    echo "✅ <strong>mail() function is available</strong>";
    echo "</div>";
} else {
    echo "<div style='color: red; padding: 10px; border: 1px solid red; background: #fff0f0;'>";
    echo "❌ <strong>mail() function is NOT available</strong>";
    echo "</div>";
}

// Test 2: Check PHP mail configuration
echo "<h3>⚙️ Test 2: PHP Mail Configuration</h3>";
echo "<div style='background: #f8f9fa; padding: 15px; border: 1px solid #ddd;'>";
echo "<p><strong>SMTP:</strong> " . ini_get('SMTP') . "</p>";
echo "<p><strong>smtp_port:</strong> " . ini_get('smtp_port') . "</p>";
echo "<p><strong>sendmail_from:</strong> " . ini_get('sendmail_from') . "</p>";
echo "<p><strong>sendmail_path:</strong> " . ini_get('sendmail_path') . "</p>";
echo "</div>";

// Test 3: Simple email test
echo "<h3>📨 Test 3: Simple Email Test</h3>";

$test_email = isset($_GET['email']) ? $_GET['email'] : '';
if ($test_email) {
    if (filter_var($test_email, FILTER_VALIDATE_EMAIL)) {
        $subject = "HelloIT Email Test - " . date('Y-m-d H:i:s');
        $message = "
        <html>
        <head><title>HelloIT Email Test</title></head>
        <body>
            <h2>🎉 Email Test Successful!</h2>
            <p>This is a test email from your HelloIT server.</p>
            <p><strong>Time:</strong> " . date('F j, Y \a\t g:i A') . "</p>
            <p><strong>Server:</strong> " . $_SERVER['HTTP_HOST'] . "</p>
            <p>If you received this email, your server email configuration is working!</p>
        </body>
        </html>";
        
        $headers = array();
        $headers[] = "From: HelloIT Test <<EMAIL>>";
        $headers[] = "Reply-To: HelloIT Support <<EMAIL>>";
        $headers[] = "MIME-Version: 1.0";
        $headers[] = "Content-Type: text/html; charset=UTF-8";
        $headerString = implode("\r\n", $headers);
        
        $result = @mail($test_email, $subject, $message, $headerString);
        
        if ($result) {
            echo "<div style='color: green; padding: 10px; border: 1px solid green; background: #f0fff0;'>";
            echo "✅ <strong>Test email sent successfully to " . htmlspecialchars($test_email) . "</strong><br>";
            echo "Check your inbox (and spam folder) for the test email.";
            echo "</div>";
        } else {
            echo "<div style='color: red; padding: 10px; border: 1px solid red; background: #fff0f0;'>";
            echo "❌ <strong>Failed to send test email to " . htmlspecialchars($test_email) . "</strong><br>";
            $lastError = error_get_last();
            if ($lastError) {
                echo "Error: " . htmlspecialchars($lastError['message']);
            }
            echo "</div>";
        }
    } else {
        echo "<div style='color: orange; padding: 10px; border: 1px solid orange; background: #fff8e1;'>";
        echo "⚠️ <strong>Invalid email format:</strong> " . htmlspecialchars($test_email);
        echo "</div>";
    }
} else {
    echo "<form method='GET' style='background: #f0f8ff; padding: 15px; border: 1px solid #0066cc;'>";
    echo "<p><strong>Enter your email to test:</strong></p>";
    echo "<input type='email' name='email' placeholder='<EMAIL>' required style='padding: 8px; width: 300px;'>";
    echo "<button type='submit' style='padding: 8px 15px; margin-left: 10px; background: #0066cc; color: white; border: none; cursor: pointer;'>Send Test Email</button>";
    echo "</form>";
}

// Test 4: Receipt email function test
echo "<h3>🧾 Test 4: Receipt Email Function</h3>";

$test_transaction = isset($_GET['test_receipt']) ? $_GET['test_receipt'] : '';
if ($test_transaction && $test_email) {
    try {
        include_once(__DIR__ . '/send-purchase-receipt.php');
        
        echo "<div style='background: #f0f8ff; padding: 15px; border: 1px solid #0066cc; margin: 10px 0;'>";
        echo "<p><strong>Testing receipt email function...</strong></p>";
        echo "<p>Transaction ID: " . htmlspecialchars($test_transaction) . "</p>";
        echo "<p>Email: " . htmlspecialchars($test_email) . "</p>";
        echo "</div>";
        
        $result = sendPurchaseReceiptEmail($test_transaction, $test_email, false, null);
        
        if ($result) {
            echo "<div style='color: green; padding: 10px; border: 1px solid green; background: #f0fff0;'>";
            echo "✅ <strong>Receipt email function executed successfully!</strong>";
            echo "</div>";
        } else {
            echo "<div style='color: red; padding: 10px; border: 1px solid red; background: #fff0f0;'>";
            echo "❌ <strong>Receipt email function failed</strong><br>";
            echo "Check error logs for more details.";
            echo "</div>";
        }
    } catch (Exception $e) {
        echo "<div style='color: red; padding: 10px; border: 1px solid red; background: #fff0f0;'>";
        echo "❌ <strong>Error testing receipt function:</strong> " . htmlspecialchars($e->getMessage());
        echo "</div>";
    }
} else if ($test_email) {
    echo "<form method='GET' style='background: #f0f8ff; padding: 15px; border: 1px solid #0066cc;'>";
    echo "<input type='hidden' name='email' value='" . htmlspecialchars($test_email) . "'>";
    echo "<p><strong>Test receipt email with a transaction ID:</strong></p>";
    echo "<input type='text' name='test_receipt' placeholder='transaction_id_123' required style='padding: 8px; width: 300px;'>";
    echo "<button type='submit' style='padding: 8px 15px; margin-left: 10px; background: #0066cc; color: white; border: none; cursor: pointer;'>Test Receipt Email</button>";
    echo "</form>";
}

// Test 5: Server environment info
echo "<h3>🖥️ Test 5: Server Environment</h3>";
echo "<div style='background: #f8f9fa; padding: 15px; border: 1px solid #ddd;'>";
echo "<p><strong>PHP Version:</strong> " . phpversion() . "</p>";
echo "<p><strong>Server Software:</strong> " . $_SERVER['SERVER_SOFTWARE'] . "</p>";
echo "<p><strong>Host:</strong> " . $_SERVER['HTTP_HOST'] . "</p>";
echo "<p><strong>Document Root:</strong> " . $_SERVER['DOCUMENT_ROOT'] . "</p>";
echo "<p><strong>Operating System:</strong> " . php_uname() . "</p>";
echo "</div>";

echo "<h3>💡 Troubleshooting Tips</h3>";
echo "<div style='background: #fff8e1; padding: 15px; border: 1px solid #ffa000;'>";
echo "<h4>If emails are not working:</h4>";
echo "<ol>";
echo "<li><strong>Check server logs:</strong> Look for PHP error logs and mail logs</li>";
echo "<li><strong>Contact hosting provider:</strong> Ask about mail server configuration</li>";
echo "<li><strong>SMTP setup:</strong> Consider using SMTP instead of PHP mail()</li>";
echo "<li><strong>SPF/DKIM records:</strong> Ensure proper DNS records for email deliverability</li>";
echo "<li><strong>Firewall:</strong> Check if outbound email ports (25, 587, 465) are open</li>";
echo "</ol>";
echo "</div>";

echo "<h3>🔗 Quick Links</h3>";
echo "<div style='background: #e8f5e8; padding: 15px; border: 1px solid #4caf50;'>";
echo "<p><a href='?email=<EMAIL>' style='color: #0066cc;'>Test with sample email</a></p>";
echo "<p><a href='test-receipt-email.php' style='color: #0066cc;'>Test receipt email function</a></p>";
echo "</div>";
?>