<?php
/**
 * Maintenance Check
 * Include this file in front-end pages to automatically redirect to maintenance page if <PERSON><PERSON><PERSON> is down
 * Usage: include_once('../functions/maintenance-check.php');
 */

// Include the status checker
include_once(__DIR__ . '/appika-status-checker.php');

/**
 * Check if current page requires A<PERSON><PERSON> and redirect if down
 * @param string $mode - 'strict' for user panels, 'account_creation' for new accounts, 'normal' for regular pages
 */
function checkMaintenanceStatus($mode = 'normal') {
    $current_page = basename($_SERVER['PHP_SELF']);

    // TEMPORARY: Allow bypass for testing
    if (isset($_GET['bypass_maintenance']) && $_GET['bypass_maintenance'] === '1') {
        error_log("MAINTENANCE_BYPASS: Bypassing maintenance check for testing");
        return true;
    }

    // Pages that require Appika functionality
    $pages_requiring_appika = [
        'create-ticket.php',
        'ticket-detail.php',
        'my-ticket.php',
        'profile.php',
        'edit-profile.php',
        'account-details.php',
        'purchase-history.php',
        'payment-methods.php'
    ];

    // User panel pages (strict mode)
    $user_panel_pages = [
        'my-ticket.php',
        'profile.php',
        'edit-profile.php',
        'account-details.php',
        'purchase-history.php',
        'payment-methods.php',
        'create-ticket.php',
        'ticket-detail.php'
    ];

    $should_check = false;

    switch ($mode) {
        case 'strict':
            // Check for user panel pages
            $should_check = in_array($current_page, $user_panel_pages);
            break;
        case 'account_creation':
            // Always check for account creation
            $should_check = true;
            break;
        case 'normal':
        default:
            // Check for pages that require Appika
            $should_check = in_array($current_page, $pages_requiring_appika);
            break;
    }

    if ($should_check && !isAppikaOnline()) {
        // Determine maintenance page URL
        $maintenance_url = 'server-down.php';

        // If we're not in front-end directory, adjust path
        if (strpos($_SERVER['REQUEST_URI'], '/front-end/') === false) {
            $maintenance_url = '../front-end/server-down.php';
        }

        header("Location: $maintenance_url");
        exit();
    }

    return isAppikaOnline();
}

/**
 * Get maintenance status for conditional display
 */
function getMaintenanceStatus() {
    return [
        'online' => isAppikaOnline(),
        'message' => getAppikaStatusMessage()
    ];
}

// Auto-check for normal pages (backward compatibility)
$current_page = basename($_SERVER['PHP_SELF']);
$pages_requiring_appika = [
    'create-ticket.php',
    'ticket-detail.php',
    'my-ticket.php',
    'profile.php',
    'edit-profile.php'
];

if (in_array($current_page, $pages_requiring_appika)) {
    checkMaintenanceStatus('normal');
}
?>
