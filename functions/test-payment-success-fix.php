<?php
/**
 * Test Payment Success Fixes
 * Tests the fixes for stripe_customer_id and country/timezone issues
 */

// Check if session is already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

echo "<h1>🧪 Test Payment Success Fixes</h1>";

// Include required files safely
try {
    if (!class_exists('DBController')) {
        include('server.php');
    }
    if (!function_exists('createCustomerMinimal')) {
        include('create-customer-minimal.php');
    }
    if (!function_exists('getCompleteCustomerData')) {
        include('customer-data-service.php');
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error loading required files: " . htmlspecialchars($e->getMessage()) . "</p>";
    exit();
}

echo "<h2>🔧 Testing Customer Creation Fixes</h2>";

try {
    // Test 1: Test createCustomerMinimal with proper data
    echo "<h3>📝 Test 1: Customer Creation with Country & Timezone</h3>";
    
    $testCustomerData = [
        'username' => 'test_' . uniqid(),
        'email' => 'test_' . uniqid() . '@example.com',
        'password' => 'testpassword123',
        'full_name' => 'Test Customer Full Name',
        'address' => 'Test Address 123',
        'address2' => 'Test Address 2',
        'city' => 'Bangkok',
        'state' => 'Bangkok',
        'country' => 'TH',
        'postal_code' => '10110',
        'phone' => '1234567890',
        'company_name' => '',
        'tax_id' => '',
        'timezone' => 'Asia/Bangkok',
        'stripe_customer_id' => null // This should not cause an error now
    ];
    
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>Test Customer Data:</h4>";
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>Field</th><th>Value</th></tr>";
    foreach ($testCustomerData as $key => $value) {
        echo "<tr><td>" . htmlspecialchars($key) . "</td><td>" . htmlspecialchars($value ?? 'NULL') . "</td></tr>";
    }
    echo "</table>";
    echo "</div>";
    
    // Test customer creation
    echo "<h4>🚀 Creating test customer...</h4>";
    $creationResult = createCustomerMinimal($testCustomerData);
    
    if ($creationResult['success']) {
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h4>✅ Customer Creation Successful!</h4>";
        echo "<p><strong>User ID:</strong> " . htmlspecialchars($creationResult['user_id']) . "</p>";
        echo "<p><strong>Appika ID:</strong> " . htmlspecialchars($creationResult['appika_id']) . "</p>";
        echo "<p><strong>Appika Customer ID:</strong> " . htmlspecialchars($creationResult['appika_customer_id']) . "</p>";
        echo "</div>";
        
        // Test 2: Verify data was saved correctly
        echo "<h3>🔍 Test 2: Verify Data Saved Correctly</h3>";
        
        $savedData = getCompleteCustomerData($testCustomerData['username']);
        
        if ($savedData) {
            echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
            echo "<h4>Saved Customer Data:</h4>";
            echo "<table border='1' style='border-collapse: collapse;'>";
            echo "<tr><th>Field</th><th>Expected</th><th>Actual</th><th>Status</th></tr>";
            
            $fieldsToCheck = [
                'username' => $testCustomerData['username'],
                'email' => $testCustomerData['email'],
                'country' => $testCustomerData['country'],
                'timezone' => $testCustomerData['timezone'],
                'stripe_customer_id' => null,
                'first_name' => $testCustomerData['full_name']
            ];
            
            foreach ($fieldsToCheck as $field => $expected) {
                $actual = $savedData[$field] ?? 'NOT_FOUND';
                $status = ($actual == $expected) ? '✅ Match' : '❌ Mismatch';
                $statusColor = ($actual == $expected) ? 'green' : 'red';
                
                echo "<tr>";
                echo "<td>" . htmlspecialchars($field) . "</td>";
                echo "<td>" . htmlspecialchars($expected ?? 'NULL') . "</td>";
                echo "<td>" . htmlspecialchars($actual ?? 'NULL') . "</td>";
                echo "<td style='color: $statusColor;'>" . $status . "</td>";
                echo "</tr>";
            }
            echo "</table>";
            echo "</div>";
            
            // Check if all critical fields match
            $allMatch = true;
            foreach ($fieldsToCheck as $field => $expected) {
                if (($savedData[$field] ?? null) != $expected) {
                    $allMatch = false;
                    break;
                }
            }
            
            if ($allMatch) {
                echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
                echo "<h4>🎉 All Data Saved Correctly!</h4>";
                echo "<ul>";
                echo "<li>✅ Country saved: " . htmlspecialchars($savedData['country']) . "</li>";
                echo "<li>✅ Timezone saved: " . htmlspecialchars($savedData['timezone']) . "</li>";
                echo "<li>✅ stripe_customer_id handled: " . htmlspecialchars($savedData['stripe_customer_id'] ?? 'NULL') . "</li>";
                echo "<li>✅ Full name saved: " . htmlspecialchars($savedData['first_name']) . "</li>";
                echo "</ul>";
                echo "</div>";
            } else {
                echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
                echo "<h4>❌ Some Data Not Saved Correctly</h4>";
                echo "<p>Check the table above for mismatches.</p>";
                echo "</div>";
            }
            
        } else {
            echo "<p style='color: red;'>❌ Failed to retrieve saved customer data</p>";
        }
        
        // Clean up test data
        echo "<h4>🧹 Cleaning up test data...</h4>";
        $cleanup = $conn->prepare("DELETE FROM user WHERE id = ?");
        $cleanup->bind_param("i", $creationResult['user_id']);
        if ($cleanup->execute()) {
            echo "<p style='color: green;'>✅ Test data cleaned up successfully</p>";
        } else {
            echo "<p style='color: orange;'>⚠️ Failed to clean up test data - you may need to manually delete user ID: " . $creationResult['user_id'] . "</p>";
        }
        
    } else {
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h4>❌ Customer Creation Failed!</h4>";
        echo "<p><strong>Message:</strong> " . htmlspecialchars($creationResult['message']) . "</p>";
        if (!empty($creationResult['errors'])) {
            echo "<p><strong>Errors:</strong></p>";
            echo "<ul>";
            foreach ($creationResult['errors'] as $error) {
                echo "<li>" . htmlspecialchars($error) . "</li>";
            }
            echo "</ul>";
        }
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<h3>💥 Error Occurred:</h3>";
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; color: #721c24;'>";
    echo "<p><strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><strong>File:</strong> " . htmlspecialchars($e->getFile()) . "</p>";
    echo "<p><strong>Line:</strong> " . $e->getLine() . "</p>";
    echo "</div>";
}

echo "<hr>";
echo "<h3>📋 Summary of Fixes</h3>";
echo "<div style='background: #e3f2fd; padding: 15px; border-radius: 5px;'>";
echo "<h4>✅ Issues Fixed:</h4>";
echo "<ol>";
echo "<li><strong>stripe_customer_id Error:</strong> Now set to NULL during user creation, updated later when Stripe customer is created</li>";
echo "<li><strong>Country Not Saved:</strong> Added country field to createMinimalCustomerRecord function</li>";
echo "<li><strong>Timezone Issue:</strong> Now properly saves timezone (default: Asia/Bangkok for Thailand)</li>";
echo "<li><strong>Full Name Missing:</strong> Added first_name field to store customer's full name</li>";
echo "</ol>";

echo "<h4>🔧 Changes Made:</h4>";
echo "<ul>";
echo "<li>Updated <code>payment-success.php</code>: Set stripe_customer_id to null, timezone to Asia/Bangkok</li>";
echo "<li>Updated <code>createMinimalCustomerRecord()</code>: Added country and first_name fields</li>";
echo "<li>Updated <code>createCustomerMinimal()</code>: Pass country and full_name to database</li>";
echo "<li>Updated <code>getLocalCustomerEssentials()</code>: Include country and first_name in SELECT</li>";
echo "</ul>";
echo "</div>";

echo "<h3>🔗 Next Steps:</h3>";
echo "<p>1. <strong>Test a real purchase:</strong> Complete a purchase with a new user to verify the fixes</p>";
echo "<p>2. <strong>Check database:</strong> Verify that country and timezone are now saved correctly</p>";
echo "<p>3. <strong>Monitor errors:</strong> The stripe_customer_id error should no longer occur</p>";

echo "<style>";
echo "body { font-family: Arial, sans-serif; margin: 20px; }";
echo "h1, h2, h3 { color: #473BF0; }";
echo "table { margin: 10px 0; }";
echo "th, td { padding: 8px; text-align: left; }";
echo "code { background: #f8f9fa; padding: 2px 4px; border-radius: 3px; }";
echo "</style>";
?>
