<?php
/**
 * Minimal Customer Creation Function
 * Creates customers with minimal local database storage + full Appika API integration
 */

// Only include dependencies if they haven't been included yet
if (!class_exists('GuzzleHttp\Client')) {
    // Auto-detect environment for file paths (same as working front-end files)
    $is_localhost = (strpos($_SERVER['HTTP_HOST'], 'localhost') !== false || strpos($_SERVER['HTTP_HOST'], '127.0.0.1') !== false);
    $file_base_path = $is_localhost ? '/helloit' : '';

    require_once $_SERVER['DOCUMENT_ROOT'] . $file_base_path . '/vendor/autoload.php';
}

// Include local dependencies
if (!class_exists('DBController')) {
    require_once __DIR__ . '/server.php';
}

if (!function_exists('getCompleteCustomerData')) {
    require_once __DIR__ . '/customer-data-service.php';
}

if (!function_exists('getCustomerApiConfig')) {
    // Auto-detect environment for file paths (same as working front-end files)
    $is_localhost = (strpos($_SERVER['HTTP_HOST'], 'localhost') !== false || strpos($_SERVER['HTTP_HOST'], '127.0.0.1') !== false);
    $file_base_path = $is_localhost ? '/helloit' : '';

    require_once $_SERVER['DOCUMENT_ROOT'] . $file_base_path . '/config/api-config.php';
}

/**
 * Create customer with minimal database storage
 * @param array $customerData Customer data for creation
 * @return array Result with success status and details
 */
function createCustomerMinimal($customerData) {
    global $conn;
    
    $result = [
        'success' => false,
        'user_id' => null,
        'appika_id' => null,
        'appika_customer_id' => null,
        'message' => '',
        'errors' => []
    ];
    
    try {
        // Validate required fields
        $requiredFields = ['username', 'email', 'password', 'full_name'];
        foreach ($requiredFields as $field) {
            if (empty($customerData[$field])) {
                $result['errors'][] = ucfirst(str_replace('_', ' ', $field)) . " is required";
            }
        }
        
        if (!empty($result['errors'])) {
            $result['message'] = 'Validation failed';
            return $result;
        }
        
        // Check if username/email already exists
        $stmt = $conn->prepare("SELECT id FROM user WHERE username = ? OR email = ?");
        $stmt->bind_param("ss", $customerData['username'], $customerData['email']);
        $stmt->execute();
        if ($stmt->get_result()->num_rows > 0) {
            $result['errors'][] = "Username or email already exists";
            $result['message'] = 'User already exists';
            return $result;
        }
        
        // Step 1: Create minimal record in local database
        $localCustomerData = [
            'username' => $customerData['username'],
            'email' => $customerData['email'],
            'password' => md5($customerData['password']), // Hash password
            'registration_time' => date('Y-m-d H:i:s'),
            'timezone' => $customerData['timezone'] ?? 'UTC',
            'stripe_customer_id' => $customerData['stripe_customer_id'] ?? null,
            'country' => $customerData['country'] ?? ''
        ];
        
        $userId = createMinimalCustomerRecord($localCustomerData);
        if (!$userId) {
            $result['message'] = 'Failed to create local customer record';
            return $result;
        }
        
        $result['user_id'] = $userId;
        
        // Step 2: Create customer in Appika API
        $appikaResult = createCustomerInAppika($userId, $customerData);
        
        if ($appikaResult['success']) {
            // Update local record with Appika IDs
            $appikaId = 'HI' . $userId;
            $appikaCustomerId = $appikaResult['appika_customer_id'];
            
            if (updateCustomerAppikaIds($userId, $appikaId, $appikaCustomerId)) {
                $result['success'] = true;
                $result['appika_id'] = $appikaId;
                $result['appika_customer_id'] = $appikaCustomerId;
                $result['message'] = 'Customer created successfully';
                
                // Log success
                error_log("Customer created successfully - User ID: $userId, Appika ID: $appikaId");
            } else {
                $result['message'] = 'Customer created but failed to update Appika IDs';
                error_log("Failed to update Appika IDs for user $userId");
            }
        } else {
            // Appika creation failed, but local record exists
            $result['success'] = true; // Still consider success since local record exists
            $result['message'] = 'Customer created locally, but Appika sync failed: ' . $appikaResult['message'];
            error_log("Appika customer creation failed for user $userId: " . $appikaResult['message']);
        }
        
    } catch (Exception $e) {
        $result['message'] = 'Error during customer creation: ' . $e->getMessage();
        error_log("Customer creation error: " . $e->getMessage());
    }
    
    return $result;
}

/**
 * Create customer in Appika API
 * @param int $userId Local user ID
 * @param array $customerData Customer data
 * @return array Result with success status
 */
function createCustomerInAppika($userId, $customerData) {
    $result = [
        'success' => false,
        'appika_customer_id' => null,
        'message' => ''
    ];
    
    try {
        // Get API configuration
        $apiConfig = getCustomerApiConfig();
        $apiEndpoint = $apiConfig['endpoint'];
        $apiPath = $apiConfig['path'];
        $apiKey = $apiConfig['key'];
        
        // Prepare customer data for Appika
        $customerNo = 'HI' . $userId;
        $customerName = $customerData['full_name'];
        $customerStartDate = date('Y-m-d');
        
        // Fixed values as per your requirements
        $customerGroup = '10';
        $ofcId = '511';
        $assignTo = '1';
        $creator = '1';
        $status = 'a';
        
        $appikaCustomerData = [
            'no' => $customerNo,
            'name' => $customerName,
            'entity_type' => '1', // 1 for COMPANY, 2 for INDIVIDUAL
            'grp_id' => $customerGroup,
            'ofc_id' => $ofcId,
            'assign2' => $assignTo,
            'creator' => $creator,
            'start_date' => $customerStartDate,
            'status' => $status
        ];
        
        // Check if Guzzle is available
        if (!class_exists('GuzzleHttp\Client')) {
            error_log("Guzzle HTTP Client not available - skipping Appika customer creation");
            return [
                'success' => false,
                'appika_id' => null,
                'appika_customer_id' => null,
                'error' => 'HTTP client not available'
            ];
        }

        // Create Guzzle client (using same settings as working GraphQL functions)
        $client = new \GuzzleHttp\Client([
            'base_uri' => $apiEndpoint,
            'timeout' => 8, // Match GraphQL timeout for consistency
            'connect_timeout' => 5, // Add connection timeout
            'http_errors' => false,
        ]);
        
        // Send customer creation request with better error handling
        try {
            $response = $client->request('POST', $apiPath, [
                'headers' => [
                    'X-api-key' => "{$apiKey}",
                    'Accept' => 'application/json',
                    'Content-Type' => 'application/json',
                ],
                'json' => $appikaCustomerData
            ]);
        } catch (\GuzzleHttp\Exception\ConnectException $e) {
            error_log("Appika Customer API Connection Error: " . $e->getMessage());
            return [
                'success' => false,
                'appika_id' => null,
                'appika_customer_id' => null,
                'error' => 'Connection Error: ' . $e->getMessage()
            ];
        } catch (\GuzzleHttp\Exception\RequestException $e) {
            error_log("Appika Customer API Request Error: " . $e->getMessage());
            return [
                'success' => false,
                'appika_id' => null,
                'appika_customer_id' => null,
                'error' => 'Request Error: ' . $e->getMessage()
            ];
        } catch (\Exception $e) {
            error_log("Appika Customer API Unexpected Error: " . $e->getMessage());
            return [
                'success' => false,
                'appika_id' => null,
                'appika_customer_id' => null,
                'error' => 'Unexpected Error: ' . $e->getMessage()
            ];
        }
        
        $statusCode = $response->getStatusCode();
        $body = $response->getBody()->getContents();
        $responseData = json_decode($body, true);
        
        if ($statusCode >= 200 && $statusCode < 300 && isset($responseData[0]['id'])) {
            $result['success'] = true;
            $result['appika_customer_id'] = $responseData[0]['id'];
            $result['message'] = 'Customer created in Appika successfully';
            
            // Create location if address data provided
            if (!empty($customerData['address']) || !empty($customerData['city'])) {
                createCustomerLocationInAppika($result['appika_customer_id'], $customerData, $customerNo, $customerName);
            }
            
        } else {
            $result['message'] = 'Appika API error: ' . ($responseData['message'] ?? 'Unknown error');
            error_log("Appika customer creation failed - Status: $statusCode, Response: $body");
        }
        
    } catch (Exception $e) {
        $result['message'] = 'Exception during Appika customer creation: ' . $e->getMessage();
        error_log("Appika customer creation exception: " . $e->getMessage());
    }
    
    return $result;
}

/**
 * Create customer location in Appika API
 * @param int $appikaCustomerId Appika customer ID
 * @param array $customerData Customer data with address info
 * @param string $customerNo Customer number
 * @param string $customerName Customer name
 * @return bool Success status
 */
function createCustomerLocationInAppika($appikaCustomerId, $customerData, $customerNo, $customerName) {
    try {
        // Get API configuration
        $apiConfig = getCustomerApiConfig();
        $apiEndpoint = $apiConfig['endpoint'];
        $apiPath = $apiConfig['path'];
        $apiKey = $apiConfig['key'];
        
        // Prepare location data
        $locationData = [
            'loc_code' => 'LOC-' . $customerNo,
            'loc_name' => $customerName . ' Location',
            'add1' => $customerData['address'] ?? '',
            'add2' => $customerData['address2'] ?? '',
            'ccode' => $customerData['country'] ?? '',
            'state_code' => $customerData['state'] ?? '',
            'city' => $customerData['city'] ?? '',
            'status' => 'a',
            'is_primary_loc' => 'y',
            'zip' => $customerData['postal_code'] ?? '',
            'parent_id' => 0,
            'tel_work' => $customerData['phone'] ?? '',
            'contact_pax' => $customerData['contact_person'] ?? $customerName
        ];
        
        // Check if Guzzle is available
        if (!class_exists('GuzzleHttp\Client')) {
            error_log("Guzzle HTTP Client not available - skipping Appika location creation");
            return [
                'success' => false,
                'location_id' => null,
                'error' => 'HTTP client not available'
            ];
        }

        // Create Guzzle client
        $client = new \GuzzleHttp\Client([
            'base_uri' => $apiEndpoint,
            'timeout' => 30,
            'http_errors' => false,
        ]);
        
        // First, check if customer already has locations
        $getLocationsResponse = $client->request('GET', $apiPath . '/' . $appikaCustomerId . '/locations', [
            'headers' => [
                'X-api-key' => "{$apiKey}",
                'Accept' => 'application/json',
            ]
        ]);

        $existingLocations = [];
        if ($getLocationsResponse->getStatusCode() >= 200 && $getLocationsResponse->getStatusCode() < 300) {
            $locationsData = json_decode($getLocationsResponse->getBody()->getContents(), true);
            $existingLocations = $locationsData['items'] ?? [];
        }

        // If there's an existing location (likely empty Location 1), update it instead of creating new one
        if (!empty($existingLocations)) {
            $firstLocation = $existingLocations[0];
            $locationId = $firstLocation['id'];

            error_log("Updating existing location $locationId for customer $appikaCustomerId");

            // Update the first location
            $response = $client->request('PUT', $apiPath . '/' . $appikaCustomerId . '/locations/' . $locationId, [
                'headers' => [
                    'X-api-key' => "{$apiKey}",
                    'Accept' => 'application/json',
                    'Content-Type' => 'application/json',
                ],
                'json' => $locationData
            ]);
        } else {
            error_log("Creating new location for customer $appikaCustomerId");

            // Create new location
            $response = $client->request('POST', $apiPath . '/' . $appikaCustomerId . '/locations', [
                'headers' => [
                    'X-api-key' => "{$apiKey}",
                    'Accept' => 'application/json',
                    'Content-Type' => 'application/json',
                ],
                'json' => $locationData
            ]);
        }

        $statusCode = $response->getStatusCode();
        if ($statusCode >= 200 && $statusCode < 300) {
            error_log("Location operation successful for customer $appikaCustomerId");
            return true;
        } else {
            $responseBody = $response->getBody()->getContents();
            error_log("Failed location operation for customer $appikaCustomerId - Status: $statusCode, Response: $responseBody");
            return false;
        }
        
    } catch (Exception $e) {
        error_log("Exception during location creation: " . $e->getMessage());
        return false;
    }
}

/**
 * Create customer from registration form data
 * @param array $formData Form data from registration
 * @return array Result with success status
 */
function createCustomerFromRegistration($formData) {
    // Map form data to customer data structure
    $customerData = [
        'username' => $formData['username'],
        'email' => $formData['email'],
        'password' => $formData['password_1'],
        'full_name' => $formData['first_name'], // first_name now contains full name
        'address' => $formData['address'] ?? '',
        'address2' => $formData['address2'] ?? '',
        'city' => $formData['city'] ?? '',
        'state' => $formData['state'] ?? '',
        'country' => $formData['country'] ?? '',
        'postal_code' => $formData['postal_code'] ?? '',
        'phone' => $formData['tell'] ?? '',
        'company_name' => $formData['company_name'] ?? '',
        'tax_id' => $formData['tax_id'] ?? '',
        'timezone' => $formData['timezone'] ?? 'UTC'
    ];
    
    return createCustomerMinimal($customerData);
}
?>
