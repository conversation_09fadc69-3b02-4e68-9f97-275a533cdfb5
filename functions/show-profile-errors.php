<?php
/**
 * Show Profile Update Errors
 * Displays the specific errors from profile update attempts
 */

session_start();

echo "<h1>🔍 Profile Update Error Details</h1>";

// Check if there are profile errors in session
if (isset($_SESSION['profile_errors']) && !empty($_SESSION['profile_errors'])) {
    echo "<h2>❌ Profile Update Errors Found:</h2>";
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; color: #721c24; margin: 10px 0;'>";
    echo "<ul>";
    foreach ($_SESSION['profile_errors'] as $error) {
        echo "<li><strong>" . htmlspecialchars($error) . "</strong></li>";
    }
    echo "</ul>";
    echo "</div>";
    
    // Clear the errors after displaying
    unset($_SESSION['profile_errors']);
    
    echo "<h3>🔧 Common Solutions:</h3>";
    echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 5px; color: #0c5460;'>";
    echo "<h4>If you see 'Unable to update profile: Customer not linked to Appika API':</h4>";
    echo "<ul>";
    echo "<li>Your account needs to be properly linked to Appika API</li>";
    echo "<li>Contact support to link your account to Appika</li>";
    echo "<li>This usually happens with older accounts created before Appika integration</li>";
    echo "</ul>";
    
    echo "<h4>If you see 'Failed to update profile in Appika API':</h4>";
    echo "<ul>";
    echo "<li>There might be an API connection issue</li>";
    echo "<li>Check if the Appika API is accessible</li>";
    echo "<li>Verify API credentials are correct</li>";
    echo "</ul>";
    
    echo "<h4>If you see validation errors:</h4>";
    echo "<ul>";
    echo "<li>Make sure all required fields are filled</li>";
    echo "<li>Check that Full Name, Address, City, and Country are not empty</li>";
    echo "</ul>";
    echo "</div>";
    
} else {
    echo "<h2>ℹ️ No Profile Errors Found</h2>";
    echo "<p>There are no profile update errors stored in the session.</p>";
    echo "<p>If you're still experiencing issues, try updating your profile again.</p>";
}

// Show current session info for debugging
echo "<h3>🔍 Debug Information:</h3>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<p><strong>Session ID:</strong> " . session_id() . "</p>";
echo "<p><strong>User Logged In:</strong> " . (isset($_SESSION['username']) ? 'Yes (' . htmlspecialchars($_SESSION['username']) . ')' : 'No') . "</p>";
echo "<p><strong>Session Variables:</strong></p>";
echo "<ul>";
foreach ($_SESSION as $key => $value) {
    if ($key !== 'profile_errors') {
        echo "<li><strong>$key:</strong> " . (is_string($value) ? htmlspecialchars($value) : gettype($value)) . "</li>";
    }
}
echo "</ul>";
echo "</div>";

echo "<hr>";
echo "<h3>🔗 Quick Actions:</h3>";
echo "<p>";
echo "<a href='debug-profile-update.php' style='background: #17a2b8; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px; margin-right: 10px;'>🔍 Debug Profile Data</a>";
echo "<a href='../front-end/profile.php' style='background: #473BF0; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px; margin-right: 10px;'>👤 Back to Profile</a>";
echo "<a href='test-profile-updates.php' style='background: #28a745; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px;'>🧪 Run Tests</a>";
echo "</p>";

echo "<style>";
echo "body { font-family: Arial, sans-serif; margin: 20px; }";
echo "h1, h2, h3 { color: #473BF0; }";
echo "</style>";
?>
