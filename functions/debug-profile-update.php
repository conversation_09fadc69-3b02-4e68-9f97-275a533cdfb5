<?php
/**
 * Debug Profile Update Issues
 * Helps identify why profile updates are failing
 */

session_start();
include('server.php');
include('customer-data-service.php');

echo "<h1>🔍 Debug Profile Update Issues</h1>";

// Check if user is logged in
if (!isset($_SESSION['username'])) {
    echo "<p style='color: red;'>❌ User not logged in. Please log in first.</p>";
    echo "<p><a href='../front-end/sign-in.php'>Go to Sign In</a></p>";
    exit();
}

$username = $_SESSION['username'];
echo "<h2>👤 Debugging for User: " . htmlspecialchars($username) . "</h2>";

try {
    // Test 1: Get user data
    echo "<h3>📊 Test 1: User Data Retrieval</h3>";
    $user = getCompleteCustomerData($username);
    
    if ($user) {
        echo "✅ User data retrieved successfully<br>";
        
        echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h4>User Data Summary:</h4>";
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>Field</th><th>Value</th></tr>";
        echo "<tr><td>Username</td><td>" . htmlspecialchars($user['username'] ?? 'N/A') . "</td></tr>";
        echo "<tr><td>Email</td><td>" . htmlspecialchars($user['email'] ?? 'N/A') . "</td></tr>";
        echo "<tr><td>Appika ID</td><td>" . htmlspecialchars($user['appika_id'] ?? 'N/A') . "</td></tr>";
        echo "<tr><td>Appika Customer ID</td><td>" . htmlspecialchars($user['appika_customer_id'] ?? 'N/A') . "</td></tr>";
        echo "<tr><td>Has Appika Data</td><td>" . ($user['has_appika_data'] ? 'Yes' : 'No') . "</td></tr>";
        echo "<tr><td>Name</td><td>" . htmlspecialchars($user['name'] ?? 'N/A') . "</td></tr>";
        echo "<tr><td>Phone (tel_work)</td><td>" . htmlspecialchars($user['tel_work'] ?? 'N/A') . "</td></tr>";
        echo "<tr><td>Tax ID</td><td>" . htmlspecialchars($user['tax_id'] ?? 'N/A') . "</td></tr>";
        echo "<tr><td>Address</td><td>" . htmlspecialchars($user['address'] ?? 'N/A') . "</td></tr>";
        echo "<tr><td>City</td><td>" . htmlspecialchars($user['city'] ?? 'N/A') . "</td></tr>";
        echo "<tr><td>Country</td><td>" . htmlspecialchars($user['country'] ?? 'N/A') . "</td></tr>";
        echo "</table>";
        echo "</div>";
        
        // Test 2: Check if user can be updated
        echo "<h3>🔧 Test 2: Update Readiness Check</h3>";
        
        if ($user['has_appika_data'] && !empty($user['appika_customer_id'])) {
            echo "✅ User has Appika data and customer ID - Ready for updates<br>";
            echo "<p><strong>Appika Customer ID:</strong> " . htmlspecialchars($user['appika_customer_id']) . "</p>";
        } else {
            echo "❌ User cannot be updated via Appika API<br>";
            echo "<p><strong>Reason:</strong> ";
            if (!$user['has_appika_data']) {
                echo "No Appika data available";
            } elseif (empty($user['appika_customer_id'])) {
                echo "Missing Appika Customer ID";
            }
            echo "</p>";
            
            echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
            echo "<h4>⚠️ Solution:</h4>";
            echo "<p>This user needs to be properly linked to Appika API. The user might have been created before Appika integration was implemented.</p>";
            echo "<p>To fix this, the user needs to:</p>";
            echo "<ol>";
            echo "<li>Have an Appika customer record created</li>";
            echo "<li>Have their local database record updated with appika_id and appika_customer_id</li>";
            echo "</ol>";
            echo "</div>";
        }
        
        // Test 3: API Configuration Check
        echo "<h3>🔗 Test 3: API Configuration Check</h3>";
        
        try {
            require_once '../config/api-config.php';
            $apiConfig = getCustomerApiConfig();
            
            if ($apiConfig && !empty($apiConfig['endpoint']) && !empty($apiConfig['key'])) {
                echo "✅ API configuration is available<br>";
                echo "<p><strong>Endpoint:</strong> " . htmlspecialchars($apiConfig['endpoint']) . "</p>";
                echo "<p><strong>API Key:</strong> " . (strlen($apiConfig['key']) > 10 ? 'Set (length: ' . strlen($apiConfig['key']) . ')' : 'Too short or missing') . "</p>";
            } else {
                echo "❌ API configuration is missing or incomplete<br>";
            }
        } catch (Exception $e) {
            echo "❌ Error loading API configuration: " . htmlspecialchars($e->getMessage()) . "<br>";
        }
        
        // Test 4: Form Data Simulation
        echo "<h3>📝 Test 4: Form Data Simulation</h3>";
        
        echo "<div style='background: #e3f2fd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h4>Sample Form Data for Testing:</h4>";
        echo "<form method='post' action='../functions/edit_profile_appika.php' style='margin: 10px 0;'>";
        echo "<table>";
        echo "<tr><td>Full Name:</td><td><input type='text' name='first_name' value='" . htmlspecialchars($user['name'] ?? 'Test Name') . "' style='width: 200px;'></td></tr>";
        echo "<tr><td>Phone:</td><td><input type='text' name='tell' value='" . htmlspecialchars($user['tel_work'] ?? '*********0') . "' style='width: 200px;'></td></tr>";
        echo "<tr><td>Tax ID:</td><td><input type='text' name='tax_id' value='" . htmlspecialchars($user['tax_id'] ?? '*********') . "' style='width: 200px;'></td></tr>";
        echo "<tr><td>Address:</td><td><input type='text' name='address' value='" . htmlspecialchars($user['address'] ?? 'Test Address') . "' style='width: 200px;'></td></tr>";
        echo "<tr><td>Address 2:</td><td><input type='text' name='address2' value='" . htmlspecialchars($user['address2'] ?? 'Test Address 2') . "' style='width: 200px;'></td></tr>";
        echo "<tr><td>City:</td><td><input type='text' name='city' value='" . htmlspecialchars($user['city'] ?? 'Bangkok') . "' style='width: 200px;'></td></tr>";
        echo "<tr><td>State:</td><td><input type='text' name='state' value='" . htmlspecialchars($user['state'] ?? 'Bangkok') . "' style='width: 200px;'></td></tr>";
        echo "<tr><td>Country:</td><td><input type='text' name='country' value='" . htmlspecialchars($user['country'] ?? 'TH') . "' style='width: 200px;'></td></tr>";
        echo "<tr><td>Postal Code:</td><td><input type='text' name='postal_code' value='" . htmlspecialchars($user['postal_code'] ?? '10110') . "' style='width: 200px;'></td></tr>";
        echo "</table>";
        
        if ($user['has_appika_data'] && !empty($user['appika_customer_id'])) {
            echo "<p><button type='submit' style='background: #473BF0; color: white; padding: 10px 20px; border: none; border-radius: 5px;'>🧪 Test Profile Update</button></p>";
        } else {
            echo "<p><button type='button' disabled style='background: #ccc; color: #666; padding: 10px 20px; border: none; border-radius: 5px;'>❌ Cannot Test (No Appika Data)</button></p>";
        }
        echo "</form>";
        echo "</div>";
        
    } else {
        echo "❌ Failed to retrieve user data<br>";
    }
    
} catch (Exception $e) {
    echo "<h3>💥 Error Occurred:</h3>";
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; color: #721c24;'>";
    echo "<p><strong>Error Message:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><strong>File:</strong> " . htmlspecialchars($e->getFile()) . "</p>";
    echo "<p><strong>Line:</strong> " . $e->getLine() . "</p>";
    echo "</div>";
}

// Test 5: Check error logs
echo "<h3>📋 Test 5: Recent Error Logs</h3>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<p><strong>Check your server error logs for entries containing:</strong></p>";
echo "<ul>";
echo "<li>\"Error updating profile for user $username\"</li>";
echo "<li>\"Profile update failed\"</li>";
echo "<li>\"Failed to update profile in Appika API\"</li>";
echo "</ul>";
echo "<p><strong>Common log locations:</strong></p>";
echo "<ul>";
echo "<li>/var/log/apache2/error.log</li>";
echo "<li>/var/log/nginx/error.log</li>";
echo "<li>Your hosting control panel error logs</li>";
echo "</ul>";
echo "</div>";

echo "<hr>";
echo "<h3>🔗 Quick Links:</h3>";
echo "<p>";
echo "<a href='../front-end/profile.php' style='background: #473BF0; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px; margin-right: 10px;'>Back to Profile</a>";
echo "<a href='test-profile-updates.php' style='background: #28a745; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px;'>Run Full Tests</a>";
echo "</p>";

echo "<style>";
echo "body { font-family: Arial, sans-serif; margin: 20px; }";
echo "h1, h2, h3 { color: #473BF0; }";
echo "table { margin: 10px 0; }";
echo "th, td { padding: 8px; text-align: left; }";
echo "</style>";
?>
