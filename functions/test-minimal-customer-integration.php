<?php
/**
 * Test Minimal Customer Integration
 * Tests the new customer data architecture: minimal database + Appika API
 */

echo "<h1>🧪 Minimal Customer Integration Test</h1>";

// Include required files
include_once(__DIR__ . '/server.php');
include_once(__DIR__ . '/customer-data-service.php');
include_once(__DIR__ . '/create-customer-minimal.php');

$test_results = [];
$total_tests = 0;
$passed_tests = 0;

// Test 1: Customer Data Service Functions
echo "<h2>📊 Test 1: Customer Data Service Functions</h2>";
$total_tests++;
try {
    // Test if functions are available
    $functions_to_test = [
        'getCompleteCustomerData',
        'getLocalCustomerEssentials', 
        'fetchAppikaCustomerById',
        'mergeCustomerData',
        'createMinimalCustomerRecord'
    ];
    
    $missing_functions = [];
    foreach ($functions_to_test as $function) {
        if (!function_exists($function)) {
            $missing_functions[] = $function;
        }
    }
    
    if (empty($missing_functions)) {
        echo "✅ All customer data service functions are available<br>";
        $test_results['data_service'] = 'PASS';
        $passed_tests++;
    } else {
        echo "❌ Missing functions: " . implode(', ', $missing_functions) . "<br>";
        $test_results['data_service'] = 'FAIL';
    }
} catch (Exception $e) {
    echo "❌ Error testing data service functions: " . htmlspecialchars($e->getMessage()) . "<br>";
    $test_results['data_service'] = 'FAIL';
}

// Test 2: Database Structure Check
echo "<h2>🗄️ Test 2: Database Structure Check</h2>";
$total_tests++;
try {
    // Check if user table has the minimal required columns
    $required_columns = [
        'id', 'appika_id', 'appika_customer_id', 'username', 'email', 
        'password', 'premium_tickets', 'ultimate_tickets', 'registration_time', 
        'timezone', 'stripe_customer_id'
    ];
    
    $stmt = $conn->prepare("SHOW COLUMNS FROM user");
    $stmt->execute();
    $result = $stmt->get_result();
    
    $existing_columns = [];
    while ($row = $result->fetch_assoc()) {
        $existing_columns[] = $row['Field'];
    }
    
    $missing_columns = array_diff($required_columns, $existing_columns);
    $extra_columns = array_diff($existing_columns, $required_columns);
    
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>Database Column Analysis:</h4>";
    echo "<p><strong>Required columns:</strong> " . implode(', ', $required_columns) . "</p>";
    echo "<p><strong>Existing columns:</strong> " . implode(', ', $existing_columns) . "</p>";
    
    if (!empty($missing_columns)) {
        echo "<p style='color: red;'><strong>Missing columns:</strong> " . implode(', ', $missing_columns) . "</p>";
    }
    
    if (!empty($extra_columns)) {
        echo "<p style='color: orange;'><strong>Extra columns (should be removed for minimal approach):</strong> " . implode(', ', $extra_columns) . "</p>";
    }
    echo "</div>";
    
    if (empty($missing_columns)) {
        echo "✅ All required columns exist<br>";
        $test_results['database_structure'] = 'PASS';
        $passed_tests++;
    } else {
        echo "❌ Missing required columns<br>";
        $test_results['database_structure'] = 'FAIL';
    }
} catch (Exception $e) {
    echo "❌ Error checking database structure: " . htmlspecialchars($e->getMessage()) . "<br>";
    $test_results['database_structure'] = 'FAIL';
}

// Test 3: Sample Customer Data Retrieval
echo "<h2>👤 Test 3: Sample Customer Data Retrieval</h2>";
$total_tests++;
try {
    // Get a sample user
    $stmt = $conn->prepare("SELECT username FROM user LIMIT 1");
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        $user = $result->fetch_assoc();
        $username = $user['username'];
        
        echo "<p><strong>Testing with username:</strong> " . htmlspecialchars($username) . "</p>";
        
        // Test local data retrieval
        $localData = getLocalCustomerEssentials($username);
        if ($localData) {
            echo "✅ Local customer data retrieved successfully<br>";
            echo "<div style='background: #e8f5e8; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
            echo "<strong>Local Data:</strong><br>";
            echo "- ID: " . htmlspecialchars($localData['id']) . "<br>";
            echo "- Username: " . htmlspecialchars($localData['username']) . "<br>";
            echo "- Email: " . htmlspecialchars($localData['email']) . "<br>";
            echo "- Appika ID: " . htmlspecialchars($localData['appika_id'] ?: 'Not set') . "<br>";
            echo "- Appika Customer ID: " . htmlspecialchars($localData['appika_customer_id'] ?: 'Not set') . "<br>";
            echo "</div>";
        } else {
            echo "❌ Failed to retrieve local customer data<br>";
        }
        
        // Test complete data retrieval
        $completeData = getCompleteCustomerData($username);
        if ($completeData) {
            echo "✅ Complete customer data retrieved successfully<br>";
            echo "<div style='background: #e8f0ff; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
            echo "<strong>Complete Data:</strong><br>";
            echo "- Has Appika Data: " . ($completeData['has_appika_data'] ? 'Yes' : 'No') . "<br>";
            echo "- Display Name: " . htmlspecialchars(getCustomerDisplayName($completeData)) . "<br>";
            if ($completeData['has_appika_data']) {
                echo "- Name from Appika: " . htmlspecialchars($completeData['name'] ?: 'Not set') . "<br>";
                echo "- Address: " . htmlspecialchars(formatCustomerAddress($completeData) ?: 'Not set') . "<br>";
            }
            echo "</div>";
            
            $test_results['data_retrieval'] = 'PASS';
            $passed_tests++;
        } else {
            echo "❌ Failed to retrieve complete customer data<br>";
            $test_results['data_retrieval'] = 'FAIL';
        }
    } else {
        echo "⚠️ No users found in database for testing<br>";
        $test_results['data_retrieval'] = 'WARNING';
    }
} catch (Exception $e) {
    echo "❌ Error testing customer data retrieval: " . htmlspecialchars($e->getMessage()) . "<br>";
    $test_results['data_retrieval'] = 'FAIL';
}

// Test 4: Appika API Connection
echo "<h2>🌐 Test 4: Appika API Connection</h2>";
$total_tests++;
try {
    // Test API configuration
    include_once(__DIR__ . '/../config/api-config.php');
    $apiConfig = getCustomerApiConfig();
    
    if ($apiConfig && !empty($apiConfig['endpoint']) && !empty($apiConfig['key'])) {
        echo "✅ API configuration loaded successfully<br>";
        echo "<p><strong>API Endpoint:</strong> " . htmlspecialchars($apiConfig['endpoint']) . "</p>";
        echo "<p><strong>API Path:</strong> " . htmlspecialchars($apiConfig['path']) . "</p>";
        
        // Test if we can make a simple API call (if there's a customer with Appika ID)
        $stmt = $conn->prepare("SELECT appika_customer_id FROM user WHERE appika_customer_id IS NOT NULL LIMIT 1");
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result->num_rows > 0) {
            $user = $result->fetch_assoc();
            $appikaCustomerId = $user['appika_customer_id'];
            
            echo "<p><strong>Testing API call with customer ID:</strong> " . htmlspecialchars($appikaCustomerId) . "</p>";
            
            $appikaData = fetchAppikaCustomerById($appikaCustomerId);
            if ($appikaData) {
                echo "✅ Appika API call successful<br>";
                echo "<div style='background: #fff3cd; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
                echo "<strong>Sample Appika Data:</strong><br>";
                echo "- Customer Name: " . htmlspecialchars($appikaData['name'] ?? 'Not set') . "<br>";
                echo "- Status: " . htmlspecialchars($appikaData['status'] ?? 'Not set') . "<br>";
                echo "- Locations: " . count($appikaData['locations'] ?? []) . "<br>";
                echo "</div>";
                
                $test_results['api_connection'] = 'PASS';
                $passed_tests++;
            } else {
                echo "❌ Appika API call failed<br>";
                $test_results['api_connection'] = 'FAIL';
            }
        } else {
            echo "⚠️ No customers with Appika ID found for API testing<br>";
            $test_results['api_connection'] = 'WARNING';
        }
    } else {
        echo "❌ API configuration not properly loaded<br>";
        $test_results['api_connection'] = 'FAIL';
    }
} catch (Exception $e) {
    echo "❌ Error testing API connection: " . htmlspecialchars($e->getMessage()) . "<br>";
    $test_results['api_connection'] = 'FAIL';
}

// Test 5: Customer Creation Functions
echo "<h2>🆕 Test 5: Customer Creation Functions</h2>";
$total_tests++;
try {
    // Test if creation functions are available
    $creation_functions = [
        'createCustomerMinimal',
        'createCustomerInAppika',
        'createCustomerFromRegistration'
    ];
    
    $missing_creation_functions = [];
    foreach ($creation_functions as $function) {
        if (!function_exists($function)) {
            $missing_creation_functions[] = $function;
        }
    }
    
    if (empty($missing_creation_functions)) {
        echo "✅ All customer creation functions are available<br>";
        $test_results['creation_functions'] = 'PASS';
        $passed_tests++;
    } else {
        echo "❌ Missing creation functions: " . implode(', ', $missing_creation_functions) . "<br>";
        $test_results['creation_functions'] = 'FAIL';
    }
} catch (Exception $e) {
    echo "❌ Error testing creation functions: " . htmlspecialchars($e->getMessage()) . "<br>";
    $test_results['creation_functions'] = 'FAIL';
}

// Summary
echo "<hr>";
echo "<h2>📊 Test Summary</h2>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px;'>";
echo "<h3>Results: $passed_tests/$total_tests tests passed</h3>";

foreach ($test_results as $test => $result) {
    $icon = $result == 'PASS' ? '✅' : ($result == 'WARNING' ? '⚠️' : '❌');
    $color = $result == 'PASS' ? 'green' : ($result == 'WARNING' ? 'orange' : 'red');
    echo "<p style='color: $color;'>$icon " . ucwords(str_replace('_', ' ', $test)) . ": $result</p>";
}

if ($passed_tests == $total_tests) {
    echo "<div style='color: green; font-weight: bold; padding: 10px; background: #d4edda; border-radius: 5px;'>";
    echo "🎉 All tests passed! Your minimal customer integration is working correctly.";
    echo "</div>";
} else {
    echo "<div style='color: orange; font-weight: bold; padding: 10px; background: #fff3cd; border-radius: 5px;'>";
    echo "⚠️ Some tests failed or have warnings. Please review the issues above.";
    echo "</div>";
}

echo "</div>";

echo "<hr>";
echo "<h3>🔧 Next Steps:</h3>";
echo "<ol>";
echo "<li>If database structure test shows extra columns, consider removing them for a truly minimal approach</li>";
echo "<li>Test the new profile page: <a href='../front-end/profile-minimal.php'>profile-minimal.php</a></li>";
echo "<li>Update your registration forms to use the new minimal customer creation</li>";
echo "<li>Test customer registration with the new system</li>";
echo "<li>Verify that customer data displays correctly from Appika API</li>";
echo "</ol>";

echo "<style>";
echo "body { font-family: Arial, sans-serif; margin: 20px; }";
echo "h1, h2, h3 { color: #473BF0; }";
echo "code { background: #f8f9fa; padding: 2px 4px; border-radius: 3px; }";
echo "</style>";
?>
