<?php
/**
 * Send Purchase Receipt Email
 * Compatible with HelloIT's purchasetickets database structure
 * Usage: sendPurchaseReceiptEmail($transactionId, $recipientEmail);
 */

function sendPurchaseReceiptEmail($transactionId, $recipientEmail = null, $isNewUser = false, $userCredentials = null) {
    include_once(__DIR__ . '/server.php');
    global $conn;

    try {
        // Fetch purchase details from purchasetickets table
        $stmt = $conn->prepare("
            SELECT pt.*, u.first_name, u.email, u.company_name, u.address, u.city, u.state, u.country
            FROM purchasetickets pt
            LEFT JOIN user u ON pt.username = u.username
            WHERE pt.transactionid = ?
            ORDER BY pt.purchase_time DESC
        ");
        $stmt->bind_param("s", $transactionId);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($result->num_rows === 0) {
            error_log("Receipt: No purchase found for transaction ID: $transactionId");
            return false;
        }

        $purchases = [];
        $customerInfo = null;
        $totalAmount = 0;
        $totalTickets = 0;

        while ($row = $result->fetch_assoc()) {
            $purchases[] = $row;
            if (!$customerInfo) {
                $customerInfo = $row;
            }
            $totalAmount += $row['dollar_price_per_package'];
            $totalTickets += $row['remaining_tickets'];
        }

        // Use provided email or customer's email
        $emailTo = $recipientEmail ?: $customerInfo['email'];
        if (!$emailTo) {
            error_log("Receipt: No email address available for transaction: $transactionId");
            return false;
        }

        // Validate email format
        if (!filter_var($emailTo, FILTER_VALIDATE_EMAIL)) {
            error_log("Receipt: Invalid email format: $emailTo for transaction: $transactionId");
            return false;
        }

        // Generate receipt HTML
        $receiptHtml = generateReceiptHTML($purchases, $customerInfo, $transactionId, $totalAmount, $totalTickets, $isNewUser, $userCredentials);

        // Send email using simple, proven method (same as HelloITOld)
        $subject = 'Your HelloIT Purchase Receipt - Transaction #' . substr($transactionId, -8);
        $headers = "From: <EMAIL>\r\n";
        $headers .= "Reply-To: <EMAIL>\r\n";
        $headers .= "MIME-Version: 1.0\r\n";
        $headers .= "Content-type: text/html; charset=UTF-8\r\n";

        // Log email attempt
        error_log("Receipt: Attempting to send email to $emailTo for transaction $transactionId");
        error_log("Receipt: Email subject: $subject");

        $emailSent = mail($emailTo, $subject, $receiptHtml, $headers);

        if ($emailSent) {
            error_log("Receipt: Email sent successfully to $emailTo for transaction $transactionId");
        } else {
            error_log("Receipt: Failed to send email to $emailTo for transaction $transactionId");
        }

        return $emailSent;

    } catch (Exception $e) {
        error_log("Receipt: Error sending receipt email: " . $e->getMessage());
        error_log("Receipt: Stack trace: " . $e->getTraceAsString());
        return false;
    }
}

function generateReceiptHTML($purchases, $customerInfo, $transactionId, $totalAmount, $totalTickets, $isNewUser = false, $userCredentials = null) {
    // Format customer name (using only first_name since last_name column was removed)
    $customerName = trim($customerInfo['first_name'] ?? '');
    if (empty($customerName)) {
        $customerName = $customerInfo['username'] ?? 'Valued Customer';
    }
    
    // Format purchase date in user's timezone using existing system function
    $purchaseDate = showCustomerTime($customerInfo['purchase_time'], 'F j, Y \a\t g:i A T');
    
    // Generate receipt number (last 8 characters of transaction ID)
    $receiptNumber = 'HIT-' . strtoupper(substr($transactionId, -8));
    
    $html = '
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <title>HelloIT Purchase Receipt</title>
    </head>
    <body style="margin:0;padding:20px;font-family:Arial,sans-serif;background-color:#f5f5f5;">
        <div style="max-width:600px;margin:0 auto;background-color:#ffffff;border:1px solid #e0e0e0;border-radius:8px;overflow:hidden;">
            
            <!-- Header -->
            <div style="background-color:#473BF0;color:white;padding:30px;text-align:center;">
                <img src="https://helloit.io/image/wp/HelloIT-new.png" alt="HelloIT Logo" style="height:50px;margin-bottom:15px;">
                <h1 style="margin:0;font-size:28px;font-weight:bold;">Purchase Receipt</h1>
                <p style="margin:5px 0 0 0;font-size:16px;opacity:0.9;">Thank you for your purchase!</p>
            </div>
            
            <!-- Receipt Details -->
            <div style="padding:30px;">
                <div style="background-color:#f8f9fa;padding:20px;border-radius:6px;margin-bottom:25px;">
                    <table style="width:100%;border-collapse:collapse;">
                        <tr>
                            <td style="padding:8px 0;font-weight:bold;color:#333;">Receipt #:</td>
                            <td style="padding:8px 0;text-align:right;">' . htmlspecialchars($receiptNumber) . '</td>
                        </tr>
                        <tr>
                            <td style="padding:8px 0;font-weight:bold;color:#333;">Date:</td>
                            <td style="padding:8px 0;text-align:right;">' . htmlspecialchars($purchaseDate) . '</td>
                        </tr>
                        <tr>
                            <td style="padding:8px 0;font-weight:bold;color:#333;">Customer:</td>
                            <td style="padding:8px 0;text-align:right;">' . htmlspecialchars($customerName) . '</td>
                        </tr>
                        <tr>
                            <td style="padding:8px 0;font-weight:bold;color:#333;">Email:</td>
                            <td style="padding:8px 0;text-align:right;">' . htmlspecialchars($customerInfo['email']) . '</td>
                        </tr>';
    
    if (!empty($customerInfo['company_name'])) {
        $html .= '
                        <tr>
                            <td style="padding:8px 0;font-weight:bold;color:#333;">Company:</td>
                            <td style="padding:8px 0;text-align:right;">' . htmlspecialchars($customerInfo['company_name']) . '</td>
                        </tr>';
    }
    
    $html .= '
                    </table>
                </div>

                <!-- Items Purchased -->
                <h3 style="color:#333;margin-bottom:15px;font-size:18px;">Items Purchased</h3>
                <table style="width:100%;border-collapse:collapse;margin-bottom:25px;border:1px solid #e0e0e0;">
                    <thead>
                        <tr style="background-color:#473BF0;color:white;">
                            <th style="padding:12px;text-align:left;font-weight:bold;">Service</th>
                            <th style="padding:12px;text-align:center;font-weight:bold;">Package</th>
                            <th style="padding:12px;text-align:center;font-weight:bold;">Tickets</th>
                            <th style="padding:12px;text-align:right;font-weight:bold;">Price</th>
                        </tr>
                    </thead>
                    <tbody>';

    foreach ($purchases as $purchase) {
        $ticketTypeDisplay = ucfirst(strtolower($purchase['ticket_type']));
        $packageDisplay = formatPackageSize($purchase['package_size']);
        $description = getTicketTypeDescription($purchase['ticket_type']);

        $html .= '
                        <tr style="border-bottom:1px solid #e0e0e0;">
                            <td style="padding:15px;vertical-align:top;">
                                <div style="font-weight:bold;color:#333;margin-bottom:5px;">' . htmlspecialchars($ticketTypeDisplay) . '</div>
                                <div style="font-size:13px;color:#666;line-height:1.4;">' . htmlspecialchars($description) . '</div>
                            </td>
                            <td style="padding:15px;text-align:center;color:#666;">' . htmlspecialchars($packageDisplay) . '</td>
                            <td style="padding:15px;text-align:center;">
                                <span style="background-color:#e8f5e8;color:#2d5a2d;padding:4px 8px;border-radius:4px;font-weight:bold;">' .
                                number_format($purchase['numbers_per_package']) . '</span>
                            </td>
                            <td style="padding:15px;text-align:right;font-weight:bold;color:#333;">$' .
                            number_format($purchase['dollar_price_per_package'], 2) . '</td>
                        </tr>';
    }

    $html .= '
                    </tbody>
                </table>

                <!-- Summary -->
                <div style="background-color:#f8f9fa;padding:20px;border-radius:6px;margin-bottom:25px;">
                    <table style="width:100%;border-collapse:collapse;">
                        <tr>
                            <td style="padding:8px 0;font-size:16px;color:#333;">Total Tickets:</td>
                            <td style="padding:8px 0;text-align:right;font-size:16px;font-weight:bold;color:#473BF0;">' .
                            number_format($totalTickets) . ' tickets</td>
                        </tr>
                        <tr style="border-top:2px solid #473BF0;">
                            <td style="padding:15px 0 8px 0;font-size:20px;font-weight:bold;color:#333;">Total Amount:</td>
                            <td style="padding:15px 0 8px 0;text-align:right;font-size:24px;font-weight:bold;color:#473BF0;">$' .
                            number_format($totalAmount, 2) . '</td>
                        </tr>
                    </table>
                </div>';

    // Add new user credentials section if this is a new user
    if ($isNewUser && $userCredentials) {
        $html .= '
                <!-- New Account Credentials -->
                <div style="background-color:#d1ecf1;border:1px solid #bee5eb;padding:20px;border-radius:6px;margin-bottom:25px;">
                    <h4 style="margin:0 0 15px 0;color:#0c5460;"><i style="margin-right:8px;">🔐</i>Your Account Login Credentials</h4>
                    <p style="margin:0 0 15px 0;color:#0c5460;line-height:1.5;">
                        A new account has been created for you. Here are your login credentials:
                    </p>
                    <div style="background-color:#ffffff;border:1px solid #bee5eb;padding:15px;border-radius:4px;margin-bottom:15px;">
                        <p style="margin:0 0 8px 0;color:#333;"><strong>Email:</strong> ' . htmlspecialchars($userCredentials['email']) . '</p>
                        <p style="margin:0 0 8px 0;color:#333;"><strong>Password:</strong> ' . htmlspecialchars($userCredentials['password']) . '</p>
                    </div>
                    <div style="background-color:#f8d7da;border:1px solid #f5c6cb;padding:15px;border-radius:4px;color:#721c24;">
                        <p style="margin:0 0 8px 0;"><strong>⚠️ Important Security Notice:</strong></p>
                        <p style="margin:0;line-height:1.5;">
                            For your security, please <strong>reset your password</strong> after your first login.
                            You can do this by going to your profile settings or using the "Forgot Password" option on the login page.
                        </p>
                    </div>
                </div>';
    }

    $html .= '
                <!-- Support Information -->
                <div style="background-color:#fff3cd;border:1px solid #ffeaa7;padding:20px;border-radius:6px;margin-bottom:25px;">
                    <h4 style="margin:0 0 10px 0;color:#856404;">📞 Need Support?</h4>
                    <p style="margin:0;color:#856404;line-height:1.5;">
                        Your tickets are now available in your HelloIT account. If you need assistance,
                        please contact our support team at <strong><EMAIL></strong> or create a ticket
                        through your dashboard.
                    </p>
                </div>

                <!-- Footer -->
                <div style="text-align:center;padding:20px 0;border-top:1px solid #e0e0e0;color:#666;font-size:13px;">
                    <p style="margin:0 0 10px 0;"><strong>HelloIT - Professional IT Support Services</strong></p>
                    <p style="margin:0 0 5px 0;">Email: <EMAIL> | Website: helloit.io</p>
                    <p style="margin:0;font-size:12px;">This is an automated receipt. Please keep this for your records.</p>
                </div>
            </div>
        </div>
    </body>
    </html>';

    return $html;
}

/**
 * Get ticket type description for receipt
 */
function getTicketTypeDescription($ticketType) {
    switch (strtoupper($ticketType)) {
        case 'STARTER':
            return 'Basic IT Support - Ticketing System Access';
        case 'PREMIUM':
        case 'BUSINESS':
            return 'Advanced IT Support - Ticketing, Chat, Email & Remote Access';
        case 'ULTIMATE':
            return 'Complete IT Support - Full Service Package with Priority Support';
        default:
            return 'IT Support Tickets';
    }
}

/**
 * Format package size for display
 */
function formatPackageSize($packageSize) {
    $sizeMap = [
        'packageXS' => 'Extra Small Package',
        'packageS' => 'Small Package', 
        'packageM' => 'Medium Package',
        'packageL' => 'Large Package',
        'packageXL' => 'Extra Large Package'
    ];
    
    return $sizeMap[$packageSize] ?? $packageSize;
}
?>