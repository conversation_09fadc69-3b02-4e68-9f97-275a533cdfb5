<?php
session_start();
include('server.php');

header('Content-Type: application/json');

// Check if this is a POST request with logout parameter
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['logout'])) {
    try {
        // Preserve guest cart and admin session data before logout
        $guest_cart = isset($_SESSION['guest_cart']) ? $_SESSION['guest_cart'] : null;
        $admin_username = isset($_SESSION['admin_username']) ? $_SESSION['admin_username'] : null;
        $admin_id = isset($_SESSION['admin_id']) ? $_SESSION['admin_id'] : null;
        $admin_role = isset($_SESSION['admin_role']) ? $_SESSION['admin_role'] : null;

        // Only unset front-end user session variables, not admin or cart variables
        unset($_SESSION['username']);
        unset($_SESSION['user_id']);
        unset($_SESSION['success']);
        unset($_SESSION['transferred_cart_hash']); // Clear transfer tracking
        unset($_SESSION['pre_checkout_cart_transferred']);

        // If no admin session exists, destroy and recreate session to preserve cart
        if (!$admin_username) {
            session_destroy();
            session_start();

            // Restore guest cart after session recreation
            if ($guest_cart) {
                $_SESSION['guest_cart'] = $guest_cart;
            }
        }

        // Return success response
        echo json_encode([
            'success' => true,
            'message' => 'Logged out successfully'
        ]);
    } catch (Exception $e) {
        // Return error response
        echo json_encode([
            'success' => false,
            'message' => 'Logout failed: ' . $e->getMessage()
        ]);
    }
} else {
    // Invalid request method or missing parameter
    echo json_encode([
        'success' => false,
        'message' => 'Invalid logout request'
    ]);
}
?>
