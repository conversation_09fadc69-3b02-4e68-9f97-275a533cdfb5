<?php
/**
 * Debug Appika Connection
 * Simple test page to debug Appika connectivity issues
 */

include_once('appika-status-checker.php');

echo "<h1>Appika Connection Debug</h1>";
echo "<p>Testing Appika connectivity...</p>";

// Test the status checker
echo "<h2>Status Checker Results:</h2>";
$status = checkAppikaStatus();

foreach ($status as $type => $result) {
    echo "<div style='border: 1px solid #ccc; margin: 10px 0; padding: 15px; border-radius: 5px;'>";
    echo "<h3>" . ucfirst($type) . " API</h3>";
    echo "<p><strong>Online:</strong> " . ($result['online'] ? '✅ YES' : '❌ NO') . "</p>";
    echo "<p><strong>HTTP Code:</strong> " . $result['http_code'] . "</p>";
    echo "<p><strong>Error:</strong> " . ($result['error'] ? $result['error'] : 'None') . "</p>";
    echo "<p><strong>Response Length:</strong> " . $result['response_length'] . " bytes</p>";
    echo "</div>";
}

echo "<h2>Overall Status:</h2>";
$isOnline = isAppikaOnline();
echo "<p><strong>Appika Online:</strong> " . ($isOnline ? '✅ YES' : '❌ NO') . "</p>";

echo "<h2>Manual cURL Test:</h2>";

// Manual test of GraphQL endpoint
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'https://dev-sgsg-tktapi.appika.com/graphql');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 10);
curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 5);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'Accept: application/json'
]);
$query = json_encode(['query' => '{ __schema { queryType { name } } }']);
curl_setopt($ch, CURLOPT_POSTFIELDS, $query);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

echo "<div style='border: 1px solid #ccc; margin: 10px 0; padding: 15px; border-radius: 5px;'>";
echo "<h3>Manual GraphQL Test</h3>";
echo "<p><strong>HTTP Code:</strong> $httpCode</p>";
echo "<p><strong>Error:</strong> " . ($error ? $error : 'None') . "</p>";
echo "<p><strong>Response:</strong></p>";
echo "<pre style='background: #f5f5f5; padding: 10px; border-radius: 3px; max-height: 200px; overflow-y: auto;'>";
echo htmlspecialchars(substr($response, 0, 1000));
if (strlen($response) > 1000) echo "\n... (truncated)";
echo "</pre>";
echo "</div>";

// Test customer API
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'https://dev-api-pooh-sgsg.appika.com/contact/customers');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 10);
curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 5);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

echo "<div style='border: 1px solid #ccc; margin: 10px 0; padding: 15px; border-radius: 5px;'>";
echo "<h3>Manual Customer API Test</h3>";
echo "<p><strong>HTTP Code:</strong> $httpCode</p>";
echo "<p><strong>Error:</strong> " . ($error ? $error : 'None') . "</p>";
echo "<p><strong>Response:</strong></p>";
echo "<pre style='background: #f5f5f5; padding: 10px; border-radius: 3px; max-height: 200px; overflow-y: auto;'>";
echo htmlspecialchars(substr($response, 0, 1000));
if (strlen($response) > 1000) echo "\n... (truncated)";
echo "</pre>";
echo "</div>";

echo "<h2>Debug Info:</h2>";
echo "<p><strong>PHP Version:</strong> " . phpversion() . "</p>";
echo "<p><strong>cURL Version:</strong> " . curl_version()['version'] . "</p>";
echo "<p><strong>Current Time:</strong> " . date('Y-m-d H:i:s') . "</p>";

echo "<hr>";
echo "<p><a href='?refresh=1'>🔄 Refresh Test</a></p>";
?>
