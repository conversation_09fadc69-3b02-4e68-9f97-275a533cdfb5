<?php
/**
 * Timezone Detection Functions
 * Detects appropriate timezone based on country code
 */

/**
 * Get timezone based on country code
 * @param string $countryCode 2-letter country code (e.g., 'TH', 'JP', 'US')
 * @return string Timezone identifier
 */
function getTimezoneByCountry($countryCode) {
    // Country to timezone mapping
    $countryTimezones = [
        // Asia Pacific
        'TH' => 'Asia/Bangkok',           // Thailand
        'JP' => 'Asia/Tokyo',             // Japan
        'SG' => 'Asia/Singapore',         // Singapore
        'MY' => 'Asia/Kuala_Lumpur',      // Malaysia
        'ID' => 'Asia/Jakarta',           // Indonesia
        'PH' => 'Asia/Manila',            // Philippines
        'VN' => 'Asia/Ho_Chi_Minh',       // Vietnam
        'KR' => 'Asia/Seoul',             // South Korea
        'CN' => 'Asia/Shanghai',          // China
        'HK' => 'Asia/Hong_Kong',         // Hong Kong
        'TW' => 'Asia/Taipei',            // Taiwan
        'IN' => 'Asia/Kolkata',           // India
        'AU' => 'Australia/Sydney',       // Australia
        'NZ' => 'Pacific/Auckland',       // New Zealand
        
        // Europe
        'GB' => 'Europe/London',          // United Kingdom
        'DE' => 'Europe/Berlin',          // Germany
        'FR' => 'Europe/Paris',           // France
        'IT' => 'Europe/Rome',            // Italy
        'ES' => 'Europe/Madrid',          // Spain
        'NL' => 'Europe/Amsterdam',       // Netherlands
        'CH' => 'Europe/Zurich',          // Switzerland
        'AT' => 'Europe/Vienna',          // Austria
        'BE' => 'Europe/Brussels',        // Belgium
        'SE' => 'Europe/Stockholm',       // Sweden
        'NO' => 'Europe/Oslo',            // Norway
        'DK' => 'Europe/Copenhagen',      // Denmark
        'FI' => 'Europe/Helsinki',        // Finland
        'PL' => 'Europe/Warsaw',          // Poland
        'CZ' => 'Europe/Prague',          // Czech Republic
        'RU' => 'Europe/Moscow',          // Russia
        
        // Americas
        'US' => 'America/New_York',       // United States (Eastern)
        'CA' => 'America/Toronto',        // Canada (Eastern)
        'MX' => 'America/Mexico_City',    // Mexico
        'BR' => 'America/Sao_Paulo',      // Brazil
        'AR' => 'America/Argentina/Buenos_Aires', // Argentina
        'CL' => 'America/Santiago',       // Chile
        'CO' => 'America/Bogota',         // Colombia
        'PE' => 'America/Lima',           // Peru
        
        // Middle East & Africa
        'AE' => 'Asia/Dubai',             // UAE
        'SA' => 'Asia/Riyadh',            // Saudi Arabia
        'IL' => 'Asia/Jerusalem',         // Israel
        'TR' => 'Europe/Istanbul',        // Turkey
        'EG' => 'Africa/Cairo',           // Egypt
        'ZA' => 'Africa/Johannesburg',    // South Africa
        'NG' => 'Africa/Lagos',           // Nigeria
        'KE' => 'Africa/Nairobi',         // Kenya
    ];
    
    // Convert to uppercase for consistency
    $countryCode = strtoupper($countryCode);
    
    // Return specific timezone if found, otherwise default to UTC
    return $countryTimezones[$countryCode] ?? 'UTC';
}

/**
 * Get user-friendly timezone name
 * @param string $timezone Timezone identifier
 * @return string User-friendly timezone name
 */
function getTimezoneDisplayName($timezone) {
    $timezoneNames = [
        'Asia/Bangkok' => 'Bangkok (GMT+7)',
        'Asia/Tokyo' => 'Tokyo (GMT+9)',
        'Asia/Singapore' => 'Singapore (GMT+8)',
        'Asia/Kuala_Lumpur' => 'Kuala Lumpur (GMT+8)',
        'Asia/Jakarta' => 'Jakarta (GMT+7)',
        'Asia/Manila' => 'Manila (GMT+8)',
        'Asia/Ho_Chi_Minh' => 'Ho Chi Minh City (GMT+7)',
        'Asia/Seoul' => 'Seoul (GMT+9)',
        'Asia/Shanghai' => 'Shanghai (GMT+8)',
        'Asia/Hong_Kong' => 'Hong Kong (GMT+8)',
        'Asia/Taipei' => 'Taipei (GMT+8)',
        'Asia/Kolkata' => 'Kolkata (GMT+5:30)',
        'Australia/Sydney' => 'Sydney (GMT+10)',
        'Pacific/Auckland' => 'Auckland (GMT+12)',
        
        'Europe/London' => 'London (GMT+0)',
        'Europe/Berlin' => 'Berlin (GMT+1)',
        'Europe/Paris' => 'Paris (GMT+1)',
        'Europe/Rome' => 'Rome (GMT+1)',
        'Europe/Madrid' => 'Madrid (GMT+1)',
        'Europe/Amsterdam' => 'Amsterdam (GMT+1)',
        'Europe/Zurich' => 'Zurich (GMT+1)',
        'Europe/Vienna' => 'Vienna (GMT+1)',
        'Europe/Brussels' => 'Brussels (GMT+1)',
        'Europe/Stockholm' => 'Stockholm (GMT+1)',
        'Europe/Oslo' => 'Oslo (GMT+1)',
        'Europe/Copenhagen' => 'Copenhagen (GMT+1)',
        'Europe/Helsinki' => 'Helsinki (GMT+2)',
        'Europe/Warsaw' => 'Warsaw (GMT+1)',
        'Europe/Prague' => 'Prague (GMT+1)',
        'Europe/Moscow' => 'Moscow (GMT+3)',
        
        'America/New_York' => 'New York (GMT-5)',
        'America/Toronto' => 'Toronto (GMT-5)',
        'America/Mexico_City' => 'Mexico City (GMT-6)',
        'America/Sao_Paulo' => 'São Paulo (GMT-3)',
        'America/Argentina/Buenos_Aires' => 'Buenos Aires (GMT-3)',
        'America/Santiago' => 'Santiago (GMT-3)',
        'America/Bogota' => 'Bogotá (GMT-5)',
        'America/Lima' => 'Lima (GMT-5)',
        
        'Asia/Dubai' => 'Dubai (GMT+4)',
        'Asia/Riyadh' => 'Riyadh (GMT+3)',
        'Asia/Jerusalem' => 'Jerusalem (GMT+2)',
        'Europe/Istanbul' => 'Istanbul (GMT+3)',
        'Africa/Cairo' => 'Cairo (GMT+2)',
        'Africa/Johannesburg' => 'Johannesburg (GMT+2)',
        'Africa/Lagos' => 'Lagos (GMT+1)',
        'Africa/Nairobi' => 'Nairobi (GMT+3)',
        
        'UTC' => 'UTC (GMT+0)'
    ];
    
    return $timezoneNames[$timezone] ?? $timezone;
}

/**
 * Get country name from country code
 * @param string $countryCode 2-letter country code
 * @return string Country name
 */
function getCountryName($countryCode) {
    $countries = [
        'TH' => 'Thailand',
        'JP' => 'Japan',
        'SG' => 'Singapore',
        'MY' => 'Malaysia',
        'ID' => 'Indonesia',
        'PH' => 'Philippines',
        'VN' => 'Vietnam',
        'KR' => 'South Korea',
        'CN' => 'China',
        'HK' => 'Hong Kong',
        'TW' => 'Taiwan',
        'IN' => 'India',
        'AU' => 'Australia',
        'NZ' => 'New Zealand',
        
        'GB' => 'United Kingdom',
        'DE' => 'Germany',
        'FR' => 'France',
        'IT' => 'Italy',
        'ES' => 'Spain',
        'NL' => 'Netherlands',
        'CH' => 'Switzerland',
        'AT' => 'Austria',
        'BE' => 'Belgium',
        'SE' => 'Sweden',
        'NO' => 'Norway',
        'DK' => 'Denmark',
        'FI' => 'Finland',
        'PL' => 'Poland',
        'CZ' => 'Czech Republic',
        'RU' => 'Russia',
        
        'US' => 'United States',
        'CA' => 'Canada',
        'MX' => 'Mexico',
        'BR' => 'Brazil',
        'AR' => 'Argentina',
        'CL' => 'Chile',
        'CO' => 'Colombia',
        'PE' => 'Peru',
        
        'AE' => 'United Arab Emirates',
        'SA' => 'Saudi Arabia',
        'IL' => 'Israel',
        'TR' => 'Turkey',
        'EG' => 'Egypt',
        'ZA' => 'South Africa',
        'NG' => 'Nigeria',
        'KE' => 'Kenya'
    ];
    
    $countryCode = strtoupper($countryCode);
    return $countries[$countryCode] ?? $countryCode;
}

/**
 * Validate timezone
 * @param string $timezone Timezone to validate
 * @return bool True if valid timezone
 */
function isValidTimezone($timezone) {
    return in_array($timezone, timezone_identifiers_list());
}

/**
 * Get current time in user's timezone
 * @param string $timezone User's timezone
 * @param string $format Date format (default: 'Y-m-d H:i:s')
 * @return string Formatted time in user's timezone
 */
function getCurrentTimeInTimezone($timezone, $format = 'Y-m-d H:i:s') {
    try {
        $tz = new DateTimeZone($timezone);
        $date = new DateTime('now', $tz);
        return $date->format($format);
    } catch (Exception $e) {
        // Fallback to UTC if timezone is invalid
        return date($format);
    }
}
?>
