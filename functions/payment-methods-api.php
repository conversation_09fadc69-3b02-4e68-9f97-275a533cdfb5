<?php
session_start();
require_once '../vendor/autoload.php';
include('../functions/server.php');

// Set your Stripe API keys
\Stripe\Stripe::setApiKey('sk_test_51ROUofEJRyUMDOj5Op3iqq0gpje1mQqwhINGCYVwVRvmOixzZh1IUmfwArcDt7FtJkN9aszopPP6dyxVWyOZeC8F00gwZtB0ju');

header('Content-Type: application/json');

// Check if user is logged in
if (!isset($_SESSION['username'])) {
    echo json_encode(['success' => false, 'message' => 'User not logged in']);
    exit;
}

$username = $_SESSION['username'];
$user_query = "SELECT id FROM user WHERE username = '$username'";
$user_result = mysqli_query($conn, $user_query);

if (!$user_result || mysqli_num_rows($user_result) == 0) {
    echo json_encode(['success' => false, 'message' => 'User not found']);
    exit;
}

$user = mysqli_fetch_assoc($user_result);
$user_id = $user['id'];

// Get the action from the request
$action = $_POST['action'] ?? '';

switch ($action) {
    case 'remove':
        // Remove a payment method from both database and Stripe
        $payment_id = $_POST['payment_id'] ?? 0;
        $payment_method_id = $_POST['payment_method_id'] ?? '';

        if (!$payment_id || !$payment_method_id) {
            echo json_encode(['success' => false, 'message' => 'Invalid payment method ID']);
            exit;
        }

        // Check if this payment method belongs to the user
        $check_query = "SELECT * FROM payment_methods WHERE id = $payment_id AND user_id = $user_id";
        $check_result = mysqli_query($conn, $check_query);

        if (!$check_result || mysqli_num_rows($check_result) == 0) {
            echo json_encode(['success' => false, 'message' => 'Payment method not found or does not belong to this user']);
            exit;
        }

        $payment_method = mysqli_fetch_assoc($check_result);
        $is_default = $payment_method['is_default'];

        // Delete the payment method from the database
        $delete_query = "DELETE FROM payment_methods WHERE id = $payment_id";
        if (!mysqli_query($conn, $delete_query)) {
            echo json_encode(['success' => false, 'message' => 'Error removing payment method: ' . mysqli_error($conn)]);
            exit;
        }

        // If this was the default payment method, set a new default
        if ($is_default) {
            // Find the oldest payment method and make it the default
            $new_default_query = "SELECT id FROM payment_methods WHERE user_id = $user_id ORDER BY created_at ASC LIMIT 1";
            $new_default_result = mysqli_query($conn, $new_default_query);

            if ($new_default_result && mysqli_num_rows($new_default_result) > 0) {
                $new_default = mysqli_fetch_assoc($new_default_result);
                $new_default_id = $new_default['id'];

                $update_query = "UPDATE payment_methods SET is_default = 1 WHERE id = $new_default_id";
                mysqli_query($conn, $update_query);
            }
        }

        // Also detach the payment method from the customer in Stripe
        try {
            $payment_method_obj = \Stripe\PaymentMethod::retrieve($payment_method_id);
            $payment_method_obj->detach();
        } catch (Exception $e) {
            // Log the error but continue
            error_log("Error detaching payment method from Stripe: " . $e->getMessage());
        }

        echo json_encode(['success' => true, 'message' => 'Payment method removed successfully']);
        break;

    case 'remove_stripe':
        // Remove a payment method directly from Stripe (not in database)
        $payment_method_id = $_POST['payment_method_id'] ?? '';

        if (!$payment_method_id) {
            echo json_encode(['success' => false, 'message' => 'Invalid payment method ID']);
            exit;
        }

        // Get user's Stripe customer ID
        $user_stripe_query = "SELECT stripe_customer_id FROM user WHERE id = $user_id";
        $user_stripe_result = mysqli_query($conn, $user_stripe_query);

        if (!$user_stripe_result || mysqli_num_rows($user_stripe_result) == 0 || !($user_stripe_data = mysqli_fetch_assoc($user_stripe_result)) || empty($user_stripe_data['stripe_customer_id'])) {
            echo json_encode(['success' => false, 'message' => 'User does not have a Stripe customer ID']);
            exit;
        }

        $stripe_customer_id = $user_stripe_data['stripe_customer_id'];

        // Verify this payment method belongs to the user's Stripe customer
        try {
            $payment_method = \Stripe\PaymentMethod::retrieve($payment_method_id);

            if ($payment_method->customer !== $stripe_customer_id) {
                echo json_encode(['success' => false, 'message' => 'Payment method does not belong to this user']);
                exit;
            }

            // Detach the payment method from the customer in Stripe
            // We already have the payment method object from the retrieve call above
            $payment_method->detach();

            echo json_encode(['success' => true, 'message' => 'Payment method removed successfully from Stripe']);
        } catch (Exception $e) {
            echo json_encode(['success' => false, 'message' => 'Error removing payment method: ' . $e->getMessage()]);
            exit;
        }
        break;

    case 'make_default':
        // Make a payment method the default
        $payment_id = $_POST['payment_id'] ?? 0;

        if (!$payment_id) {
            echo json_encode(['success' => false, 'message' => 'Invalid payment method ID']);
            exit;
        }

        // Check if this payment method belongs to the user
        $check_query = "SELECT * FROM payment_methods WHERE id = $payment_id AND user_id = $user_id";
        $check_result = mysqli_query($conn, $check_query);

        if (!$check_result || mysqli_num_rows($check_result) == 0) {
            echo json_encode(['success' => false, 'message' => 'Payment method not found or does not belong to this user']);
            exit;
        }

        // Start a transaction
        mysqli_begin_transaction($conn);

        try {
            // First, set all payment methods to non-default
            $reset_query = "UPDATE payment_methods SET is_default = 0 WHERE user_id = $user_id";
            if (!mysqli_query($conn, $reset_query)) {
                throw new Exception("Error resetting default payment methods: " . mysqli_error($conn));
            }

            // Then, set the selected payment method as default
            $update_query = "UPDATE payment_methods SET is_default = 1 WHERE id = $payment_id";
            if (!mysqli_query($conn, $update_query)) {
                throw new Exception("Error setting default payment method: " . mysqli_error($conn));
            }

            // Commit the transaction
            mysqli_commit($conn);

            echo json_encode(['success' => true, 'message' => 'Default payment method updated successfully']);
        } catch (Exception $e) {
            // Rollback the transaction
            mysqli_rollback($conn);

            echo json_encode(['success' => false, 'message' => $e->getMessage()]);
        }
        break;

    case 'update':
        // Update payment method expiry date
        $payment_id = $_POST['payment_id'] ?? '';
        $payment_method_id = $_POST['payment_method_id'] ?? '';
        $exp_month = $_POST['exp_month'] ?? '';
        $exp_year = $_POST['exp_year'] ?? '';
        $is_stripe_only = $_POST['is_stripe_only'] ?? '0';

        if (!$payment_method_id || !$exp_month || !$exp_year) {
            echo json_encode(['success' => false, 'message' => 'Missing required fields']);
            exit;
        }

        // Validate expiry date
        $current_year = date('Y');
        $current_month = date('n');

        if ($exp_year < $current_year || ($exp_year == $current_year && $exp_month < $current_month)) {
            echo json_encode(['success' => false, 'message' => 'Expiry date cannot be in the past']);
            exit;
        }

        try {
            // Update in Stripe first
            $payment_method = \Stripe\PaymentMethod::retrieve($payment_method_id);

            // Get user's Stripe customer ID to verify ownership
            $user_stripe_query = "SELECT stripe_customer_id FROM user WHERE id = $user_id";
            $user_stripe_result = mysqli_query($conn, $user_stripe_query);

            if ($user_stripe_result && mysqli_num_rows($user_stripe_result) > 0) {
                $user_stripe_data = mysqli_fetch_assoc($user_stripe_result);
                $stripe_customer_id = $user_stripe_data['stripe_customer_id'];

                // Verify this payment method belongs to the user
                if ($payment_method->customer !== $stripe_customer_id) {
                    echo json_encode(['success' => false, 'message' => 'Payment method does not belong to this user']);
                    exit;
                }
            }

            // Update the payment method in Stripe
            \Stripe\PaymentMethod::update($payment_method_id, [
                'card' => [
                    'exp_month' => (int)$exp_month,
                    'exp_year' => (int)$exp_year,
                ]
            ]);

            // Update in database if it's not Stripe-only
            if ($is_stripe_only !== '1' && !empty($payment_id)) {
                // Check if this payment method belongs to the user
                $check_query = "SELECT * FROM payment_methods WHERE id = ? AND user_id = ?";
                $check_stmt = mysqli_prepare($conn, $check_query);
                mysqli_stmt_bind_param($check_stmt, "ii", $payment_id, $user_id);
                mysqli_stmt_execute($check_stmt);
                $check_result = mysqli_stmt_get_result($check_stmt);

                if (!$check_result || mysqli_num_rows($check_result) == 0) {
                    echo json_encode(['success' => false, 'message' => 'Payment method not found in database']);
                    exit;
                }

                // Update the database record
                $update_query = "UPDATE payment_methods SET card_exp_month = ?, card_exp_year = ? WHERE id = ? AND user_id = ?";
                $update_stmt = mysqli_prepare($conn, $update_query);
                mysqli_stmt_bind_param($update_stmt, "iiii", $exp_month, $exp_year, $payment_id, $user_id);

                if (!mysqli_stmt_execute($update_stmt)) {
                    echo json_encode(['success' => false, 'message' => 'Error updating payment method in database: ' . mysqli_error($conn)]);
                    exit;
                }
            }

            echo json_encode(['success' => true, 'message' => 'Payment method updated successfully']);

        } catch (Exception $e) {
            echo json_encode(['success' => false, 'message' => 'Error updating payment method: ' . $e->getMessage()]);
        }
        break;

    default:
        echo json_encode(['success' => false, 'message' => 'Invalid action']);
        break;
}
?>
