<?php
session_start();
include('../functions/server.php');
include('../functions/cart-transfer.php');

// Auto-detect environment for URL paths
$is_localhost = (strpos($_SERVER['HTTP_HOST'], 'localhost') !== false || strpos($_SERVER['HTTP_HOST'], '127.0.0.1') !== false);
$url_base = $is_localhost ? '/helloit' : '';

$errors = array();

if (isset($_POST['signin_user'])) {
    $login_input = mysqli_real_escape_string($conn, $_POST['username']);
    $password = mysqli_real_escape_string($conn, $_POST['password']);

    if (empty($login_input)) {
        array_push($errors, "Email is required");
    }

    if (empty($password)) {
        array_push($errors, "Password is required");
    }

    if (count($errors) == 0) {
        $login_success = false;
        $user_data = null;

        // Debug logging
        error_log("Sign-in attempt: email=$login_input, password=$password");

        // Check if user exists in main user table
        $user_stmt = $conn->prepare("SELECT * FROM user WHERE email = ? LIMIT 1");
        $user_stmt->bind_param("s", $login_input);
        $user_stmt->execute();
        $user_result = $user_stmt->get_result();

        if ($user_result->num_rows == 1) {
            // User found in main table
            $user_data = $user_result->fetch_assoc();
            $stored_hash = $user_data['password'];

            error_log("User found: ID=" . $user_data['id'] . ", email=" . $user_data['email']);
            error_log("Stored hash: " . substr($stored_hash, 0, 30) . "...");

            // Method 1: Direct password verification
            if (strpos($stored_hash, '$2y$') === 0 || strpos($stored_hash, '$2a$') === 0) {
                // bcrypt hash
                $direct_verify = password_verify($password, $stored_hash);
                error_log("bcrypt direct verification: " . ($direct_verify ? 'PASS' : 'FAIL'));
                if ($direct_verify) {
                    $login_success = true;
                }
            } else {
                // md5 hash
                $direct_verify = ($stored_hash === md5($password));
                error_log("MD5 direct verification: " . ($direct_verify ? 'PASS' : 'FAIL'));
                if ($direct_verify) {
                    $login_success = true;
                }
            }

            // Method 2: If direct verification failed, check if this matches a temp password
            if (!$login_success) {
                error_log("Direct verification failed, checking payment_temp");
                $temp_stmt = $conn->prepare("SELECT password FROM payment_temp WHERE email = ? ORDER BY id DESC LIMIT 1");
                $temp_stmt->bind_param("s", $login_input);
                $temp_stmt->execute();
                $temp_result = $temp_stmt->get_result();

                if ($temp_result->num_rows > 0) {
                    $temp_data = $temp_result->fetch_assoc();
                    $temp_password = $temp_data['password'];
                    error_log("Found temp password: $temp_password");
                    error_log("Entered password: $password");
                    error_log("Passwords match: " . ($password === $temp_password ? 'YES' : 'NO'));

                    // Check if entered password matches the temp password
                    if ($password === $temp_password) {
                        // Now verify if this temp password creates the stored hash
                        if (strpos($stored_hash, '$2y$') === 0 || strpos($stored_hash, '$2a$') === 0) {
                            $temp_verify = password_verify($temp_password, $stored_hash);
                            error_log("bcrypt temp verification: " . ($temp_verify ? 'PASS' : 'FAIL'));
                            if ($temp_verify) {
                                $login_success = true;
                            }
                        } else {
                            $temp_verify = ($stored_hash === md5($temp_password));
                            error_log("MD5 temp verification: " . ($temp_verify ? 'PASS' : 'FAIL'));
                            if ($temp_verify) {
                                $login_success = true;
                            }
                        }
                    }
                } else {
                    error_log("No temp password found for user");
                }
                $temp_stmt->close();
            }
        } else {
            // User not found in main table - check if they exist only in payment_temp (webhook pending)
            error_log("User not found in main table, checking payment_temp only");
            $temp_only_stmt = $conn->prepare("SELECT * FROM payment_temp WHERE email = ? AND password = ? ORDER BY id DESC LIMIT 1");
            $temp_only_stmt->bind_param("ss", $login_input, $password);
            $temp_only_stmt->execute();
            $temp_only_result = $temp_only_stmt->get_result();

            if ($temp_only_result->num_rows > 0) {
                // Found in payment_temp but not in user table - webhook hasn't processed
                error_log("Found in payment_temp only - webhook pending");
                array_push($errors, "Your account is still being set up. Please try again in a few moments.");
                $_SESSION['error'] = "Your account is still being set up. Please try again in a few moments.";
                $temp_only_stmt->close();
                $user_stmt->close();

                // Preserve cart transfer parameters when webhook is pending
                $redirect_url = $url_base . "/front-end/sign-in.php";
                if (isset($_POST['cart_transfer']) && $_POST['cart_transfer'] == '1') {
                    $return_to = isset($_POST['return_to']) ? $_POST['return_to'] : 'cart';
                    $redirect_url .= "?cart_transfer=1&return_to=" . urlencode($return_to);
                }

                header("location: " . $redirect_url);
                exit();
            } else {
                error_log("Not found in payment_temp either");
            }
            $temp_only_stmt->close();
        }
        $user_stmt->close();

        // Handle login result
        if ($login_success && $user_data) {
            error_log("Login successful for user: " . $user_data['email']);
            $_SESSION['username'] = $user_data['username'];
            $_SESSION['SUCCESS'] = "You are now loggedin sign-in-db";
            $_SESSION['user_id'] = $user_data['id'];
            if (isset($_SESSION['success'])) {
                unset($_SESSION['success']);
            }

            // Check if this is an AJAX request from payment success page
            if (isset($_POST['ajax_login']) && $_POST['ajax_login'] == '1') {
                // Handle cart transfer for AJAX requests too
                if (isset($_POST['cart_transfer']) && $_POST['cart_transfer'] == '1') {
                    // Check if login is from pre-checkout page
                    $return_to = isset($_POST['return_to']) ? $_POST['return_to'] : '';
                    $preserve_guest_cart = ($return_to === 'pre_checkout');

                    $transfer_result = transferGuestCartToUser($user_data['id'], $conn, $preserve_guest_cart);

                    if ($transfer_result['success']) {
                        error_log("Cart transfer successful (AJAX): " . $transfer_result['message']);
                        $_SESSION['cart_transfer_success'] = $transfer_result['message'];

                        // Mark that cart transfer has been completed for this session
                        if ($preserve_guest_cart) {
                            $_SESSION['pre_checkout_cart_transferred'] = true;
                        }
                    } else {
                        error_log("Cart transfer failed (AJAX): " . $transfer_result['message']);
                        $_SESSION['cart_transfer_error'] = $transfer_result['message'];
                    }
                }

                // Return JSON response for AJAX
                header('Content-Type: application/json');
                echo json_encode([
                    'success' => true,
                    'message' => 'Login successful',
                    'redirect_url' => $url_base . "/front-end/my-ticket.php?login_success=1"
                ]);
                exit();
            }
            // Check if user should be redirected to tickets (from payment success page)
            elseif (isset($_POST['redirect_to_tickets']) && $_POST['redirect_to_tickets'] == '1') {
                header("location: " . $url_base . "/front-end/my-ticket.php?login_success=1");
            } elseif (isset($_POST['cart_transfer']) && $_POST['cart_transfer'] == '1') {
                // Cart transfer - transfer guest cart items to user cart
                $transfer_result = transferGuestCartToUser($user_data['id'], $conn);

                if ($transfer_result['success']) {
                    error_log("Cart transfer successful: " . $transfer_result['message']);
                    $_SESSION['cart_transfer_success'] = $transfer_result['message'];
                } else {
                    error_log("Cart transfer failed: " . $transfer_result['message']);
                    $_SESSION['cart_transfer_error'] = $transfer_result['message'];
                }

                // Redirect back to appropriate page with login success message
                $return_to = isset($_POST['return_to']) ? $_POST['return_to'] : 'cart';
                if ($return_to === 'cart') {
                    header("location: " . $url_base . "/front-end/cart.php?login_success=1&cart_transferred=1");
                } elseif ($return_to === 'pre_checkout') {
                    header("location: " . $url_base . "/front-end/pre-checkout.php?login_success=1&cart_transferred=1");
                } else {
                    header("location: " . $url_base . "/front-end/my-ticket.php?login_success=1");
                }
            } else {
                // Default redirect to my-ticket.php with login success popup
                header("location: " . $url_base . "/front-end/my-ticket.php?login_success=1");
            }
            exit();
        } else {
            error_log("Login failed - login_success: " . ($login_success ? 'true' : 'false') . ", user_data: " . ($user_data ? 'exists' : 'null'));
            array_push($errors, "Wrong Username/Email or Password combination");
            $_SESSION['error'] = "Wrong Username/Email or Password try again";

            // Check if this is an AJAX request
            if (isset($_POST['ajax_login']) && $_POST['ajax_login'] == '1') {
                // Return JSON error response for AJAX
                header('Content-Type: application/json');
                echo json_encode([
                    'success' => false,
                    'message' => 'Wrong Username/Email or Password combination'
                ]);
                exit();
            }
            // Check if this is from payment success page
            elseif (isset($_POST['redirect_to_tickets']) && $_POST['redirect_to_tickets'] == '1') {
                // Redirect back to payment success page with session_id
                $session_id = $_POST['session_id'] ?? '';
                if (!empty($session_id)) {
                    $redirect_url = $url_base . "/front-end/payment-success.php?session_id=" . urlencode($session_id);
                } else {
                    // Fallback if no session_id
                    $redirect_url = $url_base . "/front-end/payment-success.php";
                }
            } else {
                // Regular sign-in page redirect
                $redirect_url = $url_base . "/front-end/sign-in.php";

                // Preserve cart transfer parameters when login fails
                if (isset($_POST['cart_transfer']) && $_POST['cart_transfer'] == '1') {
                    $return_to = isset($_POST['return_to']) ? $_POST['return_to'] : 'cart';
                    $redirect_url .= "?cart_transfer=1&return_to=" . urlencode($return_to);
                }
            }

            header("location: " . $redirect_url);
            exit();
        }
    } else {
        // Validation errors occurred
        if (isset($_POST['redirect_to_tickets']) && $_POST['redirect_to_tickets'] == '1') {
            // For payment success page, redirect back with session_id
            $session_id = $_POST['session_id'] ?? '';
            if (!empty($session_id)) {
                header("location: " . $url_base . "/front-end/payment-success.php?session_id=" . urlencode($session_id));
            } else {
                header("location: " . $url_base . "/front-end/payment-success.php");
            }
            exit();
        } else {
            // Preserve cart transfer parameters for regular sign-in
            if (isset($_POST['cart_transfer']) && $_POST['cart_transfer'] == '1') {
                $return_to = isset($_POST['return_to']) ? $_POST['return_to'] : 'cart';
                $_SESSION['preserve_cart_transfer'] = [
                    'cart_transfer' => '1',
                    'return_to' => $return_to
                ];
            }
        }
    }
}