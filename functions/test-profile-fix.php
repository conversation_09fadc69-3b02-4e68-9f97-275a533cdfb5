<?php
/**
 * Test Profile Update Fix
 * Tests the fixed profile update functionality
 */

// Check if session is already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Include files only if not already included
if (!class_exists('DBController')) {
    include('server.php');
}
if (!function_exists('getCompleteCustomerData')) {
    include('customer-data-service.php');
}
require_once '../vendor/autoload.php';
require_once '../config/api-config.php';

echo "<h1>🧪 Test Profile Update Fix</h1>";

// Check if user is logged in
if (!isset($_SESSION['username'])) {
    echo "<p style='color: red;'>❌ User not logged in. Please log in first.</p>";
    echo "<p><a href='../front-end/sign-in.php'>Go to Sign In</a></p>";
    exit();
}

$username = $_SESSION['username'];
echo "<h2>👤 Testing for User: " . htmlspecialchars($username) . "</h2>";

try {
    // Get user data
    $user = getCompleteCustomerData($username);
    
    if (!$user) {
        echo "<p style='color: red;'>❌ Failed to get user data</p>";
        exit();
    }
    
    if (!$user['has_appika_data'] || empty($user['appika_customer_id'])) {
        echo "<p style='color: orange;'>⚠️ User doesn't have Appika data. Cannot test profile update.</p>";
        echo "<p><strong>User needs to be linked to Appika API first.</strong></p>";
        exit();
    }
    
    echo "<h3>✅ User has Appika data - Testing API call</h3>";
    echo "<p><strong>Appika Customer ID:</strong> " . htmlspecialchars($user['appika_customer_id']) . "</p>";
    
    // Test the fixed updateCustomerInAppika function
    echo "<h3>🔧 Testing Customer Update Function</h3>";
    
    // Include the function from edit_profile_appika.php
    include_once('edit_profile_appika.php');
    
    // Test data - just updating the name
    $testData = [
        'name' => $user['name'] ?? 'Test Customer Name',
        'tax_id' => $user['tax_id'] ?? '',
        'tel_work' => $user['tel_work'] ?? '',
        'address' => $user['address'] ?? 'Test Address',
        'address2' => $user['address2'] ?? '',
        'city' => $user['city'] ?? 'Bangkok',
        'state' => $user['state'] ?? 'Bangkok',
        'country' => $user['country'] ?? 'TH',
        'postal_code' => $user['postal_code'] ?? '10110'
    ];
    
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>Test Data to Update:</h4>";
    echo "<pre>" . json_encode($testData, JSON_PRETTY_PRINT) . "</pre>";
    echo "</div>";
    
    // Call the update function
    echo "<h4>🚀 Calling updateCustomerInAppika...</h4>";
    $result = updateCustomerInAppika($user['appika_customer_id'], $testData);
    
    echo "<div style='background: " . ($result['success'] ? '#d4edda' : '#f8d7da') . "; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>" . ($result['success'] ? '✅ Success!' : '❌ Failed!') . "</h4>";
    echo "<p><strong>Message:</strong> " . htmlspecialchars($result['message']) . "</p>";
    echo "</div>";
    
    if ($result['success']) {
        echo "<h3>🎉 Profile Update Test Successful!</h3>";
        echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 5px; color: #0c5460;'>";
        echo "<h4>What was fixed:</h4>";
        echo "<ul>";
        echo "<li>✅ Now gets existing customer data first</li>";
        echo "<li>✅ Preserves all required fields (no, entity_type, grp_id, ofc_id, etc.)</li>";
        echo "<li>✅ Only updates the name field while keeping other required fields</li>";
        echo "<li>✅ Follows the same pattern as HelloITOld implementation</li>";
        echo "</ul>";
        echo "</div>";
        
        echo "<h4>🔗 Next Steps:</h4>";
        echo "<p>1. <a href='../front-end/profile.php' style='background: #473BF0; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px;'>Try updating your profile now</a></p>";
        echo "<p>2. The profile update should now work without errors!</p>";
        
    } else {
        echo "<h3>❌ Still Having Issues</h3>";
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; color: #721c24;'>";
        echo "<h4>Possible causes:</h4>";
        echo "<ul>";
        echo "<li>API connection issues</li>";
        echo "<li>Invalid customer ID</li>";
        echo "<li>Missing API permissions</li>";
        echo "<li>Appika API server issues</li>";
        echo "</ul>";
        echo "</div>";
        
        // Show detailed error for debugging
        echo "<h4>🔍 Debug Information:</h4>";
        echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px;'>";
        echo "<p><strong>Customer ID:</strong> " . htmlspecialchars($user['appika_customer_id']) . "</p>";
        echo "<p><strong>API Endpoint:</strong> " . htmlspecialchars(getCustomerApiConfig()['endpoint'] ?? 'Not set') . "</p>";
        echo "<p><strong>API Path:</strong> " . htmlspecialchars(getCustomerApiConfig()['path'] ?? 'Not set') . "</p>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<h3>💥 Error Occurred:</h3>";
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; color: #721c24;'>";
    echo "<p><strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><strong>File:</strong> " . htmlspecialchars($e->getFile()) . "</p>";
    echo "<p><strong>Line:</strong> " . $e->getLine() . "</p>";
    echo "</div>";
}

echo "<hr>";
echo "<h3>🔗 Quick Links:</h3>";
echo "<p>";
echo "<a href='debug-profile-update.php' style='background: #17a2b8; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px; margin-right: 10px;'>🔍 Debug Profile</a>";
echo "<a href='show-profile-errors.php' style='background: #ffc107; color: black; padding: 8px 16px; text-decoration: none; border-radius: 4px; margin-right: 10px;'>📋 Show Errors</a>";
echo "<a href='../front-end/profile.php' style='background: #473BF0; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px;'>👤 Back to Profile</a>";
echo "</p>";

echo "<style>";
echo "body { font-family: Arial, sans-serif; margin: 20px; }";
echo "h1, h2, h3 { color: #473BF0; }";
echo "pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; }";
echo "</style>";
?>
