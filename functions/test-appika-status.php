<?php
/**
 * Appika API Status Checker
 * Quick tool to test if Appika API is accessible
 */

echo "<h1>🔍 Appika API Status Checker</h1>";
echo "<p>Testing connectivity to Appika API servers...</p>";

// Test URLs
$test_urls = [
    'Ticket API' => 'https://dev-sgsg-tktapi.appika.com/graphql',
    'Customer API' => 'https://dev-api-pooh-sgsg.appika.com/contact/customers'
];

foreach ($test_urls as $name => $url) {
    echo "<div style='border: 1px solid #ddd; margin: 10px 0; padding: 15px; border-radius: 5px;'>";
    echo "<h3>🌐 Testing $name</h3>";
    echo "<p><strong>URL:</strong> $url</p>";
    
    $start_time = microtime(true);
    
    // Initialize cURL
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10); // 10 second timeout
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 5); // 5 second connection timeout
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_USERAGENT, 'HelloIT API Status Checker');
    
    // Execute request
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    $total_time = curl_getinfo($ch, CURLINFO_TOTAL_TIME);
    curl_close($ch);
    
    $end_time = microtime(true);
    $execution_time = round(($end_time - $start_time) * 1000, 2);
    
    // Display results
    if ($error) {
        echo "<div style='color: red; padding: 10px; background: #fff0f0; border: 1px solid red; border-radius: 3px;'>";
        echo "<strong>❌ Connection Failed</strong><br>";
        echo "<strong>Error:</strong> " . htmlspecialchars($error) . "<br>";
        echo "<strong>Time:</strong> {$execution_time}ms";
        echo "</div>";
    } else {
        if ($http_code >= 200 && $http_code < 400) {
            echo "<div style='color: green; padding: 10px; background: #f0fff0; border: 1px solid green; border-radius: 3px;'>";
            echo "<strong>✅ Connection Successful</strong><br>";
            echo "<strong>HTTP Code:</strong> $http_code<br>";
            echo "<strong>Response Time:</strong> {$execution_time}ms<br>";
            echo "<strong>Server Response:</strong> " . (strlen($response) > 100 ? substr($response, 0, 100) . "..." : $response);
            echo "</div>";
        } else {
            echo "<div style='color: orange; padding: 10px; background: #fff8e1; border: 1px solid orange; border-radius: 3px;'>";
            echo "<strong>⚠️ Server Responded but with Error</strong><br>";
            echo "<strong>HTTP Code:</strong> $http_code<br>";
            echo "<strong>Response Time:</strong> {$execution_time}ms<br>";
            echo "<strong>Response:</strong> " . (strlen($response) > 100 ? substr($response, 0, 100) . "..." : $response);
            echo "</div>";
        }
    }
    
    echo "</div>";
}

// Test basic internet connectivity
echo "<div style='border: 1px solid #ddd; margin: 10px 0; padding: 15px; border-radius: 5px;'>";
echo "<h3>🌍 Testing Basic Internet Connectivity</h3>";

$test_sites = [
    'Google' => 'https://www.google.com',
    'GitHub' => 'https://api.github.com'
];

foreach ($test_sites as $site => $url) {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 5);
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 3);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    
    $start = microtime(true);
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    $time = round((microtime(true) - $start) * 1000, 2);
    curl_close($ch);
    
    if ($error) {
        echo "<p>❌ <strong>$site:</strong> Failed - " . htmlspecialchars($error) . "</p>";
    } else {
        echo "<p>✅ <strong>$site:</strong> OK (HTTP $http_code, {$time}ms)</p>";
    }
}
echo "</div>";

// Show current time and server info
echo "<div style='border: 1px solid #ddd; margin: 10px 0; padding: 15px; border-radius: 5px;'>";
echo "<h3>ℹ️ Server Information</h3>";
echo "<p><strong>Current Time:</strong> " . date('Y-m-d H:i:s T') . "</p>";
echo "<p><strong>Server:</strong> " . $_SERVER['HTTP_HOST'] . "</p>";
echo "<p><strong>PHP Version:</strong> " . phpversion() . "</p>";
echo "<p><strong>cURL Version:</strong> " . curl_version()['version'] . "</p>";
echo "</div>";

// Recommendations
echo "<div style='border: 1px solid #0066cc; margin: 10px 0; padding: 15px; border-radius: 5px; background: #f0f8ff;'>";
echo "<h3>💡 What to Do if Appika is Down</h3>";
echo "<ol>";
echo "<li><strong>Wait and Retry:</strong> API downtime is usually temporary</li>";
echo "<li><strong>Check Status:</strong> Contact Appika support for server status</li>";
echo "<li><strong>Local Functionality:</strong> Your local system still works normally</li>";
echo "<li><strong>Automatic Retry:</strong> Your system will automatically retry when Appika comes back online</li>";
echo "<li><strong>Monitor Logs:</strong> Check admin-ticket-logs.php for when connectivity resumes</li>";
echo "</ol>";
echo "</div>";

echo "<div style='border: 1px solid #28a745; margin: 10px 0; padding: 15px; border-radius: 5px; background: #f0fff0;'>";
echo "<h3>✅ What Still Works When Appika is Down</h3>";
echo "<ul>";
echo "<li>✅ User login and registration</li>";
echo "<li>✅ Local ticket creation</li>";
echo "<li>✅ Payment processing</li>";
echo "<li>✅ Email receipts</li>";
echo "<li>✅ Admin panel functionality</li>";
echo "<li>✅ Local customer data</li>";
echo "</ul>";
echo "</div>";

echo "<p><small>Last checked: " . date('Y-m-d H:i:s') . " | <a href='?' style='color: #0066cc;'>Refresh Test</a></small></p>";
?>
