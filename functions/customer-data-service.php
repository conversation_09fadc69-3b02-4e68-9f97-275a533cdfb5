<?php
/**
 * Customer Data Service
 * Handles customer data integration between local database and Appika API
 * 
 * Local Database: Essential data only (id, appika_id, username, email, password, tickets, etc.)
 * Appika API: All other customer data (name, address, phone, company info, etc.)
 */

require_once __DIR__ . '/../config/api-config.php';
require_once __DIR__ . '/../vendor/autoload.php';

/**
 * Get complete customer data (local + Appika)
 * @param string $username Username to get data for
 * @return array|null Complete customer data or null if not found
 */
function getCompleteCustomerData($username) {
    global $conn;
    
    // Get essential data from local database
    $localData = getLocalCustomerEssentials($username);
    if (!$localData) {
        return null;
    }
    
    // Get extended data from Appika if available
    $appikaData = null;
    if (!empty($localData['appika_customer_id'])) {
        $appikaData = fetchAppikaCustomerById($localData['appika_customer_id']);
    }
    
    // Merge data
    return mergeCustomerData($localData, $appikaData);
}

/**
 * Get essential customer data from local database only
 * @param string $username Username to get data for
 * @return array|null Essential customer data
 */
function getLocalCustomerEssentials($username) {
    global $conn;
    
    $stmt = $conn->prepare("
        SELECT id, appika_id, appika_customer_id, username, email,
               starter_tickets, premium_tickets, ultimate_tickets, registration_time,
               timezone, stripe_customer_id, country
        FROM user
        WHERE username = ?
    ");
    $stmt->bind_param("s", $username);
    $stmt->execute();
    $result = $stmt->get_result();
    
    return $result->fetch_assoc();
}

/**
 * Get customer data by user ID
 * @param int $userId User ID to get data for
 * @return array|null Complete customer data
 */
function getCompleteCustomerDataById($userId) {
    global $conn;
    
    // Get username first
    $stmt = $conn->prepare("SELECT username FROM user WHERE id = ?");
    $stmt->bind_param("i", $userId);
    $stmt->execute();
    $result = $stmt->get_result();
    $user = $result->fetch_assoc();
    
    if (!$user) {
        return null;
    }
    
    return getCompleteCustomerData($user['username']);
}

/**
 * Fetch customer data from Appika API by customer ID
 * @param int $appikaCustomerId Appika customer ID
 * @return array|null Customer data from Appika
 */
function fetchAppikaCustomerById($appikaCustomerId) {
    $apiConfig = getCustomerApiConfig();
    $apiEndpoint = $apiConfig['endpoint'];
    $apiPath = $apiConfig['path'];
    $apiKey = $apiConfig['key'];
    
    $client = new \GuzzleHttp\Client([
        'base_uri' => $apiEndpoint,
        'timeout' => 8, // Match maintenance detection timeout
        'connect_timeout' => 5,
        'http_errors' => false,
    ]);
    
    try {
        // Get customer basic data
        $response = $client->request('GET', $apiPath . '/' . $appikaCustomerId, [
            'headers' => [
                'X-api-key' => "{$apiKey}",
                'Accept' => 'application/json',
            ]
        ]);
        
        $statusCode = $response->getStatusCode();
        $body = $response->getBody()->getContents();
        
        if ($statusCode >= 200 && $statusCode < 300) {
            $customerData = json_decode($body, true);
            
            // Also fetch customer locations
            $locationsResponse = $client->request('GET', $apiPath . '/' . $appikaCustomerId . '/locations', [
                'headers' => [
                    'X-api-key' => "{$apiKey}",
                    'Accept' => 'application/json',
                ]
            ]);
            
            if ($locationsResponse->getStatusCode() >= 200 && $locationsResponse->getStatusCode() < 300) {
                $locationsData = json_decode($locationsResponse->getBody()->getContents(), true);
                $customerData['locations'] = $locationsData['items'] ?? [];
            } else {
                $customerData['locations'] = [];
            }
            
            return $customerData;
        }
        
    } catch (Exception $e) {
        $error = $e->getMessage();
        error_log("Error fetching Appika customer data: " . $error);

        // Check if this is a connection error that should trigger maintenance mode
        if (strpos($error, 'Connection') !== false ||
            strpos($error, 'timeout') !== false ||
            strpos($error, 'timed out') !== false ||
            strpos($error, 'Operation timed out') !== false) {

            // Check if we're on a profile page that should trigger maintenance
            $current_page = basename($_SERVER['PHP_SELF']);
            if ($current_page === 'profile.php') {
                error_log("MAINTENANCE_TRIGGER: Customer API connection error on profile page: $error");

                // Redirect to maintenance page
                $maintenance_url = 'server-down.php';
                if (strpos($_SERVER['REQUEST_URI'], '/front-end/') === false) {
                    $maintenance_url = '../front-end/server-down.php';
                }

                header("Location: $maintenance_url");
                exit();
            }
        }
    }

    return null;
}

/**
 * Merge local and Appika customer data
 * @param array $localData Local database data
 * @param array|null $appikaData Appika API data
 * @return array Merged customer data
 */
function mergeCustomerData($localData, $appikaData) {
    // Start with local essential data
    $mergedData = $localData;
    
    // Add Appika data if available
    if ($appikaData) {
        // Basic customer info from Appika
        $mergedData['name'] = $appikaData['name'] ?? '';
        $mergedData['entity_type'] = $appikaData['entity_type'] ?? '';
        $mergedData['status'] = $appikaData['status'] ?? '';
        $mergedData['start_date'] = $appikaData['start_date'] ?? '';
        
        // Get primary location data
        $primaryLocation = getPrimaryLocation($appikaData['locations'] ?? []);
        if ($primaryLocation) {
            $mergedData['address'] = $primaryLocation['add1'] ?? '';
            $mergedData['address2'] = $primaryLocation['add2'] ?? '';
            $mergedData['city'] = $primaryLocation['city'] ?? '';
            $mergedData['state'] = $primaryLocation['state_code'] ?? '';
            $mergedData['country'] = $primaryLocation['ccode'] ?? '';
            $mergedData['postal_code'] = $primaryLocation['zip'] ?? '';
            $mergedData['tel_work'] = $primaryLocation['tel_work'] ?? '';
            $mergedData['phone'] = $primaryLocation['tel_work'] ?? ''; // Keep for backward compatibility
            $mergedData['contact_person'] = $primaryLocation['contact_pax'] ?? '';
        }

        // Extract tax_id from customer_ext
        if (!empty($appikaData['customer_ext']['tax_id'])) {
            $mergedData['tax_id'] = $appikaData['customer_ext']['tax_id'];
        } else {
            $mergedData['tax_id'] = '';
        }
        
        // Store all locations for detailed view
        $mergedData['all_locations'] = $appikaData['locations'] ?? [];
        
        // Mark as having Appika data
        $mergedData['has_appika_data'] = true;
    } else {
        // No Appika data available
        $mergedData['has_appika_data'] = false;
        $mergedData['name'] = '';
        $mergedData['address'] = '';
        $mergedData['phone'] = '';
        // Set other fields to empty
    }
    
    return $mergedData;
}

/**
 * Get primary location from locations array
 * @param array $locations Array of location data
 * @return array|null Primary location data
 */
function getPrimaryLocation($locations) {
    if (empty($locations)) {
        return null;
    }

    // Look for primary location first
    foreach ($locations as $location) {
        if (isset($location['is_primary_loc']) && $location['is_primary_loc'] === 'y') {
            return $location;
        }
    }

    // If no primary location found, look for the first location with actual data
    foreach ($locations as $location) {
        $hasData = !empty($location['add1']) || !empty($location['city']) || !empty($location['tel_work']);
        if ($hasData) {
            return $location;
        }
    }

    // If no location with data found, return first location
    return $locations[0] ?? null;
}

/**
 * Create minimal customer record in database
 * @param array $customerData Customer data for creation
 * @return int|false User ID if successful, false if failed
 */
function createMinimalCustomerRecord($customerData) {
    global $conn;

    $username = $customerData['username'];
    $email = $customerData['email'];
    $password = $customerData['password']; // Should be hashed
    $registration_time = $customerData['registration_time'] ?? date('Y-m-d H:i:s');
    $timezone = $customerData['timezone'] ?? 'UTC';
    $stripe_customer_id = $customerData['stripe_customer_id'] ?? null;
    $country = $customerData['country'] ?? '';

    $stmt = $conn->prepare("
        INSERT INTO user (username, email, password, registration_time, timezone, stripe_customer_id, country, premium_tickets, ultimate_tickets)
        VALUES (?, ?, ?, ?, ?, ?, ?, 0, 0)
    ");
    $stmt->bind_param("sssssss", $username, $email, $password, $registration_time, $timezone, $stripe_customer_id, $country);

    if ($stmt->execute()) {
        return $conn->insert_id;
    }

    return false;
}

/**
 * Update customer's Appika IDs after successful API creation
 * @param int $userId Local user ID
 * @param string $appikaId Customer number (e.g., HI123)
 * @param int $appikaCustomerId Appika API customer ID
 * @return bool Success status
 */
function updateCustomerAppikaIds($userId, $appikaId, $appikaCustomerId) {
    global $conn;
    
    $stmt = $conn->prepare("
        UPDATE user 
        SET appika_id = ?, appika_customer_id = ? 
        WHERE id = ?
    ");
    $stmt->bind_param("sii", $appikaId, $appikaCustomerId, $userId);
    
    return $stmt->execute();
}

/**
 * Check if customer has Appika data
 * @param string $username Username to check
 * @return bool True if has Appika data
 */
function customerHasAppikaData($username) {
    $localData = getLocalCustomerEssentials($username);
    return $localData && !empty($localData['appika_customer_id']);
}

/**
 * Get customer display name
 * @param array $customerData Complete customer data
 * @return string Display name
 */
function getCustomerDisplayName($customerData) {
    if (!empty($customerData['name'])) {
        return $customerData['name'];
    }
    
    if (!empty($customerData['username'])) {
        return $customerData['username'];
    }
    
    return 'Customer';
}

/**
 * Format customer address for display
 * @param array $customerData Complete customer data
 * @return string Formatted address
 */
function formatCustomerAddress($customerData) {
    $addressParts = [];
    
    if (!empty($customerData['address'])) {
        $addressParts[] = $customerData['address'];
    }
    
    if (!empty($customerData['address2'])) {
        $addressParts[] = $customerData['address2'];
    }
    
    if (!empty($customerData['city'])) {
        $addressParts[] = $customerData['city'];
    }
    
    if (!empty($customerData['state'])) {
        $addressParts[] = $customerData['state'];
    }
    
    if (!empty($customerData['postal_code'])) {
        $addressParts[] = $customerData['postal_code'];
    }
    
    if (!empty($customerData['country'])) {
        $addressParts[] = $customerData['country'];
    }
    
    return implode(', ', $addressParts);
}
?>
