<?php
// Test page to check maintenance status
require_once 'functions/appika-status-checker.php';

echo "<h1>Maintenance Status Test</h1>";

echo "<h2>API Status Check:</h2>";
$status = checkAppikaStatus();

foreach ($status as $type => $api) {
    echo "<h3>API Type: $type</h3>";
    echo "<ul>";
    echo "<li>Online: " . ($api['online'] ? 'YES' : 'NO') . "</li>";
    echo "<li>HTTP Code: " . $api['http_code'] . "</li>";
    echo "<li>Error: " . ($api['error'] ?: 'None') . "</li>";
    echo "<li>Response Length: " . $api['response_length'] . "</li>";
    echo "</ul>";
}

echo "<h2>Overall Status:</h2>";
echo "<p>Online: " . (isAppikaOnline() ? 'YES' : 'NO') . "</p>";
echo "<p>Message: " . getAppikaStatusMessage() . "</p>";

echo "<h2>Test GraphQL Request:</h2>";
require_once 'functions/graphql_functions.php';

$result = makeGraphQLRequest('{ __schema { queryType { name } } }', []);
echo "<p>GraphQL Test Result:</p>";
echo "<pre>" . print_r($result, true) . "</pre>";
?> 