<?php
// Quick test to debug ticket update issue
session_start();
include('functions/server.php');
include('functions/graphql_functions.php');

// Test ticket ID 272 (from your screenshot)
$ticketId = 272;

echo "<h2>Testing Ticket Update for ID: $ticketId</h2>";

// First, get current ticket data
$query = '
query GetTicket($id: Int!) {
    getTicket(id: $id) {
        id
        contact_id
        agent_id
        req_email
        subject
        type
        type_name
        priority
        status
    }
}';

$result = makeGraphQLRequest($query, ['id' => $ticketId]);

echo "<h3>Current Ticket Data:</h3>";
echo "<pre>" . json_encode($result, JSON_PRETTY_PRINT) . "</pre>";

if ($result['success'] && isset($result['data']['data']['getTicket'])) {
    $currentTicket = $result['data']['data']['getTicket'];
    
    // Test update with minimal required fields
    $mutation = '
    mutation UpdateTicket(
        $id: Int!,
        $contact_id: Int,
        $agent_id: Int,
        $subject: String!,
        $type: Int!,
        $type_name: String,
        $priority: String!,
        $status: String!,
        $req_email: String,
        $time_track: String!,
        $reply_msg: String,
        $tags: String
    ) {
        updateTicket(
            id: $id,
            contact_id: $contact_id,
            agent_id: $agent_id,
            subject: $subject,
            type: $type,
            type_name: $type_name,
            priority: $priority,
            status: $status,
            req_email: $req_email,
            time_track: $time_track,
            reply_msg: $reply_msg,
            tags: $tags
        ) {
            id
            ticket_no
            subject
            priority
            status
            updated
        }
    }';

    $variables = [
        'id' => $ticketId,
        'contact_id' => $currentTicket['contact_id'],
        'agent_id' => $currentTicket['agent_id'],
        'subject' => $currentTicket['subject'] . ' [TEST UPDATE]',
        'type' => (int)($currentTicket['type'] ?? 1),
        'type_name' => $currentTicket['type_name'],
        'priority' => 'HIGH',
        'status' => 'WIP',
        'req_email' => $currentTicket['req_email'],
        'time_track' => '90',
        'reply_msg' => 'Test update from debug script',
        'tags' => ''
    ];

    echo "<h3>Update Variables:</h3>";
    echo "<pre>" . json_encode($variables, JSON_PRETTY_PRINT) . "</pre>";

    $updateResult = makeGraphQLRequest($mutation, $variables);

    echo "<h3>Update Result:</h3>";
    echo "<pre>" . json_encode($updateResult, JSON_PRETTY_PRINT) . "</pre>";
    
    // Check what the response structure looks like
    if (isset($updateResult['data'])) {
        echo "<h3>Response Data Structure:</h3>";
        echo "<pre>";
        print_r($updateResult['data']);
        echo "</pre>";
    }
    
    if (isset($updateResult['raw_response'])) {
        echo "<h3>Raw Response:</h3>";
        echo "<pre>" . htmlspecialchars($updateResult['raw_response']) . "</pre>";
    }
}
?>
