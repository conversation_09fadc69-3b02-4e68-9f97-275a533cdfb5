<?php
/**
 * Debug User Creation Script
 * This script will help identify why users aren't being added to Appika on the server
 */

// Include necessary files
require_once 'functions/server.php';
require_once 'functions/create-customer-minimal.php';
require_once 'config/api-config.php';
require_once 'vendor/autoload.php';

echo "<h1>User Creation Debug Script</h1>";
echo "<p>This script will test the user creation process and identify any issues.</p>";

// Test 1: Check database connection
echo "<h2>Test 1: Database Connection</h2>";
if ($conn) {
    echo "✅ Database connection successful<br>";
    
    // Check if user table exists and has required columns
    $result = mysqli_query($conn, "SHOW TABLES LIKE 'user'");
    if (mysqli_num_rows($result) > 0) {
        echo "✅ User table exists<br>";
        
        // Check for required columns
        $columns = ['id', 'username', 'email', 'password', 'appika_id', 'appika_customer_id'];
        foreach ($columns as $column) {
            $result = mysqli_query($conn, "SHOW COLUMNS FROM user LIKE '$column'");
            if (mysqli_num_rows($result) > 0) {
                echo "✅ Column '$column' exists<br>";
            } else {
                echo "❌ Column '$column' missing<br>";
            }
        }
    } else {
        echo "❌ User table does not exist<br>";
    }
} else {
    echo "❌ Database connection failed<br>";
    exit;
}

// Test 2: Check API configuration
echo "<h2>Test 2: API Configuration</h2>";
$apiConfig = getCustomerApiConfig();
echo "API Endpoint: " . $apiConfig['endpoint'] . "<br>";
echo "API Path: " . $apiConfig['path'] . "<br>";
echo "API Key: " . substr($apiConfig['key'], 0, 10) . "...<br>";

// Test 3: Test API connectivity
echo "<h2>Test 3: API Connectivity Test</h2>";
try {
    $client = new \GuzzleHttp\Client([
        'base_uri' => $apiConfig['endpoint'],
        'timeout' => 30,
        'http_errors' => false,
    ]);
    
    // Test a simple GET request to check connectivity
    $response = $client->request('GET', $apiConfig['path'], [
        'headers' => [
            'X-api-key' => $apiConfig['key'],
            'Accept' => 'application/json',
        ],
        'query' => ['limit' => 1] // Limit to 1 record for testing
    ]);
    
    $statusCode = $response->getStatusCode();
    $body = $response->getBody()->getContents();
    
    echo "API Response Status: $statusCode<br>";
    
    if ($statusCode >= 200 && $statusCode < 300) {
        echo "✅ API connectivity successful<br>";
        $data = json_decode($body, true);
        if ($data) {
            echo "✅ API response is valid JSON<br>";
        } else {
            echo "❌ API response is not valid JSON<br>";
        }
    } else {
        echo "❌ API connectivity failed - Status: $statusCode<br>";
        echo "Response: " . htmlspecialchars($body) . "<br>";
    }
} catch (Exception $e) {
    echo "❌ API connectivity exception: " . $e->getMessage() . "<br>";
}

// Test 4: Test minimal customer creation function
echo "<h2>Test 4: Test Customer Creation Function</h2>";

// Create test data
$testCustomerData = [
    'username' => 'test_user_' . time(),
    'email' => 'test_' . time() . '@example.com',
    'password' => 'testpassword123',
    'full_name' => 'Test User Debug',
    'address' => '123 Test Street',
    'address2' => 'Apt 1',
    'city' => 'Test City',
    'state' => 'Test State',
    'country' => 'TH',
    'postal_code' => '10110',
    'phone' => '0812345678',
    'company_name' => 'Test Company',
    'tax_id' => '1234567890',
    'timezone' => 'Asia/Bangkok'
];

echo "Testing with data:<br>";
echo "Username: " . $testCustomerData['username'] . "<br>";
echo "Email: " . $testCustomerData['email'] . "<br>";

try {
    $result = createCustomerMinimal($testCustomerData);
    
    echo "<h3>Creation Result:</h3>";
    echo "Success: " . ($result['success'] ? 'Yes' : 'No') . "<br>";
    echo "User ID: " . ($result['user_id'] ?? 'None') . "<br>";
    echo "Appika ID: " . ($result['appika_id'] ?? 'None') . "<br>";
    echo "Appika Customer ID: " . ($result['appika_customer_id'] ?? 'None') . "<br>";
    echo "Message: " . ($result['message'] ?? 'None') . "<br>";
    
    if (!empty($result['errors'])) {
        echo "Errors:<br>";
        foreach ($result['errors'] as $error) {
            echo "- $error<br>";
        }
    }
    
    // If user was created, check database
    if ($result['user_id']) {
        echo "<h3>Database Verification:</h3>";
        $stmt = $conn->prepare("SELECT id, username, email, appika_id, appika_customer_id FROM user WHERE id = ?");
        $stmt->bind_param("i", $result['user_id']);
        $stmt->execute();
        $dbResult = $stmt->get_result();
        
        if ($user = $dbResult->fetch_assoc()) {
            echo "✅ User found in database<br>";
            echo "ID: " . $user['id'] . "<br>";
            echo "Username: " . $user['username'] . "<br>";
            echo "Email: " . $user['email'] . "<br>";
            echo "Appika ID: " . ($user['appika_id'] ?? 'Not set') . "<br>";
            echo "Appika Customer ID: " . ($user['appika_customer_id'] ?? 'Not set') . "<br>";
        } else {
            echo "❌ User not found in database<br>";
        }
    }
    
} catch (Exception $e) {
    echo "❌ Exception during customer creation: " . $e->getMessage() . "<br>";
}

// Test 5: Check recent user registrations
echo "<h2>Test 5: Recent User Registrations</h2>";
$recentUsers = mysqli_query($conn, "
    SELECT id, username, email, registration_time, appika_id, appika_customer_id 
    FROM user 
    ORDER BY registration_time DESC 
    LIMIT 10
");

if ($recentUsers && mysqli_num_rows($recentUsers) > 0) {
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>ID</th><th>Username</th><th>Email</th><th>Registration Time</th><th>Appika ID</th><th>Appika Customer ID</th></tr>";
    
    while ($user = mysqli_fetch_assoc($recentUsers)) {
        echo "<tr>";
        echo "<td>" . $user['id'] . "</td>";
        echo "<td>" . $user['username'] . "</td>";
        echo "<td>" . $user['email'] . "</td>";
        echo "<td>" . $user['registration_time'] . "</td>";
        echo "<td>" . ($user['appika_id'] ?? 'Not set') . "</td>";
        echo "<td>" . ($user['appika_customer_id'] ?? 'Not set') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "No recent users found<br>";
}

// Test 6: Check error logs
echo "<h2>Test 6: Recent Error Logs</h2>";
$logFiles = [
    'logs/appika_api.log',
    'logs/appika_debug.log',
    'logs/appika_sync.log'
];

foreach ($logFiles as $logFile) {
    if (file_exists($logFile)) {
        echo "<h3>$logFile (last 10 lines):</h3>";
        $lines = file($logFile);
        $recentLines = array_slice($lines, -10);
        echo "<pre style='background: #f5f5f5; padding: 10px; max-height: 200px; overflow-y: auto;'>";
        foreach ($recentLines as $line) {
            echo htmlspecialchars($line);
        }
        echo "</pre>";
    } else {
        echo "<p>Log file $logFile does not exist</p>";
    }
}

// Test 7: Environment detection
echo "<h2>Test 7: Environment Detection</h2>";
echo "HTTP_HOST: " . ($_SERVER['HTTP_HOST'] ?? 'Not set') . "<br>";
echo "SERVER_NAME: " . ($_SERVER['SERVER_NAME'] ?? 'Not set') . "<br>";
echo "DOCUMENT_ROOT: " . ($_SERVER['DOCUMENT_ROOT'] ?? 'Not set') . "<br>";

$isLocalhost = (strpos($_SERVER['HTTP_HOST'], 'localhost') !== false || 
                strpos($_SERVER['HTTP_HOST'], '127.0.0.1') !== false ||
                strpos($_SERVER['DOCUMENT_ROOT'], 'xampp') !== false);

echo "Detected as localhost: " . ($isLocalhost ? 'Yes' : 'No') . "<br>";

// Test 8: Check if Guzzle is available
echo "<h2>Test 8: Dependencies</h2>";
if (class_exists('\GuzzleHttp\Client')) {
    echo "✅ Guzzle HTTP Client is available<br>";
} else {
    echo "❌ Guzzle HTTP Client is not available<br>";
}

if (function_exists('getCustomerApiConfig')) {
    echo "✅ getCustomerApiConfig function is available<br>";
} else {
    echo "❌ getCustomerApiConfig function is not available<br>";
}

echo "<h2>Debug Complete</h2>";
echo "<p>Check the results above to identify any issues with user creation and Appika integration.</p>";
?> 