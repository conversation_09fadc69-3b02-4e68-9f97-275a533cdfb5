name: CI

on:
  # Allows you to run this workflow manually from the Actions tab
  workflow_dispatch: {}
  push:
    branches:
      - master
      - beta
      - sdk-release/**
      - feature/**
    tags:
      - v[0-9]+.[0-9]+.[0-9]+*
  pull_request:
    branches:
      - master
      - beta
      - sdk-release/**
      - feature/**

jobs:
  php-cs-fixer:
    name: PHP-CS-Fixer

    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v3

      - name: PHP-CS-Fixer
        uses: docker://oskarstark/php-cs-fixer-ga:3.4.0
        with:
          args: --format=txt --diff --dry-run --using-cache=no --verbose

  phpstan:
    name: PHPStan

    runs-on: ubuntu-latest

    strategy:
      matrix:
        php-version:
          - "8.2"

    steps:
      - uses: actions/checkout@v3

      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: ${{ matrix.php-version }}

      - name: Get Composer Cache Directory
        id: composer-cache
        run: |
          echo "dir=$(composer config cache-files-dir)" >> $GITHUB_OUTPUT

      - uses: actions/cache@v3
        with:
          path: ${{ steps.composer-cache.outputs.dir }}
          # Conventionally you would hash "composer.lock", but we don't commit
          # that, so we hash on "composer.json" instead.
          key: ${{ runner.os }}-composer-${{ hashFiles('composer.json') }}

      - name: Validate composer.json
        run: composer validate

      - name: Install dependencies
        run: composer install --prefer-dist --no-progress

      - name: Run phpstan
        run: make phpstan

  tests:
    name: Tests

    runs-on: ubuntu-latest

    strategy:
      fail-fast: false
      matrix:
        env:
          - AUTOLOAD=0
          - AUTOLOAD=1
        php-version:
          - "5.6"
          - "7.0"
          - "7.1"
          - "7.2"
          - "7.3"
          - "7.4"
          - "8.0"
          - "8.1"
          - "8.2"

    steps:
      - uses: actions/checkout@v3

      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: ${{ matrix.php-version }}
          coverage: xdebug

      - name: Get Composer Cache Directory
        id: composer-cache
        run: |
          echo "dir=$(composer config cache-files-dir)" >> $GITHUB_OUTPUT
      - uses: actions/cache@v3
        with:
          path: ${{ steps.composer-cache.outputs.dir }}
          key: ${{ runner.os }}-composer-${{ hashFiles('composer.json') }}

      # We run php-cs-fixer and PHPStan in separate jobs, but the versions we use are not compatible
      # with all the versions of PHP that we want to execute PHPUnit upon
      - name: Trim dependency
        run: composer remove --dev --no-update friendsofphp/php-cs-fixer phpstan/phpstan

      - name: Validate composer.json
        run: composer validate

      - name: Install dependencies
        run: composer install --prefer-dist --no-progress

      - uses: stripe/openapi/actions/stripe-mock@master

      - name: Run test suite
        run: |
          php --version
          make ci-test

      - name: Coveralls
        if: matrix.php-version == '8.2' && matrix.env == 'AUTOLOAD=1'
        uses: coverallsapp/github-action@v2
        with:
          files: clover.xml
          flag-name: php-${{ matrix.php-version }}-${{ matrix.env }}
          github-token: ${{ secrets.GITHUB_TOKEN }}

  publish:
    # Doesn't actually publish. The publish happens via a packagist webhook configured in the Github UI. But we still display a message here
    name: Publish
    if: >-
      (github.event_name == 'workflow_dispatch' || github.event_name == 'push') &&
      startsWith(github.ref, 'refs/tags/v') &&
      endsWith(github.actor, '-stripe')
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - uses: stripe/openapi/actions/notify-release@master
        if: always()
        with:
          bot_token: ${{ secrets.SLACK_BOT_TOKEN }}
