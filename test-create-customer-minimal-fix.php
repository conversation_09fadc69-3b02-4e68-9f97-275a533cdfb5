<?php
/**
 * Test create-customer-minimal.php Fix
 * Test if the path fix resolves the server upload issue
 */

echo "<h1>Test create-customer-minimal.php Fix</h1>";
echo "<p>Testing if the conditional includes fix resolves the server upload issue...</p>";

// Test 1: Test including the file from different contexts
echo "<h2>1. Test File Inclusion</h2>";

try {
    // Simulate including from different directories
    echo "Testing include from root context...<br>";
    require_once 'functions/create-customer-minimal.php';
    echo "✅ Successfully included create-customer-minimal.php<br>";
} catch (Exception $e) {
    echo "❌ Error including create-customer-minimal.php: " . $e->getMessage() . "<br>";
}

// Test 2: Check if functions are available
echo "<h2>2. Function Availability Test</h2>";

$functions_to_check = [
    'createCustomerMinimal',
    'createCustomerInAppika',
    'createLocationInAppika',
    'createMinimalCustomerRecord',
    'updateCustomerAppikaIds'
];

foreach ($functions_to_check as $function) {
    if (function_exists($function)) {
        echo "✅ $function exists<br>";
    } else {
        echo "❌ $function does not exist<br>";
    }
}

// Test 3: Check if classes are available
echo "<h2>3. Class Availability Test</h2>";

$classes_to_check = [
    'GuzzleHttp\Client',
    'DBController'
];

foreach ($classes_to_check as $class) {
    if (class_exists($class)) {
        echo "✅ $class exists<br>";
    } else {
        echo "❌ $class does not exist<br>";
    }
}

// Test 4: Test API Configuration
echo "<h2>4. API Configuration Test</h2>";

if (function_exists('getCustomerApiConfig')) {
    try {
        $apiConfig = getCustomerApiConfig();
        echo "✅ API config loaded successfully<br>";
        echo "Endpoint: " . $apiConfig['endpoint'] . "<br>";
        echo "Path: " . $apiConfig['path'] . "<br>";
        echo "Key: " . substr($apiConfig['key'], 0, 10) . "...<br>";
    } catch (Exception $e) {
        echo "❌ Error loading API config: " . $e->getMessage() . "<br>";
    }
} else {
    echo "❌ getCustomerApiConfig function not available<br>";
}

// Test 5: Test Database Connection
echo "<h2>5. Database Connection Test</h2>";

if (isset($conn) && $conn) {
    echo "✅ Database connection available<br>";
} else {
    echo "❌ Database connection not available<br>";
    
    // Try to get connection from DBController
    if (class_exists('DBController')) {
        try {
            $db = new DBController();
            echo "✅ DBController available<br>";
        } catch (Exception $e) {
            echo "❌ Error creating DBController: " . $e->getMessage() . "<br>";
        }
    }
}

// Test 6: Test Guzzle HTTP Client
echo "<h2>6. Guzzle HTTP Client Test</h2>";

if (class_exists('GuzzleHttp\Client')) {
    echo "✅ GuzzleHttp\Client class exists<br>";
    
    try {
        $client = new \GuzzleHttp\Client([
            'timeout' => 8,
            'http_errors' => false,
        ]);
        echo "✅ Guzzle client can be instantiated<br>";
        
        // Test API connection
        if (function_exists('getCustomerApiConfig')) {
            $apiConfig = getCustomerApiConfig();
            
            $response = $client->request('GET', $apiConfig['endpoint'] . $apiConfig['path'], [
                'headers' => [
                    'X-api-key' => $apiConfig['key'],
                    'Accept' => 'application/json',
                ],
                'query' => ['limit' => 1],
                'timeout' => 5
            ]);
            
            $statusCode = $response->getStatusCode();
            echo "API Test Status: " . $statusCode . "<br>";
            
            if ($statusCode == 200) {
                echo "✅ Appika API connection successful<br>";
            } elseif ($statusCode == 401) {
                echo "❌ Appika API authentication failed (401)<br>";
            } else {
                echo "⚠️ Appika API returned status: " . $statusCode . "<br>";
            }
        }
        
    } catch (Exception $e) {
        echo "❌ Error with Guzzle: " . $e->getMessage() . "<br>";
    }
} else {
    echo "❌ GuzzleHttp\Client class does not exist<br>";
}

// Test 7: Test createCustomerMinimal Function (Dry Run)
echo "<h2>7. Test createCustomerMinimal Function (Dry Run)</h2>";

if (function_exists('createCustomerMinimal')) {
    echo "✅ createCustomerMinimal function is available<br>";
    
    // Prepare test data
    $testData = [
        'username' => 'test_user_' . time(),
        'email' => '<EMAIL>',
        'password' => 'testpass123',
        'full_name' => 'Test User',
        'address' => 'Test Address',
        'address2' => '',
        'city' => 'Test City',
        'state' => 'Test State',
        'country' => 'TH',
        'postal_code' => '12345',
        'phone' => '1234567890',
        'company_name' => 'Test Company',
        'tax_id' => '',
        'district' => '',
        'timezone' => 'Asia/Bangkok',
        'stripe_customer_id' => null
    ];
    
    echo "✅ Test data prepared<br>";
    echo "⚠️ Skipping actual function call to avoid creating test users<br>";
    echo "📝 Test data structure: " . json_encode(array_keys($testData)) . "<br>";
    
} else {
    echo "❌ createCustomerMinimal function not available<br>";
}

// Test 8: Environment Detection
echo "<h2>8. Environment Detection Test</h2>";

$is_localhost = (strpos($_SERVER['HTTP_HOST'], 'localhost') !== false || strpos($_SERVER['HTTP_HOST'], '127.0.0.1') !== false);
$file_base_path = $is_localhost ? '/helloit' : '';

echo "Server Host: " . $_SERVER['HTTP_HOST'] . "<br>";
echo "Is Localhost: " . ($is_localhost ? 'Yes' : 'No') . "<br>";
echo "File Base Path: '" . $file_base_path . "'<br>";
echo "Document Root: " . $_SERVER['DOCUMENT_ROOT'] . "<br>";

// Test 9: Path Resolution Test
echo "<h2>9. Path Resolution Test</h2>";

$paths_to_test = [
    'vendor/autoload.php' => $_SERVER['DOCUMENT_ROOT'] . $file_base_path . '/vendor/autoload.php',
    'config/api-config.php' => $_SERVER['DOCUMENT_ROOT'] . $file_base_path . '/config/api-config.php',
    'functions/server.php' => __DIR__ . '/server.php',
    'functions/customer-data-service.php' => __DIR__ . '/customer-data-service.php'
];

foreach ($paths_to_test as $name => $path) {
    if (file_exists($path)) {
        echo "✅ $name exists at: $path<br>";
    } else {
        echo "❌ $name NOT found at: $path<br>";
    }
}

echo "<h2>Summary</h2>";
echo "<p>This test verifies that the conditional includes fix resolves the server path issues.</p>";

echo "<h2>Next Steps</h2>";
echo "<p>If this test shows all ✅ results:</p>";
echo "<ol>";
echo "<li>Try creating a user in admin-users.php</li>";
echo "<li>Check if the user gets created in Appika</li>";
echo "<li>Test post-purchase user creation</li>";
echo "<li>Check error logs for any remaining issues</li>";
echo "</ol>";

echo "<h2>If Issues Persist</h2>";
echo "<p>If users still don't get created in Appika:</p>";
echo "<ol>";
echo "<li>Check the PHP error logs during user creation</li>";
echo "<li>Look for specific Appika API error messages</li>";
echo "<li>Test the Appika API authentication separately</li>";
echo "<li>Verify the customer data format matches Appika requirements</li>";
echo "</ol>";
?>
