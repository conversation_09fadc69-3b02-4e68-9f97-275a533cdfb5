<?php
/**
 * Ticket Expiration Widget for User Dashboard
 * Shows user's ticket status with expiration information
 */

require_once('../config/ticket-expiration-config.php');
require_once('../functions/ticket-expiration-functions.php');

// Get user's ticket summary with expiration info
if (isset($user) && isset($user['username'])) {
    $username = $user['username'];
    $ticket_summary = getUserTicketSummaryWithExpiration($username);
    $expiring_soon = getTicketsExpiringSoon($username);
    $config = getTicketExpirationConfig();
    
    // Check if system is enabled
    if (!$config['system_enabled']) {
        return; // Don't show widget if system is disabled
    }
?>

<div class="ticket-expiration-widget">
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-clock"></i> Your Tickets & Expiration Status
            </h5>
        </div>
        <div class="card-body">
            <?php if (empty($ticket_summary)): ?>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    You don't have any active tickets. <a href="buy-now.php">Purchase tickets</a> to get started.
                </div>
            <?php else: ?>
                <!-- Ticket Summary -->
                <div class="row mb-3">
                    <?php foreach ($ticket_summary as $type => $data): ?>
                    <div class="col-md-4 mb-2">
                        <div class="ticket-type-card">
                            <div class="ticket-type-header">
                                <span class="badge badge-<?php echo strtolower($type); ?>">
                                    <?php echo ucfirst($type); ?>
                                </span>
                            </div>
                            <div class="ticket-count">
                                <?php echo $data['total_remaining']; ?>
                            </div>
                            <div class="ticket-label">Available Tickets</div>
                            <div class="expiration-info">
                                <small class="text-muted">
                                    <i class="fas fa-calendar-alt"></i>
                                    Expires: <?php echo date('M d, Y', strtotime($data['earliest_expiration'])); ?>
                                </small>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>

                <!-- Expiring Soon Warning -->
                <?php if (!empty($expiring_soon)): ?>
                <div class="alert alert-warning">
                    <h6><i class="fas fa-exclamation-triangle"></i> Tickets Expiring Soon</h6>
                    <p class="mb-2">The following tickets will expire within <?php echo $config['warning_period_days']; ?> days:</p>
                    <ul class="mb-0">
                        <?php 
                        $grouped_expiring = [];
                        foreach ($expiring_soon as $ticket) {
                            $days_left = getDaysUntilExpiration($ticket['purchase_time']);
                            $key = $ticket['ticket_type'] . '_' . $days_left;
                            if (!isset($grouped_expiring[$key])) {
                                $grouped_expiring[$key] = [
                                    'type' => $ticket['ticket_type'],
                                    'days_left' => $days_left,
                                    'total_tickets' => 0
                                ];
                            }
                            $grouped_expiring[$key]['total_tickets'] += $ticket['remaining_tickets'];
                        }
                        
                        foreach ($grouped_expiring as $group): 
                        ?>
                        <li>
                            <strong><?php echo $group['total_tickets']; ?> <?php echo ucfirst($group['type']); ?> tickets</strong>
                            - Expires in <span class="text-danger"><?php echo $group['days_left']; ?> days</span>
                        </li>
                        <?php endforeach; ?>
                    </ul>
                </div>
                <?php endif; ?>

                <!-- Ticket Lifetime Info -->
                <div class="ticket-lifetime-info">
                    <small class="text-muted">
                        <i class="fas fa-info-circle"></i>
                        Tickets are valid for <?php echo $config['ticket_lifetime_months']; ?> months from purchase date.
                        Oldest tickets are used first when creating support tickets.
                    </small>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<style>
.ticket-expiration-widget {
    margin-bottom: 20px;
}

.ticket-type-card {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    text-align: center;
    height: 100%;
    border: 1px solid #dee2e6;
}

.ticket-type-header {
    margin-bottom: 10px;
}

.badge-starter {
    background-color: #fbbf24;
    color: #fff;
}

.badge-premium {
    background-color: #8b5cf6;
    color: #fff;
}

.badge-ultimate {
    background-color: #3b82f6;
    color: #fff;
}

.ticket-count {
    font-size: 2rem;
    font-weight: bold;
    color: #473BF0;
    margin-bottom: 5px;
}

.ticket-label {
    font-size: 0.9rem;
    color: #6c757d;
    margin-bottom: 10px;
}

.expiration-info {
    border-top: 1px solid #dee2e6;
    padding-top: 8px;
}

.alert {
    border-radius: 8px;
    border: none;
}

.alert-warning {
    background-color: #fff3cd;
    color: #856404;
    border-left: 4px solid #ffc107;
}

.alert-info {
    background-color: #d1ecf1;
    color: #0c5460;
    border-left: 4px solid #17a2b8;
}

.ticket-lifetime-info {
    background: #e9ecef;
    padding: 10px;
    border-radius: 6px;
    margin-top: 15px;
}

.card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.card-header {
    background: linear-gradient(135deg, #473BF0 0%, #5B4CF0 100%);
    color: white;
    border-radius: 10px 10px 0 0 !important;
    padding: 15px 20px;
}

@media (max-width: 768px) {
    .ticket-count {
        font-size: 1.5rem;
    }
    
    .ticket-type-card {
        margin-bottom: 15px;
    }
}
</style>

<?php
} // End if user exists
?>

<script>
// Auto-refresh widget every 5 minutes to show updated expiration info
setInterval(function() {
    // Only refresh if the page is visible to avoid unnecessary requests
    if (!document.hidden) {
        location.reload();
    }
}, 300000); // 5 minutes
</script>
