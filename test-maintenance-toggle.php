<?php
/**
 * Quick Maintenance Mode Toggle for Testing
 */

$statusFile = 'functions/appika-status-checker.php';
$content = file_get_contents($statusFile);

if (isset($_GET['action'])) {
    if ($_GET['action'] === 'enable') {
        // Enable maintenance mode by making the function return false
        $content = str_replace(
            '// Auto-detect server status - no manual override needed',
            '// TESTING: Force maintenance mode
    return false; // Remove this line to restore normal operation
    // Auto-detect server status - no manual override needed',
            $content
        );
        file_put_contents($statusFile, $content);
        $message = "🚨 Maintenance mode ENABLED for testing";
        $status = "enabled";
    } elseif ($_GET['action'] === 'disable') {
        // Disable maintenance mode
        $content = str_replace(
            '// TESTING: Force maintenance mode
    return false; // Remove this line to restore normal operation
    // Auto-detect server status - no manual override needed',
            '// Auto-detect server status - no manual override needed',
            $content
        );
        file_put_contents($statusFile, $content);
        $message = "✅ Maintenance mode DISABLED - normal operation restored";
        $status = "disabled";
    }
}

// Check current status
$currentStatus = (strpos($content, 'return false; // Remove this line') !== false) ? 'enabled' : 'disabled';
?>
<!DOCTYPE html>
<html>
<head>
    <title>Maintenance Mode Toggle - Testing</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .status { padding: 20px; border-radius: 8px; margin: 20px 0; font-weight: bold; }
        .enabled { background: #ffebee; border: 2px solid #f44336; color: #c62828; }
        .disabled { background: #e8f5e8; border: 2px solid #4caf50; color: #2e7d32; }
        .btn { padding: 12px 24px; margin: 10px; text-decoration: none; border-radius: 6px; display: inline-block; font-weight: bold; }
        .btn-enable { background: #f44336; color: white; }
        .btn-disable { background: #4caf50; color: white; }
        .btn:hover { opacity: 0.8; transform: translateY(-1px); }
        .test-links { background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0; }
        .test-links h3 { color: #333; margin-top: 0; }
        .test-links ul { list-style: none; padding: 0; }
        .test-links li { margin: 8px 0; }
        .test-links a { color: #007bff; text-decoration: none; padding: 5px 10px; border-radius: 4px; background: #e7f3ff; }
        .test-links a:hover { background: #007bff; color: white; }
        .warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; padding: 15px; border-radius: 5px; margin: 20px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Maintenance Mode Toggle - Testing</h1>
        
        <?php if (isset($message)): ?>
            <div class="status <?php echo $status; ?>">
                <?php echo $message; ?>
            </div>
        <?php endif; ?>
        
        <div class="status <?php echo $currentStatus; ?>">
            <strong>Current Status:</strong> Maintenance mode is <strong><?php echo strtoupper($currentStatus); ?></strong>
            <?php if ($currentStatus === 'enabled'): ?>
                <br><small>🚨 All customer panel pages will show maintenance behavior</small>
            <?php else: ?>
                <br><small>✅ Normal operation - pages will only show maintenance if Appika APIs are actually down</small>
            <?php endif; ?>
        </div>
        
        <div style="text-align: center; margin: 30px 0;">
            <a href="?action=enable" class="btn btn-enable">🚨 Enable Test Maintenance</a>
            <a href="?action=disable" class="btn btn-disable">✅ Disable Test Maintenance</a>
        </div>
        
        <div class="warning">
            <strong>⚠️ Testing Instructions:</strong>
            <ol>
                <li><strong>Enable maintenance mode</strong> using the button above</li>
                <li><strong>Test the pages</strong> using the links below</li>
                <li><strong>Verify behavior:</strong>
                    <ul>
                        <li>Most pages should redirect to maintenance page</li>
                        <li>Profile page should show warning banner and disabled edit button</li>
                    </ul>
                </li>
                <li><strong>Disable maintenance mode</strong> when done testing</li>
            </ol>
        </div>
        
        <div class="test-links">
            <h3>🧪 Test Customer Panel Pages:</h3>
            <ul>
                <li><a href="front-end/my-ticket.php" target="_blank">📋 My Tickets</a> - Should redirect to maintenance</li>
                <li><a href="front-end/profile.php" target="_blank">👤 Profile</a> - Should show warning + disable edit button</li>
                <li><a href="front-end/payment-methods.php" target="_blank">💳 Payment Methods</a> - Should redirect to maintenance</li>
                <li><a href="front-end/purchase-history.php" target="_blank">🛒 Purchase History</a> - Should redirect to maintenance</li>
                <li><a href="front-end/my-ratings.php" target="_blank">⭐ My Ratings</a> - Should redirect to maintenance</li>
                <li><a href="front-end/create-ticket.php" target="_blank">🎫 Create Ticket</a> - Should redirect to maintenance</li>
            </ul>
            
            <h3>🔧 Maintenance & Debug Pages:</h3>
            <ul>
                <li><a href="front-end/server-down.php" target="_blank">🚨 Maintenance Page</a> - Direct access</li>
                <li><a href="debug-status.php" target="_blank">🔍 API Status Debug</a> - Check real API status</li>
            </ul>
        </div>
        
        <div style="text-align: center; margin-top: 30px; color: #666; font-size: 14px;">
            <p><strong>Remember:</strong> Always disable test maintenance mode when finished testing!</p>
        </div>
    </div>
</body>
</html>
