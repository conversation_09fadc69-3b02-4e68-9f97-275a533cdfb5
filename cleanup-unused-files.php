<?php
/**
 * Cleanup Unused Files Script
 * This script will delete test files, debug files, and other unused files to free up space
 * 
 * IMPORTANT: Review the file list before running this script!
 * Run this script from the root directory of your project
 */

// Files to delete (organized by category)
$filesToDelete = [
    // Root test files
    'test-admin-phone-update.php',
    'test-admin-user-creation.php',
    'test-api-status.php',
    'test-appika-api.php',
    'test-appika-send-message.php',
    'test-composer-installation.php',
    'test-create-customer-minimal-fix.php',
    'test-guzzle-working.php',
    'test-maintenance-toggle.php',
    'test-maintenance.php',
    'test-name-display.php',
    'test-path-fix.php',
    'test-profile-maintenance.php',
    'test-server-environment.php',
    'test-ticket-update.php',
    'test-user-creation-context.php',
    'test-user-creation.php',
    
    // Root debug files
    'debug-appika-detailed.php',
    'debug-status.php',
    'debug-user-creation.php',
    
    // Debug directory
    'debug/check-table-structure.php',
    'debug/insert-expired-tickets.php',
    'debug/test-expiration-logs.php',
    'debug/test-expiration-process.php',
    
    // Functions test files
    'functions/debug-multiple-items.php',
    'functions/debug-profile-update.php',
    'functions/test-appika-debug.php',
    'functions/test-appika-status.php',
    'functions/test-email-fix.php',
    'functions/test-email-receipt-fix.php',
    'functions/test-email-server.php',
    'functions/test-location-fix.php',
    'functions/test-minimal-customer-integration.php',
    'functions/test-payment-success-fix.php',
    'functions/test-profile-appika-display.php',
    'functions/test-profile-edit-appika.php',
    'functions/test-profile-fix.php',
    'functions/test-profile-updates.php',
    'functions/test-receipt-email.php',
    'functions/test-timezone-detection.php',
    'functions/test-timezone-receipt.php',
    'functions/test-updated-customer-creation.php',
    
    // Front-end debug/test files
    'front-end/debug-cart.php',
    'front-end/debug-expiration-logs.php',
    'front-end/debug-message-api.php',
    'front-end/debug-notifications.php',
    'front-end/test-cleanup.php',
    'front-end/test-direct-simple.php',
    'front-end/test-expiration.php',
    'front-end/test-login.php',
    'front-end/test-payment-methods.php',
    'front-end/test-payment-redirect.php',
    'front-end/test-session.php',
    
    // Merlion debug/test files
    'merlion/chat-diagnostic.php',
    'merlion/create-expired-test-tickets.php',
    'merlion/debug-expiration.php',
    'merlion/debug-ticket-issue.php',
    'merlion/debug-ticket-notifications.php',
    'merlion/test-appika-api.php',
    'merlion/test-sync-disabled.php',
    'merlion/test-ticket-expiration.php',
    'merlion/test-ticket-update.php',
    'merlion/real-time-ticket-monitor.php',
    
    // Fix/Setup files (one-time use)
    'fix-composer.bat',
    'fix-composer.sh',
    'fix-customer-locations.php',
    'fix-guzzle-loading.php',
    'diagnose-guzzle-issue.php',
    'reinstall-dependencies.php',
    'server-diagnostic.php',
    'setup-company-tax-fields.php',
    'migrate_chat_direct.php',
    'check-all-users-sync.php',
    'check-appika-status.php',
    'merlion/fix-cleanup-config.php',
    'merlion/fix-missing-locations.php',
    'merlion/fix-ticket-expiration.php',
    'merlion/fix-units.php',
    'merlion/force-update-expiration.php',
    'functions/clear-test-updates.php',
    'functions/migrate-to-minimal-database.php',
    'functions/process-pending-users.php',
    'functions/remove-admin-notifications-table.php',
    'functions/update-admin-notifications-code.php',
    'functions/update-guest-credentials.php',
    'functions/update-payment-temp-table.php',
    'database/setup-ticket-expiration.php',
    'database/update-ticket-expiration-units.php',
    
    // Documentation files
    'EMAIL_RECEIPT_IMPLEMENTATION.md',
    'GUZZLE-ISSUE-SOLUTION.md',
    'front-end/EMAIL_SETUP_GUIDE.md',
    'docs/ticket-expiration-system.md',
    'graphql-ticket/README.md',
    'graphql-ticket/ticket_schema_example.md',
    'api/README.md',
    
    // Unused/Legacy files
    'front-end/index.html',
    'front-end/profile-minimal.php',
    'front-end/ticket-detail-new.php',
    'front-end/ticket-chat-local-api.php',
    'front-end/ticket-messages-direct-api.php',
    'front-end/ticket-messages-direct.php',
    'merlion/admin-chat-simple.php',
    'merlion/admin-chat.php',
    'merlion/admin-tickets-new.php',
    'header-footer/newnavtest.php',
    'form_submission_log.txt',
    'functions/password_reset.log',
    'merlion/ticket_status_update.log'
];

// Log files to clear (optional - you might want to keep recent logs)
$logFilesToClear = [
    'logs/api_key_changes.log',
    'logs/api_key_updates.log',
    'logs/appika_api.log',
    'logs/appika_api_admin.log',
    'logs/appika_api_update.log',
    'logs/appika_debug.log',
    'logs/appika_sync.log',
    'logs/email-fallback.log',
    'logs/ticket_api.log',
    'logs/ticket_consumption.log',
    'logs/ticket_logs_delete.log',
    'logs/ticket_update_debug.log'
];

// Get command line argument or web parameter
$action = '';
if (isset($argv[1])) {
    // Command line mode
    $action = $argv[1];
} elseif (isset($_GET['action'])) {
    // Web browser mode
    $action = $_GET['action'];
}

if ($action === 'preview') {
    // Check if running in web browser
    if (isset($_SERVER['HTTP_HOST'])) {
        echo "<h2>📋 Preview Mode</h2>";
        echo "<p>Files that would be deleted:</p>";
        echo "<div style='background:#f8f9fa;padding:15px;border-radius:5px;font-family:monospace;'>";
    } else {
        echo "=== PREVIEW MODE ===\n";
        echo "Files that would be deleted:\n\n";
    }
    
    $totalSize = 0;
    $deletedCount = 0;
    
    foreach ($filesToDelete as $file) {
        if (file_exists($file)) {
            $size = filesize($file);
            $totalSize += $size;
            $deletedCount++;
            echo "✓ $file (" . formatBytes($size) . ")\n";
        } else {
            echo "✗ $file (not found)\n";
        }
    }
    
    echo "\n=== LOG FILES (optional) ===\n";
    $logSize = 0;
    $logCount = 0;
    
    foreach ($logFilesToClear as $file) {
        if (file_exists($file)) {
            $size = filesize($file);
            $logSize += $size;
            $logCount++;
            echo "✓ $file (" . formatBytes($size) . ")\n";
        } else {
            echo "✗ $file (not found)\n";
        }
    }
    
    echo "\n=== SUMMARY ===\n";
    echo "Files to delete: $deletedCount\n";
    echo "Total size: " . formatBytes($totalSize) . "\n";
    echo "Log files: $logCount (" . formatBytes($logSize) . ")\n";
    echo "Total potential savings: " . formatBytes($totalSize + $logSize) . "\n";
    echo "\nTo actually delete files, run: php cleanup-unused-files.php delete\n";
    echo "To delete including logs, run: php cleanup-unused-files.php delete-all\n";
    
} elseif ($action === 'delete' || $action === 'delete-all') {
    echo "=== DELETION MODE ===\n";
    
    $deletedCount = 0;
    $deletedSize = 0;
    $errors = [];
    
    foreach ($filesToDelete as $file) {
        if (file_exists($file)) {
            $size = filesize($file);
            if (unlink($file)) {
                $deletedCount++;
                $deletedSize += $size;
                echo "✓ Deleted: $file (" . formatBytes($size) . ")\n";
            } else {
                $errors[] = "Failed to delete: $file";
                echo "✗ Failed: $file\n";
            }
        }
    }
    
    // Delete logs if requested
    if ($action === 'delete-all') {
        echo "\n=== CLEARING LOG FILES ===\n";
        foreach ($logFilesToClear as $file) {
            if (file_exists($file)) {
                $size = filesize($file);
                if (file_put_contents($file, '') !== false) {
                    $deletedSize += $size;
                    echo "✓ Cleared: $file (" . formatBytes($size) . ")\n";
                } else {
                    $errors[] = "Failed to clear: $file";
                    echo "✗ Failed: $file\n";
                }
            }
        }
    }
    
    echo "\n=== CLEANUP COMPLETE ===\n";
    echo "Files deleted: $deletedCount\n";
    echo "Space freed: " . formatBytes($deletedSize) . "\n";
    
    if (!empty($errors)) {
        echo "\n=== ERRORS ===\n";
        foreach ($errors as $error) {
            echo "• $error\n";
        }
    }
    
} else {
    // Check if running in web browser
    if (isset($_SERVER['HTTP_HOST'])) {
        // Web browser mode
        echo "<h1>🗑️ Cleanup Unused Files</h1>";
        echo "<p>This script will help you clean up test files, debug files, and other unused files.</p>";
        echo "<h3>Choose an action:</h3>";
        echo "<p><a href='?action=preview' style='background:#007bff;color:white;padding:10px 20px;text-decoration:none;border-radius:5px;'>📋 Preview Files</a> - Show what would be deleted</p>";
        echo "<p><a href='?action=delete' style='background:#28a745;color:white;padding:10px 20px;text-decoration:none;border-radius:5px;'>🗑️ Delete Files</a> - Delete files (keep logs)</p>";
        echo "<p><a href='?action=delete-all' style='background:#dc3545;color:white;padding:10px 20px;text-decoration:none;border-radius:5px;'>🔥 Delete All</a> - Delete files and clear logs</p>";
        echo "<p><strong>⚠️ IMPORTANT:</strong> Always run 'Preview Files' first to see what will be deleted!</p>";
    } else {
        // Command line mode
        echo "=== CLEANUP UNUSED FILES ===\n";
        echo "This script will help you clean up test files, debug files, and other unused files.\n\n";
        echo "Usage:\n";
        echo "  php cleanup-unused-files.php preview     - Show what would be deleted\n";
        echo "  php cleanup-unused-files.php delete      - Delete files (keep logs)\n";
        echo "  php cleanup-unused-files.php delete-all  - Delete files and clear logs\n\n";
        echo "IMPORTANT: Always run 'preview' first to see what will be deleted!\n";
    }
}

function formatBytes($size, $precision = 2) {
    $units = array('B', 'KB', 'MB', 'GB', 'TB');
    
    for ($i = 0; $size > 1024 && $i < count($units) - 1; $i++) {
        $size /= 1024;
    }
    
    return round($size, $precision) . ' ' . $units[$i];
}
?>
