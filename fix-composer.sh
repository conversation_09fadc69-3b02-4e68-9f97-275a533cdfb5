#!/bin/bash

# Fix Composer Dependencies Script
# This script will help fix Composer dependency issues

echo "=== Composer Dependencies Fix Script ==="
echo "This script will help fix Guzzle and other Composer dependencies"
echo ""

# Check if we're in the right directory
if [ ! -f "composer.json" ]; then
    echo "❌ Error: composer.json not found in current directory"
    echo "Please run this script from your project root directory"
    exit 1
fi

echo "✅ Found composer.json"

# Backup existing vendor directory
if [ -d "vendor" ]; then
    backup_name="vendor_backup_$(date +%Y%m%d_%H%M%S)"
    echo "📦 Backing up existing vendor directory to $backup_name"
    mv vendor "$backup_name"
    echo "✅ Backup created: $backup_name"
fi

# Check if composer is available
if command -v composer &> /dev/null; then
    echo "✅ Composer is available"
    COMPOSER_CMD="composer"
elif [ -f "composer.phar" ]; then
    echo "✅ Found composer.phar"
    COMPOSER_CMD="php composer.phar"
else
    echo "❌ Composer not found. Downloading composer.phar..."
    
    # Download composer
    php -r "copy('https://getcomposer.org/installer', 'composer-setup.php');"
    php composer-setup.php
    php -r "unlink('composer-setup.php');"
    
    if [ -f "composer.phar" ]; then
        echo "✅ Downloaded composer.phar"
        COMPOSER_CMD="php composer.phar"
    else
        echo "❌ Failed to download composer"
        exit 1
    fi
fi

# Clear composer cache
echo "🧹 Clearing Composer cache..."
$COMPOSER_CMD clear-cache

# Install dependencies
echo "📦 Installing dependencies..."
$COMPOSER_CMD install --no-dev --optimize-autoloader

# Check if installation was successful
if [ -f "vendor/autoload.php" ]; then
    echo "✅ Dependencies installed successfully!"
    
    # Test if Guzzle is available
    echo "🧪 Testing Guzzle availability..."
    php -r "
    require_once 'vendor/autoload.php';
    if (class_exists('GuzzleHttp\Client')) {
        echo '✅ GuzzleHttp\Client is available\n';
        try {
            \$client = new GuzzleHttp\Client(['timeout' => 5]);
            echo '✅ Guzzle client can be instantiated\n';
        } catch (Exception \$e) {
            echo '❌ Error creating Guzzle client: ' . \$e->getMessage() . '\n';
        }
    } else {
        echo '❌ GuzzleHttp\Client is not available\n';
    }
    "
else
    echo "❌ Installation failed - vendor/autoload.php not found"
    exit 1
fi

echo ""
echo "=== Installation Complete ==="
echo "You can now test your application:"
echo "1. Visit test-composer-installation.php to verify installation"
echo "2. Visit debug-appika-detailed.php to test Appika integration"
echo "3. Try creating users in admin-users.php"
echo ""
echo "If you still have issues, check:"
echo "- PHP extensions: curl, json, mbstring, openssl"
echo "- File permissions on vendor directory"
echo "- PHP memory_limit and max_execution_time"
