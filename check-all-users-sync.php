<?php
include('functions/server.php');
include('functions/ticket-expiration-functions.php');

echo "<h2>Checking All Users for Ticket Sync Issues</h2>\n";

// Get all users with tickets
$users_query = "SELECT username, starter_tickets, premium_tickets, ultimate_tickets 
                FROM user 
                WHERE username IS NOT NULL AND username != '' 
                AND (starter_tickets > 0 OR premium_tickets > 0 OR ultimate_tickets > 0)
                ORDER BY username";
$users_result = mysqli_query($conn, $users_query);

echo "<table border='1' style='border-collapse: collapse; width: 100%;'>\n";
echo "<tr style='background-color: #f0f0f0;'>
        <th>Username</th>
        <th>User Table<br>S/P/U</th>
        <th>Purchase Table<br>S/B/P/U</th>
        <th>Needs Sync?</th>
        <th>Action</th>
      </tr>\n";

$total_users = 0;
$users_needing_sync = 0;

while ($user = mysqli_fetch_assoc($users_result)) {
    $total_users++;
    $username = $user['username'];
    
    // Get purchase tickets data
    $purchase_query = "SELECT ticket_type, SUM(remaining_tickets) as total_remaining 
                       FROM purchasetickets 
                       WHERE username = ? 
                       AND remaining_tickets > 0 
                       AND expiration_date > NOW()
                       GROUP BY ticket_type";
    $stmt = mysqli_prepare($conn, $purchase_query);
    mysqli_stmt_bind_param($stmt, 's', $username);
    mysqli_stmt_execute($stmt);
    $purchase_result = mysqli_stmt_get_result($stmt);
    
    $purchase_data = [
        'starter' => 0,
        'business' => 0,
        'premium' => 0,
        'ultimate' => 0
    ];
    
    while ($row = mysqli_fetch_assoc($purchase_result)) {
        $purchase_data[strtolower($row['ticket_type'])] = (int)$row['total_remaining'];
    }
    mysqli_stmt_close($stmt);
    
    // Calculate expected user table values
    $expected_starter = $purchase_data['starter'];
    $expected_premium = $purchase_data['business'] + $purchase_data['premium'];
    $expected_ultimate = $purchase_data['ultimate'];
    
    // Check if sync is needed
    $needs_sync = (
        $user['starter_tickets'] != $expected_starter ||
        $user['premium_tickets'] != $expected_premium ||
        $user['ultimate_tickets'] != $expected_ultimate
    );
    
    if ($needs_sync) {
        $users_needing_sync++;
    }
    
    $row_color = $needs_sync ? 'background-color: #ffe6e6;' : '';
    
    echo "<tr style='$row_color'>\n";
    echo "<td>" . htmlspecialchars($username) . "</td>\n";
    echo "<td>{$user['starter_tickets']}/{$user['premium_tickets']}/{$user['ultimate_tickets']}</td>\n";
    echo "<td>{$purchase_data['starter']}/{$purchase_data['business']}/{$purchase_data['premium']}/{$purchase_data['ultimate']}</td>\n";
    echo "<td>" . ($needs_sync ? '❌ YES' : '✅ NO') . "</td>\n";
    echo "<td>";
    if ($needs_sync) {
        echo "<a href='?sync_user=" . urlencode($username) . "' style='color: blue;'>Sync Now</a>";
    } else {
        echo "No action needed";
    }
    echo "</td>\n";
    echo "</tr>\n";
}

echo "</table>\n";

echo "<h3>Summary:</h3>\n";
echo "Total Users: $total_users<br>\n";
echo "Users Needing Sync: $users_needing_sync<br>\n";

// Handle sync request
if (isset($_GET['sync_user'])) {
    $sync_username = $_GET['sync_user'];
    echo "<h3>Syncing user: " . htmlspecialchars($sync_username) . "</h3>\n";
    
    if (syncUserTableTickets($sync_username)) {
        echo "✅ Sync completed successfully!<br>\n";
        echo "<a href='?'>Refresh to see updated data</a><br>\n";
    } else {
        echo "❌ Sync failed!<br>\n";
    }
}

if ($users_needing_sync > 0) {
    echo "<br><a href='?sync_all=1' style='background-color: #007bff; color: white; padding: 10px; text-decoration: none; border-radius: 5px;'>Sync All Users</a>\n";
}

// Handle sync all request
if (isset($_GET['sync_all'])) {
    echo "<h3>Syncing All Users...</h3>\n";
    
    $users_result2 = mysqli_query($conn, "SELECT username FROM user WHERE username IS NOT NULL AND username != ''");
    $synced = 0;
    $total = 0;
    
    while ($user = mysqli_fetch_assoc($users_result2)) {
        $total++;
        if (syncUserTableTickets($user['username'])) {
            $synced++;
        }
    }
    
    echo "✅ Synced $synced out of $total users successfully!<br>\n";
    echo "<a href='?'>Refresh to see updated data</a><br>\n";
}

echo "<br><br><a href='front-end/my-ticket.php'>Go to My Tickets</a> | <a href='merlion/sync-user-tickets.php'>Admin Sync Tool</a>\n";
?>
