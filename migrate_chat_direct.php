<?php
/**
 * Database Migration Script for Direct Chat Feature
 * This script adds the user_id column to chat_messages table and populates existing data
 */

include('functions/server.php');

echo "<h2>Direct Chat Migration Script</h2>\n";

// Check if user_id column exists
$check_column_sql = "SHOW COLUMNS FROM chat_messages LIKE 'user_id'";
$result = mysqli_query($conn, $check_column_sql);

if (mysqli_num_rows($result) == 0) {
    echo "<p>Adding user_id column to chat_messages table...</p>\n";
    
    // Add user_id column
    $add_column_sql = "ALTER TABLE chat_messages ADD COLUMN user_id INT(11) NULL AFTER ticket_id";
    if (mysqli_query($conn, $add_column_sql)) {
        echo "<p style='color: green;'>✓ user_id column added successfully!</p>\n";
        
        // Populate user_id for existing records
        echo "<p>Populating user_id for existing chat messages...</p>\n";
        $populate_sql = "UPDATE chat_messages cm 
                        JOIN support_tickets st ON cm.ticket_id = st.id 
                        SET cm.user_id = st.user_id 
                        WHERE cm.user_id IS NULL";
        
        if (mysqli_query($conn, $populate_sql)) {
            $affected_rows = mysqli_affected_rows($conn);
            echo "<p style='color: green;'>✓ Updated $affected_rows existing chat messages with user_id!</p>\n";
        } else {
            echo "<p style='color: red;'>✗ Error populating user_id: " . mysqli_error($conn) . "</p>\n";
        }
        
    } else {
        echo "<p style='color: red;'>✗ Error adding user_id column: " . mysqli_error($conn) . "</p>\n";
    }
} else {
    echo "<p style='color: blue;'>ℹ user_id column already exists in chat_messages table.</p>\n";
}

// Add indexes for better performance
echo "<p>Adding database indexes for better performance...</p>\n";

$add_user_index_sql = "ALTER TABLE chat_messages ADD INDEX idx_user_id (user_id)";
$add_ticket_index_sql = "ALTER TABLE chat_messages ADD INDEX idx_ticket_id (ticket_id)";

// Try to add indexes (ignore errors if they already exist)
@mysqli_query($conn, $add_user_index_sql);
@mysqli_query($conn, $add_ticket_index_sql);

echo "<p style='color: green;'>✓ Database indexes added/verified!</p>\n";

// Make ticket_id nullable for direct chat
echo "<p>Making ticket_id nullable for direct chat support...</p>\n";
$modify_ticket_id_sql = "ALTER TABLE chat_messages MODIFY COLUMN ticket_id INT(11) NULL";
if (mysqli_query($conn, $modify_ticket_id_sql)) {
    echo "<p style='color: green;'>✓ ticket_id column is now nullable!</p>\n";
} else {
    echo "<p style='color: orange;'>⚠ ticket_id column modification: " . mysqli_error($conn) . "</p>\n";
}

echo "<h3 style='color: green;'>Migration completed successfully!</h3>\n";
echo "<p><strong>Live Chat Features:</strong></p>\n";
echo "<ul>\n";
echo "<li>✓ Pure live chat - no tickets required</li>\n";
echo "<li>✓ Direct communication between users and admins</li>\n";
echo "<li>✓ Real-time messaging without ticket management overhead</li>\n";
echo "<li>✓ Simple and clean chat interface</li>\n";
echo "</ul>\n";

echo "<p><a href='front-end/chat-support.php'>Test User Chat Interface</a> | <a href='merlion/admin-chat.php'>Test Admin Chat Interface</a></p>\n";
?>
