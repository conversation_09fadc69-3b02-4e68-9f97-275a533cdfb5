<?php
// Centralized API Key Configuration
// Last updated: 2025-06-25 09:47:21 by superadmin

// Customer API Configuration (Appika Customer API)
define('CUSTOMER_API_ENDPOINT', 'https://dev-api-pooh-sgsg.appika.com');
define('CUSTOMER_API_PATH', '/contact/customers');
define('CUSTOMER_API_KEY', 'b8705d62234b8e06b237207cb39d7c187874673d19136f7517c8973e1ab35906');

// GraphQL Ticket API Configuration
define('GRAPHQL_API_ENDPOINT', 'https://dev-sgsg-tktapi.appika.com/graphql');
define('GRAPHQL_API_KEY', 'b8705d62234b8e06b237207cb39d7c187874673d19136f7517c8973e1ab35906');

// Function to get Customer API configuration
function getCustomerApiConfig() {
    return [
        'endpoint' => CUSTOMER_API_ENDPOINT,
        'path' => CUSTOMER_API_PATH,
        'key' => CUSTOMER_API_KEY
    ];
}

// Function to get GraphQL API configuration
function getGraphqlApiConfig() {
    return [
        'endpoint' => GRAPHQL_API_ENDPOINT,
        'key' => GRAPHQL_API_KEY
    ];
}
?>