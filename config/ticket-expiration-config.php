<?php
/**
 * Ticket Expiration Configuration
 * This file contains all settings related to ticket expiration system
 */

// Default ticket expiration settings
$TICKET_EXPIRATION_CONFIG = [
    // Ticket lifetime in months (12 months = 1 year)
    'ticket_lifetime_months' => 12,
    'ticket_lifetime_unit' => 'months',

    // Enable/disable automatic expiration cleanup
    'auto_cleanup_enabled' => false, // TEMPORARILY DISABLED FOR DEBUGGING

    // How often to run cleanup (in days)
    'cleanup_frequency_days' => 7,
    'cleanup_frequency_unit' => 'days',

    // Warning period before expiration (in days)
    'warning_period_days' => 30,
    'warning_period_unit' => 'days',

    // Enable/disable user notifications
    'user_notifications_enabled' => true,

    // Admin email for expiration reports
    'admin_notification_email' => '<EMAIL>',

    // Last cleanup timestamp
    'last_cleanup_timestamp' => null,

    // System settings
    'system_enabled' => true,
    'debug_mode' => false
];

/**
 * Get ticket expiration configuration
 * @param bool $force_refresh Force refresh from database
 * @return array Configuration array
 */
function getTicketExpirationConfig($force_refresh = false) {
    global $conn;

    // Always start with defaults
    $config = [
        'ticket_lifetime_months' => 12,
        'ticket_lifetime_unit' => 'months',
        'auto_cleanup_enabled' => false, // TEMPORARILY DISABLED FOR DEBUGGING
        'cleanup_frequency_days' => 7,
        'cleanup_frequency_unit' => 'days',
        'warning_period_days' => 30,
        'warning_period_unit' => 'days',
        'user_notifications_enabled' => true,
        'admin_notification_email' => '<EMAIL>',
        'last_cleanup_timestamp' => null,
        'system_enabled' => true,
        'debug_mode' => false
    ];

    // Try to load from database
    if (!$conn) {
        include_once('../functions/server.php');
    }

    if ($conn) {
        $query = "SELECT config_key, config_value FROM ticket_expiration_settings";
        $result = mysqli_query($conn, $query);

        if ($result && mysqli_num_rows($result) > 0) {
            while ($row = mysqli_fetch_assoc($result)) {
                $key = $row['config_key'];
                $value = $row['config_value'];

                // Convert numeric values
                if (is_numeric($value)) {
                    $value = (int)$value;
                } elseif ($value === 'true') {
                    $value = true;
                } elseif ($value === 'false') {
                    $value = false;
                } elseif ($value === 'null') {
                    $value = null;
                }

                $config[$key] = $value;
            }
        }
    }

    return $config;
}

/**
 * Update ticket expiration configuration
 * @param string $key Configuration key
 * @param mixed $value Configuration value
 * @return bool Success status
 */
function updateTicketExpirationConfig($key, $value) {
    global $conn;
    if (!$conn) {
        include_once('../functions/server.php');
    }
    
    // Convert value to string for storage
    if (is_bool($value)) {
        $value = $value ? 'true' : 'false';
    } elseif (is_null($value)) {
        $value = 'null';
    } else {
        $value = (string)$value;
    }
    
    $query = "INSERT INTO ticket_expiration_settings (config_key, config_value, updated_at) 
              VALUES (?, ?, NOW()) 
              ON DUPLICATE KEY UPDATE 
              config_value = VALUES(config_value), 
              updated_at = NOW()";
    
    $stmt = mysqli_prepare($conn, $query);
    mysqli_stmt_bind_param($stmt, 'ss', $key, $value);
    $result = mysqli_stmt_execute($stmt);
    mysqli_stmt_close($stmt);
    
    return $result;
}

/**
 * Get ticket expiration date
 * @param string $purchase_date Purchase date
 * @return string Expiration date
 */
function getTicketExpirationDate($purchase_date) {
    $config = getTicketExpirationConfig();
    $lifetime_value = $config['ticket_lifetime_months']; // This field name is misleading - it can be any unit
    $lifetime_unit = $config['ticket_lifetime_unit'];

    // Calculate expiration based on the configured unit
    switch($lifetime_unit) {
        case 'minutes':
            $expiration_date = date('Y-m-d H:i:s', strtotime($purchase_date . " +{$lifetime_value} minutes"));
            break;
        case 'hours':
            $expiration_date = date('Y-m-d H:i:s', strtotime($purchase_date . " +{$lifetime_value} hours"));
            break;
        case 'days':
            $expiration_date = date('Y-m-d H:i:s', strtotime($purchase_date . " +{$lifetime_value} days"));
            break;
        case 'months':
        default:
            $expiration_date = date('Y-m-d H:i:s', strtotime($purchase_date . " +{$lifetime_value} months"));
            break;
    }

    return $expiration_date;
}

/**
 * Check if tickets are expired
 * @param string $purchase_date Purchase date
 * @return bool True if expired
 */
function areTicketsExpired($purchase_date) {
    $expiration_date = getTicketExpirationDate($purchase_date);
    return strtotime($expiration_date) < time();
}

/**
 * Get days until expiration
 * @param string $purchase_date Purchase date
 * @return int Days until expiration (negative if already expired)
 */
function getDaysUntilExpiration($purchase_date) {
    $expiration_date = getTicketExpirationDate($purchase_date);
    $days = (strtotime($expiration_date) - time()) / (24 * 60 * 60);
    return (int)$days;
}

/**
 * Initialize ticket expiration system
 * Creates necessary database tables and default configuration
 */
function initializeTicketExpirationSystem() {
    global $conn;
    if (!$conn) {
        include_once('../functions/server.php');
    }
    
    // Create configuration table
    $create_config_table = "CREATE TABLE IF NOT EXISTS ticket_expiration_settings (
        id INT AUTO_INCREMENT PRIMARY KEY,
        config_key VARCHAR(100) UNIQUE NOT NULL,
        config_value TEXT NOT NULL,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )";
    mysqli_query($conn, $create_config_table);
    
    // Create expiration log table
    $create_log_table = "CREATE TABLE IF NOT EXISTS ticket_expiration_log (
        id INT AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(100) NOT NULL,
        ticket_type VARCHAR(50) NOT NULL,
        expired_quantity INT NOT NULL,
        purchase_date DATETIME NOT NULL,
        expiration_date DATETIME NOT NULL,
        cleanup_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        transaction_id VARCHAR(100),
        INDEX idx_username (username),
        INDEX idx_cleanup_date (cleanup_date)
    )";
    mysqli_query($conn, $create_log_table);
    
    // Skip adding id column - use existing table structure

    // Check if expiration_date column exists before adding
    $check_column = "SHOW COLUMNS FROM purchasetickets LIKE 'expiration_date'";
    $result = mysqli_query($conn, $check_column);

    if (mysqli_num_rows($result) == 0) {
        // Column doesn't exist, add it
        $add_expiration_column = "ALTER TABLE purchasetickets
                                 ADD COLUMN expiration_date DATETIME NULL AFTER purchase_time";
        mysqli_query($conn, $add_expiration_column);
    }

    // Check if expired_at column exists before adding
    $check_expired_at = "SHOW COLUMNS FROM purchasetickets LIKE 'expired_at'";
    $result2 = mysqli_query($conn, $check_expired_at);

    if (mysqli_num_rows($result2) == 0) {
        // Column doesn't exist, add it
        $add_expired_at_column = "ALTER TABLE purchasetickets
                                 ADD COLUMN expired_at TIMESTAMP NULL AFTER expiration_date";
        mysqli_query($conn, $add_expired_at_column);
    }
    
    // Update existing records with expiration dates
    $update_existing = "UPDATE purchasetickets 
                       SET expiration_date = DATE_ADD(purchase_time, INTERVAL 12 MONTH) 
                       WHERE expiration_date IS NULL";
    mysqli_query($conn, $update_existing);
    
    // Insert default configuration ONLY if keys don't exist
    global $TICKET_EXPIRATION_CONFIG;
    foreach ($TICKET_EXPIRATION_CONFIG as $key => $value) {
        if ($key !== 'last_cleanup_timestamp') { // Skip timestamp for initial setup
            // Check if key already exists
            $check_query = "SELECT config_value FROM ticket_expiration_settings WHERE config_key = ?";
            $check_stmt = mysqli_prepare($conn, $check_query);
            mysqli_stmt_bind_param($check_stmt, 's', $key);
            mysqli_stmt_execute($check_stmt);
            $check_result = mysqli_stmt_get_result($check_stmt);

            // Only insert if key doesn't exist
            if (mysqli_num_rows($check_result) == 0) {
                updateTicketExpirationConfig($key, $value);
            }
            mysqli_stmt_close($check_stmt);
        }
    }
    
    return true;
}
?>
