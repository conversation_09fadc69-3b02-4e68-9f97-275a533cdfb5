<?php
/**
 * Test if Core API Examples Work
 * Check if the existing core API examples work with the same authentication
 */

// Auto-detect environment for file paths
$is_localhost = (strpos($_SERVER['HTTP_HOST'], 'localhost') !== false || strpos($_SERVER['HTTP_HOST'], '127.0.0.1') !== false);
$file_base_path = $is_localhost ? '/helloit' : '';

require_once $_SERVER['DOCUMENT_ROOT'] . $file_base_path . '/vendor/autoload.php';
require_once $_SERVER['DOCUMENT_ROOT'] . $file_base_path . '/config/api-config.php';

echo "<h1>Test Core API Examples</h1>";
echo "<p>Testing if the existing core API examples work with current authentication...</p>";

// Get API configuration
$apiConfig = getCustomerApiConfig();
$apiEndpoint = $apiConfig['endpoint'];
$apiPath = $apiConfig['path'];
$apiKey = $apiConfig['key'];

echo "<h2>API Configuration</h2>";
echo "Endpoint: " . $apiEndpoint . "<br>";
echo "Path: " . $apiPath . "<br>";
echo "Key: " . substr($apiKey, 0, 10) . "...<br>";

// Test 1: Exact same method as core_api_example.php
echo "<h2>1. Test Core API Example Method</h2>";
try {
    // Create client exactly like core_api_example.php
    $client = new \GuzzleHttp\Client([
        'base_uri' => $apiEndpoint,
        'timeout' => 30,
        'http_errors' => false,
    ]);
    
    // Send request exactly like core_api_example.php
    $response = $client->request('GET', $apiPath, [
        'headers' => [
            'X-api-key' => "{$apiKey}",
            'Accept' => 'application/json',
        ],
    ]);
    
    $statusCode = $response->getStatusCode();
    $body = $response->getBody()->getContents();
    
    echo "Status Code: " . $statusCode . "<br>";
    
    if ($statusCode == 200) {
        echo "✅ Core API method works!<br>";
        $data = json_decode($body, true);
        if (isset($data['items'])) {
            echo "Found " . count($data['items']) . " customers<br>";
        }
    } elseif ($statusCode == 401) {
        echo "❌ Authentication failed (401)<br>";
        echo "Response: " . htmlspecialchars($body) . "<br>";
    } else {
        echo "⚠️ Unexpected status: " . $statusCode . "<br>";
        echo "Response: " . htmlspecialchars(substr($body, 0, 200)) . "<br>";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "<br>";
}

// Test 2: Test with query parameters (like core_api_example.php alternative)
echo "<h2>2. Test with Query Parameters</h2>";
try {
    $response = $client->request('GET', $apiPath, [
        'headers' => [
            'X-api-key' => "{$apiKey}",
            'Accept' => 'application/json',
        ],
        'query' => [
            'limit' => 10,
            'offset' => 0,
        ],
    ]);
    
    $statusCode = $response->getStatusCode();
    $body = $response->getBody()->getContents();
    
    echo "Status Code: " . $statusCode . "<br>";
    
    if ($statusCode == 200) {
        echo "✅ Query parameters method works!<br>";
    } elseif ($statusCode == 401) {
        echo "❌ Authentication failed (401)<br>";
    } else {
        echo "⚠️ Status: " . $statusCode . "<br>";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "<br>";
}

// Test 3: Check if core API files exist and work
echo "<h2>3. Core API Files Check</h2>";
$core_files = [
    'core_api_example.php' => '/api/core_api_example.php',
    'core_api_post_example.php' => '/api/core_api_post_example.php'
];

foreach ($core_files as $name => $path) {
    $full_path = $_SERVER['DOCUMENT_ROOT'] . $file_base_path . $path;
    if (file_exists($full_path)) {
        echo "✅ $name exists<br>";
        echo "&nbsp;&nbsp;<a href='$path' target='_blank'>Test $name</a><br>";
    } else {
        echo "❌ $name not found<br>";
    }
}

// Test 4: Test different endpoints
echo "<h2>4. Test Different Endpoints</h2>";
$endpoints_to_test = [
    'customers' => '/contact/customers',
    'contacts' => '/contacts',
    'contact/customers' => '/contact/customers'
];

foreach ($endpoints_to_test as $name => $endpoint) {
    try {
        $response = $client->request('GET', $endpoint, [
            'headers' => [
                'X-api-key' => "{$apiKey}",
                'Accept' => 'application/json',
            ],
            'query' => ['limit' => 1]
        ]);
        
        $statusCode = $response->getStatusCode();
        echo "$name ($endpoint): ";
        
        if ($statusCode == 200) {
            echo "✅ Works (200)<br>";
        } elseif ($statusCode == 401) {
            echo "❌ Unauthorized (401)<br>";
        } elseif ($statusCode == 404) {
            echo "⚠️ Not Found (404)<br>";
        } else {
            echo "⚠️ Status $statusCode<br>";
        }
        
    } catch (Exception $e) {
        echo "$name ($endpoint): ❌ Error: " . $e->getMessage() . "<br>";
    }
}

// Test 5: API Key validation
echo "<h2>5. API Key Validation</h2>";
echo "Current API Key: " . $apiKey . "<br>";
echo "Key Length: " . strlen($apiKey) . " characters<br>";

// Test with a known invalid key
try {
    $response = $client->request('GET', $apiPath, [
        'headers' => [
            'X-api-key' => "invalid_key_test",
            'Accept' => 'application/json',
        ],
        'query' => ['limit' => 1]
    ]);
    
    echo "Invalid key test status: " . $response->getStatusCode() . "<br>";
    
} catch (Exception $e) {
    echo "Invalid key test error: " . $e->getMessage() . "<br>";
}

echo "<h2>Recommendations</h2>";
echo "<ol>";
echo "<li>If core API examples work but debug script doesn't, compare the exact differences</li>";
echo "<li>Check if the API key has expired or been revoked</li>";
echo "<li>Test the core API examples directly in browser</li>";
echo "<li>Contact Appika support if authentication continues to fail</li>";
echo "</ol>";

echo "<h2>Next Steps</h2>";
echo "<p>1. Click the links above to test core API examples directly</p>";
echo "<p>2. If they work, we can copy their exact authentication method</p>";
echo "<p>3. If they don't work, the API key might be the issue</p>";
?>
