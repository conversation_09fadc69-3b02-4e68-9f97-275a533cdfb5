<?php
/**
 * GraphQL Countries Example - Using Postman's free GraphQL API for testing
 * 
 * This example demonstrates how to use GraphQL with a free API before testing
 * with the Appika ticket API. This helps verify that your GraphQL setup works.
 */

// Include Composer autoloader
require_once __DIR__ . '/../vendor/autoload.php';

// Countries GraphQL API Configuration (Free API from Postman)
$graphqlEndpoint = 'https://countries.trevorblades.com/';

// Create a Guzzle HTTP client
$client = new \GuzzleHttp\Client([
    'base_uri' => $graphqlEndpoint,
    'timeout' => 30,
    'http_errors' => false,
]);

// Function to display results in a readable format
function displayResults($title, $data) {
    echo "<h2>{$title}</h2>";
    echo "<pre>";
    print_r($data);
    echo "</pre>";
}

// Function to make GraphQL requests
function makeGraphQLRequest($client, $query, $variables = [], $title = '') {
    try {
        // Display request for debugging
        echo "<h3>GraphQL Query (Debug):</h3>";
        echo "<pre>{$query}</pre>";

        if (!empty($variables)) {
            echo "<h3>Variables (Debug):</h3>";
            echo "<pre>";
            print_r($variables);
            echo "</pre>";
        }

        // Prepare GraphQL request body
        $requestBody = [
            'query' => $query
        ];
        
        if (!empty($variables)) {
            $requestBody['variables'] = $variables;
        }

        // Send the request (no auth needed for this free API)
        $response = $client->request('POST', '', [
            'headers' => [
                'Content-Type' => 'application/json',
                'Accept' => 'application/json',
            ],
            'json' => $requestBody
        ]);

        // Get status code
        $statusCode = $response->getStatusCode();
        echo "<p>Status Code: {$statusCode}</p>";

        // Get response body
        $body = $response->getBody()->getContents();
        $data = json_decode($body, true);
        
        displayResults($title . ' Response:', $data);

        return [
            'status' => $statusCode,
            'data' => $data
        ];
    } catch (\Exception $e) {
        echo "<h2>Error Occurred</h2>";
        echo "<p>Error Message: " . $e->getMessage() . "</p>";
        return [
            'status' => 500,
            'error' => $e->getMessage()
        ];
    }
}

// Process form submission
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        $action = $_POST['action'];

        switch ($action) {
            case 'get_countries':
                // Get all countries
                $query = '
                query {
                  countries {
                    code
                    name
                    emoji
                    currency
                    languages {
                      code
                      name
                    }
                  }
                }';

                $result = makeGraphQLRequest($client, $query, [], 'Get Countries');

                if ($result['status'] >= 200 && $result['status'] < 300) {
                    $message = 'Countries retrieved successfully!';
                    $messageType = 'success';
                } else {
                    $message = 'Failed to get countries. ' . ($result['error'] ?? '');
                    $messageType = 'danger';
                }
                break;

            case 'get_country':
                // Get specific country
                $countryCode = !empty($_POST['country_code']) ? $_POST['country_code'] : 'TH';

                $query = '
                query GetCountry($code: ID!) {
                  country(code: $code) {
                    code
                    name
                    emoji
                    currency
                    phone
                    capital
                    languages {
                      code
                      name
                    }
                    continent {
                      code
                      name
                    }
                  }
                }';

                $variables = [
                    'code' => $countryCode
                ];

                $result = makeGraphQLRequest($client, $query, $variables, 'Get Country');

                if ($result['status'] >= 200 && $result['status'] < 300) {
                    $message = 'Country details retrieved successfully!';
                    $messageType = 'success';
                } else {
                    $message = 'Failed to get country details. ' . ($result['error'] ?? '');
                    $messageType = 'danger';
                }
                break;

            case 'get_continents':
                // Get all continents
                $query = '
                query {
                  continents {
                    code
                    name
                    countries {
                      code
                      name
                      emoji
                    }
                  }
                }';

                $result = makeGraphQLRequest($client, $query, [], 'Get Continents');

                if ($result['status'] >= 200 && $result['status'] < 300) {
                    $message = 'Continents retrieved successfully!';
                    $messageType = 'success';
                } else {
                    $message = 'Failed to get continents. ' . ($result['error'] ?? '');
                    $messageType = 'danger';
                }
                break;
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GraphQL Countries Example</title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <style>
    .container {
        max-width: 1000px;
        margin: 0 auto;
        padding: 20px;
    }

    pre {
        background-color: #f8f9fa;
        padding: 15px;
        border-radius: 5px;
        overflow-x: auto;
        max-height: 400px;
    }

    .endpoint-info {
        background-color: #e7f3ff;
        padding: 10px;
        border-radius: 5px;
        margin-bottom: 20px;
    }
    </style>
</head>

<body>
    <div class="container">
        <h1 class="mb-4">GraphQL Countries Example</h1>
        
        <!-- Endpoint Info -->
        <div class="endpoint-info">
            <h5>Free GraphQL API for Testing:</h5>
            <p><strong>Endpoint:</strong> <?php echo $graphqlEndpoint; ?></p>
            <p><strong>Purpose:</strong> Test GraphQL functionality before using Appika ticket API</p>
        </div>

        <?php if (!empty($message)): ?>
        <div class="alert alert-<?php echo $messageType; ?>" role="alert">
            <?php echo $message; ?>
        </div>
        <?php endif; ?>

        <div class="row">
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5>Get All Countries</h5>
                    </div>
                    <div class="card-body">
                        <form method="post" action="">
                            <input type="hidden" name="action" value="get_countries">
                            <button type="submit" class="btn btn-primary">Get Countries</button>
                        </form>
                    </div>
                </div>
            </div>

            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5>Get Specific Country</h5>
                    </div>
                    <div class="card-body">
                        <form method="post" action="">
                            <input type="hidden" name="action" value="get_country">
                            <div class="form-group">
                                <label for="country_code">Country Code</label>
                                <input type="text" class="form-control" id="country_code" name="country_code" 
                                    value="TH" placeholder="TH, US, JP, etc.">
                            </div>
                            <button type="submit" class="btn btn-info">Get Country</button>
                        </form>
                    </div>
                </div>
            </div>

            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5>Get All Continents</h5>
                    </div>
                    <div class="card-body">
                        <form method="post" action="">
                            <input type="hidden" name="action" value="get_continents">
                            <button type="submit" class="btn btn-success">Get Continents</button>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <div class="mt-4">
            <p><strong>Note:</strong> This example uses a free GraphQL API to test your GraphQL setup. 
            Once this works, you can be confident that your GraphQL implementation is correct for testing 
            with the Appika ticket API.</p>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.5.2/dist/js/bootstrap.bundle.min.js"></script>
</body>

</html>
