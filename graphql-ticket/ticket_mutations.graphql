# GraphQL Ticket Mutations

# Create a new ticket
mutation CreateTicket(
  $ticket_no: String!
  $contact_id: Int!
  $agent_id: Int
  $req_email: String!
  $subject: String!
  $priority: String!
  $type_name: String!
  $type: Int!
  $status: String!
) {
  createTicket(
    ticket_no: $ticket_no
    contact_id: $contact_id
    agent_id: $agent_id
    req_email: $req_email
    subject: $subject
    priority: $priority
    type_name: $type_name
    type: $type
    status: $status
  ) {
    id
    ticket_no
    contact_id
    agent_id
    req_email
    subject
    type
    type_name
    priority
    status
    created
    updated
  }
}

# Update an existing ticket
mutation UpdateTicket(
  $id: Int!
  $subject: String
  $priority: String
  $status: String
  $agent_id: Int
) {
  updateTicket(
    id: $id
    subject: $subject
    priority: $priority
    status: $status
    agent_id: $agent_id
  ) {
    id
    ticket_no
    contact_id
    agent_id
    req_email
    subject
    type
    type_name
    priority
    status
    created
    updated
  }
}

# Delete a ticket
mutation DeleteTicket($id: Int!) {
  deleteTicket(id: $id) {
    success
    message
  }
}

# Assign ticket to agent
mutation AssignTicket($ticketId: Int!, $agentId: Int!) {
  assignTicket(ticketId: $ticketId, agentId: $agentId) {
    id
    ticket_no
    contact_id
    agent_id
    status
    updated
  }
}

# Change ticket status
mutation ChangeTicketStatus($id: Int!, $status: String!) {
  changeTicketStatus(id: $id, status: $status) {
    id
    ticket_no
    status
    updated
  }
}

# Change ticket priority
mutation ChangeTicketPriority($id: Int!, $priority: String!) {
  changeTicketPriority(id: $id, priority: $priority) {
    id
    ticket_no
    priority
    updated
  }
}

# Add comment to ticket
mutation AddTicketComment($ticketId: Int!, $comment: String!, $authorId: Int!) {
  addTicketComment(
    ticketId: $ticketId
    comment: $comment
    authorId: $authorId
  ) {
    id
    ticket_id
    comment
    author_id
    created
  }
}

# Close ticket
mutation CloseTicket($id: Int!, $reason: String) {
  closeTicket(id: $id, reason: $reason) {
    id
    ticket_no
    status
    updated
  }
}

# Reopen ticket
mutation ReopenTicket($id: Int!, $reason: String) {
  reopenTicket(id: $id, reason: $reason) {
    id
    ticket_no
    status
    updated
  }
}

# Variables examples for the mutations above:

# For CreateTicket:
# {
#   "ticket_no": "HT-001234",
#   "contact_id": 1,
#   "agent_id": null,
#   "req_email": "<EMAIL>",
#   "subject": "Test Ticket",
#   "priority": "medium",
#   "type_name": "starter",
#   "type": 1,
#   "status": "open"
# }

# For UpdateTicket:
# {
#   "id": 1,
#   "subject": "Updated Test Ticket",
#   "priority": "high",
#   "status": "in_progress",
#   "agent_id": 2
# }

# For DeleteTicket:
# {
#   "id": "1"
# }

# For AssignTicket:
# {
#   "ticketId": "1",
#   "agentId": "5"
# }

# For ChangeTicketStatus:
# {
#   "id": "1",
#   "status": "IN_PROGRESS"
# }

# For ChangeTicketPriority:
# {
#   "id": "1",
#   "priority": "HIGH"
# }

# For AddTicketComment:
# {
#   "ticketId": "1",
#   "comment": "This is a comment on the ticket",
#   "authorId": "2"
# }

# For CloseTicket:
# {
#   "id": "1",
#   "reason": "Issue resolved"
# }

# For ReopenTicket:
# {
#   "id": "1",
#   "reason": "Issue not fully resolved"
# }
