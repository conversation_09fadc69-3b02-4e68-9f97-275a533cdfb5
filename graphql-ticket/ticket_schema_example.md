# Appika Ticket GraphQL Schema Example

This document outlines the expected GraphQL schema structure for the Appika Ticket API based on your requirements and database structure.

## Types

### Ticket Type
```graphql
type Ticket {
  id: ID!
  ticket_no: String!
  req_email: String!
  subject: String!
  priority: TicketPriority!
  type_name: TicketType!
  status: TicketStatus!
  created: DateTime!
  updated: DateTime!
  description: String
  severity: String
  problem_type: String
  contact_id: ID
  agent_id: ID
  assigned_to: ID
}
```

### Enums
```graphql
enum TicketStatus {
  OPEN
  IN_PROGRESS
  RESOLVED
  CLOSED
}

enum TicketPriority {
  LOW
  MEDIUM
  HIGH
  URGENT
}

enum TicketType {
  STARTER
  PREMIUM
  ULTIMATE
}
```

### Input Types
```graphql
input CreateTicketInput {
  ticket_no: String!
  req_email: String!
  subject: String!
  priority: TicketPriority!
  type_name: TicketType!
  status: TicketStatus!
  description: String
  severity: String
  problem_type: String
  contact_id: ID
  agent_id: ID
}

input UpdateTicketInput {
  subject: String
  priority: TicketPriority
  status: TicketStatus
  description: String
  severity: String
  problem_type: String
  agent_id: ID
}

input TicketSearchInput {
  subject: String
  status: TicketStatus
  priority: TicketPriority
  type_name: TicketType
  req_email: String
  contact_id: ID
  agent_id: ID
}
```

### Pagination Types
```graphql
type TicketConnection {
  tickets: [Ticket!]!
  totalCount: Int!
  hasNextPage: Boolean!
  hasPreviousPage: Boolean!
}
```

## Queries

```graphql
type Query {
  # Get a specific ticket by ID
  getTicket(id: ID!): Ticket
  
  # List tickets with pagination
  listTickets(limit: Int, offset: Int, status: TicketStatus): TicketConnection!
  
  # Search tickets by criteria
  searchTickets(input: TicketSearchInput!): TicketConnection!
  
  # Get tickets by status
  getTicketsByStatus(status: TicketStatus!): [Ticket!]!
  
  # Get tickets by priority
  getTicketsByPriority(priority: TicketPriority!): [Ticket!]!
  
  # Get tickets by type
  getTicketsByType(type: TicketType!): [Ticket!]!
  
  # Get user's tickets
  getUserTickets(userId: ID!, limit: Int, offset: Int): TicketConnection!
}
```

## Mutations

```graphql
type Mutation {
  # Create a new ticket
  createTicket(input: CreateTicketInput!): Ticket!
  
  # Update an existing ticket
  updateTicket(id: ID!, input: UpdateTicketInput!): Ticket!
  
  # Delete a ticket
  deleteTicket(id: ID!): DeleteResult!
  
  # Assign ticket to agent
  assignTicket(ticketId: ID!, agentId: ID!): Ticket!
  
  # Change ticket status
  changeTicketStatus(id: ID!, status: TicketStatus!): Ticket!
  
  # Change ticket priority
  changeTicketPriority(id: ID!, priority: TicketPriority!): Ticket!
  
  # Close ticket
  closeTicket(id: ID!, reason: String): Ticket!
  
  # Reopen ticket
  reopenTicket(id: ID!, reason: String): Ticket!
}
```

### Additional Types
```graphql
type DeleteResult {
  success: Boolean!
  message: String
}

scalar DateTime
```

## Example Usage

### Your Specific Query (from request)
```graphql
query {
  ticket1: getTicket(id: 1) {
    id
    ticket_no
    req_email
    subject
    priority
    type_name
    status
    created
    updated
  }
  ticket3: getTicket(id: 3) {
    id
    ticket_no
    req_email
    subject
    priority
    type_name
    status
    created
    updated
  }
}
```

### Create Ticket Example
```graphql
mutation {
  createTicket(input: {
    ticket_no: "TKT-001234"
    req_email: "<EMAIL>"
    subject: "Test Ticket"
    priority: MEDIUM
    type_name: STARTER
    status: OPEN
    description: "This is a test ticket"
  }) {
    id
    ticket_no
    subject
    status
    created
  }
}
```

### Update Ticket Example
```graphql
mutation {
  updateTicket(id: "1", input: {
    subject: "Updated Test Ticket"
    priority: HIGH
    status: IN_PROGRESS
  }) {
    id
    ticket_no
    subject
    priority
    status
    updated
  }
}
```

## Database Field Mapping

| GraphQL Field | Database Table | Database Field | Notes |
|---------------|----------------|----------------|-------|
| `id` | `support_tickets` | `id` | Primary key |
| `ticket_no` | Generated | - | Format: TKT-XXXXXX |
| `req_email` | `user` | `email` | Via JOIN on user_id |
| `subject` | `support_tickets` | `subject` | Direct mapping |
| `priority` | `support_tickets` | `priority` | Direct mapping |
| `type_name` | `support_tickets` | `ticket_type` | Direct mapping |
| `status` | `support_tickets` | `status` | Direct mapping |
| `created` | `support_tickets` | `created_at` | Direct mapping |
| `updated` | `support_tickets` | `updated_at` | Direct mapping |
| `description` | `support_tickets` | `description` | Direct mapping |
| `severity` | `support_tickets` | `severity` | Direct mapping |
| `problem_type` | `support_tickets` | `problem_type` | Direct mapping |
| `contact_id` | `support_tickets` | `user_id` | Maps to user_id |
| `agent_id` | `support_tickets` | `assigned_admin_id` | Direct mapping |
