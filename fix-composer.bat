@echo off
echo === Composer Dependencies Fix Script ===
echo This script will help fix <PERSON><PERSON><PERSON> and other Composer dependencies
echo.

REM Check if we're in the right directory
if not exist "composer.json" (
    echo ❌ Error: composer.json not found in current directory
    echo Please run this script from your project root directory
    pause
    exit /b 1
)

echo ✅ Found composer.json

REM Backup existing vendor directory
if exist "vendor" (
    set backup_name=vendor_backup_%date:~-4,4%%date:~-10,2%%date:~-7,2%_%time:~0,2%%time:~3,2%%time:~6,2%
    set backup_name=%backup_name: =0%
    echo 📦 Backing up existing vendor directory to %backup_name%
    move vendor "%backup_name%"
    echo ✅ Backup created: %backup_name%
)

REM Check if composer is available
where composer >nul 2>nul
if %errorlevel% == 0 (
    echo ✅ Composer is available
    set COMPOSER_CMD=composer
) else if exist "composer.phar" (
    echo ✅ Found composer.phar
    set COMPOSER_CMD=php composer.phar
) else (
    echo ❌ Composer not found. Downloading composer.phar...
    
    REM Download composer
    php -r "copy('https://getcomposer.org/installer', 'composer-setup.php');"
    php composer-setup.php
    php -r "unlink('composer-setup.php');"
    
    if exist "composer.phar" (
        echo ✅ Downloaded composer.phar
        set COMPOSER_CMD=php composer.phar
    ) else (
        echo ❌ Failed to download composer
        pause
        exit /b 1
    )
)

REM Clear composer cache
echo 🧹 Clearing Composer cache...
%COMPOSER_CMD% clear-cache

REM Install dependencies
echo 📦 Installing dependencies...
%COMPOSER_CMD% install --no-dev --optimize-autoloader

REM Check if installation was successful
if exist "vendor\autoload.php" (
    echo ✅ Dependencies installed successfully!
    
    REM Test if Guzzle is available
    echo 🧪 Testing Guzzle availability...
    php -r "require_once 'vendor/autoload.php'; if (class_exists('GuzzleHttp\Client')) { echo '✅ GuzzleHttp\Client is available\n'; try { $client = new GuzzleHttp\Client(['timeout' => 5]); echo '✅ Guzzle client can be instantiated\n'; } catch (Exception $e) { echo '❌ Error creating Guzzle client: ' . $e->getMessage() . '\n'; } } else { echo '❌ GuzzleHttp\Client is not available\n'; }"
) else (
    echo ❌ Installation failed - vendor\autoload.php not found
    pause
    exit /b 1
)

echo.
echo === Installation Complete ===
echo You can now test your application:
echo 1. Visit test-composer-installation.php to verify installation
echo 2. Visit debug-appika-detailed.php to test Appika integration
echo 3. Try creating users in admin-users.php
echo.
echo If you still have issues, check:
echo - PHP extensions: curl, json, mbstring, openssl
echo - File permissions on vendor directory
echo - PHP memory_limit and max_execution_time
echo.
pause
