<?php
/**
 * Simple Appika API Test Script
 * Tests basic connectivity and authentication with Appika API
 */

require_once 'config/api-config.php';
require_once 'vendor/autoload.php';

echo "<h1>Appika API Test</h1>";

// Get API configuration
$apiConfig = getCustomerApiConfig();
echo "<h2>API Configuration</h2>";
echo "Endpoint: " . $apiConfig['endpoint'] . "<br>";
echo "Path: " . $apiConfig['path'] . "<br>";
echo "Key: " . substr($apiConfig['key'], 0, 10) . "...<br>";

// Test 1: Basic connectivity
echo "<h2>Test 1: Basic Connectivity</h2>";
try {
    $client = new \GuzzleHttp\Client([
        'base_uri' => $apiConfig['endpoint'],
        'timeout' => 30,
        'http_errors' => false,
    ]);
    
    // Test GET request to list customers
    $response = $client->request('GET', $apiConfig['path'], [
        'headers' => [
            'X-api-key' => $apiConfig['key'],
            'Accept' => 'application/json',
        ],
        'query' => ['limit' => 5] // Limit to 5 records
    ]);
    
    $statusCode = $response->getStatusCode();
    $body = $response->getBody()->getContents();
    
    echo "Status Code: $statusCode<br>";
    echo "Response Headers: <pre>" . print_r($response->getHeaders(), true) . "</pre>";
    
    if ($statusCode >= 200 && $statusCode < 300) {
        echo "✅ GET request successful<br>";
        $data = json_decode($body, true);
        if ($data) {
            echo "✅ Response is valid JSON<br>";
            echo "Response structure: <pre>" . print_r($data, true) . "</pre>";
        } else {
            echo "❌ Response is not valid JSON<br>";
            echo "Raw response: <pre>" . htmlspecialchars($body) . "</pre>";
        }
    } else {
        echo "❌ GET request failed<br>";
        echo "Error response: <pre>" . htmlspecialchars($body) . "</pre>";
    }
    
} catch (Exception $e) {
    echo "❌ Exception: " . $e->getMessage() . "<br>";
}

// Test 2: Create a test customer
echo "<h2>Test 2: Create Test Customer</h2>";
try {
    $testCustomerData = [
        'no' => 'TEST' . time(),
        'name' => 'Test Customer ' . date('Y-m-d H:i:s'),
        'entity_type' => '1',
        'grp_id' => '10',
        'ofc_id' => '511',
        'assign2' => '1',
        'creator' => '1',
        'start_date' => date('Y-m-d'),
        'status' => 'a'
    ];
    
    echo "Creating test customer with data:<br>";
    echo "<pre>" . print_r($testCustomerData, true) . "</pre>";
    
    $response = $client->request('POST', $apiConfig['path'], [
        'headers' => [
            'X-api-key' => $apiConfig['key'],
            'Accept' => 'application/json',
            'Content-Type' => 'application/json',
        ],
        'json' => $testCustomerData
    ]);
    
    $statusCode = $response->getStatusCode();
    $body = $response->getBody()->getContents();
    
    echo "Status Code: $statusCode<br>";
    
    if ($statusCode >= 200 && $statusCode < 300) {
        echo "✅ Customer creation successful<br>";
        $data = json_decode($body, true);
        if ($data && isset($data[0]['id'])) {
            echo "✅ Customer created with ID: " . $data[0]['id'] . "<br>";
            echo "Customer number: " . $data[0]['no'] . "<br>";
            echo "Full response: <pre>" . print_r($data, true) . "</pre>";
            
            // Test 3: Delete the test customer
            echo "<h2>Test 3: Delete Test Customer</h2>";
            $customerId = $data[0]['id'];
            
            $deleteResponse = $client->request('DELETE', $apiConfig['path'] . '/' . $customerId, [
                'headers' => [
                    'X-api-key' => $apiConfig['key'],
                    'Accept' => 'application/json',
                ]
            ]);
            
            $deleteStatusCode = $deleteResponse->getStatusCode();
            echo "Delete Status Code: $deleteStatusCode<br>";
            
            if ($deleteStatusCode >= 200 && $deleteStatusCode < 300) {
                echo "✅ Test customer deleted successfully<br>";
            } else {
                echo "❌ Failed to delete test customer<br>";
                echo "Delete response: <pre>" . htmlspecialchars($deleteResponse->getBody()->getContents()) . "</pre>";
            }
        } else {
            echo "❌ Unexpected response format<br>";
            echo "Response: <pre>" . htmlspecialchars($body) . "</pre>";
        }
    } else {
        echo "❌ Customer creation failed<br>";
        echo "Error response: <pre>" . htmlspecialchars($body) . "</pre>";
    }
    
} catch (Exception $e) {
    echo "❌ Exception during customer creation: " . $e->getMessage() . "<br>";
}

// Test 4: Check server environment
echo "<h2>Test 4: Server Environment</h2>";
echo "PHP Version: " . PHP_VERSION . "<br>";
echo "HTTP_HOST: " . ($_SERVER['HTTP_HOST'] ?? 'Not set') . "<br>";
echo "SERVER_NAME: " . ($_SERVER['SERVER_NAME'] ?? 'Not set') . "<br>";
echo "DOCUMENT_ROOT: " . ($_SERVER['DOCUMENT_ROOT'] ?? 'Not set') . "<br>";

// Check if cURL is available
if (function_exists('curl_version')) {
    $curlInfo = curl_version();
    echo "✅ cURL is available - Version: " . $curlInfo['version'] . "<br>";
} else {
    echo "❌ cURL is not available<br>";
}

// Check if Guzzle is available
if (class_exists('\GuzzleHttp\Client')) {
    echo "✅ Guzzle HTTP Client is available<br>";
} else {
    echo "❌ Guzzle HTTP Client is not available<br>";
}

echo "<h2>Test Complete</h2>";
echo "<p>Review the results above to identify any API connectivity or authentication issues.</p>";
?> 