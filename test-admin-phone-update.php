<?php
/**
 * Test Admin Phone Update
 * Debug why phone number isn't getting updated in Appika
 */

// Auto-detect environment for file paths
$is_localhost = (strpos($_SERVER['HTTP_HOST'], 'localhost') !== false || strpos($_SERVER['HTTP_HOST'], '127.0.0.1') !== false);
$file_base_path = $is_localhost ? '/helloit' : '';

require_once $_SERVER['DOCUMENT_ROOT'] . $file_base_path . '/vendor/autoload.php';
require_once $_SERVER['DOCUMENT_ROOT'] . $file_base_path . '/functions/server.php';
require_once $_SERVER['DOCUMENT_ROOT'] . $file_base_path . '/config/api-config.php';

echo "<h1>Test Admin Phone Update Debug</h1>";
echo "<p>Testing why phone number isn't getting updated in Appika...</p>";

// Get API configuration
$apiConfig = getCustomerApiConfig();
$apiEndpoint = $apiConfig['endpoint'];
$apiPath = $apiConfig['path'];
$apiKey = $apiConfig['key'];

echo "<h2>1. API Configuration</h2>";
echo "Endpoint: " . $apiEndpoint . "<br>";
echo "Path: " . $apiPath . "<br>";
echo "Key: " . substr($apiKey, 0, 10) . "...<br>";

// Test user (replace with actual user ID)
$test_user_id = 212; // HC212
echo "<h2>2. Test User: HC$test_user_id</h2>";

// Get user from database
$user_query = "SELECT * FROM user WHERE id = $test_user_id";
$user_result = mysqli_query($conn, $user_query);

if ($user_result && mysqli_num_rows($user_result) > 0) {
    $user = mysqli_fetch_assoc($user_result);
    echo "✅ User found in database<br>";
    echo "Username: " . $user['username'] . "<br>";
    echo "Email: " . $user['email'] . "<br>";
    echo "Appika ID: " . ($user['appika_id'] ?? 'N/A') . "<br>";
    echo "Local Phone: " . ($user['tell'] ?? 'N/A') . "<br>";
} else {
    echo "❌ User not found in database<br>";
    exit;
}

// Test 3: Check if user exists in Appika
echo "<h2>3. Check User in Appika</h2>";

if (!empty($user['appika_id'])) {
    try {
        $client = new \GuzzleHttp\Client([
            'base_uri' => $apiEndpoint,
            'timeout' => 10,
            'http_errors' => false,
        ]);
        
        // Search for customer by appika_id
        $searchResult = $client->request('GET', $apiPath, [
            'headers' => [
                'X-api-key' => $apiKey,
                'Accept' => 'application/json',
            ],
            'query' => [
                'no' => $user['appika_id']
            ]
        ]);
        
        $searchData = json_decode($searchResult->getBody()->getContents(), true);
        
        if ($searchResult->getStatusCode() == 200 && isset($searchData['items']) && !empty($searchData['items'])) {
            $customerData = $searchData['items'][0];
            echo "✅ User found in Appika<br>";
            echo "Appika Customer ID: " . $customerData['id'] . "<br>";
            echo "Appika Name: " . ($customerData['name'] ?? 'N/A') . "<br>";
            echo "Appika Phone: " . ($customerData['tel_work'] ?? 'N/A') . "<br>";
            
            $customerDbId = $customerData['id'];
            
            // Test 4: Check locations
            echo "<h2>4. Check User Locations in Appika</h2>";
            
            $locationsResult = $client->request('GET', $apiPath . '/' . $customerDbId . '/locations', [
                'headers' => [
                    'X-api-key' => $apiKey,
                    'Accept' => 'application/json',
                ]
            ]);
            
            $locationsData = json_decode($locationsResult->getBody()->getContents(), true);
            
            if ($locationsResult->getStatusCode() == 200 && isset($locationsData['items'])) {
                echo "✅ Locations found: " . count($locationsData['items']) . "<br>";
                
                foreach ($locationsData['items'] as $location) {
                    echo "Location ID: " . $location['id'] . "<br>";
                    echo "Location Name: " . ($location['loc_name'] ?? 'N/A') . "<br>";
                    echo "Location Phone: " . ($location['tel_work'] ?? 'N/A') . "<br>";
                    echo "Is Primary: " . ($location['is_primary_loc'] ?? 'N/A') . "<br>";
                    echo "Address: " . ($location['add1'] ?? 'N/A') . "<br>";
                    echo "<hr>";
                }
            } else {
                echo "❌ No locations found or error: " . $locationsResult->getStatusCode() . "<br>";
            }
            
            // Test 5: Test Phone Update
            echo "<h2>5. Test Phone Update</h2>";
            
            $test_phone = "0555555555"; // Test phone number
            echo "Testing phone update with: $test_phone<br>";
            
            // Test updating customer record
            $updateData = [
                'no' => $user['appika_id'],
                'name' => $customerData['name'],
                'entity_type' => '1',
                'grp_id' => '10',
                'ofc_id' => '511',
                'assign2' => '1',
                'creator' => '1',
                'start_date' => date('Y-m-d', strtotime($customerData['start_date'])),
                'status' => 'a',
                'tel_work' => $test_phone
            ];
            
            echo "<h3>5a. Customer Update Data:</h3>";
            echo "<pre>" . json_encode($updateData, JSON_PRETTY_PRINT) . "</pre>";
            
            $updatePath = $apiPath . '/' . $customerDbId;
            echo "Update URL: " . $apiEndpoint . $updatePath . "<br>";
            
            // Don't actually update, just test the request structure
            echo "⚠️ Skipping actual update to avoid changing real data<br>";
            echo "To test actual update, uncomment the code below<br>";
            
            /*
            $updateResult = $client->request('PUT', $updatePath, [
                'headers' => [
                    'X-api-key' => $apiKey,
                    'Accept' => 'application/json',
                    'Content-Type' => 'application/json',
                ],
                'json' => $updateData
            ]);
            
            echo "Update Status: " . $updateResult->getStatusCode() . "<br>";
            echo "Update Response: " . $updateResult->getBody()->getContents() . "<br>";
            */
            
        } else {
            echo "❌ User not found in Appika or error: " . $searchResult->getStatusCode() . "<br>";
            echo "Response: " . $searchResult->getBody()->getContents() . "<br>";
        }
        
    } catch (Exception $e) {
        echo "❌ Error connecting to Appika: " . $e->getMessage() . "<br>";
    }
} else {
    echo "❌ User has no Appika ID<br>";
}

// Test 6: Check Admin Logs
echo "<h2>6. Check Admin Logs</h2>";

$log_file_path = $_SERVER['DOCUMENT_ROOT'] . $file_base_path . '/logs/appika_api_admin.log';
if (file_exists($log_file_path)) {
    echo "✅ Admin log file exists<br>";
    $log_lines = file($log_file_path);
    $recent_lines = array_slice($log_lines, -10); // Last 10 lines
    
    echo "<h3>Recent Admin Log Entries:</h3>";
    echo "<pre style='background: #f5f5f5; padding: 10px; max-height: 200px; overflow-y: auto;'>";
    foreach ($recent_lines as $line) {
        if (stripos($line, 'HC212') !== false || stripos($line, 'phone') !== false || stripos($line, 'tel_work') !== false) {
            echo "<strong>" . htmlspecialchars($line) . "</strong>";
        } else {
            echo htmlspecialchars($line);
        }
    }
    echo "</pre>";
} else {
    echo "❌ Admin log file not found at: $log_file_path<br>";
}

echo "<h2>Possible Issues & Solutions</h2>";
echo "<ol>";
echo "<li><strong>Phone field not in form:</strong> Check if edit_phone field is properly submitted</li>";
echo "<li><strong>Appika API field name:</strong> Maybe tel_work is not the correct field name</li>";
echo "<li><strong>Location overwriting customer:</strong> Location update might be overwriting customer phone</li>";
echo "<li><strong>API validation:</strong> Appika might be rejecting the phone format</li>";
echo "<li><strong>Update order:</strong> Customer update might happen before location update</li>";
echo "</ol>";

echo "<h2>Next Steps</h2>";
echo "<ol>";
echo "<li>Try updating a user's phone in admin-user-detail.php</li>";
echo "<li>Check the admin log file immediately after the update</li>";
echo "<li>Look for any error messages in the log</li>";
echo "<li>Check if the phone appears in the 'Sending to Appika' log entry</li>";
echo "</ol>";
?>
