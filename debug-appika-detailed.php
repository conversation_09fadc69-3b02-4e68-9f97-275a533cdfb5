<?php
/**
 * Detailed Appika Debug Script
 * This will check every step of the user creation process
 */

echo "<h1>Detailed Appika Debug</h1>";
echo "<p>Checking every aspect of the Appika integration...</p>";

// Auto-detect environment for file paths (same as working front-end files)
$is_localhost = (strpos($_SERVER['HTTP_HOST'], 'localhost') !== false || strpos($_SERVER['HTTP_HOST'], '127.0.0.1') !== false);
$file_base_path = $is_localhost ? '/helloit' : '';

// Include necessary files using absolute paths
require_once $_SERVER['DOCUMENT_ROOT'] . $file_base_path . '/vendor/autoload.php'; // Include Composer autoloader for Guzzle
require_once $_SERVER['DOCUMENT_ROOT'] . $file_base_path . '/functions/server.php';
require_once $_SERVER['DOCUMENT_ROOT'] . $file_base_path . '/config/api-config.php';

// Test 1: Check if Guzzle is actually working
echo "<h2>1. Guzzle HTTP Client Test</h2>";
try {
    if (class_exists('GuzzleHttp\Client')) {
        echo "✅ GuzzleHttp\Client class exists<br>";
        
        // Test creating a client
        $client = new GuzzleHttp\Client(['timeout' => 10]);
        echo "✅ GuzzleHttp\Client can be instantiated<br>";
    } else {
        echo "❌ GuzzleHttp\Client class does not exist<br>";
        echo "This means the vendor/autoload.php is not loading Guzzle properly<br>";
    }
} catch (Exception $e) {
    echo "❌ Error creating Guzzle client: " . $e->getMessage() . "<br>";
}

// Test 2: Check API Configuration
echo "<h2>2. API Configuration Test</h2>";
try {
    $apiConfig = getCustomerApiConfig();
    echo "✅ API config loaded successfully<br>";
    echo "Endpoint: " . $apiConfig['endpoint'] . "<br>";
    echo "Path: " . $apiConfig['path'] . "<br>";
    echo "Key: " . substr($apiConfig['key'], 0, 10) . "...<br>";
} catch (Exception $e) {
    echo "❌ Error loading API config: " . $e->getMessage() . "<br>";
}

// Test 3: Test actual API call
echo "<h2>3. Test API Call to Appika</h2>";
try {
    if (class_exists('GuzzleHttp\Client')) {
        $client = new GuzzleHttp\Client([
            'base_uri' => $apiConfig['endpoint'],
            'timeout' => 30,
            'http_errors' => false,
        ]);
        
        // Test GET request to list customers
        $response = $client->request('GET', $apiConfig['path'], [
            'headers' => [
                'Authorization' => 'Bearer ' . $apiConfig['key'],
                'Content-Type' => 'application/json',
            ]
        ]);
        
        echo "✅ API call completed<br>";
        echo "Status Code: " . $response->getStatusCode() . "<br>";
        echo "Response Body: <pre>" . substr($response->getBody(), 0, 500) . "</pre><br>";
        
        if ($response->getStatusCode() == 200) {
            echo "✅ API connection successful!<br>";
        } else {
            echo "⚠️ API returned status " . $response->getStatusCode() . "<br>";
        }
    } else {
        echo "❌ Cannot test API call - Guzzle not available<br>";
    }
} catch (Exception $e) {
    echo "❌ API call failed: " . $e->getMessage() . "<br>";
}

// Test 4: Check recent user creation attempts
echo "<h2>4. Recent User Creation Check</h2>";
$recent_users_sql = "SELECT id, username, email, appika_id, appika_customer_id, created_at 
                     FROM user 
                     WHERE created_at >= DATE_SUB(NOW(), INTERVAL 1 DAY)
                     ORDER BY created_at DESC 
                     LIMIT 5";
$recent_users_result = mysqli_query($conn, $recent_users_sql);

if ($recent_users_result) {
    echo "✅ Recent users query successful<br>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>ID</th><th>Username</th><th>Email</th><th>Appika ID</th><th>Appika Customer ID</th><th>Created</th></tr>";
    
    while ($user = mysqli_fetch_assoc($recent_users_result)) {
        $appika_id = $user['appika_id'] ?: 'NULL';
        $appika_customer_id = $user['appika_customer_id'] ?: 'NULL';
        $status = ($user['appika_id'] && $user['appika_customer_id']) ? '✅' : '❌';
        
        echo "<tr>";
        echo "<td>{$user['id']}</td>";
        echo "<td>{$user['username']}</td>";
        echo "<td>{$user['email']}</td>";
        echo "<td>$appika_id</td>";
        echo "<td>$appika_customer_id</td>";
        echo "<td>{$user['created_at']}</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "❌ Error querying recent users: " . mysqli_error($conn) . "<br>";
}

// Test 5: Check error logs
echo "<h2>5. Recent Error Logs</h2>";
$log_files = ['logs/appika_debug.log', 'logs/appika_api.log', 'logs/error.log'];
foreach ($log_files as $log_file) {
    if (file_exists($log_file)) {
        echo "📄 $log_file exists<br>";
        $lines = file($log_file);
        $recent_lines = array_slice($lines, -10); // Last 10 lines
        echo "<pre style='background: #f5f5f5; padding: 10px; max-height: 200px; overflow-y: auto;'>";
        foreach ($recent_lines as $line) {
            echo htmlspecialchars($line);
        }
        echo "</pre>";
    } else {
        echo "❌ $log_file does not exist<br>";
    }
}

// Test 6: Check if createCustomerMinimal function exists
echo "<h2>6. Function Availability Check</h2>";
if (function_exists('createCustomerMinimal')) {
    echo "✅ createCustomerMinimal function exists<br>";
} else {
    echo "❌ createCustomerMinimal function does not exist<br>";
}

if (function_exists('createCustomerInAppika')) {
    echo "✅ createCustomerInAppika function exists<br>";
} else {
    echo "❌ createCustomerInAppika function does not exist<br>";
}

// Test 7: Check file permissions
echo "<h2>7. File Permissions Check</h2>";
$files_to_check = [
    'vendor/autoload.php',
    'functions/create-customer-minimal.php',
    'functions/customer-data-service.php',
    'config/api-config.php',
    'logs/'
];

foreach ($files_to_check as $file) {
    if (file_exists($file)) {
        $perms = fileperms($file);
        $perms_octal = substr(sprintf('%o', $perms), -4);
        echo "✅ $file exists (permissions: $perms_octal)<br>";
    } else {
        echo "❌ $file does not exist<br>";
    }
}

echo "<h2>8. PHP Environment Info</h2>";
echo "PHP Version: " . PHP_VERSION . "<br>";
echo "Server: " . ($_SERVER['SERVER_SOFTWARE'] ?? 'Unknown') . "<br>";
echo "Document Root: " . ($_SERVER['DOCUMENT_ROOT'] ?? 'Unknown') . "<br>";
echo "Current Directory: " . getcwd() . "<br>";
echo "Include Path: " . get_include_path() . "<br>";

// Test 9: Check if vendor directory is complete
echo "<h2>9. Vendor Directory Check</h2>";
$vendor_files = [
    'vendor/autoload.php',
    'vendor/composer/autoload_real.php',
    'vendor/guzzlehttp/guzzle/src/Client.php'
];

foreach ($vendor_files as $file) {
    if (file_exists($file)) {
        echo "✅ $file exists<br>";
    } else {
        echo "❌ $file missing<br>";
    }
}

echo "<h2>Summary</h2>";
echo "<p>This diagnostic shows exactly what's working and what's not. Look for any ❌ marks above to identify the issue.</p>";
?> 