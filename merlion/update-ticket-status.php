<?php
// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Include database connection
include('../functions/server.php');

// Include centralized Appika sync functions
require_once '../functions/appika_sync.php';

// Set headers for JSON response
header('Content-Type: application/json');
header('Cache-Control: no-cache, must-revalidate');

// Log the request for debugging
$log_file = fopen("ticket_status_update.log", "a");
fwrite($log_file, "Request received at " . date('Y-m-d H:i:s') . "\n");
fwrite($log_file, "POST data: " . print_r($_POST, true) . "\n");

// Check if admin is logged in
session_start();
if (!isset($_SESSION['admin_username'])) {
    echo json_encode(['status' => 'error', 'message' => 'Not logged in']);
    exit();
}

$admin_id = $_SESSION['admin_id'];
$admin_username = $_SESSION['admin_username'];
fwrite($log_file, "Admin: $admin_username (ID: $admin_id)\n");

// Check if it's a GET request with redirect parameter
$redirect_mode = false;
if ($_SERVER['REQUEST_METHOD'] === 'GET' && isset($_GET['redirect'])) {
    fwrite($log_file, "GET request with redirect parameter detected\n");
    $ticket_id = isset($_GET['ticket_id']) ? intval($_GET['ticket_id']) : null;
    $status = isset($_GET['status']) ? $_GET['status'] : null;
    $redirect_mode = true;
}
// Check if it's a POST request
else if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    fwrite($log_file, "POST request detected\n");
    $ticket_id = isset($_POST['ticket_id']) ? intval($_POST['ticket_id']) : null;
    $status = isset($_POST['status']) ? $_POST['status'] : null;
}
// Invalid request method
else {
    fwrite($log_file, "Error: Invalid request method\n\n");
    echo json_encode(['success' => false, 'error' => 'Invalid request method']);
    fclose($log_file);
    exit();
}

fwrite($log_file, "Ticket ID: $ticket_id, Status: $status, Redirect Mode: " . ($redirect_mode ? "Yes" : "No") . "\n");

// Validate data
if (!$ticket_id || !$status) {
    fwrite($log_file, "Error: Missing required data\n\n");
    echo json_encode(['success' => false, 'error' => 'Missing required data']);
    fclose($log_file);
    exit();
}

// Validate status
$allowed_statuses = ['open', 'in_progress', 'resolved', 'closed'];
if (!in_array($status, $allowed_statuses)) {
    fwrite($log_file, "Error: Invalid status\n\n");
    echo json_encode(['success' => false, 'error' => 'Invalid status']);
    fclose($log_file);
    exit();
}

// Check if ticket exists and get current status, user_id, and appika_id
$check_query = "SELECT st.status, st.user_id, st.subject, st.ticket_type, st.appika_id, u.email
                FROM support_tickets st
                JOIN user u ON st.user_id = u.id
                WHERE st.id = ?";
$stmt = mysqli_prepare($conn, $check_query);
mysqli_stmt_bind_param($stmt, 'i', $ticket_id);
mysqli_stmt_execute($stmt);
$result = mysqli_stmt_get_result($stmt);
$ticket_info = mysqli_fetch_assoc($result);

fwrite($log_file, "Checking if ticket exists...\n");
if (!$ticket_info) {
    fwrite($log_file, "Error: Ticket not found\n\n");
    mysqli_stmt_close($stmt);
    echo json_encode(['success' => false, 'error' => 'Ticket not found']);
    fclose($log_file);
    exit();
}

$user_id = $ticket_info['user_id'];
$current_status = $ticket_info['status'];
fwrite($log_file, "Ticket found. User ID: $user_id, Current Status: $current_status\n");

mysqli_stmt_close($stmt);
fwrite($log_file, "Ticket found. Proceeding with update...\n");

// Update ticket status
$update_query = "UPDATE support_tickets SET status = ? WHERE id = ?";
$stmt = mysqli_prepare($conn, $update_query);
mysqli_stmt_bind_param($stmt, 'si', $status, $ticket_id);
$success = mysqli_stmt_execute($stmt);
fwrite($log_file, "Update query executed. Success: " . ($success ? "Yes" : "No") . "\n");
if (!$success) {
    fwrite($log_file, "MySQL Error: " . mysqli_error($conn) . "\n");
}
mysqli_stmt_close($stmt);

if ($success) {
    // Status update logging removed to save database storage
    fwrite($log_file, "Status update successful - logging skipped to save storage\n");

    // Use centralized sync function for Appika API update
    $syncResult = syncTicketToAppika($ticket_id, $status, null, $admin_username);

    // Log the sync operation
    logAppikaSync($ticket_id, 'admin_status_update', $syncResult);

    $appika_update_success = $syncResult['appika_updated'];
    $appika_error = $syncResult['appika_updated'] ? '' : $syncResult['message'];

    fwrite($log_file, "Appika sync result: " . $syncResult['message'] . "\n");

    // Get updated ticket info
    $ticket_query = "SELECT id, status FROM support_tickets WHERE id = ?";
    $stmt = mysqli_prepare($conn, $ticket_query);
    mysqli_stmt_bind_param($stmt, 'i', $ticket_id);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    $ticket_data = mysqli_fetch_assoc($result);
    mysqli_stmt_close($stmt);

    // Handle success response
    if ($redirect_mode) {
        // If in redirect mode, redirect back to the chat page with success message
        $success_message = $appika_update_success
            ? "Ticket status updated to " . ucfirst($status) . " in both local database and Appika API"
            : "Ticket status updated to " . ucfirst($status) . " in local database, but failed to update Appika API: " . $appika_error;

        $redirect_url = "admin-chat.php?user_id=" . (isset($_GET['user_id']) ? intval($_GET['user_id']) : '') .
                        "&ticket_id=$ticket_id&success=1&message=" . urlencode($success_message);
        fwrite($log_file, "Success! Redirecting to: $redirect_url\n\n");
        fclose($log_file);
        header("Location: $redirect_url");
        exit();
    } else {
        // If in AJAX mode, return JSON
        $response = [
            'success' => true,
            'message' => $appika_update_success
                ? 'Ticket status updated successfully in both local database and Appika API'
                : 'Ticket status updated in local database, but failed to update Appika API: ' . $appika_error,
            'ticket' => $ticket_data,
            'appika_success' => $appika_update_success,
            'appika_error' => $appika_error
        ];

        fwrite($log_file, "Success response: " . json_encode($response) . "\n\n");
        echo json_encode($response);
    }
} else {
    // Handle error response
    $error_message = 'Failed to update ticket status: ' . mysqli_error($conn);

    if ($redirect_mode) {
        // If in redirect mode, redirect back with error parameter
        $redirect_url = "admin-chat.php?user_id=" . (isset($_GET['user_id']) ? intval($_GET['user_id']) : '') . "&ticket_id=$ticket_id&error=" . urlencode($error_message);
        fwrite($log_file, "Error! Redirecting to: $redirect_url\n\n");
        fclose($log_file);
        header("Location: $redirect_url");
        exit();
    } else {
        // If in AJAX mode, return JSON error
        $error_response = [
            'success' => false,
            'error' => $error_message
        ];

        fwrite($log_file, "Error response: " . json_encode($error_response) . "\n\n");
        echo json_encode($error_response);
    }
}

fclose($log_file);
?>
