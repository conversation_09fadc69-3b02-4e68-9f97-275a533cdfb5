<?php
session_start();
include('../functions/server.php');

// Check if admin is logged in
if (!isset($_SESSION['admin_username'])) {
    header('location: admin-login');
    exit();
}

$admin_username = $_SESSION['admin_username'];
$admin_id = $_SESSION['admin_id'];
$admin_role = $_SESSION['admin_role'];

// Get search and filter parameters
$search = isset($_GET['search']) ? trim($_GET['search']) : "";
$action_filter = isset($_GET['action']) ? trim($_GET['action']) : "";
$type_filter = isset($_GET['type']) ? trim($_GET['type']) : "";

// Build search and filter conditions
$where_conditions = [];

if (!empty($search)) {
    $search_safe = mysqli_real_escape_string($conn, $search);
    $where_conditions[] = "(tl.id = '$search_safe' OR tl.ticket_id = '$search_safe' OR tl.description LIKE '%$search_safe%' OR u.username LIKE '%$search_safe%' OR DATE(tl.created_at) = '$search_safe')";
}

if (!empty($action_filter)) {
    $action_safe = mysqli_real_escape_string($conn, $action_filter);
    $where_conditions[] = "tl.action = '$action_safe'";
}

if (!empty($type_filter)) {
    $type_safe = mysqli_real_escape_string($conn, $type_filter);
    $where_conditions[] = "tl.ticket_type = '$type_safe'";
}

// Combine conditions
$where_clause = "";
if (!empty($where_conditions)) {
    $where_clause = "WHERE " . implode(" AND ", $where_conditions);
}

// Pagination settings
$items_per_page = 10;
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
if ($page < 1) $page = 1;
$offset = ($page - 1) * $items_per_page;

// Count total logs for pagination
$count_sql = "SELECT COUNT(*) as total
              FROM ticket_logs tl
              LEFT JOIN user u ON tl.user_id = u.id
              $where_clause";
$count_result = mysqli_query($conn, $count_sql);
$total_items = mysqli_fetch_assoc($count_result)['total'];
$total_pages = ceil($total_items / $items_per_page);

// Get logs with pagination
$logs_sql = "SELECT tl.*, u.username, au.username as admin_username
             FROM ticket_logs tl
             LEFT JOIN user u ON tl.user_id = u.id
             LEFT JOIN admin_users au ON tl.performed_by_admin_id = au.id
             $where_clause
             ORDER BY tl.created_at DESC
             LIMIT $items_per_page OFFSET $offset";
$logs_result = mysqli_query($conn, $logs_sql);

// 1. Add delete all logs logic at the top
if (isset($_POST['delete_all_logs']) && $_POST['delete_all_logs'] === '1') {
    $deleted_count = 0;
    $result = mysqli_query($conn, "SELECT COUNT(*) as cnt FROM ticket_logs");
    if ($result) {
        $deleted_count = mysqli_fetch_assoc($result)['cnt'];
    }
    mysqli_query($conn, "TRUNCATE TABLE ticket_logs");
    // Log deletion to a single local file
    $log_dir = __DIR__ . '/../logs';
    if (!is_dir($log_dir)) mkdir($log_dir, 0777, true);
    $log_file = $log_dir . '/ticket_logs_delete.log';
    $log_msg = date('Y-m-d H:i:s') . " - Admin '{$admin_username}' (ID: {$admin_id}) deleted {$deleted_count} logs\n";
    file_put_contents($log_file, $log_msg, FILE_APPEND);
    header('Location: admin-ticket-logs.php?deleted=1');
    exit();
}
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin - Ticket Logs</title>
    <link rel="shortcut icon" href="../image/png/favicon.png" type="image/x-icon">
    <!-- Bootstrap, fonts & icons -->
    <link rel="stylesheet" href="../css/bootstrap.css">
    <link rel="stylesheet" href="../fonts/icon-font/css/style.css">
    <link rel="stylesheet" href="../fonts/typography-font/typo.css">
    <link rel="stylesheet" href="../fonts/fontawesome-5/css/all.css">
    <!-- Vendor stylesheets -->
    <link rel="stylesheet" href="../css/main.css">
    <style>
    body {
        background-color: #f8f9fa;
    }

    .admin-container {
        padding: 20px;
        max-width: 100%;
        overflow-x: hidden;
    }

    .admin-header {
        background-color: #fff;
        padding: 15px 20px;
        border-radius: 5px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
    }

    /* Responsive container */
    @media (max-width: 991px) {
        .admin-container {
            padding: 15px;
        }
    }

    @media (max-width: 767px) {
        .admin-container {
            padding: 10px;
        }
    }

    .admin-header h1 {
        margin: 0;
        font-size: 24px;
        color: #333;
    }

    .admin-user {
        display: flex;
        align-items: center;
    }

    .admin-user span {
        margin-right: 10px;
    }

    .admin-sidebar {
        background-color: #fff;
        border-radius: 5px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        padding: 20px;
        height: calc(100vh - 100px);
        overflow-y: auto;
    }

    .admin-sidebar ul {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .admin-sidebar ul li {
        margin-bottom: 10px;
    }

    .admin-sidebar ul li a {
        display: block;
        padding: 10px 15px;
        color: #333;
        text-decoration: none;
        border-radius: 5px;
        transition: all 0.3s;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .admin-sidebar ul li a:hover,
    .admin-sidebar ul li a.active {
        background-color: #473BF0;
        color: #fff;
    }

    .admin-sidebar ul li a i {
        margin-right: 10px;
        width: 20px;
        text-align: center;
    }

    /* Responsive sidebar */
    @media (max-width: 991px) {
        .admin-sidebar {
            padding: 15px;
        }

        .admin-sidebar ul li a {
            padding: 8px 12px;
            font-size: 14px;
        }
    }

    @media (max-width: 767px) {
        .admin-sidebar {
            height: auto;
            margin-bottom: 20px;
        }

        .admin-sidebar ul li a {
            padding: 8px 10px;
            font-size: 13px;
        }

        .admin-sidebar ul li {
            margin-bottom: 5px;
        }
    }

    .admin-content {
        background-color: #fff;
        border-radius: 5px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        padding: 20px;
        min-height: calc(100vh - 120px);
        overflow-x: hidden;
    }

    .filter-row {
        margin-bottom: 20px;
    }

    /* Responsive content */
    @media (max-width: 991px) {
        .admin-content {
            padding: 15px;
        }

        .filter-row {
            margin-bottom: 15px;
        }
    }

    @media (max-width: 767px) {
        .admin-content {
            height: auto;
            padding: 12px;
        }

        .filter-row {
            margin-bottom: 12px;
        }
    }

    .badge {
        font-size: 16px;
        padding: 6.8px;
    }

    /* Table styles */
    .table {
        width: 100%;
        margin-bottom: 1rem;
    }

    .table th,
    .table td {
        vertical-align: middle;
        padding: 0.75rem;
    }

    .table-responsive {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
        margin-bottom: 15px;
    }

    /* Responsive table */
    @media (max-width: 991px) {
        .table {
            font-size: 14px;
        }

        .table th,
        .table td {
            padding: 0.5rem;
        }

        .badge {
            font-size: 14px;
            padding: 5px;
        }
    }

    @media (max-width: 767px) {
        .table {
            font-size: 13px;
        }

        .table th,
        .table td {
            padding: 0.4rem 0.3rem;
        }

        .badge {
            font-size: 12px;
            padding: 4px;
        }
    }

    @media (max-width: 480px) {
        .table {
            font-size: 12px;
        }

        .table th,
        .table td {
            padding: 0.3rem 0.2rem;
        }
    }

    /* Search box and filter styles */
    .search-box {
        width: 100%;
    }

    .search-box .input-group {
        width: 100%;
        max-width: 450px;
    }

    .search-input {
        border-top-right-radius: 0 !important;
        border-bottom-right-radius: 0 !important;
        height: 38px;
        font-size: 14px;
    }

    .search-button {
        border-top-right-radius: 4px !important;
        border-bottom-right-radius: 4px !important;
        border-top-left-radius: 0 !important;
        border-bottom-left-radius: 0 !important;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0.375rem 0.75rem;
        width: 40px !important;
        min-width: 40px !important;
    }

    .filter-select {
        font-size: 14px;
        height: 40px;
        max-height: 300px;
        overflow-y: auto;
    }

    /* Responsive search and filters */
    @media (max-width: 991px) {
        .search-box .input-group {
            max-width: 100%;
        }

        .filter-select {
            font-size: 13px;
        }
    }

    @media (max-width: 767px) {
        .search-input {
            font-size: 13px;
        }

        .filter-select {
            font-size: 13px;
        }
    }

    @media (max-width: 480px) {
        .search-input::placeholder {
            font-size: 12px;
        }

        .filter-select {
            font-size: 12px;
        }
    }

    /* Pagination styles */
    .pagination-container {
        margin-top: 20px;
    }

    .pagination .page-link {
        color: #473BF0;
        border-color: #dee2e6;
    }

    .pagination .page-item.active .page-link {
        background-color: #473BF0;
        border-color: #473BF0;
    }

    @media (max-width: 767px) {
        .pagination {
            font-size: 14px;
        }

        .pagination .page-link {
            padding: 0.3rem 0.6rem;
        }
    }

    @media (max-width: 480px) {
        .pagination {
            font-size: 12px;
        }

        .pagination .page-link {
            padding: 0.25rem 0.5rem;
        }
    }

    /* Ticket Status badges */
    .badge-open {
        background-color: #4CAF50;
        color: #fff;
    }

    .badge-in_progress {
        background-color: #2196F3;
        color: #fff;
    }

    .badge-resolved {
        background-color: #9C27B0;
        color: #fff;
    }

    .badge-closed {
        background-color: #757575;
        color: #fff;
    }

    /* Ticket Log Status badges */
    .badge-success {
        background-color: #28a745;
        color: #fff;
    }

    .badge-pending {
        background-color: #ffc107;
        color: #212529;
    }

    .badge-fail {
        background-color: #dc3545;
        color: #fff;
    }

    .badge-cancel {
        background-color: #6c757d;
        color: #fff;
    }

    /* Ticket Type badges */
    .badge-starter {
        background-color: #fbbf24;
        color: #fff;
    }

    .badge-premium,
    .badge-business {
        background-color: #01A7E1;
        color: #fff;
    }

    .badge-ultimate {
        background-color: #793BF0;
        color: #fff;
    }

    /* User dropdown styles */
    .user-dropdown {
        position: relative;
        display: inline-block;
    }

    .user-info {
        cursor: pointer;
        display: flex;
        align-items: center;
    }

    .mobile-menu-toggle {
        margin-left: 5px;
        transition: transform 0.3s ease;
        display: none;
    }

    .mobile-menu-toggle.active {
        transform: rotate(180deg);
    }

    .dropdown-content {
        display: block;
    }

    /* Mobile Styles */
    @media (max-width: 767px) {
        .admin-header {
            flex-direction: column;
            align-items: flex-start;
        }

        .admin-header h1 {
            margin-bottom: 10px;
        }

        .admin-user {
            width: 100%;
        }

        .user-dropdown {
            width: 100%;
        }

        .user-info {
            width: 100%;
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
        }

        .mobile-menu-toggle {
            display: inline-block;
        }

        .dropdown-content {
            display: none;
            position: absolute;
            background-color: #f9f9f9;
            width: 100%;
            left: 0;
            box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.2);
            z-index: 1;
            border-radius: 4px;
            margin-top: 5px;
            padding: 8px;
        }

        .dropdown-content .btn {
            width: 100%;
            text-align: center;
        }

        .user-dropdown.active .dropdown-content {
            display: block;
        }
    }
    </style>
</head>

<body>
    <div class="admin-container">
        <div class="admin-header">
            <h1>Ticket Logs</h1>
            <div class="admin-user">
                <div class="user-dropdown">
                    <span class="user-info">
                        Welcome, <?php echo htmlspecialchars($admin_username); ?>
                        (<?php echo htmlspecialchars($admin_role); ?>)
                        <i class="mobile-menu-toggle fas fa-chevron-down"></i>
                    </span>
                    <div class="dropdown-content">
                        <a href="admin-logout" class="btn btn-sm btn-outline-danger">Logout</a>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-2 col-md-3 col-sm-12">
                <?php include('admin-menu.php'); ?>
            </div>

            <div class="col-lg-10 col-md-9 col-sm-12">
                <div class="admin-content">
                    <div class="filter-row">
                        <div class="d-flex flex-wrap align-items-center justify-content-between" style="gap: 10px;">
                            <div class="d-flex flex-wrap align-items-center"
                                style="gap: 10px; flex:1; min-width:320px;">
                                <form method="GET" class="d-flex flex-wrap align-items-center"
                                    style="gap: 10px; flex:1; width:100%;">
                                    <div class="search-box" style="flex:1; min-width:220px;">
                                        <div class="input-group">
                                            <input type="text" name="search" class="form-control search-input"
                                                placeholder="Search by description, username or date"
                                                value="<?php echo htmlspecialchars($search); ?>">
                                            <div class="input-group-append">
                                                <button type="submit" class="btn btn-primary search-button">
                                                    <i class="fas fa-search"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                    <div style="width:150px;">
                                        <select name="action" class="form-control filter-select"
                                            onchange="this.form.submit()">
                                            <option value="">All Actions</option>
                                            <option value="success"
                                                <?php echo $action_filter === 'success' ? 'selected' : ''; ?>>Success
                                            </option>
                                            <option value="pending"
                                                <?php echo $action_filter === 'pending' ? 'selected' : ''; ?>>Pending
                                            </option>
                                            <option value="fail"
                                                <?php echo $action_filter === 'fail' ? 'selected' : ''; ?>>Fail</option>
                                            <option value="cancel"
                                                <?php echo $action_filter === 'cancel' ? 'selected' : ''; ?>>Cancel
                                            </option>
                                        </select>
                                    </div>
                                    <div style="width:150px;">
                                        <select name="type" class="form-control filter-select"
                                            onchange="this.form.submit()">
                                            <option value="">All Types</option>
                                            <option value="starter"
                                                <?php echo $type_filter === 'starter' ? 'selected' : ''; ?>>Starter
                                            </option>
                                            <option value="premium"
                                                <?php echo $type_filter === 'premium' ? 'selected' : ''; ?>>Business
                                            </option>
                                            <option value="ultimate"
                                                <?php echo $type_filter === 'ultimate' ? 'selected' : ''; ?>>Ultimate
                                            </option>
                                        </select>
                                    </div>
                                </form>
                            </div>
                            <?php if ($total_items > 0): ?>
                            <form method="POST" style="margin:0;">
                                <input type="hidden" name="delete_all_logs" value="1">
                                <button type="submit" class="btn btn-danger"
                                    style="height:40px; font-size:14px; padding:0 22px; font-weight:600; margin-left:10px;"
                                    onclick="return confirm('Are you sure you want to delete ALL logs? This cannot be undone.');">Delete
                                    All Logs</button>
                            </form>
                            <?php endif; ?>
                        </div>
                    </div>

                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Ticket ID</th>
                                    <th>User</th>
                                    <th>Action</th>
                                    <th>Type</th>
                                    <th>Amount</th>
                                    <th>Description</th>
                                    <th>Performed By</th>
                                    <th>Date</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (mysqli_num_rows($logs_result) > 0): ?>
                                <?php while ($log = mysqli_fetch_assoc($logs_result)): ?>
                                <tr>
                                    <td><?php echo $log['id']; ?></td>
                                    <td>
                                        <?php if (!empty($log['ticket_id'])): ?>
                                        <a
                                            href="admin-ticket-detail.php?id=<?php echo $log['ticket_id']; ?>"><?php echo $log['ticket_id']; ?></a>
                                        <?php else: ?>
                                        -
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo htmlspecialchars($log['username'] ?? 'N/A'); ?></td>
                                    <td>
                                        <span class="badge badge-<?php echo $log['action']; ?>">
                                            <?php echo ucfirst($log['action']); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <?php if (!empty($log['ticket_type'])): ?>
                                        <?php
                                                    // Display 'Business' for premium tickets
                                                    $ticketType = $log['ticket_type'];
                                                    $badgeClass = $ticketType;
                                                    $displayText = ucfirst($ticketType);

                                                    if (strtolower($ticketType) == 'premium') {
                                                        $displayText = 'Business';
                                                    }
                                                    ?>
                                        <span class="badge badge-<?php echo $badgeClass; ?>">
                                            <?php echo $displayText; ?>
                                        </span>
                                        <?php else: ?>
                                        -
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo $log['amount'] ?? '-'; ?></td>
                                    <td><?php echo htmlspecialchars($log['description']); ?></td>
                                    <td>
                                        <?php
                                                if (!empty($log['performed_by_admin_id'])) {
                                                    echo htmlspecialchars($log['admin_username'] ?? 'Admin #' . $log['performed_by_admin_id']);
                                                } else {
                                                    echo 'System';
                                                }
                                                ?>
                                    </td>
                                    <td><?php echo date('Y-m-d H:i', strtotime($log['created_at'])); ?></td>
                                </tr>
                                <?php endwhile; ?>
                                <?php else: ?>
                                <tr>
                                    <td colspan="9" class="text-center">No logs found</td>
                                </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>

                    <?php if ($total_pages > 1): ?>
                    <div class="pagination-container mt-4">
                        <nav aria-label="Page navigation">
                            <ul class="pagination justify-content-center">
                                <li class="page-item <?php echo ($page <= 1) ? 'disabled' : ''; ?>">
                                    <a class="page-link"
                                        href="?page=<?php echo $page - 1; ?>&search=<?php echo urlencode($search); ?>&action=<?php echo urlencode($action_filter); ?>&type=<?php echo urlencode($type_filter); ?>"
                                        aria-label="Previous">
                                        <span aria-hidden="true">&laquo; Previous</span>
                                    </a>
                                </li>

                                <?php for ($i = 1; $i <= $total_pages; $i++): ?>
                                <li class="page-item <?php echo ($page == $i) ? 'active' : ''; ?>">
                                    <a class="page-link"
                                        href="?page=<?php echo $i; ?>&search=<?php echo urlencode($search); ?>&action=<?php echo urlencode($action_filter); ?>&type=<?php echo urlencode($type_filter); ?>">
                                        <?php echo $i; ?>
                                    </a>
                                </li>
                                <?php endfor; ?>

                                <li class="page-item <?php echo ($page >= $total_pages) ? 'disabled' : ''; ?>">
                                    <a class="page-link"
                                        href="?page=<?php echo $page + 1; ?>&search=<?php echo urlencode($search); ?>&action=<?php echo urlencode($action_filter); ?>&type=<?php echo urlencode($type_filter); ?>"
                                        aria-label="Next">
                                        <span aria-hidden="true">Next &raquo;</span>
                                    </a>
                                </li>
                            </ul>
                        </nav>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="../js/vendor.min.js"></script>
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        const userDropdown = document.querySelector('.user-dropdown');
        const userInfo = document.querySelector('.user-info');
        const menuToggle = document.querySelector('.mobile-menu-toggle');

        // Only apply dropdown functionality on mobile
        if (window.innerWidth <= 767) {
            userInfo.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                userDropdown.classList.toggle('active');
                menuToggle.classList.toggle('active');
            });

            // Also add direct click handler to the toggle icon for better mobile experience
            menuToggle.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                userDropdown.classList.toggle('active');
                menuToggle.classList.toggle('active');
            });

            // Close dropdown when clicking outside
            document.addEventListener('click', function(event) {
                if (!userDropdown.contains(event.target)) {
                    userDropdown.classList.remove('active');
                    menuToggle.classList.remove('active');
                }
            });
        }

        // Handle window resize
        window.addEventListener('resize', function() {
            if (window.innerWidth > 767) {
                userDropdown.classList.remove('active');
                menuToggle.classList.remove('active');
            }
        });
    });
    </script>
</body>

</html>