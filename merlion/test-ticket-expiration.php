<?php
session_start();
include('../functions/server.php');
require_once('../config/ticket-expiration-config.php');
require_once('../functions/ticket-expiration-functions.php');

// Check if admin is logged in
if (!isset($_SESSION['admin_username'])) {
    header('location: admin-login');
    exit();
}

$message = '';
$message_type = '';

// Handle test actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['create_test_data'])) {
        // Create test ticket purchases with different dates
        $test_username = 'HC185'; // Use existing user
        
        // Test data: simulate purchases at different times
        $test_purchases = [
            [
                'date' => '2024-06-22 10:00:00', // 1 year ago (should be expired)
                'type' => 'starter',
                'quantity' => 10
            ],
            [
                'date' => '2024-08-22 15:30:00', // 10 months ago
                'type' => 'ultimate', 
                'quantity' => 5
            ],
            [
                'date' => '2025-06-22 09:15:00', // Recent (should be active)
                'type' => 'starter',
                'quantity' => 5
            ],
            [
                'date' => '2025-06-22 14:45:00', // Recent (should be active)
                'type' => 'ultimate',
                'quantity' => 5
            ]
        ];
        
        foreach ($test_purchases as $purchase) {
            $expiration_date = getTicketExpirationDate($purchase['date']);
            
            $insert_query = "INSERT INTO purchasetickets 
                           (username, ticket_type, package_size, numbers_per_package, dollar_price_per_package, 
                            purchase_time, transactionid, remaining_tickets, expiration_date) 
                           VALUES (?, ?, 'Test Package', ?, 99.99, ?, ?, ?, ?)";
            
            $stmt = mysqli_prepare($conn, $insert_query);
            $transaction_id = 'TEST_' . time() . '_' . rand(1000, 9999);
            mysqli_stmt_bind_param($stmt, 'ssissis', 
                $test_username, 
                $purchase['type'], 
                $purchase['quantity'],
                $purchase['date'],
                $transaction_id,
                $purchase['quantity'],
                $expiration_date
            );
            
            if (!mysqli_stmt_execute($stmt)) {
                $message = 'Failed to create test data: ' . mysqli_stmt_error($stmt);
                $message_type = 'error';
                break;
            }
            mysqli_stmt_close($stmt);
        }
        
        if (empty($message)) {
            $message = 'Test data created successfully!';
            $message_type = 'success';
        }
        
    } elseif (isset($_POST['test_fifo'])) {
        // Test FIFO ticket usage
        $test_username = 'HC185';
        $ticket_type = $_POST['ticket_type'];
        $quantity = (int)$_POST['quantity'];
        
        $success = useTicketsFIFO($test_username, $ticket_type, $quantity);
        
        if ($success) {
            $message = "Successfully used $quantity $ticket_type tickets using FIFO logic!";
            $message_type = 'success';
        } else {
            $message = "Failed to use tickets - insufficient quantity available.";
            $message_type = 'error';
        }
        
    } elseif (isset($_POST['cleanup_expired'])) {
        // Test cleanup function
        $cleanup_summary = removeExpiredTickets();
        
        if (empty($cleanup_summary)) {
            $message = 'No expired tickets found to remove.';
            $message_type = 'info';
        } else {
            $total_removed = array_sum(array_column($cleanup_summary, 'total_expired'));
            $message = "Cleanup test completed! Removed $total_removed expired tickets.";
            $message_type = 'success';
        }
        
    } elseif (isset($_POST['clear_test_data'])) {
        // Clear test data
        $delete_query = "DELETE FROM purchasetickets WHERE transactionid LIKE 'TEST_%'";
        if (mysqli_query($conn, $delete_query)) {
            $affected = mysqli_affected_rows($conn);
            $message = "Cleared $affected test records.";
            $message_type = 'success';
        } else {
            $message = 'Failed to clear test data.';
            $message_type = 'error';
        }
    }
}

// Get current test data
$test_data_query = "SELECT * FROM purchasetickets WHERE username = 'HC185' ORDER BY purchase_time ASC";
$test_data_result = mysqli_query($conn, $test_data_query);

// Get user's current ticket summary
$ticket_summary = getUserTicketSummaryWithExpiration('HC185');
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Ticket Expiration System</title>
    <link rel="shortcut icon" href="../image/png/favicon.png" type="image/x-icon">
    <link rel="stylesheet" href="../css/bootstrap.css">
    <link rel="stylesheet" href="../fonts/fontawesome-5/css/all.css">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .container {
            max-width: 1200px;
            margin: 20px auto;
            padding: 20px;
        }
        .card {
            border: none;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .card-header {
            background: linear-gradient(135deg, #473BF0 0%, #5B4CF0 100%);
            color: white;
            border-radius: 10px 10px 0 0 !important;
            padding: 15px 20px;
        }
        .btn-primary {
            background: linear-gradient(135deg, #473BF0 0%, #5B4CF0 100%);
            border: none;
            border-radius: 6px;
        }
        .btn-danger {
            background: linear-gradient(135deg, #dc3545 0%, #e74c3c 100%);
            border: none;
            border-radius: 6px;
        }
        .btn-warning {
            background: linear-gradient(135deg, #ffc107 0%, #ffb300 100%);
            border: none;
            border-radius: 6px;
            color: #000;
        }
        .btn-success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            border: none;
            border-radius: 6px;
        }
        .table {
            border-radius: 8px;
            overflow: hidden;
        }
        .table thead th {
            background-color: #f8f9fa;
            border: none;
            font-weight: 600;
        }
        .badge-expired {
            background-color: #dc3545;
        }
        .badge-active {
            background-color: #28a745;
        }
        .badge-expiring {
            background-color: #ffc107;
            color: #000;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="fas fa-vial"></i> Test Ticket Expiration System</h1>
            <a href="ticket-expiration-manager.php" class="btn btn-secondary">
                <i class="fas fa-cog"></i> Expiration Manager
            </a>
        </div>

        <!-- Messages -->
        <?php if ($message): ?>
        <div class="alert alert-<?php echo $message_type === 'error' ? 'danger' : $message_type; ?> alert-dismissible fade show">
            <?php echo htmlspecialchars($message); ?>
            <button type="button" class="close" data-dismiss="alert">
                <span>&times;</span>
            </button>
        </div>
        <?php endif; ?>

        <!-- Test Actions -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-play"></i> Test Actions</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <form method="POST" class="mb-2">
                            <button type="submit" name="create_test_data" class="btn btn-primary btn-block" 
                                    onclick="return confirm('This will create test ticket purchases for HC185. Continue?')">
                                <i class="fas fa-database"></i> Create Test Data
                            </button>
                        </form>
                    </div>
                    <div class="col-md-3">
                        <form method="POST" class="mb-2">
                            <div class="input-group">
                                <select name="ticket_type" class="form-control" required>
                                    <option value="starter">Starter</option>
                                    <option value="ultimate">Ultimate</option>
                                </select>
                                <input type="number" name="quantity" class="form-control" value="3" min="1" max="10" required>
                                <div class="input-group-append">
                                    <button type="submit" name="test_fifo" class="btn btn-success">
                                        <i class="fas fa-arrow-right"></i> Use FIFO
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="col-md-3">
                        <form method="POST" class="mb-2">
                            <button type="submit" name="cleanup_expired" class="btn btn-warning btn-block"
                                    onclick="return confirm('This will remove all expired tickets. Continue?')">
                                <i class="fas fa-trash"></i> Test Cleanup
                            </button>
                        </form>
                    </div>
                    <div class="col-md-3">
                        <form method="POST" class="mb-2">
                            <button type="submit" name="clear_test_data" class="btn btn-danger btn-block"
                                    onclick="return confirm('This will delete all test data. Continue?')">
                                <i class="fas fa-eraser"></i> Clear Test Data
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Current Ticket Summary -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-chart-bar"></i> Current Ticket Summary (HC185)</h5>
            </div>
            <div class="card-body">
                <?php if (empty($ticket_summary)): ?>
                    <p class="text-muted">No active tickets found for HC185.</p>
                <?php else: ?>
                    <div class="row">
                        <?php foreach ($ticket_summary as $type => $data): ?>
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-body text-center">
                                    <h3 class="text-primary"><?php echo $data['total_remaining']; ?></h3>
                                    <p class="mb-1"><strong><?php echo ucfirst($type); ?> Tickets</strong></p>
                                    <small class="text-muted">
                                        Earliest expires: <?php echo date('M d, Y', strtotime($data['earliest_expiration'])); ?>
                                    </small>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Test Data Table -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-table"></i> Test Data (HC185 Purchases)</h5>
            </div>
            <div class="card-body">
                <?php if (mysqli_num_rows($test_data_result) > 0): ?>
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Username</th>
                                <th>Ticket Type</th>
                                <th>Purchase Date</th>
                                <th>Expiration Date</th>
                                <th>Remaining</th>
                                <th>Status</th>
                                <th>Transaction ID</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php while ($row = mysqli_fetch_assoc($test_data_result)): 
                                $is_expired = areTicketsExpired($row['purchase_time']);
                                $days_left = getDaysUntilExpiration($row['purchase_time']);
                                
                                if ($is_expired) {
                                    $status = '<span class="badge badge-expired">Expired</span>';
                                } elseif ($days_left <= 30) {
                                    $status = '<span class="badge badge-expiring">Expiring Soon</span>';
                                } else {
                                    $status = '<span class="badge badge-active">Active</span>';
                                }
                            ?>
                            <tr>
                                <td><?php echo $row['username'] ?? 'N/A'; ?></td>
                                <td><?php echo ucfirst($row['ticket_type'] ?? 'Unknown'); ?></td>
                                <td><?php echo date('M d, Y H:i', strtotime($row['purchase_time'] ?? 'now')); ?></td>
                                <td><?php echo isset($row['expiration_date']) ? date('M d, Y H:i', strtotime($row['expiration_date'])) : 'Not Set'; ?></td>
                                <td><?php echo $row['remaining_tickets'] ?? 0; ?></td>
                                <td><?php echo $status; ?></td>
                                <td><?php echo $row['transactionid'] ?? 'N/A'; ?></td>
                            </tr>
                            <?php endwhile; ?>
                        </tbody>
                    </table>
                </div>
                <?php else: ?>
                <p class="text-muted">No test data found. Click "Create Test Data" to generate sample records.</p>
                <?php endif; ?>
            </div>
        </div>

        <!-- How It Works -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-info-circle"></i> How the System Works</h5>
            </div>
            <div class="card-body">
                <h6>FIFO (First In, First Out) Logic:</h6>
                <ul>
                    <li>When a user creates a ticket, the system uses the <strong>oldest</strong> tickets first</li>
                    <li>This ensures tickets don't expire unused while newer tickets are being consumed</li>
                </ul>
                
                <h6>Expiration Process:</h6>
                <ol>
                    <li>Tickets are purchased with a configurable lifetime (default: 12 months)</li>
                    <li>System tracks expiration dates for each purchase batch</li>
                    <li>Automatic cleanup removes expired tickets periodically</li>
                    <li>Users receive warnings before tickets expire</li>
                </ol>
                
                <h6>Test Scenario:</h6>
                <p>The test data simulates this scenario:</p>
                <ul>
                    <li><strong>June 22, 2024:</strong> User bought 10 starter + 5 ultimate (should be expired)</li>
                    <li><strong>August 22, 2024:</strong> User bought 5 ultimate (should be expired)</li>
                    <li><strong>June 22, 2025:</strong> User bought 5 starter + 5 ultimate (should be active)</li>
                    <li>When user creates tickets, system uses oldest available tickets first</li>
                    <li>After cleanup, only the 2025 tickets should remain</li>
                </ul>
            </div>
        </div>
    </div>

    <script src="../js/vendor.min.js"></script>
</body>
</html>
