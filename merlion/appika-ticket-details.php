<?php
// Prevent caching
header('Cache-Control: no-cache, no-store, must-revalidate');
header('Pragma: no-cache');
header('Expires: 0');

session_start();
include('../functions/server.php');

// Check if admin is logged in
if (!isset($_SESSION['admin_username'])) {
    http_response_code(403);
    echo '<div class="alert alert-danger">Access denied. Please log in.</div>';
    exit();
}

// Include GraphQL functions
include('../functions/graphql_functions.php');

// Check if ticket ID is provided (support both GET and POST)
$ticketId = 0;
if (isset($_GET['ticket_id']) && !empty($_GET['ticket_id'])) {
    $ticketId = intval($_GET['ticket_id']);
} elseif (isset($_POST['ticket_id']) && !empty($_POST['ticket_id'])) {
    $ticketId = intval($_POST['ticket_id']);
}

if ($ticketId <= 0) {
    echo '<div class="alert alert-warning">No ticket ID provided.</div>';
    exit();
}

// Get admin info for display
$admin_username = $_SESSION['admin_username'];
$admin_id = $_SESSION['admin_id'];
$admin_role = $_SESSION['admin_role'];

// Define the GraphQL query for fetching ticket details
$query = '
query GetTicket($id: Int!) {
    getTicket(id: $id) {
        id
        ticket_no
        contact_id
        agent_id
        req_email
        subject
        type
        type_name
        priority
        status
        created
        updated
        contacts {
            id
            email
            fname
            status
        }
        ticketMsg {
            id
            message
        }
    }
}';

// Get update messages from session (after redirect)
$updateMessage = $_SESSION['update_message'] ?? '';
$updateMessageType = $_SESSION['update_message_type'] ?? '';

// Clear session messages after getting them
if (!empty($updateMessage)) {
    unset($_SESSION['update_message']);
    unset($_SESSION['update_message_type']);
}

// Handle ticket update

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_ticket'])) {
    $updateSubject = trim($_POST['subject'] ?? '');
    $updatePriority = trim($_POST['priority'] ?? '');
    $updateStatus = trim($_POST['status'] ?? '');

    // Validate required fields
    if (empty($updateSubject)) {
        $_SESSION['update_message'] = 'Subject is required.';
        $_SESSION['update_message_type'] = 'danger';
    } else {
        // Map priority and status to Appika API format (ALL CAPS)
        $priorityMapping = [
            'low' => 'LOW',
            'medium' => 'MEDIUM',
            'high' => 'HIGH',
            'urgent' => 'URGENT'
        ];

        $statusMapping = [
            'open' => 'OPEN',
            'wip' => 'WIP',
            'solved' => 'SOLVED',
            'closed' => 'CLOSED'
        ];

        $apiPriority = $priorityMapping[$updatePriority] ?? 'MEDIUM';
        $apiStatus = $statusMapping[$updateStatus] ?? 'OPEN';

        // Get current ticket data to preserve required fields
        $currentTicketQuery = '
        query GetTicket($id: Int!) {
            getTicket(id: $id) {
                id
                contact_id
                agent_id
                req_email
                type
                type_name
            }
        }';

        $currentTicketResult = makeGraphQLRequest($currentTicketQuery, ['id' => $ticketId]);
        if (!$currentTicketResult['success'] || !isset($currentTicketResult['data']['data']['getTicket'])) {
            $_SESSION['update_message'] = 'Failed to fetch current ticket data for update.';
            $_SESSION['update_message_type'] = 'danger';
        } else {
            $currentTicket = $currentTicketResult['data']['data']['getTicket'];

            // Map ticket type names to numbers (as per user specification)
            $typeMapping = [
                'starter' => 1,
                'business' => 2,
                'premium' => 2,  // Business = Premium = 2
                'ultimate' => 3
            ];

            // Get type from current ticket data
            $ticketType = $currentTicket['type'] ?? 1;
            $ticketTypeName = strtolower($currentTicket['type_name'] ?? 'starter');

            // Use COMPLETE UPDATE mutation with all required fields
            $mutation = '
            mutation UpdateTicket(
                $id: Int!,
                $contact_id: Int,
                $agent_id: Int,
                $subject: String!,
                $type: Int!,
                $type_name: String,
                $priority: String!,
                $status: String!,
                $req_email: String,
                $time_track: String!,
                $reply_msg: String,
                $tags: String
            ) {
                updateTicket(
                    id: $id,
                    contact_id: $contact_id,
                    agent_id: $agent_id,
                    subject: $subject,
                    type: $type,
                    type_name: $type_name,
                    priority: $priority,
                    status: $status,
                    req_email: $req_email,
                    time_track: $time_track,
                    reply_msg: $reply_msg,
                    tags: $tags
                ) {
                    id
                    ticket_no
                    contact_id
                    agent_id
                    req_email
                    subject
                    type
                    type_name
                    priority
                    status
                    created
                    updated
                }
            }';

            // Include all required fields with preserved values
            $variables = [
                'id' => $ticketId,
                'contact_id' => $currentTicket['contact_id'],
                'agent_id' => $currentTicket['agent_id'],
                'subject' => $updateSubject,
                'type' => (int)$ticketType,
                'type_name' => $currentTicket['type_name'],
                'priority' => $apiPriority,
                'status' => $apiStatus,
                'req_email' => $currentTicket['req_email'],
                'time_track' => '00:00:00', // Default time_track as requested
                'reply_msg' => "Ticket updated: Subject, Priority, and Status modified",
                'tags' => ''
            ];

            // Debug logging to see what we're sending
            error_log("COMPLETE UPDATE - Updating ticket $ticketId with all required fields: " . json_encode($variables));
            error_log("Required fields included: type=" . $variables['type'] . ", time_track=" . $variables['time_track']);

            $updateResult = makeGraphQLRequest($mutation, $variables);

            // Enhanced debugging for the response - write to file for easier viewing
            $debugFile = __DIR__ . '/../logs/ticket_update_debug.log';
            $debugInfo = [
                'timestamp' => date('Y-m-d H:i:s'),
                'ticket_id' => $ticketId,
                'variables_sent' => $variables,
                'response' => $updateResult
            ];
            file_put_contents($debugFile, json_encode($debugInfo, JSON_PRETTY_PRINT) . "\n\n", FILE_APPEND);

            error_log("GraphQL Update Response: " . json_encode($updateResult));

            // Check if the update was actually successful despite error message
            // Sometimes the API returns errors but still processes the update
            if ($updateResult['success'] && isset($updateResult['data']['data']['updateTicket'])) {
                $_SESSION['update_message'] = 'Ticket updated successfully in Appika API!';
                $_SESSION['update_message_type'] = 'success';
            } else if ($updateResult['status'] == 200 && isset($updateResult['data']['data']['updateTicket'])) {
                // Sometimes success=false but status=200 and data exists (API quirk)
                $_SESSION['update_message'] = 'Ticket updated successfully in Appika API!';
                $_SESSION['update_message_type'] = 'success';
            } else {
                // Check if it's just an error message but update might have worked
                // Refresh ticket data to see if changes were applied
                $refreshResult = makeGraphQLRequest($query, ['id' => $ticketId]);

                if ($refreshResult['success'] && isset($refreshResult['data']['data']['getTicket'])) {
                    $refreshedTicket = $refreshResult['data']['data']['getTicket'];

                    // Compare if the update actually worked by checking if values changed
                    $subjectChanged = ($refreshedTicket['subject'] ?? '') === $updateSubject;
                    $priorityChanged = (strtoupper($refreshedTicket['priority'] ?? '')) === $apiPriority;
                    $statusChanged = (strtoupper($refreshedTicket['status'] ?? '')) === $apiStatus;

                    if ($subjectChanged && $priorityChanged && $statusChanged) {
                        // Update actually worked despite error message
                        $_SESSION['update_message'] = 'Ticket updated successfully! (Note: API returned error but changes were applied)';
                        $_SESSION['update_message_type'] = 'success';
                        $ticketData = $refreshedTicket; // Use refreshed data

                        error_log("UPDATE SUCCESS DESPITE ERROR - Ticket $ticketId was actually updated successfully");
                    } else {
                        // Update truly failed
                        $_SESSION['update_message'] = 'Failed to update ticket: ' . ($updateResult['error'] ?? 'Unknown error');
                        $_SESSION['update_message_type'] = 'danger';

                        error_log("UPDATE TRULY FAILED - Ticket $ticketId changes not applied: " . json_encode($updateResult));
                    }
                } else {
                    // Can't verify, show original error
                    $_SESSION['update_message'] = 'Failed to update ticket: ' . ($updateResult['error'] ?? 'Unknown error');
                    $_SESSION['update_message_type'] = 'danger';

                    error_log("UPDATE VERIFICATION FAILED - Cannot verify if ticket $ticketId was updated: " . json_encode($updateResult));
                }
            }

            // Always refresh ticket data for display
            $finalRefresh = makeGraphQLRequest($query, ['id' => $ticketId]);
            if ($finalRefresh['success'] && isset($finalRefresh['data']['data']['getTicket'])) {
                $ticketData = $finalRefresh['data']['data']['getTicket'];

                // Debug logging to show final state
                error_log("FINAL TICKET STATE - After update ticket $ticketId:");
                error_log("  - subject: " . ($ticketData['subject'] ?? 'null'));
                error_log("  - priority: " . ($ticketData['priority'] ?? 'null'));
                error_log("  - status: " . ($ticketData['status'] ?? 'null'));
                error_log("  - updated: " . ($ticketData['updated'] ?? 'null'));
            }
        }
    }

    // Redirect to prevent form resubmission (Post-Redirect-Get pattern)
    header("Location: appika-ticket-details.php?ticket_id=" . $ticketId);
    exit();
}

// Function to format ticket type
function formatTicketType($type, $type_name) {
    if (!empty($type_name)) {
        $badge_class = strtolower($type_name);
        return '<span class="badge badge-' . $badge_class . '">' . ucfirst($type_name) . '</span>';
    }

    // Fallback based on type number (as per user specification)
    $types = [1 => 'Starter', 2 => 'Business', 3 => 'Ultimate'];
    $type_name = $types[$type] ?? 'Unknown';
    $badge_class = strtolower($type_name);
    return '<span class="badge badge-' . $badge_class . '">' . $type_name . '</span>';
}

// Function to format priority
function formatPriority($priority) {
    $priority_lower = strtolower($priority);
    $badge_class = $priority_lower;
    
    // Special handling for URGENT priority
    if ($priority_lower === 'urgent') {
        $badge_class = 'urgent';
    }
    
    return '<span class="badge badge-' . $badge_class . '">' . strtoupper($priority) . '</span>';
}

// Function to format status
function formatStatus($status) {
    $status_lower = strtolower($status);
    $badge_class = $status_lower;
    
    // Map WIP to in_progress for styling
    if ($status_lower === 'wip') {
        $badge_class = 'wip';
    }
    
    return '<span class="badge badge-' . $badge_class . '">' . strtoupper($status) . '</span>';
}

// Function to format date
function formatDate($dateString) {
    if (empty($dateString)) {
        return 'N/A';
    }
    
    try {
        $date = new DateTime($dateString);
        return $date->format('d M Y H:i');
    } catch (Exception $e) {
        return htmlspecialchars($dateString);
    }
}

// Fetch ticket details from Appika API
$variables = ['id' => $ticketId];
$result = makeGraphQLRequest($query, $variables);

if (!$result['success']) {
    echo '<div class="alert alert-danger">
        <i class="fas fa-exclamation-triangle"></i>
        Error fetching ticket details: ' . htmlspecialchars($result['error'] ?? 'Unknown error') . '
    </div>';
    exit();
}

$ticketData = $result['data']['data']['getTicket'] ?? null;

// Get the description from the first ticket message
$description = '';
if ($ticketData && isset($ticketData['ticketMsg']) && is_array($ticketData['ticketMsg']) && !empty($ticketData['ticketMsg'])) {
    // Get the first message as the description
    $firstMessage = $ticketData['ticketMsg'][0];
    if (isset($firstMessage['message']) && !empty($firstMessage['message'])) {
        $description = $firstMessage['message'];
    } else {
        $description = '<em>No description found for this ticket.</em>';
    }
} else {
    $description = '<em>No description found for this ticket.</em>';
}

if (!$ticketData) {
    echo '<div class="alert alert-warning">
        <i class="fas fa-search"></i>
        No ticket found with ID: ' . htmlspecialchars($ticketId) . '
    </div>';
    exit();
}
?>



<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin - Ticket Details</title>
    <link rel="shortcut icon" href="../image/png/favicon.png" type="image/x-icon">
    <!-- Bootstrap, fonts & icons -->
    <link rel="stylesheet" href="../css/bootstrap.css">
    <link rel="stylesheet" href="../fonts/icon-font/css/style.css">
    <link rel="stylesheet" href="../fonts/typography-font/typo.css">
    <link rel="stylesheet" href="../fonts/fontawesome-5/css/all.css">
    <!-- Vendor stylesheets -->
    <link rel="stylesheet" href="../css/main.css">
    <style>
    body {
        background-color: #f8f9fa;
    }

    .admin-container {
        padding: 20px;
        max-width: 100%;
        overflow-x: hidden;
    }

    .admin-header {
        background-color: #fff;
        padding: 15px 20px;
        border-radius: 5px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        position: relative;
    }

    .admin-header h1 {
        margin: 0;
        font-size: 24px;
        color: #333;
    }

    .admin-user {
        display: flex;
        align-items: center;
    }

    .admin-content {
        background-color: #fff;
        border-radius: 5px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        padding: 20px;
    }

    .admin-content h2 {
        margin-top: 0;
        margin-bottom: 20px;
        font-size: 22px;
        color: #333;
    }

    .ticket-info-card {
        background-color: #fff;
        border-radius: 8px;
        padding: 20px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        margin-bottom: 20px;
    }

    .info-row {
        display: flex;
        margin-bottom: 12px;
        align-items: flex-start;
    }

    .info-label {
        font-weight: 600;
        color: #495057;
        min-width: 140px;
        margin-right: 10px;
    }

    .info-value {
        color: #212529;
        flex: 1;
    }

    .badge {
        display: inline-block;
        padding: 0.25em 0.6em;
        font-size: 75%;
        font-weight: 700;
        line-height: 1;
        text-align: center;
        white-space: nowrap;
        vertical-align: baseline;
        border-radius: 0.25rem;
    }

    .badge-starter {
        background-color: #fbbf24;
        color: #fff;
    }

    .badge-business {
        background-color: #01A7E1;
        color: #fff;
    }

    .badge-premium {
        background-color: #01A7E1;
        color: #fff;
    }

    .badge-ultimate {
        background-color: #793BF0;
        color: #fff;
    }

    .badge-high {
        background-color: #dc3545;
        color: #fff;
    }

    .badge-medium {
        background-color: #ffc107;
        color: #212529;
    }

    .badge-low {
        background-color: #28a745;
        color: #fff;
    }

    .badge-urgent {
        background-color: #dc3545;
        color: #fff;
    }

    .badge-open {
        background-color: #4CAF50;
        color: #fff;
    }

    .badge-wip {
        background-color: #FF9800;
        color: #fff;
    }

    .badge-solved {
        background-color: #17a2b8;
        color: #fff;
    }

    .badge-closed {
        background-color: #757575;
        color: #fff;
    }

    /* User dropdown styles */
    .user-dropdown {
        position: relative;
        display: inline-block;
    }

    .mobile-menu-toggle {
        display: none;
        transition: transform 0.3s ease;
    }

    .user-dropdown.active .mobile-menu-toggle {
        transform: rotate(180deg);
    }

    .dropdown-content {
        display: block;
    }

    /* Mobile Styles */
    @media (max-width: 767px) {
        .admin-container {
            padding: 10px;
        }

        .admin-header {
            flex-direction: column;
            align-items: flex-start;
            padding: 12px 15px;
        }

        .admin-header h1 {
            margin-bottom: 10px;
        }

        .admin-user {
            width: 100%;
        }

        .user-dropdown {
            width: 100%;
        }

        .mobile-menu-toggle {
            display: inline-block;
        }

        .dropdown-content {
            display: none;
            position: absolute;
            background-color: #f9f9f9;
            width: 100%;
            left: 0;
            box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.2);
            z-index: 1;
            border-radius: 4px;
            margin-top: 5px;
            padding: 8px;
        }

        .dropdown-content .btn {
            width: 100%;
            text-align: center;
        }

        .user-dropdown.active .dropdown-content {
            display: block;
        }
    }
    </style>
</head>

<body>
    <div class="admin-container">
        <div class="admin-header">
            <h1>Ticket Details - #<?php echo htmlspecialchars($ticketData['ticket_no'] ?? $ticketId); ?></h1>
            <div class="admin-user">
                <div class="user-dropdown">
                    <span class="user-info">
                        Welcome, <?php echo htmlspecialchars($admin_username); ?>
                        (<?php echo htmlspecialchars($admin_role); ?>)
                        <i class="mobile-menu-toggle fas fa-chevron-down"></i>
                    </span>
                    <div class="dropdown-content">
                        <a href="admin-logout.php" class="btn btn-sm btn-outline-danger">Logout</a>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-3">
                <?php include('admin-menu.php'); ?>
            </div>
            <div class="col-md-9">
                <div class="admin-content">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h2>Ticket Information</h2>
                        <div>
                            <?php
                            $isTicketClosed = (strtoupper($ticketData['status'] ?? '') === 'CLOSED');
                            if ($isTicketClosed): ?>
                                <button type="button" class="btn btn-sm me-2" disabled title="Cannot edit closed tickets" style="background-color: #6c757d; color: white;">
                                    <i class="fas fa-lock"></i> &nbsp; Ticket Closed
                                </button>
                            <?php else: ?>
                                <button type="button" class="btn btn-sm btn-warning me-2" onclick="toggleEditMode()">
                                    <i class="fas fa-edit"></i> &nbsp; Edit Ticket
                                </button>
                            <?php endif; ?>
                            <a href="admin-ticket-chat.php?ticket_id=<?php echo $ticketId; ?>" class="btn btn-sm btn-info me-2">
                                <i class="fas fa-comments"></i> &nbsp; Open Chat
                            </a>
                            <a href="admin-tickets.php" class="btn btn-sm" style="background-color: #473BF0; color: white;">
                                <i class="fas fa-arrow-left"></i> &nbsp; Back to Tickets
                            </a>
                        </div>
                    </div>

                    <?php if (!empty($updateMessage)): ?>
                    <div class="alert alert-<?php echo $updateMessageType; ?> alert-dismissible fade show" role="alert">
                        <i class="fas fa-<?php echo $updateMessageType === 'success' ? 'check-circle' : 'exclamation-triangle'; ?>"></i>
                        <?php echo htmlspecialchars($updateMessage); ?>
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <?php endif; ?>

                    <?php if ($isTicketClosed): ?>
                    <div class="alert alert-info" role="alert">
                        <i class="fas fa-info-circle"></i>
                        <strong>Notice:</strong> This ticket is closed and cannot be edited.
                    </div>
                    <?php endif; ?>

<div class="ticket-info-card">
    <div class="d-flex justify-content-between align-items-center mb-3">
        <span class="badge badge-primary">ID: <?php echo htmlspecialchars($ticketData['id']); ?></span>
    </div>

    <div class="row">
        <div class="col-md-6">
            <div class="info-row">
                <span class="info-label">Ticket Number:</span>
                <span
                    class="info-value"><strong><?php echo htmlspecialchars($ticketData['ticket_no'] ?? 'N/A'); ?></strong></span>
            </div>
            <div class="info-row">
                <span class="info-label">Subject:</span>
                <span class="info-value"><?php echo htmlspecialchars($ticketData['subject'] ?? 'N/A'); ?></span>
            </div>
            <div class="info-row">
                <span class="info-label">Requester Email:</span>
                <span class="info-value">
                    <?php if (!empty($ticketData['req_email'])): ?>
                    <a href="mailto:<?php echo htmlspecialchars($ticketData['req_email']); ?>">
                        <?php echo htmlspecialchars($ticketData['req_email']); ?>
                    </a>
                    <?php else: ?>
                    N/A
                    <?php endif; ?>
                </span>
            </div>
            <div class="info-row">
                <span class="info-label">Ticket Type:</span>
                <span
                    class="info-value"><?php echo formatTicketType($ticketData['type'] ?? 0, $ticketData['type_name'] ?? ''); ?></span>
            </div>
        </div>
        <div class="col-md-6">
            <div class="info-row">
                <span class="info-label">Priority:</span>
                <span class="info-value"><?php echo formatPriority($ticketData['priority'] ?? ''); ?></span>
            </div>
            <div class="info-row">
                <span class="info-label">Status:</span>
                <span class="info-value"><?php echo formatStatus($ticketData['status'] ?? ''); ?></span>
            </div>
            <div class="info-row">
                <span class="info-label">Contact ID:</span>
                <span class="info-value"><?php echo htmlspecialchars($ticketData['contact_id'] ?? 'N/A'); ?></span>
            </div>
            <?php if (isset($ticketData['contacts']) && !empty($ticketData['contacts'])): ?>
            <div class="info-row">
                <span class="info-label">Name:</span>
                <span class="info-value"><?php echo htmlspecialchars($ticketData['contacts']['fname'] ?? 'N/A'); ?></span>
            </div>
            <?php endif; ?>
            <div class="info-row">
                <span class="info-label">Agent ID:</span>
                <span class="info-value"><?php echo htmlspecialchars($ticketData['agent_id'] ?? 'Unassigned'); ?></span>
            </div>
        </div>
    </div>

    <!-- Edit Form (Hidden by default, completely hidden for closed tickets) -->
    <?php if (!$isTicketClosed): ?>
    <div id="editForm" style="display: none;">
        <hr>
        <h6><i class="fas fa-edit"></i> Edit Ticket</h6>
        <form method="POST" action="">
            <input type="hidden" name="update_ticket" value="1">
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="subject" class="form-label">Subject <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="subject" name="subject"
                               value="<?php echo htmlspecialchars($ticketData['subject'] ?? ''); ?>" required>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="priority" class="form-label">Priority</label>
                        <select class="form-control" id="priority" name="priority">
                            <option value="low" <?php echo (strtolower($ticketData['priority'] ?? '') === 'low') ? 'selected' : ''; ?>>Low</option>
                            <option value="medium" <?php echo (strtolower($ticketData['priority'] ?? '') === 'medium') ? 'selected' : ''; ?>>Medium</option>
                            <option value="high" <?php echo (strtolower($ticketData['priority'] ?? '') === 'high') ? 'selected' : ''; ?>>High</option>
                            <option value="urgent" <?php echo (strtolower($ticketData['priority'] ?? '') === 'urgent') ? 'selected' : ''; ?>>Urgent</option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="status" class="form-label">Status</label>
                        <select class="form-control" id="status" name="status">
                            <option value="open" <?php echo (strtolower($ticketData['status'] ?? '') === 'open') ? 'selected' : ''; ?>>Open</option>
                            <option value="wip" <?php echo (strtolower($ticketData['status'] ?? '') === 'wip') ? 'selected' : ''; ?>>In Progress</option>
                            <option value="solved" <?php echo (strtolower($ticketData['status'] ?? '') === 'solved') ? 'selected' : ''; ?>>Solved</option>
                            <option value="closed" <?php echo (strtolower($ticketData['status'] ?? '') === 'closed') ? 'selected' : ''; ?>>Closed</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">&nbsp;</label>
                        <div>
                            <button type="submit" class="btn btn-sm me-2" style="background-color: #473BF0; color: white;">
                                <i class="fas fa-save"></i> &nbsp; Save Changes
                            </button>
                            <button type="button" class="btn btn-sm" style="background-color: #dc3545; color: white;" onclick="toggleEditMode()">
                                <i class="fas fa-times"></i> &nbsp; Cancel
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <?php endif; ?>

    <!-- Timestamps -->
    <hr>
    <h6><i class="fas fa-clock"></i> Timeline</h6>
    <div class="row">
        <div class="col-md-6">
            <div class="info-row">
                <span class="info-label">Created:</span>
                <span class="info-value"><?php echo formatDate($ticketData['created'] ?? ''); ?></span>
            </div>
        </div>
        <div class="col-md-6">
            <div class="info-row">
                <span class="info-label">Last Updated:</span>
                <span class="info-value"><?php echo formatDate($ticketData['updated'] ?? ''); ?></span>
            </div>
        </div>
    </div>

    <!-- Description section with full-width layout -->
    <div style="margin-top: 20px;">
        <div style="font-weight: bold; margin-bottom: 8px; color: #333;">Description:</div>
        <div style="background-color: #f8f9fa; padding: 12px; border-radius: 4px; border-left: 3px solid #007bff; line-height: 1.5;">
            <?php
            if (strpos($description, '<em>') === 0) {
                // If it's the "No description found" message, display as-is
                echo $description;
            } else {
                // For actual descriptions, escape HTML and convert newlines
                // First normalize line endings (both actual and literal \r\n), then convert to HTML
                $cleanDescription = str_replace(["\r\n", "\r", "\\r\\n"], "\n", $description);
                echo nl2br(htmlspecialchars($cleanDescription));
            }
            ?>
        </div>
    </div>

                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="../js/jquery-3.4.1.min.js"></script>
    <script src="../js/bootstrap.js"></script>
    <script>
        // Toggle edit mode
        function toggleEditMode() {
            const editForm = document.getElementById('editForm');
            const editButton = document.querySelector('button[onclick="toggleEditMode()"]');

            if (editForm.style.display === 'none' || editForm.style.display === '') {
                editForm.style.display = 'block';
                editButton.innerHTML = '<i class="fas fa-times"></i> &nbsp; Cancel Edit';
                editButton.className = 'btn btn-sm me-2';
                editButton.style.backgroundColor = '#dc3545';
                editButton.style.color = 'white';
            } else {
                editForm.style.display = 'none';
                editButton.innerHTML = '<i class="fas fa-edit"></i> &nbsp; Edit Ticket';
                editButton.className = 'btn btn-sm btn-warning me-2';
                editButton.style.backgroundColor = '';
                editButton.style.color = '';
            }
        }

        // User dropdown functionality for mobile
        const userDropdown = document.querySelector('.user-dropdown');
        const userInfo = document.querySelector('.user-info');
        const menuToggle = document.querySelector('.mobile-menu-toggle');

        if (userInfo && menuToggle) {
            userInfo.addEventListener('click', function() {
                userDropdown.classList.toggle('active');
            });
        }
    </script>
</body>
</html>