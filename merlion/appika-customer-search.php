<?php
session_start();
include('../functions/server.php');

// Check if admin is logged in
if (!isset($_SESSION['admin_username'])) {
    header('location: admin-login');
    exit();
}

$admin_username = $_SESSION['admin_username'];
$admin_id = $_SESSION['admin_id'];
$admin_role = $_SESSION['admin_role'];

// Load centralized API configuration
require_once '../config/api-config.php';
require_once '../vendor/autoload.php';

// Get API configuration
$apiConfig = getCustomerApiConfig();
$apiEndpoint = $apiConfig['endpoint'];
$apiPath = $apiConfig['path'];
$apiKey = $apiConfig['key'];

// Create a Guzzle HTTP client
$client = new \GuzzleHttp\Client([
    'base_uri' => $apiEndpoint,
    'timeout' => 30,
    'http_errors' => false,
]);

// Variables for search results
$message = '';
$messageType = '';
$customerData = null;
$customerAddresses = null;

// Process search form
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['search_customer'])) {
    $customerCode = trim($_POST['customer_code']);

    if (!empty($customerCode)) {
        try {
            // Search for customer by customer code
            $response = $client->request('GET', $apiPath, [
                'headers' => [
                    'X-api-key' => "{$apiKey}",
                    'Accept' => 'application/json',
                ],
                'query' => [
                    'no' => $customerCode
                ]
            ]);

            $statusCode = $response->getStatusCode();
            $responseBody = $response->getBody()->getContents();
            $responseData = json_decode($responseBody, true);

            if ($statusCode >= 200 && $statusCode < 300) {
                if (isset($responseData['items']) && !empty($responseData['items'])) {
                    $customerData = $responseData['items'][0];
                    $message = 'Customer found successfully!';
                    $messageType = 'success';

                    // Fetch customer addresses
                    $customerDbId = $customerData['id'];
                    $addressResponse = $client->request('GET', $apiPath . '/' . $customerDbId . '/locations', [
                        'headers' => [
                            'X-api-key' => "{$apiKey}",
                            'Accept' => 'application/json',
                        ]
                    ]);

                    if ($addressResponse->getStatusCode() >= 200 && $addressResponse->getStatusCode() < 300) {
                        $addressData = json_decode($addressResponse->getBody()->getContents(), true);
                        if (isset($addressData['items'])) {
                            $customerAddresses = $addressData['items'];
                        }
                    }
                } else {
                    $message = 'No customer found with customer code: ' . htmlspecialchars($customerCode);
                    $messageType = 'warning';
                }
            } else {
                $message = 'API Error: HTTP ' . $statusCode . ' - ' . ($responseData['message'] ?? 'Unknown error');
                $messageType = 'danger';
            }
        } catch (Exception $e) {
            $message = 'Connection Error: ' . $e->getMessage();
            $messageType = 'danger';
        }
    } else {
        $message = 'Please enter a customer code to search.';
        $messageType = 'warning';
    }
}

// Helper function to format date
function formatDate($dateString) {
    if (empty($dateString)) return 'Not set';
    try {
        $date = new DateTime($dateString);
        return $date->format('d M Y');
    } catch (Exception $e) {
        return $dateString;
    }
}

// Helper function to format entity type
function formatEntityType($entityType) {
    switch ($entityType) {
        case '1': return 'Company';
        case '2': return 'Individual';
        default: return $entityType ?: 'Not specified';
    }
}

// Helper function to format status
function formatStatus($status) {
    switch (strtolower($status)) {
        case 'a': return '<span class="badge badge-success">Active</span>';
        case 'i': return '<span class="badge badge-secondary">Inactive</span>';
        default: return '<span class="badge badge-light">' . htmlspecialchars($status) . '</span>';
    }
}
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin - Customer Search</title>
    <link rel="shortcut icon" href="../image/png/favicon.png" type="image/x-icon">
    <!-- Bootstrap, fonts & icons -->
    <link rel="stylesheet" href="../css/bootstrap.css">
    <link rel="stylesheet" href="../fonts/icon-font/css/style.css">
    <link rel="stylesheet" href="../fonts/typography-font/typo.css">
    <link rel="stylesheet" href="../fonts/fontawesome-5/css/all.css">
    <!-- Vendor stylesheets -->
    <link rel="stylesheet" href="../css/main.css">
    <style>
    .badge-success {
        color: white;
        background-color: #68d585;
    }

    body {
        background-color: #f8f9fa;
    }

    .admin-container {
        padding: 20px;
        max-width: 100%;
        overflow-x: hidden;
    }

    .admin-header {
        background-color: #fff;
        padding: 15px 20px;
        border-radius: 5px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        position: relative;
    }

    .admin-header h1 {
        margin: 0;
        font-size: 24px;
        color: #333;
    }

    .admin-user {
        display: flex;
        align-items: center;
    }

    .admin-content {
        background-color: #fff;
        border-radius: 5px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        padding: 20px;
        min-height: calc(100vh - 100px);
    }

    .search-box {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 20px;
        border-radius: 10px;
        margin-bottom: 20px;
    }

    .customer-info-card {
        border: 1px solid #e0e6ed;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
        background: #fff;
    }

    .info-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 0;
        border-bottom: 1px solid #f0f0f0;
    }

    .info-row:last-child {
        border-bottom: none;
    }

    .info-label {
        font-weight: 600;
        color: #555;
        min-width: 150px;
    }

    .info-value {
        color: #333;
        flex: 1;
        text-align: right;
    }

    .address-card {
        border: 1px solid #e0e6ed;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 15px;
        background: #f8f9fa;
    }

    .primary-location {
        border-color: #28a745;
        background: #f8fff9;
    }

    .user-dropdown {
        position: relative;
        display: inline-block;
    }

    .user-info {
        cursor: pointer;
        display: flex;
        align-items: center;
    }

    .mobile-menu-toggle {
        margin-left: 5px;
        transition: transform 0.3s ease;
        display: none;
    }

    .dropdown-content {
        display: block;
    }

    /* Mobile Styles */
    @media (max-width: 767px) {
        .admin-container {
            padding: 10px;
        }

        .admin-header {
            flex-direction: column;
            align-items: flex-start;
            padding: 10px;
        }

        .admin-header h1 {
            margin-bottom: 10px;
            font-size: 20px;
        }

        .mobile-menu-toggle {
            display: inline-block;
        }

        .dropdown-content {
            display: none;
            position: absolute;
            background-color: #f9f9f9;
            width: 100%;
            left: 0;
            box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.2);
            z-index: 1;
            border-radius: 4px;
            margin-top: 5px;
            padding: 8px;
        }

        .user-dropdown.active .dropdown-content {
            display: block;
        }

        .info-row {
            flex-direction: column;
            align-items: flex-start;
        }

        .info-value {
            text-align: left;
            margin-top: 5px;
        }
    }
    </style>
</head>

<body>
    <div class="admin-container">
        <div class="admin-header">
            <h1><i class="fas fa-search"></i> Customer Search</h1>
            <div class="admin-user">
                <div class="user-dropdown">
                    <span class="user-info">
                        Welcome, <?php echo htmlspecialchars($admin_username); ?>
                        (<?php echo htmlspecialchars($admin_role); ?>)
                        <i class="mobile-menu-toggle fas fa-chevron-down"></i>
                    </span>
                    <div class="dropdown-content">
                        <a href="admin-logout.php" class="btn btn-sm btn-outline-danger">Logout</a>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-2 col-md-3 col-sm-12">
                <?php include('admin-menu.php'); ?>
            </div>

            <div class="col-lg-10 col-md-9 col-sm-12">
                <div class="admin-content">
                    <!-- Search Form -->
                    <div class="search-box">
                        <h3 style="color: white;"><i class="fas fa-search"></i> Search Customer by Customer Code</h3>
                        <p class="mb-3" style="color: white;">Enter the customer code to retrieve detailed information
                            from Appika API</p>

                        <form method="POST" action="">
                            <div class="row">
                                <div class="col-md-8">
                                    <div class="form-group">
                                        <input type="text" class="form-control form-control-lg" name="customer_code"
                                            placeholder="Enter customer code (e.g., HI003, CUST1234)"
                                            value="<?php echo isset($_POST['customer_code']) ? htmlspecialchars($_POST['customer_code']) : ''; ?>"
                                            required>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <button type="submit" name="search_customer" class="btn btn-light btn-lg btn-block">
                                        <i class="fas fa-search"></i> Search Customer
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>

                    <!-- Messages -->
                    <?php if ($message): ?>
                    <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert"
                        id="alertMessage">
                        <i
                            class="fas fa-<?php echo $messageType === 'success' ? 'check-circle' : ($messageType === 'warning' ? 'exclamation-triangle' : 'times-circle'); ?>"></i>
                        <?php echo $message; ?>
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close"
                            onclick="closeAlert()">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <?php endif; ?>

                    <!-- Customer Information -->
                    <?php if ($customerData): ?>
                    <div class="customer-info-card">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h4><i class="fas fa-user"></i> Customer Information</h4>
                            <span class="badge badge-primary">ID:
                                <?php echo htmlspecialchars($customerData['id']); ?></span>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="info-row">
                                    <span class="info-label">Customer Code:</span>
                                    <span
                                        class="info-value"><strong><?php echo htmlspecialchars($customerData['no'] ?? 'N/A'); ?></strong></span>
                                </div>
                                <div class="info-row">
                                    <span class="info-label">Customer Name:</span>
                                    <span
                                        class="info-value"><?php echo htmlspecialchars($customerData['name'] ?? 'N/A'); ?></span>
                                </div>
                                <div class="info-row">
                                    <span class="info-label">Entity Type:</span>
                                    <span
                                        class="info-value"><?php echo formatEntityType($customerData['entity_type'] ?? ''); ?></span>
                                </div>
                                <div class="info-row">
                                    <span class="info-label">Status:</span>
                                    <span
                                        class="info-value"><?php echo formatStatus($customerData['status'] ?? ''); ?></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="info-row">
                                    <span class="info-label">Group ID:</span>
                                    <span
                                        class="info-value"><?php echo htmlspecialchars($customerData['grp_id'] ?? 'N/A'); ?></span>
                                </div>
                                <div class="info-row">
                                    <span class="info-label">Office ID:</span>
                                    <span
                                        class="info-value"><?php echo htmlspecialchars($customerData['ofc_id'] ?? 'N/A'); ?></span>
                                </div>
                                <div class="info-row">
                                    <span class="info-label">Start Date:</span>
                                    <span
                                        class="info-value"><?php echo formatDate($customerData['start_date'] ?? ''); ?></span>
                                </div>
                                <div class="info-row">
                                    <span class="info-label">Assigned To:</span>
                                    <span
                                        class="info-value"><?php echo htmlspecialchars($customerData['assign2'] ?? 'N/A'); ?></span>
                                </div>
                            </div>
                        </div>

                        <!-- Additional Information -->
                        <?php if (!empty($customerData['creator']) || !empty($customerData['created']) || !empty($customerData['updated'])): ?>
                        <hr>
                        <h5><i class="fas fa-info-circle"></i> Additional Details</h5>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="info-row">
                                    <span class="info-label">Creator:</span>
                                    <span
                                        class="info-value"><?php echo htmlspecialchars($customerData['creator'] ?? 'N/A'); ?></span>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="info-row">
                                    <span class="info-label">Created:</span>
                                    <span
                                        class="info-value"><?php echo formatDate($customerData['created'] ?? ''); ?></span>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="info-row">
                                    <span class="info-label">Last Updated:</span>
                                    <span
                                        class="info-value"><?php echo formatDate($customerData['updated'] ?? ''); ?></span>
                                </div>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>

                    <!-- Customer Addresses -->
                    <?php if ($customerAddresses): ?>
                    <div class="customer-info-card">
                        <h4><i class="fas fa-map-marker-alt"></i> Customer Locations
                            (<?php echo count($customerAddresses); ?>)</h4>

                        <?php foreach ($customerAddresses as $index => $address): ?>
                        <div
                            class="address-card <?php echo ($address['is_primary_loc'] ?? '') === 'y' ? 'primary-location' : ''; ?>">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <h6 class="mb-0">
                                    <i class="fas fa-building"></i>
                                    <?php echo htmlspecialchars($address['loc_name'] ?? 'Location ' . ($index + 1)); ?>
                                    <?php if (($address['is_primary_loc'] ?? '') === 'y'): ?>
                                    <span class="badge badge-success ml-2">Primary</span>
                                    <?php endif; ?>
                                </h6>
                                <small class="text-muted">ID:
                                    <?php echo htmlspecialchars($address['id'] ?? 'N/A'); ?></small>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="info-row">
                                        <span class="info-label">Location Code:</span>
                                        <span
                                            class="info-value"><?php echo htmlspecialchars($address['loc_code'] ?? 'N/A'); ?></span>
                                    </div>
                                    <div class="info-row">
                                        <span class="info-label">Address:</span>
                                        <span
                                            class="info-value"><?php echo htmlspecialchars($address['add1'] ?? 'N/A'); ?></span>
                                    </div>
                                    <?php if (!empty($address['add2'])): ?>
                                    <div class="info-row">
                                        <span class="info-label">Address 2:</span>
                                        <span
                                            class="info-value"><?php echo htmlspecialchars($address['add2']); ?></span>
                                    </div>
                                    <?php endif; ?>
                                    <div class="info-row">
                                        <span class="info-label">City:</span>
                                        <span
                                            class="info-value"><?php echo htmlspecialchars($address['city'] ?? 'N/A'); ?></span>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="info-row">
                                        <span class="info-label">State:</span>
                                        <span
                                            class="info-value"><?php echo htmlspecialchars($address['state_code'] ?? 'N/A'); ?></span>
                                    </div>
                                    <div class="info-row">
                                        <span class="info-label">Postal Code:</span>
                                        <span
                                            class="info-value"><?php echo htmlspecialchars($address['zip'] ?? 'N/A'); ?></span>
                                    </div>
                                    <div class="info-row">
                                        <span class="info-label">Country:</span>
                                        <span
                                            class="info-value"><?php echo htmlspecialchars($address['ccode'] ?? 'N/A'); ?></span>
                                    </div>
                                    <div class="info-row">
                                        <span class="info-label">Status:</span>
                                        <span
                                            class="info-value"><?php echo formatStatus($address['status'] ?? ''); ?></span>
                                    </div>
                                </div>
                            </div>

                            <!-- Contact Information -->
                            <?php if (!empty($address['email']) || !empty($address['tel_work']) || !empty($address['contact_pax'])): ?>
                            <hr>
                            <h6><i class="fas fa-phone"></i> Contact Information</h6>
                            <div class="row">
                                <?php if (!empty($address['contact_pax'])): ?>
                                <div class="col-md-4">
                                    <div class="info-row">
                                        <span class="info-label">Contact Person:</span>
                                        <span
                                            class="info-value"><?php echo htmlspecialchars($address['contact_pax']); ?></span>
                                    </div>
                                </div>
                                <?php endif; ?>
                                <?php if (!empty($address['email'])): ?>
                                <div class="col-md-4">
                                    <div class="info-row">
                                        <span class="info-label">Email:</span>
                                        <span class="info-value">
                                            <a href="mailto:<?php echo htmlspecialchars($address['email']); ?>">
                                                <?php echo htmlspecialchars($address['email']); ?>
                                            </a>
                                        </span>
                                    </div>
                                </div>
                                <?php endif; ?>
                                <?php if (!empty($address['tel_work'])): ?>
                                <div class="col-md-4">
                                    <div class="info-row">
                                        <span class="info-label">Phone:</span>
                                        <span class="info-value">
                                            <a href="tel:<?php echo htmlspecialchars($address['tel_work']); ?>">
                                                <?php echo htmlspecialchars($address['tel_work']); ?>
                                            </a>
                                        </span>
                                    </div>
                                </div>
                                <?php endif; ?>
                            </div>
                            <?php endif; ?>
                        </div>
                        <?php endforeach; ?>
                    </div>
                    <?php endif; ?>
                    <?php endif; ?>

                    <!-- No Results Message -->
                    <?php if ($_SERVER['REQUEST_METHOD'] === 'POST' && !$customerData && $messageType !== 'danger'): ?>
                    <div class="text-center py-5">
                        <i class="fas fa-search fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No customer found</h5>
                        <p class="text-muted">Try searching with a different customer code</p>
                    </div>
                    <?php endif; ?>

                    <!-- Quick Actions -->
                    <?php if ($customerData): ?>
                    <!-- <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-tools"></i> Quick Actions</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3">
                                    <a href="appika-customers.php" class="btn btn-outline-primary btn-block">
                                        <i class="fas fa-list"></i> &nbsp; View All Customers
                                    </a>
                                </div>
                                <div class="col-md-3">
                                    <a href="../api/core_api_post_example.php"
                                        class="btn btn-outline-secondary btn-block">
                                        <i class="fas fa-edit"></i> Edit Customer
                                    </a>
                                </div>
                                <div class="col-md-3">
                                    <button class="btn btn-outline-info btn-block" onclick="window.print()">
                                        <i class="fas fa-print"></i> Print Details
                                    </button>
                                </div>
                                <div class="col-md-3">
                                    <button class="btn btn-outline-success btn-block" onclick="searchAnother()">
                                        <i class="fas fa-search-plus"></i> Search Another
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div> -->
                    <?php endif; ?>

                    <!-- Help Section -->
                    <div class="card mt-4">
                        <div class="card-header">
                            <h5><i class="fas fa-question-circle"></i> Help & Tips</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6>Search Tips:</h6>
                                    <ul class="list-unstyled">
                                        <li><i class="fas fa-check text-success"></i> Use exact customer codes (e.g.,
                                            HI003, CUST1234)</li>
                                        <li><i class="fas fa-check text-success"></i> Customer codes are case-sensitive
                                        </li>
                                        <li><i class="fas fa-check text-success"></i> Search returns complete customer
                                            profile</li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <h6>Available Information:</h6>
                                    <ul class="list-unstyled">
                                        <li><i class="fas fa-info text-primary"></i> Basic customer details</li>
                                        <li><i class="fas fa-info text-primary"></i> All customer locations/addresses
                                        </li>
                                        <li><i class="fas fa-info text-primary"></i> Contact information</li>
                                        <li><i class="fas fa-info text-primary"></i> Status and dates</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="../js/jquery.min.js"></script>
    <script src="../js/bootstrap.bundle.min.js"></script>
    <script>
    // Close alert function
    function closeAlert() {
        const alertElement = document.getElementById('alertMessage');
        if (alertElement) {
            alertElement.style.opacity = '0';
            setTimeout(function() {
                alertElement.style.display = 'none';
            }, 150);
        }
    }

    // Mobile menu toggle
    document.addEventListener('DOMContentLoaded', function() {
        const userDropdown = document.querySelector('.user-dropdown');
        const userInfo = document.querySelector('.user-info');
        const mobileToggle = document.querySelector('.mobile-menu-toggle');

        if (userInfo && mobileToggle) {
            userInfo.addEventListener('click', function() {
                userDropdown.classList.toggle('active');
                mobileToggle.classList.toggle('active');
            });
        }

        // Initialize Bootstrap alert dismissal if Bootstrap is available
        if (typeof bootstrap !== 'undefined' && bootstrap.Alert) {
            // Bootstrap 5 style
            const alertElement = document.getElementById('alertMessage');
            if (alertElement) {
                alertElement.addEventListener('click', function(e) {
                    if (e.target.classList.contains('close') || e.target.closest('.close')) {
                        closeAlert();
                    }
                });
            }
        } else if (typeof $ !== 'undefined' && $.fn.alert) {
            // Bootstrap 4 style with jQuery
            $('.alert').alert();
        }

        // Auto-dismiss success alerts after 5 seconds
        const successAlert = document.querySelector('.alert-success');
        if (successAlert) {
            setTimeout(function() {
                closeAlert();
            }, 5000);
        }
    });

    // Search another customer
    function searchAnother() {
        document.querySelector('input[name="customer_code"]').value = '';
        document.querySelector('input[name="customer_code"]').focus();
        // Scroll to top
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    }

    // Auto-focus on search input when page loads
    document.addEventListener('DOMContentLoaded', function() {
        const searchInput = document.querySelector('input[name="customer_code"]');
        if (searchInput && !searchInput.value) {
            searchInput.focus();
        }
    });
    </script>
</body>

</html>