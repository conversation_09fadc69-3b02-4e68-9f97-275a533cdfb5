<?php
/**
 * Test if sync function is completely disabled
 */

session_start();
include('../functions/server.php');
require_once('../functions/ticket-expiration-functions.php');

// Check if admin is logged in
if (!isset($_SESSION['admin_username'])) {
    header('location: admin-login.php');
    exit();
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Sync Disabled</title>
    <link rel="stylesheet" href="../css/bootstrap.css">
    <style>
        body { background-color: #f8f9fa; padding: 20px; }
        .container { max-width: 800px; }
        .test-section { background: white; padding: 20px; margin: 15px 0; border-radius: 8px; border-left: 4px solid #007bff; }
        .success { border-left-color: #28a745; }
        .error { border-left-color: #dc3545; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Test Sync Function Disabled</h1>
        
        <?php
        $test_username = 'HC200';
        
        echo "<div class='test-section'>";
        echo "<h3>Testing syncUserTableTickets Function</h3>";
        
        // Get current ticket counts before
        $before_query = "SELECT starter_tickets, premium_tickets, ultimate_tickets FROM user WHERE username = '$test_username'";
        $before_result = mysqli_query($conn, $before_query);
        $before = mysqli_fetch_assoc($before_result);
        
        echo "<p><strong>Before sync call:</strong> Starter: {$before['starter_tickets']}, Premium: {$before['premium_tickets']}, Ultimate: {$before['ultimate_tickets']}</p>";
        
        // Call the sync function
        echo "<p>Calling syncUserTableTickets('$test_username')...</p>";
        $sync_result = syncUserTableTickets($test_username);
        echo "<p><strong>Sync function returned:</strong> " . ($sync_result ? 'true' : 'false') . "</p>";
        
        // Get ticket counts after
        $after_result = mysqli_query($conn, $before_query);
        $after = mysqli_fetch_assoc($after_result);
        
        echo "<p><strong>After sync call:</strong> Starter: {$after['starter_tickets']}, Premium: {$after['premium_tickets']}, Ultimate: {$after['ultimate_tickets']}</p>";
        
        // Check if anything changed
        $changed = (
            $before['starter_tickets'] != $after['starter_tickets'] ||
            $before['premium_tickets'] != $after['premium_tickets'] ||
            $before['ultimate_tickets'] != $after['ultimate_tickets']
        );
        
        if ($changed) {
            echo "<div class='alert alert-danger'>";
            echo "<h4>❌ SYNC FUNCTION IS STILL ACTIVE!</h4>";
            echo "<p>The sync function modified ticket counts even though it should be disabled.</p>";
            echo "</div>";
        } else {
            echo "<div class='alert alert-success'>";
            echo "<h4>✅ SYNC FUNCTION IS DISABLED!</h4>";
            echo "<p>The sync function did not modify any ticket counts.</p>";
            echo "</div>";
        }
        
        echo "</div>";
        
        // Test if there are any other processes running
        echo "<div class='test-section'>";
        echo "<h3>Checking for Other Processes</h3>";
        
        // Check if there are any cron jobs or scheduled tasks
        echo "<p><strong>Checking for Windows scheduled tasks...</strong></p>";
        
        // Check if there are any other files that might be running
        $suspicious_files = [
            '../cron/ticket-expiration-cleanup.php',
            '../check-all-users-sync.php',
            '../merlion/force-update-expiration.php'
        ];
        
        foreach ($suspicious_files as $file) {
            if (file_exists($file)) {
                echo "<p>⚠️ Found: " . basename($file) . "</p>";
            }
        }
        
        echo "</div>";
        ?>
        
        <div class="mt-4">
            <a href="real-time-ticket-monitor.php" class="btn btn-info">🔍 Back to Monitor</a>
            <a href="admin-users.php" class="btn btn-secondary">← Back to Users</a>
        </div>
    </div>
</body>
</html>
