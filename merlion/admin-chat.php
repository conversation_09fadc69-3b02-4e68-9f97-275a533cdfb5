<?php
session_start();
include_once('../functions/server.php');

// Include timezone helper for proper time handling (use include_once to prevent multiple inclusions)
include_once('../functions/timezone-helper.php');

// Check if admin is logged in
if (!isset($_SESSION['admin_username'])) {
    header('location: admin-login.php');
    exit();
}

$admin_id = $_SESSION['admin_id'];
$admin_username = $_SESSION['admin_username'];
$admin_role = $_SESSION['admin_role'];

// Get ticket ID if provided
$ticket_id = isset($_GET['ticket_id']) ? intval($_GET['ticket_id']) : null;
$ticket_info = null;
$user_id = null;

// If ticket ID is provided, get ticket information
if ($ticket_id) {
    $ticket_query = "SELECT st.*, u.username, u.email, u.tell
                    FROM support_tickets st
                    JOIN user u ON st.user_id = u.id
                    WHERE st.id = $ticket_id";
    $ticket_result = mysqli_query($conn, $ticket_query);

    if ($ticket_result && mysqli_num_rows($ticket_result) > 0) {
        $ticket_info = mysqli_fetch_assoc($ticket_result);
        $user_id = $ticket_info['user_id'];
    }
}

// Create chat_messages table if it doesn't exist (modified for direct chat)
$create_table_sql = "CREATE TABLE IF NOT EXISTS chat_messages (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    ticket_id INT(11) NULL,
    user_id INT(11) NOT NULL,
    sender_id INT(11) NOT NULL,
    sender_type ENUM('user', 'admin') NOT NULL,
    message TEXT NOT NULL,
    is_read TINYINT(1) DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_ticket_id (ticket_id)
);";

mysqli_query($conn, $create_table_sql);

// Add user_id column if it doesn't exist (for existing installations)
$check_user_id_sql = "SHOW COLUMNS FROM chat_messages LIKE 'user_id'";
$check_result = mysqli_query($conn, $check_user_id_sql);

if (mysqli_num_rows($check_result) == 0) {
    $add_user_id_sql = "ALTER TABLE chat_messages ADD COLUMN user_id INT(11) NULL AFTER ticket_id";
    $result = mysqli_query($conn, $add_user_id_sql);

    // If the column was added successfully, populate it with data from existing records
    if ($result) {
        $populate_user_id_sql = "UPDATE chat_messages cm
                                JOIN support_tickets st ON cm.ticket_id = st.id
                                SET cm.user_id = st.user_id
                                WHERE cm.user_id IS NULL AND cm.ticket_id IS NOT NULL";
        mysqli_query($conn, $populate_user_id_sql);
    }
}

// Get all users with live chat messages
$users_query = "SELECT DISTINCT u.id, u.username, u.email,
                (SELECT MAX(cm.created_at) FROM chat_messages cm WHERE cm.user_id = u.id) as last_message_time,
                (SELECT COUNT(*) FROM chat_messages cm WHERE cm.user_id = u.id AND cm.sender_type = 'user' AND cm.is_read = 0) as unread_count
                FROM user u
                WHERE u.id IN (SELECT DISTINCT user_id FROM chat_messages)
                ORDER BY last_message_time DESC, unread_count DESC, u.username ASC";
$users_result = mysqli_query($conn, $users_query);

// Handle delete chat request
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['delete_chat']) && $_POST['delete_chat'] === '1') {
    $ticket_id_to_delete = isset($_POST['ticket_id']) ? intval($_POST['ticket_id']) : null;
    $admin_password = isset($_POST['admin_password']) ? $_POST['admin_password'] : '';
    $is_ajax = !empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest';

    $response = array('success' => false, 'error' => '');

    if (!$ticket_id_to_delete) {
        $response['error'] = 'Invalid ticket ID';
    } elseif (empty($admin_password)) {
        $response['error'] = 'Admin password is required';
    } else {
        // Verify admin password
        $hashed_password = md5($admin_password);
        $verify_query = "SELECT id FROM admin_users WHERE id = $admin_id AND password = '$hashed_password'";
        $verify_result = mysqli_query($conn, $verify_query);

        if (mysqli_num_rows($verify_result) === 0) {
            $response['error'] = 'Invalid admin password';
        } else {
            // Check if ticket status is closed
            $status_check_query = "SELECT status FROM support_tickets WHERE id = $ticket_id_to_delete";
            $status_result = mysqli_query($conn, $status_check_query);

            if ($status_result && mysqli_num_rows($status_result) > 0) {
                $ticket_status = mysqli_fetch_assoc($status_result);

                if ($ticket_status['status'] !== 'closed') {
                    $response['error'] = 'Chat can only be deleted for closed tickets';
                } else {
                    // Delete all chat messages for this ticket
                    $delete_query = "DELETE FROM chat_messages WHERE ticket_id = $ticket_id_to_delete";

                    if (mysqli_query($conn, $delete_query)) {
                        $deleted_count = mysqli_affected_rows($conn);

                        // Log the deletion
                        $log_description = "Admin deleted $deleted_count chat messages for ticket #$ticket_id_to_delete";
                        $log_sql = "INSERT INTO ticket_logs (action, description, user_id, ticket_id, ticket_type, performed_by_admin_id, created_at)
                                   SELECT 'delete_chat', '$log_description', st.user_id, st.id, st.ticket_type, $admin_id, NOW()
                                   FROM support_tickets st WHERE st.id = $ticket_id_to_delete";
                        mysqli_query($conn, $log_sql);

                        $response['success'] = true;
                        $response['message'] = "Chat deleted successfully. $deleted_count messages removed.";
                    } else {
                        $response['error'] = 'Failed to delete chat messages';
                    }
                }
            } else {
                $response['error'] = 'Ticket not found';
            }
        }
    }

    if ($is_ajax) {
        header('Content-Type: application/json');
        echo json_encode($response);
        exit();
    } else {
        if ($response['success']) {
            header("Location: admin-chat.php?user_id=$chat_user_id&ticket_id=$ticket_id_to_delete&success=1&message=" . urlencode($response['message']));
        } else {
            header("Location: admin-chat.php?user_id=$chat_user_id&ticket_id=$ticket_id_to_delete&error=" . urlencode($response['error']));
        }
        exit();
    }
}

// Handle sending new messages (pure live chat mode - no tickets)
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['message']) && !empty($_POST['message'])) {
    $message = mysqli_real_escape_string($conn, $_POST['message']);
    $chat_user_id = isset($_POST['user_id']) ? intval($_POST['user_id']) : $user_id;

    if ($chat_user_id) {
        // Get current UTC time for consistent storage
        $utc_time = UTCTimeHelper::getCurrentUTC();

        // Insert message directly (no ticket required)
        $insert_query = "INSERT INTO chat_messages (ticket_id, user_id, sender_id, sender_type, message, created_at)
                        VALUES (NULL, ?, ?, 'admin', ?, ?)";
        $stmt = mysqli_prepare($conn, $insert_query);
        mysqli_stmt_bind_param($stmt, 'iiss', $chat_user_id, $admin_id, $message, $utc_time);
        $success = mysqli_stmt_execute($stmt);
        $message_id = mysqli_insert_id($conn);
        mysqli_stmt_close($stmt);

        // Check if this is an AJAX request
        $isAjax = !empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest';

        if ($isAjax) {
            // For AJAX requests, return JSON response
            if ($success) {
                // Get the inserted message data
                $message_query = "SELECT cm.*, a.username as sender_name
                                 FROM chat_messages cm
                                 LEFT JOIN admin_users a ON cm.sender_id = a.id
                                 WHERE cm.id = ?";
                $stmt = mysqli_prepare($conn, $message_query);
                mysqli_stmt_bind_param($stmt, 'i', $message_id);
                mysqli_stmt_execute($stmt);
                $result = mysqli_stmt_get_result($stmt);
                $message_data = mysqli_fetch_assoc($result);
                mysqli_stmt_close($stmt);

                // Format time for display
                $message_data['formatted_time'] = showCustomerTime($message_data['created_at'], 'g:i A');

                echo json_encode([
                    'success' => true,
                    'message' => $message_data
                ]);
            } else {
                echo json_encode([
                    'success' => false,
                    'error' => 'Failed to send message'
                ]);
            }
            exit();
        } else {
            // For traditional form submissions, redirect
            header("Location: admin-chat.php" . ($chat_user_id ? "?user_id=$chat_user_id" : ""));
            exit();
        }
    }
}

// Get user ID from URL if provided
$chat_user_id = isset($_GET['user_id']) ? intval($_GET['user_id']) : $user_id;

// Get chat messages for the selected user
$messages = [];
if ($chat_user_id) {
    // Mark user messages as read for this user (live chat mode)
    $update_query = "UPDATE chat_messages
                    SET is_read = 1
                    WHERE user_id = $chat_user_id
                    AND sender_type = 'user'
                    AND is_read = 0";
    mysqli_query($conn, $update_query);

    // Get all live chat messages for this user
    $messages_query = "SELECT cm.*,
                      CASE
                          WHEN cm.sender_type = 'admin' THEN a.username
                          WHEN cm.sender_type = 'user' THEN u.username
                      END as sender_name
                      FROM chat_messages cm
                      LEFT JOIN admin_users a ON cm.sender_id = a.id AND cm.sender_type = 'admin'
                      LEFT JOIN user u ON cm.sender_id = u.id AND cm.sender_type = 'user'
                      WHERE cm.user_id = $chat_user_id";

    $messages_query .= " ORDER BY cm.created_at ASC";
    $messages_result = mysqli_query($conn, $messages_query);

    if ($messages_result) {
        while ($message = mysqli_fetch_assoc($messages_result)) {
            $messages[] = $message;
        }
    }

    // Get user information
    $user_query = "SELECT * FROM user WHERE id = $chat_user_id";
    $user_result = mysqli_query($conn, $user_query);
    $user_info = mysqli_fetch_assoc($user_result);

    // Get user's tickets with unread message counts
    $user_tickets_query = "SELECT st.*,
                          (SELECT COUNT(*) FROM chat_messages cm
                           WHERE cm.ticket_id = st.id
                           AND cm.sender_type = 'user'
                           AND cm.is_read = 0) as unread_count
                          FROM support_tickets st
                          WHERE st.user_id = $chat_user_id
                          ORDER BY st.created_at DESC";
    $user_tickets_result = mysqli_query($conn, $user_tickets_query);
}

// --- AJAX endpoint for admin notification count ---
if (
    isset($_GET['action']) && $_GET['action'] === 'get_admin_notifications'
    && isset($_SESSION['admin_id'])
    && !empty($_SERVER['HTTP_X_REQUESTED_WITH'])
    && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest'
) {
    // Count unread messages directly from chat_messages table
    $notif_sql = "SELECT COUNT(*) as unread_count FROM chat_messages WHERE sender_type = 'user' AND is_read = 0";
    $notif_result = mysqli_query($conn, $notif_sql);
    $notif_row = mysqli_fetch_assoc($notif_result);
    $unread_count = intval($notif_row['unread_count']);
    header('Content-Type: application/json');
    echo json_encode(['success' => true, 'unread_count' => $unread_count]);
    exit();
}

// --- AJAX endpoint for chat user list (for sidebar auto-update) ---
if (
    isset($_GET['action']) && $_GET['action'] === 'get_chat_user_list'
    && isset($_SESSION['admin_id'])
    && !empty($_SERVER['HTTP_X_REQUESTED_WITH'])
    && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest'
) {
    $admin_id = $_SESSION['admin_id'];
    $users_query = "SELECT DISTINCT u.id, u.username, u.email,
        (SELECT MAX(cm.created_at) FROM chat_messages cm WHERE cm.user_id = u.id) as last_message_time,
        (SELECT COUNT(*) FROM chat_messages cm WHERE cm.user_id = u.id AND cm.sender_type = 'user' AND cm.is_read = 0) as unread_count
        FROM user u
        WHERE u.id IN (SELECT DISTINCT user_id FROM chat_messages)
        ORDER BY last_message_time DESC, unread_count DESC, u.username ASC";
    $users_result = mysqli_query($conn, $users_query);
    $users = [];
    while ($user = mysqli_fetch_assoc($users_result)) {
        $users[] = $user;
    }
    header('Content-Type: application/json');
    echo json_encode(['success' => true, 'users' => $users]);
    exit();
}

if (
    isset($_GET['action']) && $_GET['action'] === 'get_ticket_notifications'
    && isset($_SESSION['admin_id'])
    && !empty($_SERVER['HTTP_X_REQUESTED_WITH'])
    && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest'
) {
    $user_id = intval($_GET['user_id'] ?? 0);
    $tickets = [];
    if ($user_id) {
        $tickets_query = "SELECT st.id,
            (SELECT COUNT(*) FROM chat_messages cm
             WHERE cm.ticket_id = st.id
             AND cm.sender_type = 'user'
             AND cm.is_read = 0) as unread_count
            FROM support_tickets st
            WHERE st.user_id = $user_id";
        $tickets_result = mysqli_query($conn, $tickets_query);
        while ($ticket = mysqli_fetch_assoc($tickets_result)) {
            $tickets[] = [
                'id' => $ticket['id'],
                'unread_count' => (int)$ticket['unread_count']
            ];
        }
    }
    header('Content-Type: application/json');
    echo json_encode(['success' => true, 'tickets' => $tickets]);
    exit();
}

// --- AJAX endpoint for global notifications (all users) ---
if (
    isset($_GET['action']) && $_GET['action'] === 'get_global_notifications'
    && isset($_SESSION['admin_id'])
    && !empty($_SERVER['HTTP_X_REQUESTED_WITH'])
    && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest'
) {
    // Count total unread messages from all users
    $global_query = "SELECT COUNT(*) as total_unread FROM chat_messages
                     WHERE sender_type = 'user' AND is_read = 0";
    $global_result = mysqli_query($conn, $global_query);
    $global_data = mysqli_fetch_assoc($global_result);
    $total_unread = (int)$global_data['total_unread'];

    header('Content-Type: application/json');
    echo json_encode(['success' => true, 'total_unread' => $total_unread]);
    exit();
}

// --- AJAX endpoint for fetching new messages (live chat) ---
if (
    isset($_GET['action']) && $_GET['action'] === 'get_new_messages'
    && isset($_SESSION['admin_id'])
    && !empty($_SERVER['HTTP_X_REQUESTED_WITH'])
    && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest'
) {
    $user_id = isset($_GET['user_id']) ? intval($_GET['user_id']) : 0;
    $last_message_id = isset($_GET['last_id']) ? intval($_GET['last_id']) : 0;

    if ($user_id) {
        // Get new messages for this user
        $messages_query = "SELECT cm.*,
                          CASE
                              WHEN cm.sender_type = 'admin' THEN a.username
                              WHEN cm.sender_type = 'user' THEN u.username
                          END as sender_name
                          FROM chat_messages cm
                          LEFT JOIN admin_users a ON cm.sender_id = a.id AND cm.sender_type = 'admin'
                          LEFT JOIN user u ON cm.sender_id = u.id AND cm.sender_type = 'user'
                          WHERE cm.user_id = ? AND cm.id > ?
                          ORDER BY cm.created_at ASC";

        $stmt = mysqli_prepare($conn, $messages_query);
        mysqli_stmt_bind_param($stmt, 'ii', $user_id, $last_message_id);
        mysqli_stmt_execute($stmt);
        $result = mysqli_stmt_get_result($stmt);

        $messages = [];
        while ($message = mysqli_fetch_assoc($result)) {
            // Format time for display
            $message['formatted_time'] = showCustomerTime($message['created_at'], 'g:i A');
            $messages[] = $message;
        }
        mysqli_stmt_close($stmt);

        header('Content-Type: application/json');
        echo json_encode([
            'success' => true,
            'messages' => $messages
        ]);
    } else {
        header('Content-Type: application/json');
        echo json_encode([
            'success' => false,
            'error' => 'Invalid user ID'
        ]);
    }
    exit();
}
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin - Live Chat</title>
    <link rel="shortcut icon" href="../image/png/favicon.png" type="image/x-icon">
    <!-- Bootstrap, fonts & icons -->
    <link rel="stylesheet" href="../css/bootstrap.css">
    <link rel="stylesheet" href="../fonts/icon-font/css/style.css">
    <link rel="stylesheet" href="../fonts/typography-font/typo.css">
    <link rel="stylesheet" href="../fonts/fontawesome-5/css/all.css">
    <!-- Vendor stylesheets -->
    <link rel="stylesheet" href="../css/main.css">
    <style>
    body {
        background-color: #f8f9fa;
    }

    .admin-container {
        padding: 20px;
        max-width: 100%;
        overflow-x: hidden;
    }

    /* Notification styling */
    .notification {
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 15px 20px;
        border-radius: 4px;
        color: white;
        font-weight: 500;
        z-index: 9999;
        opacity: 0;
        transform: translateY(-20px);
        transition: all 0.3s ease;
        max-width: 350px;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    .notification.show {
        opacity: 1;
        transform: translateY(0);
    }

    .notification.success {
        background-color: #28a745;
    }

    .notification.danger {
        background-color: #dc3545;
    }

    .notification.info {
        background-color: #17a2b8;
    }

    .notification.warning {
        background-color: #ffc107;
        color: #212529;
    }

    @media (max-width: 767px) {
        .notification {
            top: 10px;
            right: 10px;
            left: 10px;
            max-width: none;
        }
    }

    .admin-header {
        background-color: #fff;
        padding: 15px 20px;
        border-radius: 5px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
    }

    /* Responsive container */
    @media (max-width: 991px) {
        .admin-container {
            padding: 15px;
        }

        .admin-header {
            padding: 12px 15px;
        }
    }

    @media (max-width: 767px) {
        .admin-container {
            padding: 10px;
        }

        .admin-header {
            padding: 10px;
        }
    }

    .admin-header h1 {
        margin: 0;
        font-size: 24px;
        color: #333;
    }

    .admin-user {
        display: flex;
        align-items: center;
    }

    .admin-user span {
        margin-right: 10px;
    }

    .admin-sidebar {
        background-color: #fff;
        border-radius: 5px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        padding: 20px;
        height: calc(100vh - 100px);
    }

    .admin-content {
        background-color: #fff;
        border-radius: 5px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        padding: 20px;
        height: calc(100vh - 100px);
        overflow-y: auto;
        overflow-x: hidden;
    }

    .chat-container {
        display: flex;
        height: 100%;
        width: 100%;
        position: relative;
    }

    @media (max-width: 991px) {
        .admin-content {
            padding: 15px;
            height: calc(100vh - 90px);
        }
    }

    @media (max-width: 767px) {
        .admin-content {
            padding: 10px;
            height: calc(100vh - 80px);
        }

        .chat-container {
            flex-direction: column;
        }
    }

    .chat-sidebar {
        width: 300px;
        border-right: 1px solid #e5e5e5;
        height: 100%;
        overflow-y: auto;
        transition: all 0.3s ease;
    }

    .chat-sidebar-header {
        padding: 15px;
        border-bottom: 1px solid #e5e5e5;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .chat-sidebar-header h3 {
        margin: 0;
        font-size: 18px;
    }

    .mobile-toggle-chat-sidebar {
        display: none;
        background: none;
        border: none;
        color: #473BF0;
        font-size: 18px;
        cursor: pointer;
    }

    @media (max-width: 991px) {
        .chat-sidebar {
            width: 250px;
        }
    }

    @media (max-width: 767px) {
        .chat-sidebar {
            width: 100%;
            height: 300px;
            border-right: none;
            border-bottom: 1px solid #e5e5e5;
        }

        .mobile-toggle-chat-sidebar {
            display: block;
        }

        .chat-sidebar.collapsed {
            height: 60px;
            overflow: hidden;
        }
    }

    @media (max-width: 480px) {
        .chat-sidebar {
            height: 250px;
        }
    }

    .chat-list {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .chat-list-item {
        padding: 15px;
        border-bottom: 1px solid #f0f0f0;
        cursor: pointer;
        transition: background-color 0.2s;
        position: relative;
    }

    .chat-list-item:hover {
        background-color: #f8f9fa;
    }

    .chat-list-item.active {
        background-color: #e9ecef;
    }

    .chat-list-item-name {
        font-weight: 600;
        margin-bottom: 5px;
        display: flex;
        justify-content: space-between;
    }

    .chat-list-item-email {
        font-size: 12px;
        color: #6c757d;
    }

    .unread-badge {
        background-color: #473BF0;
        color: white;
        border-radius: 50%;
        min-width: 20px;
        height: 20px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        padding: 0 5px;
    }

    .ticket-notification-badge {
        background-color: #dc3545;
        color: white;
        border-radius: 50%;
        min-width: 18px;
        height: 18px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        font-size: 11px;
        font-weight: bold;
        margin-left: 5px;
        border: 2px solid white;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        animation: notificationPulse 0.5s ease-in-out;
        vertical-align: middle;
    }

    @keyframes notificationPulse {
        0% {
            transform: scale(0.8);
            opacity: 0.7;
        }
        50% {
            transform: scale(1.1);
            opacity: 1;
        }
        100% {
            transform: scale(1);
            opacity: 1;
        }
    }

    .ticket-item {
        position: relative;
    }

    .chat-main {
        flex: 1;
        display: flex;
        flex-direction: column;
        height: 100%;
        min-height: 0;
        /* Important for flex child to respect parent height */
    }

    .chat-header {
        padding: 15px;
        border-bottom: 1px solid #e5e5e5;
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
        position: relative;
    }

    /* Ensure buttons have proper spacing */
    .chat-header-actions .btn {
        margin-bottom: 0;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    /* Profile action styling */
    .profile-action {
        margin-left: auto;
        margin-right: 10px;
    }

    @media (max-width: 767px) {
        .chat-main {
            height: calc(100% - 300px);
        }

        .chat-header {
            padding: 10px;
        }
    }

    @media (max-width: 480px) {
        .chat-main {
            height: calc(100% - 250px);
        }
    }

    .chat-header-user {
        display: flex;
        align-items: center;
        margin-bottom: 5px;
    }

    .chat-header-user-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background-color: #473BF0;
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
        margin-right: 10px;
        flex-shrink: 0;
    }

    .chat-header-user-info {
        min-width: 0;
        overflow: hidden;
    }

    .chat-header-user-info h4 {
        margin: 0;
        font-size: 16px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .chat-header-user-info p {
        margin: 0;
        font-size: 12px;
        color: #6c757d;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .chat-header-actions {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        justify-content: flex-start;
        width: 100%;
        margin-top: 10px;
    }

    .ticket-actions {
        display: flex;
        align-items: center;
        padding-left: 15px;
    }

    .chat-header-actions a {
        margin-left: 10px;
        color: #6c757d;
        text-decoration: none;
    }

    .chat-header-actions .btn-group {
        display: flex;
        flex-wrap: nowrap;
        margin-right: 10px;
    }

    .chat-header-actions .btn-group .btn {
        border-radius: 0;
        transition: all 0.2s ease;
    }

    .chat-header-actions .btn-group .btn:first-child {
        border-top-left-radius: 4px;
        border-bottom-left-radius: 4px;
    }

    .chat-header-actions .btn-group .btn:last-child {
        border-top-right-radius: 4px;
        border-bottom-right-radius: 4px;
    }

    /* Specific hover styles for Resolve and Close buttons */
    .btn-outline-success:hover {
        background-color: #28a745;
        border-color: #28a745;
    }

    .btn-outline-secondary:hover {
        background-color: #6c757d;
        border-color: #6c757d;
    }

    .btn-outline-danger:hover {
        background-color: #dc3545;
        border-color: #dc3545;
        color: #fff !important;
    }

    .chat-header-actions .btn {
        white-space: nowrap;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    @media (max-width: 767px) {
        .chat-header {
            flex-direction: column;
            align-items: flex-start;
        }

        .chat-header-user {
            width: 100%;
            margin-bottom: 10px;
        }

        .profile-action {
            position: absolute;
            top: 15px;
            right: 15px;
            margin-right: 0;
        }

        .chat-header-actions {
            margin-top: 10px;
            width: 100%;
            justify-content: center;
            /* Center on mobile */
        }

        .ticket-actions {
            width: 100%;
            justify-content: center;
            padding-left: 0;
        }

        .chat-header-actions .btn-group {
            margin-right: 0 !important;
            margin-bottom: 5px;
        }

        .chat-header-actions .btn {
            padding: 5px 10px;
            font-size: 13px;
            height: auto;
        }

        .chat-header-actions .btn i {
            margin-right: 5px;
            font-size: 12px;
        }
    }

    @media (max-width: 480px) {

        /* Keep text visible on mobile */
        .chat-header-actions .btn span {
            display: inline-block;
            font-size: 12px;
        }

        .chat-header-actions .btn i {
            margin-right: 3px;
            font-size: 12px;
        }

        .chat-header-actions .btn {
            min-width: auto;
            padding: 4px 8px;
            margin-bottom: 5px;
        }

        .profile-action {
            top: 10px;
            right: 10px;
        }

        .profile-action .btn {
            padding: 4px 8px;
            font-size: 12px;
        }

        .profile-action .btn span {
            display: none;
        }

        .profile-action .btn i {
            margin-right: 0;
        }
    }

    @media (max-width: 767px) {
        .chat-header-user {
            margin-right: 10px;
        }

        .chat-header-actions {
            margin-top: 5px;
        }

        .chat-header-actions .btn {
            padding: 4px 8px;
            font-size: 12px;
        }
    }

    @media (max-width: 480px) {
        .chat-header-user-avatar {
            width: 32px;
            height: 32px;
            font-size: 14px;
        }

        .chat-header-user-info h4 {
            font-size: 14px;
        }

        .chat-header-user-info p {
            font-size: 11px;
        }
    }

    .chat-messages {
        flex: 1;
        padding: 15px;
        overflow-y: auto;
        background-color: #f8f9fa;
        min-height: 0;
        /* Important for flex child to respect parent height */
    }

    .chat-message {
        margin-bottom: 15px;
        display: flex;
        flex-direction: column;
    }

    .chat-message-content {
        max-width: 80%;
        padding: 10px 15px;
        border-radius: 18px;
        position: relative;
        word-wrap: break-word;
    }

    .chat-message-time {
        font-size: 11px;
        color: #6c757d;
        margin-top: 5px;
    }

    .chat-message.outgoing {
        align-items: flex-end;
    }

    .chat-message.incoming {
        align-items: flex-start;
    }

    .chat-message.outgoing .chat-message-content {
        background-color: #473BF0;
        color: white;
        border-bottom-right-radius: 5px;
    }

    .chat-message.incoming .chat-message-content {
        background-color: #e9ecef;
        color: #212529;
        border-bottom-left-radius: 5px;
    }

    .chat-input {
        padding: 15px;
        border-top: 1px solid #e5e5e5;
        display: flex;
        align-items: center;
    }

    .chat-input form {
        display: flex;
        width: 100%;
        align-items: center;
    }

    .chat-input input,
    .chat-input textarea {
        flex: 1;
        padding: 10px 15px;
        border: 1px solid #ced4da;
        border-radius: 30px;
        outline: none;
        font-size: 14px;
        font-family: inherit;
        resize: none;
        min-height: 40px;
        max-height: 120px;
        overflow-y: auto;
    }

    .chat-input button {
        margin-left: 10px;
        border: none;
        background-color: #473BF0;
        color: white;
        border-radius: 30px;
        padding: 10px 20px;
        cursor: pointer;
        transition: background-color 0.2s;
        white-space: nowrap;
        display: flex;
        align-items: center;
        justify-content: center;
        align-self: center;
        height: 40px;
        flex-shrink: 0;
    }

    .chat-input button:hover {
        background-color: #3730c0;
    }

    .chat-input button i {
        margin-right: 5px;
    }

    @media (max-width: 991px) {
        .chat-messages {
            padding: 12px;
        }

        .chat-message-content {
            max-width: 85%;
            padding: 8px 12px;
        }
    }

    @media (max-width: 767px) {
        .chat-messages {
            padding: 10px;
        }

        .chat-input {
            padding: 10px;
        }

        .chat-input input,
        .chat-input textarea {
            padding: 8px 12px;
            font-size: 14px;
        }

        .chat-input button {
            padding: 8px 12px;
            font-size: 14px;
        }

        .chat-input button span {
            display: none;
        }

        .chat-input button i {
            margin-right: 0;
        }
    }

    @media (max-width: 480px) {
        .chat-message-content {
            max-width: 90%;
            padding: 8px 10px;
            font-size: 14px;
        }

        .chat-message-time {
            font-size: 10px;
        }
    }

    .chat-empty {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100%;
        color: #6c757d;
    }

    .chat-empty i {
        font-size: 48px;
        margin-bottom: 15px;
        color: #dee2e6;
    }

    .ticket-info {
        padding: 15px;
        background-color: #f8f9fa;
        border-bottom: 1px solid #e5e5e5;
    }

    .ticket-header {
        cursor: pointer;
    }

    .ticket-info h5 {
        margin-top: 0;
        margin-bottom: 10px;
        font-size: 14px;
        color: #6c757d;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .ticket-toggle-btn {
        background: none;
        border: none;
        color: #473BF0;
        font-size: 14px;
        cursor: pointer;
        padding: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 24px;
        height: 24px;
        transition: transform 0.3s ease;
    }

    .ticket-toggle-btn.collapsed i {
        transform: rotate(-90deg);
    }

    .ticket-list {
        list-style: none;
        padding: 0;
        margin: 0;
        max-height: 150px;
        overflow-y: auto;
        transition: max-height 0.3s ease, opacity 0.3s ease;
        -webkit-overflow-scrolling: touch;
        /* Smooth scrolling on iOS */
        scrollbar-width: thin;
        /* Firefox */
        scrollbar-color: #473BF0 #f0f0f0;
        /* Firefox */
    }

    /* Webkit scrollbar styling */
    .ticket-list::-webkit-scrollbar {
        width: 6px;
    }

    .ticket-list::-webkit-scrollbar-track {
        background: #f0f0f0;
        border-radius: 10px;
    }

    .ticket-list::-webkit-scrollbar-thumb {
        background: #473BF0;
        border-radius: 10px;
    }

    .ticket-list.collapsed {
        max-height: 0;
        overflow: hidden;
        opacity: 0;
        margin: 0;
    }

    .ticket-item {
        padding: 8px 10px;
        border-radius: 5px;
        margin-bottom: 8px;
        background-color: #fff;
        border: 1px solid #e5e5e5;
        font-size: 13px;
        cursor: pointer;
        transition: background-color 0.2s;
    }

    .ticket-list .ticket-item:last-child {
        margin-bottom: 5px;
    }

    .ticket-item-content {
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        width: 100%;
    }

    .ticket-id {
        font-weight: 600;
        margin-right: 5px;
        white-space: nowrap;
    }

    .ticket-subject {
        flex: 1;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        margin-right: 5px;
    }

    .ticket-badges {
        display: flex;
        flex-wrap: wrap;
        gap: 5px;
        margin-top: 3px;
    }

    .ticket-item:hover {
        background-color: #e9ecef;
    }

    .ticket-item.active {
        background-color: #473BF0;
        color: white;
        border-color: #473BF0;
    }

    .ticket-item .badge {
        margin-left: 0;
    }

    @media (max-width: 991px) {
        .ticket-info {
            padding: 12px;
        }

        .ticket-list {
            max-height: 120px;
        }

        .ticket-toggle-btn {
            width: 22px;
            height: 22px;
        }
    }

    @media (max-width: 767px) {
        .ticket-info {
            padding: 10px;
        }

        .ticket-item {
            padding: 6px 8px;
            font-size: 12px;
        }

        .ticket-info h5 {
            font-size: 13px;
        }

        .ticket-toggle-btn {
            width: 20px;
            height: 20px;
            font-size: 12px;
        }

        /* Default collapsed on mobile */
        .ticket-list {
            max-height: 0;
            overflow: hidden;
            opacity: 0;
            margin: 0;
        }

        .ticket-list.expanded {
            max-height: 200px;
            opacity: 1;
            margin-top: 10px;
            overflow-y: auto;
            -webkit-overflow-scrolling: touch;
        }
    }

    @media (max-width: 480px) {
        .ticket-list.expanded {
            max-height: 150px;
            overflow-y: auto;
            -webkit-overflow-scrolling: touch;
        }

        .ticket-item {
            margin-bottom: 3px;
        }

        .ticket-badges {
            width: 100%;
            margin-top: 5px;
            justify-content: flex-start;
        }
    }

    .badge {
        font-size: 14px;
        padding: 5px 8px;
        border-radius: 4px;
        font-weight: 500;
        display: inline-flex;
        align-items: center;
        justify-content: center;
    }

    @media (max-width: 767px) {
        .badge {
            font-size: 12px;
            padding: 4px 6px;
        }
    }

    @media (max-width: 480px) {
        .badge {
            font-size: 11px;
            padding: 3px 5px;
        }
    }

    .badge-starter {
        background-color: #fbbf24;
        color: #fff !important;
    }

    .badge-premium,
    .badge-business {
        background-color: #fd7e14;
        color: #fff !important;
    }

    .badge-ultimate {
        background-color: #007BFF;
        color: #fff !important;
    }

    .badge-open {
        background-color: #fd7e14;
        color: #fff;
    }

    .badge-in_progress {
        background-color: #0d6efd;
        color: #fff;
    }

    .badge-resolved {
        background-color: #28a745;
        color: #fff;
    }

    .badge-closed {
        background-color: #6c757d;
        color: #fff;
    }

    /* Priority badges */
    .badge-low {
        background-color: #28a745;
        color: #fff;
    }

    .badge-medium {
        background-color: #ffc107;
        color: #fff;
    }

    .badge-high {
        background-color: #fd7e14;
        color: #fff;
    }

    .badge-urgent {
        background-color: #dc3545;
        color: #fff;
        font-weight: bold;
    }

    .badge-critical {
        background-color: #dc3545;
        color: #fff;
        font-weight: bold;
    }

    /* Additional priority badges for your system */
    .badge-information {
        background-color: #17a2b8;
        color: #fff;
    }

    .badge-normal {
        background-color: #ffc107;
        color: #fff;
    }

    .badge-important {
        background-color: #fd7e14;
        color: #fff;
        font-weight: bold;
    }

    .btn {
        font-size: 14px;
        font-weight: 500;
        padding: 8px 16px;
        border-radius: 5px;
        transition: all 0.3s ease;
    }

    .btn-primary {
        background-color: #473BF0;
        border-color: #473BF0;
    }

    .btn-primary:hover {
        background-color: #3730c0;
        border-color: #3730c0;
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    .btn-outline-primary {
        color: #473BF0;
        border-color: #473BF0;
    }

    /* Button hover styles */
    .btn-outline-primary:hover,
    .btn-outline-success:hover,
    .btn-outline-secondary:hover {
        color: #fff !important;
    }

    .btn-outline-primary:hover {
        background-color: #473BF0;
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    /* User dropdown styles */
    .user-dropdown {
        position: relative;
        display: inline-block;
    }

    .user-info {
        cursor: pointer;
        display: flex;
        align-items: center;
    }

    .mobile-menu-toggle {
        margin-left: 5px;
        transition: transform 0.3s ease;
        display: none;
    }

    .mobile-menu-toggle.active {
        transform: rotate(180deg);
    }

    .dropdown-content {
        display: block;
    }

    /* Mobile Styles */
    @media (max-width: 767px) {
        .admin-header {
            flex-direction: column;
            align-items: flex-start;
        }

        .admin-header h1 {
            margin-bottom: 10px;
        }

        .admin-user {
            width: 100%;
        }

        .user-dropdown {
            width: 100%;
        }

        .user-info {
            width: 100%;
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
        }

        .mobile-menu-toggle {
            display: inline-block;
        }

        .dropdown-content {
            display: none;
            position: absolute;
            background-color: #f9f9f9;
            width: 100%;
            left: 0;
            box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.2);
            z-index: 1;
            border-radius: 4px;
            margin-top: 5px;
            padding: 8px;
        }

        .dropdown-content .btn {
            width: 100%;
            text-align: center;
        }

        .user-dropdown.active .dropdown-content {
            display: block;
        }
    }
    </style>
</head>

<body>
    <div class="admin-container">
        <div class="admin-header">
            <h1>Live Chat Support</h1>
            <div class="admin-user">
                <div class="user-dropdown">
                    <span class="user-info">
                        Welcome, <?php echo htmlspecialchars($admin_username); ?>
                        (<?php echo htmlspecialchars($admin_role); ?>)
                        <i class="mobile-menu-toggle fas fa-chevron-down"></i>
                    </span>
                    <div class="dropdown-content">
                        <a href="admin-logout.php" class="btn btn-sm btn-outline-danger">Logout</a>
                    </div>
                </div>
            </div>
        </div>


        <div class="row">
            <!-- User sidebar -->
            <div class="col-lg-2 col-md-3 col-sm-12">
                <?php include('admin-menu.php'); ?>
                <style>
                .admin-sidebar {
                    background-color: #fff;
                    border-radius: 5px;
                    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
                    padding: 20px;
                    height: calc(100vh - 100px);
                    overflow-y: auto;
                }

                .admin-sidebar ul {
                    list-style: none;
                    padding: 0;
                    margin: 0;
                }

                .admin-sidebar ul li {
                    margin-bottom: 5px;
                }

                .admin-sidebar ul li a {
                    display: block;
                    padding: 10px 15px;
                    color: #333;
                    text-decoration: none;
                    border-radius: 5px;
                    transition: all 0.3s;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }

                .admin-sidebar ul li a:hover,
                .admin-sidebar ul li a.active {
                    background-color: #473BF0;
                    color: #fff;
                }

                .admin-sidebar ul li a i {
                    margin-right: 10px;
                    width: 20px;
                    text-align: center;
                }

                @media (max-width: 991px) {
                    .admin-sidebar {
                        padding: 15px;
                        height: calc(100vh - 90px);
                    }

                    .admin-sidebar ul li a {
                        padding: 8px 12px;
                        font-size: 14px;
                    }
                }

                @media (max-width: 767px) {
                    .admin-sidebar {
                        height: auto;
                        margin-bottom: 15px;
                    }

                    .admin-sidebar ul {
                        display: flex;
                        flex-wrap: wrap;
                    }

                    .admin-sidebar ul li {
                        margin-right: 5px;
                        margin-bottom: 5px;
                        width: calc(50% - 5px);
                    }

                    .admin-sidebar ul li a {
                        padding: 8px 10px;
                        font-size: 13px;
                        text-align: center;
                    }

                    .admin-sidebar ul li a i {
                        margin-right: 5px;
                        width: auto;
                    }
                }

                @media (max-width: 480px) {
                    .admin-sidebar ul li {
                        width: 100%;
                        margin-right: 0;
                    }
                }
                </style>
            </div>

            <div class="col-lg-10 col-md-9 col-sm-12">
                <div class="admin-content">
                    <div class="chat-container">
                        <!-- Chat Sidebar - User List -->
                        <div class="chat-sidebar">
                            <div class="chat-sidebar-header">
                                <h3>Conversations</h3>
                                <button type="button" class="mobile-toggle-chat-sidebar" id="toggleChatSidebar">
                                    <i class="fas fa-chevron-down"></i>
                                </button>
                            </div>
                            <ul class="chat-list">
                                <?php if (mysqli_num_rows($users_result) > 0): ?>
                                <?php while ($user = mysqli_fetch_assoc($users_result)): ?>
                                <li class="chat-list-item <?php echo $chat_user_id == $user['id'] ? 'active' : ''; ?>"
                                    data-user-id="<?php echo $user['id']; ?>"
                                    onclick="selectUser(<?php echo $user['id']; ?>)">
                                    <div class="chat-list-item-name">
                                        <span><?php echo htmlspecialchars($user['username']); ?></span>
                                        <?php if ($user['unread_count'] > 0): ?>
                                        <span class="unread-badge"><?php echo $user['unread_count']; ?></span>
                                        <?php endif; ?>
                                    </div>
                                    <div class="chat-list-item-email"><?php echo htmlspecialchars($user['email']); ?>
                                    </div>
                                </li>
                                <?php endwhile; ?>
                                <?php else: ?>
                                <li class="chat-list-item">No users found</li>
                                <?php endif; ?>
                            </ul>
                        </div>

                        <!-- Chat Main Area -->
                        <div class="chat-main">
                            <?php if (isset($_GET['error'])): ?>
                            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                                Error: <?php echo htmlspecialchars($_GET['error']); ?>
                                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                                    <span aria-hidden="true">&times;</span>
                                </button>
                            </div>
                            <?php endif; ?>

                            <?php if (isset($_GET['success']) && isset($_GET['message'])): ?>
                            <div class="alert alert-success alert-dismissible fade show" role="alert">
                                <?php echo htmlspecialchars($_GET['message']); ?>
                                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                                    <span aria-hidden="true">&times;</span>
                                </button>
                            </div>
                            <?php endif; ?>

                            <?php if ($chat_user_id && isset($user_info)): ?>
                            <!-- Chat Header -->
                            <div class="chat-header">
                                <div class="chat-header-user">
                                    <div class="chat-header-user-avatar">
                                        <?php echo strtoupper(substr($user_info['username'], 0, 1)); ?>
                                    </div>
                                    <div class="chat-header-user-info">
                                        <h4><?php echo htmlspecialchars($user_info['username']); ?></h4>
                                        <p><?php echo htmlspecialchars($user_info['email']); ?></p>
                                    </div>
                                </div>

                                <div class="profile-action">
                                    <a href="admin-user-detail.php?id=<?php echo $chat_user_id; ?>"
                                        title="View User Profile" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-user"></i> <span> &nbsp; View Profile</span>
                                    </a>
                                </div>

                                <div class="chat-header-actions">
                                    <div class="ticket-actions">
                                        <?php if ($ticket_id): ?>
                                        <div class="alert alert-success mb-0">
                                            <i class="fas fa-comments mr-2"></i>
                                            <strong>Live Chat:</strong> Direct communication with users - no tickets required.
                                        </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>

                            <!-- Ticket Information -->
                            <?php if (mysqli_num_rows($user_tickets_result) > 0): ?>
                            <div class="ticket-info">
                                <div class="ticket-header" id="ticketHeader">
                                    <h5>
                                        <span>User's Tickets</span>
                                        <button type="button" class="ticket-toggle-btn" id="toggleTicketList">
                                            <i class="fas fa-chevron-down"></i>
                                        </button>
                                    </h5>
                                </div>
                                <ul class="ticket-list" id="ticketList">
                                    <?php while ($user_ticket = mysqli_fetch_assoc($user_tickets_result)): ?>
                                    <?php
                                                // Display 'Business' for premium tickets
                                                $ticketType = $user_ticket['ticket_type'];
                                                $badgeClass = $ticketType;
                                                $displayText = ucfirst($ticketType);

                                                if (strtolower($ticketType) == 'premium') {
                                                    $displayText = 'Business';
                                                }
                                                ?>
                                    <li class="ticket-item <?php echo $ticket_id == $user_ticket['id'] ? 'active' : ''; ?>" id="ticket-item-<?php echo $user_ticket['id']; ?>"
                                        onclick="window.location.href='admin-chat.php?user_id=<?php echo $chat_user_id; ?>&ticket_id=<?php echo $user_ticket['id']; ?>'">
                                        <div class="ticket-item-content">
                                            <span class="ticket-id">#<?php echo $user_ticket['id']; ?>:</span>
                                            <span
                                                class="ticket-subject"><?php echo htmlspecialchars(substr($user_ticket['subject'], 0, 30)); ?></span>
                                            <div class="ticket-badges">
                                                <span
                                                    class="badge badge-<?php echo $user_ticket['status']; ?>"><?php echo ucfirst($user_ticket['status']); ?></span>
                                                <span
                                                    class="badge badge-<?php echo $badgeClass; ?>"><?php echo $displayText; ?></span>
                                                <?php if ($user_ticket['unread_count'] > 0): ?>
                                                <span id="ticket-notification-<?php echo $user_ticket['id']; ?>"
                                                    class="ticket-notification-badge">
                                                    <?php echo $user_ticket['unread_count'] >= 10 ? '9+' : $user_ticket['unread_count']; ?>
                                                </span>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </li>
                                    <?php endwhile; ?>
                                </ul>
                            </div>
                            <?php endif; ?>

                            <!-- Chat Messages -->
                            <div class="chat-messages" id="chatMessages">
                                <?php foreach ($messages as $message): ?>
                                <div
                                    class="chat-message <?php echo $message['sender_type'] == 'admin' ? 'outgoing' : 'incoming'; ?>">
                                    <div class="chat-message-content">
                                        <?php echo nl2br(htmlspecialchars($message['message'])); ?>
                                    </div>
                                    <div class="chat-message-time">
                                        <?php echo date('M d, H:i', strtotime($message['created_at'])); ?> -
                                        <?php echo htmlspecialchars($message['sender_name']); ?>
                                    </div>
                                </div>
                                <?php endforeach; ?>
                            </div>

                            <!-- Chat Input -->
                            <div class="chat-input">
                                <form id="chatForm" method="post" onsubmit="sendMessage(event)">
                                    <input type="hidden" id="user_id" name="user_id"
                                        value="<?php echo $chat_user_id; ?>">
                                    <textarea id="message" name="message" placeholder="Type a message... (Shift+Enter for new line)"
                                        required autocomplete="off" rows="1" onkeydown="handleMessageKeydown(event)"
                                        oninput="autoResizeTextarea(this)"></textarea>
                                    <button type="submit">
                                        <i class="fas fa-paper-plane"></i> &nbsp; Send
                                    </button>
                                </form>
                            </div>
                            <?php else: ?>
                            <!-- Empty State -->
                            <div class="chat-empty">
                                <i class="fas fa-comments"></i>
                                <p>Select a user to start chatting</p>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="../js/vendor.min.js"></script>
    <script src="../js/chat-notifications.js"></script>

    <!-- Define selectUser function immediately for onclick handlers -->
    <script>
        function selectUser(userId) {
            console.log('Early selectUser called with userId:', userId);
            window.location.href = `admin-chat.php?user_id=${userId}`;
        }
        window.selectUser = selectUser;
        console.log('Early selectUser function loaded');
    </script>

    <script>
    // selectUser function already defined above for immediate availability

    // Scroll to bottom of chat messages
    function scrollToBottom() {
        const chatMessages = document.getElementById('chatMessages');
        if (chatMessages) {
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }
    }

    // Format admin message time
    function formatAdminMessageTime(utcTimeString) {
        try {
            // Parse UTC time and convert to local timezone
            const utcDate = new Date(utcTimeString + 'Z'); // Add Z to indicate UTC

            // Format in admin's local timezone
            return utcDate.toLocaleString('en-US', {
                month: 'short',
                day: 'numeric',
                hour: 'numeric',
                minute: '2-digit',
                hour12: true
            });
        } catch (error) {
            console.error('Admin time formatting error:', error);
            return utcTimeString;
        }
    }

    // Format message HTML
    function formatMessage(message) {
        const messageType = message.sender_type === 'admin' ? 'outgoing' : 'incoming';
        const senderName = message.sender_name || (message.sender_type === 'admin' ? 'Admin' : 'User');
        const time = message.formatted_time || new Date(message.created_at).toLocaleString();

        return `
                <div class="chat-message ${messageType}">
                    <div class="chat-message-content">
                        ${message.message.replace(/\n/g, '<br>')}
                    </div>
                    <div class="chat-message-time">
                        ${time} - ${senderName}
                    </div>
                </div>
            `;
    }

    // Get current messages count
    let currentMessagesCount = <?php echo count($messages); ?>;
    console.log('Initial message count:', currentMessagesCount);

    // Function to fetch new messages
    function fetchMessages() {
        <?php if ($chat_user_id): ?>
        // Auto-detect environment for URL paths
        const isLocalhost = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';
        const urlBase = isLocalhost ? '/helloit' : '';

        fetch(urlBase + '/merlion/get-messages?user_id=<?php echo $chat_user_id; ?>' + (
                <?php echo $ticket_id ? 'true' : 'false'; ?> ?
                '&ticket_id=<?php echo $ticket_id; ?>' : ''))
            .then(response => response.json())
            .then(data => {
                // Debug log to help troubleshoot
                console.log('Fetched messages:', data.messages.length, 'Current count:', currentMessagesCount);

                if (data.success && data.messages.length > currentMessagesCount) {
                    // Get chat container
                    const chatMessages = document.getElementById('chatMessages');

                    // Check if there are new messages from the user (not from admin)
                    let hasNewUserMessages = false;

                    // Add only new messages
                    for (let i = currentMessagesCount; i < data.messages.length; i++) {
                        const message = data.messages[i];
                        const messageHTML = formatMessage(message);

                        // Check if chat is empty (has the "Select a user" placeholder)
                        const chatEmpty = chatMessages.querySelector('.chat-empty');
                        if (chatEmpty && i === 0) {
                            // Replace the empty state with the first message
                            chatMessages.innerHTML = messageHTML;
                        } else {
                            // Append to existing messages
                            chatMessages.innerHTML += messageHTML;
                        }

                        // Check if this is a user message (not from admin)
                        if (message.sender_type === 'user') {
                            hasNewUserMessages = true;
                        }
                    }

                    // Play notification sound and show browser notification for new user messages
                    if (hasNewUserMessages && window.chatNotificationSystem) {
                        // Initialize notification system if not already done
                        if (!window.chatNotificationSystem.isInitialized) {
                            window.chatNotificationSystem.initialize();
                        }

                        const username = data.messages[currentMessagesCount].sender_name || 'User';
                        const message = data.messages[currentMessagesCount].message || 'New message';

                        // Notify with the first new message
                        window.chatNotificationSystem.notify(
                            message.substring(0, 100) + (message.length > 100 ? '...' : ''), {
                                title: `New message from ${username}`,
                                sender: username
                            }
                        );

                        // Update ticket notifications when new user messages arrive
                        setTimeout(updateTicketNotifications, 500);
                    }

                    // Update count and scroll to bottom
                    currentMessagesCount = data.messages.length;
                    scrollToBottom();
                }
            })
            .catch(error => console.error('Error checking for new messages:', error));
        <?php endif; ?>
    }

    // Handle Shift+Enter for line breaks and Enter for sending
    function handleMessageKeydown(event) {
        if (event.key === 'Enter') {
            if (event.shiftKey) {
                // Shift+Enter: Allow line break (default behavior)
                return true;
            } else {
                // Enter: Send message
                event.preventDefault();
                sendMessage(event);
                return false;
            }
        }
    }

    // Auto-resize textarea based on content
    function autoResizeTextarea(textarea) {
        textarea.style.height = 'auto';
        textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px'; // Max height of 120px
    }

    // Function to send message via AJAX
    function sendMessage(event) {
        event.preventDefault();

        const messageInput = document.getElementById('message');
        const message = messageInput.value.trim();
        const userId = document.getElementById('user_id').value;

        if (message === '') return;

        // Create form data for live chat
        const formData = new FormData();
        formData.append('message', message);
        formData.append('user_id', userId);

        // Send message via AJAX to current page
        fetch('admin-chat.php', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                // Always clear input field and reset height
                messageInput.value = '';
                messageInput.style.height = 'auto';

                if (data.success) {
                    // Add new message to chat if message data is available
                    if (data.message) {
                        const chatMessages = document.getElementById('chatMessages');
                        const messageHTML = formatMessage(data.message);

                        // Check if chat is empty (has the "Select a user" placeholder)
                        const chatEmpty = chatMessages.querySelector('.chat-empty');
                        if (chatEmpty) {
                            // Replace the empty state with the first message
                            chatMessages.innerHTML = messageHTML;
                        } else {
                            // Append to existing messages
                            chatMessages.innerHTML += messageHTML;
                        }

                        // Update lastMessageId to prevent duplicate fetching
                        lastMessageId = Math.max(lastMessageId, parseInt(data.message.id));

                        // Scroll to bottom
                        scrollToBottom();
                    }

                    // Live chat - no status updates needed
                } else {
                    alert(data.error || 'Error sending message. Please try again.');
                }
            })
            .catch(error => {
                // Always clear input field and reset height even on error
                messageInput.value = '';
                messageInput.style.height = 'auto';
                console.error('Error sending message:', error);
                alert('Error sending message. Please try again.');
            });
    }

    // Live chat - no status change links needed
    document.addEventListener('DOMContentLoaded', function() {
                if (!confirm(
                        `Are you sure you want to change the ticket status to ${status}?`)) {
                    e.preventDefault();
                }
            });
        });
    });

    // Function to show notification
    function showNotification(message, type, duration = 5000) {
        // Remove any existing notifications
        const existingNotifications = document.querySelectorAll('.notification');
        existingNotifications.forEach(notification => notification.remove());

        // Create notification element
        const notification = document.createElement('div');
        notification.className = 'notification ' + type;
        notification.textContent = message;

        // Add to body
        document.body.appendChild(notification);

        // Show with animation
        setTimeout(() => {
            notification.classList.add('show');
        }, 10);

        // Hide after duration
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => {
                notification.remove();
            }, 300);
        }, duration);
    }



    // Add event delegation for user list clicks (backup method)
    document.addEventListener('DOMContentLoaded', function() {
        console.log('DOM loaded, setting up event delegation');
        const chatList = document.querySelector('.chat-list');
        if (chatList) {
            console.log('Chat list found, adding click listener');
            chatList.addEventListener('click', function(e) {
                console.log('Chat list clicked', e.target);
                const listItem = e.target.closest('.chat-list-item');
                if (listItem) {
                    console.log('List item found:', listItem);

                    // Try to get user ID from onclick attribute first
                    if (listItem.hasAttribute('onclick')) {
                        const onclickAttr = listItem.getAttribute('onclick');
                        const userIdMatch = onclickAttr.match(/selectUser\((\d+)\)/);
                        if (userIdMatch) {
                            const userId = userIdMatch[1];
                            console.log('Event delegation: selectUser called with userId:', userId);
                            e.preventDefault(); // Prevent default onclick
                            selectUser(parseInt(userId));
                            return;
                        }
                    }

                    // Alternative: try to get user ID from data attribute
                    const userId = listItem.getAttribute('data-user-id');
                    if (userId) {
                        console.log('Event delegation (data-attr): selectUser called with userId:', userId);
                        selectUser(parseInt(userId));
                        return;
                    }

                    console.log('No user ID found in onclick or data attributes');
                }
            });
        } else {
            console.log('Chat list not found');
        }
    });

    // --- Auto-update admin notification badge ---
    function updateAdminNotificationBadge() {
        fetch('admin-chat.php?action=get_admin_notifications', {
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                const badge = document.getElementById('admin-notification-badge');
                if (data.success && badge) {
                    if (data.unread_count > 0) {
                        badge.textContent = data.unread_count;
                        badge.style.display = 'inline-block';
                    } else {
                        badge.style.display = 'none';
                    }
                }
            })
            .catch(() => {});
    }

    // --- Auto-update Conversations list (sidebar) ---
    function updateChatUserList() {
        fetch('admin-chat.php?action=get_chat_user_list', {
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const chatList = document.querySelector('.chat-list');
                    if (!chatList) return;
                    let html = '';
                    if (data.users.length > 0) {
                        data.users.forEach(user => {
                            html += `<li class="chat-list-item${user.id == <?php echo json_encode($chat_user_id); ?> ? ' active' : ''}" onclick="selectUser(${user.id})">
                            <div class="chat-list-item-name">
                                <span>${user.username.replace(/</g, "&lt;")}</span>
                                ${user.unread_count > 0 ? `<span class="unread-badge">${user.unread_count}</span>` : ''}
                            </div>
                            <div class="chat-list-item-email">${user.email.replace(/</g, "&lt;")}</div>
                        </li>`;
                        });
                    } else {
                        html = '<li class="chat-list-item">No users found</li>';
                    }
                    chatList.innerHTML = html;

                    // Also update ticket notifications when user list changes (indicates new activity)
                    <?php if ($chat_user_id): ?>
                    updateTicketNotifications();
                    <?php endif; ?>
                }
            });
    }

    // --- Auto-update User's Tickets notifications ---
    function updateTicketNotifications() {
        <?php if ($chat_user_id): ?>
        console.log('Updating ticket notifications for user <?php echo $chat_user_id; ?>');
        fetch('admin-chat.php?action=get_ticket_notifications&user_id=<?php echo $chat_user_id; ?>', {
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                console.log('Ticket notifications response:', data);
                if (data.success) {
                    data.tickets.forEach(ticket => {
                        console.log(
                            `Processing ticket ${ticket.id} with ${ticket.unread_count} unread messages`
                        );
                        const badge = document.getElementById('ticket-notification-' + ticket.id);

                        if (ticket.unread_count > 0) {
                            if (badge) {
                                // Update existing badge
                                console.log(`Updating existing badge for ticket ${ticket.id}`);
                                badge.textContent = ticket.unread_count >= 10 ? '9+' : ticket.unread_count;
                                badge.style.display = 'inline-flex';
                            } else {
                                // Create new badge
                                console.log(`Creating new badge for ticket ${ticket.id}`);
                                const ticketBadges = document.querySelector(
                                    `#ticket-item-${ticket.id} .ticket-badges`);
                                if (ticketBadges) {
                                    const newBadge = document.createElement('span');
                                    newBadge.id = 'ticket-notification-' + ticket.id;
                                    newBadge.className = 'ticket-notification-badge';
                                    newBadge.textContent = ticket.unread_count >= 10 ? '9+' : ticket
                                        .unread_count;
                                    newBadge.style.display = 'inline-flex';
                                    ticketBadges.appendChild(newBadge);

                                    // Trigger animation for new badge
                                    setTimeout(() => {
                                        newBadge.style.animation = 'notificationPulse 0.5s ease-in-out';
                                    }, 10);

                                    console.log(`Badge created for ticket ${ticket.id}`);
                                } else {
                                    console.log(`Could not find ticket badges container for ticket ${ticket.id}`);
                                }
                            }
                        } else {
                            // Remove badge if no unread messages
                            if (badge) {
                                console.log(`Removing badge for ticket ${ticket.id}`);
                                badge.remove();
                            }
                        }
                    });
                } else {
                    console.error('Ticket notifications request failed:', data);
                }
            })
            .catch(error => console.error('Error updating ticket notifications:', error));
        <?php else: ?>
        console.log('No chat user ID available for ticket notifications');
        <?php endif; ?>
    }

    // --- Global notification monitoring for ALL users ---
    let lastGlobalMessageCount = 0;

    function checkGlobalNotifications() {
        fetch('admin-chat.php?action=get_global_notifications', {
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Check if there are new messages since last check
                    if (data.total_unread > lastGlobalMessageCount && lastGlobalMessageCount > 0) {
                        const newMessageCount = data.total_unread - lastGlobalMessageCount;
                        console.log(`${newMessageCount} new message(s) detected globally`);

                        // Trigger notification for new messages
                        if (window.chatNotificationSystem) {
                            // Initialize notification system if not already done
                            if (!window.chatNotificationSystem.isInitialized) {
                                window.chatNotificationSystem.initialize();
                            }

                            // Show notification
                            const message = newMessageCount === 1 ?
                                'You have a new message from a customer' :
                                `You have ${newMessageCount} new messages from customers`;

                            window.chatNotificationSystem.notify(message, {
                                title: 'New Customer Message',
                                forceNotification: false // Only notify if tab is not focused
                            });
                        }
                    }

                    lastGlobalMessageCount = data.total_unread;
                }
            })
            .catch(error => console.error('Error checking global notifications:', error));
    }

    // --- Function to fetch new messages for live chat ---
    let lastMessageId = <?php
        if ($chat_user_id && !empty($messages)) {
            echo max(array_column($messages, 'id'));
        } else {
            echo '0';
        }
    ?>;

    function fetchMessages() {
        <?php if ($chat_user_id): ?>
        fetch(`admin-chat.php?action=get_new_messages&user_id=<?php echo $chat_user_id; ?>&last_id=${lastMessageId}`, {
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success && data.messages.length > 0) {
                    // Get chat container
                    const chatMessages = document.getElementById('chatMessages');
                    let hasNewUserMessages = false;

                    // Add new messages
                    data.messages.forEach(message => {
                        const messageHTML = formatMessage(message);

                        // Check if chat is empty (has the "No messages yet" placeholder)
                        const chatEmpty = chatMessages.querySelector('.chat-empty');
                        if (chatEmpty) {
                            // Replace the empty state with the first message
                            chatMessages.innerHTML = messageHTML;
                        } else {
                            // Append to existing messages
                            chatMessages.innerHTML += messageHTML;
                        }

                        // Check if this is a user message (not from admin)
                        if (message.sender_type === 'user') {
                            hasNewUserMessages = true;
                        }

                        // Update last message ID
                        lastMessageId = Math.max(lastMessageId, parseInt(message.id));
                    });

                    // Play notification sound for new user messages
                    if (hasNewUserMessages && window.chatNotificationSystem) {
                        // Initialize notification system if not already done
                        if (!window.chatNotificationSystem.isInitialized) {
                            window.chatNotificationSystem.initialize();
                        }

                        const message = data.messages[0].message || 'New message';

                        // Notify with the first new message
                        window.chatNotificationSystem.notify(
                            message.substring(0, 100) + (message.length > 100 ? '...' : ''), {
                                title: 'New message from Customer',
                                sender: data.messages[0].sender_name || 'Customer'
                            }
                        );
                    }

                    // Scroll to bottom
                    scrollToBottom();
                }
            })
            .catch(error => console.error('Error fetching new messages:', error));
        <?php endif; ?>
    }

    // Poll every 3 seconds for ticket notifications (more frequent for better UX)
    // Poll every 5 seconds for user list
    // Poll every 4 seconds for global notifications (sound/tab alerts)
    setInterval(updateChatUserList, 5000);
    setInterval(updateTicketNotifications, 3000);
    setInterval(checkGlobalNotifications, 4000);

    // Also call once on page load
    updateChatUserList();
    updateTicketNotifications();
    checkGlobalNotifications();

    // Call on page load
    window.onload = function() {
        scrollToBottom();

        // Initialize notification system
        if (window.chatNotificationSystem) {
            window.chatNotificationSystem.initialize();

            // Request notification permission when page loads
            setTimeout(() => {
                window.chatNotificationSystem.requestPermission();
            }, 2000);
        }

        // Set up auto-refresh for messages only if a user is selected
        <?php if ($chat_user_id): ?>
        setInterval(fetchMessages, 3000);
        <?php endif; ?>

        // Live chat - no ticket status checks needed
    };

    document.addEventListener('DOMContentLoaded', function() {
        const userDropdown = document.querySelector('.user-dropdown');
        const userInfo = document.querySelector('.user-info');
        const menuToggle = document.querySelector('.mobile-menu-toggle');
        const chatSidebar = document.querySelector('.chat-sidebar');
        const toggleChatSidebar = document.getElementById('toggleChatSidebar');
        const ticketHeader = document.getElementById('ticketHeader');
        const toggleTicketList = document.getElementById('toggleTicketList');
        const ticketList = document.getElementById('ticketList');

        // Initialize ticket list state based on screen size
        function initTicketListState() {
            if (window.innerWidth <= 767) {
                // On mobile, default to collapsed
                ticketList?.classList.remove('expanded');
                toggleTicketList?.classList.add('collapsed');
                toggleTicketList?.querySelector('i')?.classList.add('fa-chevron-right');
                toggleTicketList?.querySelector('i')?.classList.remove('fa-chevron-down');
            } else {
                // On desktop, default to expanded
                ticketList?.classList.remove('collapsed');
                toggleTicketList?.classList.remove('collapsed');
                toggleTicketList?.querySelector('i')?.classList.remove('fa-chevron-right');
                toggleTicketList?.querySelector('i')?.classList.add('fa-chevron-down');
            }
        }

        // Initialize on page load
        initTicketListState();

        // Toggle ticket list functionality
        if (ticketHeader && toggleTicketList && ticketList) {
            ticketHeader.addEventListener('click', function(e) {
                toggleTicketList.click();
            });

            toggleTicketList.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();

                const icon = this.querySelector('i');

                if (window.innerWidth <= 767) {
                    // On mobile, toggle expanded class
                    ticketList.classList.toggle('expanded');
                    this.classList.toggle('collapsed');

                    if (ticketList.classList.contains('expanded')) {
                        icon.classList.remove('fa-chevron-right');
                        icon.classList.add('fa-chevron-down');

                        // Ensure scrolling works by forcing a reflow
                        setTimeout(function() {
                            ticketList.style.overflow = 'hidden';
                            ticketList.offsetHeight; // Force reflow
                            ticketList.style.overflow = 'auto';
                        }, 10);
                    } else {
                        icon.classList.remove('fa-chevron-down');
                        icon.classList.add('fa-chevron-right');
                    }
                } else {
                    // On desktop, toggle collapsed class
                    ticketList.classList.toggle('collapsed');
                    this.classList.toggle('collapsed');

                    if (ticketList.classList.contains('collapsed')) {
                        icon.classList.remove('fa-chevron-down');
                        icon.classList.add('fa-chevron-right');
                    } else {
                        icon.classList.remove('fa-chevron-right');
                        icon.classList.add('fa-chevron-down');

                        // Ensure scrolling works by forcing a reflow
                        setTimeout(function() {
                            ticketList.style.overflow = 'hidden';
                            ticketList.offsetHeight; // Force reflow
                            ticketList.style.overflow = 'auto';
                        }, 10);
                    }
                }
            });
        }

        // Only apply dropdown functionality on mobile
        if (window.innerWidth <= 767) {
            userInfo.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                userDropdown.classList.toggle('active');
                menuToggle.classList.toggle('active');
            });

            // Also add direct click handler to the toggle icon for better mobile experience
            menuToggle.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                userDropdown.classList.toggle('active');
                menuToggle.classList.toggle('active');
            });

            // Close dropdown when clicking outside
            document.addEventListener('click', function(event) {
                if (!userDropdown.contains(event.target)) {
                    userDropdown.classList.remove('active');
                    menuToggle.classList.remove('active');
                }
            });

            // Toggle chat sidebar on mobile
            if (toggleChatSidebar) {
                toggleChatSidebar.addEventListener('click', function() {
                    chatSidebar.classList.toggle('collapsed');
                    const icon = this.querySelector('i');
                    if (chatSidebar.classList.contains('collapsed')) {
                        icon.classList.remove('fa-chevron-down');
                        icon.classList.add('fa-chevron-up');
                    } else {
                        icon.classList.remove('fa-chevron-up');
                        icon.classList.add('fa-chevron-down');
                    }
                });

                // If a chat is selected, collapse the sidebar by default on mobile
                <?php if ($chat_user_id): ?>
                chatSidebar.classList.add('collapsed');
                toggleChatSidebar.querySelector('i').classList.remove('fa-chevron-down');
                toggleChatSidebar.querySelector('i').classList.add('fa-chevron-up');
                <?php endif; ?>
            }
        }

        // Handle window resize
        window.addEventListener('resize', function() {
            if (window.innerWidth > 767) {
                userDropdown.classList.remove('active');
                menuToggle.classList.remove('active');

                // Remove collapsed class from chat sidebar when returning to desktop
                if (chatSidebar) {
                    chatSidebar.classList.remove('collapsed');
                    if (toggleChatSidebar) {
                        toggleChatSidebar.querySelector('i').classList.remove('fa-chevron-up');
                        toggleChatSidebar.querySelector('i').classList.add('fa-chevron-down');
                    }
                }

                // Reset ticket list state for desktop
                if (ticketList) {
                    ticketList.classList.remove('expanded');
                    if (toggleTicketList) {
                        if (ticketList.classList.contains('collapsed')) {
                            toggleTicketList.classList.add('collapsed');
                            toggleTicketList.querySelector('i').classList.remove('fa-chevron-down');
                            toggleTicketList.querySelector('i').classList.add('fa-chevron-right');
                        } else {
                            toggleTicketList.classList.remove('collapsed');
                            toggleTicketList.querySelector('i').classList.remove('fa-chevron-right');
                            toggleTicketList.querySelector('i').classList.add('fa-chevron-down');
                        }
                    }
                }
            } else {
                // Reset ticket list state for mobile
                if (ticketList) {
                    ticketList.classList.remove('collapsed');
                    if (toggleTicketList) {
                        if (ticketList.classList.contains('expanded')) {
                            toggleTicketList.classList.remove('collapsed');
                            toggleTicketList.querySelector('i').classList.remove('fa-chevron-right');
                            toggleTicketList.querySelector('i').classList.add('fa-chevron-down');
                        } else {
                            toggleTicketList.classList.add('collapsed');
                            toggleTicketList.querySelector('i').classList.remove('fa-chevron-down');
                            toggleTicketList.querySelector('i').classList.add('fa-chevron-right');
                        }
                    }
                }
            }
        });
    });

    // Delete Chat Modal and Functionality - Removed for live chat mode
    <?php if (false): // Disabled for pure live chat ?>
    // Handle delete chat button click
    document.getElementById('deleteChatBtn').addEventListener('click', function() {
        document.getElementById('deleteTicketId').value = this.getAttribute('data-ticket-id');
        document.getElementById('adminPassword').value = '';
        document.getElementById('deleteErrorMessage').style.display = 'none';
    });

    // Toggle password visibility for delete modal
    document.addEventListener('click', function(e) {
        if (e.target.closest('.toggle-password')) {
            const toggleBtn = e.target.closest('.toggle-password');
            const input = document.querySelector(toggleBtn.getAttribute('toggle'));
            const icon = toggleBtn.querySelector('i');

            if (input.type === 'password') {
                input.type = 'text';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            } else {
                input.type = 'password';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            }
        }
    });

    // Handle delete chat form submission
    document.getElementById('deleteChatForm').addEventListener('submit', function(e) {
        e.preventDefault();

        const form = this;
        const submitBtn = document.getElementById('confirmDeleteBtn');
        const errorMessage = document.getElementById('deleteErrorMessage');
        const password = document.getElementById('adminPassword').value;

        if (!password.trim()) {
            errorMessage.textContent = 'Admin password is required';
            errorMessage.style.display = 'block';
            return;
        }

        // Show loading state
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Deleting...';
        submitBtn.disabled = true;
        errorMessage.style.display = 'none';

        // Create form data
        const formData = new FormData(form);

        // Send AJAX request
        fetch('admin-chat.php', {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Close modal
                $('#deleteChatModal').modal('hide');

                // Show success notification
                showNotification(data.message, 'success');

                // Reload the page to refresh the chat
                setTimeout(() => {
                    window.location.reload();
                }, 1500);
            } else {
                errorMessage.textContent = data.error || 'Failed to delete chat';
                errorMessage.style.display = 'block';
            }
        })
        .catch(error => {
            console.error('Error:', error);
            errorMessage.textContent = 'An error occurred while deleting the chat';
            errorMessage.style.display = 'block';
        })
        .finally(() => {
            // Reset button state
            submitBtn.innerHTML = '<i class="fas fa-trash"></i> Delete Chat';
            submitBtn.disabled = false;
        });
    });
    <?php endif; ?>
    </script>

    <!-- Delete Chat Modal - Removed for live chat mode -->
    <?php if (false): // Disabled for pure live chat ?>
    <div class="modal fade" id="deleteChatModal" tabindex="-1" role="dialog" aria-labelledby="deleteChatModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title" id="deleteChatModalLabel" style="color: white;">
                        <i class="fas fa-exclamation-triangle mr-2" ></i>Confirm Chat Deletion
                    </h5>
                    <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <form id="deleteChatForm" method="POST">
                    <div class="modal-body">
                        <input type="hidden" name="delete_chat" value="1">
                        <input type="hidden" name="ticket_id" id="deleteTicketId" value="">

                        <div class="text-center mb-4">
                            <i class="fas fa-trash-alt text-danger" style="font-size: 48px;"></i>
                        </div>

                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle mr-2"></i>
                            <strong>Warning:</strong> This action will permanently delete all chat messages for this ticket. This cannot be undone.
                        </div>

                        <p class="text-center mb-4">
                            Are you sure you want to delete all chat messages for ticket <strong>#<?php echo $ticket_id; ?></strong>?
                        </p>

                        <div class="form-group">
                            <label for="adminPassword">Enter your admin password to confirm:</label>
                            <div class="input-group">
                                <input type="password" class="form-control" id="adminPassword" name="admin_password"
                                       placeholder="Admin password" required>
                                <div class="input-group-append">
                                    <span class="input-group-text toggle-password" toggle="#adminPassword">
                                        <i class="fa fa-eye-slash" aria-hidden="true"></i>
                                    </span>
                                </div>
                            </div>
                        </div>

                        <div class="alert alert-danger" id="deleteErrorMessage" style="display: none;"></div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal" style="background-color: #473BF0; color: white; border-color: #473BF0;">
                            <i class="fas fa-times mr-1"></i> Cancel
                        </button>
                        <button type="submit" class="btn btn-danger" id="confirmDeleteBtn">
                            <i class="fas fa-trash mr-1"></i> Delete Chat
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <?php endif; ?>
</body>

</html>