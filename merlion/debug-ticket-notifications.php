<?php
// Debug script to check ticket notifications
include_once('../functions/server.php');

// Check if admin is logged in
session_start();
if (!isset($_SESSION['admin_username'])) {
    echo "Not logged in as admin";
    exit();
}

$admin_id = $_SESSION['admin_id'];

// Get user ID from URL parameter
$user_id = isset($_GET['user_id']) ? intval($_GET['user_id']) : null;

if (!$user_id) {
    echo "Please provide user_id parameter. Example: debug-ticket-notifications.php?user_id=185";
    exit();
}

echo "<h2>Debug Ticket Notifications for User ID: $user_id</h2>";

// Check if user exists
$user_query = "SELECT * FROM user WHERE id = $user_id";
$user_result = mysqli_query($conn, $user_query);
if (mysqli_num_rows($user_result) == 0) {
    echo "<p style='color: red;'>User ID $user_id not found!</p>";
    exit();
}
$user_info = mysqli_fetch_assoc($user_result);
echo "<p><strong>User:</strong> {$user_info['username']} ({$user_info['email']})</p>";

// Get all tickets for this user
echo "<h3>All Tickets for this User:</h3>";
$tickets_query = "SELECT * FROM support_tickets WHERE user_id = $user_id ORDER BY id DESC";
$tickets_result = mysqli_query($conn, $tickets_query);

if (mysqli_num_rows($tickets_result) == 0) {
    echo "<p style='color: orange;'>No tickets found for this user!</p>";
} else {
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>Ticket ID</th><th>Subject</th><th>Status</th><th>Created</th></tr>";
    while ($ticket = mysqli_fetch_assoc($tickets_result)) {
        echo "<tr>";
        echo "<td>{$ticket['id']}</td>";
        echo "<td>{$ticket['subject']}</td>";
        echo "<td>{$ticket['status']}</td>";
        echo "<td>{$ticket['created_at']}</td>";
        echo "</tr>";
    }
    echo "</table>";
}

// Check chat messages for each ticket
echo "<h3>Chat Messages Analysis:</h3>";
$tickets_result = mysqli_query($conn, $tickets_query); // Re-run query
while ($ticket = mysqli_fetch_assoc($tickets_result)) {
    $ticket_id = $ticket['id'];
    echo "<h4>Ticket #{$ticket_id}: {$ticket['subject']}</h4>";
    
    // Get all messages for this ticket
    $messages_query = "SELECT cm.*, 
                      CASE 
                          WHEN cm.sender_type = 'user' THEN u.username 
                          WHEN cm.sender_type = 'admin' THEN a.username 
                      END as sender_name
                      FROM chat_messages cm 
                      LEFT JOIN user u ON cm.sender_id = u.id AND cm.sender_type = 'user'
                      LEFT JOIN admin_users a ON cm.sender_id = a.id AND cm.sender_type = 'admin'
                      WHERE cm.ticket_id = $ticket_id 
                      ORDER BY cm.created_at DESC LIMIT 10";
    $messages_result = mysqli_query($conn, $messages_query);
    
    if (mysqli_num_rows($messages_result) == 0) {
        echo "<p style='color: orange;'>No chat messages found for this ticket.</p>";
    } else {
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin-bottom: 20px;'>";
        echo "<tr><th>Message ID</th><th>Sender</th><th>Type</th><th>Message</th><th>Is Read</th><th>Created</th></tr>";
        while ($message = mysqli_fetch_assoc($messages_result)) {
            $read_status = $message['is_read'] ? 'YES' : 'NO';
            $read_color = $message['is_read'] ? 'green' : 'red';
            echo "<tr>";
            echo "<td>{$message['id']}</td>";
            echo "<td>{$message['sender_name']}</td>";
            echo "<td>{$message['sender_type']}</td>";
            echo "<td>" . substr($message['message'], 0, 50) . "...</td>";
            echo "<td style='color: $read_color; font-weight: bold;'>$read_status</td>";
            echo "<td>{$message['created_at']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // Count unread messages for this ticket
    $unread_query = "SELECT COUNT(*) as unread_count FROM chat_messages 
                     WHERE ticket_id = $ticket_id 
                     AND sender_type = 'user' 
                     AND is_read = 0";
    $unread_result = mysqli_query($conn, $unread_query);
    $unread_data = mysqli_fetch_assoc($unread_result);
    $unread_count = $unread_data['unread_count'];
    
    echo "<p><strong>Unread messages for ticket #{$ticket_id}:</strong> <span style='color: " . ($unread_count > 0 ? 'red' : 'green') . "; font-weight: bold;'>$unread_count</span></p>";
}

// Test the exact query used by get_ticket_notifications
echo "<h3>Testing get_ticket_notifications Query:</h3>";
$notifications_query = "SELECT st.id,
    (SELECT COUNT(*) FROM chat_messages cm
     WHERE cm.ticket_id = st.id
     AND cm.sender_type = 'user'
     AND cm.is_read = 0) as unread_count
    FROM support_tickets st
    WHERE st.user_id = $user_id";

$notifications_result = mysqli_query($conn, $notifications_query);
echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th>Ticket ID</th><th>Unread Count</th><th>Should Show Badge?</th></tr>";
while ($notification = mysqli_fetch_assoc($notifications_result)) {
    $should_show = $notification['unread_count'] > 0 ? 'YES' : 'NO';
    $color = $notification['unread_count'] > 0 ? 'red' : 'green';
    echo "<tr>";
    echo "<td>{$notification['id']}</td>";
    echo "<td style='color: $color; font-weight: bold;'>{$notification['unread_count']}</td>";
    echo "<td style='color: $color; font-weight: bold;'>$should_show</td>";
    echo "</tr>";
}
echo "</table>";

// Check if chat_messages table exists and has correct structure
echo "<h3>Database Table Structure:</h3>";
$structure_query = "DESCRIBE chat_messages";
$structure_result = mysqli_query($conn, $structure_query);
if ($structure_result) {
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    while ($field = mysqli_fetch_assoc($structure_result)) {
        echo "<tr>";
        echo "<td>{$field['Field']}</td>";
        echo "<td>{$field['Type']}</td>";
        echo "<td>{$field['Null']}</td>";
        echo "<td>{$field['Key']}</td>";
        echo "<td>{$field['Default']}</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p style='color: red;'>Error checking table structure: " . mysqli_error($conn) . "</p>";
}

echo "<hr>";
echo "<p><strong>Debug completed at:</strong> " . date('Y-m-d H:i:s') . "</p>";
?>
