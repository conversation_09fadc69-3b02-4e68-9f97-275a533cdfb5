<?php
/**
 * Appika API Testing Backend
 * Tests connection to Appika APIs and returns JSON response
 */

session_start();

// Check if admin is logged in
if (!isset($_SESSION['admin_username'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit();
}

// Get request data
$input = json_decode(file_get_contents('php://input'), true);
$type = $input['type'] ?? '';

if (!in_array($type, ['ticket', 'customer'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Invalid API type']);
    exit();
}

// API endpoints
$endpoints = [
    'ticket' => 'https://dev-sgsg-tktapi.appika.com/graphql',
    'customer' => 'https://dev-api-pooh-sgsg.appika.com/contact/customers'
];

$url = $endpoints[$type];
$startTime = microtime(true);

// Initialize cURL
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 15); // 15 second timeout
curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10); // 10 second connection timeout
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
curl_setopt($ch, CURLOPT_USERAGENT, 'HelloIT Admin Connection Tester');

// For GraphQL endpoint, send a simple introspection query
if ($type === 'ticket') {
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Accept: application/json'
    ]);
    
    // Simple introspection query to test if GraphQL is responding
    $query = json_encode([
        'query' => '{ __schema { queryType { name } } }'
    ]);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $query);
}

// Execute request
$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
$totalTime = curl_getinfo($ch, CURLINFO_TOTAL_TIME);
curl_close($ch);

$endTime = microtime(true);
$responseTime = round(($endTime - $startTime) * 1000); // Convert to milliseconds

// Prepare response
$result = [
    'success' => false,
    'responseTime' => $responseTime,
    'httpCode' => $httpCode,
    'message' => '',
    'details' => []
];

if ($error) {
    $result['message'] = 'Connection failed: ' . $error;
    $result['details'] = [
        'error_type' => 'curl_error',
        'error_message' => $error,
        'url' => $url
    ];
} else {
    if ($httpCode >= 200 && $httpCode < 400) {
        $result['success'] = true;
        
        if ($type === 'ticket') {
            // Check if it's a valid GraphQL response
            $jsonResponse = json_decode($response, true);
            if ($jsonResponse && isset($jsonResponse['data'])) {
                $result['message'] = 'GraphQL endpoint is responding correctly';
            } else if ($jsonResponse && isset($jsonResponse['errors'])) {
                $result['message'] = 'GraphQL endpoint is online but returned errors';
            } else {
                $result['message'] = 'Server is responding but may not be GraphQL';
            }
        } else {
            $result['message'] = 'API endpoint is responding correctly';
        }
        
        $result['details'] = [
            'http_code' => $httpCode,
            'response_size' => strlen($response),
            'total_time' => $totalTime
        ];
    } else {
        $result['message'] = "Server responded with HTTP $httpCode";
        $result['details'] = [
            'http_code' => $httpCode,
            'response' => substr($response, 0, 200) // First 200 chars of response
        ];
        
        // Some HTTP codes might still indicate the server is reachable
        if ($httpCode >= 400 && $httpCode < 500) {
            $result['message'] .= ' (Server is reachable but request was rejected)';
        } else if ($httpCode >= 500) {
            $result['message'] .= ' (Server error - service may be down)';
        }
    }
}

// Add response time category
if ($result['success']) {
    if ($responseTime < 500) {
        $result['speed'] = 'excellent';
    } else if ($responseTime < 1000) {
        $result['speed'] = 'good';
    } else if ($responseTime < 2000) {
        $result['speed'] = 'fair';
    } else {
        $result['speed'] = 'slow';
    }
} else {
    $result['speed'] = 'failed';
}

// Return JSON response
header('Content-Type: application/json');
echo json_encode($result);
?>
