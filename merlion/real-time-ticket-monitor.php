<?php
/**
 * Real-time Ticket Monitor - Watch tickets in real-time to see what's changing them
 */

session_start();
include('../functions/server.php');

// Check if admin is logged in
if (!isset($_SESSION['admin_username'])) {
    header('location: admin-login.php');
    exit();
}

$test_user_id = 200;
$test_username = 'HC200';

// Handle AJAX requests for real-time monitoring
if (isset($_GET['action']) && $_GET['action'] == 'get_status') {
    header('Content-Type: application/json');
    
    // Get current ticket counts
    $user_query = "SELECT starter_tickets, premium_tickets, ultimate_tickets FROM user WHERE id = $test_user_id";
    $user_result = mysqli_query($conn, $user_query);
    $user = mysqli_fetch_assoc($user_result);
    
    // Get latest ticket log
    $log_query = "SELECT * FROM ticket_logs WHERE user_id = $test_user_id ORDER BY created_at DESC LIMIT 1";
    $log_result = mysqli_query($conn, $log_query);
    $latest_log = mysqli_fetch_assoc($log_result);
    
    // Get purchase records count
    $purchase_query = "SELECT COUNT(*) as count FROM purchasetickets WHERE username = '$test_username'";
    $purchase_result = mysqli_query($conn, $purchase_query);
    $purchase_count = mysqli_fetch_assoc($purchase_result)['count'];
    
    // Check if any cleanup functions are being called
    $cleanup_status = "Unknown";
    if (function_exists('runAutomaticCleanup')) {
        $cleanup_status = "Function exists";
    }
    
    echo json_encode([
        'timestamp' => date('Y-m-d H:i:s'),
        'tickets' => $user,
        'latest_log' => $latest_log,
        'purchase_records' => $purchase_count,
        'cleanup_status' => $cleanup_status
    ]);
    exit();
}

// Handle manual ticket addition for testing
if (isset($_POST['add_ticket'])) {
    $ticket_type = $_POST['ticket_type'];
    $amount = (int)$_POST['amount'];
    
    echo "<div class='alert alert-info'>";
    echo "<h4>Adding $amount $ticket_type tickets...</h4>";
    
    // Log before state
    $before_query = "SELECT starter_tickets, premium_tickets, ultimate_tickets FROM user WHERE id = $test_user_id";
    $before_result = mysqli_query($conn, $before_query);
    $before = mysqli_fetch_assoc($before_result);
    echo "<p><strong>Before:</strong> Starter: {$before['starter_tickets']}, Premium: {$before['premium_tickets']}, Ultimate: {$before['ultimate_tickets']}</p>";
    
    // Add tickets
    $update_query = "UPDATE user SET $ticket_type = $ticket_type + $amount WHERE id = $test_user_id";
    echo "<p><strong>SQL:</strong> <code>$update_query</code></p>";
    
    if (mysqli_query($conn, $update_query)) {
        // Log after state immediately
        $after_result = mysqli_query($conn, $before_query);
        $after = mysqli_fetch_assoc($after_result);
        echo "<p><strong>After:</strong> Starter: {$after['starter_tickets']}, Premium: {$after['premium_tickets']}, Ultimate: {$after['ultimate_tickets']}</p>";
        
        // Create log entry
        $admin_id = $_SESSION['admin_id'];
        $log_query = "INSERT INTO ticket_logs (action, description, user_id, ticket_type, amount, performed_by_admin_id, created_at) 
                      VALUES ('success', 'Real-time test: Added $amount $ticket_type tickets', $test_user_id, '$ticket_type', $amount, $admin_id, NOW())";
        mysqli_query($conn, $log_query);
        
        echo "<p class='text-success'>✅ Tickets added successfully!</p>";
        echo "<p><strong>Now monitoring for changes...</strong></p>";
    } else {
        echo "<p class='text-danger'>❌ Failed to add tickets: " . mysqli_error($conn) . "</p>";
    }
    echo "</div>";
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Real-time Ticket Monitor</title>
    <link rel="stylesheet" href="../css/bootstrap.css">
    <style>
        body { background-color: #f8f9fa; padding: 20px; }
        .container { max-width: 1000px; }
        .monitor-section { background: white; padding: 20px; margin: 15px 0; border-radius: 8px; border-left: 4px solid #007bff; }
        .status-box { background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .timestamp { color: #666; font-size: 12px; }
        .change-detected { background-color: #fff3cd; border-color: #ffeaa7; }
        .stable { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        #log { max-height: 400px; overflow-y: auto; font-family: monospace; font-size: 12px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Real-time Ticket Monitor for HC200</h1>
        
        <div class="monitor-section">
            <h3>Current Status</h3>
            <div id="current-status" class="status-box">
                Loading...
            </div>
        </div>
        
        <div class="monitor-section">
            <h3>Add Test Tickets</h3>
            <form method="POST" class="form-inline">
                <select name="ticket_type" class="form-control mr-2">
                    <option value="starter_tickets">Starter</option>
                    <option value="premium_tickets">Premium</option>
                    <option value="ultimate_tickets">Ultimate</option>
                </select>
                <input type="number" name="amount" value="1" min="1" max="10" class="form-control mr-2">
                <button type="submit" name="add_ticket" class="btn btn-primary">Add Tickets</button>
            </form>
        </div>
        
        <div class="monitor-section">
            <h3>Real-time Log</h3>
            <div id="log" class="status-box">
                <div class="timestamp">Starting monitor...</div>
            </div>
        </div>
        
        <div class="monitor-section">
            <h3>Controls</h3>
            <button id="start-monitor" class="btn btn-success">▶️ Start Monitoring</button>
            <button id="stop-monitor" class="btn btn-danger">⏹️ Stop Monitoring</button>
            <button id="clear-log" class="btn btn-warning">🗑️ Clear Log</button>
            <a href="admin-users.php" class="btn btn-secondary">← Back to Users</a>
        </div>
    </div>

    <script>
        let monitorInterval;
        let lastStatus = null;
        let isMonitoring = false;
        
        function addLog(message, type = 'info') {
            const log = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${type}`;
            logEntry.innerHTML = `<span class="timestamp">[${timestamp}]</span> ${message}`;
            log.appendChild(logEntry);
            log.scrollTop = log.scrollHeight;
        }
        
        function updateStatus(data) {
            const statusDiv = document.getElementById('current-status');
            const tickets = data.tickets;
            
            let html = `
                <div class="row">
                    <div class="col-md-4">
                        <strong>Starter:</strong> ${tickets.starter_tickets}<br>
                        <strong>Premium:</strong> ${tickets.premium_tickets}<br>
                        <strong>Ultimate:</strong> ${tickets.ultimate_tickets}
                    </div>
                    <div class="col-md-4">
                        <strong>Purchase Records:</strong> ${data.purchase_records}<br>
                        <strong>Cleanup Status:</strong> ${data.cleanup_status}
                    </div>
                    <div class="col-md-4">
                        <strong>Last Update:</strong> ${data.timestamp}<br>
                        ${data.latest_log ? `<strong>Latest Log:</strong> ${data.latest_log.description}` : 'No logs'}
                    </div>
                </div>
            `;
            
            statusDiv.innerHTML = html;
            
            // Check for changes
            if (lastStatus) {
                const changes = [];
                if (lastStatus.tickets.starter_tickets !== tickets.starter_tickets) {
                    changes.push(`Starter: ${lastStatus.tickets.starter_tickets} → ${tickets.starter_tickets}`);
                }
                if (lastStatus.tickets.premium_tickets !== tickets.premium_tickets) {
                    changes.push(`Premium: ${lastStatus.tickets.premium_tickets} → ${tickets.premium_tickets}`);
                }
                if (lastStatus.tickets.ultimate_tickets !== tickets.ultimate_tickets) {
                    changes.push(`Ultimate: ${lastStatus.tickets.ultimate_tickets} → ${tickets.ultimate_tickets}`);
                }
                
                if (changes.length > 0) {
                    addLog(`🚨 CHANGE DETECTED: ${changes.join(', ')}`, 'warning');
                    statusDiv.className = 'status-box change-detected';
                } else {
                    statusDiv.className = 'status-box stable';
                }
            }
            
            lastStatus = data;
        }
        
        function fetchStatus() {
            fetch('?action=get_status')
                .then(response => response.json())
                .then(data => {
                    updateStatus(data);
                    if (isMonitoring) {
                        addLog(`Status check: S:${data.tickets.starter_tickets} P:${data.tickets.premium_tickets} U:${data.tickets.ultimate_tickets}`, 'info');
                    }
                })
                .catch(error => {
                    addLog(`❌ Error fetching status: ${error}`, 'error');
                });
        }
        
        function startMonitoring() {
            if (isMonitoring) return;
            
            isMonitoring = true;
            addLog('🟢 Monitoring started - checking every 2 seconds', 'success');
            
            monitorInterval = setInterval(fetchStatus, 2000);
            fetchStatus(); // Initial fetch
            
            document.getElementById('start-monitor').disabled = true;
            document.getElementById('stop-monitor').disabled = false;
        }
        
        function stopMonitoring() {
            if (!isMonitoring) return;
            
            isMonitoring = false;
            clearInterval(monitorInterval);
            addLog('🔴 Monitoring stopped', 'warning');
            
            document.getElementById('start-monitor').disabled = false;
            document.getElementById('stop-monitor').disabled = true;
        }
        
        function clearLog() {
            document.getElementById('log').innerHTML = '<div class="timestamp">Log cleared...</div>';
        }
        
        // Event listeners
        document.getElementById('start-monitor').addEventListener('click', startMonitoring);
        document.getElementById('stop-monitor').addEventListener('click', stopMonitoring);
        document.getElementById('clear-log').addEventListener('click', clearLog);
        
        // Initial status fetch
        fetchStatus();
        
        // Auto-start monitoring
        setTimeout(startMonitoring, 1000);
    </script>
</body>
</html>
