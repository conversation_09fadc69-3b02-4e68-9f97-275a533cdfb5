<?php
session_start();
include_once('../functions/server.php');

// Check if admin is logged in
if (!isset($_SESSION['admin_id'])) {
    echo json_encode(['error' => 'Not authenticated']);
    exit;
}

// Count unseen tickets
$new_tickets_sql = "SELECT COUNT(*) as cnt FROM support_tickets WHERE is_seen_by_admin=0";
$new_tickets_result = mysqli_query($conn, $new_tickets_sql);
$new_tickets_count = mysqli_fetch_assoc($new_tickets_result)['cnt'];

// Count unread messages
$unread_messages_query = "SELECT COUNT(*) as count FROM chat_messages WHERE sender_type = 'user' AND is_read = 0";
$unread_messages_result = mysqli_query($conn, $unread_messages_query);
$unread_messages_count = mysqli_fetch_assoc($unread_messages_result)['count'];

// Return JSON response
header('Content-Type: application/json');
echo json_encode([
    'new_tickets' => (int)$new_tickets_count,
    'unread_messages' => (int)$unread_messages_count
]); 