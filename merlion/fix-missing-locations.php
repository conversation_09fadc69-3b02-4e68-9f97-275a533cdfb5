<?php
session_start();
require_once '../config/database.php';
require_once '../config/appika_config.php';
require_once 'vendor/autoload.php';

// Check if user is admin
if (!isset($_SESSION['admin_username'])) {
    header('Location: admin-login.php');
    exit();
}

// Function to send data to Appika API
function sendToAppikaAPI($customerData, $method = 'POST', $path = null) {
    global $apiEndpoint, $apiPath, $apiKey;

    $fullPath = $path ?: $apiPath;

    try {
        $client = new \GuzzleHttp\Client([
            'base_uri' => $apiEndpoint,
            'timeout' => 30,
            'http_errors' => false,
        ]);

        // Send the request
        $response = $client->request($method, $fullPath, [
            'headers' => [
                'X-api-key' => "{$apiKey}",
                'Accept' => 'application/json',
                'Content-Type' => 'application/json',
            ],
            'json' => $customerData
        ]);

        // Get status code and response body
        $statusCode = $response->getStatusCode();
        $body = $response->getBody()->getContents();

        // Parse JSON if the response is JSON
        if (strpos($response->getHeaderLine('Content-Type'), 'application/json') !== false) {
            $data = json_decode($body, true);
            return [
                'success' => ($statusCode >= 200 && $statusCode < 300),
                'status' => $statusCode,
                'data' => $data
            ];
        } else {
            return [
                'success' => false,
                'status' => $statusCode,
                'data' => $body
            ];
        }
    } catch (\Exception $e) {
        return [
            'success' => false,
            'status' => 500,
            'error' => $e->getMessage()
        ];
    }
}

// Function to add location to a customer in Appika
function addLocationToAppika($customerDbId, $locationData) {
    global $apiEndpoint, $apiPath, $apiKey;

    // Create the path for adding a location
    $locationPath = $apiPath . '/' . $customerDbId . '/locations';

    // Send the location data to Appika
    return sendToAppikaAPI($locationData, 'POST', $locationPath);
}

$message = '';
$messageType = '';
$results = [];

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        if ($_POST['action'] === 'scan') {
            // Scan for users without locations in Appika
            $results = scanUsersWithoutLocations();
        } elseif ($_POST['action'] === 'fix_user' && isset($_POST['user_id'])) {
            // Fix a specific user
            $userId = intval($_POST['user_id']);
            $result = fixUserLocation($userId);
            $message = $result['message'];
            $messageType = $result['success'] ? 'success' : 'danger';

            // Re-scan after fixing
            $results = scanUsersWithoutLocations();
        } elseif ($_POST['action'] === 'fix_all') {
            // Fix all users without locations
            $result = fixAllUsersLocations();
            $message = $result['message'];
            $messageType = $result['success'] ? 'success' : 'warning';

            // Re-scan after fixing
            $results = scanUsersWithoutLocations();
        }
    }
}

function scanUsersWithoutLocations() {
    global $conn, $apiEndpoint, $apiPath, $apiKey;

    $usersWithoutLocations = [];

    // Get all users with appika_id
    $query = "SELECT id, username, first_name, email, appika_id, address, city, state, country, postal_code
              FROM user
              WHERE appika_id IS NOT NULL AND appika_id != ''
              ORDER BY id DESC";

    $result = mysqli_query($conn, $query);

    if ($result) {
        $client = new \GuzzleHttp\Client([
            'base_uri' => $apiEndpoint,
            'timeout' => 30,
            'http_errors' => false,
        ]);

        while ($user = mysqli_fetch_assoc($result)) {
            // Search for customer in Appika
            $searchResult = $client->request('GET', $apiPath, [
                'headers' => [
                    'X-api-key' => "{$apiKey}",
                    'Accept' => 'application/json',
                ],
                'query' => [
                    'no' => $user['appika_id']
                ]
            ]);

            $searchData = json_decode($searchResult->getBody()->getContents(), true);

            if (isset($searchData['items']) && !empty($searchData['items'])) {
                $customerData = $searchData['items'][0];
                $customerDbId = $customerData['id'];

                // Check if customer has locations
                $locationsResult = $client->request('GET', $apiPath . '/' . $customerDbId . '/locations', [
                    'headers' => [
                        'X-api-key' => "{$apiKey}",
                        'Accept' => 'application/json',
                    ]
                ]);

                $locationsData = json_decode($locationsResult->getBody()->getContents(), true);

                // If no locations found, add to list
                if (!isset($locationsData['items']) || empty($locationsData['items'])) {
                    $user['appika_customer_id'] = $customerDbId;
                    $usersWithoutLocations[] = $user;
                }
            }
        }
    }

    return $usersWithoutLocations;
}

function fixUserLocation($userId) {
    global $conn;

    // Get user data
    $query = "SELECT * FROM user WHERE id = $userId";
    $result = mysqli_query($conn, $query);

    if (!$result || mysqli_num_rows($result) === 0) {
        return ['success' => false, 'message' => 'User not found'];
    }

    $user = mysqli_fetch_assoc($result);

    if (empty($user['appika_id'])) {
        return ['success' => false, 'message' => 'User has no Appika ID'];
    }

    return createLocationForUser($user);
}

function fixAllUsersLocations() {
    $usersWithoutLocations = scanUsersWithoutLocations();
    $fixed = 0;
    $failed = 0;

    foreach ($usersWithoutLocations as $user) {
        $result = createLocationForUser($user);
        if ($result['success']) {
            $fixed++;
        } else {
            $failed++;
        }
    }

    $total = count($usersWithoutLocations);
    return [
        'success' => $failed === 0,
        'message' => "Processed $total users: $fixed fixed, $failed failed"
    ];
}

function createLocationForUser($user) {
    // Prepare location data
    $customerName = trim($user['first_name']);
    $locationData = [
        'loc_code' => 'LOC-' . $user['appika_id'],
        'loc_name' => $customerName . ' Location',
        'add1' => $user['address'] ?: '',
        'add2' => '',
        'ccode' => $user['country'] ?: 'TH',
        'state_code' => $user['state'] ?: '',
        'city' => $user['city'] ?: '',
        'status' => 'a',
        'is_primary_loc' => 'y',
        'zip' => $user['postal_code'] ?: '',
        'parent_id' => 0
    ];

    // Add location to Appika
    $locationResult = addLocationToAppika($user['appika_customer_id'], $locationData);

    if ($locationResult['success']) {
        return [
            'success' => true,
            'message' => "Location created for user {$user['username']} ({$user['appika_id']})"
        ];
    } else {
        return [
            'success' => false,
            'message' => "Failed to create location for user {$user['username']}: " . ($locationResult['error'] ?? 'Unknown error')
        ];
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fix Missing Locations - HelloIT Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1>Fix Missing Locations</h1>
                    <a href="admin-dashboard.php" class="btn btn-secondary">Back to Dashboard</a>
                </div>

                <?php if (!empty($message)): ?>
                <div class="alert alert-<?php echo $messageType; ?>" role="alert">
                    <?php echo htmlspecialchars($message); ?>
                </div>
                <?php endif; ?>

                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">Scan for Users Without Locations</h5>
                    </div>
                    <div class="card-body">
                        <p>This tool will scan all users with Appika IDs and check if they have location data in Appika.</p>
                        <form method="post">
                            <input type="hidden" name="action" value="scan">
                            <button type="submit" class="btn btn-info">Scan Users</button>
                        </form>
                    </div>
                </div>

                <?php if (!empty($results)): ?>
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">Users Without Locations (<?php echo count($results); ?> found)</h5>
                        <form method="post" style="display: inline;">
                            <input type="hidden" name="action" value="fix_all">
                            <button type="submit" class="btn btn-warning" onclick="return confirm('Fix all users? This will create locations for all users listed below.')">
                                Fix All Users
                            </button>
                        </form>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Username</th>
                                        <th>Name</th>
                                        <th>Appika ID</th>
                                        <th>Address</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($results as $user): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($user['id']); ?></td>
                                        <td><?php echo htmlspecialchars($user['username']); ?></td>
                                        <td><?php echo htmlspecialchars(trim($user['first_name'])); ?></td>
                                        <td><?php echo htmlspecialchars($user['appika_id']); ?></td>
                                        <td><?php echo htmlspecialchars($user['address'] ?: 'No address'); ?></td>
                                        <td>
                                            <form method="post" style="display: inline;">
                                                <input type="hidden" name="action" value="fix_user">
                                                <input type="hidden" name="user_id" value="<?php echo $user['id']; ?>">
                                                <button type="submit" class="btn btn-sm btn-success">Fix Location</button>
                                            </form>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <?php elseif ($_SERVER['REQUEST_METHOD'] === 'POST' && $_POST['action'] === 'scan'): ?>
                <div class="alert alert-success" role="alert">
                    ✅ Great! All users with Appika IDs have location data.
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>