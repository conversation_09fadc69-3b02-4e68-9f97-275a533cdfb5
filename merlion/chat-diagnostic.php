<?php
// Chat System Diagnostic Script
// This script helps identify issues with the admin-chat.php system

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Chat System Diagnostic</h1>";
echo "<style>body{font-family:Arial,sans-serif;margin:20px;} .success{color:green;} .error{color:red;} .warning{color:orange;} .info{color:blue;}</style>";

// Test 1: PHP Version
echo "<h2>1. PHP Version Check</h2>";
$php_version = phpversion();
echo "<p>PHP Version: <strong>$php_version</strong>";
if (version_compare($php_version, '7.0', '>=')) {
    echo " <span class='success'>✓ OK</span></p>";
} else {
    echo " <span class='error'>✗ Too old (requires 7.0+)</span></p>";
}

// Test 2: Required Extensions
echo "<h2>2. Required PHP Extensions</h2>";
$required_extensions = ['mysqli', 'session', 'json'];
foreach ($required_extensions as $ext) {
    echo "<p>$ext: ";
    if (extension_loaded($ext)) {
        echo "<span class='success'>✓ Loaded</span></p>";
    } else {
        echo "<span class='error'>✗ Missing</span></p>";
    }
}

// Test 3: File Permissions
echo "<h2>3. File Permissions</h2>";
$files_to_check = [
    '../functions/server.php',
    '../functions/timezone-helper.php',
    'admin-chat.php'
];

foreach ($files_to_check as $file) {
    echo "<p>$file: ";
    if (file_exists($file)) {
        if (is_readable($file)) {
            echo "<span class='success'>✓ Readable</span>";
        } else {
            echo "<span class='error'>✗ Not readable</span>";
        }
    } else {
        echo "<span class='error'>✗ File not found</span>";
    }
    echo "</p>";
}

// Test 4: Database Connection
echo "<h2>4. Database Connection Test</h2>";
try {
    include('../functions/server.php');
    if (isset($conn) && $conn) {
        echo "<p>Database Connection: <span class='success'>✓ Connected</span></p>";
        
        // Test database queries
        $test_query = "SELECT 1 as test";
        $result = mysqli_query($conn, $test_query);
        if ($result) {
            echo "<p>Database Query Test: <span class='success'>✓ Working</span></p>";
        } else {
            echo "<p>Database Query Test: <span class='error'>✗ Failed - " . mysqli_error($conn) . "</span></p>";
        }
        
        // Check if required tables exist
        $tables_to_check = ['user', 'support_tickets', 'admin_users'];
        foreach ($tables_to_check as $table) {
            $check_table = "SHOW TABLES LIKE '$table'";
            $table_result = mysqli_query($conn, $check_table);
            echo "<p>Table '$table': ";
            if ($table_result && mysqli_num_rows($table_result) > 0) {
                echo "<span class='success'>✓ Exists</span></p>";
            } else {
                echo "<span class='error'>✗ Missing</span></p>";
            }
        }
        
    } else {
        echo "<p>Database Connection: <span class='error'>✗ Failed</span></p>";
    }
} catch (Exception $e) {
    echo "<p>Database Connection: <span class='error'>✗ Error: " . $e->getMessage() . "</span></p>";
}

// Test 5: Session Test
echo "<h2>5. Session Test</h2>";
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

if (session_status() === PHP_SESSION_ACTIVE) {
    echo "<p>Session Status: <span class='success'>✓ Active</span></p>";
} else {
    echo "<p>Session Status: <span class='error'>✗ Not working</span></p>";
}

// Test 6: Memory and Execution Limits
echo "<h2>6. PHP Configuration</h2>";
echo "<p>Memory Limit: <strong>" . ini_get('memory_limit') . "</strong></p>";
echo "<p>Max Execution Time: <strong>" . ini_get('max_execution_time') . " seconds</strong></p>";
echo "<p>Upload Max Filesize: <strong>" . ini_get('upload_max_filesize') . "</strong></p>";
echo "<p>Post Max Size: <strong>" . ini_get('post_max_size') . "</strong></p>";

// Test 7: Timezone Helper Test
echo "<h2>7. Timezone Helper Test</h2>";
try {
    // Check if UTCTimeHelper is already loaded from server.php
    if (class_exists('UTCTimeHelper')) {
        echo "<p>UTCTimeHelper Class: <span class='success'>✓ Already loaded</span></p>";

        $current_utc = UTCTimeHelper::getCurrentUTC();
        echo "<p>Current UTC Time: <strong>$current_utc</strong> <span class='success'>✓ Working</span></p>";
    } else {
        echo "<p>UTCTimeHelper Class: <span class='warning'>⚠ Not loaded (this might be normal)</span></p>";
    }
} catch (Exception $e) {
    echo "<p>Timezone Helper: <span class='error'>✗ Error: " . $e->getMessage() . "</span></p>";
}

// Test 8: Admin Session Check
echo "<h2>8. Admin Session Check</h2>";
if (isset($_SESSION['admin_username'])) {
    echo "<p>Admin Username: <strong>" . htmlspecialchars($_SESSION['admin_username']) . "</strong> <span class='success'>✓ Logged in</span></p>";
    echo "<p>Admin ID: <strong>" . htmlspecialchars($_SESSION['admin_id']) . "</strong></p>";
    echo "<p>Admin Role: <strong>" . htmlspecialchars($_SESSION['admin_role']) . "</strong></p>";
} else {
    echo "<p>Admin Session: <span class='warning'>⚠ Not logged in (this is normal if accessing directly)</span></p>";
    echo "<p><a href='admin-login.php'>Click here to login as admin</a></p>";
}

// Test 9: Error Log Check
echo "<h2>9. Error Logging</h2>";
$error_log_path = ini_get('error_log');
echo "<p>Error Log Path: <strong>$error_log_path</strong></p>";

if ($error_log_path && file_exists($error_log_path)) {
    echo "<p>Error Log File: <span class='success'>✓ Exists</span></p>";
    
    // Try to read last few lines of error log
    $lines = file($error_log_path);
    if ($lines && count($lines) > 0) {
        echo "<h3>Recent Error Log Entries (last 5):</h3>";
        echo "<pre style='background:#f5f5f5;padding:10px;border:1px solid #ddd;'>";
        $recent_lines = array_slice($lines, -5);
        foreach ($recent_lines as $line) {
            echo htmlspecialchars($line);
        }
        echo "</pre>";
    }
} else {
    echo "<p>Error Log File: <span class='warning'>⚠ Not found or not accessible</span></p>";
}

// Test 10: URL Rewrite Test
echo "<h2>10. URL Rewrite Test</h2>";
$current_url = $_SERVER['REQUEST_URI'];
echo "<p>Current URL: <strong>$current_url</strong></p>";

if (strpos($current_url, '.php') !== false) {
    echo "<p>URL Rewrite: <span class='info'>ℹ Direct PHP access (normal for diagnostic)</span></p>";
} else {
    echo "<p>URL Rewrite: <span class='success'>✓ Clean URLs working</span></p>";
}

echo "<hr>";
echo "<h2>Summary</h2>";
echo "<p>If you see any <span class='error'>red errors</span> above, those need to be fixed first.</p>";
echo "<p>If everything shows <span class='success'>green checkmarks</span>, the issue might be:</p>";
echo "<ul>";
echo "<li>Server-specific configuration differences</li>";
echo "<li>Different PHP error reporting settings</li>";
echo "<li>Specific database query issues</li>";
echo "<li>URL rewrite conflicts</li>";
echo "</ul>";

echo "<p><strong>Next Steps:</strong></p>";
echo "<ol>";
echo "<li>Check your server's error logs for specific error messages</li>";
echo "<li>Try accessing admin-chat.php directly: <a href='admin-chat.php'>admin-chat.php</a></li>";
echo "<li>Compare this diagnostic between localhost and server</li>";
echo "</ol>";
?>
