<?php
session_start();
include('../functions/server.php');

// Run automatic ticket expiration cleanup if enabled
// TEMPORARILY DISABLED FOR DEBUGGING - Testing if cleanup is interfering
// include_once('../functions/ticket-expiration-functions.php');
// runAutomaticCleanup();

// Add Appika integration dependencies at the top
require_once '../vendor/autoload.php';
require_once '../functions/graphql_functions.php';
require_once '../functions/customer-data-service.php'; // Include customer data service
require_once '../config/api-config.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_username'])) {
    header('location: admin-login.php');
    exit();
}

/**
 * Fetch all tickets from Appika API with filtering and search
 */
function fetchAllAppikaTickets($search = "", $typeFilter = "", $statusFilter = "", $priorityFilter = "", $severityFilter = "") {
    // Use the working GraphQL query from my-ticket.php
    $query = '
    query GetTickets {
        getTickets {
            data {
                id
                ticket_no
                contact_id
                agent_id
                req_email
                subject
                type
                type_name
                priority
                status
                created
                updated
                creator_by_contact
                creator_by_agent
                contacts {
                    id
                    email
                    fname
                    status
                }
            }
        }
    }';

    $result = makeGraphQLRequest($query, []);

    if (!$result['success']) {
        return ['tickets' => [], 'error' => $result['error']];
    }

    $tickets = $result['data']['data']['getTickets']['data'] ?? [];
    
    // Apply filters
    $filteredTickets = [];
    foreach ($tickets as $ticket) {
        // Search filter
        if (!empty($search)) {
            $searchLower = strtolower($search);
            $searchFields = [
                strtolower($ticket['id'] ?? ''),
                strtolower($ticket['ticket_no'] ?? ''),
                strtolower($ticket['subject'] ?? ''),
                strtolower($ticket['req_email'] ?? ''),
                strtolower($ticket['contacts'][0]['fname'] ?? '')
            ];
            
            $matchFound = false;
            foreach ($searchFields as $field) {
                if (strpos($field, $searchLower) !== false) {
                    $matchFound = true;
                    break;
                }
            }
            
            if (!$matchFound) {
                continue;
            }
        }
        
        // Type filter
        if (!empty($typeFilter) && $typeFilter !== 'all') {
            if (strtolower($ticket['type_name'] ?? '') !== strtolower($typeFilter)) {
                continue;
            }
        }
        
        // Status filter
        if (!empty($statusFilter) && $statusFilter !== 'all') {
            if (strtolower($ticket['status'] ?? '') !== strtolower($statusFilter)) {
                continue;
            }
        }
        
        // Priority filter
        if (!empty($priorityFilter) && $priorityFilter !== 'all') {
            if (strtolower($ticket['priority'] ?? '') !== strtolower($priorityFilter)) {
                continue;
            }
        }
        
        // Severity filter (same as priority for now)
        if (!empty($severityFilter) && $severityFilter !== 'all') {
            if (strtolower($ticket['priority'] ?? '') !== strtolower($severityFilter)) {
                continue;
            }
        }
        
        $filteredTickets[] = $ticket;
    }

    return ['tickets' => $filteredTickets, 'error' => null];
}

/**
 * Get complete user data by email for ticket display
 */
function getLocalUserByEmail($email) {
    global $conn;
    
    // First try to get user by email from local database
    $stmt = $conn->prepare("SELECT username FROM user WHERE email = ?");
    $stmt->bind_param("s", $email);
    $stmt->execute();
    $result = $stmt->get_result();
    $user = $result->fetch_assoc();
    
    if ($user) {
        // Get complete customer data including Appika data
        $completeData = getCompleteCustomerData($user['username']);
        if ($completeData) {
            return $completeData;
        }
    }
    
    // Fallback to basic local data if customer service fails
    $stmt = $conn->prepare("SELECT id, username, appika_id FROM user WHERE email = ?");
    $stmt->bind_param("s", $email);
    $stmt->execute();
    $result = $stmt->get_result();
    
    return $result->fetch_assoc();
}

/**
 * Format date for display
 */
function formatTicketDate($dateString) {
    if (empty($dateString)) {
        return 'N/A';
    }
    
    try {
        $date = new DateTime($dateString);
        return $date->format('Y-m-d H:i');
    } catch (Exception $e) {
        return htmlspecialchars($dateString);
    }
}

/**
 * Get status badge class
 */
function getStatusBadgeClass($status) {
    switch (strtolower($status)) {
        case 'open':
            return 'badge-success';
        case 'wip':
            return 'badge-warning';
        case 'solved':
            return 'badge-info';
        case 'closed':
            return 'badge-secondary';
        case 'pending':
            return 'badge-warning';
        case 'resolved':
            return 'badge-info';
        default:
            return 'badge-primary';
    }
}

/**
 * Get type badge class
 */
function getTypeBadgeClass($type) {
    switch (strtolower($type)) {
        case 'starter':
            return 'badge-warning';
        case 'business':
        case 'premium':
            return 'badge-info';
        case 'ultimate':
            return 'badge-primary';
        default:
            return 'badge-secondary';
    }
}

/**
 * Get priority badge class
 */
function getPriorityBadgeClass($priority) {
    switch (strtolower($priority)) {
        case 'high':
            return 'badge-danger';
        case 'medium':
            return 'badge-warning';
        case 'low':
            return 'badge-success';
        case 'normal':
            return 'badge-info';
        default:
            return 'badge-secondary';
    }
}

// Handle search and filters
$search = isset($_GET['search']) ? trim($_GET['search']) : '';
$typeFilter = isset($_GET['type']) ? $_GET['type'] : '';
$statusFilter = isset($_GET['status']) ? $_GET['status'] : '';
$priorityFilter = isset($_GET['priority']) ? $_GET['priority'] : '';
$severityFilter = isset($_GET['severity']) ? $_GET['severity'] : '';

// Pagination settings
$itemsPerPage = 10;
$page = isset($_GET['page']) ? max(1, (int)$_GET['page']) : 1;

// Fetch tickets from Appika API
$apiResult = fetchAllAppikaTickets($search, $typeFilter, $statusFilter, $priorityFilter, $severityFilter);
$allTickets = $apiResult['tickets'];
$apiError = $apiResult['error'];

// Calculate pagination
$totalItems = count($allTickets);
$totalPages = ceil($totalItems / $itemsPerPage);
$offset = ($page - 1) * $itemsPerPage;

// Get tickets for current page
$tickets = array_slice($allTickets, $offset, $itemsPerPage);

// Process form submissions
$message = '';
$message_type = '';

// Check for session messages (from redirects)
if (isset($_SESSION['ticket_message'])) {
    $message = $_SESSION['ticket_message'];
    $message_type = $_SESSION['ticket_message_type'];
    unset($_SESSION['ticket_message']);
    unset($_SESSION['ticket_message_type']);
}

// Handle create ticket form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['create_ticket'])) {
    $selectedUserId = (int)$_POST['user_id'];
    $subject = trim($_POST['subject']);
    $ticketType = $_POST['ticket_type'];
    $priority = $_POST['priority'];
    $description = trim($_POST['description']);

    // Validate inputs
    if (empty($selectedUserId) || empty($subject) || empty($ticketType) || empty($priority) || empty($description)) {
        $_SESSION['ticket_message'] = 'All fields are required.';
        $_SESSION['ticket_message_type'] = 'danger';
        header('Location: admin-tickets.php');
        exit();
    } else {
        // Get user data
        $selectedUser = getCompleteCustomerDataById($selectedUserId);
        if (!$selectedUser) {
            // Fallback to basic user data if customer service fails
            $userQuery = "SELECT * FROM user WHERE id = ?";
            $stmt = mysqli_prepare($conn, $userQuery);
            mysqli_stmt_bind_param($stmt, 'i', $selectedUserId);
            mysqli_stmt_execute($stmt);
            $userResult = mysqli_stmt_get_result($stmt);
            $selectedUser = mysqli_fetch_assoc($userResult);
            mysqli_stmt_close($stmt);
        }

        if ($selectedUser) {
            // Check if user has remaining tickets of selected type
            $remaining = $selectedUser[$ticketType . '_tickets'] ?? 0;

            if ($remaining > 0) {
                // Create ticket in Appika API
                $mutation = '
                mutation CreateTicketByContact(
                  $name: String!,
                  $email: String!,
                  $subject: String!,
                  $reply_msg: String!,
                  $type: Int!
                ) {
                  createTicketByContact(
                    name: $name,
                    email: $email,
                    subject: $subject,
                    reply_msg: $reply_msg,
                    type: $type
                  ) {
                    id
                    subject
                    ticketMsg {
                      message
                    }
                  }
                }';

                $typeMapping = ['starter' => 1, 'premium' => 2, 'ultimate' => 3];
                $variables = [
                    'name' => getCustomerDisplayName($selectedUser),
                    'email' => $selectedUser['email'],
                    'subject' => $subject,
                    'reply_msg' => $description,
                    'type' => $typeMapping[$ticketType] ?? 1
                ];

                $apiResult = makeGraphQLRequest($mutation, $variables);

                if ($apiResult['success'] && isset($apiResult['data']['data']['createTicketByContact']['id'])) {
                    $appikaApiId = $apiResult['data']['data']['createTicketByContact']['id'];

                    // Update ticket with priority and type
                    $typeMapping = ['starter' => 1, 'premium' => 2, 'ultimate' => 3];
                    $priorityMapping = ['low' => 'LOW', 'medium' => 'MEDIUM', 'high' => 'HIGH', 'urgent' => 'URGENT'];

                    $updateData = [
                        'agent_id' => null,
                        'subject' => $subject,
                        'type' => $typeMapping[$ticketType] ?? 1,
                        'type_name' => ucfirst($ticketType),
                        'priority' => $priorityMapping[strtolower($priority)] ?? 'MEDIUM',
                        'status' => 'OPEN',
                        'req_email' => $selectedUser['email'],
                        'time_track' => '00:00:00',
                        'reply_msg' => "Ticket created by admin. Priority: $priority, Type: " . ucfirst($ticketType),
                        'tags' => ''
                    ];

                    $updateResult = updateAppikaTicket($appikaApiId, $updateData);

                    // Reduce user's ticket count
                    $updateUserQuery = "UPDATE user SET {$ticketType}_tickets = {$ticketType}_tickets - 1 WHERE id = ?";
                    $stmt = mysqli_prepare($conn, $updateUserQuery);
                    mysqli_stmt_bind_param($stmt, 'i', $selectedUserId);
                    mysqli_stmt_execute($stmt);
                    mysqli_stmt_close($stmt);

                    // Redirect to prevent form resubmission
                    $_SESSION['ticket_message'] = 'Ticket created successfully in Appika API!';
                    $_SESSION['ticket_message_type'] = 'success';
                    header('Location: admin-tickets.php');
                    exit();
                } else {
                    $_SESSION['ticket_message'] = 'Failed to create ticket in Appika API: ' . ($apiResult['error'] ?? 'Unknown error');
                    $_SESSION['ticket_message_type'] = 'danger';
                    header('Location: admin-tickets.php');
                    exit();
                }
            } else {
                $_SESSION['ticket_message'] = 'Selected user has no remaining ' . ucfirst($ticketType) . ' tickets.';
                $_SESSION['ticket_message_type'] = 'warning';
                header('Location: admin-tickets.php');
                exit();
            }
        } else {
            $_SESSION['ticket_message'] = 'Selected user not found.';
            $_SESSION['ticket_message_type'] = 'danger';
            header('Location: admin-tickets.php');
            exit();
        }
    }
}

// Get admin info for display
$admin_username = $_SESSION['admin_username'];
$admin_id = $_SESSION['admin_id'];
$admin_role = $_SESSION['admin_role'];
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin - Ticket Management</title>
    <link rel="shortcut icon" href="../image/png/favicon.png" type="image/x-icon">
    <!-- Bootstrap, fonts & icons -->
    <link rel="stylesheet" href="../css/bootstrap.css">
    <link rel="stylesheet" href="../fonts/icon-font/css/style.css">
    <link rel="stylesheet" href="../fonts/typography-font/typo.css">
    <link rel="stylesheet" href="../fonts/fontawesome-5/css/all.css">
    <!-- Select2 for searchable dropdowns -->
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <!-- Vendor stylesheets -->
    <link rel="stylesheet" href="../css/main.css">

    <style>
    body {
        background-color: #f8f9fa;
    }

    .admin-container {
        padding: 20px;
        max-width: 100%;
        overflow-x: hidden;
    }

    .admin-header {
        background-color: #fff;
        padding: 15px 20px;
        border-radius: 5px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        position: relative;
    }

    /* Responsive container */
    @media (max-width: 991px) {
        .admin-container {
            padding: 15px;
        }

        .admin-header {
            padding: 12px 15px;
        }
    }

    @media (max-width: 767px) {
        .admin-container {
            padding: 10px;
        }
    }

    .admin-header h1 {
        margin: 0;
        font-size: 24px;
        color: #333;
    }

    .admin-user {
        display: flex;
        align-items: center;
    }

    .admin-sidebar {
        background-color: #fff;
        border-radius: 5px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
        padding: 20px;
    }

    .admin-sidebar h3 {
        margin-top: 0;
        margin-bottom: 15px;
        font-size: 18px;
        color: #333;
    }

    .admin-sidebar ul {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .admin-sidebar ul li {
        margin-bottom: 5px;
    }

    .admin-sidebar ul li a {
        display: block;
        padding: 10px 15px;
        color: #333;
        text-decoration: none;
        border-radius: 3px;
        transition: background-color 0.3s;
    }

    .admin-sidebar ul li a:hover,
    .admin-sidebar ul li a.active {
        background-color: #473BF0;
        color: #fff;
    }

    .admin-sidebar ul li a i {
        margin-right: 8px;
        width: 16px;
        text-align: center;
    }

    .admin-content {
        background-color: #fff;
        border-radius: 5px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        padding: 20px;
    }

    .admin-content h2 {
        margin-top: 0;
        margin-bottom: 20px;
        font-size: 22px;
        color: #333;
    }

    .table {
        width: 100%;
        margin-bottom: 1rem;
        color: #212529;
        border-collapse: collapse;
    }

    .table th,
    .table td {
        padding: 0.75rem;
        vertical-align: middle;
        border-top: 1px solid #dee2e6;
    }

    .table thead th {
        vertical-align: bottom;
        border-bottom: 2px solid #dee2e6;
        background-color: #f8f9fa;
        font-weight: 600;
    }

    .table-striped tbody tr:nth-of-type(odd) {
        background-color: rgba(0, 0, 0, 0.03);
    }

    .badge {
        display: inline-block;
        padding: 0.25em 0.6em;
        font-size: 75%;
        font-weight: 700;
        line-height: 1;
        text-align: center;
        white-space: nowrap;
        vertical-align: baseline;
        border-radius: 0.25rem;
    }

    .badge-open {
        background-color: #4CAF50;
        color: #fff;
    }

    .badge-in_progress {
        background-color: #2196F3;
        color: #fff;
    }

    .badge-resolved {
        background-color: #9C27B0;
        color: #fff;
    }

    .badge-closed {
        background-color: #757575;
        color: #fff;
    }

    .badge-starter {
        background-color: #fbbf24;
        color: #fff;
    }

    .badge-premium {
        background-color: #01A7E1;
        color: #fff;
    }

    .badge-ultimate {
        background-color: #793BF0;
        color: #fff;
    }

    .badge-success {
        background-color: #28a745;
        color: #fff;
    }

    .badge-danger {
        background-color: #dc3545;
        color: #fff;
    }

    .badge-warning {
        background-color: #ffc107;
        color: #212529;
    }

    .badge-info {
        background-color: #17a2b8;
        color: #fff;
    }

    .badge-primary {
        background-color: #007bff;
        color: #fff;
    }

    .badge-secondary {
        background-color: #6c757d;
        color: #fff;
    }

    .pagination {
        display: flex;
        padding-left: 0;
        list-style: none;
        border-radius: 0.25rem;
    }

    .pagination .page-link {
        color: #473BF0;
        border-color: #dee2e6;
    }

    .pagination .page-item.active .page-link {
        background-color: #473BF0;
        border-color: #473BF0;
    }

    /* Search and filter styles */
    .search-box {
        width: 100%;
    }

    .search-box .input-group {
        width: 100%;
        max-width: 450px;
    }

    .search-input {
        border-top-right-radius: 0 !important;
        border-bottom-right-radius: 0 !important;
        height: 38px;
        font-size: 14px;
    }

    .search-button {
        border-top-right-radius: 4px !important;
        border-bottom-right-radius: 4px !important;
        border-top-left-radius: 0 !important;
        border-bottom-left-radius: 0 !important;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0.375rem 0.75rem;
        width: 40px !important;
        min-width: 40px !important;
    }

    .filter-select {
        font-size: 14px;
        height: 40px;
        max-height: 300px;
        overflow-y: auto;
    }

    @media (max-width: 991px) {
        .search-box .input-group {
            max-width: 100%;
        }

        .filter-select {
            font-size: 13px;
        }
    }

    @media (max-width: 767px) {
        .search-input {
            font-size: 13px;
        }

        .filter-select {
            font-size: 13px;
        }
    }

    @media (max-width: 480px) {
        .search-input::placeholder {
            font-size: 12px;
        }

        .filter-select {
            font-size: 12px;
        }

        .d-flex.flex-wrap {
            flex-direction: column !important;
        }

        .search-box {
            min-width: 100% !important;
            margin-bottom: 10px;
        }

        .filter-select {
            width: 100% !important;
            margin-bottom: 5px;
        }
    }

    /* Modal Styles */
    .modal {
        z-index: 1050;
    }

    .modal-backdrop {
        z-index: 1040;
    }

    .modal-dialog {
        margin: 30px auto;
    }

    .modal-content {
        border-radius: 8px;
        border: none;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    }

    .modal-header {
        background-color: #473BF0;
        color: white;
        border-bottom: none;
        border-radius: 8px 8px 0 0;
    }

    .modal-header .close {
        color: white;
        opacity: 0.8;
    }

    .modal-header .close:hover {
        opacity: 1;
    }

    /* Select2 Custom Styles */
    .select2-container--default .select2-selection--single {
        height: 38px;
        border: 1px solid #ced4da;
        border-radius: 4px;
    }

    .select2-container--default .select2-selection--single .select2-selection__rendered {
        line-height: 36px;
        padding-left: 12px;
    }

    .select2-container--default .select2-selection--single .select2-selection__arrow {
        height: 36px;
    }

    .select2-dropdown {
        border: 1px solid #ced4da;
        border-radius: 4px;
    }

    .select2-container--default .select2-results__option--highlighted[aria-selected] {
        background-color: #473BF0;
    }

    /* Manual modal styles */
    .modal.show {
        display: block !important;
    }

    .modal-backdrop {
        position: fixed;
        top: 0;
        left: 0;
        z-index: 1040;
        width: 100vw;
        height: 100vh;
        background-color: #000;
        opacity: 0.5;
    }

    .modal-backdrop.fade.show {
        opacity: 0.5;
    }

    body.modal-open {
        overflow: hidden;
    }

    /* User dropdown styles */
    .user-dropdown {
        position: relative;
        display: inline-block;
    }

    .mobile-menu-toggle {
        display: none;
        transition: transform 0.3s ease;
    }

    .user-dropdown.active .mobile-menu-toggle {
        transform: rotate(180deg);
    }

    .dropdown-content {
        display: block;
    }

    /* Mobile Styles */
    @media (max-width: 767px) {
        .admin-header {
            flex-direction: column;
            align-items: flex-start;
        }

        .admin-header h1 {
            margin-bottom: 10px;
        }

        .admin-user {
            width: 100%;
        }

        .user-dropdown {
            width: 100%;
        }

        .mobile-menu-toggle {
            display: inline-block;
        }

        .dropdown-content {
            display: none;
            position: absolute;
            background-color: #f9f9f9;
            width: 100%;
            left: 0;
            box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.2);
            z-index: 1;
            border-radius: 4px;
            margin-top: 5px;
            padding: 8px;
        }

        .dropdown-content .btn {
            width: 100%;
            text-align: center;
        }

        .user-dropdown.active .dropdown-content {
            display: block;
        }
    }

    /* Button width and chat button color fixes */
    .btn-group .btn {
        min-width: 60px !important;
        max-width: 80px !important;
        padding: 0.25rem 0.5rem !important;
        font-size: 0.875rem !important;
    }

    /* Chat button - use brand color */
    .btn-success {
        background-color: #473BF0 !important;
        border-color: #473BF0 !important;
        color: white !important;
    }

    .btn-success:hover {
        background-color: #3d32d9 !important;
        border-color: #3d32d9 !important;
        color: white !important;
    }
    </style>
</head>

<body>
    <div class="admin-container">
        <div class="admin-header">
            <h1>Ticket Management</h1>
            <div class="admin-user">
                <div class="user-dropdown">
                    <span class="user-info">
                        Welcome, <?php echo htmlspecialchars($admin_username); ?>
                        (<?php echo htmlspecialchars($admin_role); ?>)
                        <i class="mobile-menu-toggle fas fa-chevron-down"></i>
                    </span>
                    <div class="dropdown-content">
                        <a href="admin-logout.php" class="btn btn-sm btn-outline-danger">Logout</a>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-3">
                <?php include('admin-menu.php'); ?>
            </div>
            <div class="col-md-9">
                <div class="admin-content">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h2>All Tickets</h2>
                        <button type="button" class="btn btn-sm" style="background-color: #473BF0; color: white;" onclick="openCreateTicketModal()">
                            <i class="fas fa-plus"></i> &nbsp; Create New Ticket
                        </button>
                    </div>

                    <?php if (!empty($message)): ?>
                    <div class="alert alert-<?php echo $message_type; ?> alert-dismissible fade show" role="alert">
                        <?php echo htmlspecialchars($message); ?>
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <?php endif; ?>

                    <?php if ($apiError): ?>
                    <div class="alert alert-danger" role="alert">
                        <strong>API Error:</strong> <?php echo htmlspecialchars($apiError); ?>
                    </div>
                    <?php endif; ?>

                    <!-- Search and Filter Form -->
                    <div class="d-flex flex-wrap align-items-center justify-content-between" style="gap: 10px; margin-bottom: 20px;">
                        <div class="d-flex flex-wrap align-items-center" style="gap: 10px; flex:1; min-width:320px;">
                            <form method="GET" class="d-flex flex-wrap align-items-center" style="gap: 10px; flex:1; width:100%;">
                                <div class="search-box" style="flex:1; min-width:220px;">
                                    <div class="input-group">
                                        <input type="text" name="search" class="form-control search-input"
                                               placeholder="Search by ID, APK No, subject, username or date"
                                               value="<?php echo htmlspecialchars($search); ?>">
                                        <div class="input-group-append">
                                            <button type="submit" class="btn btn-primary search-button">
                                                <i class="fas fa-search"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <div style="width:120px;">
                                    <select name="type" class="form-control filter-select" onchange="this.form.submit()">
                                        <option value="">All Types</option>
                                        <option value="starter" <?php echo $typeFilter === 'starter' ? 'selected' : ''; ?>>Starter</option>
                                        <option value="business" <?php echo $typeFilter === 'business' ? 'selected' : ''; ?>>Business</option>
                                        <option value="ultimate" <?php echo $typeFilter === 'ultimate' ? 'selected' : ''; ?>>Ultimate</option>
                                    </select>
                                </div>
                                <div style="width:120px;">
                                    <select name="status" class="form-control filter-select" onchange="this.form.submit()">
                                        <option value="">All Status</option>
                                        <option value="open" <?php echo $statusFilter === 'open' ? 'selected' : ''; ?>>Open</option>
                                        <option value="wip" <?php echo $statusFilter === 'wip' ? 'selected' : ''; ?>>WIP</option>
                                        <option value="solved" <?php echo $statusFilter === 'solved' ? 'selected' : ''; ?>>Solved</option>
                                        <option value="closed" <?php echo $statusFilter === 'closed' ? 'selected' : ''; ?>>Closed</option>
                                    </select>
                                </div>
                                <div style="width:120px;">
                                    <select name="priority" class="form-control filter-select" onchange="this.form.submit()">
                                        <option value="">All Priorities</option>
                                        <option value="high" <?php echo $priorityFilter === 'high' ? 'selected' : ''; ?>>High</option>
                                        <option value="medium" <?php echo $priorityFilter === 'medium' ? 'selected' : ''; ?>>Medium</option>
                                        <option value="normal" <?php echo $priorityFilter === 'normal' ? 'selected' : ''; ?>>Normal</option>
                                        <option value="low" <?php echo $priorityFilter === 'low' ? 'selected' : ''; ?>>Low</option>
                                    </select>
                                </div>
                                <div style="width:120px;">
                                    <select name="severity" class="form-control filter-select" onchange="this.form.submit()">
                                        <option value="">All Severities</option>
                                        <option value="high" <?php echo $severityFilter === 'high' ? 'selected' : ''; ?>>High</option>
                                        <option value="medium" <?php echo $severityFilter === 'medium' ? 'selected' : ''; ?>>Medium</option>
                                        <option value="normal" <?php echo $severityFilter === 'normal' ? 'selected' : ''; ?>>Normal</option>
                                        <option value="low" <?php echo $severityFilter === 'low' ? 'selected' : ''; ?>>Low</option>
                                    </select>
                                </div>
                            </form>
                        </div>
                        <?php if (!empty($search) || !empty($typeFilter) || !empty($statusFilter) || !empty($priorityFilter) || !empty($severityFilter)): ?>
                        <div>
                            <a href="admin-tickets.php" class="btn btn-sm" style="background-color: #473BF0; color: white;">
                                <i class="fas fa-times"></i> Clear Filters
                            </a>
                        </div>
                        <?php endif; ?>
                    </div>

                    <!-- Tickets Table -->
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Subject</th>
                                    <th>User</th>
                                    <th>Type</th>
                                    <th>Priority</th>
                                    <th>Severity</th>
                                    <th>Status</th>
                                    <th>Assigned Admin</th>
                                    <th>Created</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                    <?php if (!empty($tickets)): ?>
                                    <?php foreach ($tickets as $ticket): ?>
                                    <?php 
                                        // Get local user data for display
                                        $localUser = getLocalUserByEmail($ticket['req_email'] ?? '');
                                        $username = $localUser ? $localUser['username'] : 'Unknown';
                                        $userAppikaId = $localUser ? $localUser['appika_id'] : 'N/A';
                                        
                                        // Get contact name from contacts array
                                        $contactName = '';
                                        if (!empty($ticket['contacts']) && is_array($ticket['contacts'])) {
                                            $contactName = $ticket['contacts'][0]['fname'] ?? '';
                                        }
                                        
                                        // Use contact name if available, otherwise use username
                                        $displayName = !empty($contactName) ? $contactName : $username;
                                    ?>
                                    <tr>
                                        <td>
                                            <strong><?php echo htmlspecialchars($ticket['ticket_no'] ?? ''); ?></strong>
                                            <?php if ($ticket['creator_by_contact'] === 'y'): ?>
                                            <span class="badge badge-info badge-sm ml-1">New</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <strong><?php echo htmlspecialchars($ticket['subject'] ?? ''); ?></strong>
                                        </td>
                                        <td>
                                            <?php echo htmlspecialchars($displayName); ?>
                                            <?php if ($userAppikaId !== 'N/A'): ?>
                                            <br><small class="text-muted"><?php echo htmlspecialchars($userAppikaId); ?></small>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <span class="badge <?php echo getTypeBadgeClass($ticket['type_name'] ?? ''); ?>">
                                                <?php echo htmlspecialchars($ticket['type_name'] ?? 'Unknown'); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge <?php echo getPriorityBadgeClass($ticket['priority'] ?? ''); ?>">
                                                <?php echo htmlspecialchars(ucfirst($ticket['priority'] ?? 'Normal')); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge <?php echo getPriorityBadgeClass($ticket['priority'] ?? ''); ?>">
                                                <?php echo htmlspecialchars(ucfirst($ticket['priority'] ?? 'Normal')); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge <?php echo getStatusBadgeClass($ticket['status'] ?? ''); ?>">
                                                <?php echo htmlspecialchars(ucfirst($ticket['status'] ?? 'Unknown')); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <?php if (!empty($ticket['agent_id'])): ?>
                                                Agent ID: <?php echo htmlspecialchars($ticket['agent_id']); ?>
                                            <?php else: ?>
                                                <span class="text-muted">Not assigned</span>
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo formatTicketDate($ticket['created'] ?? ''); ?></td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="appika-ticket-details.php?ticket_id=<?php echo urlencode($ticket['id']); ?>"
                                               class="btn btn-sm btn-primary" target="_blank">
                                                <i class="fas fa-eye"></i>&nbsp;View
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                                <?php else: ?>
                                <tr>
                                    <td colspan="11" class="text-center text-muted">
                                        <?php if (!empty($search) || !empty($typeFilter) || !empty($statusFilter) || !empty($priorityFilter) || !empty($severityFilter)): ?>
                                            No tickets found matching your filters.
                                        <?php else: ?>
                                            No tickets found.
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <?php if ($totalPages > 1): ?>
                    <nav aria-label="Page navigation" class="mt-4">
                        <ul class="pagination justify-content-center">
                            <?php if ($page > 1): ?>
                            <li class="page-item">
                                <a class="page-link" href="?page=<?php echo $page - 1; ?><?php echo !empty($search) ? '&search=' . urlencode($search) : ''; ?><?php echo !empty($typeFilter) ? '&type=' . urlencode($typeFilter) : ''; ?><?php echo !empty($statusFilter) ? '&status=' . urlencode($statusFilter) : ''; ?><?php echo !empty($priorityFilter) ? '&priority=' . urlencode($priorityFilter) : ''; ?><?php echo !empty($severityFilter) ? '&severity=' . urlencode($severityFilter) : ''; ?>">
                                    Previous
                                </a>
                            </li>
                            <?php endif; ?>

                            <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
                            <li class="page-item <?php echo $i == $page ? 'active' : ''; ?>">
                                <a class="page-link" href="?page=<?php echo $i; ?><?php echo !empty($search) ? '&search=' . urlencode($search) : ''; ?><?php echo !empty($typeFilter) ? '&type=' . urlencode($typeFilter) : ''; ?><?php echo !empty($statusFilter) ? '&status=' . urlencode($statusFilter) : ''; ?><?php echo !empty($priorityFilter) ? '&priority=' . urlencode($priorityFilter) : ''; ?><?php echo !empty($severityFilter) ? '&severity=' . urlencode($severityFilter) : ''; ?>">
                                    <?php echo $i; ?>
                                </a>
                            </li>
                            <?php endfor; ?>

                            <?php if ($page < $totalPages): ?>
                            <li class="page-item">
                                <a class="page-link" href="?page=<?php echo $page + 1; ?><?php echo !empty($search) ? '&search=' . urlencode($search) : ''; ?><?php echo !empty($typeFilter) ? '&type=' . urlencode($typeFilter) : ''; ?><?php echo !empty($statusFilter) ? '&status=' . urlencode($statusFilter) : ''; ?><?php echo !empty($priorityFilter) ? '&priority=' . urlencode($priorityFilter) : ''; ?><?php echo !empty($severityFilter) ? '&severity=' . urlencode($severityFilter) : ''; ?>">
                                    Next
                                </a>
                            </li>
                            <?php endif; ?>
                        </ul>
                    </nav>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Create Ticket Modal -->
    <div class="modal fade" id="createTicketModal" tabindex="-1" role="dialog" aria-labelledby="createTicketModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="createTicketModalLabel" style="color: white;">Create New Ticket</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close" onclick="closeCreateTicketModal()">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <form method="POST" action="" id="createTicketForm">
                    <div class="modal-body">
                        <input type="hidden" name="create_ticket" value="1">

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="user_id">Select User <span class="text-danger">*</span></label>
                                    <select class="form-control" id="user_id" name="user_id" required onchange="updateTicketTypes()">
                                        <option value="">Choose a user...</option>
                                        <?php
                                        // Fetch users for dropdown
                                        $usersQuery = "SELECT id, username, email, starter_tickets, premium_tickets, ultimate_tickets FROM user ORDER BY username ASC";
                                        $usersResult = mysqli_query($conn, $usersQuery);
                                        while ($user = mysqli_fetch_assoc($usersResult)) {
                                            // Get complete customer data for proper display name
                                            $completeUserData = getCompleteCustomerData($user['username']);
                                            $displayName = $completeUserData ? getCustomerDisplayName($completeUserData) : $user['username'];
                                            echo "<option value='{$user['id']}' data-starter='{$user['starter_tickets']}' data-premium='{$user['premium_tickets']}' data-ultimate='{$user['ultimate_tickets']}'>";
                                            echo htmlspecialchars($displayName) . " ({$user['email']})";
                                            echo "</option>";
                                        }
                                        ?>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="ticket_type">Ticket Type <span class="text-danger">*</span></label>
                                    <select class="form-control" id="ticket_type" name="ticket_type" required>
                                        <option value="">Select user first...</option>
                                    </select>
                                    <small class="form-text text-muted">Available types based on user's remaining tickets</small>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="subject">Subject <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="subject" name="subject" required maxlength="255">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="priority">Priority <span class="text-danger">*</span></label>
                                    <select class="form-control" id="priority" name="priority" required>
                                        <option value="">Select priority...</option>
                                        <option value="low">Low</option>
                                        <option value="medium" selected>Medium</option>
                                        <option value="high">High</option>
                                        <option value="urgent">Urgent</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="description">Description <span class="text-danger">*</span></label>
                            <textarea class="form-control" id="description" name="description" rows="4" required placeholder="Describe the issue in detail..."></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn" style="background-color: #dc3545; color: white;" data-dismiss="modal" onclick="closeCreateTicketModal()">
                            <i class="fas fa-times"></i> &nbsp; Cancel
                        </button>
                        <button type="submit" class="btn" style="background-color: #473BF0; color: white !important;">
                            <i class="fas fa-plus"></i> &nbsp; Create Ticket
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="../plugins/jquery/jquery.min.js"></script>
    <!-- <script src="../js/bootstrap.js"></script> -->
    <!-- Select2 JS -->
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <script>
        // User dropdown functionality for mobile
        const userDropdown = document.querySelector('.user-dropdown');
        const userInfo = document.querySelector('.user-info');
        const menuToggle = document.querySelector('.mobile-menu-toggle');

        if (userInfo && menuToggle) {
            userInfo.addEventListener('click', function() {
                userDropdown.classList.toggle('active');
            });
        }

        // Function to manually open the modal
        function openCreateTicketModal() {
            // Reset form first
            resetCreateTicketModal();

            // Try Bootstrap modal first
            if (typeof $ !== 'undefined' && typeof $.fn.modal !== 'undefined') {
                $('#createTicketModal').modal('show');
            } else {
                // Manual modal display
                const modal = document.getElementById('createTicketModal');
                if (!modal) {
                    console.error('Modal element not found!');
                    return;
                }
                modal.style.display = 'block';
                modal.classList.add('show');
                modal.setAttribute('aria-hidden', 'false');
                document.body.classList.add('modal-open');

                // Create backdrop
                let backdrop = document.querySelector('.modal-backdrop');
                if (!backdrop) {
                    backdrop = document.createElement('div');
                    backdrop.className = 'modal-backdrop fade show';
                    document.body.appendChild(backdrop);

                    // Close modal when clicking backdrop
                    backdrop.addEventListener('click', function() {
                        closeCreateTicketModal();
                    });
                }
            }
        }

        // Function to close modal manually
        function closeCreateTicketModal() {
            const modal = document.getElementById('createTicketModal');
            const backdrop = document.querySelector('.modal-backdrop');

            if (modal) {
                modal.style.display = 'none';
                modal.classList.remove('show');
                modal.setAttribute('aria-hidden', 'true');
            }

            if (backdrop) {
                backdrop.remove();
            }

            document.body.classList.remove('modal-open');
        }

        // Function to update ticket types based on selected user
        function updateTicketTypes() {
            const userSelect = document.getElementById('user_id');
            const ticketTypeSelect = document.getElementById('ticket_type');
            const selectedOption = userSelect.options[userSelect.selectedIndex];

            // Clear existing options
            ticketTypeSelect.innerHTML = '<option value="">Select ticket type...</option>';

            if (selectedOption.value) {
                const starterTickets = parseInt(selectedOption.getAttribute('data-starter')) || 0;
                const premiumTickets = parseInt(selectedOption.getAttribute('data-premium')) || 0;
                const ultimateTickets = parseInt(selectedOption.getAttribute('data-ultimate')) || 0;

                // Add available ticket types
                if (starterTickets > 0) {
                    ticketTypeSelect.innerHTML += `<option value="starter">Starter (${starterTickets} remaining)</option>`;
                }
                if (premiumTickets > 0) {
                    ticketTypeSelect.innerHTML += `<option value="premium">Business (${premiumTickets} remaining)</option>`;
                }
                if (ultimateTickets > 0) {
                    ticketTypeSelect.innerHTML += `<option value="ultimate">Ultimate (${ultimateTickets} remaining)</option>`;
                }

                // Show message if no tickets available
                if (starterTickets === 0 && premiumTickets === 0 && ultimateTickets === 0) {
                    ticketTypeSelect.innerHTML += '<option value="" disabled>No tickets available for this user</option>';
                }
            }
        }

        // Function to reset modal form
        function resetCreateTicketModal() {
            document.getElementById('createTicketForm').reset();

            // Reset Select2 dropdown safely
            const userSelect = document.getElementById('user_id');
            if (userSelect) {
                userSelect.value = '';
                // Try to trigger Select2 change if available
                if (typeof $ !== 'undefined' && $('#user_id').data('select2')) {
                    $('#user_id').val(null).trigger('change');
                }
            }

            document.getElementById('ticket_type').innerHTML = '<option value="">Select ticket type...</option>';
        }

        // Initialize Select2 and other components
        $(document).ready(function() {
            // Initialize Select2 for user dropdown
            $('#user_id').select2({
                placeholder: 'Choose a user...',
                allowClear: true,
                dropdownParent: $('#createTicketModal'),
                width: '100%'
            });

            // Add change event listener for user selection
            $('#user_id').on('change', function() {
                updateTicketTypes();
            });
        });
    </script>
</body>
</html>
