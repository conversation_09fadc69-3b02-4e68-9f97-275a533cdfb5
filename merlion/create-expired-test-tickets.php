<?php
session_start();
include('../functions/server.php');
require_once('../config/ticket-expiration-config.php');
require_once('../functions/ticket-expiration-functions.php');

// Check if admin is logged in
if (!isset($_SESSION['admin_username'])) {
    header('location: admin-login');
    exit();
}

$message = '';
$message_type = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['create_expired_tickets'])) {
        $test_username = $_POST['username'] ?? 'HC185';
        
        // Create tickets with different expiration scenarios
        $test_scenarios = [
            [
                'name' => 'Expired 6 months ago',
                'purchase_date' => date('Y-m-d H:i:s', strtotime('-18 months')), // 18 months ago
                'ticket_type' => 'starter',
                'quantity' => 5
            ],
            [
                'name' => 'Expired 1 month ago', 
                'purchase_date' => date('Y-m-d H:i:s', strtotime('-13 months')), // 13 months ago
                'ticket_type' => 'ultimate',
                'quantity' => 3
            ],
            [
                'name' => 'Expiring in 1 week',
                'purchase_date' => date('Y-m-d H:i:s', strtotime('-11 months -3 weeks')), // Almost expired
                'ticket_type' => 'starter',
                'quantity' => 2
            ],
            [
                'name' => 'Expiring in 1 month',
                'purchase_date' => date('Y-m-d H:i:s', strtotime('-11 months')), // Expiring soon
                'ticket_type' => 'ultimate',
                'quantity' => 4
            ],
            [
                'name' => 'Fresh tickets (6 months old)',
                'purchase_date' => date('Y-m-d H:i:s', strtotime('-6 months')), // Still valid
                'ticket_type' => 'starter',
                'quantity' => 10
            ]
        ];
        
        $created_count = 0;
        foreach ($test_scenarios as $scenario) {
            $expiration_date = getTicketExpirationDate($scenario['purchase_date']);
            
            $insert_query = "INSERT INTO purchasetickets 
                           (username, ticket_type, package_size, numbers_per_package, dollar_price_per_package, 
                            purchase_time, transactionid, remaining_tickets, expiration_date) 
                           VALUES (?, ?, ?, ?, 99.99, ?, ?, ?, ?)";
            
            $stmt = mysqli_prepare($conn, $insert_query);
            $transaction_id = 'EXPIRED_TEST_' . time() . '_' . rand(1000, 9999);
            
            mysqli_stmt_bind_param($stmt, 'ssississ', 
                $test_username,
                $scenario['ticket_type'],
                $scenario['name'],
                $scenario['quantity'],
                $scenario['purchase_date'],
                $transaction_id,
                $scenario['quantity'],
                $expiration_date
            );
            
            if (mysqli_stmt_execute($stmt)) {
                $created_count++;
            }
            mysqli_stmt_close($stmt);
        }
        
        $message = "Created $created_count test ticket scenarios for testing expiration!";
        $message_type = 'success';
        
    } elseif (isset($_POST['clear_expired_tests'])) {
        $delete_query = "DELETE FROM purchasetickets WHERE transactionid LIKE 'EXPIRED_TEST_%'";
        if (mysqli_query($conn, $delete_query)) {
            $affected = mysqli_affected_rows($conn);
            $message = "Cleared $affected expired test records.";
            $message_type = 'success';
        }
    }
}

// Get current expired tickets
$expired_tickets = getExpiredTickets();
$expiring_soon = getTicketsExpiringSoon();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Expired Tickets</title>
    <link rel="shortcut icon" href="../image/png/favicon.png" type="image/x-icon">
    <link rel="stylesheet" href="../css/bootstrap.css">
    <link rel="stylesheet" href="../fonts/fontawesome-5/css/all.css">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .container {
            max-width: 1200px;
            margin: 20px auto;
            padding: 20px;
        }
        .card {
            border: none;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .card-header {
            background: linear-gradient(135deg, #473BF0 0%, #5B4CF0 100%);
            color: white;
            border-radius: 10px 10px 0 0 !important;
            padding: 15px 20px;
        }
        .btn-primary {
            background: linear-gradient(135deg, #473BF0 0%, #5B4CF0 100%);
            border: none;
            border-radius: 6px;
        }
        .btn-danger {
            background: linear-gradient(135deg, #dc3545 0%, #e74c3c 100%);
            border: none;
            border-radius: 6px;
        }
        .btn-warning {
            background: linear-gradient(135deg, #ffc107 0%, #ffb300 100%);
            border: none;
            border-radius: 6px;
            color: #000;
        }
        .table {
            border-radius: 8px;
            overflow: hidden;
        }
        .table thead th {
            background-color: #f8f9fa;
            border: none;
            font-weight: 600;
        }
        .badge-expired {
            background-color: #dc3545;
        }
        .badge-expiring {
            background-color: #ffc107;
            color: #000;
        }
        .badge-active {
            background-color: #28a745;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="fas fa-clock"></i> Test Expired Tickets</h1>
            <div>
                <a href="test-ticket-expiration.php" class="btn btn-secondary mr-2">
                    <i class="fas fa-vial"></i> Main Test Page
                </a>
                <a href="ticket-expiration-manager.php" class="btn btn-secondary">
                    <i class="fas fa-cog"></i> Expiration Manager
                </a>
            </div>
        </div>

        <!-- Messages -->
        <?php if ($message): ?>
        <div class="alert alert-<?php echo $message_type === 'error' ? 'danger' : $message_type; ?> alert-dismissible fade show">
            <?php echo htmlspecialchars($message); ?>
            <button type="button" class="close" data-dismiss="alert">
                <span>&times;</span>
            </button>
        </div>
        <?php endif; ?>

        <!-- Test Actions -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-play"></i> Create Test Scenarios</h5>
            </div>
            <div class="card-body">
                <form method="POST" class="mb-3">
                    <div class="row">
                        <div class="col-md-6">
                            <label for="username">Test Username:</label>
                            <input type="text" name="username" id="username" class="form-control" value="HC185" required>
                        </div>
                        <div class="col-md-6 d-flex align-items-end">
                            <button type="submit" name="create_expired_tickets" class="btn btn-primary mr-2">
                                <i class="fas fa-plus"></i> Create Expired Test Tickets
                            </button>
                            <button type="submit" name="clear_expired_tests" class="btn btn-danger">
                                <i class="fas fa-trash"></i> Clear Test Data
                            </button>
                        </div>
                    </div>
                </form>
                
                <div class="alert alert-info">
                    <h6><i class="fas fa-info-circle"></i> This will create 5 test scenarios:</h6>
                    <ul class="mb-0">
                        <li><strong>Expired 6 months ago:</strong> 5 starter tickets (should be removed by cleanup)</li>
                        <li><strong>Expired 1 month ago:</strong> 3 ultimate tickets (should be removed by cleanup)</li>
                        <li><strong>Expiring in 1 week:</strong> 2 starter tickets (should show warning)</li>
                        <li><strong>Expiring in 1 month:</strong> 4 ultimate tickets (should show warning)</li>
                        <li><strong>Fresh tickets:</strong> 10 starter tickets (should remain active)</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Current Expired Tickets -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-exclamation-triangle"></i> Currently Expired Tickets</h5>
            </div>
            <div class="card-body">
                <?php if (empty($expired_tickets)): ?>
                    <p class="text-muted">No expired tickets found.</p>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Username</th>
                                    <th>Ticket Type</th>
                                    <th>Remaining Qty</th>
                                    <th>Purchase Date</th>
                                    <th>Expiration Date</th>
                                    <th>Days Expired</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($expired_tickets as $ticket): 
                                    $days_expired = abs(getDaysUntilExpiration($ticket['purchase_time']));
                                ?>
                                <tr>
                                    <td><?php echo $ticket['username']; ?></td>
                                    <td><span class="badge badge-secondary"><?php echo ucfirst($ticket['ticket_type']); ?></span></td>
                                    <td><?php echo $ticket['remaining_tickets']; ?></td>
                                    <td><?php echo date('M d, Y', strtotime($ticket['purchase_time'])); ?></td>
                                    <td><?php echo date('M d, Y', strtotime($ticket['expiration_date'])); ?></td>
                                    <td><span class="badge badge-expired"><?php echo $days_expired; ?> days ago</span></td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Tickets Expiring Soon -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-clock"></i> Tickets Expiring Soon</h5>
            </div>
            <div class="card-body">
                <?php if (empty($expiring_soon)): ?>
                    <p class="text-muted">No tickets expiring soon.</p>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Username</th>
                                    <th>Ticket Type</th>
                                    <th>Remaining Qty</th>
                                    <th>Purchase Date</th>
                                    <th>Expiration Date</th>
                                    <th>Days Left</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($expiring_soon as $ticket): 
                                    $days_left = getDaysUntilExpiration($ticket['purchase_time']);
                                ?>
                                <tr>
                                    <td><?php echo $ticket['username']; ?></td>
                                    <td><span class="badge badge-secondary"><?php echo ucfirst($ticket['ticket_type']); ?></span></td>
                                    <td><?php echo $ticket['remaining_tickets']; ?></td>
                                    <td><?php echo date('M d, Y', strtotime($ticket['purchase_time'])); ?></td>
                                    <td><?php echo date('M d, Y', strtotime($ticket['expiration_date'])); ?></td>
                                    <td><span class="badge badge-expiring"><?php echo $days_left; ?> days</span></td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Testing Instructions -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-book"></i> How to Test Expired Tickets</h5>
            </div>
            <div class="card-body">
                <h6>Step-by-Step Testing:</h6>
                <ol>
                    <li><strong>Create Test Data:</strong> Click "Create Expired Test Tickets" above</li>
                    <li><strong>View Expired Tickets:</strong> Check the "Currently Expired Tickets" table</li>
                    <li><strong>Test Cleanup:</strong> Go to <a href="ticket-expiration-manager.php">Expiration Manager</a> and click "Run Cleanup Now"</li>
                    <li><strong>Verify Removal:</strong> Refresh this page to see expired tickets removed</li>
                    <li><strong>Check Logs:</strong> View the expiration log in the admin panel</li>
                </ol>
                
                <h6 class="mt-4">Alternative Testing Methods:</h6>
                <ul>
                    <li><strong>Change Configuration:</strong> Set ticket lifetime to 1 month in admin panel</li>
                    <li><strong>Manual Database:</strong> Insert tickets with past dates directly in phpMyAdmin</li>
                    <li><strong>Cron Job Test:</strong> Run the cleanup script manually: <code>php cron/ticket-expiration-cleanup.php</code></li>
                </ul>
            </div>
        </div>
    </div>

    <script src="../js/vendor.min.js"></script>
</body>
</html>
