<?php
/**
 * Fix Ticket Expiration Settings
 * This script checks and fixes the ticket expiration configuration
 * to prevent newly added tickets from being immediately removed
 */

session_start();
include('../functions/server.php');
include('../config/ticket-expiration-config.php');

// Check if admin is logged in
if (!isset($_SESSION['admin_username'])) {
    header('location: admin-login.php');
    exit();
}

$admin_username = $_SESSION['admin_username'];
$admin_role = $_SESSION['admin_role'];

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fix Ticket Expiration Settings</title>
    <link rel="stylesheet" href="../css/bootstrap.css">
    <style>
        body { background-color: #f8f9fa; padding: 20px; }
        .container { max-width: 800px; }
        .alert { margin: 10px 0; }
        .config-item { background: white; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid #007bff; }
        .btn-fix { background-color: #28a745; color: white; }
        .btn-disable { background-color: #dc3545; color: white; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Fix Ticket Expiration Settings</h1>
        
        <?php
        // Get current configuration
        $config = getTicketExpirationConfig();
        
        echo "<div class='alert alert-info'>";
        echo "<h4>Current Configuration:</h4>";
        echo "<ul>";
        echo "<li><strong>System Enabled:</strong> " . ($config['system_enabled'] ? 'Yes' : 'No') . "</li>";
        echo "<li><strong>Auto Cleanup Enabled:</strong> " . ($config['auto_cleanup_enabled'] ? 'Yes' : 'No') . "</li>";
        echo "<li><strong>Ticket Lifetime:</strong> " . $config['ticket_lifetime_months'] . " " . ($config['ticket_lifetime_unit'] ?? 'months') . "</li>";
        echo "<li><strong>Cleanup Frequency:</strong> " . $config['cleanup_frequency_days'] . " " . ($config['cleanup_frequency_unit'] ?? 'days') . "</li>";
        echo "<li><strong>Warning Period:</strong> " . $config['warning_period_days'] . " " . ($config['warning_period_unit'] ?? 'days') . "</li>";
        echo "<li><strong>Last Cleanup:</strong> " . ($config['last_cleanup_timestamp'] ? date('Y-m-d H:i:s', $config['last_cleanup_timestamp']) : 'Never') . "</li>";
        echo "</ul>";
        echo "</div>";
        
        // Check for problematic settings
        $problems = [];
        if (($config['ticket_lifetime_unit'] ?? 'months') === 'minutes') {
            $problems[] = "Ticket lifetime is set to MINUTES - tickets expire too quickly!";
        }
        if (($config['cleanup_frequency_unit'] ?? 'days') === 'minutes') {
            $problems[] = "Cleanup frequency is set to MINUTES - cleanup runs too often!";
        }
        if ($config['auto_cleanup_enabled'] && count($problems) > 0) {
            $problems[] = "Auto cleanup is ENABLED with problematic settings!";
        }
        
        if (!empty($problems)) {
            echo "<div class='alert alert-danger'>";
            echo "<h4>⚠️ Problems Found:</h4>";
            echo "<ul>";
            foreach ($problems as $problem) {
                echo "<li>$problem</li>";
            }
            echo "</ul>";
            echo "</div>";
        } else {
            echo "<div class='alert alert-success'>";
            echo "<h4>✅ Configuration looks good!</h4>";
            echo "</div>";
        }
        
        // Handle fix actions
        if (isset($_POST['action'])) {
            $action = $_POST['action'];
            $success = false;
            $message = '';
            
            switch ($action) {
                case 'disable_auto_cleanup':
                    $success = updateTicketExpirationConfig('auto_cleanup_enabled', false);
                    $message = $success ? "Auto cleanup disabled successfully!" : "Failed to disable auto cleanup.";
                    break;
                    
                case 'fix_units':
                    $updates = [
                        'ticket_lifetime_unit' => 'months',
                        'cleanup_frequency_unit' => 'days',
                        'warning_period_unit' => 'days'
                    ];
                    $success = true;
                    foreach ($updates as $key => $value) {
                        if (!updateTicketExpirationConfig($key, $value)) {
                            $success = false;
                            break;
                        }
                    }
                    $message = $success ? "Time units fixed successfully!" : "Failed to fix time units.";
                    break;
                    
                case 'safe_settings':
                    $safe_updates = [
                        'auto_cleanup_enabled' => false,
                        'ticket_lifetime_months' => 12,
                        'ticket_lifetime_unit' => 'months',
                        'cleanup_frequency_days' => 7,
                        'cleanup_frequency_unit' => 'days',
                        'warning_period_days' => 30,
                        'warning_period_unit' => 'days'
                    ];
                    $success = true;
                    foreach ($safe_updates as $key => $value) {
                        if (!updateTicketExpirationConfig($key, $value)) {
                            $success = false;
                            break;
                        }
                    }
                    $message = $success ? "Safe settings applied successfully!" : "Failed to apply safe settings.";
                    break;
            }
            
            if ($success) {
                echo "<div class='alert alert-success'>✅ $message</div>";
                echo "<script>setTimeout(function(){ window.location.reload(); }, 2000);</script>";
            } else {
                echo "<div class='alert alert-danger'>❌ $message</div>";
            }
        }
        ?>
        
        <div class="row">
            <div class="col-md-4">
                <div class="config-item">
                    <h5>🛑 Quick Fix: Disable Auto Cleanup</h5>
                    <p>Temporarily disable automatic cleanup to prevent tickets from being removed.</p>
                    <form method="POST">
                        <input type="hidden" name="action" value="disable_auto_cleanup">
                        <button type="submit" class="btn btn-disable">Disable Auto Cleanup</button>
                    </form>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="config-item">
                    <h5>🔧 Fix Time Units</h5>
                    <p>Fix time units to use proper values (months/days instead of minutes).</p>
                    <form method="POST">
                        <input type="hidden" name="action" value="fix_units">
                        <button type="submit" class="btn btn-fix">Fix Time Units</button>
                    </form>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="config-item">
                    <h5>✅ Apply Safe Settings</h5>
                    <p>Apply recommended safe settings for production use.</p>
                    <form method="POST">
                        <input type="hidden" name="action" value="safe_settings">
                        <button type="submit" class="btn btn-fix">Apply Safe Settings</button>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="alert alert-info">
            <h5>📋 Recommended Settings:</h5>
            <ul>
                <li><strong>Ticket Lifetime:</strong> 12 months (tickets valid for 1 year)</li>
                <li><strong>Cleanup Frequency:</strong> 7 days (cleanup runs weekly)</li>
                <li><strong>Warning Period:</strong> 30 days (warn users 30 days before expiration)</li>
                <li><strong>Auto Cleanup:</strong> Disabled (manual cleanup only)</li>
            </ul>
        </div>
        
        <div class="mt-4">
            <a href="admin-tickets.php" class="btn btn-secondary">← Back to Tickets</a>
            <a href="ticket-expiration-manager.php" class="btn btn-primary">Ticket Expiration Manager</a>
        </div>
    </div>
</body>
</html>
