<?php
/**
 * Fix Cleanup Configuration - Disable auto cleanup in database
 */

session_start();
include('../functions/server.php');

// Check if admin is logged in
if (!isset($_SESSION['admin_username'])) {
    header('location: admin-login.php');
    exit();
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fix Cleanup Configuration</title>
    <link rel="stylesheet" href="../css/bootstrap.css">
    <style>
        body { background-color: #f8f9fa; padding: 20px; }
        .container { max-width: 800px; }
        .fix-section { background: white; padding: 20px; margin: 15px 0; border-radius: 8px; }
        .success { border-left: 4px solid #28a745; }
        .error { border-left: 4px solid #dc3545; }
        .warning { border-left: 4px solid #ffc107; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Fix Cleanup Configuration</h1>
        
        <?php
        if (isset($_POST['disable_cleanup'])) {
            echo "<div class='fix-section warning'>";
            echo "<h3>Disabling Auto Cleanup...</h3>";

            // Method 1: Modify the function file directly
            $function_file = '../functions/ticket-expiration-functions.php';
            if (file_exists($function_file)) {
                $content = file_get_contents($function_file);

                // Replace the runAutomaticCleanup function to always return null
                $old_function = 'function runAutomaticCleanup() {
    if (!isCleanupNeeded()) {
        return null;
    }

    return removeExpiredTickets();
}';

                $new_function = 'function runAutomaticCleanup() {
    // DISABLED FOR DEBUGGING - Always return null to prevent cleanup
    return null;

    // Original code commented out:
    // if (!isCleanupNeeded()) {
    //     return null;
    // }
    // return removeExpiredTickets();
}';

                $new_content = str_replace($old_function, $new_function, $content);

                if ($new_content !== $content) {
                    if (file_put_contents($function_file, $new_content)) {
                        echo "<p class='text-success'>✅ Modified runAutomaticCleanup function to always return null</p>";
                    } else {
                        echo "<p class='text-danger'>❌ Failed to write to function file</p>";
                    }
                } else {
                    echo "<p class='text-warning'>⚠️ Function already appears to be modified or pattern not found</p>";
                }
            }

            // Method 2: Also modify isCleanupNeeded to always return false
            if (file_exists($function_file)) {
                $content = file_get_contents($function_file);

                $old_cleanup_check = 'function isCleanupNeeded() {
    $config = getTicketExpirationConfig();

    if (!$config[\'auto_cleanup_enabled\']) {
        return false;
    }';

                $new_cleanup_check = 'function isCleanupNeeded() {
    // DISABLED FOR DEBUGGING - Always return false
    return false;

    // Original code commented out:
    // $config = getTicketExpirationConfig();
    // if (!$config[\'auto_cleanup_enabled\']) {
    //     return false;
    // }';

                $new_content = str_replace($old_cleanup_check, $new_cleanup_check, $content);

                if ($new_content !== $content) {
                    if (file_put_contents($function_file, $new_content)) {
                        echo "<p class='text-success'>✅ Modified isCleanupNeeded function to always return false</p>";
                    } else {
                        echo "<p class='text-danger'>❌ Failed to write to function file</p>";
                    }
                } else {
                    echo "<p class='text-warning'>⚠️ isCleanupNeeded function already appears to be modified</p>";
                }
            }

            echo "<div class='alert alert-success mt-3'>";
            echo "<h4>🎉 Cleanup Functions Disabled!</h4>";
            echo "<p>The cleanup functions have been modified to never run. Tickets should now persist!</p>";
            echo "</div>";

            echo "</div>";
        }
        
        // Show current configuration
        echo "<div class='fix-section'>";
        echo "<h3>Current Configuration</h3>";
        
        // Check what tables exist
        echo "<h4>Available Tables:</h4>";
        $tables_query = "SHOW TABLES LIKE '%expiration%' OR SHOW TABLES LIKE '%ticket%'";
        $tables_result = mysqli_query($conn, "SHOW TABLES");

        $relevant_tables = [];
        if ($tables_result) {
            echo "<ul>";
            while ($table = mysqli_fetch_array($tables_result)) {
                $table_name = $table[0];
                if (strpos($table_name, 'ticket') !== false || strpos($table_name, 'expiration') !== false) {
                    $relevant_tables[] = $table_name;
                    echo "<li><strong>$table_name</strong></li>";
                }
            }
            echo "</ul>";
        }

        // Check each relevant table structure
        foreach ($relevant_tables as $table_name) {
            echo "<h5>Table: $table_name</h5>";
            $desc_query = "DESCRIBE $table_name";
            $desc_result = mysqli_query($conn, $desc_query);

            if ($desc_result) {
                echo "<table class='table table-sm'>";
                echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
                while ($field = mysqli_fetch_assoc($desc_result)) {
                    echo "<tr>";
                    echo "<td>{$field['Field']}</td>";
                    echo "<td>{$field['Type']}</td>";
                    echo "<td>{$field['Null']}</td>";
                    echo "<td>{$field['Key']}</td>";
                    echo "<td>{$field['Default']}</td>";
                    echo "</tr>";
                }
                echo "</table>";

                // Show sample data
                $sample_query = "SELECT * FROM $table_name LIMIT 3";
                $sample_result = mysqli_query($conn, $sample_query);
                if ($sample_result && mysqli_num_rows($sample_result) > 0) {
                    echo "<p><strong>Sample Data:</strong></p>";
                    echo "<table class='table table-sm'>";
                    $first_row = true;
                    while ($row = mysqli_fetch_assoc($sample_result)) {
                        if ($first_row) {
                            echo "<tr>";
                            foreach (array_keys($row) as $column) {
                                echo "<th>$column</th>";
                            }
                            echo "</tr>";
                            $first_row = false;
                        }
                        echo "<tr>";
                        foreach ($row as $value) {
                            echo "<td>" . htmlspecialchars($value) . "</td>";
                        }
                        echo "</tr>";
                    }
                    echo "</table>";
                }
            }
        }
        echo "</div>";
        
        // Alternative approach - delete all purchase records
        if (isset($_POST['delete_purchase_records'])) {
            echo "<div class='fix-section error'>";
            echo "<h3>⚠️ Deleting Purchase Records</h3>";
            
            $delete_query = "DELETE FROM purchasetickets WHERE username = 'HC200'";
            echo "<p>Executing: <code>$delete_query</code></p>";
            
            if (mysqli_query($conn, $delete_query)) {
                $deleted = mysqli_affected_rows($conn);
                echo "<p class='text-success'>✅ Deleted $deleted purchase records for HC200</p>";
                echo "<p><strong>Result:</strong> HC200's tickets should now be permanent (no expiration tracking)</p>";
            } else {
                echo "<p class='text-danger'>❌ Failed to delete purchase records: " . mysqli_error($conn) . "</p>";
            }
            echo "</div>";
        }
        ?>
        
        <div class="fix-section">
            <h3>🛠️ Fix Options</h3>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">Option 1: Disable Cleanup</h5>
                            <p class="card-text">Disable automatic cleanup in database configuration</p>
                            <form method="POST">
                                <button type="submit" name="disable_cleanup" class="btn btn-primary">🔧 Disable Auto Cleanup</button>
                            </form>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">Option 2: Remove Purchase Records</h5>
                            <p class="card-text">Delete HC200's purchase records to make tickets permanent</p>
                            <form method="POST">
                                <button type="submit" name="delete_purchase_records" class="btn btn-warning">🗑️ Delete Purchase Records</button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="mt-4">
            <a href="debug-ticket-issue.php" class="btn btn-info">🔍 Back to Debug</a>
            <a href="admin-users.php" class="btn btn-secondary">← Back to Users</a>
        </div>
    </div>
</body>
</html>
