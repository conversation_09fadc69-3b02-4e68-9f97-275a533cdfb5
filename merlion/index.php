<?php
/**
 * Merlion Admin System Entry Point
 * Redirects to admin login page
 */

// Start session to check if admin is already logged in
session_start();

// Check if admin is already logged in
if (isset($_SESSION['admin_username']) && isset($_SESSION['admin_id'])) {
    // Admin is logged in, redirect to admin tickets page
    header('Location: admin-tickets.php');
    exit();
} else {
    // Admin not logged in, redirect to login page
    header('Location: admin-login.php');
    exit();
}
?>
