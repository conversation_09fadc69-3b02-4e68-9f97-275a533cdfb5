<?php
session_start();
include('../functions/server.php');

// Check if admin is logged in
if (!isset($_SESSION['admin_username'])) {
    header('location: admin-login');
    exit();
}

$admin_username = $_SESSION['admin_username'];
$admin_id = $_SESSION['admin_id'];
$admin_role = $_SESSION['admin_role'];

// Include GraphQL functions
include('../functions/graphql_functions.php');

// Variables for search results
$message = '';
$messageType = '';
$ticketData = null;

// Process search form
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['search_ticket'])) {
    $ticketId = trim($_POST['ticket_id']);

    if (!empty($ticketId) && is_numeric($ticketId)) {
        try {
            // Search for ticket by ID using GraphQL
            $query = '
            query GetTicket($id: Int!) {
              getTicket(id: $id) {
                id
                ticket_no
                contact_id
                agent_id
                req_email
                subject
                type
                type_name
                priority
                status
                created
                updated
              }
            }';

            $variables = [
                'id' => (int)$ticketId
            ];

            $result = makeGraphQLRequest($query, $variables);

            if ($result['success']) {
                if (isset($result['data']['data']['getTicket']) && $result['data']['data']['getTicket']) {
                    // Store results in session to prevent form resubmission
                    $_SESSION['search_result'] = [
                        'ticket_data' => $result['data']['data']['getTicket'],
                        'message' => 'Ticket found successfully!',
                        'message_type' => 'success',
                        'ticket_id' => $ticketId
                    ];
                } else {
                    $_SESSION['search_result'] = [
                        'ticket_data' => null,
                        'message' => 'No ticket found with ID: ' . htmlspecialchars($ticketId),
                        'message_type' => 'warning',
                        'ticket_id' => $ticketId
                    ];
                }
            } else {
                $_SESSION['search_result'] = [
                    'ticket_data' => null,
                    'message' => 'API Error: ' . ($result['error'] ?? 'Unknown error'),
                    'message_type' => 'danger',
                    'ticket_id' => $ticketId
                ];
            }
        } catch (Exception $e) {
            $_SESSION['search_result'] = [
                'ticket_data' => null,
                'message' => 'Connection Error: ' . $e->getMessage(),
                'message_type' => 'danger',
                'ticket_id' => $ticketId
            ];
        }
    } else {
        $_SESSION['search_result'] = [
            'ticket_data' => null,
            'message' => 'Please enter a valid ticket ID (numbers only).',
            'message_type' => 'warning',
            'ticket_id' => $ticketId
        ];
    }

    // Redirect to prevent form resubmission (Post-Redirect-Get pattern)
    header('Location: ' . $_SERVER['PHP_SELF'] . '?searched=1');
    exit();
}

// Handle search results from session (after redirect)
if (isset($_GET['searched']) && isset($_SESSION['search_result'])) {
    $searchResult = $_SESSION['search_result'];
    $ticketData = $searchResult['ticket_data'];
    $message = $searchResult['message'];
    $messageType = $searchResult['message_type'];
    $lastSearchedId = $searchResult['ticket_id'];

    // Clear the session data after use
    unset($_SESSION['search_result']);
}

// Helper function to format date
function formatDate($dateString) {
    if (empty($dateString)) return 'Not set';
    try {
        $date = new DateTime($dateString);
        return $date->format('d M Y H:i');
    } catch (Exception $e) {
        return $dateString;
    }
}

// Helper function to format ticket type
function formatTicketType($type, $typeName) {
    $typeMap = [
        1 => 'Starter',
        2 => 'Business',  // Changed from 'Premium' to 'Business'
        3 => 'Ultimate'
    ];

    $displayType = $typeMap[$type] ?? $typeName ?? 'Unknown';
    $badgeClass = $type == 1 ? 'badge-secondary' : ($type == 2 ? 'badge-primary' : 'badge-success');

    return "<span class='badge {$badgeClass}'>{$displayType}</span>";
}

// Helper function to format priority
function formatPriority($priority) {
    $priorityMap = [
        'LOW' => ['Low', 'badge-secondary'],
        'MEDIUM' => ['Medium', 'badge-warning'],
        'HIGH' => ['High', 'badge-danger'],
        'URGENT' => ['Urgent', 'badge-dark']
    ];

    $priorityInfo = $priorityMap[strtoupper($priority)] ?? ['Unknown', 'badge-light'];
    return "<span class='badge {$priorityInfo[1]}'>{$priorityInfo[0]}</span>";
}

// Helper function to format status
function formatStatus($status) {
    $statusMap = [
        'OPEN' => ['Open', 'badge-info'],
        'WIP' => ['In Progress', 'badge-warning'],
        'SOLVED' => ['Solved', 'badge-primary'],
        'CLOSED' => ['Closed', 'badge-success'],
        'RESOLVED' => ['Resolved', 'badge-success']
    ];

    $statusInfo = $statusMap[strtoupper($status)] ?? ['Unknown', 'badge-light'];
    return "<span class='badge {$statusInfo[1]}'>{$statusInfo[0]}</span>";
}
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin - Ticket Search</title>
    <link rel="shortcut icon" href="../image/png/favicon.png" type="image/x-icon">
    <!-- Bootstrap, fonts & icons -->
    <link rel="stylesheet" href="../css/bootstrap.css">
    <link rel="stylesheet" href="../fonts/icon-font/css/style.css">
    <link rel="stylesheet" href="../fonts/typography-font/typo.css">
    <link rel="stylesheet" href="../fonts/fontawesome-5/css/all.css">
    <!-- Vendor stylesheets -->
    <link rel="stylesheet" href="../css/main.css">
    <style>
    body {
        background-color: #f8f9fa;
    }

    .badge-secondary {
        color: white;
        background-color: #68d585;
    }

    .admin-container {
        padding: 20px;
        max-width: 100%;
        overflow-x: hidden;
    }

    .admin-header {
        background-color: #fff;
        padding: 15px 20px;
        border-radius: 5px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        position: relative;
    }

    .admin-header h1 {
        margin: 0;
        font-size: 24px;
        color: #333;
    }

    .admin-user {
        display: flex;
        align-items: center;
    }

    .admin-content {
        background-color: #fff;
        border-radius: 5px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        padding: 20px;
        min-height: calc(100vh - 100px);
    }

    .search-box {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 20px;
        border-radius: 10px;
        margin-bottom: 20px;
    }

    .ticket-info-card {
        border: 1px solid #e0e6ed;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
        background: #fff;
    }

    .info-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 0;
        border-bottom: 1px solid #f0f0f0;
    }

    .info-row:last-child {
        border-bottom: none;
    }

    .info-label {
        font-weight: 600;
        color: #555;
        min-width: 150px;
    }

    .info-value {
        color: #333;
        flex: 1;
        text-align: right;
    }

    .user-dropdown {
        position: relative;
        display: inline-block;
    }

    .user-info {
        cursor: pointer;
        display: flex;
        align-items: center;
    }

    .mobile-menu-toggle {
        margin-left: 5px;
        transition: transform 0.3s ease;
        display: none;
    }

    .dropdown-content {
        display: block;
    }

    /* Mobile Styles */
    @media (max-width: 767px) {
        .admin-container {
            padding: 10px;
        }

        .admin-header {
            flex-direction: column;
            align-items: flex-start;
            padding: 10px;
        }

        .admin-header h1 {
            margin-bottom: 10px;
            font-size: 20px;
        }

        .mobile-menu-toggle {
            display: inline-block;
        }

        .dropdown-content {
            display: none;
            position: absolute;
            background-color: #f9f9f9;
            width: 100%;
            left: 0;
            box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.2);
            z-index: 1;
            border-radius: 4px;
            margin-top: 5px;
            padding: 8px;
        }

        .user-dropdown.active .dropdown-content {
            display: block;
        }

        .info-row {
            flex-direction: column;
            align-items: flex-start;
        }

        .info-value {
            text-align: left;
            margin-top: 5px;
        }
    }
    </style>
</head>

<body>
    <div class="admin-container">
        <div class="admin-header">
            <h1><i class="fas fa-search"></i> Ticket Search</h1>
            <div class="admin-user">
                <div class="user-dropdown">
                    <span class="user-info">
                        Welcome, <?php echo htmlspecialchars($admin_username); ?>
                        (<?php echo htmlspecialchars($admin_role); ?>)
                        <i class="mobile-menu-toggle fas fa-chevron-down"></i>
                    </span>
                    <div class="dropdown-content">
                        <a href="admin-logout.php" class="btn btn-sm btn-outline-danger">Logout</a>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-2 col-md-3 col-sm-12">
                <?php include('admin-menu.php'); ?>
            </div>

            <div class="col-lg-10 col-md-9 col-sm-12">
                <div class="admin-content">
                    <!-- Search Form -->
                    <div class="search-box">
                        <h3 style="color: white;"><i class="fas fa-search"></i> Search Ticket by ID</h3>
                        <p class="mb-3" style="color: white;">Enter the ticket ID to retrieve detailed information from
                            Appika GraphQL API</p>

                        <form method="POST" action="">
                            <div class="row">
                                <div class="col-md-8">
                                    <div class="form-group">
                                        <input type="number" class="form-control form-control-lg" name="ticket_id"
                                            placeholder="Enter ticket ID (e.g., 1, 2, 3)"
                                            value="<?php echo isset($lastSearchedId) ? htmlspecialchars($lastSearchedId) : (isset($_POST['ticket_id']) ? htmlspecialchars($_POST['ticket_id']) : ''); ?>"
                                            min="1" required>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <button type="submit" name="search_ticket" class="btn btn-light btn-lg btn-block">
                                        <i class="fas fa-search"></i> Search Ticket
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>

                    <!-- Messages -->
                    <?php if ($message): ?>
                    <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert"
                        id="alertMessage">
                        <i
                            class="fas fa-<?php echo $messageType === 'success' ? 'check-circle' : ($messageType === 'warning' ? 'exclamation-triangle' : 'times-circle'); ?>"></i>
                        <?php echo $message; ?>
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close"
                            onclick="closeAlert()">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <?php endif; ?>

                    <!-- Ticket Information -->
                    <?php if ($ticketData): ?>
                    <div class="ticket-info-card">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h4><i class="fas fa-ticket-alt"></i> Ticket Information</h4>
                            <span class="badge badge-primary">ID:
                                <?php echo htmlspecialchars($ticketData['id']); ?></span>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="info-row">
                                    <span class="info-label">Ticket Number:</span>
                                    <span
                                        class="info-value"><strong><?php echo htmlspecialchars($ticketData['ticket_no'] ?? 'N/A'); ?></strong></span>
                                </div>
                                <div class="info-row">
                                    <span class="info-label">Subject:</span>
                                    <span
                                        class="info-value"><?php echo htmlspecialchars($ticketData['subject'] ?? 'N/A'); ?></span>
                                </div>
                                <div class="info-row">
                                    <span class="info-label">Requester Email:</span>
                                    <span class="info-value">
                                        <?php if (!empty($ticketData['req_email'])): ?>
                                        <a href="mailto:<?php echo htmlspecialchars($ticketData['req_email']); ?>">
                                            <?php echo htmlspecialchars($ticketData['req_email']); ?>
                                        </a>
                                        <?php else: ?>
                                        N/A
                                        <?php endif; ?>
                                    </span>
                                </div>
                                <div class="info-row">
                                    <span class="info-label">Ticket Type:</span>
                                    <span
                                        class="info-value"><?php echo formatTicketType($ticketData['type'] ?? 0, $ticketData['type_name'] ?? ''); ?></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="info-row">
                                    <span class="info-label">Priority:</span>
                                    <span
                                        class="info-value"><?php echo formatPriority($ticketData['priority'] ?? ''); ?></span>
                                </div>
                                <div class="info-row">
                                    <span class="info-label">Status:</span>
                                    <span
                                        class="info-value"><?php echo formatStatus($ticketData['status'] ?? ''); ?></span>
                                </div>
                                <div class="info-row">
                                    <span class="info-label">Contact ID:</span>
                                    <span
                                        class="info-value"><?php echo htmlspecialchars($ticketData['contact_id'] ?? 'N/A'); ?></span>
                                </div>
                                <div class="info-row">
                                    <span class="info-label">Agent ID:</span>
                                    <span
                                        class="info-value"><?php echo htmlspecialchars($ticketData['agent_id'] ?? 'Unassigned'); ?></span>
                                </div>
                            </div>
                        </div>

                        <!-- Timestamps -->
                        <hr>
                        <h5><i class="fas fa-clock"></i> Timeline</h5>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="info-row">
                                    <span class="info-label">Created:</span>
                                    <span
                                        class="info-value"><?php echo formatDate($ticketData['created'] ?? ''); ?></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="info-row">
                                    <span class="info-label">Last Updated:</span>
                                    <span
                                        class="info-value"><?php echo formatDate($ticketData['updated'] ?? ''); ?></span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <!-- <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-tools"></i> Quick Actions</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3">
                                    <a href="appika-tickets.php" class="btn btn-outline-primary btn-block">
                                        <i class="fas fa-list"></i> View All Tickets
                                    </a>
                                </div>
                                <div class="col-md-3">
                                    <a href="../api/graphql_ticket_test.php"
                                        class="btn btn-outline-secondary btn-block">
                                        <i class="fas fa-edit"></i> Edit Ticket
                                    </a>
                                </div>
                                <div class="col-md-3">
                                    <button class="btn btn-outline-info btn-block" onclick="window.print()">
                                        <i class="fas fa-print"></i> Print Details
                                    </button>
                                </div>
                                <div class="col-md-3">
                                    <button class="btn btn-outline-success btn-block" onclick="searchAnother()">
                                        <i class="fas fa-search-plus"></i> Search Another
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div> -->
                    <?php endif; ?>

                    <!-- No Results Message -->
                    <?php if ($_SERVER['REQUEST_METHOD'] === 'POST' && !$ticketData && $messageType !== 'danger'): ?>
                    <div class="text-center py-5">
                        <i class="fas fa-search fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No ticket found</h5>
                        <p class="text-muted">Try searching with a different ticket ID</p>
                    </div>
                    <?php endif; ?>

                    <!-- Help Section -->
                    <div class="card mt-4">
                        <div class="card-header">
                            <h5><i class="fas fa-question-circle"></i> Help & Tips</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6>Search Tips:</h6>
                                    <ul class="list-unstyled">
                                        <li><i class="fas fa-check text-success"></i> Use numeric ticket IDs (e.g., 1,
                                            2, 3)</li>
                                        <li><i class="fas fa-check text-success"></i> Search returns complete ticket
                                            details</li>
                                        <li><i class="fas fa-check text-success"></i> Data comes from Appika GraphQL API
                                        </li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <h6>Available Information:</h6>
                                    <ul class="list-unstyled">
                                        <li><i class="fas fa-info text-primary"></i> Ticket number and subject</li>
                                        <li><i class="fas fa-info text-primary"></i> Type, priority, and status</li>
                                        <li><i class="fas fa-info text-primary"></i> Contact and agent information</li>
                                        <li><i class="fas fa-info text-primary"></i> Creation and update timestamps</li>
                                    </ul>
                                </div>
                            </div>

                            <div class="mt-3">
                                <h6>GraphQL API Details:</h6>
                                <div class="alert alert-info">
                                    <small>
                                        <strong>Endpoint:</strong> <?php echo htmlspecialchars($graphqlEndpoint); ?><br>
                                        <strong>Query:</strong> getTicket(id: Int!)<br>
                                        <strong>Fields:</strong> id, ticket_no, contact_id, agent_id, req_email,
                                        subject, type, type_name, priority, status, created, updated
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="../js/jquery.min.js"></script>
    <script src="../js/bootstrap.bundle.min.js"></script>
    <script>
    // Close alert function
    function closeAlert() {
        const alertElement = document.getElementById('alertMessage');
        if (alertElement) {
            alertElement.style.opacity = '0';
            setTimeout(function() {
                alertElement.style.display = 'none';
            }, 150);
        }
    }

    // Mobile menu toggle
    document.addEventListener('DOMContentLoaded', function() {
        const userDropdown = document.querySelector('.user-dropdown');
        const userInfo = document.querySelector('.user-info');
        const mobileToggle = document.querySelector('.mobile-menu-toggle');

        if (userInfo && mobileToggle) {
            userInfo.addEventListener('click', function() {
                userDropdown.classList.toggle('active');
                mobileToggle.classList.toggle('active');
            });
        }

        // Initialize Bootstrap alert dismissal if Bootstrap is available
        if (typeof bootstrap !== 'undefined' && bootstrap.Alert) {
            // Bootstrap 5 style
            const alertElement = document.getElementById('alertMessage');
            if (alertElement) {
                alertElement.addEventListener('click', function(e) {
                    if (e.target.classList.contains('close') || e.target.closest('.close')) {
                        closeAlert();
                    }
                });
            }
        } else if (typeof $ !== 'undefined' && $.fn.alert) {
            // Bootstrap 4 style with jQuery
            $('.alert').alert();
        }

        // Auto-dismiss success alerts after 5 seconds
        const successAlert = document.querySelector('.alert-success');
        if (successAlert) {
            setTimeout(function() {
                closeAlert();
            }, 5000);
        }
    });

    // Search another ticket
    function searchAnother() {
        document.querySelector('input[name="ticket_id"]').value = '';
        document.querySelector('input[name="ticket_id"]').focus();
        // Scroll to top
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    }

    // Auto-focus on search input when page loads
    document.addEventListener('DOMContentLoaded', function() {
        const searchInput = document.querySelector('input[name="ticket_id"]');
        if (searchInput && !searchInput.value) {
            searchInput.focus();
        }
    });
    </script>
</body>

</html>