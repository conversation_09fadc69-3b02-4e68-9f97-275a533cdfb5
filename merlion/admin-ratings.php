<?php
include('../functions/server.php');
include('../functions/timezone-helper.php');
session_start();

// Check if admin is logged in
if (!isset($_SESSION['admin_username'])) {
    header("Location: admin-login");
    exit();
}

// Handle edit/delete actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    $rating_id = (int)($_POST['rating_id'] ?? 0);
    
    if ($action === 'edit' && $rating_id > 0) {
        $new_rating = (int)($_POST['new_rating'] ?? 0);
        $new_comment = mysqli_real_escape_string($conn, $_POST['new_comment'] ?? '');
        
        if ($new_rating >= 1 && $new_rating <= 5) {
            $update_sql = "UPDATE ticket_ratings SET rating = $new_rating, comment = '$new_comment', updated_at = NOW() WHERE id = $rating_id";
            if (mysqli_query($conn, $update_sql)) {
                $success_message = "Rating updated successfully.";
            } else {
                $error_message = "Error updating rating: " . mysqli_error($conn);
            }
        } else {
            $error_message = "Invalid rating value. Must be between 1 and 5.";
        }
    } elseif ($action === 'delete' && $rating_id > 0) {
        $delete_sql = "DELETE FROM ticket_ratings WHERE id = $rating_id";
        if (mysqli_query($conn, $delete_sql)) {
            $success_message = "Rating deleted successfully.";
        } else {
            $error_message = "Error deleting rating: " . mysqli_error($conn);
        }
    }
}

// Pagination settings
$itemsPerPage = 4;
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
if ($page < 1) $page = 1;
$offset = ($page - 1) * $itemsPerPage;

// Search functionality
$search = isset($_GET['search']) ? trim($_GET['search']) : '';
$searchSql = '';
if (!empty($search)) {
    $searchSafe = mysqli_real_escape_string($conn, $search);
    $searchSql = "AND (
        st.id LIKE '%$searchSafe%' OR
        st.subject LIKE '%$searchSafe%' OR
        u.username LIKE '%$searchSafe%' OR
        u.email LIKE '%$searchSafe%' OR
        tr.comment LIKE '%$searchSafe%' OR
        CONCAT('HC', u.id) LIKE '%$searchSafe%'
    )";
}

// Count total ratings for pagination
$countQuery = "
    SELECT COUNT(*) as total 
    FROM ticket_ratings tr
    JOIN support_tickets st ON tr.ticket_id = st.id
    JOIN user u ON tr.user_id = u.id
    WHERE 1=1 $searchSql
";
$countResult = mysqli_query($conn, $countQuery);
$totalItems = mysqli_fetch_assoc($countResult)['total'];
$totalPages = ceil($totalItems / $itemsPerPage);

// Get all ratings with user and ticket information (local tickets)
$localRatingsQuery = "
    SELECT
        tr.*,
        st.id as ticket_id,
        st.subject,
        st.ticket_type,
        st.status,
        st.created_at as ticket_created,
        u.username,
        u.email,
        u.id as user_id
    FROM ticket_ratings tr
    JOIN support_tickets st ON tr.ticket_id = st.id
    JOIN user u ON tr.user_id = u.id
    WHERE (tr.is_appika_ticket = 0 OR tr.is_appika_ticket IS NULL) $searchSql
    ORDER BY tr.created_at DESC
    LIMIT $itemsPerPage OFFSET $offset
";
$localRatingsResult = mysqli_query($conn, $localRatingsQuery);

// Get Appika ratings
$appikaRatingsQuery = "SELECT tr.*, u.username, u.email, u.id as user_id FROM ticket_ratings tr JOIN user u ON tr.user_id = u.id WHERE tr.is_appika_ticket = 1 $searchSql ORDER BY tr.created_at DESC";
$appikaRatingsResult = mysqli_query($conn, $appikaRatingsQuery);
$appikaRatings = [];
require_once '../functions/graphql_functions.php';
while ($row = mysqli_fetch_assoc($appikaRatingsResult)) {
    $appikaId = $row['appika_ticket_id'];
    $query = '
    query GetTicket($id: Int!) {
        getTicket(id: $id) {
            id
            subject
            type
            status
            created
            updated
        }
    }';
    $variables = ['id' => (int)$appikaId];
    $result = makeGraphQLRequest($query, $variables);
    if ($result['success'] && isset($result['data']['data']['getTicket'])) {
        $ticket = $result['data']['data']['getTicket'];
        $row['ticket_id'] = $ticket['id'];
        $row['subject'] = $ticket['subject'];
        $row['ticket_type'] = $ticket['type'];
        $row['status'] = $ticket['status'];
        $row['ticket_created'] = $ticket['created'];
        $row['ticket_updated'] = $ticket['updated'] ?? $ticket['created'];
        $appikaRatings[] = $row;
    }
}
// Merge local and Appika ratings for display
$allRatings = [];
while ($row = mysqli_fetch_assoc($localRatingsResult)) {
    $allRatings[] = $row;
}
$allRatings = array_merge($allRatings, $appikaRatings);
// Optionally sort by created date descending
usort($allRatings, function($a, $b) {
    return strtotime($b['created_at']) - strtotime($a['created_at']);
});
// Map Appika type numbers to names for display
foreach ($allRatings as &$rating) {
    if (isset($rating['is_appika_ticket']) && $rating['is_appika_ticket']) {
        $typeMapping = [1 => 'Starter', 2 => 'Business', 3 => 'Ultimate'];
        $rating['ticket_type'] = $typeMapping[$rating['ticket_type']] ?? $rating['ticket_type'];
    }
}
unset($rating);

// Get admin session info
$admin_username = $_SESSION['admin_username'];
$admin_id = $_SESSION['admin_id'];
$admin_role = $_SESSION['admin_role'];
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin - Ticket Ratings Management</title>
    <link rel="shortcut icon" href="../image/png/favicon.png" type="image/x-icon">
    <!-- Bootstrap, fonts & icons -->
    <link rel="stylesheet" href="../css/bootstrap.css">
    <link rel="stylesheet" href="../fonts/icon-font/css/style.css">
    <link rel="stylesheet" href="../fonts/typography-font/typo.css">
    <link rel="stylesheet" href="../fonts/fontawesome-5/css/all.css">
    <!-- Vendor stylesheets -->
    <link rel="stylesheet" href="../css/main.css">

    <style>
        body {
            background-color: #f8f9fa;
        }

        .admin-container {
            padding: 20px;
            max-width: 100%;
            overflow-x: hidden;
        }

        .admin-header {
            background-color: #fff;
            padding: 15px 20px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
        }

        .admin-header h1 {
            margin: 0;
            font-size: 24px;
            color: #333;
        }

        .admin-user {
            display: flex;
            align-items: center;
        }

        .admin-user span {
            margin-right: 10px;
        }

        .admin-content {
            background-color: #fff;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            padding: 20px;
            min-height: calc(100vh - 140px);
            overflow-y: auto;
            overflow-x: hidden;
        }

        /* Responsive container */
        @media (max-width: 991px) {
            .admin-container {
                padding: 15px;
            }
        }

        @media (max-width: 767px) {
            .admin-container {
                padding: 10px;
            }

            .admin-header {
                padding: 12px 15px;
            }

            .admin-content {
                padding: 15px;
            }
        }



        .rating-card {
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 25px;
            margin-bottom: 25px;
            transition: box-shadow 0.3s ease;
            background-color: #fff;
        }

        .rating-card:hover {
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            border-color: #d1ecf1;
        }

        .rating-card h5 {
            color: #2c3e50;
            font-weight: 600;
        }

        .rating-card h5 a {
            color: #473BF0;
            text-decoration: none;
        }

        .rating-card h5 a:hover {
            color: #3d32d9;
            text-decoration: underline;
        }

        .star-display {
            color: #ffc107;
            font-size: 1.2rem;
        }

        .star-display .empty {
            color: #ddd;
        }

        .ticket-type-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: bold;
            text-transform: uppercase;
        }

        .type-starter {
            background-color: #fbbf24;
            color: #fff;
        }

        .type-premium {
            background-color: #01A7E1;
            color: #fff;
        }

        .type-ultimate {
            background-color: #793BF0;
            color: #fff;
        }

        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: bold;
            text-transform: uppercase;
        }

        .status-resolved {
            background-color: #28a745;
            color: white;
        }

        .status-closed {
            background-color: #6c757d;
            color: white;
        }

        .admin-actions {
            display: flex;
            flex-direction: column;
            gap: 8px;
            align-items: flex-end;
        }

        .admin-actions .btn {
            width: 100px;
            font-size: 12px;
            padding: 6px 12px;
        }

        .btn-edit {
            background-color: #473BF0;
            border-color: #473BF0;
            color: white;
        }

        .btn-edit:hover {
            background-color: #3d32d9;
            border-color: #3d32d9;
            color: white;
        }

        .btn-delete {
            background-color: #dc3545;
            border-color: #dc3545;
            color: white;
        }

        .btn-delete:hover {
            background-color: #c82333;
            border-color: #bd2130;
            color: white;
        }

        .btn-view {
            background-color: #6c757d;
            border-color: #6c757d;
            color: white;
        }

        .btn-view:hover {
            background-color: #5a6268;
            border-color: #545b62;
            color: white;
        }



        /* Pagination styling */
        .pagination-container .pagination {
            margin-bottom: 0;
        }

        .pagination .page-link {
            color: #473BF0;
            border-color: #dee2e6;
        }

        .pagination .page-item.active .page-link {
            background-color: #473BF0;
            border-color: #473BF0;
            color: white;
        }

        .pagination .page-link:hover {
            color: #3d32d9;
            background-color: #e9ecef;
            border-color: #dee2e6;
        }

        .pagination .page-item.disabled .page-link {
            color: #6c757d;
            background-color: #fff;
            border-color: #dee2e6;
        }

        @media (max-width: 767px) {
            .ratings-container {
                padding: 20px 15px;
            }

            .rating-card {
                padding: 15px;
            }

            .admin-actions {
                flex-direction: row;
                justify-content: center;
                margin-top: 15px;
                gap: 5px;
            }

            .admin-actions .btn {
                width: auto;
                font-size: 11px;
                padding: 4px 8px;
            }

            .pagination-container .pagination {
                flex-wrap: wrap;
                justify-content: center;
            }

            .pagination-container .page-item {
                margin-bottom: 5px;
            }

            .col-md-4 {
                margin-top: 15px;
            }
        }
        /* Search form styles */
        .filter-row {
            margin-bottom: 20px;
        }

        .search-filter-row {
            display: flex;
            justify-content: flex-start;
            align-items: center;
            margin-bottom: 15px;
        }

        .search-box {
            width: 100%;
            max-width: 500px;
        }

        .search-box .input-group {
            width: 100%;
        }

        .search-input {
            border-radius: 4px 0 0 4px;
            border-right: none;
            font-size: 14px;
            height: 38px;
        }

        .search-button {
            border-radius: 0 4px 4px 0;
            background-color: #473BF0;
            border-color: #473BF0;
            height: 38px;
            padding: 0 15px;
        }

        .search-button:hover {
            background-color: #3d32d9;
            border-color: #3d32d9;
        }

        /* Responsive search */
        @media (max-width: 991px) {
            .search-box {
                width: 100%;
            }

            .search-box .input-group {
                width: 100% !important;
                max-width: 500px !important;
                margin: 0 auto;
            }
        }

        @media (max-width: 767px) {
            .search-filter-row {
                padding: 0 15px;
            }

            .search-box .input-group {
                width: 100% !important;
                max-width: 100% !important;
            }

            .search-input {
                font-size: 14px;
            }
        }

        /* User dropdown styles */
        .user-dropdown {
            position: relative;
            display: inline-block;
        }

        .user-info {
            margin-right: 10px;
            font-size: 14px;
            cursor: pointer;
            display: flex;
            align-items: center;
        }

        .mobile-menu-toggle {
            margin-left: 5px;
            transition: transform 0.3s ease;
            display: none;
        }

        .mobile-menu-toggle.active {
            transform: rotate(180deg);
        }

        .dropdown-content {
            display: block;
        }

        /* Mobile Styles */
        @media (max-width: 767px) {
            .mobile-menu-toggle {
                display: inline-block;
            }

            .dropdown-content {
                display: none;
                position: absolute;
                background-color: #f9f9f9;
                width: 100%;
                right: 0;
                box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.2);
                z-index: 1;
                border-radius: 4px;
                margin-top: 5px;
                padding: 8px;
            }

            .user-dropdown.active .dropdown-content {
                display: block;
            }
        }
    </style>
</head>

<body>
    <div class="admin-container">
        <div class="admin-header">
            <h1>Ticket Ratings Management</h1>
            <div class="admin-user">
                <div class="user-dropdown">
                    <span class="user-info">
                        Welcome, <?php echo htmlspecialchars($admin_username); ?>
                        (<?php echo htmlspecialchars($admin_role); ?>)
                        <i class="mobile-menu-toggle fas fa-chevron-down"></i>
                    </span>
                    <div class="dropdown-content">
                        <a href="admin-logout.php" class="btn btn-sm btn-outline-danger">Logout</a>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-2 col-md-3 col-sm-12">
                <?php include('admin-menu.php'); ?>
            </div>

            <div class="col-lg-10 col-md-9 col-sm-12">
                <div class="admin-content">

                    <!-- Success/Error Messages -->
                    <?php if (isset($success_message)): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="fas fa-check-circle"></i> <?php echo $success_message; ?>
                        <button type="button" class="close" data-dismiss="alert">
                            <span>&times;</span>
                        </button>
                    </div>
                    <?php endif; ?>

                    <?php if (isset($error_message)): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="fas fa-exclamation-circle"></i> <?php echo $error_message; ?>
                        <button type="button" class="close" data-dismiss="alert">
                            <span>&times;</span>
                        </button>
                    </div>
                    <?php endif; ?>

                    <!-- Search Form -->
                    <div class="filter-row">
                        <form method="GET" class="row">
                            <div class="col-md-6">
                                <div class="search-filter-row">
                                    <div class="search-box">
                                        <div class="input-group">
                                            <input type="text" name="search" class="form-control search-input"
                                                placeholder="Search by ticket ID, subject, username, email, or comment"
                                                value="<?php echo htmlspecialchars($search); ?>">
                                            <div class="input-group-append">
                                                <button type="submit" class="btn btn-primary search-button">
                                                    <i class="fas fa-search"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 d-flex align-items-center justify-content-end">
                                <?php if (!empty($search)): ?>
                                <a href="admin-ratings" class="btn btn-secondary mr-3">
                                    <i class="fas fa-times"></i> Clear
                                </a>
                                <?php endif; ?>
                                <small class="text-muted">
                                    Total Ratings: <?php echo $totalItems; ?>
                                    <?php if (!empty($search)): ?>
                                    (filtered)
                                    <?php endif; ?>
                                </small>
                            </div>
                        </form>
                    </div>

                    <?php if (count($allRatings) > 0): ?>
                        <!-- Rating Cards -->
                        <?php foreach ($allRatings as $rating): ?>
                        <div class="rating-card">
                            <div class="row">
                                <div class="col-md-8">
                                    <h5 class="mb-2">
                                        <a href="admin-ticket-detail?<?php echo ($rating['is_appika_ticket'] ?? 0) ? 'ticket_id=' . $rating['ticket_id'] : 'id=' . $rating['ticket_id']; ?>" class="text-decoration-none">
                                            Ticket #<?php echo $rating['ticket_id']; ?>: <?php echo htmlspecialchars($rating['subject']); ?>
                                        </a>
                                    </h5>
                                    <div class="mb-2">
                                        <strong>Customer:</strong>
                                        <a href="admin-user-detail?id=<?php echo $rating['user_id']; ?>" class="text-decoration-none">
                                            <?php echo htmlspecialchars($rating['username']); ?>
                                            (HC<?php echo $rating['user_id']; ?>)
                                        </a>
                                        <br>
                                        <small class="text-muted"><?php echo htmlspecialchars($rating['email']); ?></small>
                                    </div>
                                    <div class="mb-2">
                                        <span class="ticket-type-badge type-<?php echo $rating['ticket_type']; ?>">
                                            <?php echo ucfirst($rating['ticket_type']); ?>
                                        </span>
                                        &nbsp;
                                        <span class="status-badge status-<?php echo $rating['status']; ?>">
                                            <?php echo ucfirst($rating['status']); ?>
                                        </span>
                                    </div>
                                    <div class="star-display mb-2">
                                        <?php for ($i = 1; $i <= 5; $i++): ?>
                                            <i class="fas fa-star <?php echo $i <= $rating['rating'] ? '' : 'empty'; ?>"></i>
                                        <?php endfor; ?>
                                        <span class="ml-2 text-muted">(<?php echo $rating['rating']; ?>/5)</span>
                                    </div>
                                    <?php if (!empty($rating['comment'])): ?>
                                    <div class="mb-2">
                                        <strong>Comment:</strong>
                                        <p class="mb-0"><?php echo htmlspecialchars($rating['comment']); ?></p>
                                    </div>
                                    <?php endif; ?>
                                    <small class="text-muted">
                                        <i class="fas fa-clock"></i>
                                        Rated on: <?php echo showCustomerTime($rating['created_at']); ?>
                                        <?php if (($rating['created_at'] ?? '') != ($rating['updated_at'] ?? '')): ?>
                                            <br><i class="fas fa-edit"></i>
                                            Last updated: <?php echo showCustomerTime($rating['updated_at']); ?>
                                        <?php endif; ?>
                                    </small>
                                </div>
                                <div class="col-md-4 text-md-right">
                                    <div class="admin-actions">
                                        <button type="button" class="btn btn-edit"
                                                onclick="editRating(<?php echo $rating['id']; ?>, <?php echo $rating['rating']; ?>, '<?php echo addslashes($rating['comment']); ?>')">
                                            <i class="fas fa-edit"></i> &nbsp; Edit
                                        </button>
                                        <button type="button" class="btn btn-delete"
                                                onclick="deleteRating(<?php echo $rating['id']; ?>, '<?php echo htmlspecialchars($rating['subject']); ?>')">
                                            <i class="fas fa-trash"></i> &nbsp; Delete
                                        </button>
                                        <a href="admin-rating-detail?id=<?php echo $rating['id']; ?>"
                                           class="btn btn-view">
                                            <i class="fas fa-eye"></i> &nbsp; View
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; ?>

                        <!-- Pagination Controls -->
                        <?php if ($totalPages > 1): ?>
                        <div class="pagination-container mt-4">
                            <nav aria-label="Page navigation">
                                <ul class="pagination justify-content-center">
                                    <!-- Previous Button -->
                                    <li class="page-item <?php echo ($page <= 1) ? 'disabled' : ''; ?>">
                                        <a class="page-link" href="?page=<?php echo $page - 1; ?><?php echo !empty($search) ? '&search=' . urlencode($search) : ''; ?>" aria-label="Previous">
                                            <span aria-hidden="true">&laquo; Previous</span>
                                        </a>
                                    </li>

                                    <!-- Page Numbers -->
                                    <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                                    <li class="page-item <?php echo ($page == $i) ? 'active' : ''; ?>">
                                        <a class="page-link" href="?page=<?php echo $i; ?><?php echo !empty($search) ? '&search=' . urlencode($search) : ''; ?>">
                                            <?php echo $i; ?>
                                        </a>
                                    </li>
                                    <?php endfor; ?>

                                    <!-- Next Button -->
                                    <li class="page-item <?php echo ($page >= $totalPages) ? 'disabled' : ''; ?>">
                                        <a class="page-link" href="?page=<?php echo $page + 1; ?><?php echo !empty($search) ? '&search=' . urlencode($search) : ''; ?>" aria-label="Next">
                                            <span aria-hidden="true">Next &raquo;</span>
                                        </a>
                                    </li>
                                </ul>
                            </nav>
                        </div>
                        <?php endif; ?>

                    <?php else: ?>
                        <!-- No Ratings -->
                        <div class="text-center mt-5">
                            <i class="fas fa-star-half-alt" style="font-size: 4rem; color: #ddd; margin-bottom: 20px;"></i>
                            <h4>No Ratings Found</h4>
                            <?php if (!empty($search)): ?>
                            <p class="mb-4">No ratings found matching your search criteria.</p>
                            <a href="admin-ratings" class="btn btn-primary">
                                <i class="fas fa-list"></i> View All Ratings
                            </a>
                            <?php else: ?>
                            <p class="mb-4">No ticket ratings have been submitted yet.</p>
                            <a href="admin-tickets" class="btn btn-primary">
                                <i class="fas fa-ticket-alt"></i> View Tickets
                            </a>
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>

                </div>
            </div>
        </div>
    </div>

    <!-- Edit Rating Modal -->
    <div class="modal fade" id="editRatingModal" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Edit Rating</h5>
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <form method="POST">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="edit">
                        <input type="hidden" name="rating_id" id="edit_rating_id">

                        <div class="form-group">
                            <label for="edit_rating">Rating (1-5 stars):</label>
                            <select name="new_rating" id="edit_rating" class="form-control" required>
                                <option value="1">1 Star - Poor</option>
                                <option value="2">2 Stars - Fair</option>
                                <option value="3">3 Stars - Good</option>
                                <option value="4">4 Stars - Very Good</option>
                                <option value="5">5 Stars - Excellent</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="edit_comment">Comment:</label>
                            <textarea name="new_comment" id="edit_comment" class="form-control" rows="4"
                                      placeholder="Customer's comment about their experience..."></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-danger" data-dismiss="modal" style="background-color: #dc3545; color: white;">Cancel</button>
                        <button type="submit" class="btn btn-primary">Update Rating</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Delete Rating Modal -->
    <div class="modal fade" id="deleteRatingModal" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title" style="color: white;">Delete Rating</h5>
                    <button type="button" class="close text-white" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <form method="POST">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="delete">
                        <input type="hidden" name="rating_id" id="delete_rating_id">

                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle"></i>
                            <strong>Warning:</strong> This action cannot be undone.
                        </div>

                        <p>Are you sure you want to delete the rating for:</p>
                        <p><strong id="delete_ticket_subject"></strong></p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn" data-dismiss="modal" style="background-color: #6c757d; color: white;">Cancel</button>
                        <button type="submit" class="btn btn-danger">Delete Rating</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="../js/vendor.min.js"></script>
    <script>
        function editRating(ratingId, currentRating, currentComment) {
            document.getElementById('edit_rating_id').value = ratingId;
            document.getElementById('edit_rating').value = currentRating;
            document.getElementById('edit_comment').value = currentComment;
            $('#editRatingModal').modal('show');
        }

        function deleteRating(ratingId, ticketSubject) {
            document.getElementById('delete_rating_id').value = ratingId;
            document.getElementById('delete_ticket_subject').textContent = 'Ticket: ' + ticketSubject;
            $('#deleteRatingModal').modal('show');
        }

        // Auto-hide alerts after 5 seconds
        document.addEventListener('DOMContentLoaded', function() {
            const alerts = document.querySelectorAll('.alert-dismissible');
            alerts.forEach(function(alert) {
                setTimeout(function() {
                    if (alert.parentNode) {
                        alert.classList.remove('show');
                        setTimeout(function() {
                            if (alert.parentNode) {
                                alert.parentNode.removeChild(alert);
                            }
                        }, 150);
                    }
                }, 5000);
            });

            // User dropdown functionality
            const userDropdown = document.querySelector('.user-dropdown');
            const userInfo = document.querySelector('.user-info');
            const menuToggle = document.querySelector('.mobile-menu-toggle');

            // Only apply dropdown functionality on mobile
            if (window.innerWidth <= 767) {
                userInfo.addEventListener('click', function(e) {
                    e.preventDefault();
                    userDropdown.classList.toggle('active');
                    menuToggle.classList.toggle('active');
                });

                // Close dropdown when clicking outside
                document.addEventListener('click', function(e) {
                    if (!userDropdown.contains(e.target)) {
                        userDropdown.classList.remove('active');
                        menuToggle.classList.remove('active');
                    }
                });
            }
        });
    </script>
</body>
</html>
