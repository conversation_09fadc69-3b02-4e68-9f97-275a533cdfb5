<?php
session_start();
include('../functions/server.php');
include('../functions/timezone-helper.php');
include('../functions/graphql_functions.php');
require_once '../vendor/autoload.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_username'])) {
    header('Location: admin-login.php');
    exit();
}

$admin_username = $_SESSION['admin_username'];
$admin_id = $_SESSION['admin_id'];

// Get ticket ID from URL
$ticket_id = isset($_GET['ticket_id']) ? intval($_GET['ticket_id']) : 0;

if ($ticket_id <= 0) {
    echo '<div class="alert alert-danger">Invalid ticket ID provided.</div>';
    exit();
}

// Fetch ticket details from Appika API
$query = '
query GetTicket($id: Int!) {
    getTicket(id: $id) {
        id
        ticket_no
        contact_id
        agent_id
        req_email
        subject
        type
        type_name
        priority
        status
        created
        updated
        contacts {
            id
            email
            fname
            status
        }
    }
}';

$variables = ['id' => $ticket_id];
$result = makeGraphQLRequest($query, $variables);

if (!$result['success']) {
    echo '<div class="alert alert-danger">Error fetching ticket: ' . htmlspecialchars($result['error'] ?? 'Unknown error') . '</div>';
    exit();
}

$ticket = $result['data']['data']['getTicket'] ?? null;

if (!$ticket) {
    echo '<div class="alert alert-warning">Ticket not found.</div>';
    exit();
}

// Fetch additional customer data from Appika Customer API
$customerData = null;
$debugInfo = [];

// Try to get customer data by finding the local user via email
if (isset($ticket['contacts']['email']) && !empty($ticket['contacts']['email'])) {
    include('../functions/customer-data-service.php');

    $customerEmail = $ticket['contacts']['email'];
    $debugInfo['customer_email'] = $customerEmail;

    try {
        // Find local user by email and get company_name from database
        $userQuery = "SELECT id, username, appika_customer_id, company_name FROM user WHERE email = ?";
        $stmt = mysqli_prepare($conn, $userQuery);
        mysqli_stmt_bind_param($stmt, 's', $customerEmail);
        mysqli_stmt_execute($stmt);
        $userResult = mysqli_stmt_get_result($stmt);
        $localUser = mysqli_fetch_assoc($userResult);
        mysqli_stmt_close($stmt);

        $debugInfo['local_user_found'] = $localUser ? 'Yes' : 'No';

        if ($localUser && !empty($localUser['appika_customer_id'])) {
            $debugInfo['appika_customer_id'] = $localUser['appika_customer_id'];

            // Use the customer data service to get complete customer data
            $customerData = getCompleteCustomerData($localUser['username']);
            $debugInfo['customer_data_fetched'] = $customerData ? 'Yes' : 'No';

            // Ensure local company_name is included in customer data
            if ($customerData && isset($localUser['company_name'])) {
                $customerData['company_name'] = $localUser['company_name'];
            }

        } elseif ($localUser) {
            $debugInfo['no_appika_id'] = 'User found but no Appika customer ID';

            // Even without Appika data, we can still show local company info
            $customerData = [
                'has_appika_data' => false,
                'company_name' => $localUser['company_name'] ?? '',
                'name' => '',
                'tel_work' => ''
            ];
        }

    } catch (Exception $e) {
        error_log("Failed to fetch customer data: " . $e->getMessage());
        $debugInfo['error'] = $e->getMessage();
    }
}

// Debug output (remove this after fixing)
if (isset($_GET['debug']) && $_GET['debug'] == '1') {
    echo '<pre style="background: #f0f0f0; padding: 10px; margin: 10px; border: 1px solid #ccc;">';
    echo "Debug Info:\n";
    print_r($debugInfo);
    echo "\nTicket Data:\n";
    print_r($ticket);
    echo "\nCustomer Data:\n";
    print_r($customerData);
    echo '</pre>';
}

// Get messages from chat_messages table (like HelloITOld)
$messages_query = "SELECT cm.*,
                  CASE
                      WHEN cm.sender_type = 'admin' THEN a.username
                      WHEN cm.sender_type = 'user' THEN u.username
                  END as sender_name
                  FROM chat_messages cm
                  LEFT JOIN admin_users a ON cm.sender_id = a.id AND cm.sender_type = 'admin'
                  LEFT JOIN user u ON cm.sender_id = u.id AND cm.sender_type = 'user'
                  WHERE cm.ticket_id = ?
                  ORDER BY cm.created_at ASC";
$stmt = mysqli_prepare($conn, $messages_query);
mysqli_stmt_bind_param($stmt, 'i', $ticket_id);
mysqli_stmt_execute($stmt);
$messages_result = mysqli_stmt_get_result($stmt);
$messages = [];
while ($message = mysqli_fetch_assoc($messages_result)) {
    $messages[] = $message;
}
mysqli_stmt_close($stmt);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Ticket Chat - #<?php echo htmlspecialchars($ticket['ticket_no'] ?? $ticket_id); ?></title>
    <link rel="shortcut icon" href="../image/png/favicon.png" type="image/x-icon">
    <!-- Bootstrap and plugins -->
    <link rel="stylesheet" href="../css/bootstrap.css">
    <link rel="stylesheet" href="../css/main.css">
    <link rel="stylesheet" href="../fonts/fontawesome-5/css/all.css">
    <style>
    body {
        background-color: #f8f9fa;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }

    .chat-container {
        height: 100vh;
        display: flex;
        flex-direction: column;
    }

    .chat-header {
        background-color: #473BF0;
        color: white;
        padding: 15px 20px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .chat-header h4 {
        margin: 0;
        font-size: 18px;
    }

    .back-btn {
        background-color: rgba(255,255,255,0.2);
        color: white;
        border: none;
        padding: 8px 16px;
        border-radius: 5px;
        text-decoration: none;
        transition: background-color 0.3s;
    }

    .back-btn:hover {
        background-color: rgba(255,255,255,0.3);
        color: white;
        text-decoration: none;
    }

    .header-actions {
        display: flex;
        gap: 10px;
    }

    .header-actions .btn {
        border: 1px solid rgba(255,255,255,0.3);
        font-size: 0.875rem;
        padding: 0.25rem 0.75rem;
    }

    .header-actions .btn:hover {
        background-color: rgba(255,255,255,0.1);
        border-color: rgba(255,255,255,0.5);
    }

    .header-actions .btn-info {
        background-color: #17a2b8;
        border-color: #17a2b8;
    }

    .header-actions .btn-info:hover {
        background-color: #138496;
        border-color: #117a8b;
    }

    .chat-body {
        flex: 1;
        display: flex;
        overflow: hidden;
    }

    /* Left Side - Ticket Details */
    .ticket-details {
        width: 35%;
        background-color: white;
        border-right: 1px solid #dee2e6;
        overflow-y: auto;
        padding: 20px;
    }

    .ticket-info-card {
        background-color: #f8f9fa;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
    }

    .ticket-info-card h5 {
        color: #473BF0;
        margin-bottom: 15px;
        font-weight: 600;
    }

    .info-row {
        display: flex;
        justify-content: space-between;
        margin-bottom: 10px;
        padding: 8px 0;
        border-bottom: 1px solid #e9ecef;
    }

    .info-row:last-child {
        border-bottom: none;
        margin-bottom: 0;
    }

    .info-label {
        font-weight: 600;
        color: #495057;
        flex: 0 0 40%;
    }

    .info-value {
        color: #212529;
        flex: 1;
        text-align: right;
    }

    .badge {
        padding: 0.25em 0.6em;
        font-size: 75%;
        font-weight: 700;
        border-radius: 0.25rem;
    }

    .badge-starter { background-color: #fbbf24; color: #fff; }
    .badge-business { background-color: #01A7E1; color: #fff; }
    .badge-ultimate { background-color: #793BF0; color: #fff; }
    .badge-high { background-color: #dc3545; color: #fff; }
    .badge-medium { background-color: #ffc107; color: #212529; }
    .badge-low { background-color: #28a745; color: #fff; }
    .badge-urgent { background-color: #dc3545; color: #fff; }
    .badge-open { background-color: #4CAF50; color: #fff; }
    .badge-wip { background-color: #FF9800; color: #fff; }
    .badge-solved { background-color: #17a2b8; color: #fff; }
    .badge-closed { background-color: #757575; color: #fff; }

    /* Right Side - Chat */
    .chat-area {
        width: 65%;
        display: flex;
        flex-direction: column;
        background-color: white;
    }

    .messages-container {
        flex: 1;
        overflow-y: auto;
        padding: 20px;
        background-color: #f8f9fa;
    }

    .message {
        margin-bottom: 15px;
        display: flex;
        flex-direction: column;
    }

    .message.user {
        align-items: flex-start;
    }

    .message.admin {
        align-items: flex-end;
    }

    .message.description {
        justify-content: center;
        margin-bottom: 25px;
    }

    .message-bubble {
        max-width: 70%;
        padding: 12px 16px;
        border-radius: 18px;
        word-wrap: break-word;
        position: relative;
    }

    .message.user .message-bubble {
        background-color: #e9ecef;
        color: #333;
        border-bottom-left-radius: 5px;
    }

    .message.admin .message-bubble {
        background-color: #473BF0;
        color: white;
        border-bottom-right-radius: 5px;
    }

    .message.description .message-bubble {
        background-color: #e3f2fd;
        color: #1976d2;
        border-left: 4px solid #2196f3;
        border-radius: 5px;
        max-width: 90%;
    }

    .message-info {
        font-size: 12px;
        color: #6c757d;
        margin-top: 5px;
        text-align: left;
    }

    .message.admin .message-info {
        text-align: right;
    }

    .message-form {
        padding: 20px;
        background-color: white;
        border-top: 1px solid #dee2e6;
    }

    .message-input-group {
        display: flex;
        gap: 10px;
        align-items: flex-end;
    }

    .message-input {
        flex: 1;
        border: 1px solid #ced4da;
        border-radius: 25px;
        padding: 12px 20px;
        resize: none;
        min-height: 50px;
        max-height: 120px;
        font-family: inherit;
    }

    .send-btn {
        background-color: #473BF0;
        color: white;
        border: none;
        border-radius: 50%;
        width: 50px;
        height: 50px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: background-color 0.3s;
    }

    .send-btn:hover {
        background-color: #3d32d9;
    }

    .send-btn:disabled {
        background-color: #6c757d;
        cursor: not-allowed;
    }

    .empty-messages {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        text-align: center;
        color: #6c757d;
        padding: 40px;
        height: 100%;
        min-height: 200px;
    }

    .empty-messages i {
        font-size: 48px;
        margin-bottom: 15px;
        color: #dee2e6;
    }

    /* Admin specific styles */
    .admin-badge {
        background-color: #dc3545;
        color: white;
        font-size: 10px;
        padding: 2px 6px;
        border-radius: 10px;
        margin-left: 5px;
    }

    /* Mobile Responsive */
    @media (max-width: 768px) {
        .chat-body {
            flex-direction: column;
        }
        
        .ticket-details {
            width: 100%;
            max-height: 40vh;
        }
        
        .chat-area {
            width: 100%;
            flex: 1;
        }
        
        .message-bubble {
            max-width: 85%;
        }
        
        .chat-header h4 {
            font-size: 16px;
        }
    }
    </style>
</head>
<body>
    <div class="chat-container">
        <!-- Header -->
        <div class="chat-header">
            <a href="admin-tickets.php" class="back-btn">
                <i class="fas fa-arrow-left"></i> Back to Tickets
            </a>
            <div class="header-actions">
                <button class="btn btn-sm btn-info me-2" onclick="showPriorityModal()" title="Update Priority" <?php echo (strtolower($ticket['status']) === 'closed') ? 'disabled' : ''; ?>>
                    <i class="fas fa-exclamation-triangle"></i> Priority
                </button>
                <button class="btn btn-sm btn-success me-2" onclick="updateTicketStatus('solved')" title="Mark as Solved">
                    <i class="fas fa-check-circle"></i> Solved
                </button>
                <button class="btn btn-sm btn-warning me-2" onclick="updateTicketStatus('resolved')" title="Mark as Resolved">
                    <i class="fas fa-check"></i> Resolve
                </button>
                <button class="btn btn-sm btn-secondary" onclick="updateTicketStatus('closed')" title="Close Ticket">
                    <i class="fas fa-times"></i> Close
                </button>
            </div>
        </div>

        <!-- Body -->
        <div class="chat-body">
            <!-- Left Side - Ticket Details -->
            <div class="ticket-details">
                <div class="ticket-info-card">
                    <h5><i class="fas fa-info-circle"></i> Ticket Information</h5>
                    
                    <div class="info-row">
                        <span class="info-label">Ticket ID:</span>
                        <span class="info-value">#<?php echo htmlspecialchars($ticket['ticket_no'] ?? $ticket_id); ?></span>
                    </div>
                    
                    <div class="info-row">
                        <span class="info-label">Subject:</span>
                        <span class="info-value"><?php echo htmlspecialchars($ticket['subject'] ?? 'N/A'); ?></span>
                    </div>
                    
                    <div class="info-row">
                        <span class="info-label">Status:</span>
                        <span class="info-value">
                            <span class="badge badge-<?php echo strtolower($ticket['status'] ?? 'open'); ?>">
                                <?php echo strtoupper($ticket['status'] ?? 'OPEN'); ?>
                            </span>
                        </span>
                    </div>
                    
                    <div class="info-row">
                        <span class="info-label">Priority:</span>
                        <span class="info-value">
                            <span class="badge badge-<?php echo strtolower($ticket['priority'] ?? 'medium'); ?>">
                                <?php echo strtoupper($ticket['priority'] ?? 'MEDIUM'); ?>
                            </span>
                        </span>
                    </div>
                    
                    <div class="info-row">
                        <span class="info-label">Type:</span>
                        <span class="info-value">
                            <span class="badge badge-<?php echo strtolower($ticket['type_name'] ?? 'starter'); ?>">
                                <?php echo ucfirst($ticket['type_name'] ?? 'Starter'); ?>
                            </span>
                        </span>
                    </div>
                    
                    <div class="info-row">
                        <span class="info-label">Created:</span>
                        <span class="info-value"><?php echo date('M j, Y H:i', strtotime($ticket['created'] ?? 'now')); ?></span>
                    </div>
                    
                    <div class="info-row">
                        <span class="info-label">Updated:</span>
                        <span class="info-value"><?php echo date('M j, Y H:i', strtotime($ticket['updated'] ?? 'now')); ?></span>
                    </div>
                </div>

                <?php if (isset($ticket['contacts']) && !empty($ticket['contacts'])): ?>
                <div class="ticket-info-card">
                    <h5><i class="fas fa-user"></i> Customer Information</h5>

                    <div class="info-row">
                        <span class="info-label">Name:</span>
                        <span class="info-value">
                            <?php
                            // Priority: 1. Appika Customer API name, 2. Ticket contacts fname, 3. Empty
                            $displayName = '';
                            if ($customerData && $customerData['has_appika_data'] && !empty($customerData['name'])) {
                                $displayName = $customerData['name'];
                            } elseif (isset($ticket['contacts']['fname']) && !empty($ticket['contacts']['fname'])) {
                                $displayName = $ticket['contacts']['fname'];
                            }
                            echo htmlspecialchars($displayName);
                            ?>
                        </span>
                    </div>

                    <div class="info-row">
                        <span class="info-label">Email:</span>
                        <span class="info-value">
                            <?php
                            // Display email from ticket contacts (primary source)
                            echo htmlspecialchars($ticket['contacts']['email'] ?? '');
                            ?>
                        </span>
                    </div>

                    <div class="info-row">
                        <span class="info-label">Company:</span>
                        <span class="info-value">
                            <?php
                            // Display company name from local database only
                            $companyName = '';

                            // Get company name from local database
                            if ($customerData && !empty($customerData['company_name'])) {
                                $companyName = $customerData['company_name'];
                            }

                            echo htmlspecialchars($companyName);
                            ?>
                        </span>
                    </div>

                    <div class="info-row">
                        <span class="info-label">Phone:</span>
                        <span class="info-value">
                            <?php
                            // Display phone from Appika Customer API
                            $phone = '';
                            if ($customerData && $customerData['has_appika_data'] && !empty($customerData['tel_work'])) {
                                $phone = $customerData['tel_work'];
                            }
                            echo htmlspecialchars($phone);
                            ?>
                        </span>
                    </div>
                </div>
                <?php endif; ?>
            </div>

            <!-- Right Side - Chat Area -->
            <div class="chat-area">
                <!-- Messages Container -->
                <div class="messages-container" id="messagesContainer">
                    <?php if (empty($messages)): ?>
                    <div class="empty-messages">
                        <i class="fas fa-comments"></i>
                        <p>No messages yet. Start the conversation by sending a message below.</p>
                    </div>
                    <?php else: ?>
                        <?php foreach ($messages as $message): ?>
                            <div class="message <?php echo $message['sender_type']; ?>">
                                <div class="message-bubble">
                                    <?php echo nl2br(htmlspecialchars($message['message'])); ?>
                                </div>
                                <div class="message-info">
                                    <?php
                                    echo htmlspecialchars($message['sender_name'] ?? 'Unknown');
                                    if ($message['sender_type'] === 'admin') {
                                        echo ' <span class="admin-badge">ADMIN</span>';
                                    }
                                    ?>
                                    • <?php echo date('M j, H:i', strtotime($message['created_at'])); ?>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>

                <!-- Message Form -->
                <div class="message-form">
                    <form id="messageForm" onsubmit="sendMessage(event)">
                        <div class="message-input-group">
                            <textarea 
                                id="messageInput" 
                                class="message-input" 
                                placeholder="Type your admin message here..." 
                                required
                                onkeydown="handleKeyDown(event)"
                                oninput="autoResize(this)"
                            ></textarea>
                            <button type="submit" class="send-btn" id="sendBtn">
                                <i class="fas fa-paper-plane"></i>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Priority Update Modal -->
    <div class="modal fade" id="priorityModal" tabindex="-1" aria-labelledby="priorityModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="priorityModalLabel">Update Ticket Priority</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="priorityForm">
                        <div class="mb-3">
                            <label for="prioritySelect" class="form-label">Select Priority:</label>
                            <select class="form-select" id="prioritySelect" required>
                                <option value="low">Low</option>
                                <option value="medium">Medium</option>
                                <option value="high">High</option>
                                <option value="urgent">Urgent</option>
                            </select>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn" style="background-color: #dc3545; color: white;" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" onclick="updateTicketPriority()">Update Priority</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
    const ticketId = <?php echo $ticket_id; ?>;
    const adminId = <?php echo $admin_id; ?>;
    
    // Auto-resize textarea
    function autoResize(textarea) {
        textarea.style.height = 'auto';
        textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px';
    }
    
    // Handle Enter key
    function handleKeyDown(event) {
        if (event.key === 'Enter' && !event.shiftKey) {
            event.preventDefault();
            document.getElementById('messageForm').dispatchEvent(new Event('submit'));
        }
    }
    
    // Send message function
    function sendMessage(event) {
        event.preventDefault();
        
        const messageInput = document.getElementById('messageInput');
        const sendBtn = document.getElementById('sendBtn');
        const message = messageInput.value.trim();
        
        if (!message) return;
        
        // Disable form
        sendBtn.disabled = true;
        sendBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
        
        // Send message via fetch
        fetch('admin-ticket-chat-api.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `action=send_message&ticket_id=${ticketId}&message=${encodeURIComponent(message)}`
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                messageInput.value = '';
                messageInput.style.height = 'auto';
                loadMessages(); // Reload messages
            } else {
                alert('Error sending message: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error sending message. Please try again.');
        })
        .finally(() => {
            sendBtn.disabled = false;
            sendBtn.innerHTML = '<i class="fas fa-paper-plane"></i>';
        });
    }
    
    // Load messages function
    function loadMessages() {
        fetch(`admin-ticket-chat-api.php?action=get_messages&ticket_id=${ticketId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayMessages(data.messages);
            }
        })
        .catch(error => console.error('Error loading messages:', error));
    }
    
    // Display messages
    function displayMessages(messages) {
        const messagesContainer = document.getElementById('messagesContainer');
        let html = '';
        
        if (messages.length === 0) {
            html = `
                <div class="empty-messages">
                    <i class="fas fa-comments"></i>
                    <p>No messages yet. Start the conversation by sending a message below.</p>
                </div>
            `;
        } else {
            messages.forEach((message, index) => {
                const messageClass = message.message_type;
                const senderName = message.sender_name || 'Unknown';
                const adminBadge = messageClass === 'admin' ? ' <span class="admin-badge">ADMIN</span>' : '';
                const createdAt = new Date(message.created_at).toLocaleDateString('en-US', {
                    month: 'short',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit'
                });
                
                html += `
                    <div class="message ${messageClass}">
                        <div class="message-bubble">
                            ${messageClass === 'description' ? '<strong>Ticket Description:</strong><br>' : ''}
                            ${escapeHtml(message.message).replace(/\n/g, '<br>')}
                        </div>
                        <div class="message-info">
                            ${senderName}${adminBadge} • ${createdAt}
                        </div>
                    </div>
                `;
            });
        }
        
        messagesContainer.innerHTML = html;
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }
    
    // Escape HTML
    function escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
    
    // Auto-refresh messages every 10 seconds
    setInterval(loadMessages, 10000);

    // Update ticket status function
    function updateTicketStatus(status) {
        if (!confirm(`Are you sure you want to mark this ticket as ${status}?`)) {
            return;
        }

        const formData = new FormData();
        formData.append('action', 'update_status');
        formData.append('ticket_id', <?php echo $ticket_id; ?>);
        formData.append('status', status);

        fetch('admin-ticket-chat-api.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(`Ticket marked as ${status} successfully!`);
                location.reload(); // Refresh to show updated status
            } else {
                alert('Error updating ticket status: ' + (data.error || 'Unknown error'));
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error updating ticket status');
        });
    }

    // Show priority modal
    function showPriorityModal() {
        // Check if ticket is closed
        const ticketStatus = '<?php echo strtolower($ticket['status']); ?>';
        if (ticketStatus === 'closed') {
            alert('Cannot update priority for closed tickets.');
            return;
        }

        const modal = new bootstrap.Modal(document.getElementById('priorityModal'));
        modal.show();
    }

    // Update ticket priority function
    function updateTicketPriority() {
        // Check if ticket is closed
        const ticketStatus = '<?php echo strtolower($ticket['status']); ?>';
        if (ticketStatus === 'closed') {
            alert('Cannot update priority for closed tickets.');
            return;
        }

        const priority = document.getElementById('prioritySelect').value;

        if (!priority) {
            alert('Please select a priority');
            return;
        }

        const formData = new FormData();
        formData.append('action', 'update_priority');
        formData.append('ticket_id', <?php echo $ticket_id; ?>);
        formData.append('priority', priority);

        fetch('admin-ticket-chat-api.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(`Ticket priority updated to ${priority.toUpperCase()} successfully!`);
                // Hide modal
                const modal = bootstrap.Modal.getInstance(document.getElementById('priorityModal'));
                modal.hide();
                location.reload(); // Refresh to show updated priority
            } else {
                alert('Error updating ticket priority: ' + (data.error || 'Unknown error'));
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error updating ticket priority');
        });
    }
    </script>
</body>
</html>
