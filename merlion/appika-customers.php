<?php
session_start();
include('../functions/server.php');

// Check if admin is logged in
if (!isset($_SESSION['admin_username'])) {
    header('location: admin-login');
    exit();
}

$admin_username = $_SESSION['admin_username'];
$admin_id = $_SESSION['admin_id'];
$admin_role = $_SESSION['admin_role'];

// Include Composer autoloader for Guzzle
require_once __DIR__ . '/../vendor/autoload.php';

// Get search and filter parameters
$search = isset($_GET['search']) ? trim($_GET['search']) : "";
$entity_filter = isset($_GET['entity_type']) ? trim($_GET['entity_type']) : "";
$status_filter = isset($_GET['status']) ? trim($_GET['status']) : "";
$group_filter = isset($_GET['group']) ? trim($_GET['group']) : "";

// Pagination settings
$items_per_page = 10;
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
if ($page < 1) $page = 1;

// Load centralized API configuration
require_once '../config/api-config.php';

// Get API configuration
$apiConfig = getCustomerApiConfig();
$apiEndpoint = $apiConfig['endpoint'];
$apiPath = $apiConfig['path'];
$apiKey = $apiConfig['key'];

// Function to make API requests
function makeCustomerApiRequest($method, $endpoint, $options = []) {
    global $apiEndpoint, $apiKey;

    try {
        $client = new \GuzzleHttp\Client([
            'base_uri' => $apiEndpoint,
            'timeout' => 30,
            'http_errors' => false,
        ]);

        $defaultOptions = [
            'headers' => [
                'X-api-key' => "{$apiKey}",
                'Accept' => 'application/json',
            ]
        ];

        $options = array_merge_recursive($defaultOptions, $options);
        $response = $client->request($method, $endpoint, $options);

        $statusCode = $response->getStatusCode();
        $body = $response->getBody()->getContents();
        $data = json_decode($body, true);

        return [
            'success' => $statusCode >= 200 && $statusCode < 300,
            'status' => $statusCode,
            'data' => $data,
            'error' => $statusCode >= 400 ? "HTTP Error {$statusCode}" : null,
            'raw_response' => $body
        ];
    } catch (\Exception $e) {
        return [
            'success' => false,
            'status' => 0,
            'data' => null,
            'error' => 'Request Error: ' . $e->getMessage(),
            'raw_response' => null
        ];
    }
}

// Fetch customers from Appika API
function fetchAppikaCustomers($search = "", $entity_filter = "", $status_filter = "", $group_filter = "", $page = 1, $items_per_page = 10) {
    global $apiPath;

    // Build query parameters
    $queryParams = [
        'limit' => 100, // Get more data to filter locally
        'offset' => 0
    ];

    // If searching for specific customer number, add it to query
    if (!empty($search) && preg_match('/^[A-Z0-9]+$/', $search)) {
        $queryParams['no'] = $search;
    }

    $result = makeCustomerApiRequest('GET', $apiPath, [
        'query' => $queryParams
    ]);

    if ($result['success'] && isset($result['data']['items'])) {
        $customers = $result['data']['items'];

        // Apply filters
        if (!empty($search)) {
            $customers = array_filter($customers, function($customer) use ($search) {
                $search_lower = strtolower($search);
                return (
                    strpos(strtolower($customer['id'] ?? ''), $search_lower) !== false ||
                    strpos(strtolower($customer['no'] ?? ''), $search_lower) !== false ||
                    strpos(strtolower($customer['name'] ?? ''), $search_lower) !== false ||
                    strpos(strtolower($customer['start_date'] ?? ''), $search_lower) !== false
                );
            });
        }

        if (!empty($entity_filter)) {
            $customers = array_filter($customers, function($customer) use ($entity_filter) {
                return ($customer['entity_type'] ?? '') === $entity_filter;
            });
        }

        if (!empty($status_filter)) {
            $customers = array_filter($customers, function($customer) use ($status_filter) {
                return strtolower($customer['status'] ?? '') === strtolower($status_filter);
            });
        }

        if (!empty($group_filter)) {
            $customers = array_filter($customers, function($customer) use ($group_filter) {
                return ($customer['grp_id'] ?? '') === $group_filter;
            });
        }

        // Sort by start date (newest first)
        usort($customers, function($a, $b) {
            return strtotime($b['start_date'] ?? '0') - strtotime($a['start_date'] ?? '0');
        });

        // Apply pagination
        $total_items = count($customers);
        $offset = ($page - 1) * $items_per_page;
        $customers = array_slice($customers, $offset, $items_per_page);

        return [
            'success' => true,
            'customers' => $customers,
            'total_items' => $total_items,
            'total_pages' => ceil($total_items / $items_per_page)
        ];
    }

    return [
        'success' => false,
        'customers' => [],
        'total_items' => 0,
        'total_pages' => 0,
        'error' => $result['error'] ?? 'Failed to fetch customers'
    ];
}

// Function to fetch individual customer details
function fetchCustomerDetails($customerId) {
    global $apiPath;

    $result = makeCustomerApiRequest('GET', $apiPath . '/' . $customerId);

    if ($result['success'] && isset($result['data'])) {
        $customer = $result['data'];

        // Also fetch customer locations
        $locationsResult = makeCustomerApiRequest('GET', $apiPath . '/' . $customerId . '/locations');
        if ($locationsResult['success'] && isset($locationsResult['data']['items'])) {
            $customer['locations'] = $locationsResult['data']['items'];
        } else {
            $customer['locations'] = [];
        }

        return [
            'success' => true,
            'customer' => $customer
        ];
    }

    return [
        'success' => false,
        'error' => $result['error'] ?? 'Failed to fetch customer details'
    ];
}

// Handle AJAX request for customer details
if (isset($_GET['action']) && $_GET['action'] === 'get_customer_details' && isset($_GET['customer_id'])) {
    $customerId = intval($_GET['customer_id']);
    $customerDetails = fetchCustomerDetails($customerId);

    header('Content-Type: application/json');
    echo json_encode($customerDetails);
    exit;
}

// Fetch customers
$customer_data = fetchAppikaCustomers($search, $entity_filter, $status_filter, $group_filter, $page, $items_per_page);
$customers = $customer_data['customers'];
$total_items = $customer_data['total_items'];
$total_pages = $customer_data['total_pages'];
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin - Appika Customers</title>
    <link rel="shortcut icon" href="../image/png/favicon.png" type="image/x-icon">
    <!-- Bootstrap, fonts & icons -->
    <link rel="stylesheet" href="../css/bootstrap.css">
    <link rel="stylesheet" href="../fonts/icon-font/css/style.css">
    <link rel="stylesheet" href="../fonts/typography-font/typo.css">
    <link rel="stylesheet" href="../fonts/fontawesome-5/css/all.css">
    <!-- Vendor stylesheets -->
    <link rel="stylesheet" href="../css/main.css">
    <style>
    body {
        background-color: #f8f9fa;
    }

    .admin-container {
        padding: 20px;
        max-width: 100%;
        overflow-x: hidden;
    }

    .admin-header {
        background-color: #fff;
        padding: 15px 20px;
        border-radius: 5px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
    }

    .admin-header h1 {
        margin: 0;
        font-size: 24px;
        color: #333;
    }

    .admin-user {
        display: flex;
        align-items: center;
    }

    .admin-user span {
        margin-right: 10px;
    }

    .admin-sidebar {
        background-color: #fff;
        border-radius: 5px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        padding: 20px;
        height: calc(100vh - 100px);
        overflow-y: auto;
    }

    .admin-sidebar ul {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .admin-sidebar ul li {
        margin-bottom: 10px;
    }

    .admin-sidebar ul li a {
        display: block;
        padding: 10px 15px;
        color: #333;
        text-decoration: none;
        border-radius: 5px;
        transition: all 0.3s;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .admin-sidebar ul li a:hover,
    .admin-sidebar ul li a.active {
        background-color: #473BF0;
        color: #fff;
    }

    .admin-sidebar ul li a i {
        margin-right: 10px;
        width: 20px;
        text-align: center;
    }

    .admin-content {
        background-color: #fff;
        border-radius: 5px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        padding: 20px;
        min-height: calc(100vh - 120px);
        overflow-x: hidden;
    }

    .filter-row {
        margin-bottom: 20px;
    }

    .badge {
        font-size: 16px;
        padding: 6.8px;
    }

    /* Customer Status badges */
    .badge-active {
        background-color: #28a745;
        color: #fff;
    }

    .badge-inactive {
        background-color: #6c757d;
        color: #fff;
    }

    /* Entity Type badges */
    .badge-company {
        background-color: #473BF0;
        color: #fff;
    }

    .badge-individual {
        background-color: #fd7e14;
        color: #fff;
    }

    /* Customer ID styling */
    .customer-id {
        font-family: 'Courier New', monospace;
        font-weight: bold;
        color: #473BF0;
        font-size: 12px;
    }

    .customer-no {
        font-family: 'Courier New', monospace;
        font-weight: bold;
        color: #28a745;
        font-size: 12px;
    }

    /* Search bar styles */
    .search-filter-row {
        padding: 0 40px;
    }

    .search-box {
        width: auto;
    }

    .search-box .input-group {
        width: 550px !important;
        max-width: 100% !important;
    }

    .search-input {
        border-top-right-radius: 0 !important;
        border-bottom-right-radius: 0 !important;
        height: 38px;
        font-size: 14px;
    }

    .search-button {
        border-top-right-radius: 4px !important;
        border-bottom-right-radius: 4px !important;
        border-top-left-radius: 0 !important;
        border-bottom-left-radius: 0 !important;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0.375rem 0.75rem;
        width: 40px !important;
        min-width: 40px !important;
    }

    .filter-row select {
        width: auto !important;
        padding: 4px 8px;
        height: 38px !important;
        margin-right: 5px !important;
        font-size: 14px;
    }

    .filter-container {
        padding-right: 20px;
        padding-left: 0;
    }

    .filter-container .d-flex {
        justify-content: flex-start !important;
    }

    @media (min-width: 992px) {
        .filter-container select {
            width: 120px !important;
            margin-right: 8px;
            margin-bottom: 8px;
        }
    }

    @media (max-width: 991px) {
        .filter-container {
            margin-top: 15px;
        }
    }

    /* Responsive adjustments */
    @media (max-width: 991px) {
        .search-box {
            width: 100%;
        }

        .search-box .input-group {
            width: 100% !important;
            max-width: 500px !important;
            margin: 0 auto;
        }
    }

    @media (max-width: 767px) {
        .search-filter-row {
            padding: 0 15px;
        }

        .search-box .input-group {
            width: 100% !important;
            max-width: 100% !important;
        }

        .search-input {
            font-size: 14px;
        }

        .filter-container {
            padding-left: 15px !important;
            padding-right: 15px !important;
            margin-top: 15px;
        }

        .filter-container select {
            width: 48% !important;
            margin-bottom: 10px;
            font-size: 13px;
        }

        .filter-container .d-flex {
            justify-content: space-between !important;
        }
    }

    /* Error message styling */
    .error-message {
        background-color: #f8d7da;
        color: #721c24;
        padding: 15px;
        border-radius: 5px;
        margin-bottom: 20px;
        border: 1px solid #f5c6cb;
    }

    /* Dropdown styles */
    .user-dropdown {
        position: relative;
        display: inline-block;
    }

    .user-info {
        margin-right: 10px;
        font-size: 14px;
        cursor: pointer;
        display: flex;
        align-items: center;
    }

    .mobile-menu-toggle {
        margin-left: 5px;
        transition: transform 0.3s ease;
        display: none;
    }

    .mobile-menu-toggle.active {
        transform: rotate(180deg);
    }

    .dropdown-content {
        display: block;
    }

    /* Mobile Styles */
    @media (max-width: 767px) {
        .mobile-menu-toggle {
            display: inline-block;
        }

        .dropdown-content {
            display: none;
            position: absolute;
            background-color: #f9f9f9;
            width: 100%;
            right: 0;
            box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.2);
            z-index: 1;
            border-radius: 4px;
            margin-top: 5px;
            padding: 8px;
        }

        .user-dropdown.active .dropdown-content {
            display: block;
        }
    }

    /* Table styles for mobile */
    @media (max-width: 767px) {
        .table {
            font-size: 14px;
        }

        .table th,
        .table td {
            padding: 0.5rem 0.3rem;
        }

        .badge {
            font-size: 12px;
            padding: 4px 6px;
        }

        .btn-sm {
            padding: 0.25rem 0.4rem;
            font-size: 12px;
        }
    }

    @media (max-width: 480px) {
        .table {
            font-size: 13px;
        }

        .table th,
        .table td {
            padding: 0.4rem 0.2rem;
        }
    }

    /* Customer Details Modal Styles */
    .modal-lg {
        max-width: 900px;
    }

    #customerDetailsModal .table-sm td {
        padding: 0.3rem;
        border-top: 1px solid #dee2e6;
    }

    #customerDetailsModal .table-sm td:first-child {
        width: 40%;
        background-color: #f8f9fa;
    }

    #customerDetailsModal .card {
        border: 1px solid #dee2e6;
    }

    #customerDetailsModal .card-header {
        background-color: #f8f9fa;
        border-bottom: 1px solid #dee2e6;
        padding: 0.5rem 1rem;
    }

    #customerDetailsModal .card-body {
        padding: 1rem;
    }

    /* Loading spinner */
    .fa-spinner {
        color: #473BF0;
    }
    </style>
</head>

<body>
    <div class="admin-container">
        <div class="admin-header">
            <h1>Appika API Customers</h1>
            <div class="admin-user">
                <div class="user-dropdown">
                    <span class="user-info">
                        Welcome, <?php echo htmlspecialchars($admin_username); ?>
                        (<?php echo htmlspecialchars($admin_role); ?>)
                        <i class="mobile-menu-toggle fas fa-chevron-down"></i>
                    </span>
                    <div class="dropdown-content">
                        <a href="admin-logout.php" class="btn btn-sm btn-outline-danger">Logout</a>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-2 col-md-3 col-sm-12">
                <?php include('admin-menu.php'); ?>
            </div>

            <div class="col-lg-10 col-md-9 col-sm-12">
                <div class="admin-content">
                    <?php if (!$customer_data['success']): ?>
                    <div class="error-message">
                        <strong>Error:</strong> <?php echo htmlspecialchars($customer_data['error']); ?>
                    </div>
                    <?php endif; ?>

                    <div class="filter-row">
                        <form method="GET" class="row">
                            <div class="col-md-6">
                                <div class="search-filter-row">
                                    <div class="search-box">
                                        <div class="input-group">
                                            <input type="text" name="search" class="form-control search-input"
                                                placeholder="Search by ID, customer no, name or date"
                                                value="<?php echo htmlspecialchars($search); ?>">
                                            <div class="input-group-append">
                                                <button type="submit" class="btn btn-primary search-button">
                                                    <i class="fas fa-search"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 filter-container">
                                <div class="d-flex flex-wrap">
                                    <select name="entity_type" class="form-control filter-select mr-2"
                                        onchange="this.form.submit()">
                                        <option value="">All Types</option>
                                        <option value="1" <?php echo $entity_filter === '1' ? 'selected' : ''; ?>>
                                            Company</option>
                                        <option value="2" <?php echo $entity_filter === '2' ? 'selected' : ''; ?>>
                                            Individual
                                        </option>
                                    </select>
                                    <select name="status" class="form-control filter-select mr-2"
                                        onchange="this.form.submit()">
                                        <option value="">All Status</option>
                                        <option value="a" <?php echo $status_filter === 'a' ? 'selected' : ''; ?>>
                                            Active</option>
                                        <option value="i" <?php echo $status_filter === 'i' ? 'selected' : ''; ?>>
                                            Inactive
                                        </option>
                                    </select>

                                    <select name="group" class="form-control filter-select mr-2"
                                        onchange="this.form.submit()">
                                        <option value="">All Groups</option>
                                        <option value="1" <?php echo $group_filter === '1' ? 'selected' : ''; ?>>
                                            Group 1</option>
                                        <option value="10" <?php echo $group_filter === '10' ? 'selected' : ''; ?>>Group
                                            10
                                        </option>
                                    </select>
                                </div>
                            </div>
                        </form>
                    </div>

                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>API ID</th>
                                    <th>Customer No</th>
                                    <th>Name</th>
                                    <th>Status</th>
                                    <th>Start Date</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (!empty($customers)): ?>
                                <?php foreach ($customers as $customer): ?>
                                <tr>
                                    <td>
                                        <span
                                            class="customer-id"><?php echo htmlspecialchars($customer['id'] ?? '-'); ?></span>
                                    </td>
                                    <td>
                                        <span
                                            class="customer-no"><?php echo htmlspecialchars($customer['no'] ?? '-'); ?></span>
                                    </td>
                                    <td><?php echo htmlspecialchars($customer['name'] ?? '-'); ?></td>
                                    <td>
                                        <?php
                                        $status = strtolower($customer['status'] ?? '');
                                        if ($status === 'a') {
                                            echo '<span class="badge badge-active">Active</span>';
                                        } elseif ($status === 'i') {
                                            echo '<span class="badge badge-inactive">Inactive</span>';
                                        } else {
                                            echo '<span class="badge badge-secondary">-</span>';
                                        }
                                        ?>
                                    </td>
                                    <td>
                                        <?php
                                        if (!empty($customer['start_date'])) {
                                            echo date('Y-m-d', strtotime($customer['start_date']));
                                        } else {
                                            echo '-';
                                        }
                                        ?>
                                    </td>
                                    <td>
                                        <button type="button" class="btn btn-info btn-sm"
                                                onclick="viewCustomerDetails(<?php echo htmlspecialchars($customer['id'] ?? '0'); ?>)"
                                                title="View Customer Details">
                                            <i class="fas fa-eye"></i> Details
                                        </button>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                                <?php else: ?>
                                <tr>
                                    <td colspan="6" class="text-center">
                                        <?php echo $customer_data['success'] ? 'No customers found' : 'Unable to load customers from API'; ?>
                                    </td>
                                </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>

                    <?php if ($total_pages > 1): ?>
                    <nav aria-label="Page navigation">
                        <ul class="pagination justify-content-center">
                            <li class="page-item <?php echo ($page <= 1) ? 'disabled' : ''; ?>">
                                <a class="page-link"
                                    href="?page=<?php echo $page - 1; ?>&search=<?php echo urlencode($search); ?>&entity_type=<?php echo urlencode($entity_filter); ?>&status=<?php echo urlencode($status_filter); ?>&group=<?php echo urlencode($group_filter); ?>">Previous</a>
                            </li>

                            <?php for ($i = 1; $i <= $total_pages; $i++): ?>
                            <li class="page-item <?php echo ($page == $i) ? 'active' : ''; ?>">
                                <a class="page-link"
                                    href="?page=<?php echo $i; ?>&search=<?php echo urlencode($search); ?>&entity_type=<?php echo urlencode($entity_filter); ?>&status=<?php echo urlencode($status_filter); ?>&group=<?php echo urlencode($group_filter); ?>"><?php echo $i; ?></a>
                            </li>
                            <?php endfor; ?>

                            <li class="page-item <?php echo ($page >= $total_pages) ? 'disabled' : ''; ?>">
                                <a class="page-link"
                                    href="?page=<?php echo $page + 1; ?>&search=<?php echo urlencode($search); ?>&entity_type=<?php echo urlencode($entity_filter); ?>&status=<?php echo urlencode($status_filter); ?>&group=<?php echo urlencode($group_filter); ?>">Next</a>
                            </li>
                        </ul>
                    </nav>
                    <?php endif; ?>

                    <div class="mt-3">
                        <small class="text-muted">
                            Showing <?php echo count($customers); ?> of <?php echo $total_items; ?> customers from
                            Appika API
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Customer Details Modal -->
    <div class="modal fade" id="customerDetailsModal" tabindex="-1" role="dialog" aria-labelledby="customerDetailsModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="customerDetailsModalLabel">
                        <i class="fas fa-user"></i> Customer Details
                    </h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body" id="customerDetailsContent">
                    <div class="text-center">
                        <i class="fas fa-spinner fa-spin fa-2x"></i>
                        <p class="mt-2">Loading customer details...</p>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary" data-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="../js/vendor.min.js"></script>
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        const userDropdown = document.querySelector('.user-dropdown');
        const userInfo = document.querySelector('.user-info');
        const menuToggle = document.querySelector('.mobile-menu-toggle');

        // Only apply dropdown functionality on mobile
        if (window.innerWidth <= 767) {
            userInfo.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                userDropdown.classList.toggle('active');
                menuToggle.classList.toggle('active');
            });

            // Also add direct click handler to the toggle icon for better mobile experience
            menuToggle.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                userDropdown.classList.toggle('active');
                menuToggle.classList.toggle('active');
            });

            // Close dropdown when clicking outside
            document.addEventListener('click', function(event) {
                if (!userDropdown.contains(event.target)) {
                    userDropdown.classList.remove('active');
                    menuToggle.classList.remove('active');
                }
            });
        }

        // Handle window resize
        window.addEventListener('resize', function() {
            if (window.innerWidth > 767) {
                userDropdown.classList.remove('active');
                menuToggle.classList.remove('active');
            }
        });
    });

    // Function to view customer details
    function viewCustomerDetails(customerId) {
        if (!customerId) {
            alert('Invalid customer ID');
            return;
        }

        // Show the modal
        $('#customerDetailsModal').modal('show');

        // Reset modal content to loading state
        document.getElementById('customerDetailsContent').innerHTML = `
            <div class="text-center">
                <i class="fas fa-spinner fa-spin fa-2x"></i>
                <p class="mt-2">Loading customer details...</p>
            </div>
        `;

        // Fetch customer details via AJAX
        fetch(`?action=get_customer_details&customer_id=${customerId}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    displayCustomerDetails(data.customer);
                } else {
                    document.getElementById('customerDetailsContent').innerHTML = `
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle"></i>
                            <strong>Error:</strong> ${data.error || 'Failed to load customer details'}
                        </div>
                    `;
                }
            })
            .catch(error => {
                console.error('Error fetching customer details:', error);
                document.getElementById('customerDetailsContent').innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle"></i>
                        <strong>Error:</strong> Failed to load customer details. Please try again.
                    </div>
                `;
            });
    }

    // Function to display customer details in the modal
    function displayCustomerDetails(customer) {
        const formatDate = (dateString) => {
            if (!dateString) return '-';
            try {
                return new Date(dateString).toLocaleDateString();
            } catch (e) {
                return dateString;
            }
        };

        const formatEntityType = (type) => {
            if (type === '1') return '<span class="badge badge-company">Company</span>';
            if (type === '2') return '<span class="badge badge-individual">Individual</span>';
            return '<span class="badge badge-secondary">-</span>';
        };

        const formatStatus = (status) => {
            if (status === 'a') return '<span class="badge badge-active">Active</span>';
            if (status === 'i') return '<span class="badge badge-inactive">Inactive</span>';
            return '<span class="badge badge-secondary">-</span>';
        };

        let locationsHtml = '';
        if (customer.locations && customer.locations.length > 0) {
            locationsHtml = customer.locations.map((location, index) => `
                <div class="card mb-2">
                    <div class="card-header">
                        <h6 class="mb-0">Location ${index + 1} ${location.is_primary_loc === 'y' ? '<span class="badge badge-primary">Primary</span>' : ''}</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <strong>Address:</strong> ${location.add1 || '-'}<br>
                                ${location.add2 ? `<strong>Address 2:</strong> ${location.add2}<br>` : ''}
                                <strong>City:</strong> ${location.city || '-'}<br>
                                <strong>State:</strong> ${location.state_code || '-'}<br>
                            </div>
                            <div class="col-md-6">
                                <strong>Postal Code:</strong> ${location.zip || '-'}<br>
                                <strong>Country:</strong> ${location.ccode || '-'}<br>
                                <strong>Phone:</strong> ${location.tel_work || location.tel_alt || '-'}<br>
                            </div>
                        </div>
                    </div>
                </div>
            `).join('');
        } else {
            locationsHtml = '<p class="text-muted">No locations found</p>';
        }

        const content = `
            <div class="row">
                <div class="col-md-12">
                    <h6><i class="fas fa-info-circle"></i> Basic Information</h6>
                    <table class="table table-sm">
                        <tr><td><strong>API ID:</strong></td><td><span class="customer-id">${customer.id || '-'}</span></td></tr>
                        <tr><td><strong>Customer No:</strong></td><td><span class="customer-no">${customer.no || '-'}</span></td></tr>
                        <tr><td><strong>Name:</strong></td><td>${customer.name || '-'}</td></tr>
                        <tr><td><strong>Status:</strong></td><td>${formatStatus(customer.status)}</td></tr>
                        <tr><td><strong>Start Date:</strong></td><td>${formatDate(customer.start_date)}</td></tr>
                    </table>
                </div>
            </div>
            <hr>
            <h6><i class="fas fa-map-marker-alt"></i> Locations</h6>
            ${locationsHtml}
        `;

        document.getElementById('customerDetailsContent').innerHTML = content;
    }
    </script>
</body>

</html>