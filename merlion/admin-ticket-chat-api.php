<?php
// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 0); // Don't display errors in output
ini_set('log_errors', 1);

session_start();
include('../functions/server.php');
include('../functions/graphql_functions.php');

// Set JSON header
header('Content-Type: application/json');

try {
    // Check if admin is logged in
    if (!isset($_SESSION['admin_username'])) {
        echo json_encode(['success' => false, 'error' => 'Admin not logged in']);
        exit();
    }

    $admin_username = $_SESSION['admin_username'];
    $admin_id = $_SESSION['admin_id'] ?? 0;

// Get action
$action = $_GET['action'] ?? $_POST['action'] ?? '';
$ticket_id = intval($_GET['ticket_id'] ?? $_POST['ticket_id'] ?? 0);

if ($ticket_id <= 0) {
    echo json_encode(['success' => false, 'error' => 'Invalid ticket ID']);
    exit();
}

// Verify ticket exists in Appika API
$verifyQuery = '
query GetTicket($id: Int!) {
    getTicket(id: $id) {
        id
        req_email
        status
        subject
        type
        priority
    }
}';

$verifyResult = makeGraphQLRequest($verifyQuery, ['id' => $ticket_id]);

if (!$verifyResult['success']) {
    echo json_encode(['success' => false, 'error' => 'Failed to verify ticket']);
    exit();
}

$ticketData = $verifyResult['data']['data']['getTicket'] ?? null;

if (!$ticketData) {
    echo json_encode(['success' => false, 'error' => 'Ticket not found']);
    exit();
}

switch ($action) {
    case 'get_messages':
        // Get messages from chat_messages table (like HelloITOld)
        $messages_query = "SELECT cm.*,
                          CASE
                              WHEN cm.sender_type = 'admin' THEN a.username
                              WHEN cm.sender_type = 'user' THEN u.username
                          END as sender_name
                          FROM chat_messages cm
                          LEFT JOIN admin_users a ON cm.sender_id = a.id AND cm.sender_type = 'admin'
                          LEFT JOIN user u ON cm.sender_id = u.id AND cm.sender_type = 'user'
                          WHERE cm.ticket_id = ?
                          ORDER BY cm.created_at ASC";
        $stmt = mysqli_prepare($conn, $messages_query);
        mysqli_stmt_bind_param($stmt, 'i', $ticket_id);
        mysqli_stmt_execute($stmt);
        $messages_result = mysqli_stmt_get_result($stmt);

        $messages = [];
        while ($message = mysqli_fetch_assoc($messages_result)) {
            $messages[] = [
                'id' => $message['id'],
                'message' => $message['message'],
                'message_type' => $message['sender_type'], // 'user' or 'admin'
                'sender_name' => $message['sender_name'] ?? 'Unknown',
                'created_at' => $message['created_at'],
                'is_read' => $message['is_read']
            ];
        }
        mysqli_stmt_close($stmt);

        echo json_encode(['success' => true, 'messages' => $messages]);
        break;
        
    case 'send_message':
        $message = isset($_POST['message']) ? trim($_POST['message']) : '';

        if (empty($message)) {
            echo json_encode(['success' => false, 'error' => 'Message cannot be empty']);
            exit();
        }

        // Create table if not exists (like HelloITOld)
        $create_table_sql = "CREATE TABLE IF NOT EXISTS chat_messages (
            id INT(11) AUTO_INCREMENT PRIMARY KEY,
            ticket_id INT(11) NOT NULL,
            sender_id INT(11) NOT NULL,
            sender_type ENUM('user', 'admin') NOT NULL,
            message TEXT NOT NULL,
            is_read TINYINT(1) DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )";
        mysqli_query($conn, $create_table_sql);

        // Insert admin message into chat_messages table (like HelloITOld)
        $insert_query = "INSERT INTO chat_messages (ticket_id, sender_id, sender_type, message)
                        VALUES (?, ?, 'admin', ?)";

        $stmt = mysqli_prepare($conn, $insert_query);
        mysqli_stmt_bind_param($stmt, 'iis',
            $ticket_id,
            $admin_id,
            $message
        );
        
        if (mysqli_stmt_execute($stmt)) {
            $message_id = mysqli_insert_id($conn);
            mysqli_stmt_close($stmt);

            // Try to sync to Appika API in background (optional)
            syncAdminMessageToAppika($ticket_id, $message, $ticketData);

            echo json_encode([
                'success' => true,
                'message' => [
                    'id' => $message_id,
                    'message' => $message,
                    'message_type' => 'admin',
                    'sender_name' => $admin_username,
                    'created_at' => date('Y-m-d H:i:s')
                ]
            ]);
        } else {
            $error = mysqli_stmt_error($stmt);
            mysqli_stmt_close($stmt);
            error_log("Admin message insert error: " . $error);
            echo json_encode(['success' => false, 'error' => 'Failed to save message: ' . $error]);
        }
        break;

    case 'update_status':
        $status = $_POST['status'] ?? '';

        if (!in_array($status, ['resolved', 'closed'])) {
            echo json_encode(['success' => false, 'error' => 'Invalid status']);
            exit();
        }

        // Use the exact same approach as working appika-ticket-details.php
        try {
            // Map status to API format
            $statusMapping = [
                'resolved' => 'RESOLVED',
                'closed' => 'CLOSED'
            ];
            $apiStatus = $statusMapping[$status];

            // Get current ticket data first (exactly like appika-ticket-details.php)
            $currentTicketQuery = '
            query GetTicket($id: Int!) {
                getTicket(id: $id) {
                    id
                    contact_id
                    agent_id
                    req_email
                    subject
                    type
                    type_name
                    priority
                }
            }';

            $currentTicketResult = makeGraphQLRequest($currentTicketQuery, ['id' => $ticket_id]);

            if (!$currentTicketResult['success'] || !isset($currentTicketResult['data']['data']['getTicket'])) {
                echo json_encode(['success' => false, 'error' => 'Failed to fetch current ticket data']);
                exit();
            }

            $currentTicket = $currentTicketResult['data']['data']['getTicket'];

            // Use the exact same mutation as appika-ticket-details.php
            $mutation = '
            mutation UpdateTicket(
                $id: Int!,
                $contact_id: Int,
                $agent_id: Int,
                $subject: String!,
                $type: Int!,
                $type_name: String,
                $priority: String!,
                $status: String!,
                $req_email: String,
                $time_track: String!,
                $reply_msg: String,
                $tags: String
            ) {
                updateTicket(
                    id: $id,
                    contact_id: $contact_id,
                    agent_id: $agent_id,
                    subject: $subject,
                    type: $type,
                    type_name: $type_name,
                    priority: $priority,
                    status: $status,
                    req_email: $req_email,
                    time_track: $time_track,
                    reply_msg: $reply_msg,
                    tags: $tags
                ) {
                    id
                    status
                }
            }';

            // Use exact same variable structure as appika-ticket-details.php
            $variables = [
                'id' => $ticket_id,
                'contact_id' => $currentTicket['contact_id'],
                'agent_id' => $currentTicket['agent_id'],
                'subject' => $currentTicket['subject'],
                'type' => (int)$currentTicket['type'],
                'type_name' => $currentTicket['type_name'],
                'priority' => $currentTicket['priority'],
                'status' => $apiStatus,
                'req_email' => $currentTicket['req_email'],
                'time_track' => '00:00:00',
                'reply_msg' => "Ticket status updated to $status by admin",
                'tags' => ''
            ];

            $updateResult = makeGraphQLRequest($mutation, $variables);

            // Use exact same success checking as appika-ticket-details.php
            if ($updateResult['success'] && isset($updateResult['data']['data']['updateTicket'])) {
                echo json_encode(['success' => true, 'message' => "Ticket marked as $status successfully"]);
            } else if ($updateResult['status'] == 200 && isset($updateResult['data']['data']['updateTicket'])) {
                echo json_encode(['success' => true, 'message' => "Ticket marked as $status successfully"]);
            } else {
                $error = 'Failed to update ticket status';
                if (isset($updateResult['error'])) {
                    $error .= ': ' . $updateResult['error'];
                }
                echo json_encode(['success' => false, 'error' => $error]);
            }
        } catch (Exception $e) {
            echo json_encode(['success' => false, 'error' => 'Error updating ticket: ' . $e->getMessage()]);
        }
        break;

    case 'update_priority':
        $priority = $_POST['priority'] ?? '';

        if (!in_array($priority, ['low', 'medium', 'high', 'urgent'])) {
            echo json_encode(['success' => false, 'error' => 'Invalid priority']);
            exit();
        }

        // Use the exact same approach as status update
        try {
            // Map priority to API format (same as appika-ticket-details.php)
            $priorityMapping = [
                'low' => 'LOW',
                'medium' => 'MEDIUM',
                'high' => 'HIGH',
                'urgent' => 'URGENT'
            ];
            $apiPriority = $priorityMapping[$priority];

            // Get current ticket data first (exactly like status update)
            $currentTicketQuery = '
            query GetTicket($id: Int!) {
                getTicket(id: $id) {
                    id
                    contact_id
                    agent_id
                    req_email
                    subject
                    type
                    type_name
                    priority
                    status
                }
            }';

            $currentTicketResult = makeGraphQLRequest($currentTicketQuery, ['id' => $ticket_id]);

            if (!$currentTicketResult['success'] || !isset($currentTicketResult['data']['data']['getTicket'])) {
                echo json_encode(['success' => false, 'error' => 'Failed to fetch current ticket data']);
                exit();
            }

            $currentTicket = $currentTicketResult['data']['data']['getTicket'];

            // Use the exact same mutation as status update
            $mutation = '
            mutation UpdateTicket(
                $id: Int!,
                $contact_id: Int,
                $agent_id: Int,
                $subject: String!,
                $type: Int!,
                $type_name: String,
                $priority: String!,
                $status: String!,
                $req_email: String,
                $time_track: String!,
                $reply_msg: String,
                $tags: String
            ) {
                updateTicket(
                    id: $id,
                    contact_id: $contact_id,
                    agent_id: $agent_id,
                    subject: $subject,
                    type: $type,
                    type_name: $type_name,
                    priority: $priority,
                    status: $status,
                    req_email: $req_email,
                    time_track: $time_track,
                    reply_msg: $reply_msg,
                    tags: $tags
                ) {
                    id
                    priority
                }
            }';

            // Use exact same variable structure, only changing priority
            $variables = [
                'id' => $ticket_id,
                'contact_id' => $currentTicket['contact_id'],
                'agent_id' => $currentTicket['agent_id'],
                'subject' => $currentTicket['subject'],
                'type' => (int)$currentTicket['type'],
                'type_name' => $currentTicket['type_name'],
                'priority' => $apiPriority,
                'status' => $currentTicket['status'],
                'req_email' => $currentTicket['req_email'],
                'time_track' => '00:00:00',
                'reply_msg' => "Ticket priority updated to $priority by admin",
                'tags' => ''
            ];

            $updateResult = makeGraphQLRequest($mutation, $variables);

            // Use exact same success checking as status update
            if ($updateResult['success'] && isset($updateResult['data']['data']['updateTicket'])) {
                echo json_encode(['success' => true, 'message' => "Ticket priority updated to $priority successfully"]);
            } else if ($updateResult['status'] == 200 && isset($updateResult['data']['data']['updateTicket'])) {
                echo json_encode(['success' => true, 'message' => "Ticket priority updated to $priority successfully"]);
            } else {
                $error = 'Failed to update ticket priority';
                if (isset($updateResult['error'])) {
                    $error .= ': ' . $updateResult['error'];
                }
                echo json_encode(['success' => false, 'error' => $error]);
            }
        } catch (Exception $e) {
            echo json_encode(['success' => false, 'error' => 'Error updating ticket: ' . $e->getMessage()]);
        }
        break;

    default:
        echo json_encode(['success' => false, 'error' => 'Invalid action']);
        break;
}

// Function to sync admin message to Appika API (background operation)
function syncAdminMessageToAppika($ticket_id, $message, $ticketData) {
    try {
        // Direct GraphQL mutation to Appika
        $mutation = '
        mutation updateTicket(
          $id: Int!,
          $subject: String!,
          $type: Int!,
          $priority: String!,
          $status: String!,
          $time_track: String!,
          $reply_msg: String!,
          $reply_type: String!,
          $tags: String
        ) {
          updateTicket(
            id: $id,
            subject: $subject,
            type: $type,
            priority: $priority,
            status: $status,
            time_track: $time_track,
            reply_msg: $reply_msg,
            reply_type: $reply_type,
            tags: $tags
          ) {
            id
            updated
          }
        }';

        $variables = [
            'id' => $ticket_id,
            'subject' => $ticketData['subject'],
            'type' => (int)$ticketData['type'],
            'priority' => strtoupper($ticketData['priority']),
            'status' => strtoupper($ticketData['status']),
            'time_track' => '00:00:00',
            'reply_msg' => $message,
            'reply_type' => 'admin', // Admin message type
            'tags' => ''
        ];

        $result = makeGraphQLRequest($mutation, $variables);
        
        // Update sync status in database
        global $conn;
        if ($result['success']) {
            $update_sync = "UPDATE ticket_messages 
                           SET appika_synced = 1, appika_sync_at = NOW() 
                           WHERE appika_ticket_id = ? AND message = ? AND appika_synced = 0 
                           ORDER BY created_at DESC LIMIT 1";
            $stmt = mysqli_prepare($conn, $update_sync);
            mysqli_stmt_bind_param($stmt, 'is', $ticket_id, $message);
            mysqli_stmt_execute($stmt);
            mysqli_stmt_close($stmt);
            
            error_log("Admin message synced to Appika successfully for ticket $ticket_id");
        } else {
            error_log("Failed to sync admin message to Appika for ticket $ticket_id: " . ($result['error'] ?? 'Unknown error'));
        }
    } catch (Exception $e) {
        error_log("Exception syncing admin message to Appika: " . $e->getMessage());
    }
}

} catch (Exception $e) {
    error_log("Admin chat API error: " . $e->getMessage());
    echo json_encode(['success' => false, 'error' => 'Server error: ' . $e->getMessage()]);
}
?>
