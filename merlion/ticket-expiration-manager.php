<?php
session_start();
include('../functions/server.php');
require_once('../config/ticket-expiration-config.php');
require_once('../functions/ticket-expiration-functions.php');

// Check if admin is logged in
if (!isset($_SESSION['admin_username'])) {
    header('location: admin-login');
    exit();
}

$admin_username = $_SESSION['admin_username'];
$message = '';
$message_type = '';

// Initialize system if needed
initializeTicketExpirationSystem();

// Run automatic cleanup if enabled
// TEMPORARILY DISABLED FOR DEBUGGING - Testing if cleanup is interfering
// runAutomaticCleanup();

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['update_config'])) {
        // Update configuration
        $updates = [
            'ticket_lifetime_months' => (int)$_POST['ticket_lifetime_months'],
            'ticket_lifetime_unit' => $_POST['ticket_lifetime_unit'] ?? 'months',
            'auto_cleanup_enabled' => isset($_POST['auto_cleanup_enabled']),
            'cleanup_frequency_days' => (int)$_POST['cleanup_frequency_days'],
            'cleanup_frequency_unit' => $_POST['cleanup_frequency_unit'] ?? 'days',
            'warning_period_days' => (int)$_POST['warning_period_days'],
            'warning_period_unit' => $_POST['warning_period_unit'] ?? 'days',
            'user_notifications_enabled' => isset($_POST['user_notifications_enabled']),
            'admin_notification_email' => $_POST['admin_notification_email'],
            'system_enabled' => isset($_POST['system_enabled']),
            'debug_mode' => isset($_POST['debug_mode'])
        ];

        // DEBUG: Log what we're about to save
        if (isset($_GET['debug'])) {
            echo "<div style='background: yellow; padding: 10px; margin: 10px;'>";
            echo "<h3>DEBUG: Form Submission Values</h3>";
            echo "<p>ticket_lifetime_unit: '" . ($_POST['ticket_lifetime_unit'] ?? 'NOT SET') . "'</p>";
            echo "<p>cleanup_frequency_unit: '" . ($_POST['cleanup_frequency_unit'] ?? 'NOT SET') . "'</p>";
            echo "<p>warning_period_unit: '" . ($_POST['warning_period_unit'] ?? 'NOT SET') . "'</p>";
            echo "<p>Full POST data:</p><pre>" . print_r($_POST, true) . "</pre>";
            echo "</div>";
        }
        
        $success = true;
        foreach ($updates as $key => $value) {
            if (!updateTicketExpirationConfig($key, $value)) {
                $success = false;
                break;
            }
        }
        
        if ($success) {
            // Force refresh configuration cache
            $config = getTicketExpirationConfig(true);

            $message = 'Configuration updated successfully!';
            $message_type = 'success';

            // Force page redirect to prevent form resubmission and ensure fresh data
            header("Location: " . $_SERVER['PHP_SELF'] . "?updated=" . time());
            exit;
        } else {
            $message = 'Failed to update configuration.';
            $message_type = 'error';
        }
    } elseif (isset($_POST['run_cleanup'])) {
        // Manual cleanup
        $cleanup_summary = removeExpiredTickets();
        
        if (empty($cleanup_summary)) {
            $message = 'No expired tickets found to remove.';
            $message_type = 'info';
        } else {
            $total_removed = array_sum(array_column($cleanup_summary, 'total_expired'));
            $message = "Cleanup completed! Removed {$total_removed} expired tickets from " . count($cleanup_summary) . " user(s).";
            $message_type = 'success';
        }
    } elseif (isset($_POST['update_expiration_dates'])) {
        // Update expiration dates for existing tickets using new calculation
        $config = getTicketExpirationConfig();

        // Get ALL tickets to update their expiration dates based on current settings
        $select_query = "SELECT purchase_time FROM purchasetickets";
        $select_result = mysqli_query($conn, $select_query);

        $updated_count = 0;
        if ($select_result && mysqli_num_rows($select_result) > 0) {
            while ($row = mysqli_fetch_assoc($select_result)) {
                $purchase_time = $row['purchase_time'];
                $new_expiration = getTicketExpirationDate($purchase_time);

                $update_query = "UPDATE purchasetickets
                               SET expiration_date = ?
                               WHERE purchase_time = ?";

                $stmt = mysqli_prepare($conn, $update_query);
                mysqli_stmt_bind_param($stmt, 'ss', $new_expiration, $purchase_time);

                if (mysqli_stmt_execute($stmt)) {
                    $updated_count += mysqli_stmt_affected_rows($stmt);
                }
                mysqli_stmt_close($stmt);
            }
        }

        if ($updated_count > 0) {
            $message = "Updated expiration dates for {$updated_count} ticket records using current settings ({$config['ticket_lifetime_months']} {$config['ticket_lifetime_unit']}).";
            $message_type = 'success';
        } else {
            $message = 'No tickets found that needed expiration date updates.';
            $message_type = 'info';
        }
    }
}

// Check if redirected after successful update
if (isset($_GET['updated'])) {
    $message = 'Configuration updated successfully!';
    $message_type = 'success';
}

// Get current configuration (always force refresh to ensure we get latest values)
$config = getTicketExpirationConfig(true);

// TEMP DEBUG: Force reload config multiple times to ensure fresh data
if (isset($_GET['debug'])) {
    // Try loading config multiple times
    $config = getTicketExpirationConfig(true);
    $config = getTicketExpirationConfig(true);
    $config = getTicketExpirationConfig(true);
}

// DEBUG: Add debug output to see what config is loaded
if (isset($_GET['debug'])) {
    echo "<div style='background: #f0f0f0; padding: 10px; margin: 10px; border: 1px solid #ccc;'>";
    echo "<h3>DEBUG: Configuration Values</h3>";
    echo "<pre>";
    print_r($config);
    echo "</pre>";
    echo "<p>ticket_lifetime_unit: '" . ($config['ticket_lifetime_unit'] ?? 'NOT SET') . "'</p>";
    echo "<p>cleanup_frequency_unit: '" . ($config['cleanup_frequency_unit'] ?? 'NOT SET') . "'</p>";
    echo "<p>warning_period_unit: '" . ($config['warning_period_unit'] ?? 'NOT SET') . "'</p>";

    // Test database connection and query directly
    echo "<h3>DEBUG: Direct Database Test</h3>";
    echo "<p>Database connection status: " . ($conn ? 'Connected' : 'NOT Connected') . "</p>";

    if ($conn) {
        $test_query = "SELECT config_key, config_value FROM ticket_expiration_settings WHERE config_key LIKE '%_unit'";
        $test_result = mysqli_query($conn, $test_query);
        echo "<p>Query result: " . ($test_result ? 'Success' : 'Failed') . "</p>";

        if ($test_result && mysqli_num_rows($test_result) > 0) {
            echo "<p>Direct database unit values:</p>";
            while ($row = mysqli_fetch_assoc($test_result)) {
                echo "<p>" . $row['config_key'] . " = '" . $row['config_value'] . "'</p>";
            }
        } else {
            echo "<p>No unit values found in direct query</p>";
        }

        // Test updating values directly from main form
        if (isset($_GET['fix'])) {
            echo "<h3>DEBUG: Fixing Values</h3>";
            $fix_updates = [
                'ticket_lifetime_unit' => 'minutes',
                'cleanup_frequency_unit' => 'minutes',
                'warning_period_unit' => 'minutes'
            ];

            foreach ($fix_updates as $key => $value) {
                $result = updateTicketExpirationConfig($key, $value);
                echo "<p>Update $key to '$value': " . ($result ? 'SUCCESS' : 'FAILED') . "</p>";
            }

            echo "<p><a href='?debug=1'>Refresh to see changes</a></p>";
        } else {
            echo "<p><a href='?debug=1&fix=1'>Click here to fix the values</a></p>";
        }
    }
    echo "</div>";
}

// Get statistics
// Build the correct SQL interval based on warning period unit
$warning_value = $config['warning_period_days']; // Misleading name - can be any unit
$warning_unit = $config['warning_period_unit'];

switch($warning_unit) {
    case 'minutes':
        $interval_sql = "INTERVAL ? MINUTE";
        break;
    case 'hours':
        $interval_sql = "INTERVAL ? HOUR";
        break;
    case 'days':
        $interval_sql = "INTERVAL ? DAY";
        break;
    case 'months':
        $interval_sql = "INTERVAL ? MONTH";
        break;
    default:
        $interval_sql = "INTERVAL ? DAY";
}

$stats_query = "SELECT
    COUNT(*) as total_tickets,
    SUM(remaining_tickets) as total_remaining,
    COUNT(CASE WHEN expiration_date < NOW() AND remaining_tickets > 0 THEN 1 END) as expired_records,
    SUM(CASE WHEN expiration_date < NOW() AND remaining_tickets > 0 THEN remaining_tickets ELSE 0 END) as expired_tickets,
    COUNT(CASE WHEN expiration_date BETWEEN NOW() AND DATE_ADD(NOW(), $interval_sql) AND remaining_tickets > 0 THEN 1 END) as expiring_soon_records,
    SUM(CASE WHEN expiration_date BETWEEN NOW() AND DATE_ADD(NOW(), $interval_sql) AND remaining_tickets > 0 THEN remaining_tickets ELSE 0 END) as expiring_soon_tickets
FROM purchasetickets";

$stmt = mysqli_prepare($conn, $stats_query);
mysqli_stmt_bind_param($stmt, 'ii', $warning_value, $warning_value);
mysqli_stmt_execute($stmt);
$stats = mysqli_fetch_assoc(mysqli_stmt_get_result($stmt));
mysqli_stmt_close($stmt);

// Get recent expiration log
$log_query = "SELECT * FROM ticket_expiration_log ORDER BY cleanup_date DESC LIMIT 10";
$log_result = mysqli_query($conn, $log_query);
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ticket Expiration Manager - Admin Panel</title>
    <link rel="shortcut icon" href="../image/png/favicon.png" type="image/x-icon">
    <!-- Bootstrap, fonts & icons -->
    <link rel="stylesheet" href="../css/bootstrap.css">
    <link rel="stylesheet" href="../fonts/icon-font/css/style.css">
    <link rel="stylesheet" href="../fonts/typography-font/typo.css">
    <link rel="stylesheet" href="../fonts/fontawesome-5/css/all.css">
    <!-- Vendor stylesheets -->
    <link rel="stylesheet" href="../css/main.css">
    <style>
    body {
        background-color: #f8f9fa;
    }

    .admin-container {
        padding: 20px;
        max-width: 100%;
        overflow-x: hidden;
    }

    .admin-header {
        background-color: #fff;
        padding: 15px 20px;
        border-radius: 5px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
        width: 100%;
    }

    .admin-header h1 {
        margin: 0;
        font-size: 24px;
        color: #333;
        transition: all 0.3s ease;
    }

    .admin-user {
        display: flex;
        align-items: center;
    }

    .user-dropdown {
        position: relative;
        display: inline-block;
    }

    .user-info {
        margin-right: 10px;
        font-size: 14px;
        cursor: pointer;
        display: flex;
        align-items: center;
    }

    .mobile-menu-toggle {
        margin-left: 5px;
        transition: transform 0.3s ease;
        display: none;
    }

    .mobile-menu-toggle.active {
        transform: rotate(180deg);
    }

    .dropdown-content {
        display: block;
    }

    /* Tablet Styles */
    @media (max-width: 991px) {
        .admin-header {
            padding: 12px 15px;
        }

        .admin-header h1 {
            font-size: 22px;
        }

        .user-info {
            font-size: 13px;
        }
    }

    /* Mobile Styles */
    @media (max-width: 767px) {
        .admin-header {
            flex-direction: column;
            align-items: flex-start;
            padding: 12px;
        }

        .admin-header h1 {
            font-size: 20px;
            margin-bottom: 10px;
        }

        .admin-user {
            width: 100%;
        }

        .user-dropdown {
            width: 100%;
        }

        .user-info {
            font-size: 16px;
            padding: 8px 0;
            display: flex;
            justify-content: space-between;
            width: 100%;
        }

        .mobile-menu-toggle {
            display: inline-block;
        }

        .dropdown-content {
            display: none;
            position: absolute;
            background-color: #f9f9f9;
            width: 100%;
            left: 0;
            box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.2);
            z-index: 1;
            border-radius: 4px;
            margin-top: 5px;
            padding: 8px;
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease, padding 0.3s ease;
        }

        .dropdown-content .btn {
            width: 100%;
            text-align: center;
        }

        .user-dropdown.active .dropdown-content {
            display: block;
            max-height: 200px;
        }
    }

    /* Small Mobile Styles */
    @media (max-width: 480px) {
        .admin-header h1 {
            font-size: 22px;
        }
    }

    .admin-content {
        background-color: #fff;
        border-radius: 5px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        padding: 20px;
        min-height: calc(100vh - 120px);
        overflow-x: hidden;
    }

    .dashboard-card {
        background-color: #fff;
        border-radius: 5px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        padding: 20px;
        margin-bottom: 20px;
        text-align: center;
        width: 100%;
    }

    /* Responsive dashboard cards */
    @media (max-width: 991px) {
        .dashboard-card {
            padding: 15px;
        }

        .dashboard-card .number {
            font-size: 30px !important;
        }
    }

    @media (max-width: 767px) {
        .dashboard-card {
            padding: 12px;
            margin-bottom: 15px;
        }

        .dashboard-card .number {
            font-size: 24px !important;
            margin: 8px 0;
        }

        .dashboard-card h3 {
            font-size: 14px !important;
        }

        .dashboard-card .btn {
            font-size: 12px;
            padding: 0.25rem 0.5rem;
        }
    }

    .dashboard-card h3 {
        margin: 0;
        font-size: 16px;
        color: #666;
    }

    .dashboard-card .number {
        font-size: 36px;
        font-weight: 600;
        color: #473BF0;
        margin: 10px 0;
    }

    .dashboard-card.tickets {
        border-top: 3px solid #473BF0;
    }

    .dashboard-card.open-tickets {
        border-top: 3px solid #4B0082;
    }

    .dashboard-card.users {
        border-top: 3px solid #014421;
    }

    .dashboard-card.purchases {
        border-top: 3px solid #dc3545;
    }

    .recent-section {
        margin-top: 30px;
    }

    .recent-section h2 {
        font-size: 20px;
        margin-bottom: 15px;
        color: #333;
    }

    .table th {
        background-color: #f8f9fa;
    }

    .table-responsive {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
        margin-bottom: 15px;
    }

    .table {
        width: 100%;
        margin-bottom: 1rem;
    }

    /* Table cell styles */
    .table td,
    .table th {
        vertical-align: middle;
        padding: 0.75rem;
    }

    .badge {
        font-size: 16px;
        padding: 6.8px;
    }

    /* Responsive table styles */
    @media (max-width: 991px) {
        .table {
            font-size: 14px;
        }

        .table td,
        .table th {
            padding: 0.5rem;
        }

        .badge {
            font-size: 14px;
            padding: 5px;
        }
    }

    @media (max-width: 767px) {
        .table {
            font-size: 13px;
        }

        .table td,
        .table th {
            padding: 0.4rem 0.3rem;
        }

        .badge {
            font-size: 12px;
            padding: 4px;
        }

        .table-responsive {
            margin: 0 -15px;
            width: calc(100% + 30px);
            max-width: none;
        }
    }

    @media (max-width: 480px) {
        .table {
            font-size: 12px;
        }

        .table td,
        .table th {
            padding: 0.3rem 0.2rem;
        }
    }

    /* Card styles */
    .card {
        border: none;
        border-radius: 5px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
    }

    .card-header {
        background-color: #473BF0;
        color: white !important;
        border-radius: 5px 5px 0 0 !important;
        padding: 15px 20px;
        font-weight: 600;
    }

    .card-header h5 {
        color: white !important;
        margin: 0;
    }

    .stat-card {
        text-align: center;
        padding: 20px;
    }

    .stat-number {
        font-size: 2rem;
        font-weight: bold;
        color: #473BF0;
    }

    .stat-label {
        color: #6c757d;
        font-size: 0.9rem;
    }

    .alert {
        border-radius: 5px;
        border: none;
    }

    .btn-primary {
        background-color: #473BF0;
        border-color: #473BF0;
        border-radius: 5px;
    }

    .btn-danger {
        background-color: #dc3545;
        border-color: #dc3545;
        border-radius: 5px;
    }

    .btn-warning {
        background-color: #ffc107;
        border-color: #ffc107;
        border-radius: 5px;
        color: #000;
    }

    .btn-secondary {
        background-color: #6c757d;
        border-color: #6c757d;
        border-radius: 5px;
    }

    .form-control {
        border-radius: 5px;
        border: 1px solid #dee2e6;
    }

    .input-group .form-control:first-child {
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
    }

    .input-group .form-control:last-child {
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;
    }

    .input-group-append .form-control {
        border-left: 0;
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;
        border-top-right-radius: 5px;
        border-bottom-right-radius: 5px;
        min-width: 120px;
        height: 38px;
        line-height: 1.5;
        padding: 0.375rem 0.75rem;
        font-size: 1rem;
        display: flex;
        align-items: center;
    }

    .input-group .form-control {
        height: 38px;
        line-height: 1.5;
        padding: 0.375rem 0.75rem;
        font-size: 1rem;
    }

    .input-group-append select.form-control {
        padding-right: 2rem;
        background-position: right 0.75rem center;
        background-size: 16px 12px;
        font-family: inherit;
        font-weight: 400;
        color: #495057;
        vertical-align: middle;
        -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none;
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
        background-repeat: no-repeat;
    }

    .input-group-append select.form-control option {
        padding: 0.375rem 0.75rem;
        font-size: 1rem;
        line-height: 1.5;
    }

    .badge-expired {
        background-color: #dc3545;
    }

    .badge-warning {
        background-color: #ffc107;
        color: #000;
    }

    .badge-success {
        background-color: #28a745;
    }

    .badge-secondary {
        background-color: #6c757d;
    }

    /* Ticket type badges */
    .badge-starter {
        background-color: #fbbf24 !important;
        color: #fff !important;
    }

    .badge-premium {
        background-color: #01A7E1 !important;
        color: #fff !important;
    }

    .badge-ultimate {
        background-color: #793BF0 !important;
        color: #fff !important;
    }
    </style>
</head>

<body>
    <div class="admin-container">
        <!-- Header -->
        <div class="admin-header">
            <h1><i class="fas fa-clock"></i> Ticket Expiration Manager</h1>
            <div class="admin-user">
                <div class="user-dropdown">
                    <div class="user-info">
                        Welcome, <?php echo htmlspecialchars($admin_username); ?> (admin)
                        <span class="mobile-menu-toggle"><i class="fa fa-chevron-down"></i></span>
                    </div>
                    
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Sidebar -->
            <div class="col-lg-3 col-md-4">
                <?php include('admin-menu.php'); ?>
            </div>

            <!-- Main Content -->
            <div class="col-lg-9 col-md-8">
                <div class="admin-content">

                    <!-- Messages -->
                    <?php if ($message): ?>
                    <div class="alert alert-<?php echo $message_type === 'error' ? 'danger' : $message_type; ?> alert-dismissible fade show">
                        <?php echo htmlspecialchars($message); ?>
                        <button type="button" class="close" data-dismiss="alert">
                            <span>&times;</span>
                        </button>
                    </div>
                    <?php endif; ?>

                    <!-- Statistics -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="dashboard-card">
                                <div class="number"><?php echo number_format($stats['total_remaining'] ?? 0); ?></div>
                                <h3>Active Tickets</h3>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="dashboard-card">
                                <div class="number text-danger"><?php echo number_format($stats['expired_tickets'] ?? 0); ?></div>
                                <h3>Expired Tickets</h3>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="dashboard-card">
                                <div class="number text-warning"><?php echo number_format($stats['expiring_soon_tickets'] ?? 0); ?></div>
                                <h3>Expiring Soon</h3>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="dashboard-card">
                                <div class="number"><?php echo $config['ticket_lifetime_months']; ?></div>
                                <h3><?php echo ucfirst($config['ticket_lifetime_unit'] ?? 'months'); ?> Lifetime</h3>
                            </div>
                        </div>
                    </div>

                    <!-- Configuration Form -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-cog"></i> Expiration Settings</h5>
                        </div>
                        <div class="card-body">
                            <form method="POST">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="ticket_lifetime_months">Ticket Lifetime</label>
                                            <div class="input-group">
                                                <input type="number" class="form-control" id="ticket_lifetime_months"
                                                       name="ticket_lifetime_months" value="<?php echo $config['ticket_lifetime_months']; ?>"
                                                       min="1" max="60" required>
                                                <div class="input-group-append">
                                                    <!-- DEBUG: Show what unit value we're checking -->
                                                    <?php if (isset($_GET['debug'])): ?>
                                                        <small style="display:block; color: red;">DEBUG: ticket_lifetime_unit = '<?php echo $config['ticket_lifetime_unit'] ?? 'NOT SET'; ?>'</small>
                                                    <?php endif; ?>
                                                    <select class="form-control" name="ticket_lifetime_unit">
                                                        <option value="months" <?php echo ($config['ticket_lifetime_unit'] ?? 'months') == 'months' ? 'selected' : ''; ?>>Months</option>
                                                        <option value="days" <?php echo ($config['ticket_lifetime_unit'] ?? 'months') == 'days' ? 'selected' : ''; ?>>Days</option>
                                                        <option value="hours" <?php echo ($config['ticket_lifetime_unit'] ?? 'months') == 'hours' ? 'selected' : ''; ?>>Hours</option>
                                                        <option value="minutes" <?php echo ($config['ticket_lifetime_unit'] ?? 'months') == 'minutes' ? 'selected' : ''; ?>>Minutes</option>
                                                    </select>
                                                </div>
                                            </div>
                                            <small class="form-text text-muted">How long tickets remain valid after purchase</small>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="warning_period_days">Warning Period</label>
                                            <div class="input-group">
                                                <input type="number" class="form-control" id="warning_period_days"
                                                       name="warning_period_days" value="<?php echo $config['warning_period_days']; ?>"
                                                       min="1" max="365" required>
                                                <div class="input-group-append">
                                                    <select class="form-control" name="warning_period_unit">
                                                        <option value="days" <?php echo ($config['warning_period_unit'] ?? 'days') == 'days' ? 'selected' : ''; ?>>Days</option>
                                                        <option value="months" <?php echo ($config['warning_period_unit'] ?? 'days') == 'months' ? 'selected' : ''; ?>>Months</option>
                                                        <option value="hours" <?php echo ($config['warning_period_unit'] ?? 'days') == 'hours' ? 'selected' : ''; ?>>Hours</option>
                                                        <option value="minutes" <?php echo ($config['warning_period_unit'] ?? 'days') == 'minutes' ? 'selected' : ''; ?>>Minutes</option>
                                                    </select>
                                                </div>
                                            </div>
                                            <small class="form-text text-muted">Time before expiration to warn users</small>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="cleanup_frequency_days">Cleanup Frequency</label>
                                            <div class="input-group">
                                                <input type="number" class="form-control" id="cleanup_frequency_days"
                                                       name="cleanup_frequency_days" value="<?php echo $config['cleanup_frequency_days']; ?>"
                                                       min="1" max="30" required>
                                                <div class="input-group-append">
                                                    <select class="form-control" name="cleanup_frequency_unit">
                                                        <option value="days" <?php echo ($config['cleanup_frequency_unit'] ?? 'days') == 'days' ? 'selected' : ''; ?>>Days</option>
                                                        <option value="months" <?php echo ($config['cleanup_frequency_unit'] ?? 'days') == 'months' ? 'selected' : ''; ?>>Months</option>
                                                        <option value="hours" <?php echo ($config['cleanup_frequency_unit'] ?? 'days') == 'hours' ? 'selected' : ''; ?>>Hours</option>
                                                        <option value="minutes" <?php echo ($config['cleanup_frequency_unit'] ?? 'days') == 'minutes' ? 'selected' : ''; ?>>Minutes</option>
                                                    </select>
                                                </div>
                                            </div>
                                            <small class="form-text text-muted">How often to automatically remove expired tickets</small>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="admin_notification_email">Admin Email</label>
                                            <input type="email" class="form-control" id="admin_notification_email"
                                                   name="admin_notification_email" value="<?php echo htmlspecialchars($config['admin_notification_email']); ?>">
                                            <small class="form-text text-muted">Email for expiration reports</small>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="form-check mb-2">
                                            <input type="checkbox" class="form-check-input" id="system_enabled"
                                                   name="system_enabled" <?php echo $config['system_enabled'] ? 'checked' : ''; ?>>
                                            <label class="form-check-label" for="system_enabled">
                                                Enable Expiration System
                                            </label>
                                        </div>

                                        <div class="form-check mb-2">
                                            <input type="checkbox" class="form-check-input" id="auto_cleanup_enabled"
                                                   name="auto_cleanup_enabled" <?php echo $config['auto_cleanup_enabled'] ? 'checked' : ''; ?>>
                                            <label class="form-check-label" for="auto_cleanup_enabled">
                                                Enable Automatic Cleanup
                                            </label>
                                        </div>

                                        <div class="form-check mb-2">
                                            <input type="checkbox" class="form-check-input" id="user_notifications_enabled"
                                                   name="user_notifications_enabled" <?php echo $config['user_notifications_enabled'] ? 'checked' : ''; ?>>
                                            <label class="form-check-label" for="user_notifications_enabled">
                                                Enable User Notifications
                                            </label>
                                        </div>

                                        <div class="form-check mb-3">
                                            <input type="checkbox" class="form-check-input" id="debug_mode"
                                                   name="debug_mode" <?php echo $config['debug_mode'] ? 'checked' : ''; ?>>
                                            <label class="form-check-label" for="debug_mode">
                                                Debug Mode
                                            </label>
                                        </div>
                                    </div>
                                </div>

                                <div class="d-flex justify-content-between">
                                    <button type="submit" name="update_config" class="btn btn-primary">
                                        <i class="fas fa-save"></i>&nbsp;Save Configuration
                                    </button>

                                    <div>
                                        <button type="submit" name="update_expiration_dates" class="btn btn-warning mr-2"
                                                onclick="return confirm('This will update expiration dates for all existing tickets. Continue?')">
                                            <i class="fas fa-sync"></i>&nbsp;Update Existing Dates
                                        </button>

                                        <button type="submit" name="run_cleanup" class="btn btn-danger"
                                                onclick="return confirm('This will permanently remove all expired tickets. Continue?')">
                                            <i class="fas fa-trash"></i>&nbsp;Run Cleanup Now
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Recent Expiration Log -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-history"></i> Recent Expiration Log</h5>
                        </div>
                        <div class="card-body">
                            <?php if (mysqli_num_rows($log_result) > 0): ?>
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>Date</th>
                                            <th>Username</th>
                                            <th>Ticket Type</th>
                                            <th>Expired Quantity</th>
                                            <th>Purchase Date</th>
                                            <th>Transaction ID</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php while ($log = mysqli_fetch_assoc($log_result)): ?>
                                        <tr>
                                            <td><?php echo date('M d, Y H:i', strtotime($log['cleanup_date'])); ?></td>
                                            <td><?php echo htmlspecialchars($log['username']); ?></td>
                                            <td>
                                                <?php
                                                $ticket_type = strtolower($log['ticket_type']);
                                                $badge_class = 'badge-secondary';
                                                $display_text = ucfirst($log['ticket_type']);

                                                switch ($ticket_type) {
                                                    case 'starter':
                                                        $badge_class = 'badge-starter';
                                                        $display_text = 'Starter';
                                                        break;
                                                    case 'premium':
                                                        $badge_class = 'badge-premium';
                                                        $display_text = 'Business';
                                                        break;
                                                    case 'ultimate':
                                                        $badge_class = 'badge-ultimate';
                                                        $display_text = 'Ultimate';
                                                        break;
                                                }
                                                ?>
                                                <span class="badge <?php echo $badge_class; ?>">
                                                    <?php echo $display_text; ?>
                                                </span>
                                            </td>
                                            <td><?php echo $log['expired_quantity']; ?></td>
                                            <td><?php echo date('M d, Y', strtotime($log['purchase_date'])); ?></td>
                                            <td><?php echo htmlspecialchars($log['transaction_id'] ?? 'N/A'); ?></td>
                                        </tr>
                                        <?php endwhile; ?>
                                    </tbody>
                                </table>
                            </div>
                            <?php else: ?>
                            <p class="text-muted">No expiration log entries found.</p>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Vendor Scripts -->
    <script src="../js/vendor.min.js"></script>

    <script>
    // Mobile dropdown functionality
    document.addEventListener('DOMContentLoaded', function() {
        const userDropdown = document.querySelector('.user-dropdown');
        const userInfo = document.querySelector('.user-info');
        const mobileToggle = document.querySelector('.mobile-menu-toggle');

        if (userInfo && mobileToggle) {
            userInfo.addEventListener('click', function() {
                userDropdown.classList.toggle('active');
                mobileToggle.classList.toggle('active');
            });
        }
    });
    </script>
</body>

</html>
