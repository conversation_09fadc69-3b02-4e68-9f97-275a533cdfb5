<?php
// Simple Admin Chat Test - Minimal version to test basic functionality
error_reporting(E_ALL);
ini_set('display_errors', 1);

session_start();

// Basic database connection test
try {
    include('../functions/server.php');
    $db_connected = isset($conn) && $conn;
} catch (Exception $e) {
    $db_connected = false;
    $db_error = $e->getMessage();
}

// Check admin login
$admin_logged_in = isset($_SESSION['admin_username']);

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Admin Chat Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        .container { max-width: 800px; margin: 0 auto; }
        .status-box { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .status-success { background: #d4edda; border: 1px solid #c3e6cb; }
        .status-error { background: #f8d7da; border: 1px solid #f5c6cb; }
        .status-warning { background: #fff3cd; border: 1px solid #ffeaa7; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Simple Admin Chat Test</h1>
        
        <h2>System Status</h2>
        
        <!-- Database Connection Status -->
        <div class="status-box <?php echo $db_connected ? 'status-success' : 'status-error'; ?>">
            <strong>Database Connection:</strong>
            <?php if ($db_connected): ?>
                <span class="success">✓ Connected successfully</span>
            <?php else: ?>
                <span class="error">✗ Connection failed</span>
                <?php if (isset($db_error)): ?>
                    <br>Error: <?php echo htmlspecialchars($db_error); ?>
                <?php endif; ?>
            <?php endif; ?>
        </div>
        
        <!-- Admin Login Status -->
        <div class="status-box <?php echo $admin_logged_in ? 'status-success' : 'status-warning'; ?>">
            <strong>Admin Login:</strong>
            <?php if ($admin_logged_in): ?>
                <span class="success">✓ Logged in as: <?php echo htmlspecialchars($_SESSION['admin_username']); ?></span>
            <?php else: ?>
                <span class="warning">⚠ Not logged in</span>
                <br><a href="admin-login.php">Click here to login</a>
            <?php endif; ?>
        </div>
        
        <?php if ($db_connected): ?>
        <!-- Database Tables Check -->
        <div class="status-box">
            <strong>Required Tables:</strong><br>
            <?php
            $required_tables = ['user', 'support_tickets', 'admin_users', 'chat_messages'];
            foreach ($required_tables as $table) {
                $check_query = "SHOW TABLES LIKE '$table'";
                $result = mysqli_query($conn, $check_query);
                echo "<span style='display: inline-block; width: 150px;'>$table:</span>";
                if ($result && mysqli_num_rows($result) > 0) {
                    echo "<span class='success'>✓ Exists</span><br>";
                } else {
                    echo "<span class='error'>✗ Missing</span><br>";
                }
            }
            ?>
        </div>
        
        <?php if ($admin_logged_in): ?>
        <!-- Simple Chat Interface -->
        <div class="status-box status-success">
            <h3>Basic Chat Functionality Test</h3>
            
            <!-- Users with Tickets -->
            <h4>Users with Support Tickets:</h4>
            <?php
            try {
                $users_query = "SELECT DISTINCT u.id, u.username, u.email 
                               FROM user u 
                               JOIN support_tickets st ON u.id = st.user_id 
                               ORDER BY u.username ASC 
                               LIMIT 10";
                $users_result = mysqli_query($conn, $users_query);
                
                if ($users_result && mysqli_num_rows($users_result) > 0) {
                    echo "<ul>";
                    while ($user = mysqli_fetch_assoc($users_result)) {
                        echo "<li>";
                        echo "<strong>" . htmlspecialchars($user['username']) . "</strong> ";
                        echo "(" . htmlspecialchars($user['email']) . ") ";
                        echo "<a href='admin-chat.php?user_id=" . $user['id'] . "'>Open Chat</a>";
                        echo "</li>";
                    }
                    echo "</ul>";
                } else {
                    echo "<p class='warning'>No users with support tickets found.</p>";
                }
            } catch (Exception $e) {
                echo "<p class='error'>Error loading users: " . htmlspecialchars($e->getMessage()) . "</p>";
            }
            ?>
            
            <!-- Recent Chat Messages -->
            <h4>Recent Chat Messages (Last 5):</h4>
            <?php
            try {
                // Create chat_messages table if it doesn't exist
                $create_table_sql = "CREATE TABLE IF NOT EXISTS chat_messages (
                    id INT(11) AUTO_INCREMENT PRIMARY KEY,
                    ticket_id INT(11) NOT NULL,
                    sender_id INT(11) NOT NULL,
                    sender_type ENUM('user', 'admin') NOT NULL,
                    message TEXT NOT NULL,
                    is_read TINYINT(1) DEFAULT 0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )";
                mysqli_query($conn, $create_table_sql);
                
                $messages_query = "SELECT cm.*, 
                                  CASE 
                                      WHEN cm.sender_type = 'admin' THEN a.username 
                                      WHEN cm.sender_type = 'user' THEN u.username 
                                  END as sender_name
                                  FROM chat_messages cm
                                  LEFT JOIN admin_users a ON cm.sender_id = a.id AND cm.sender_type = 'admin'
                                  LEFT JOIN user u ON cm.sender_id = u.id AND cm.sender_type = 'user'
                                  ORDER BY cm.created_at DESC 
                                  LIMIT 5";
                $messages_result = mysqli_query($conn, $messages_query);
                
                if ($messages_result && mysqli_num_rows($messages_result) > 0) {
                    echo "<ul>";
                    while ($message = mysqli_fetch_assoc($messages_result)) {
                        echo "<li>";
                        echo "<strong>" . htmlspecialchars($message['sender_name']) . "</strong> ";
                        echo "(" . $message['sender_type'] . "): ";
                        echo htmlspecialchars(substr($message['message'], 0, 100));
                        if (strlen($message['message']) > 100) echo "...";
                        echo " <small>(" . $message['created_at'] . ")</small>";
                        echo "</li>";
                    }
                    echo "</ul>";
                } else {
                    echo "<p class='warning'>No chat messages found.</p>";
                }
            } catch (Exception $e) {
                echo "<p class='error'>Error loading messages: " . htmlspecialchars($e->getMessage()) . "</p>";
            }
            ?>
        </div>
        <?php endif; ?>
        <?php endif; ?>
        
        <h2>Test Links</h2>
        <ul>
            <li><a href="admin-chat.php">Try Full Admin Chat</a></li>
            <li><a href="chat-diagnostic.php">Run Diagnostic Script</a></li>
            <li><a href="admin-login.php">Admin Login</a></li>
            <li><a href="../">Back to Main Site</a></li>
        </ul>
        
        <h2>Server Information</h2>
        <div class="status-box">
            <strong>PHP Version:</strong> <?php echo phpversion(); ?><br>
            <strong>Server Software:</strong> <?php echo $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown'; ?><br>
            <strong>Document Root:</strong> <?php echo $_SERVER['DOCUMENT_ROOT'] ?? 'Unknown'; ?><br>
            <strong>Current Script:</strong> <?php echo $_SERVER['SCRIPT_NAME'] ?? 'Unknown'; ?><br>
            <strong>Request URI:</strong> <?php echo $_SERVER['REQUEST_URI'] ?? 'Unknown'; ?><br>
        </div>
    </div>
</body>
</html>
