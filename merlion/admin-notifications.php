<?php
// Include database connection
include('../functions/server.php');

// Check if admin is logged in
session_start();
if (!isset($_SESSION['admin_id'])) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'error' => 'Not logged in']);
    exit();
}

$admin_id = $_SESSION['admin_id'];

// admin_notifications table removed - using existing notification systems

// Count only unseen tickets for notification
$new_tickets_sql = "SELECT COUNT(*) as cnt FROM support_tickets WHERE is_seen_by_admin=0";
$new_tickets_result = mysqli_query($conn, $new_tickets_sql);
$new_tickets_count = mysqli_fetch_assoc($new_tickets_result)['cnt'];

// Count unread messages from users
$unread_messages_query = "SELECT COUNT(*) as count FROM chat_messages WHERE sender_type = 'user' AND is_read = 0";
$unread_messages_result = mysqli_query($conn, $unread_messages_query);
$unread_messages_count = mysqli_fetch_assoc($unread_messages_result)['count'];

// Total notification count
$total_notifications = $new_tickets_count + $unread_messages_count;

// Return data as JSON
header('Content-Type: application/json');
echo json_encode([
    'success' => true,
    'new_tickets_count' => (int)$new_tickets_count,
    'unread_messages_count' => (int)$unread_messages_count
]);
?>