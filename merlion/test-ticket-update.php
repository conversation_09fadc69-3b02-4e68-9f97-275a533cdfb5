<?php
/**
 * Test Ticket Update - Debug <PERSON><PERSON><PERSON>
 * This script tests if ticket updates are persisting in the database
 */

session_start();
include('../functions/server.php');

// Check if admin is logged in
if (!isset($_SESSION['admin_username'])) {
    header('location: admin-login.php');
    exit();
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Ticket Update</title>
    <link rel="stylesheet" href="../css/bootstrap.css">
    <style>
        body { background-color: #f8f9fa; padding: 20px; }
        .container { max-width: 800px; }
        .test-result { background: white; padding: 15px; margin: 10px 0; border-radius: 5px; }
        .success { border-left: 4px solid #28a745; }
        .error { border-left: 4px solid #dc3545; }
        .info { border-left: 4px solid #007bff; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Test Ticket Update</h1>
        
        <?php
        // Test user ID (HC200 = user ID 200)
        $test_user_id = 200;
        
        echo "<div class='test-result info'>";
        echo "<h4>Testing User ID: $test_user_id (HC200)</h4>";
        echo "</div>";
        
        // 1. Check current ticket count
        echo "<div class='test-result'>";
        echo "<h5>1. Current Ticket Count:</h5>";
        $current_query = "SELECT id, username, starter_tickets, premium_tickets, ultimate_tickets FROM user WHERE id = $test_user_id";
        $current_result = mysqli_query($conn, $current_query);
        
        if ($current_result && mysqli_num_rows($current_result) > 0) {
            $user = mysqli_fetch_assoc($current_result);
            echo "<p><strong>Username:</strong> {$user['username']}</p>";
            echo "<p><strong>Starter Tickets:</strong> {$user['starter_tickets']}</p>";
            echo "<p><strong>Premium Tickets:</strong> {$user['premium_tickets']}</p>";
            echo "<p><strong>Ultimate Tickets:</strong> {$user['ultimate_tickets']}</p>";
            
            $original_starter = $user['starter_tickets'];
        } else {
            echo "<p class='text-danger'>❌ User not found!</p>";
            exit;
        }
        echo "</div>";
        
        // 2. Test manual update
        if (isset($_POST['test_update'])) {
            echo "<div class='test-result'>";
            echo "<h5>2. Testing Manual Update:</h5>";
            
            $add_amount = 1;
            $update_sql = "UPDATE user SET starter_tickets = starter_tickets + $add_amount WHERE id = $test_user_id";
            echo "<p><strong>SQL:</strong> $update_sql</p>";
            
            if (mysqli_query($conn, $update_sql)) {
                $affected = mysqli_affected_rows($conn);
                echo "<p class='text-success'>✅ Update executed successfully</p>";
                echo "<p><strong>Affected rows:</strong> $affected</p>";
                
                // Check new count
                $check_result = mysqli_query($conn, $current_query);
                $updated_user = mysqli_fetch_assoc($check_result);
                echo "<p><strong>New starter tickets:</strong> {$updated_user['starter_tickets']}</p>";
                echo "<p><strong>Expected:</strong> " . ($original_starter + $add_amount) . "</p>";
                
                if ($updated_user['starter_tickets'] == ($original_starter + $add_amount)) {
                    echo "<p class='text-success'>✅ Update successful!</p>";
                } else {
                    echo "<p class='text-danger'>❌ Update failed - count doesn't match expected value</p>";
                }
            } else {
                echo "<p class='text-danger'>❌ Update failed: " . mysqli_error($conn) . "</p>";
            }
            echo "</div>";
        }
        
        // 3. Check purchase records
        echo "<div class='test-result'>";
        echo "<h5>3. Purchase Records for HC200:</h5>";
        $purchase_query = "SELECT * FROM purchasetickets WHERE username = 'HC200' ORDER BY purchase_time DESC LIMIT 5";
        $purchase_result = mysqli_query($conn, $purchase_query);
        
        if ($purchase_result && mysqli_num_rows($purchase_result) > 0) {
            echo "<table class='table table-sm'>";
            echo "<tr><th>Type</th><th>Quantity</th><th>Remaining</th><th>Purchase Time</th><th>Expiration</th><th>Transaction ID</th></tr>";
            while ($purchase = mysqli_fetch_assoc($purchase_result)) {
                echo "<tr>";
                echo "<td>{$purchase['ticket_type']}</td>";
                echo "<td>{$purchase['quantity']}</td>";
                echo "<td>{$purchase['remaining_tickets']}</td>";
                echo "<td>{$purchase['purchase_time']}</td>";
                echo "<td>{$purchase['expiration_date']}</td>";
                echo "<td>{$purchase['transaction_id']}</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p class='text-warning'>⚠️ No purchase records found for HC200</p>";
        }
        echo "</div>";
        
        // 4. Check if cleanup is running
        echo "<div class='test-result'>";
        echo "<h5>4. Check Recent Cleanup Activity:</h5>";
        
        // Check ticket expiration log
        $cleanup_query = "SELECT * FROM ticket_expiration_log WHERE username = 'HC200' ORDER BY cleanup_date DESC LIMIT 3";
        $cleanup_result = mysqli_query($conn, $cleanup_query);
        
        if ($cleanup_result && mysqli_num_rows($cleanup_result) > 0) {
            echo "<p class='text-danger'>⚠️ Recent cleanup activity found:</p>";
            echo "<table class='table table-sm'>";
            echo "<tr><th>Type</th><th>Expired Qty</th><th>Purchase Date</th><th>Cleanup Date</th></tr>";
            while ($cleanup = mysqli_fetch_assoc($cleanup_result)) {
                echo "<tr>";
                echo "<td>{$cleanup['ticket_type']}</td>";
                echo "<td>{$cleanup['expired_quantity']}</td>";
                echo "<td>{$cleanup['purchase_date']}</td>";
                echo "<td>{$cleanup['cleanup_date']}</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p class='text-success'>✅ No recent cleanup activity for HC200</p>";
        }
        echo "</div>";
        ?>
        
        <div class="mt-4">
            <form method="POST">
                <button type="submit" name="test_update" class="btn btn-primary">🧪 Test Manual Update (+1 Starter Ticket)</button>
            </form>
            <a href="admin-users.php" class="btn btn-secondary mt-2">← Back to Users</a>
            <button onclick="window.location.reload()" class="btn btn-info mt-2">🔄 Refresh</button>

            <div class="alert alert-info mt-3">
                <h5>🔧 Cleanup Status:</h5>
                <p><strong>Auto Cleanup:</strong> DISABLED (for debugging)</p>
                <p><strong>Files Modified:</strong> admin-tickets.php, ticket-expiration-manager.php, my-ticket.php, config</p>
                <p><strong>Expected Result:</strong> Tickets should now persist after adding</p>
            </div>
        </div>
    </div>
</body>
</html>
