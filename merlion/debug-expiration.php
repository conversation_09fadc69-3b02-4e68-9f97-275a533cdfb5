<?php
// Debug script to check ticket expiration
include_once('../functions/server.php');
include_once('../config/ticket-expiration-config.php');

echo "<h2>Ticket Expiration Debug</h2>";

// Get current configuration
$config = getTicketExpirationConfig();
echo "<h3>Current Configuration:</h3>";
echo "<p>Ticket Lifetime: " . $config['ticket_lifetime_months'] . " " . $config['ticket_lifetime_unit'] . "</p>";
echo "<p>Warning Period: " . $config['warning_period_days'] . " " . $config['warning_period_unit'] . "</p>";
echo "<p>Cleanup Frequency: " . $config['cleanup_frequency_days'] . " " . $config['cleanup_frequency_unit'] . "</p>";
echo "<p>System Enabled: " . ($config['system_enabled'] ? 'Yes' : 'No') . "</p>";
echo "<p>Auto Cleanup Enabled: " . ($config['auto_cleanup_enabled'] ? 'Yes' : 'No') . "</p>";

// Check recent purchases
echo "<h3>Recent Ticket Purchases (Last 10):</h3>";
$recent_query = "SELECT username, ticket_type, remaining_tickets, purchase_time, expiration_date, 
                 TIMESTAMPDIFF(SECOND, NOW(), expiration_date) as seconds_until_expiry,
                 CASE 
                   WHEN expiration_date < NOW() THEN 'EXPIRED'
                   WHEN expiration_date BETWEEN NOW() AND DATE_ADD(NOW(), INTERVAL ? MINUTE) THEN 'EXPIRING_SOON'
                   ELSE 'ACTIVE'
                 END as status
                 FROM purchasetickets 
                 ORDER BY purchase_time DESC 
                 LIMIT 10";

$stmt = mysqli_prepare($conn, $recent_query);
$warning_minutes = $config['warning_period_days']; // This should be 1 minute based on your config
mysqli_stmt_bind_param($stmt, 'i', $warning_minutes);
mysqli_stmt_execute($stmt);
$result = mysqli_stmt_get_result($stmt);

if (mysqli_num_rows($result) > 0) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>Username</th><th>Type</th><th>Remaining</th><th>Purchase Time</th><th>Expiration Date</th><th>Seconds Until Expiry</th><th>Status</th></tr>";
    
    while ($row = mysqli_fetch_assoc($result)) {
        $status_color = '';
        switch($row['status']) {
            case 'EXPIRED': $status_color = 'background-color: #ffcccc;'; break;
            case 'EXPIRING_SOON': $status_color = 'background-color: #ffffcc;'; break;
            case 'ACTIVE': $status_color = 'background-color: #ccffcc;'; break;
        }
        
        echo "<tr style='$status_color'>";
        echo "<td>" . htmlspecialchars($row['username']) . "</td>";
        echo "<td>" . htmlspecialchars($row['ticket_type']) . "</td>";
        echo "<td>" . $row['remaining_tickets'] . "</td>";
        echo "<td>" . $row['purchase_time'] . "</td>";
        echo "<td>" . ($row['expiration_date'] ?? 'NULL') . "</td>";
        echo "<td>" . $row['seconds_until_expiry'] . "</td>";
        echo "<td>" . $row['status'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>No ticket purchases found.</p>";
}

// Check if expiration calculation is working
echo "<h3>Test Expiration Date Calculation:</h3>";
$test_purchase_time = date('Y-m-d H:i:s');
echo "<p>If a ticket was purchased now ($test_purchase_time):</p>";

// Test the actual function
echo "<p><strong>Testing getTicketExpirationDate() function:</strong></p>";
$expiration_from_function = getTicketExpirationDate($test_purchase_time);
echo "<p>Function result: <strong>$expiration_from_function</strong></p>";

// Manual calculation for comparison
$lifetime_value = $config['ticket_lifetime_months'];
$lifetime_unit = $config['ticket_lifetime_unit'];

echo "<p>Manual calculation with config values:</p>";
echo "<p>Lifetime value: $lifetime_value</p>";
echo "<p>Lifetime unit: $lifetime_unit</p>";

switch($lifetime_unit) {
    case 'minutes':
        $expiration_test = date('Y-m-d H:i:s', strtotime($test_purchase_time . " +{$lifetime_value} minutes"));
        break;
    case 'hours':
        $expiration_test = date('Y-m-d H:i:s', strtotime($test_purchase_time . " +{$lifetime_value} hours"));
        break;
    case 'days':
        $expiration_test = date('Y-m-d H:i:s', strtotime($test_purchase_time . " +{$lifetime_value} days"));
        break;
    case 'months':
        $expiration_test = date('Y-m-d H:i:s', strtotime($test_purchase_time . " +{$lifetime_value} months"));
        break;
    default:
        $expiration_test = "ERROR: Unknown unit";
}

echo "<p>Manual calculation result: <strong>$expiration_test</strong></p>";

// Check if they match
if ($expiration_from_function === $expiration_test) {
    echo "<p style='color: green;'>✅ Function and manual calculation match!</p>";
} else {
    echo "<p style='color: red;'>❌ Function and manual calculation don't match!</p>";
}

// Check current time
echo "<h3>Current Server Time:</h3>";
echo "<p>Current time: " . date('Y-m-d H:i:s') . "</p>";
echo "<p>Current timestamp: " . time() . "</p>";

// Manual expiration check
echo "<h3>Manual Expiration Check:</h3>";
$manual_check = "SELECT COUNT(*) as expired_count 
                 FROM purchasetickets 
                 WHERE expiration_date < NOW() 
                 AND remaining_tickets > 0";
$manual_result = mysqli_query($conn, $manual_check);
$manual_row = mysqli_fetch_assoc($manual_result);
echo "<p>Tickets that should be expired: " . $manual_row['expired_count'] . "</p>";

echo "<p><a href='ticket-expiration-manager.php'>Back to Manager</a></p>";
?>
