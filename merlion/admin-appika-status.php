<?php
session_start();
include('../functions/server.php');

// Check if admin is logged in
if (!isset($_SESSION['admin_username'])) {
    header('location: admin-login');
    exit();
}

$admin_username = $_SESSION['admin_username'];
$admin_id = $_SESSION['admin_id'];
$admin_role = $_SESSION['admin_role'];
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin - Appika Connection Status</title>
    <link rel="shortcut icon" href="../image/png/favicon.png" type="image/x-icon">
    <!-- Bootstrap, fonts & icons -->
    <link rel="stylesheet" href="../css/bootstrap.css">
    <link rel="stylesheet" href="../fonts/icon-font/css/style.css">
    <link rel="stylesheet" href="../fonts/typography-font/typo.css">
    <link rel="stylesheet" href="../fonts/fontawesome-5/css/all.css">
    <!-- Vendor stylesheets -->
    <link rel="stylesheet" href="../css/main.css">
    <style>
    body {
        background-color: #f8f9fa;
    }

    .admin-container {
        padding: 20px;
        max-width: 100%;
        overflow-x: hidden;
    }

    .admin-header {
        background-color: #fff;
        padding: 15px 20px;
        border-radius: 5px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
    }

    .admin-header h1 {
        margin: 0;
        font-size: 24px;
        color: #333;
    }

    .admin-user {
        display: flex;
        align-items: center;
    }

    .admin-user span {
        margin-right: 10px;
    }

    .admin-sidebar {
        background-color: #fff;
        border-radius: 5px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        padding: 20px;
        height: calc(100vh - 100px);
        overflow-y: auto;
    }

    .admin-sidebar ul {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .admin-sidebar ul li {
        margin-bottom: 10px;
    }

    .admin-sidebar ul li a {
        display: block;
        padding: 10px 15px;
        color: #333;
        text-decoration: none;
        border-radius: 5px;
        transition: all 0.3s;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .admin-sidebar ul li a:hover,
    .admin-sidebar ul li a.active {
        background-color: #473BF0;
        color: #fff;
    }

    .admin-sidebar ul li a i {
        margin-right: 10px;
        width: 20px;
        text-align: center;
    }

    .admin-content {
        background-color: #fff;
        border-radius: 5px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        padding: 20px;
        min-height: calc(100vh - 120px);
        overflow-x: hidden;
    }

    /* Speedtest-style design */
    .speedtest-container {
        max-width: 800px;
        margin: 0 auto;
        text-align: center;
        padding: 40px 20px;
    }

    .test-circle {
        width: 300px;
        height: 300px;
        border: 4px solid #e0e0e0;
        border-radius: 50%;
        margin: 40px auto;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        background: #2c3e50;
        transition: all 0.3s ease;
    }

    .test-circle.testing {
        border-color: #00d4aa;
        animation: pulse 2s infinite;
    }

    .test-circle.success {
        border-color: #28a745;
    }

    .test-circle.error {
        border-color: #dc3545;
    }

    @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.05); }
        100% { transform: scale(1); }
    }

    .test-button {
        background: none;
        border: none;
        color: #fff;
        font-size: 48px;
        font-weight: bold;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .test-button:hover {
        color: #00d4aa;
        transform: scale(1.1);
    }

    .test-button:disabled {
        cursor: not-allowed;
        opacity: 0.6;
    }

    .status-info {
        margin-top: 30px;
    }

    .server-info {
        display: flex;
        justify-content: space-around;
        margin-top: 40px;
        flex-wrap: wrap;
    }

    .server-card {
        background: #34495e;
        color: #fff;
        padding: 20px;
        border-radius: 10px;
        margin: 10px;
        min-width: 200px;
        flex: 1;
    }

    .server-card h4 {
        margin: 0 0 10px 0;
        color: #00d4aa;
    }

    .server-card .status {
        font-size: 18px;
        font-weight: bold;
    }

    .server-card .response-time {
        font-size: 14px;
        opacity: 0.8;
    }

    .status-online {
        color: #28a745;
    }

    .status-offline {
        color: #dc3545;
    }

    .status-testing {
        color: #ffc107;
    }

    .results-section {
        margin-top: 40px;
        display: none;
    }

    .results-section.show {
        display: block;
    }

    .result-item {
        background: #f8f9fa;
        padding: 15px;
        margin: 10px 0;
        border-radius: 8px;
        border-left: 4px solid #473BF0;
    }

    /* Responsive design */
    @media (max-width: 768px) {
        .test-circle {
            width: 250px;
            height: 250px;
        }
        
        .test-button {
            font-size: 36px;
        }
        
        .server-info {
            flex-direction: column;
        }
        
        .speedtest-container {
            padding: 20px 10px;
        }
    }

    @media (max-width: 480px) {
        .test-circle {
            width: 200px;
            height: 200px;
        }
        
        .test-button {
            font-size: 28px;
        }
    }

    .loading-spinner {
        display: none;
        width: 40px;
        height: 40px;
        border: 4px solid #f3f3f3;
        border-top: 4px solid #00d4aa;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin: 0 auto;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .user-dropdown {
        position: relative;
        display: inline-block;
    }

    .user-info {
        cursor: pointer;
        display: flex;
        align-items: center;
    }

    .mobile-menu-toggle {
        margin-left: 5px;
        transition: transform 0.3s ease;
        display: none;
    }

    .mobile-menu-toggle.active {
        transform: rotate(180deg);
    }

    .dropdown-content {
        display: block;
    }

    /* Mobile Styles */
    @media (max-width: 767px) {
        .admin-header {
            flex-direction: column;
            align-items: flex-start;
        }

        .admin-header h1 {
            margin-bottom: 10px;
        }

        .admin-user {
            width: 100%;
        }

        .user-dropdown {
            width: 100%;
        }

        .user-info {
            width: 100%;
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
        }

        .mobile-menu-toggle {
            display: inline-block;
        }

        .dropdown-content {
            display: none;
            position: absolute;
            background-color: #f9f9f9;
            width: 100%;
            left: 0;
            box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.2);
            z-index: 1;
            border-radius: 4px;
            margin-top: 5px;
            padding: 8px;
        }

        .dropdown-content .btn {
            width: 100%;
            text-align: center;
        }

        .user-dropdown.active .dropdown-content {
            display: block;
        }

        .admin-sidebar {
            height: auto;
            margin-bottom: 20px;
        }
    }
    </style>
</head>

<body>
    <div class="admin-container">
        <div class="admin-header">
            <h1>Appika Connection Status</h1>
            <div class="admin-user">
                <div class="user-dropdown">
                    <span class="user-info">
                        Welcome, <?php echo htmlspecialchars($admin_username); ?>
                        (<?php echo htmlspecialchars($admin_role); ?>)
                        <i class="mobile-menu-toggle fas fa-chevron-down"></i>
                    </span>
                    <div class="dropdown-content">
                        <a href="admin-logout" class="btn btn-sm btn-outline-danger">Logout</a>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-2 col-md-3 col-sm-12">
                <?php include('admin-menu.php'); ?>
            </div>

            <div class="col-lg-10 col-md-9 col-sm-12">
                <div class="admin-content">
                    <div class="speedtest-container">
                        <h2>Appika API Connection Test</h2>
                        <p class="text-muted">Test the connection speed and availability of Appika API servers</p>

                        <div class="test-circle" id="testCircle">
                            <button class="test-button" id="testButton" onclick="startTest()">GO</button>
                            <div class="loading-spinner" id="loadingSpinner"></div>
                        </div>

                        <div class="status-info">
                            <h4 id="statusText">Click GO to test connection</h4>
                            <p id="statusDetails" class="text-muted"></p>
                        </div>

                        <div class="server-info">
                            <div class="server-card">
                                <h4><i class="fas fa-ticket-alt"></i> Ticket API</h4>
                                <div class="status" id="ticketStatus">Ready</div>
                                <div class="response-time" id="ticketTime"></div>
                                <small>dev-sgsg-tktapi.appika.com</small>
                            </div>
                            <div class="server-card">
                                <h4><i class="fas fa-users"></i> Customer API</h4>
                                <div class="status" id="customerStatus">Ready</div>
                                <div class="response-time" id="customerTime"></div>
                                <small>dev-api-pooh-sgsg.appika.com</small>
                            </div>
                        </div>

                        <div class="results-section" id="resultsSection">
                            <h3>Test Results</h3>
                            <div id="resultsContent"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="../js/vendor.min.js"></script>
    <script>
    let isTestRunning = false;

    function startTest() {
        if (isTestRunning) return;
        
        isTestRunning = true;
        const testButton = document.getElementById('testButton');
        const testCircle = document.getElementById('testCircle');
        const loadingSpinner = document.getElementById('loadingSpinner');
        const statusText = document.getElementById('statusText');
        const statusDetails = document.getElementById('statusDetails');
        const resultsSection = document.getElementById('resultsSection');
        
        // Reset UI
        testButton.style.display = 'none';
        loadingSpinner.style.display = 'block';
        testCircle.className = 'test-circle testing';
        statusText.textContent = 'Testing connection...';
        statusDetails.textContent = 'Please wait while we test the Appika API servers';
        resultsSection.classList.remove('show');
        
        // Reset server status
        document.getElementById('ticketStatus').textContent = 'Testing...';
        document.getElementById('ticketStatus').className = 'status status-testing';
        document.getElementById('customerStatus').textContent = 'Testing...';
        document.getElementById('customerStatus').className = 'status status-testing';
        
        // Test APIs
        testAppikaAPIs();
    }

    async function testAppikaAPIs() {
        const results = [];
        
        try {
            // Test Ticket API
            const ticketResult = await testAPI('ticket');
            results.push(ticketResult);
            updateServerStatus('ticket', ticketResult);
            
            // Test Customer API
            const customerResult = await testAPI('customer');
            results.push(customerResult);
            updateServerStatus('customer', customerResult);
            
            // Show final results
            showResults(results);
            
        } catch (error) {
            console.error('Test error:', error);
            showError('Test failed: ' + error.message);
        }
        
        isTestRunning = false;
    }

    async function testAPI(type) {
        const startTime = Date.now();
        
        try {
            const response = await fetch('test-appika-api.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ type: type })
            });
            
            const endTime = Date.now();
            const responseTime = endTime - startTime;
            
            const data = await response.json();
            
            return {
                type: type,
                success: data.success,
                responseTime: responseTime,
                message: data.message,
                httpCode: data.httpCode || null
            };
            
        } catch (error) {
            const endTime = Date.now();
            const responseTime = endTime - startTime;
            
            return {
                type: type,
                success: false,
                responseTime: responseTime,
                message: error.message,
                httpCode: null
            };
        }
    }

    function updateServerStatus(type, result) {
        const statusElement = document.getElementById(type + 'Status');
        const timeElement = document.getElementById(type + 'Time');
        
        if (result.success) {
            statusElement.textContent = 'Online';
            statusElement.className = 'status status-online';
            timeElement.textContent = result.responseTime + 'ms';
        } else {
            statusElement.textContent = 'Offline';
            statusElement.className = 'status status-offline';
            timeElement.textContent = 'Timeout';
        }
    }

    function showResults(results) {
        const testButton = document.getElementById('testButton');
        const testCircle = document.getElementById('testCircle');
        const loadingSpinner = document.getElementById('loadingSpinner');
        const statusText = document.getElementById('statusText');
        const statusDetails = document.getElementById('statusDetails');
        const resultsSection = document.getElementById('resultsSection');
        const resultsContent = document.getElementById('resultsContent');
        
        // Check overall status
        const allOnline = results.every(r => r.success);
        const avgResponseTime = results.reduce((sum, r) => sum + r.responseTime, 0) / results.length;
        
        // Update UI
        loadingSpinner.style.display = 'none';
        testButton.style.display = 'block';
        testButton.textContent = 'TEST AGAIN';
        
        if (allOnline) {
            testCircle.className = 'test-circle success';
            statusText.textContent = 'All Systems Online';
            statusDetails.textContent = `Average response time: ${Math.round(avgResponseTime)}ms`;
        } else {
            testCircle.className = 'test-circle error';
            statusText.textContent = 'Connection Issues Detected';
            statusDetails.textContent = 'Some Appika services are unavailable';
        }
        
        // Show detailed results
        let resultsHTML = '';
        results.forEach(result => {
            const icon = result.success ? '✅' : '❌';
            const apiName = result.type === 'ticket' ? 'Ticket API' : 'Customer API';
            resultsHTML += `
                <div class="result-item">
                    <strong>${icon} ${apiName}</strong><br>
                    Status: ${result.success ? 'Online' : 'Offline'}<br>
                    Response Time: ${result.responseTime}ms<br>
                    ${result.httpCode ? `HTTP Code: ${result.httpCode}<br>` : ''}
                    Message: ${result.message}
                </div>
            `;
        });
        
        resultsContent.innerHTML = resultsHTML;
        resultsSection.classList.add('show');
    }

    function showError(message) {
        const testButton = document.getElementById('testButton');
        const testCircle = document.getElementById('testCircle');
        const loadingSpinner = document.getElementById('loadingSpinner');
        const statusText = document.getElementById('statusText');
        const statusDetails = document.getElementById('statusDetails');
        
        loadingSpinner.style.display = 'none';
        testButton.style.display = 'block';
        testButton.textContent = 'TRY AGAIN';
        testCircle.className = 'test-circle error';
        statusText.textContent = 'Test Failed';
        statusDetails.textContent = message;
    }

    // Mobile dropdown functionality
    document.addEventListener('DOMContentLoaded', function() {
        const userDropdown = document.querySelector('.user-dropdown');
        const userInfo = document.querySelector('.user-info');
        const menuToggle = document.querySelector('.mobile-menu-toggle');

        if (window.innerWidth <= 767) {
            userInfo.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                userDropdown.classList.toggle('active');
                menuToggle.classList.toggle('active');
            });

            menuToggle.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                userDropdown.classList.toggle('active');
                menuToggle.classList.toggle('active');
            });

            document.addEventListener('click', function(event) {
                if (!userDropdown.contains(event.target)) {
                    userDropdown.classList.remove('active');
                    menuToggle.classList.remove('active');
                }
            });
        }

        window.addEventListener('resize', function() {
            if (window.innerWidth > 767) {
                userDropdown.classList.remove('active');
                menuToggle.classList.remove('active');
            }
        });
    });
    </script>
</body>
</html>
