<?php
// Include database connection
include_once('../functions/server.php');

// Check if admin is logged in
session_start();
if (!isset($_SESSION['admin_username'])) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'error' => 'Not logged in']);
    exit();
}

$admin_id = $_SESSION['admin_id'];
$admin_username = $_SESSION['admin_username'];

// Check if request is POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'error' => 'Invalid request method']);
    exit();
}

// Get message data
$message = isset($_POST['message']) ? trim($_POST['message']) : '';
$chat_user_id = isset($_POST['user_id']) ? intval($_POST['user_id']) : null;
$chat_ticket_id = isset($_POST['ticket_id']) ? intval($_POST['ticket_id']) : null;
$update_status = isset($_POST['update_status']) ? $_POST['update_status'] : null;

// Validate data
if (empty($message) || !$chat_user_id) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'error' => 'Missing required data']);
    exit();
}

// Create chat_messages table if it doesn't exist
$create_table_sql = "CREATE TABLE IF NOT EXISTS chat_messages (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    ticket_id INT(11) NOT NULL,
    sender_id INT(11) NOT NULL,
    sender_type ENUM('user', 'admin') NOT NULL,
    message TEXT NOT NULL,
    is_read TINYINT(1) DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);";

mysqli_query($conn, $create_table_sql);

// Make sure ticket_id is not null
if (!$chat_ticket_id) {
    // Get the first ticket for this user
    $first_ticket_query = "SELECT id FROM support_tickets WHERE user_id = $chat_user_id LIMIT 1";
    $first_ticket_result = mysqli_query($conn, $first_ticket_query);
    if ($first_ticket_result && mysqli_num_rows($first_ticket_result) > 0) {
        $first_ticket = mysqli_fetch_assoc($first_ticket_result);
        $chat_ticket_id = $first_ticket['id'];
    } else {
        // If no ticket exists, create a default value
        $chat_ticket_id = 0;
    }
}

// Insert message
$insert_query = "INSERT INTO chat_messages (ticket_id, sender_id, sender_type, message)
                VALUES (?, ?, 'admin', ?)";
$stmt = mysqli_prepare($conn, $insert_query);

if (!$stmt) {
    header('Content-Type: application/json');
    echo json_encode([
        'success' => false,
        'error' => 'Database prepare error: ' . mysqli_error($conn)
    ]);
    exit();
}

mysqli_stmt_bind_param($stmt, 'iis', $chat_ticket_id, $admin_id, $message);
$success = mysqli_stmt_execute($stmt);

if (!$success) {
    header('Content-Type: application/json');
    echo json_encode([
        'success' => false,
        'error' => 'Database execute error: ' . mysqli_stmt_error($stmt)
    ]);
    mysqli_stmt_close($stmt);
    exit();
}

$message_id = mysqli_insert_id($conn);
mysqli_stmt_close($stmt);

if ($success) {
    // Update ticket status to in_progress and assign admin if requested
    if ($update_status === 'in_progress' && $chat_ticket_id > 0) {
        // Check if ticket is already assigned to this admin
        $check_assignment_query = "SELECT assigned_admin_id FROM support_tickets WHERE id = ?";
        $stmt = mysqli_prepare($conn, $check_assignment_query);
        mysqli_stmt_bind_param($stmt, 'i', $chat_ticket_id);
        mysqli_stmt_execute($stmt);
        $result = mysqli_stmt_get_result($stmt);
        $ticket_data = mysqli_fetch_assoc($result);
        $current_assigned_admin = $ticket_data['assigned_admin_id'];
        mysqli_stmt_close($stmt);

        // Update both status and assigned admin (if not already assigned)
        if ($current_assigned_admin === null || $current_assigned_admin == 0) {
            $update_query = "UPDATE support_tickets SET status = 'in_progress', assigned_admin_id = ? WHERE id = ?";
            $stmt = mysqli_prepare($conn, $update_query);
            if (!$stmt) {
                header('Content-Type: application/json');
                echo json_encode([
                    'success' => false,
                    'error' => 'Database prepare error (status update): ' . mysqli_error($conn)
                ]);
                exit();
            }
            mysqli_stmt_bind_param($stmt, 'ii', $admin_id, $chat_ticket_id);
            $admin_assigned = true;
        } else {
            // Only update status if admin is already assigned
            $update_query = "UPDATE support_tickets SET status = 'in_progress' WHERE id = ?";
            $stmt = mysqli_prepare($conn, $update_query);
            if (!$stmt) {
                header('Content-Type: application/json');
                echo json_encode([
                    'success' => false,
                    'error' => 'Database prepare error (status update): ' . mysqli_error($conn)
                ]);
                exit();
            }
            mysqli_stmt_bind_param($stmt, 'i', $chat_ticket_id);
            $admin_assigned = false;
        }

        $status_updated = mysqli_stmt_execute($stmt);
        if (!$status_updated) {
            header('Content-Type: application/json');
            echo json_encode([
                'success' => false,
                'error' => 'Failed to update ticket status: ' . mysqli_stmt_error($stmt)
            ]);
            mysqli_stmt_close($stmt);
            exit();
        }
        mysqli_stmt_close($stmt);

        // Status change logging removed to save database storage
        // Admin assignment and status changes are tracked in the ticket record itself
    }

    // Get the inserted message
    $message_query = "SELECT cm.*,
                     a.username as sender_name,
                     DATE_FORMAT(cm.created_at, '%M %d, %H:%i') as formatted_time
                     FROM chat_messages cm
                     JOIN admin_users a ON cm.sender_id = a.id
                     WHERE cm.id = $message_id";
    $message_result = mysqli_query($conn, $message_query);

    if (!$message_result) {
        header('Content-Type: application/json');
        echo json_encode([
            'success' => false,
            'error' => 'Database error: ' . mysqli_error($conn)
        ]);
        exit();
    }

    $message_data = mysqli_fetch_assoc($message_result);

    header('Content-Type: application/json');
    echo json_encode([
        'success' => true,
        'message' => $message_data,
        'status_updated' => isset($status_updated) ? $status_updated : false,
        'admin_assigned' => isset($admin_assigned) ? $admin_assigned : false
    ]);
} else {
    header('Content-Type: application/json');
    echo json_encode([
        'success' => false,
        'error' => 'Failed to send message'
    ]);
}
