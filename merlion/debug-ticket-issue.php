<?php
/**
 * Debug Ticket Issue - Find out why tickets disappear
 */

session_start();
include('../functions/server.php');

// Check if admin is logged in
if (!isset($_SESSION['admin_username'])) {
    header('location: admin-login.php');
    exit();
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Ticket Issue</title>
    <link rel="stylesheet" href="../css/bootstrap.css">
    <style>
        body { background-color: #f8f9fa; padding: 20px; }
        .container { max-width: 1000px; }
        .debug-section { background: white; padding: 20px; margin: 15px 0; border-radius: 8px; border-left: 4px solid #007bff; }
        .error { border-left-color: #dc3545; }
        .success { border-left-color: #28a745; }
        .warning { border-left-color: #ffc107; }
        .code { background: #f8f9fa; padding: 10px; border-radius: 4px; font-family: monospace; }
        table { font-size: 14px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Debug Ticket Issue for HC200</h1>
        
        <?php
        $test_user_id = 200;
        $test_username = 'HC200';
        
        // 1. Check current user table state
        echo "<div class='debug-section'>";
        echo "<h3>1. Current User Table State</h3>";
        $user_query = "SELECT id, username, starter_tickets, premium_tickets, ultimate_tickets FROM user WHERE id = $test_user_id";
        $user_result = mysqli_query($conn, $user_query);
        
        if ($user_result && mysqli_num_rows($user_result) > 0) {
            $user = mysqli_fetch_assoc($user_result);
            echo "<table class='table table-sm'>";
            echo "<tr><th>ID</th><th>Username</th><th>Starter</th><th>Premium</th><th>Ultimate</th></tr>";
            echo "<tr>";
            echo "<td>{$user['id']}</td>";
            echo "<td>{$user['username']}</td>";
            echo "<td>{$user['starter_tickets']}</td>";
            echo "<td>{$user['premium_tickets']}</td>";
            echo "<td>{$user['ultimate_tickets']}</td>";
            echo "</tr>";
            echo "</table>";
        } else {
            echo "<p class='text-danger'>❌ User not found!</p>";
        }
        echo "</div>";
        
        // 2. Check purchase records
        echo "<div class='debug-section'>";
        echo "<h3>2. Purchase Records for HC200</h3>";
        $purchase_query = "SELECT * FROM purchasetickets WHERE username = '$test_username' ORDER BY purchase_time DESC LIMIT 10";
        $purchase_result = mysqli_query($conn, $purchase_query);
        
        if ($purchase_result && mysqli_num_rows($purchase_result) > 0) {
            echo "<table class='table table-sm'>";
            echo "<tr><th>Type</th><th>Qty</th><th>Remaining</th><th>Purchase Time</th><th>Expiration</th><th>Transaction ID</th></tr>";
            while ($purchase = mysqli_fetch_assoc($purchase_result)) {
                $expired = strtotime($purchase['expiration_date']) < time() ? 'style="background-color: #ffebee;"' : '';
                echo "<tr $expired>";
                echo "<td>{$purchase['ticket_type']}</td>";
                echo "<td>{$purchase['quantity']}</td>";
                echo "<td>{$purchase['remaining_tickets']}</td>";
                echo "<td>{$purchase['purchase_time']}</td>";
                echo "<td>{$purchase['expiration_date']}</td>";
                echo "<td>{$purchase['transaction_id']}</td>";
                echo "</tr>";
            }
            echo "</table>";
            echo "<small class='text-muted'>Red background = expired records</small>";
        } else {
            echo "<p class='text-warning'>⚠️ No purchase records found for HC200</p>";
        }
        echo "</div>";
        
        // 3. Check ticket logs
        echo "<div class='debug-section'>";
        echo "<h3>3. Recent Ticket Logs for HC200</h3>";
        $log_query = "SELECT * FROM ticket_logs WHERE user_id = $test_user_id ORDER BY created_at DESC LIMIT 10";
        $log_result = mysqli_query($conn, $log_query);
        
        if ($log_result && mysqli_num_rows($log_result) > 0) {
            echo "<table class='table table-sm'>";
            echo "<tr><th>Action</th><th>Description</th><th>Type</th><th>Amount</th><th>Admin ID</th><th>Created At</th></tr>";
            while ($log = mysqli_fetch_assoc($log_result)) {
                echo "<tr>";
                echo "<td>{$log['action']}</td>";
                echo "<td>{$log['description']}</td>";
                echo "<td>{$log['ticket_type']}</td>";
                echo "<td>{$log['amount']}</td>";
                echo "<td>{$log['performed_by_admin_id']}</td>";
                echo "<td>{$log['created_at']}</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p class='text-warning'>⚠️ No ticket logs found for HC200</p>";
        }
        echo "</div>";
        
        // 4. Check expiration log
        echo "<div class='debug-section'>";
        echo "<h3>4. Ticket Expiration Log for HC200</h3>";
        $exp_query = "SELECT * FROM ticket_expiration_log WHERE username = '$test_username' ORDER BY cleanup_date DESC LIMIT 5";
        $exp_result = mysqli_query($conn, $exp_query);
        
        if ($exp_result && mysqli_num_rows($exp_result) > 0) {
            echo "<p class='text-danger'><strong>⚠️ FOUND EXPIRATION ACTIVITY!</strong></p>";
            echo "<table class='table table-sm'>";
            echo "<tr><th>Type</th><th>Expired Qty</th><th>Purchase Date</th><th>Cleanup Date</th></tr>";
            while ($exp = mysqli_fetch_assoc($exp_result)) {
                echo "<tr>";
                echo "<td>{$exp['ticket_type']}</td>";
                echo "<td>{$exp['expired_quantity']}</td>";
                echo "<td>{$exp['purchase_date']}</td>";
                echo "<td>{$exp['cleanup_date']}</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p class='text-success'>✅ No expiration cleanup activity found for HC200</p>";
        }
        echo "</div>";
        
        // 5. Test manual ticket addition
        if (isset($_POST['add_test_ticket'])) {
            echo "<div class='debug-section warning'>";
            echo "<h3>5. Manual Test Results</h3>";
            
            $add_query = "UPDATE user SET premium_tickets = premium_tickets + 1 WHERE id = $test_user_id";
            echo "<p><strong>SQL:</strong> <code>$add_query</code></p>";
            
            if (mysqli_query($conn, $add_query)) {
                $affected = mysqli_affected_rows($conn);
                echo "<p class='text-success'>✅ Update executed successfully</p>";
                echo "<p><strong>Affected rows:</strong> $affected</p>";
                
                // Check new count immediately
                $check_result = mysqli_query($conn, $user_query);
                $updated_user = mysqli_fetch_assoc($check_result);
                echo "<p><strong>New premium tickets:</strong> {$updated_user['premium_tickets']}</p>";
                
                // Wait 2 seconds and check again
                echo "<p>Waiting 2 seconds...</p>";
                sleep(2);
                
                $check_result2 = mysqli_query($conn, $user_query);
                $updated_user2 = mysqli_fetch_assoc($check_result2);
                echo "<p><strong>After 2 seconds:</strong> {$updated_user2['premium_tickets']}</p>";
                
                if ($updated_user2['premium_tickets'] != $updated_user['premium_tickets']) {
                    echo "<p class='text-danger'>❌ TICKETS CHANGED! Something is modifying them!</p>";
                } else {
                    echo "<p class='text-success'>✅ Tickets remained stable</p>";
                }
            } else {
                echo "<p class='text-danger'>❌ Update failed: " . mysqli_error($conn) . "</p>";
            }
            echo "</div>";
        }
        
        // 6. Check configuration
        echo "<div class='debug-section'>";
        echo "<h3>6. System Configuration</h3>";
        
        if (file_exists('../config/ticket-expiration-config.php')) {
            include_once('../config/ticket-expiration-config.php');
            $config = getTicketExpirationConfig();
            echo "<table class='table table-sm'>";
            echo "<tr><th>Setting</th><th>Value</th></tr>";
            echo "<tr><td>System Enabled</td><td>" . ($config['system_enabled'] ? 'YES' : 'NO') . "</td></tr>";
            echo "<tr><td>Auto Cleanup Enabled</td><td>" . ($config['auto_cleanup_enabled'] ? 'YES' : 'NO') . "</td></tr>";
            echo "<tr><td>Ticket Lifetime</td><td>{$config['ticket_lifetime_months']} {$config['ticket_lifetime_unit']}</td></tr>";
            echo "<tr><td>Last Cleanup</td><td>" . ($config['last_cleanup_timestamp'] ? date('Y-m-d H:i:s', $config['last_cleanup_timestamp']) : 'Never') . "</td></tr>";
            echo "</table>";
        } else {
            echo "<p class='text-warning'>⚠️ Expiration config file not found</p>";
        }
        echo "</div>";
        ?>
        
        <div class="mt-4">
            <form method="POST">
                <button type="submit" name="add_test_ticket" class="btn btn-warning">🧪 Add 1 Premium Ticket & Monitor</button>
            </form>
            <a href="admin-users.php" class="btn btn-secondary mt-2">← Back to Users</a>
            <button onclick="window.location.reload()" class="btn btn-info mt-2">🔄 Refresh</button>
        </div>
    </div>
</body>
</html>
