<?php
// Quick fix script to update unit values to minutes
include_once('../functions/server.php');
include_once('../config/ticket-expiration-config.php');

echo "<h2>Database Connection Debug</h2>";
echo "<p>Connection status: " . ($conn ? 'Connected' : 'NOT Connected') . "</p>";
if ($conn) {
    echo "<p>Connection info: " . mysqli_get_host_info($conn) . "</p>";
    echo "<p>Database: " . mysqli_get_server_info($conn) . "</p>";
}

echo "<h2>Fixing Unit Values</h2>";

// Direct database updates
$updates = [
    'ticket_lifetime_unit' => 'minutes',
    'cleanup_frequency_unit' => 'minutes', 
    'warning_period_unit' => 'minutes'
];

foreach ($updates as $key => $value) {
    echo "<p>Updating $key to '$value'...</p>";
    
    // Direct SQL update
    $query = "INSERT INTO ticket_expiration_settings (config_key, config_value, updated_at) 
              VALUES (?, ?, NOW()) 
              ON DUPLICATE KEY UPDATE 
              config_value = VALUES(config_value), 
              updated_at = NOW()";
    
    $stmt = mysqli_prepare($conn, $query);
    if ($stmt) {
        mysqli_stmt_bind_param($stmt, 'ss', $key, $value);
        $result = mysqli_stmt_execute($stmt);
        
        if ($result) {
            echo "<span style='color: green;'>SUCCESS</span><br>";
        } else {
            echo "<span style='color: red;'>FAILED: " . mysqli_error($conn) . "</span><br>";
        }
        mysqli_stmt_close($stmt);
    } else {
        echo "<span style='color: red;'>PREPARE FAILED: " . mysqli_error($conn) . "</span><br>";
    }
}

echo "<h3>Verification - Current Database Values:</h3>";
$verify_query = "SELECT config_key, config_value FROM ticket_expiration_settings WHERE config_key LIKE '%_unit'";
$result = mysqli_query($conn, $verify_query);

if ($result && mysqli_num_rows($result) > 0) {
    while ($row = mysqli_fetch_assoc($result)) {
        echo "<p>" . $row['config_key'] . " = '" . $row['config_value'] . "'</p>";
    }
} else {
    echo "<p>No unit values found</p>";
}

echo "<h3>Testing getTicketExpirationConfig() function:</h3>";
$config_test = getTicketExpirationConfig();
echo "<p>ticket_lifetime_unit from function: '" . ($config_test['ticket_lifetime_unit'] ?? 'NOT SET') . "'</p>";
echo "<p>cleanup_frequency_unit from function: '" . ($config_test['cleanup_frequency_unit'] ?? 'NOT SET') . "'</p>";
echo "<p>warning_period_unit from function: '" . ($config_test['warning_period_unit'] ?? 'NOT SET') . "'</p>";

echo "<h3>Testing with fresh connection:</h3>";
// Create a fresh connection
$fresh_conn = mysqli_connect($servername, $username, $password, $dbname);
if ($fresh_conn) {
    $fresh_query = "SELECT config_key, config_value FROM ticket_expiration_settings WHERE config_key LIKE '%_unit'";
    $fresh_result = mysqli_query($fresh_conn, $fresh_query);

    if ($fresh_result && mysqli_num_rows($fresh_result) > 0) {
        echo "<p>Fresh connection results:</p>";
        while ($row = mysqli_fetch_assoc($fresh_result)) {
            echo "<p>" . $row['config_key'] . " = '" . $row['config_value'] . "'</p>";
        }
    }
    mysqli_close($fresh_conn);
} else {
    echo "<p>Fresh connection failed</p>";
}

echo "<p><a href='ticket-expiration-manager.php?debug=1'>Go back to main page</a></p>";
?>
