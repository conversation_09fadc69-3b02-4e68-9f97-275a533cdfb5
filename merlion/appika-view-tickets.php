<?php
session_start();
include('../functions/server.php');
require_once '../vendor/autoload.php';
require_once '../config/api-config.php';

// Get GraphQL API configuration
$graphqlConfig = getGraphqlApiConfig();
$graphqlEndpoint = $graphqlConfig['endpoint'];
$apiKey = $graphqlConfig['key'];

function makeGraphQLRequest($query, $variables = []) {
    global $graphqlEndpoint, $apiKey;
    $client = new \GuzzleHttp\Client([
        'timeout' => 30,
        'http_errors' => false,
    ]);
    try {
        $response = $client->post($graphqlEndpoint, [
            'headers' => [
                'X-api-key' => "{$apiKey}",
                'Content-Type' => 'application/json',
                'Accept' => 'application/json',
            ],
            'json' => [
                'query' => $query,
                'variables' => $variables
            ]
        ]);
        $statusCode = $response->getStatusCode();
        $body = $response->getBody()->getContents();
        $data = json_decode($body, true);
        return [
            'success' => ($statusCode >= 200 && $statusCode < 300 && !isset($data['errors'])),
            'status' => $statusCode,
            'data' => $data,
            'error' => isset($data['errors']) ? json_encode($data['errors']) : null
        ];
    } catch (Exception $e) {
        return [
            'success' => false,
            'status' => 500,
            'error' => $e->getMessage()
        ];
    }
}

$tickets = [];
$searchResult = null;
$searchError = '';

// Handle search by ticket ID
if ($_SERVER['REQUEST_METHOD'] === 'POST' && !empty($_POST['search_ticket_id'])) {
    $ticketId = intval($_POST['search_ticket_id']);
    $query = 'query GetTicket($id: Int!) {
  getTicket(id: $id) {
    id
    ticket_no
    contact_id
    agent_id
    req_email
    subject
    type
    type_name
    priority
    status
    created
    updated
    agent_group
    assign2
    followers
    tags
    creator_by_contact
    creator_by_agent
    ofc_id
    solved_date
    sla_id
    first_response_time
    first_response_due
    first_response
    next_response_time
    next_response_due
    next_response
    resolution_due
    remind_first
    remind_next
    remind_res
    # Add any other fields available in your schema
  }
}';
    $variables = ['id' => $ticketId];
    $result = makeGraphQLRequest($query, $variables);
    if ($result['success'] && isset($result['data']['data']['getTicket'])) {
        $searchResult = $result['data']['data']['getTicket'];
    } else {
        $searchError = $result['error'] ?? 'Ticket not found or API error.';
    }
}

// Fetch all tickets (first 100 for demo)
$query = 'query GetTickets { getTickets { id ticket_no contact_id agent_id req_email subject type type_name priority status created updated } }';
$result = makeGraphQLRequest($query);
if ($result['success'] && isset($result['data']['data']['getTickets'])) {
    $tickets = $result['data']['data']['getTickets'];
}

// Helper function to get the first comment (description) for a ticket
function getTicketDescription($ticketId) {
    $commentQuery = 'query GetTicketComments($ticketId: Int!) { getTicketComments(ticketId: $ticketId) { id comment author_id created } }';
    $commentVars = ['ticketId' => (int)$ticketId];
    $commentResult = makeGraphQLRequest($commentQuery, $commentVars);
    if ($commentResult['success'] && isset($commentResult['data']['data']['getTicketComments'][0]['comment'])) {
        return $commentResult['data']['data']['getTicketComments'][0]['comment'];
    }
    return null;
}
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <title>Appika Tickets Viewer</title>
    <link rel="stylesheet" href="../css/bootstrap.css">
    <style>
    body {
        background: #f8f9fa;
        padding: 30px;
    }

    .ticket-array {
        background: #fff;
        border: 1px solid #ddd;
        padding: 15px;
        margin-bottom: 20px;
        border-radius: 5px;
        font-family: monospace;
        font-size: 14px;
    }

    .search-form {
        margin-bottom: 30px;
    }
    </style>
</head>

<body>
    <div class="container">
        <h2>Appika Tickets Viewer</h2>
        <form method="POST" class="search-form form-inline">
            <label for="search_ticket_id" class="mr-2">Search Ticket ID:</label>
            <input type="number" name="search_ticket_id" id="search_ticket_id" class="form-control mr-2"
                placeholder="Enter Ticket ID" required>
            <button type="submit" class="btn btn-primary">Search</button>
        </form>
        <?php if ($searchResult): ?>
        <div class="alert alert-success"><strong>Search Result for Ticket ID
                <?php echo htmlspecialchars($searchResult['id']); ?>:</strong></div>
        <div class="ticket-array">
            <pre><?php print_r($searchResult); ?></pre>
        </div>
        <?php $desc = getTicketDescription($searchResult['id']); ?>
        <div class="ticket-array"><strong>Description:</strong>
            <?php
            if ($desc) {
                $cleanDesc = str_replace(["\r\n", "\r"], "\n", $desc);
                echo nl2br(htmlspecialchars($cleanDesc));
            } else {
                echo '<em>No description found for this ticket.</em>';
            }
            ?>
        </div>
        <?php elseif ($searchError): ?>
        <div class="alert alert-danger"><?php echo htmlspecialchars($searchError); ?></div>
        <?php endif; ?>
        <h4>All Tickets (first 100):</h4>
        <?php foreach ($tickets as $ticket): ?>
        <div class="ticket-array">
            <pre><?php print_r($ticket); ?></pre>
        </div>
        <?php $desc = getTicketDescription($ticket['id']); ?>
        <div class="ticket-array"><strong>Description:</strong>
            <?php
            if ($desc) {
                $cleanDesc = str_replace(["\r\n", "\r"], "\n", $desc);
                echo nl2br(htmlspecialchars($cleanDesc));
            } else {
                echo '<em>No description found for this ticket.</em>';
            }
            ?>
        </div>
        <?php endforeach; ?>
    </div>
</body>

</html>