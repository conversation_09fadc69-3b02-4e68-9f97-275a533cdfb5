<?php
session_start();
include('../functions/server.php');

// Add Appika integration dependencies at the top
require_once '../vendor/autoload.php';
require_once '../functions/graphql_functions.php';
require_once '../config/api-config.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_username'])) {
    header('location: admin-login.php');
    exit();
}

/**
 * Fetch all tickets from Appika API with filtering and search
 */
function fetchAllAppikaTickets($search = "", $typeFilter = "", $statusFilter = "", $priorityFilter = "", $severityFilter = "") {
    // Use the working GraphQL query from my-ticket.php
    $query = '
    query GetTickets {
        getTickets {
            data {
                id
                ticket_no
                contact_id
                agent_id
                req_email
                subject
                type
                type_name
                priority
                status
                created
                updated
                creator_by_contact
                creator_by_agent
                contacts {
                    id
                    email
                    fname
                    status
                }
            }
        }
    }';

    $result = makeGraphQLRequest($query, []);

    if (!$result['success']) {
        return ['tickets' => [], 'error' => $result['error']];
    }

    $tickets = $result['data']['data']['getTickets']['data'] ?? [];
    
    // Apply filters
    $filteredTickets = [];
    foreach ($tickets as $ticket) {
        // Search filter
        if (!empty($search)) {
            $searchLower = strtolower($search);
            $searchFields = [
                strtolower($ticket['id'] ?? ''),
                strtolower($ticket['ticket_no'] ?? ''),
                strtolower($ticket['subject'] ?? ''),
                strtolower($ticket['req_email'] ?? ''),
                strtolower($ticket['contacts'][0]['fname'] ?? '')
            ];
            
            $matchFound = false;
            foreach ($searchFields as $field) {
                if (strpos($field, $searchLower) !== false) {
                    $matchFound = true;
                    break;
                }
            }
            
            if (!$matchFound) {
                continue;
            }
        }
        
        // Type filter
        if (!empty($typeFilter) && $typeFilter !== 'all') {
            if (strtolower($ticket['type_name'] ?? '') !== strtolower($typeFilter)) {
                continue;
            }
        }
        
        // Status filter
        if (!empty($statusFilter) && $statusFilter !== 'all') {
            if (strtolower($ticket['status'] ?? '') !== strtolower($statusFilter)) {
                continue;
            }
        }
        
        // Priority filter
        if (!empty($priorityFilter) && $priorityFilter !== 'all') {
            if (strtolower($ticket['priority'] ?? '') !== strtolower($priorityFilter)) {
                continue;
            }
        }
        
        // Severity filter (same as priority for now)
        if (!empty($severityFilter) && $severityFilter !== 'all') {
            if (strtolower($ticket['priority'] ?? '') !== strtolower($severityFilter)) {
                continue;
            }
        }
        
        $filteredTickets[] = $ticket;
    }

    return ['tickets' => $filteredTickets, 'error' => null];
}

/**
 * Get local user data by email for ticket display
 */
function getLocalUserByEmail($email) {
    global $conn;
    
    $stmt = $conn->prepare("SELECT id, username, appika_id FROM user WHERE email = ?");
    $stmt->bind_param("s", $email);
    $stmt->execute();
    $result = $stmt->get_result();
    
    return $result->fetch_assoc();
}

/**
 * Format date for display
 */
function formatTicketDate($dateString) {
    if (empty($dateString)) {
        return 'N/A';
    }
    
    try {
        $date = new DateTime($dateString);
        return $date->format('Y-m-d H:i');
    } catch (Exception $e) {
        return htmlspecialchars($dateString);
    }
}

/**
 * Get status badge class
 */
function getStatusBadgeClass($status) {
    switch (strtolower($status)) {
        case 'open':
            return 'badge-success';
        case 'closed':
            return 'badge-secondary';
        case 'pending':
            return 'badge-warning';
        case 'resolved':
            return 'badge-info';
        default:
            return 'badge-primary';
    }
}

/**
 * Get type badge class
 */
function getTypeBadgeClass($type) {
    switch (strtolower($type)) {
        case 'starter':
            return 'badge-warning';
        case 'business':
        case 'premium':
            return 'badge-info';
        case 'ultimate':
            return 'badge-primary';
        default:
            return 'badge-secondary';
    }
}

/**
 * Get priority badge class
 */
function getPriorityBadgeClass($priority) {
    switch (strtolower($priority)) {
        case 'high':
            return 'badge-danger';
        case 'medium':
            return 'badge-warning';
        case 'low':
            return 'badge-success';
        case 'normal':
            return 'badge-info';
        default:
            return 'badge-secondary';
    }
}

// Handle search and filters
$search = isset($_GET['search']) ? trim($_GET['search']) : '';
$typeFilter = isset($_GET['type']) ? $_GET['type'] : '';
$statusFilter = isset($_GET['status']) ? $_GET['status'] : '';
$priorityFilter = isset($_GET['priority']) ? $_GET['priority'] : '';
$severityFilter = isset($_GET['severity']) ? $_GET['severity'] : '';

// Pagination settings
$itemsPerPage = 10;
$page = isset($_GET['page']) ? max(1, (int)$_GET['page']) : 1;

// Fetch tickets from Appika API
$apiResult = fetchAllAppikaTickets($search, $typeFilter, $statusFilter, $priorityFilter, $severityFilter);
$allTickets = $apiResult['tickets'];
$apiError = $apiResult['error'];

// Calculate pagination
$totalItems = count($allTickets);
$totalPages = ceil($totalItems / $itemsPerPage);
$offset = ($page - 1) * $itemsPerPage;

// Get tickets for current page
$tickets = array_slice($allTickets, $offset, $itemsPerPage);

// Process form submissions
$message = '';
$message_type = '';

include('header.php');
?>

<div class="main-content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h4 class="mb-0">
                            <i class="fa fa-ticket-alt"></i> All Tickets
                        </h4>
                        <div class="d-flex align-items-center">
                            <span class="badge badge-info mr-3">Total: <?php echo $totalItems; ?></span>
                        </div>
                    </div>
                    
                    <div class="card-body">
                        <?php if (!empty($message)): ?>
                        <div class="alert alert-<?php echo $message_type; ?> alert-dismissible fade show" role="alert">
                            <?php echo htmlspecialchars($message); ?>
                            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                        </div>
                        <?php endif; ?>
                        
                        <?php if ($apiError): ?>
                        <div class="alert alert-danger" role="alert">
                            <strong>API Error:</strong> <?php echo htmlspecialchars($apiError); ?>
                        </div>
                        <?php endif; ?>
                        
                        <!-- Search and Filter Form -->
                        <form method="GET" class="mb-4">
                            <div class="row">
                                <div class="col-md-3">
                                    <input type="text" class="form-control" name="search" 
                                           placeholder="Search by ID, APK No, subject, username or date" 
                                           value="<?php echo htmlspecialchars($search); ?>">
                                </div>
                                <div class="col-md-2">
                                    <select class="form-control" name="type">
                                        <option value="">All Types</option>
                                        <option value="starter" <?php echo $typeFilter === 'starter' ? 'selected' : ''; ?>>Starter</option>
                                        <option value="business" <?php echo $typeFilter === 'business' ? 'selected' : ''; ?>>Business</option>
                                        <option value="ultimate" <?php echo $typeFilter === 'ultimate' ? 'selected' : ''; ?>>Ultimate</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <select class="form-control" name="status">
                                        <option value="">All Statuses</option>
                                        <option value="open" <?php echo $statusFilter === 'open' ? 'selected' : ''; ?>>Open</option>
                                        <option value="closed" <?php echo $statusFilter === 'closed' ? 'selected' : ''; ?>>Closed</option>
                                        <option value="pending" <?php echo $statusFilter === 'pending' ? 'selected' : ''; ?>>Pending</option>
                                        <option value="resolved" <?php echo $statusFilter === 'resolved' ? 'selected' : ''; ?>>Resolved</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <select class="form-control" name="priority">
                                        <option value="">All Priorities</option>
                                        <option value="high" <?php echo $priorityFilter === 'high' ? 'selected' : ''; ?>>High</option>
                                        <option value="medium" <?php echo $priorityFilter === 'medium' ? 'selected' : ''; ?>>Medium</option>
                                        <option value="normal" <?php echo $priorityFilter === 'normal' ? 'selected' : ''; ?>>Normal</option>
                                        <option value="low" <?php echo $priorityFilter === 'low' ? 'selected' : ''; ?>>Low</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <select class="form-control" name="severity">
                                        <option value="">All Severities</option>
                                        <option value="high" <?php echo $severityFilter === 'high' ? 'selected' : ''; ?>>High</option>
                                        <option value="medium" <?php echo $severityFilter === 'medium' ? 'selected' : ''; ?>>Medium</option>
                                        <option value="normal" <?php echo $severityFilter === 'normal' ? 'selected' : ''; ?>>Normal</option>
                                        <option value="low" <?php echo $severityFilter === 'low' ? 'selected' : ''; ?>>Low</option>
                                    </select>
                                </div>
                                <div class="col-md-1">
                                    <button class="btn btn-primary" type="submit">
                                        <i class="fa fa-search"></i>
                                    </button>
                                </div>
                            </div>
                            <?php if (!empty($search) || !empty($typeFilter) || !empty($statusFilter) || !empty($priorityFilter) || !empty($severityFilter)): ?>
                            <div class="row mt-2">
                                <div class="col-md-12">
                                    <a href="admin-tickets.php" class="btn btn-secondary btn-sm">
                                        <i class="fa fa-times"></i> Clear Filters
                                    </a>
                                </div>
                            </div>
                            <?php endif; ?>
                        </form>
                        
                        <!-- Tickets Table -->
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Appika ID</th>
                                        <th>Subject</th>
                                        <th>User</th>
                                        <th>Type</th>
                                        <th>Priority</th>
                                        <th>Severity</th>
                                        <th>Status</th>
                                        <th>Assigned Admin</th>
                                        <th>Created</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if (!empty($tickets)): ?>
                                    <?php foreach ($tickets as $ticket): ?>
                                    <?php 
                                        // Get local user data for display
                                        $localUser = getLocalUserByEmail($ticket['req_email'] ?? '');
                                        $username = $localUser ? $localUser['username'] : 'Unknown';
                                        $userAppikaId = $localUser ? $localUser['appika_id'] : 'N/A';
                                        
                                        // Get contact name from contacts array
                                        $contactName = '';
                                        if (!empty($ticket['contacts']) && is_array($ticket['contacts'])) {
                                            $contactName = $ticket['contacts'][0]['fname'] ?? '';
                                        }
                                        
                                        // Use contact name if available, otherwise use username
                                        $displayName = !empty($contactName) ? $contactName : $username;
                                    ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($ticket['id'] ?? ''); ?></td>
                                        <td>
                                            <strong><?php echo htmlspecialchars($ticket['ticket_no'] ?? ''); ?></strong>
                                            <?php if ($ticket['creator_by_contact'] === 'y'): ?>
                                            <span class="badge badge-info badge-sm ml-1">New</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <strong><?php echo htmlspecialchars($ticket['subject'] ?? ''); ?></strong>
                                        </td>
                                        <td>
                                            <?php echo htmlspecialchars($displayName); ?>
                                            <?php if ($userAppikaId !== 'N/A'): ?>
                                            <br><small class="text-muted"><?php echo htmlspecialchars($userAppikaId); ?></small>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <span class="badge <?php echo getTypeBadgeClass($ticket['type_name'] ?? ''); ?>">
                                                <?php echo htmlspecialchars($ticket['type_name'] ?? 'Unknown'); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge <?php echo getPriorityBadgeClass($ticket['priority'] ?? ''); ?>">
                                                <?php echo htmlspecialchars(ucfirst($ticket['priority'] ?? 'Normal')); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge <?php echo getPriorityBadgeClass($ticket['priority'] ?? ''); ?>">
                                                <?php echo htmlspecialchars(ucfirst($ticket['priority'] ?? 'Normal')); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge <?php echo getStatusBadgeClass($ticket['status'] ?? ''); ?>">
                                                <?php echo htmlspecialchars(ucfirst($ticket['status'] ?? 'Unknown')); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <?php if (!empty($ticket['agent_id'])): ?>
                                                Agent ID: <?php echo htmlspecialchars($ticket['agent_id']); ?>
                                            <?php else: ?>
                                                <span class="text-muted">Not assigned</span>
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo formatTicketDate($ticket['created'] ?? ''); ?></td>
                                        <td>
                                            <a href="appika-ticket-details.php?ticket_id=<?php echo urlencode($ticket['id']); ?>" 
                                               class="btn btn-sm btn-primary" target="_blank">
                                                <i class="fa fa-eye"></i> View
                                            </a>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                    <?php else: ?>
                                    <tr>
                                        <td colspan="11" class="text-center text-muted">
                                            <?php if (!empty($search) || !empty($typeFilter) || !empty($statusFilter) || !empty($priorityFilter) || !empty($severityFilter)): ?>
                                                No tickets found matching your filters.
                                            <?php else: ?>
                                                No tickets found.
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                        
                        <!-- Pagination -->
                        <?php if ($totalPages > 1): ?>
                        <nav aria-label="Page navigation">
                            <ul class="pagination justify-content-center">
                                <?php if ($page > 1): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?page=<?php echo $page - 1; ?><?php echo !empty($search) ? '&search=' . urlencode($search) : ''; ?><?php echo !empty($typeFilter) ? '&type=' . urlencode($typeFilter) : ''; ?><?php echo !empty($statusFilter) ? '&status=' . urlencode($statusFilter) : ''; ?><?php echo !empty($priorityFilter) ? '&priority=' . urlencode($priorityFilter) : ''; ?><?php echo !empty($severityFilter) ? '&severity=' . urlencode($severityFilter) : ''; ?>">
                                        Previous
                                    </a>
                                </li>
                                <?php endif; ?>
                                
                                <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
                                <li class="page-item <?php echo $i == $page ? 'active' : ''; ?>">
                                    <a class="page-link" href="?page=<?php echo $i; ?><?php echo !empty($search) ? '&search=' . urlencode($search) : ''; ?><?php echo !empty($typeFilter) ? '&type=' . urlencode($typeFilter) : ''; ?><?php echo !empty($statusFilter) ? '&status=' . urlencode($statusFilter) : ''; ?><?php echo !empty($priorityFilter) ? '&priority=' . urlencode($priorityFilter) : ''; ?><?php echo !empty($severityFilter) ? '&severity=' . urlencode($severityFilter) : ''; ?>">
                                        <?php echo $i; ?>
                                    </a>
                                </li>
                                <?php endfor; ?>
                                
                                <?php if ($page < $totalPages): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?page=<?php echo $page + 1; ?><?php echo !empty($search) ? '&search=' . urlencode($search) : ''; ?><?php echo !empty($typeFilter) ? '&type=' . urlencode($typeFilter) : ''; ?><?php echo !empty($statusFilter) ? '&status=' . urlencode($statusFilter) : ''; ?><?php echo !empty($priorityFilter) ? '&priority=' . urlencode($priorityFilter) : ''; ?><?php echo !empty($severityFilter) ? '&severity=' . urlencode($severityFilter) : ''; ?>">
                                        Next
                                    </a>
                                </li>
                                <?php endif; ?>
                            </ul>
                        </nav>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include('footer.php'); ?>
