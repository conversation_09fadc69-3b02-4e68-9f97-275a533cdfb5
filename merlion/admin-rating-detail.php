<?php
include('../functions/server.php');
include('../functions/timezone-helper.php');
session_start();

// Check if admin is logged in
if (!isset($_SESSION['admin_username'])) {
    header("Location: admin-login");
    exit();
}

// Get admin session info
$admin_username = $_SESSION['admin_username'];
$admin_id = $_SESSION['admin_id'];
$admin_role = $_SESSION['admin_role'];

// Get rating ID from URL
$ratingId = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if ($ratingId <= 0) {
    header("Location: admin-ratings");
    exit();
}

// Get rating details with ticket and user information
$ratingQuery = "SELECT * FROM ticket_ratings WHERE id = $ratingId LIMIT 1";
$ratingResult = mysqli_query($conn, $ratingQuery);
if (!$ratingResult || mysqli_num_rows($ratingResult) == 0) {
    header("Location: admin-ratings");
    exit();
}
$rating = mysqli_fetch_assoc($ratingResult);

if (!empty($rating['is_appika_ticket'])) {
    // Appika ticket: fetch ticket info from Appika API and user info from DB
    require_once '../functions/graphql_functions.php';
    $appikaId = $rating['appika_ticket_id'];
    $query = '
    query GetTicket($id: Int!) {
        getTicket(id: $id) {
            id
            subject
            type
            status
            priority
            created
            updated
        }
    }';
    $variables = ['id' => (int)$appikaId];
    $result = makeGraphQLRequest($query, $variables);
    if ($result['success'] && isset($result['data']['data']['getTicket'])) {
        $ticket = $result['data']['data']['getTicket'];
        $rating['ticket_id'] = $ticket['id'];
        $rating['subject'] = $ticket['subject'];
        $typeMapping = [1 => 'Starter', 2 => 'Business', 3 => 'Ultimate'];
        $rating['ticket_type'] = $typeMapping[$ticket['type']] ?? $ticket['type'];
        $rating['status'] = $ticket['status'];
        $rating['priority'] = $ticket['priority'] ?? '';
        $rating['ticket_created'] = $ticket['created'];
        $rating['ticket_updated'] = $ticket['updated'] ?? $ticket['created'];
    } else {
        $rating['subject'] = '[Appika ticket not found]';
        $rating['ticket_type'] = '';
        $rating['status'] = '';
        $rating['priority'] = '';
        $rating['ticket_created'] = '';
        $rating['ticket_updated'] = '';
    }
    // Fetch user info
    $userId = $rating['user_id'];
    $userResult = mysqli_query($conn, "SELECT * FROM user WHERE id = $userId LIMIT 1");
    $user = mysqli_fetch_assoc($userResult);
    $rating['username'] = $user['username'] ?? '';
    $rating['email'] = $user['email'] ?? '';
} else {
    // Local ticket: fetch with join as before
    $ratingQuery = "
        SELECT 
            tr.*,
            st.id as ticket_id,
            st.subject,
            st.ticket_type,
            st.status,
            st.priority,
            st.severity,
            st.description,
            st.created_at as ticket_created,
            st.updated_at as ticket_updated,
            u.username,
            u.email,
            u.id as user_id
        FROM ticket_ratings tr
        JOIN support_tickets st ON tr.ticket_id = st.id
        JOIN user u ON tr.user_id = u.id
        WHERE tr.id = $ratingId
        LIMIT 1
    ";
    $ratingResult = mysqli_query($conn, $ratingQuery);
    if (!$ratingResult || mysqli_num_rows($ratingResult) == 0) {
        header("Location: admin-ratings");
        exit();
    }
    $rating = mysqli_fetch_assoc($ratingResult);
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin - Rating Detail</title>
    <link rel="shortcut icon" href="../image/png/favicon.png" type="image/x-icon">
    <!-- Bootstrap, fonts & icons -->
    <link rel="stylesheet" href="../css/bootstrap.css">
    <link rel="stylesheet" href="../fonts/icon-font/css/style.css">
    <link rel="stylesheet" href="../fonts/typography-font/typo.css">
    <link rel="stylesheet" href="../fonts/fontawesome-5/css/all.css">
    <!-- Vendor stylesheets -->
    <link rel="stylesheet" href="../css/main.css">
    
    <style>
        body {
            background-color: #f8f9fa;
        }

        .admin-container {
            padding: 20px;
            max-width: 100%;
            overflow-x: hidden;
        }

        .admin-header {
            background-color: #fff;
            padding: 15px 20px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
        }

        .admin-header h1 {
            margin: 0;
            font-size: 24px;
            color: #333;
        }

        .admin-user {
            display: flex;
            align-items: center;
        }

        .admin-user span {
            margin-right: 10px;
        }

        .admin-content {
            background-color: #fff;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            padding: 20px;
            min-height: calc(100vh - 140px);
            overflow-y: auto;
            overflow-x: hidden;
        }

        .rating-detail-container {
            max-width: 800px;
            margin: 0 auto;
        }

        .rating-header {
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 1.5rem;
        }

        .rating-header .btn {
            position: absolute;
            left: 0;
            flex-shrink: 0;
        }

        .rating-header h2 {
            margin: 0;
            text-align: center;
        }

        .back-btn-40 {
            width: 40px !important;
            height: 40px !important;
            padding: 0 !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            min-width: 40px !important;
            min-height: 40px !important;
            max-width: 40px !important;
            max-height: 40px !important;
        }

        .ticket-info {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .rating-display {
            background-color: #e7f3ff;
            border: 2px solid #007bff;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .star-display {
            color: #ffc107;
            font-size: 1.5rem;
            margin-bottom: 10px;
        }

        .star-display .empty {
            color: #ddd;
        }

        .status-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
        }

        .status-resolved {
            background-color: #28a745;
            color: white;
        }

        .status-closed {
            background-color: #6c757d;
            color: white;
        }

        .ticket-type-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: bold;
            text-transform: uppercase;
        }

        .type-starter {
            background-color: #fbbf24;
            color: #fff;
        }

        .type-premium {
            background-color: #01A7E1;
            color: #fff;
        }

        .type-ultimate {
            background-color: #793BF0;
            color: #fff;
        }

        /* User dropdown styles */
        .user-dropdown {
            position: relative;
            display: inline-block;
        }

        .user-info {
            margin-right: 10px;
            font-size: 14px;
            cursor: pointer;
            display: flex;
            align-items: center;
        }

        .mobile-menu-toggle {
            margin-left: 5px;
            transition: transform 0.3s ease;
            display: none;
        }

        .mobile-menu-toggle.active {
            transform: rotate(180deg);
        }

        .dropdown-content {
            display: block;
        }

        /* Responsive container */
        @media (max-width: 991px) {
            .admin-container {
                padding: 15px;
            }
        }

        @media (max-width: 767px) {
            .admin-container {
                padding: 10px;
            }

            .admin-header {
                padding: 12px 15px;
            }

            .admin-content {
                padding: 15px;
            }

            .mobile-menu-toggle {
                display: inline-block;
            }

            .dropdown-content {
                display: none;
                position: absolute;
                background-color: #f9f9f9;
                width: 100%;
                right: 0;
                box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.2);
                z-index: 1;
                border-radius: 4px;
                margin-top: 5px;
                padding: 8px;
            }

            .user-dropdown.active .dropdown-content {
                display: block;
            }

            .rating-header {
                flex-direction: column;
                align-items: center;
                gap: 15px;
                justify-content: flex-start;
            }

            .rating-header .btn {
                position: static;
                align-self: flex-start;
            }

            .rating-header h2 {
                font-size: 1.5rem;
                text-align: center !important;
            }
        }

        /* Font size adjustments */
        h5 {
            font-size: 16px !important;
        }

        p, span, small, label, .form-control, .btn, .text-muted, .alert, div:not(.rating-detail-container):not(.ticket-info):not(.rating-display):not(.rating-header) {
            font-size: 14px !important;
        }

        .form-text {
            font-size: 14px !important;
        }

        .status-badge, .ticket-type-badge {
            font-size: 14px !important;
        }
    </style>
</head>

<body>
    <div class="admin-container">
        <div class="admin-header">
            <h1>Rating Detail</h1>
            <div class="admin-user">
                <div class="user-dropdown">
                    <span class="user-info">
                        Welcome, <?php echo htmlspecialchars($admin_username); ?>
                        (<?php echo htmlspecialchars($admin_role); ?>)
                        <i class="mobile-menu-toggle fas fa-chevron-down"></i>
                    </span>
                    <div class="dropdown-content">
                        <a href="admin-logout.php" class="btn btn-sm btn-outline-danger">Logout</a>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-2 col-md-3 col-sm-12">
                <?php include('admin-menu.php'); ?>
            </div>

            <div class="col-lg-10 col-md-9 col-sm-12">
                <div class="admin-content">
                    <div class="rating-detail-container">
                        <!-- Header with back button -->
                        <div class="rating-header">
                            <a href="admin-ratings" class="btn btn-outline-primary back-btn-40 mr-3">
                                <i class="fas fa-arrow-left"></i>
                            </a>
                            <h2 class="mb-0">
                                <i class="fas fa-star text-warning"></i>
                                Customer Rating Details
                            </h2>
                        </div>

                        <!-- Ticket Information -->
                        <div class="ticket-info">
                            <div class="row">
                                <div class="col-md-8">
                                    <h5><strong>Ticket #<?php echo $rating['ticket_id']; ?>:</strong> <?php echo htmlspecialchars($rating['subject']); ?></h5>
                                    <p class="mb-1"><strong>Customer:</strong> 
                                        <a href="admin-user-detail?id=<?php echo $rating['user_id']; ?>" class="text-decoration-none">
                                            <?php echo htmlspecialchars($rating['username']); ?> (HC<?php echo $rating['user_id']; ?>)
                                        </a>
                                    </p>
                                    <p class="mb-1"><strong>Email:</strong> <?php echo htmlspecialchars($rating['email']); ?></p>
                                    <p class="mb-1"><strong>Type:</strong> 
                                        <span class="ticket-type-badge type-<?php echo $rating['ticket_type']; ?>">
                                            <?php echo ucfirst($rating['ticket_type']); ?>
                                        </span>
                                    </p>
                                    <p class="mb-0"><strong>Created:</strong> <?php echo showCustomerTime($rating['ticket_created']); ?></p>
                                </div>
                                <div class="col-md-4 text-md-right">
                                    <span class="status-badge status-<?php echo $rating['status']; ?>">
                                        <?php echo ucfirst($rating['status']); ?>
                                    </span>
                                    <div class="mt-2">
                                        <a href="admin-ticket-detail?id=<?php echo $rating['ticket_id']; ?>" class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-ticket-alt"></i> &nbsp; View Ticket
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Rating Display -->
                        <div class="rating-display">
                            <h5><i class="fas fa-check-circle text-success"></i> Customer Rating</h5>
                            <div class="star-display mb-3">
                                <?php for ($i = 1; $i <= 5; $i++): ?>
                                    <i class="fas fa-star <?php echo $i <= $rating['rating'] ? '' : 'empty'; ?>"></i>
                                <?php endfor; ?>
                                <span class="ml-2 text-muted">(<?php echo $rating['rating']; ?>/5 stars)</span>
                            </div>
                            
                            <?php if (!empty($rating['comment'])): ?>
                            <div class="mb-3">
                                <strong>Customer Comment:</strong>
                                <div class="mt-2 p-3" style="background-color: #fff; border-radius: 5px; border-left: 4px solid #ffc107;">
                                    <?php echo nl2br(htmlspecialchars($rating['comment'])); ?>
                                </div>
                            </div>
                            <?php endif; ?>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <small class="text-muted">
                                        <i class="fas fa-clock"></i>
                                        <strong>Rated on:</strong> <?php echo showCustomerTime($rating['created_at']); ?>
                                    </small>
                                </div>
                                <div class="col-md-6">
                                    <?php if ($rating['created_at'] != $rating['updated_at']): ?>
                                    <small class="text-muted">
                                        <i class="fas fa-edit"></i>
                                        <strong>Last updated:</strong> <?php echo showCustomerTime($rating['updated_at']); ?>
                                    </small>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>

                        <!-- Admin Actions -->
                        <div class="text-center">
                            <button type="button" class="btn btn-primary mr-2"
                                    onclick="editRating(<?php echo $rating['id']; ?>, <?php echo $rating['rating']; ?>, '<?php echo addslashes($rating['comment']); ?>')">
                                <i class="fas fa-edit"></i> &nbsp; Edit Rating
                            </button>
                            <button type="button" class="btn btn-danger"
                                    onclick="deleteRating(<?php echo $rating['id']; ?>, '<?php echo htmlspecialchars($rating['subject']); ?>')">
                                <i class="fas fa-trash"></i> &nbsp; Delete Rating
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit Rating Modal -->
    <div class="modal fade" id="editRatingModal" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Edit Rating</h5>
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <form method="POST" action="admin-ratings">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="edit">
                        <input type="hidden" name="rating_id" id="edit_rating_id">

                        <div class="form-group">
                            <label for="edit_rating">Rating (1-5 stars):</label>
                            <select name="new_rating" id="edit_rating" class="form-control" required>
                                <option value="1">1 Star - Poor</option>
                                <option value="2">2 Stars - Fair</option>
                                <option value="3">3 Stars - Good</option>
                                <option value="4">4 Stars - Very Good</option>
                                <option value="5">5 Stars - Excellent</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="edit_comment">Comment:</label>
                            <textarea name="new_comment" id="edit_comment" class="form-control" rows="4"
                                      placeholder="Customer's comment about their experience..."></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-danger" data-dismiss="modal" style="background-color: #dc3545; color: white;">Cancel</button>
                        <button type="submit" class="btn btn-primary">Update Rating</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Delete Rating Modal -->
    <div class="modal fade" id="deleteRatingModal" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title" style="color: white;">Delete Rating</h5>
                    <button type="button" class="close text-white" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <form method="POST" action="admin-ratings">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="delete">
                        <input type="hidden" name="rating_id" id="delete_rating_id">

                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle"></i>
                            <strong>Warning:</strong> This action cannot be undone.
                        </div>

                        <p>Are you sure you want to delete the rating for:</p>
                        <p><strong id="delete_ticket_subject"></strong></p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn" data-dismiss="modal" style="background-color: #6c757d; color: white;">Cancel</button>
                        <button type="submit" class="btn btn-danger">Delete Rating</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="../js/vendor.min.js"></script>
    <script>
        function editRating(ratingId, currentRating, currentComment) {
            document.getElementById('edit_rating_id').value = ratingId;
            document.getElementById('edit_rating').value = currentRating;
            document.getElementById('edit_comment').value = currentComment;
            $('#editRatingModal').modal('show');
        }

        function deleteRating(ratingId, ticketSubject) {
            document.getElementById('delete_rating_id').value = ratingId;
            document.getElementById('delete_ticket_subject').textContent = 'Ticket: ' + ticketSubject;
            $('#deleteRatingModal').modal('show');
        }

        // User dropdown functionality
        document.addEventListener('DOMContentLoaded', function() {
            const userDropdown = document.querySelector('.user-dropdown');
            const userInfo = document.querySelector('.user-info');
            const menuToggle = document.querySelector('.mobile-menu-toggle');

            // Only apply dropdown functionality on mobile
            if (window.innerWidth <= 767) {
                userInfo.addEventListener('click', function(e) {
                    e.preventDefault();
                    userDropdown.classList.toggle('active');
                    menuToggle.classList.toggle('active');
                });

                // Close dropdown when clicking outside
                document.addEventListener('click', function(e) {
                    if (!userDropdown.contains(e.target)) {
                        userDropdown.classList.remove('active');
                        menuToggle.classList.remove('active');
                    }
                });
            }
        });
    </script>
</body>
</html>
