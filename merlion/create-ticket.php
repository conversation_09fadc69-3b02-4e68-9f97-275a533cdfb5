<?php
include('../functions/server.php');
session_start();

if (!isset($_SESSION['username'])) {
    header("Location: index");
    exit();
}

$username = $_SESSION['username'];

// Fetch user info
$userQuery = "SELECT * FROM user WHERE username = '$username'";
$userResult = mysqli_query($conn, $userQuery);
$user = mysqli_fetch_assoc($userResult);
$userId = $user['id'] ?? 0;

// Get the latest purchase to show "- New" indicator
$latestPurchaseQuery = "SELECT DISTINCT ticket_type, MAX(purchase_time) as latest_purchase_time
                       FROM purchasetickets
                       WHERE username = '$username'
                       AND purchase_time = (
                           SELECT MAX(purchase_time) FROM purchasetickets
                           WHERE username = '$username'
                       )
                       GROUP BY ticket_type";
$latestPurchaseResult = mysqli_query($conn, $latestPurchaseQuery);
$latestPurchaseTypes = [];
$latestPurchaseTime = null;

if ($latestPurchaseResult) {
    while ($row = mysqli_fetch_assoc($latestPurchaseResult)) {
        $latestPurchaseTypes[] = strtolower($row['ticket_type']);
        if (!$latestPurchaseTime) {
            $latestPurchaseTime = $row['latest_purchase_time'];
        }
    }
}

// Check which ticket types have been used since the latest purchase
$usedTicketTypes = [];
if ($latestPurchaseTime && !empty($latestPurchaseTypes)) {
    $usedTicketsQuery = "SELECT DISTINCT ticket_type FROM support_tickets
                        WHERE user_id = $userId
                        AND created_at > '$latestPurchaseTime'";
    $usedTicketsResult = mysqli_query($conn, $usedTicketsQuery);

    if ($usedTicketsResult) {
        while ($row = mysqli_fetch_assoc($usedTicketsResult)) {
            $usedTicketTypes[] = strtolower($row['ticket_type']);
        }
    }
}

// Filter out ticket types that have been used since latest purchase
$newTicketTypes = array_diff($latestPurchaseTypes, $usedTicketTypes);

// Check if user has any tickets available
$hasTickets = ($user['starter_tickets'] > 0) || ($user['premium_tickets'] > 0) || ($user['ultimate_tickets'] > 0);

// Initialize message
$message = "";

// Flag to show success animation
$showSuccessAnimation = false;

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Initialize variables
    $subject = mysqli_real_escape_string($conn, $_POST['subject'] ?? '');
    $priority = mysqli_real_escape_string($conn, $_POST['priority'] ?? '');
    $ticketType = mysqli_real_escape_string($conn, $_POST['ticket_type'] ?? '');
    $severity = mysqli_real_escape_string($conn, $_POST['severity'] ?? '');
    $problemType = mysqli_real_escape_string($conn, $_POST['problem_type'] ?? '');
    $description = mysqli_real_escape_string($conn, $_POST['description'] ?? '');

    // Validate problem type is not empty
    if (empty($problemType)) {
        $message = "Please select a problem type.";
    } else {
        // Check if user has remaining tickets of selected type
        $remaining = $user[$ticketType . '_tickets'] ?? 0;

        if ($remaining > 0) {
            // Insert ticket with description and problem type
            $insert = "INSERT INTO support_tickets (user_id, subject, priority, ticket_type, severity, problem_type, description, status, created_at)
                       VALUES ($userId, '$subject', '$priority', '$ticketType',  '$severity', '$problemType','$description', 'open', NOW())";

            if (mysqli_query($conn, $insert)) {
                // Get the newly created ticket ID
                $ticketId = mysqli_insert_id($conn);

                // Use FIFO ticket system
                require_once('../functions/ticket-expiration-functions.php');
                $ticket_used = useTicketsFIFO($user['username'], $ticketType, 1);

                if (!$ticket_used) {
                    // Fallback to old system if FIFO fails
                    $update = "UPDATE user SET {$ticketType}_tickets = {$ticketType}_tickets - 1 WHERE id = $userId";
                    mysqli_query($conn, $update);
                }

                // Success - no logging needed (only log failures for admin review)

                // admin_notifications table removed - notifications handled by support_tickets.is_seen_by_admin system

                // Set flag to show success animation
                $showSuccessAnimation = true;
            } else {
                $message = "Failed to create ticket.";
            }
        } else {
            $message = "You don't have remaining $ticketType tickets.";
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <title>Create New Support Ticket</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link rel="shortcut icon" href="../image/png/favicon.png" type="image/x-icon">
    <!-- Bootstrap and plugins -->
    <link rel="stylesheet" href="../css/bootstrap.css">
    <link rel="stylesheet" href="../css/main.css">
    <link rel="stylesheet" href="../fonts/icon-font/css/style.css">
    <link rel="stylesheet" href="../fonts/typography-font/typo.css">
    <link rel="stylesheet" href="../fonts/fontawesome-5/css/all.css">
    <link rel="stylesheet" href="../plugins/aos/aos.min.css">
    <link rel="stylesheet" href="../plugins/fancybox/jquery.fancybox.min.css">
    <link rel="stylesheet" href="../plugins/nice-select/nice-select.min.css">
    <link rel="stylesheet" href="../plugins/slick/slick.min.css">
    <link rel="stylesheet" href="../plugins/date-picker/css/gijgo.min.css" type="text/css" />
    <link rel="stylesheet" href="../plugins/theme-mode-switcher/switcher-panel.css">
    <link rel="stylesheet" href="../css/theme-mode-custom.css">
    <style>
    body {
        padding-top: 200px;
    }

    .container {
        width: 1200px;
        max-width: 95%;
    }

    /* Make site-wrapper wider */
    .site-wrapper {
        max-width: 100% !important;
        width: 100% !important;
        overflow-x: visible !important;
    }

    /* Responsive styles */
    @media (max-width: 991px) {
        body {
            padding-top: 150px;
        }
    }

    @media (max-width: 767px) {
        body {
            padding-top: 120px;
        }
    }

    @media (max-width: 575px) {
        .btn {
            padding: 0.5rem;
            font-size: 0.9rem;
        }
    }

    /* Menu open state for mobile */
    body.menu-open {
        overflow: hidden;
    }

    /* Custom Dropdown Styling */
    .custom-dropdown-container {
        position: relative;
    }

    .custom-dropdown {
        position: relative;
    }

    .dropdown-toggle-btn {
        cursor: pointer;
        text-align: left;
        position: relative;
        padding-right: 30px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .dropdown-toggle-btn::after {
        content: '\f107';
        font-family: 'Font Awesome 5 Free';
        font-weight: 900;
        position: absolute;
        right: 12px;
        top: 50%;
        transform: translateY(-50%);
        transition: transform 0.2s ease;
    }

    .custom-dropdown.open .dropdown-toggle-btn::after {
        transform: translateY(-50%) rotate(180deg);
    }

    .dropdown-menu {
        display: none;
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        z-index: 1000;
        background-color: #fff;
        border: 1px solid rgba(0, 0, 0, 0.15);
        border-radius: 4px;
        box-shadow: 0 6px 12px rgba(0, 0, 0, 0.175);
        max-height: 300px;
        overflow-y: auto;
        margin-top: 5px;
    }

    .custom-dropdown.open .dropdown-menu {
        display: block;
    }

    .dropdown-search {
        background-color: #fff;
        /* Removed sticky positioning */
        padding: 8px;
        border-bottom: 1px solid #ddd;
    }

    #problem_type_search:focus {
        border-color: #473BF0;
        box-shadow: 0 0 0 0.2rem rgba(71, 59, 240, 0.25);
        outline: none;
    }

    .dropdown-options {
        max-height: 250px;
        overflow-y: auto;
        overflow-x: hidden;
        position: relative;
        /* Ensure proper positioning of children */
    }

    .dropdown-item {
        padding: 8px 16px;
        cursor: pointer;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .dropdown-item:hover {
        background-color: #f8f9fa;
    }

    .dropdown-item.active {
        background-color: #473BF0;
        color: white;
    }

    .dropdown-item.hidden {
        display: none;
    }

    /* Category header styling */
    .dropdown-category-header {
        padding: 10px 16px;
        font-weight: bold;
        background-color: #473BF0;
        color: white;
        border-bottom: 1px solid #3730c0;
        font-size: 14px;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        /* Removed sticky positioning */
    }

    .dropdown-category-header.hidden {
        display: none;
    }

    /* Improve dropdown item styling */
    .dropdown-item {
        padding: 10px 16px;
        cursor: pointer;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        border-bottom: 1px solid #f0f0f0;
    }

    /* Remove hover effect but keep active state */
    .dropdown-item:hover {
        background-color: transparent;
    }

    /* Style for selected item */
    .dropdown-item.active {
        background-color: #473BF0;
        color: white;
    }

    /* Custom Alert Popup Styling */
    .custom-alert {
        display: none;
        position: fixed;
        top: 20%;
        left: 50%;
        transform: translateX(-50%);
        min-width: 300px;
        max-width: 500px;
        background-color: #fff;
        border-radius: 8px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        z-index: 9999;
        overflow: hidden;
        animation: alertFadeIn 0.3s ease-out;
    }

    .custom-alert.show {
        display: block;
    }

    .custom-alert-header {
        padding: 15px 20px;
        background-color: #f8d7da;
        color: #721c24;
        font-weight: bold;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .custom-alert-header i {
        margin-right: 10px;
        font-size: 20px;
    }

    .custom-alert-body {
        padding: 20px;
        color: #333;
    }

    .custom-alert-footer {
        padding: 10px 20px 15px;
        text-align: right;
    }

    .custom-alert-btn {
        padding: 8px 16px;
        background-color: #473BF0;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-weight: 500;
        transition: all 0.2s ease;
    }

    .custom-alert-btn:hover {
        background-color: #3b31c8;
        transform: translateY(-2px);
        box-shadow: 0 2px 5px rgba(71, 59, 240, 0.3);
    }

    .custom-alert-overlay {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 9998;
    }

    .custom-alert-overlay.show {
        display: block;
    }

    @keyframes alertFadeIn {
        from {
            opacity: 0;
            transform: translate(-50%, -20px);
        }

        to {
            opacity: 1;
            transform: translate(-50%, 0);
        }
    }
    </style>
</head>

<body>

    <?php include('../header-footer/header.php'); ?>

    <div class="container">
        <div class="row">
            <!-- Sidebar - Hidden on mobile, visible on larger screens -->
            <div class="col-lg-3 col-md-4 d-none d-md-block mb-4">
                <?php include('user-menu.php'); ?>
            </div>

            <!-- Mobile menu - Visible only on mobile -->
            <div class="col-12 d-md-none mb-4">
                <?php include('user-menu.php'); ?>
            </div>

            <!-- Main content -->
            <div class="col-lg-9 col-md-8">
                <h2 class="text-center mb-4">Create New Support Ticket</h2>

                <?php if (!empty($message)) : ?>
                <div class="alert alert-warning text-center"><?php echo $message; ?></div>
                <?php endif; ?>

                <form method="POST" id="ticketForm" onsubmit="return false;">
                    <div class="form-group">
                        <label for="subject">Subject</label>
                        <input type="text" name="subject" class="form-control" required>
                    </div>

                    <!-- Form fields in a row -->
                    <div class="form-row">
                        <!-- Severity field -->
                        <div class="form-group col-md-4">
                            <label for="severity">Severity</label>
                            <select name="severity" class="form-control" required>
                                <option value="">Select a severity level</option>
                                <option value="Information">Information (5 Business days)</option>
                                <option value="Minor">Minor (2 Business days)</option>
                                <option value="Important">Important (8 Business hours)</option>
                                <option value="Critical">Critical (4 Business hours)</option>
                            </select>
                        </div>

                        <!-- Priority field -->
                        <div class="form-group col-md-4">
                            <label for="priority">Priority</label>
                            <select name="priority" class="form-control" required>
                                <option value="">Select a priority level</option>
                                <option value="Information">Information (8 Business hours)</option>
                                <option value="Minor">Minor (8 Business hours)</option>
                                <option value="Important">Important (4 Business hours)</option>
                                <option value="Critical">Critical (2 Business hours)</option>
                            </select>
                        </div>

                        <!-- Ticket Type field -->
                        <div class="form-group col-md-4">
                            <label for="ticket_type">Ticket Type</label>
                            <select name="ticket_type" id="ticket_type" class="form-control" required>
                                <option value="starter" data-remaining="<?php echo $user['starter_tickets']; ?>">Starter (<?php echo $user['starter_tickets']; ?> remaining)<?php echo (in_array('starter', $newTicketTypes) && $user['starter_tickets'] > 0) ? ' - New' : ''; ?></option>
                                <option value="premium" data-remaining="<?php echo $user['premium_tickets']; ?>">Premium (<?php echo $user['premium_tickets']; ?> remaining)<?php echo (in_array('premium', $newTicketTypes) && $user['premium_tickets'] > 0) ? ' - New' : ''; ?></option>
                                <option value="ultimate" data-remaining="<?php echo $user['ultimate_tickets']; ?>">Ultimate (<?php echo $user['ultimate_tickets']; ?> remaining)<?php echo (in_array('ultimate', $newTicketTypes) && $user['ultimate_tickets'] > 0) ? ' - New' : ''; ?></option>
                            </select>
                        </div>
                    </div>

                    <!-- Problem Type Dropdown with Search -->
                    <div class="form-group">
                        <label for="problem_type_display">Problem Type</label>
                        <div class="custom-dropdown-container">
                            <!-- Hidden actual select that will be submitted with the form -->
                            <select name="problem_type" id="problem_type" class="d-none" required>
                                <option value="">Select a problem type...</option>
                                <option value="Account Access">Account Access</option>
                                <option value="Application Error">Application Error</option>
                                <option value="Audio/Sound Issues">Audio/Sound Issues</option>
                                <option value="Backup Failure">Backup Failure</option>
                                <option value="Billing/Finance">Billing/Finance</option>
                                <option value="Blue Screen Error">Blue Screen Error</option>
                                <option value="Cloud Storage Issues">Cloud Storage Issues</option>
                                <option value="Data Backup">Data Backup</option>
                                <option value="Data Migration">Data Migration</option>
                                <option value="Data Recovery">Data Recovery</option>
                                <option value="Database Error">Database Error</option>
                                <option value="Device Connectivity">Device Connectivity</option>
                                <option value="Display/Monitor Issues">Display/Monitor Issues</option>
                                <option value="Email Problem">Email Problem</option>
                                <option value="File Corruption">File Corruption</option>
                                <option value="Hardware Failure">Hardware Failure</option>
                                <option value="Hardware Installation">Hardware Installation</option>
                                <option value="Hardware Issue">Hardware Issue</option>
                                <option value="Internet Connection">Internet Connection</option>
                                <option value="Keyboard/Mouse Issues">Keyboard/Mouse Issues</option>
                                <option value="License/Activation">License/Activation</option>
                                <option value="Mobile Device Issue">Mobile Device Issue</option>
                                <option value="Network Configuration">Network Configuration</option>
                                <option value="Network Problem">Network Problem</option>
                                <option value="Operating System Error">Operating System Error</option>
                                <option value="Password Reset">Password Reset</option>
                                <option value="Performance Issue">Performance Issue</option>
                                <option value="Printer Issue">Printer Issue</option>
                                <option value="Remote Access Problem">Remote Access Problem</option>
                                <option value="Security Concern">Security Concern</option>
                                <option value="Server Down">Server Down</option>
                                <option value="Software Compatibility">Software Compatibility</option>
                                <option value="Software Installation">Software Installation</option>
                                <option value="Software Issue">Software Issue</option>
                                <option value="Software Update">Software Update</option>
                                <option value="Storage Full">Storage Full</option>
                                <option value="System Crash">System Crash</option>
                                <option value="System Upgrade">System Upgrade</option>
                                <option value="User Training Request">User Training Request</option>
                                <option value="VPN Connection Issue">VPN Connection Issue</option>
                                <option value="Virus/Malware">Virus/Malware</option>
                                <option value="Website Error">Website Error</option>
                                <option value="Wi-Fi Connectivity">Wi-Fi Connectivity</option>
                                <option value="Other">Other</option>
                            </select>

                            <!-- Custom dropdown display -->
                            <div class="custom-dropdown">
                                <div class="dropdown-toggle-btn form-control" id="problem_type_display">Select a problem
                                    type...</div>
                                <div class="dropdown-menu">
                                    <div class="dropdown-search p-2">
                                        <input type="text" class="form-control" id="problem_type_search"
                                            placeholder="Search problem types...">
                                    </div>
                                    <div class="dropdown-divider"></div>
                                    <div class="dropdown-options">
                                        <div class="dropdown-item" data-value="">Select a problem type...</div>
                                        <div class="dropdown-item" data-value="Account Access">Account Access</div>
                                        <div class="dropdown-item" data-value="Application Error">Application Error
                                        </div>
                                        <div class="dropdown-item" data-value="Audio/Sound Issues">Audio/Sound Issues
                                        </div>
                                        <div class="dropdown-item" data-value="Backup Failure">Backup Failure</div>
                                        <div class="dropdown-item" data-value="Billing/Finance">Billing/Finance</div>
                                        <div class="dropdown-item" data-value="Blue Screen Error">Blue Screen Error
                                        </div>
                                        <div class="dropdown-item" data-value="Cloud Storage Issues">Cloud Storage
                                            Issues</div>
                                        <div class="dropdown-item" data-value="Data Backup">Data Backup</div>
                                        <div class="dropdown-item" data-value="Data Migration">Data Migration</div>
                                        <div class="dropdown-item" data-value="Data Recovery">Data Recovery</div>
                                        <div class="dropdown-item" data-value="Database Error">Database Error</div>
                                        <div class="dropdown-item" data-value="Device Connectivity">Device Connectivity
                                        </div>
                                        <div class="dropdown-item" data-value="Display/Monitor Issues">Display/Monitor
                                            Issues</div>
                                        <div class="dropdown-item" data-value="Email Problem">Email Problem</div>
                                        <div class="dropdown-item" data-value="File Corruption">File Corruption</div>
                                        <div class="dropdown-item" data-value="Hardware Failure">Hardware Failure</div>
                                        <div class="dropdown-item" data-value="Hardware Installation">Hardware
                                            Installation</div>
                                        <div class="dropdown-item" data-value="Hardware Issue">Hardware Issue</div>
                                        <div class="dropdown-item" data-value="Internet Connection">Internet Connection
                                        </div>
                                        <div class="dropdown-item" data-value="Keyboard/Mouse Issues">Keyboard/Mouse
                                            Issues</div>
                                        <div class="dropdown-item" data-value="License/Activation">License/Activation
                                        </div>
                                        <div class="dropdown-item" data-value="Mobile Device Issue">Mobile Device Issue
                                        </div>
                                        <div class="dropdown-item" data-value="Network Configuration">Network
                                            Configuration</div>
                                        <div class="dropdown-item" data-value="Network Problem">Network Problem</div>
                                        <div class="dropdown-item" data-value="Operating System Error">Operating System
                                            Error</div>
                                        <div class="dropdown-item" data-value="Password Reset">Password Reset</div>
                                        <div class="dropdown-item" data-value="Performance Issue">Performance Issue
                                        </div>
                                        <div class="dropdown-item" data-value="Printer Issue">Printer Issue</div>
                                        <div class="dropdown-item" data-value="Remote Access Problem">Remote Access
                                            Problem</div>
                                        <div class="dropdown-item" data-value="Security Concern">Security Concern</div>
                                        <div class="dropdown-item" data-value="Server Down">Server Down</div>
                                        <div class="dropdown-item" data-value="Software Compatibility">Software
                                            Compatibility</div>
                                        <div class="dropdown-item" data-value="Software Installation">Software
                                            Installation</div>
                                        <div class="dropdown-item" data-value="Software Issue">Software Issue</div>
                                        <div class="dropdown-item" data-value="Software Update">Software Update</div>
                                        <div class="dropdown-item" data-value="Storage Full">Storage Full</div>
                                        <div class="dropdown-item" data-value="System Crash">System Crash</div>
                                        <div class="dropdown-item" data-value="System Upgrade">System Upgrade</div>
                                        <div class="dropdown-item" data-value="User Training Request">User Training
                                            Request</div>
                                        <div class="dropdown-item" data-value="VPN Connection Issue">VPN Connection
                                            Issue</div>
                                        <div class="dropdown-item" data-value="Virus/Malware">Virus/Malware</div>
                                        <div class="dropdown-item" data-value="Website Error">Website Error</div>
                                        <div class="dropdown-item" data-value="Wi-Fi Connectivity">Wi-Fi Connectivity
                                        </div>
                                        <div class="dropdown-item" data-value="Other">Other</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Add Problem Description -->
                    <div class="form-group">
                        <label for="description">Problem Description</label>
                        <textarea name="description" class="form-control" rows="5" required
                            placeholder="Please describe your issue in detail."></textarea>
                    </div>

                    <div class="form-group row mt-12">
                        <div class="col-md-12 mb-3 mb-md-0 d-flex justify-content-center align-items-center">
                            <a href="my-ticket.php" class="btn btn-danger btn-lg w-auto mr-2"
                                style="border-radius: 6px; padding: 10px 15px; font-weight: 500; transition: all 0.3s ease;">
                                Cancel
                            </a>
                            <button type="button" class="btn btn-lg w-auto" onclick="confirmSubmit()"
                                style="background-color: #473BF0; color: white; border-radius: 6px; padding: 10px 15px; font-weight: 600; transition: all 0.3s ease; box-shadow: 0 4px 6px rgba(71, 59, 240, 0.2);">
                                Create Ticket
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Confirmation Modal -->
    <div class="modal fade" id="confirmModal" tabindex="-1" role="dialog" aria-labelledby="confirmModalLabel"
        aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="confirmModalLabel">Confirm Ticket Creation</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    Are you sure you want to create this support ticket? This will use one of your available tickets.
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-danger text" data-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" onclick="submitForm()">Create Ticket</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Success Animation Modal -->
    <?php if ($showSuccessAnimation) : ?>
    <div class="modal fade" id="successModal" tabindex="-1" role="dialog" aria-hidden="true" data-backdrop="static">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-body text-center py-5">
                    <div class="success-animation">
                        <svg class="checkmark" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 52 52">
                            <circle class="checkmark__circle" cx="26" cy="26" r="25" fill="none" />
                            <path class="checkmark__check" fill="none" d="M14.1 27.2l7.1 7.2 16.7-16.8" />
                        </svg>
                    </div>
                    <h3 class="mt-4">Ticket Created Successfully!</h3>
                    <p class="mb-4">Your support ticket has been created and our team will respond shortly.</p>
                    <p class="text-muted">Redirecting to My Tickets in <span id="countdown">3</span> seconds...</p>
                    <a href="my-ticket.php" class="btn btn-primary">View My Tickets Now</a>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <style>
    /* Success Animation Styles */
    .success-animation {
        margin: 0 auto;
        width: 100px;
        height: 100px;
    }

    .checkmark {
        width: 100px;
        height: 100px;
        border-radius: 50%;
        display: block;
        stroke-width: 2;
        stroke: #4bb71b;
        stroke-miterlimit: 10;
        box-shadow: inset 0px 0px 0px #4bb71b;
        animation: fill .4s ease-in-out .4s forwards, scale .3s ease-in-out .9s both;
    }

    .checkmark__circle {
        stroke-dasharray: 166;
        stroke-dashoffset: 166;
        stroke-width: 1;
        /* ค่าตั้งต้น stroke-width */
        stroke-miterlimit: 2;
        stroke: #4bb71b;
        fill: none;
        animation: stroke 0.6s cubic-bezier(0.65, 0, 0.45, 1) forwards, resetStrokeWidth 0.1s 1s forwards;
        /* เพิ่มการ reset ค่า stroke-width หลังจากอนิเมชั่นเสร็จ */
    }

    .checkmark__check {
        transform-origin: 50% 50%;
        stroke-dasharray: 48;
        stroke-dashoffset: 48;
        animation: stroke 0.3s cubic-bezier(0.65, 0, 0.45, 1) 0.8s forwards;
    }

    @keyframes stroke {
        100% {
            stroke-dashoffset: 0;
        }
    }

    @keyframes scale {

        0%,
        100% {
            transform: none;
        }

        50% {
            transform: scale3d(1.1, 1.1, 1);
        }
    }

    /* แก้ตรงนี้ถ้าขอบหนา */
    @keyframes fill {
        100% {
            box-shadow: inset 0px 0px 0px 1px #4bb71b;
        }
    }

    /* Animation to reset stroke-width back to its initial value */
    @keyframes resetStrokeWidth {
        0% {
            stroke-width: 1;
        }

        100% {
            stroke-width: 1;
            /* รีเซ็ต stroke-width ให้กลับเป็น 1 */
        }
    }
    </style>

    <!-- Vendor Scripts -->
    <script src="../js/vendor.min.js"></script>
    <!-- Plugin's Scripts -->
    <script src="../plugins/fancybox/jquery.fancybox.min.js"></script>
    <script src="../plugins/nice-select/jquery.nice-select.min.js"></script>
    <script src="../plugins/aos/aos.min.js"></script>
    <script src="../plugins/slick/slick.min.js"></script>
    <script src="../plugins/date-picker/js/gijgo.min.js"></script>
    <script src="../plugins/counter-up/jquery.waypoints.min.js"></script>
    <script src="../plugins/counter-up/jquery.counterup.min.js"></script>
    <script src="../plugins/theme-mode-switcher/gr-theme-mode-switcher.js"></script>
    <!-- Activation Script -->
    <!-- <script src="../js/custom.js"></script> -->

    <script>
    // Show success modal on page load if needed
    <?php if ($showSuccessAnimation) : ?>
    $(document).ready(function() {
        $('#successModal').modal('show');

        // Prevent form resubmission on refresh
        if (window.history.replaceState) {
            window.history.replaceState(null, null, window.location.href);
        }

        // Start countdown for automatic redirect
        var countdownElement = document.getElementById('countdown');
        var secondsLeft = 3;

        // Update countdown every second
        var countdownInterval = setInterval(function() {
            secondsLeft--;
            countdownElement.textContent = secondsLeft;

            if (secondsLeft <= 0) {
                clearInterval(countdownInterval);
                window.location.href = 'my-ticket.php';
            }
        }, 1000);

        // Add event listener to the success modal
        $('#successModal').on('hidden.bs.modal', function() {
            // Clear the interval if modal is closed manually
            clearInterval(countdownInterval);
            // Redirect to my-ticket.php when modal is closed
            window.location.href = 'my-ticket.php';
        });
    });
    <?php endif; ?>

    // Function to show custom alert
    function showAlert(message, title = 'Warning') {
        document.getElementById('alertTitle').textContent = title;
        document.getElementById('alertMessage').textContent = message;
        document.getElementById('alertOverlay').classList.add('show');
        document.getElementById('customAlert').classList.add('show');
    }

    // Function to close custom alert
    function closeAlert() {
        document.getElementById('alertOverlay').classList.remove('show');
        document.getElementById('customAlert').classList.remove('show');
    }

    // Function to validate form fields
    function validateForm() {
        var form = document.getElementById('ticketForm');
        var subject = form.elements['subject'].value.trim();
        var priority = form.elements['priority'].value;
        var severity = form.elements['severity'].value;
        var ticketType = form.elements['ticket_type'].value;
        var problemTypeValue = $('#problem_type').val();
        var description = form.elements['description'].value.trim();

        // Check subject
        if (!subject) {
            showAlert('Please enter a subject for your ticket.');
            form.elements['subject'].focus();
            return false;
        }

        // Check severity
        if (!severity) {
            showAlert('Please select a severity level.');
            form.elements['severity'].focus();
            return false;
        }

        // Check priority
        if (!priority) {
            showAlert('Please select a priority level.');
            form.elements['priority'].focus();
            return false;
        }

        // Check ticket type
        if (!ticketType) {
            showAlert('Please select a ticket type.');
            form.elements['ticket_type'].focus();
            return false;
        }

        // Check if selected ticket type has remaining tickets
        var selectedOption = form.elements['ticket_type'].options[form.elements['ticket_type'].selectedIndex];
        var remainingTickets = parseInt(selectedOption.getAttribute('data-remaining') || '0');

        if (remainingTickets <= 0) {
            var ticketTypeName = ticketType.charAt(0).toUpperCase() + ticketType.slice(1);
            if (ticketType === 'premium') ticketTypeName = 'Premium';

            showAlert(`Not enough ${ticketTypeName} tickets available. Please select another ticket type or purchase more tickets from our pricing page.`);
            form.elements['ticket_type'].focus();
            return false;
        }

        // Check problem type
        if (!problemTypeValue) {
            showAlert('Please select a problem type.');
            $('#problem_type_display').click(); // Open the dropdown
            setTimeout(function() {
                $('#problem_type_search').focus();
            }, 100);
            return false;
        }

        // Check description
        if (!description) {
            showAlert('Please provide a detailed description of your problem.');
            form.elements['description'].focus();
            return false;
        }

        return true;
    }

    // Function to show confirmation modal
    function confirmSubmit() {
        // Validate form first
        if (!validateForm()) {
            return;
        }

        // Show confirmation modal
        $('#confirmModal').modal('show');
    }

    // Function to submit the form after confirmation
    function submitForm() {
        // Hide the confirmation modal
        $('#confirmModal').modal('hide');

        // Submit the form
        document.getElementById('ticketForm').method = 'POST';
        document.getElementById('ticketForm').onsubmit = null;
        document.getElementById('ticketForm').submit();
    }
    </script>

    <!-- Custom Dropdown with Search Script -->
    <script>
    $(document).ready(function() {
        var $customDropdown = $('.custom-dropdown');
        var $dropdownToggle = $('.dropdown-toggle-btn');
        var $dropdownMenu = $('.dropdown-menu');
        var $dropdownItems = $('.dropdown-item');
        var $hiddenSelect = $('#problem_type');
        var $searchInput = $('#problem_type_search');
        var $ticketTypeSelect = $('#ticket_type');

        // Define problem types for each ticket type
        var problemTypesByTicket = {
            'starter': ['etc', 'PC Support'],
            'premium': ['etc', 'PC Support', 'Peripherals (printer, scanner, mobile)'],
            'ultimate': ['etc', 'PC Support', 'Peripherals (printer, scanner, mobile)', 'Servers',
                'Network'
            ]
        };

        // Map ticket types to their display names
        var ticketTypeNames = {
            'starter': 'Starter',
            'premium': 'Premium',
            'ultimate': 'Ultimate'
        };

        // Define problem types that belong to each category
        var problemTypeCategories = {
            'etc': [
                'Account Access', 'Billing/Finance', 'Email Problem', 'Password Reset',
                'Security Concern', 'Website Error', 'Other'
            ],
            'PC Support': [
                'Blue screen / system crash', 'Slow performance', 'Software not responding',
                'OS boot failure', 'Login issues (Windows/Mac)', 'File corruption / data loss',
                'Antivirus or malware infection', 'Unable to install/uninstall software',
                'System auto-restart / overheating', 'Driver issues or missing drivers'
            ],
            'Peripherals (printer, scanner, mobile)': [
                'Printer not detected', 'Scanner not responding', 'Mobile device not connecting to PC',
                'Printing blank pages', 'Paper jam errors', 'USB device not recognized',
                'Wireless printer setup issue', 'Mobile file transfer failure',
                'Ink/toner not recognized',
                'Device driver conflict', 'Mobile email not syncing', 'Bluetooth connectivity issue',
                'Touchscreen not responding', 'Device overheating', 'Battery draining quickly',
                'App crashes on mobile', 'Cannot update mobile OS', 'Device charging issue'
            ],
            'Network': [
                'No internet connection', 'Slow network speed', 'Cannot connect to Wi-Fi',
                'VPN not working', 'IP conflict', 'DNS resolution failure',
                'Network drive inaccessible',
                'Ethernet not detected', 'Frequent disconnections', 'Firewall blocking services',
                'Router configuration issues', 'Network printer problems', 'Network security breach',
                'Wireless signal interference', 'Port forwarding issues', 'Network adapter failure',
                'Internet service provider outage', 'Network cable damage', 'Switch/hub failure',
                'DHCP configuration problems', 'Network access control issues', 'Bandwidth throttling',
                'Network latency/high ping', 'Packet loss', 'MAC address filtering problems'
            ],
            'Servers': [
                'Backup Failure', 'Cloud Storage Issues', 'Database Error', 'Server Down'
            ]
        };

        // Common problem types available for all ticket types - now using the etc category
        var commonProblemTypes = [];

        // Function to update problem type options based on ticket type
        function updateProblemTypeOptions(ticketType) {
            // Clear current options except the placeholder
            $hiddenSelect.find('option:not(:first)').remove();
            $('.dropdown-options').find('.dropdown-item:not(:first)').remove();
            $('.dropdown-options').find('.dropdown-category-header').remove();

            // Get allowed categories for this ticket type
            var allowedCategories = problemTypesByTicket[ticketType] || [];

            // Add problem types from allowed categories
            var addedOptions = [];
            var addedCategories = [];

            // First add the etc category (always shown first)
            if (problemTypeCategories['etc'] && problemTypeCategories['etc'].length > 0) {
                // Add etc header
                $('.dropdown-options').append(
                    '<div class="dropdown-category-header">etc</div>'
                );

                // Add etc problem types
                problemTypeCategories['etc'].forEach(function(problemType) {
                    // Add to hidden select
                    $hiddenSelect.append('<option value="' + problemType + '">' + problemType +
                        '</option>');

                    // Add to visible dropdown
                    $('.dropdown-options').append(
                        '<div class="dropdown-item" data-value="' + problemType +
                        '" data-category="etc">' + problemType + '</div>'
                    );

                    addedOptions.push(problemType);
                });

                // Mark etc as added
                addedCategories.push('etc');
            }

            // Then add other category headers and their problem types
            allowedCategories.forEach(function(category) {
                // Skip if we've already added this category (like etc)
                if (addedCategories.indexOf(category) !== -1) {
                    return;
                }

                // Get problem types for this category
                var categoryProblemTypes = problemTypeCategories[category] || [];

                // Only add the category header if there are problem types in this category
                if (categoryProblemTypes.length > 0) {
                    // Add category header
                    $('.dropdown-options').append(
                        '<div class="dropdown-category-header">' + category + '</div>'
                    );

                    // Mark this category as added
                    addedCategories.push(category);

                    // Add problem types for this category
                    categoryProblemTypes.forEach(function(problemType) {
                        // Skip if we've already added this problem type
                        if (addedOptions.indexOf(problemType) !== -1) {
                            return;
                        }

                        // Add to hidden select
                        $hiddenSelect.append('<option value="' + problemType + '">' +
                            problemType + '</option>');

                        // Add to visible dropdown
                        $('.dropdown-options').append(
                            '<div class="dropdown-item" data-value="' + problemType +
                            '" data-category="' + category + '">' + problemType + '</div>'
                        );

                        addedOptions.push(problemType);
                    });
                }
            });

            // We no longer need to add common problem types here
            // since we're now using the etc category which is added at the beginning

            // Reset dropdown display
            $dropdownToggle.text('Select a problem type...');
            $hiddenSelect.val('');

            // Reattach click handlers for new items
            $dropdownItems = $('.dropdown-item');
            attachDropdownItemHandlers();

            // Log for debugging
            console.log('Updated problem types for ' + ticketTypeNames[ticketType] + ' ticket');
        }

        // Function to attach handlers to dropdown items
        function attachDropdownItemHandlers() {
            // Handle item selection
            $dropdownItems.off('click').on('click', function() {
                var value = $(this).data('value');
                var text = $(this).text();

                // Update the hidden select value
                $hiddenSelect.val(value);

                // Update the dropdown display
                $dropdownToggle.text(text);

                // Mark the selected item with active class for visual feedback
                $dropdownItems.removeClass('active');
                $(this).addClass('active');

                // Close the dropdown
                $customDropdown.removeClass('open');
            });
        }

        // Initialize problem types based on initial ticket type
        updateProblemTypeOptions($ticketTypeSelect.val());

        // Update problem types when ticket type changes
        $ticketTypeSelect.on('change', function() {
            updateProblemTypeOptions($(this).val());
        });

        // Toggle dropdown when clicking the button
        $dropdownToggle.on('click', function() {
            $customDropdown.toggleClass('open');
            if ($customDropdown.hasClass('open')) {
                $searchInput.focus();

                // When reopening, highlight the currently selected item
                var currentValue = $hiddenSelect.val();
                if (currentValue) {
                    $dropdownItems.removeClass('active');
                    $dropdownItems.filter('[data-value="' + currentValue + '"]').addClass('active');
                }
            }
        });

        // Close dropdown when clicking outside
        $(document).on('click', function(e) {
            if (!$(e.target).closest('.custom-dropdown').length) {
                $customDropdown.removeClass('open');
            }
        });

        // Attach initial handlers
        attachDropdownItemHandlers();

        // Search functionality
        $searchInput.on('input', function(e) {
            e.stopPropagation();
            var searchText = $(this).val().toLowerCase();

            // First hide/show category headers based on whether they have visible items
            $('.dropdown-category-header').each(function() {
                var $header = $(this);
                var $nextItems = $header.nextUntil('.dropdown-category-header');
                var hasVisibleItems = false;

                $nextItems.each(function() {
                    var itemText = $(this).text().toLowerCase();
                    var isVisible = itemText.indexOf(searchText) > -1 || $(this).data(
                        'value') === '';
                    $(this).toggleClass('hidden', !isVisible);
                    if (isVisible) hasVisibleItems = true;
                });

                $header.toggleClass('hidden', !hasVisibleItems);
            });

            // Handle the first placeholder item separately
            var $firstItem = $('.dropdown-item').first();
            if ($firstItem.data('value') === '') {
                $firstItem.removeClass('hidden');
            }
        });

        // Prevent dropdown from closing when clicking on search input
        $searchInput.on('click', function(e) {
            e.stopPropagation();
        });

        // Form validation - use our custom validation
        $('#ticketForm').on('submit', function(e) {
            if (!validateForm()) {
                e.preventDefault();
                return false;
            }
        });
    });
    </script>

    <!-- Custom Alert Popup -->
    <div class="custom-alert-overlay" id="alertOverlay"></div>
    <div class="custom-alert" id="customAlert">
        <div class="custom-alert-header">
            <div>
                <i class="fas fa-exclamation-triangle"></i>
                <span id="alertTitle">Warning</span>
            </div>
            <button type="button" class="close" onclick="closeAlert()">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
        <div class="custom-alert-body" id="alertMessage">
            Please fill in all required fields.
        </div>
        <div class="custom-alert-footer">
            <button type="button" class="custom-alert-btn" onclick="closeAlert()">OK</button>
        </div>
    </div>

</body>

</html>