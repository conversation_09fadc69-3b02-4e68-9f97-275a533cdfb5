<?php
// Force update ALL ticket expiration dates
include_once('../functions/server.php');
include_once('../config/ticket-expiration-config.php');

echo "<h2>Force Update All Ticket Expiration Dates</h2>";

// Get current configuration
$config = getTicketExpirationConfig();
echo "<p>Current settings: {$config['ticket_lifetime_months']} {$config['ticket_lifetime_unit']}</p>";

if (isset($_GET['confirm']) && $_GET['confirm'] === 'yes') {
    echo "<h3>Updating ALL tickets...</h3>";
    
    // Get all tickets
    $select_query = "SELECT purchase_time FROM purchasetickets";
    $select_result = mysqli_query($conn, $select_query);
    
    $updated_count = 0;
    $total_count = 0;
    
    if ($select_result && mysqli_num_rows($select_result) > 0) {
        while ($row = mysqli_fetch_assoc($select_result)) {
            $total_count++;
            $purchase_time = $row['purchase_time'];
            $new_expiration = getTicketExpirationDate($purchase_time);
            
            $update_query = "UPDATE purchasetickets 
                           SET expiration_date = ? 
                           WHERE purchase_time = ?";
            
            $stmt = mysqli_prepare($conn, $update_query);
            mysqli_stmt_bind_param($stmt, 'ss', $new_expiration, $purchase_time);
            
            if (mysqli_stmt_execute($stmt)) {
                $updated_count += mysqli_stmt_affected_rows($stmt);
            }
            mysqli_stmt_close($stmt);
        }
    }
    
    echo "<p style='color: green;'>✅ Updated $updated_count out of $total_count ticket records.</p>";
    
    // Check how many should be expired now
    $expired_check = "SELECT COUNT(*) as expired_count 
                     FROM purchasetickets 
                     WHERE expiration_date < NOW() 
                     AND remaining_tickets > 0";
    $expired_result = mysqli_query($conn, $expired_check);
    $expired_row = mysqli_fetch_assoc($expired_result);
    
    echo "<p style='color: blue;'>📊 Tickets that should now be expired: " . $expired_row['expired_count'] . "</p>";
    
    echo "<p><a href='debug-expiration.php'>Check Debug Page</a> | <a href='ticket-expiration-manager.php'>Back to Manager</a></p>";
    
} else {
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>⚠️ Warning</h3>";
    echo "<p>This will update ALL ticket expiration dates to use the current configuration:</p>";
    echo "<p><strong>{$config['ticket_lifetime_months']} {$config['ticket_lifetime_unit']}</strong></p>";
    echo "<p>This action cannot be undone. Are you sure?</p>";
    echo "<p><a href='?confirm=yes' style='background: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Yes, Update All Tickets</a></p>";
    echo "<p><a href='ticket-expiration-manager.php'>Cancel</a></p>";
    echo "</div>";
}
?>
