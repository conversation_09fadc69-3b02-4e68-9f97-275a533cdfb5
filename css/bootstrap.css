/*!
 * Bootstrap v4.5.3 (https://getbootstrap.com/)
 * Copyright 2011-2020 The Bootstrap Authors
 * Copyright 2011-2020 Twitter, Inc.
 * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)
 */
:root {
  --blue: #473bf0;
  --indigo: #6610f2;
  --purple: #6f42c1;
  --pink: #e83e8c;
  --red: #f64b4b;
  --orange: #fd7e14;
  --yellow: #f7e36d;
  --green: #68d585;
  --teal: #20c997;
  --cyan: #17a2b8;
  --white: #fff;
  --gray: #6c757d;
  --gray-dark: #343a40;
  --primary: #473bf0;
  --secondary: #68d585;
  --success: #68d585;
  --info: #17a2b8;
  --warning: #f7e36d;
  --danger: #f64b4b;
  --light: #f8f9fa;
  --dark: #343a40;
  --red: #f64b4b;
  --green: #68d585;
  --green-shamrock: #2bd67b;
  --blue: #473bf0;
  --sky-blue: #1082e9;
  --yellow: #f7e36d;
  --yellow-orange: #fcad38;
  --blackish-blue: #13151c;
  --black: #000;
  --mirage: #131829;
  --mirage-2: #161c2d;
  --white: #fff;
  --smoke: #f8f8f8;
  --storm: #7d818d;
  --ghost: #fdfdff;
  --gray-1: #fcfdfe;
  --gray-2: #f4f7fa;
  --gray-3: #e7e9ed;
  --gray-310: #d5d7dd;
  --gray-opacity: rgba(231, 233, 237, 0.13);
  --blackish-blue-opacity: rgba(22, 28, 45, 0.7);
  --narvik: #edf9f2;
  --breakpoint-xxs: 0px;
  --breakpoint-xs: 480px;
  --breakpoint-sm: 576px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 992px;
  --breakpoint-xl: 1200px;
  --font-family-sans-serif: -apple-system, BlinkMacSystemFont, 'Segoe UI',
    Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif,
    'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
  --font-family-monospace: SFMono-Regular, Menlo, Monaco, Consolas,
    'Liberation Mono', 'Courier New', monospace;
}

*,
*::before,
*::after {
  box-sizing: border-box;
}

html {
  font-family: sans-serif;
  line-height: 1.15;
  -webkit-text-size-adjust: 100%;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}

article,
aside,
figcaption,
figure,
footer,
header,
hgroup,
main,
nav,
section {
  display: block;
}

body {
  margin: 0;
  font-family: 'Circular Std', sans-serif;
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.5;
  color: var(--color-texts-opacity);
  text-align: left;
  background-color: var(--bg);
}

[tabindex='-1']:focus:not(:focus-visible) {
  outline: 0 !important;
}

hr {
  box-sizing: content-box;
  height: 0;
  overflow: visible;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  margin-top: 0;
  margin-bottom: 0.5rem;
}

p {
  margin-top: 0;
  margin-bottom: 1rem;
}

abbr[title],
abbr[data-original-title] {
  text-decoration: underline;
  -webkit-text-decoration: underline dotted;
  text-decoration: underline dotted;
  cursor: help;
  border-bottom: 0;
  -webkit-text-decoration-skip-ink: none;
  text-decoration-skip-ink: none;
}

address {
  margin-bottom: 1rem;
  font-style: normal;
  line-height: inherit;
}

ol,
ul,
dl {
  margin-top: 0;
  margin-bottom: 1rem;
}

ol ol,
ul ul,
ol ul,
ul ol {
  margin-bottom: 0;
}

dt {
  font-weight: 700;
}

dd {
  margin-bottom: 0.5rem;
  margin-left: 0;
}

blockquote {
  margin: 0 0 1rem;
}

b,
strong {
  font-weight: bolder;
}

small {
  font-size: 80%;
}

sub,
sup {
  position: relative;
  font-size: 75%;
  line-height: 0;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

a {
  color: #473bf0;
  text-decoration: none;
  background-color: transparent;
}

a:hover {
  color: #1c10cf;
  text-decoration: none;
}

a:not([href]):not([class]) {
  color: inherit;
  text-decoration: none;
}

a:not([href]):not([class]):hover {
  color: inherit;
  text-decoration: none;
}

pre,
code,
kbd,
samp {
  font-family: SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono',
    'Courier New', monospace;
  font-size: 1em;
}

pre {
  margin-top: 0;
  margin-bottom: 1rem;
  overflow: auto;
  -ms-overflow-style: scrollbar;
}

figure {
  margin: 0 0 1rem;
}

img {
  vertical-align: middle;
  border-style: none;
}

svg {
  overflow: hidden;
  vertical-align: middle;
}

table {
  border-collapse: collapse;
}

caption {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
  color: #6c757d;
  text-align: left;
  caption-side: bottom;
}

th {
  text-align: inherit;
  text-align: -webkit-match-parent;
}

label {
  display: inline-block;
  margin-bottom: 0.5rem;
}

button {
  border-radius: 0;
}

button:focus {
  outline: 1px dotted;
  outline: 5px auto -webkit-focus-ring-color;
}

input,
button,
select,
optgroup,
textarea {
  margin: 0;
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
}

button,
input {
  overflow: visible;
}

button,
select {
  text-transform: none;
}

[role='button'] {
  cursor: pointer;
}

select {
  word-wrap: normal;
}

button,
[type='button'],
[type='reset'],
[type='submit'] {
  -webkit-appearance: button;
}

button:not(:disabled),
[type='button']:not(:disabled),
[type='reset']:not(:disabled),
[type='submit']:not(:disabled) {
  cursor: pointer;
}

button::-moz-focus-inner,
[type='button']::-moz-focus-inner,
[type='reset']::-moz-focus-inner,
[type='submit']::-moz-focus-inner {
  padding: 0;
  border-style: none;
}

input[type='radio'],
input[type='checkbox'] {
  box-sizing: border-box;
  padding: 0;
}

textarea {
  overflow: auto;
  resize: vertical;
}

fieldset {
  min-width: 0;
  padding: 0;
  margin: 0;
  border: 0;
}

legend {
  display: block;
  width: 100%;
  max-width: 100%;
  padding: 0;
  margin-bottom: 0.5rem;
  font-size: 1.5rem;
  line-height: inherit;
  color: inherit;
  white-space: normal;
}

@media (max-width: 1200px) {
  legend {
    font-size: calc(1.275rem + 0.3vw);
  }
}

progress {
  vertical-align: baseline;
}

[type='number']::-webkit-inner-spin-button,
[type='number']::-webkit-outer-spin-button {
  height: auto;
}

[type='search'] {
  outline-offset: -2px;
  -webkit-appearance: none;
}

[type='search']::-webkit-search-decoration {
  -webkit-appearance: none;
}

::-webkit-file-upload-button {
  font: inherit;
  -webkit-appearance: button;
}

output {
  display: inline-block;
}

summary {
  display: list-item;
  cursor: pointer;
}

template {
  display: none;
}

[hidden] {
  display: none !important;
}

h1,
h2,
h3,
h4,
h5,
h6,
.h1,
.h2,
.h3,
.h4,
.h5,
.h6 {
  margin-bottom: 0.5rem;
  font-weight: 700;
  line-height: 1.2;
  color: var(--color-headings);
}

h1,
.h1 {
  font-size: 3rem;
}

@media (max-width: 1200px) {
  h1,
  .h1 {
    font-size: calc(1.425rem + 2.1vw);
  }
}

h2,
.h2 {
  font-size: 2.25rem;
}

@media (max-width: 1200px) {
  h2,
  .h2 {
    font-size: calc(1.35rem + 1.2vw);
  }
}

h3,
.h3 {
  font-size: 2rem;
}

@media (max-width: 1200px) {
  h3,
  .h3 {
    font-size: calc(1.325rem + 0.9vw);
  }
}

h4,
.h4 {
  font-size: 1.5rem;
}

@media (max-width: 1200px) {
  h4,
  .h4 {
    font-size: calc(1.275rem + 0.3vw);
  }
}

h5,
.h5 {
  font-size: 1.3125rem;
}

@media (max-width: 1200px) {
  h5,
  .h5 {
    font-size: calc(1.25625rem + 0.075vw);
  }
}

h6,
.h6 {
  font-size: 1.1rem;
}

.lead {
  font-size: 1.25rem;
  font-weight: 300;
}

.display-1 {
  font-size: 6rem;
  font-weight: 300;
  line-height: 1.2;
}

@media (max-width: 1200px) {
  .display-1 {
    font-size: calc(1.725rem + 5.7vw);
  }
}

.display-2 {
  font-size: 5.5rem;
  font-weight: 300;
  line-height: 1.2;
}

@media (max-width: 1200px) {
  .display-2 {
    font-size: calc(1.675rem + 5.1vw);
  }
}

.display-3 {
  font-size: 5rem;
  font-weight: 300;
  line-height: 1.2;
}

@media (max-width: 1200px) {
  .display-3 {
    font-size: calc(1.625rem + 4.5vw);
  }
}

.display-4 {
  font-size: 3.75rem;
  font-weight: 300;
  line-height: 1.2;
}

@media (max-width: 1200px) {
  .display-4 {
    font-size: calc(1.5rem + 3vw);
  }
}

hr {
  margin-top: 1rem;
  margin-bottom: 1rem;
  border: 0;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}

small,
.small {
  font-size: 80%;
  font-weight: 400;
}

mark,
.mark {
  padding: 0.2em;
  background-color: #fcf8e3;
}

.list-unstyled {
  padding-left: 0;
  list-style: none;
}

.list-inline {
  padding-left: 0;
  list-style: none;
}

.list-inline-item {
  display: inline-block;
}

.list-inline-item:not(:last-child) {
  margin-right: 0.5rem;
}

.initialism {
  font-size: 90%;
  text-transform: uppercase;
}

.blockquote {
  margin-bottom: 1rem;
  font-size: 1.25rem;
}

.blockquote-footer {
  display: block;
  font-size: 80%;
  color: #6c757d;
}

.blockquote-footer::before {
  content: '\2014\00A0';
}

.img-fluid {
  max-width: 100%;
  height: auto;
}

.img-thumbnail {
  padding: 0.25rem;
  background-color: var(--bg);
  border: 1px solid #dee2e6;
  border-radius: 0.25rem;
  max-width: 100%;
  height: auto;
}

.figure {
  display: inline-block;
}

.figure-img {
  margin-bottom: 0.5rem;
  line-height: 1;
}

.figure-caption {
  font-size: 90%;
  color: #6c757d;
}

code {
  font-size: 87.5%;
  color: #e83e8c;
  word-wrap: break-word;
}

a > code {
  color: inherit;
}

kbd {
  padding: 0.2rem 0.4rem;
  font-size: 87.5%;
  color: #fff;
  background-color: #212529;
  border-radius: 0.2rem;
}

kbd kbd {
  padding: 0;
  font-size: 100%;
  font-weight: 700;
}

pre {
  display: block;
  font-size: 87.5%;
  color: #212529;
}

pre code {
  font-size: inherit;
  color: inherit;
  word-break: normal;
}

.pre-scrollable {
  max-height: 340px;
  overflow-y: scroll;
}

.container,
.container-fluid,
.container-sm,
.container-md,
.container-lg,
.container-xl {
  width: 100%;
  padding-right: 15px;
  padding-left: 15px;
  margin-right: auto;
  margin-left: auto;
}

@media (min-width: 576px) {
  .container,
  .container-xs,
  .container-sm {
    max-width: 540px;
  }
}

@media (min-width: 768px) {
  .container,
  .container-xs,
  .container-sm,
  .container-md {
    max-width: 720px;
  }
}

@media (min-width: 992px) {
  .container,
  .container-xs,
  .container-sm,
  .container-md,
  .container-lg {
    max-width: 960px;
  }
}

@media (min-width: 1200px) {
  .container,
  .container-xs,
  .container-sm,
  .container-md,
  .container-lg,
  .container-xl {
    max-width: 1140px;
  }
}

.row {
  display: flex;
  flex-wrap: wrap;
  margin-right: -15px;
  margin-left: -15px;
}

.no-gutters {
  margin-right: 0;
  margin-left: 0;
}

.no-gutters > .col,
.no-gutters > [class*='col-'] {
  padding-right: 0;
  padding-left: 0;
}

.col-1,
.col-2,
.col-3,
.col-4,
.col-5,
.col-6,
.col-7,
.col-8,
.col-9,
.col-10,
.col-11,
.col-12,
.col,
.col-auto,
.col-xs-1,
.col-xs-2,
.col-xs-3,
.col-xs-4,
.col-xs-5,
.col-xs-6,
.col-xs-7,
.col-xs-8,
.col-xs-9,
.col-xs-10,
.col-xs-11,
.col-xs-12,
.col-xs,
.col-xs-auto,
.col-sm-1,
.col-sm-2,
.col-sm-3,
.col-sm-4,
.col-sm-5,
.col-sm-6,
.col-sm-7,
.col-sm-8,
.col-sm-9,
.col-sm-10,
.col-sm-11,
.col-sm-12,
.col-sm,
.col-sm-auto,
.col-md-1,
.col-md-2,
.col-md-3,
.col-md-4,
.col-md-5,
.col-md-6,
.col-md-7,
.col-md-8,
.col-md-9,
.col-md-10,
.col-md-11,
.col-md-12,
.col-md,
.col-md-auto,
.col-lg-1,
.col-lg-2,
.col-lg-3,
.col-lg-4,
.col-lg-5,
.col-lg-6,
.col-lg-7,
.col-lg-8,
.col-lg-9,
.col-lg-10,
.col-lg-11,
.col-lg-12,
.col-lg,
.col-lg-auto,
.col-xl-1,
.col-xl-2,
.col-xl-3,
.col-xl-4,
.col-xl-5,
.col-xl-6,
.col-xl-7,
.col-xl-8,
.col-xl-9,
.col-xl-10,
.col-xl-11,
.col-xl-12,
.col-xl,
.col-xl-auto {
  position: relative;
  width: 100%;
  padding-right: 15px;
  padding-left: 15px;
}

.col {
  flex-basis: 0;
  flex-grow: 1;
  max-width: 100%;
}

.row-cols-1 > * {
  flex: 0 0 100%;
  max-width: 100%;
}

.row-cols-2 > * {
  flex: 0 0 50%;
  max-width: 50%;
}

.row-cols-3 > * {
  flex: 0 0 33.33333%;
  max-width: 33.33333%;
}

.row-cols-4 > * {
  flex: 0 0 25%;
  max-width: 25%;
}

.row-cols-5 > * {
  flex: 0 0 20%;
  max-width: 20%;
}

.row-cols-6 > * {
  flex: 0 0 16.66667%;
  max-width: 16.66667%;
}

.col-auto {
  flex: 0 0 auto;
  width: auto;
  max-width: 100%;
}

.col-1 {
  flex: 0 0 8.33333%;
  max-width: 8.33333%;
}

.col-2 {
  flex: 0 0 16.66667%;
  max-width: 16.66667%;
}

.col-3 {
  flex: 0 0 25%;
  max-width: 25%;
}

.col-4 {
  flex: 0 0 33.33333%;
  max-width: 33.33333%;
}

.col-5 {
  flex: 0 0 41.66667%;
  max-width: 41.66667%;
}

.col-6 {
  flex: 0 0 50%;
  max-width: 50%;
}

.col-7 {
  flex: 0 0 58.33333%;
  max-width: 58.33333%;
}

.col-8 {
  flex: 0 0 66.66667%;
  max-width: 66.66667%;
}

.col-9 {
  flex: 0 0 75%;
  max-width: 75%;
}

.col-10 {
  flex: 0 0 83.33333%;
  max-width: 83.33333%;
}

.col-11 {
  flex: 0 0 91.66667%;
  max-width: 91.66667%;
}

.col-12 {
  flex: 0 0 100%;
  max-width: 100%;
}

.order-first {
  order: -1;
}

.order-last {
  order: 13;
}

.order-0 {
  order: 0;
}

.order-1 {
  order: 1;
}

.order-2 {
  order: 2;
}

.order-3 {
  order: 3;
}

.order-4 {
  order: 4;
}

.order-5 {
  order: 5;
}

.order-6 {
  order: 6;
}

.order-7 {
  order: 7;
}

.order-8 {
  order: 8;
}

.order-9 {
  order: 9;
}

.order-10 {
  order: 10;
}

.order-11 {
  order: 11;
}

.order-12 {
  order: 12;
}

.offset-1 {
  margin-left: 8.33333%;
}

.offset-2 {
  margin-left: 16.66667%;
}

.offset-3 {
  margin-left: 25%;
}

.offset-4 {
  margin-left: 33.33333%;
}

.offset-5 {
  margin-left: 41.66667%;
}

.offset-6 {
  margin-left: 50%;
}

.offset-7 {
  margin-left: 58.33333%;
}

.offset-8 {
  margin-left: 66.66667%;
}

.offset-9 {
  margin-left: 75%;
}

.offset-10 {
  margin-left: 83.33333%;
}

.offset-11 {
  margin-left: 91.66667%;
}

@media (min-width: 480px) {
  .col-xs {
    flex-basis: 0;
    flex-grow: 1;
    max-width: 100%;
  }
  .row-cols-xs-1 > * {
    flex: 0 0 100%;
    max-width: 100%;
  }
  .row-cols-xs-2 > * {
    flex: 0 0 50%;
    max-width: 50%;
  }
  .row-cols-xs-3 > * {
    flex: 0 0 33.33333%;
    max-width: 33.33333%;
  }
  .row-cols-xs-4 > * {
    flex: 0 0 25%;
    max-width: 25%;
  }
  .row-cols-xs-5 > * {
    flex: 0 0 20%;
    max-width: 20%;
  }
  .row-cols-xs-6 > * {
    flex: 0 0 16.66667%;
    max-width: 16.66667%;
  }
  .col-xs-auto {
    flex: 0 0 auto;
    width: auto;
    max-width: 100%;
  }
  .col-xs-1 {
    flex: 0 0 8.33333%;
    max-width: 8.33333%;
  }
  .col-xs-2 {
    flex: 0 0 16.66667%;
    max-width: 16.66667%;
  }
  .col-xs-3 {
    flex: 0 0 25%;
    max-width: 25%;
  }
  .col-xs-4 {
    flex: 0 0 33.33333%;
    max-width: 33.33333%;
  }
  .col-xs-5 {
    flex: 0 0 41.66667%;
    max-width: 41.66667%;
  }
  .col-xs-6 {
    flex: 0 0 50%;
    max-width: 50%;
  }
  .col-xs-7 {
    flex: 0 0 58.33333%;
    max-width: 58.33333%;
  }
  .col-xs-8 {
    flex: 0 0 66.66667%;
    max-width: 66.66667%;
  }
  .col-xs-9 {
    flex: 0 0 75%;
    max-width: 75%;
  }
  .col-xs-10 {
    flex: 0 0 83.33333%;
    max-width: 83.33333%;
  }
  .col-xs-11 {
    flex: 0 0 91.66667%;
    max-width: 91.66667%;
  }
  .col-xs-12 {
    flex: 0 0 100%;
    max-width: 100%;
  }
  .order-xs-first {
    order: -1;
  }
  .order-xs-last {
    order: 13;
  }
  .order-xs-0 {
    order: 0;
  }
  .order-xs-1 {
    order: 1;
  }
  .order-xs-2 {
    order: 2;
  }
  .order-xs-3 {
    order: 3;
  }
  .order-xs-4 {
    order: 4;
  }
  .order-xs-5 {
    order: 5;
  }
  .order-xs-6 {
    order: 6;
  }
  .order-xs-7 {
    order: 7;
  }
  .order-xs-8 {
    order: 8;
  }
  .order-xs-9 {
    order: 9;
  }
  .order-xs-10 {
    order: 10;
  }
  .order-xs-11 {
    order: 11;
  }
  .order-xs-12 {
    order: 12;
  }
  .offset-xs-0 {
    margin-left: 0;
  }
  .offset-xs-1 {
    margin-left: 8.33333%;
  }
  .offset-xs-2 {
    margin-left: 16.66667%;
  }
  .offset-xs-3 {
    margin-left: 25%;
  }
  .offset-xs-4 {
    margin-left: 33.33333%;
  }
  .offset-xs-5 {
    margin-left: 41.66667%;
  }
  .offset-xs-6 {
    margin-left: 50%;
  }
  .offset-xs-7 {
    margin-left: 58.33333%;
  }
  .offset-xs-8 {
    margin-left: 66.66667%;
  }
  .offset-xs-9 {
    margin-left: 75%;
  }
  .offset-xs-10 {
    margin-left: 83.33333%;
  }
  .offset-xs-11 {
    margin-left: 91.66667%;
  }
}

@media (min-width: 576px) {
  .col-sm {
    flex-basis: 0;
    flex-grow: 1;
    max-width: 100%;
  }
  .row-cols-sm-1 > * {
    flex: 0 0 100%;
    max-width: 100%;
  }
  .row-cols-sm-2 > * {
    flex: 0 0 50%;
    max-width: 50%;
  }
  .row-cols-sm-3 > * {
    flex: 0 0 33.33333%;
    max-width: 33.33333%;
  }
  .row-cols-sm-4 > * {
    flex: 0 0 25%;
    max-width: 25%;
  }
  .row-cols-sm-5 > * {
    flex: 0 0 20%;
    max-width: 20%;
  }
  .row-cols-sm-6 > * {
    flex: 0 0 16.66667%;
    max-width: 16.66667%;
  }
  .col-sm-auto {
    flex: 0 0 auto;
    width: auto;
    max-width: 100%;
  }
  .col-sm-1 {
    flex: 0 0 8.33333%;
    max-width: 8.33333%;
  }
  .col-sm-2 {
    flex: 0 0 16.66667%;
    max-width: 16.66667%;
  }
  .col-sm-3 {
    flex: 0 0 25%;
    max-width: 25%;
  }
  .col-sm-4 {
    flex: 0 0 33.33333%;
    max-width: 33.33333%;
  }
  .col-sm-5 {
    flex: 0 0 41.66667%;
    max-width: 41.66667%;
  }
  .col-sm-6 {
    flex: 0 0 50%;
    max-width: 50%;
  }
  .col-sm-7 {
    flex: 0 0 58.33333%;
    max-width: 58.33333%;
  }
  .col-sm-8 {
    flex: 0 0 66.66667%;
    max-width: 66.66667%;
  }
  .col-sm-9 {
    flex: 0 0 75%;
    max-width: 75%;
  }
  .col-sm-10 {
    flex: 0 0 83.33333%;
    max-width: 83.33333%;
  }
  .col-sm-11 {
    flex: 0 0 91.66667%;
    max-width: 91.66667%;
  }
  .col-sm-12 {
    flex: 0 0 100%;
    max-width: 100%;
  }
  .order-sm-first {
    order: -1;
  }
  .order-sm-last {
    order: 13;
  }
  .order-sm-0 {
    order: 0;
  }
  .order-sm-1 {
    order: 1;
  }
  .order-sm-2 {
    order: 2;
  }
  .order-sm-3 {
    order: 3;
  }
  .order-sm-4 {
    order: 4;
  }
  .order-sm-5 {
    order: 5;
  }
  .order-sm-6 {
    order: 6;
  }
  .order-sm-7 {
    order: 7;
  }
  .order-sm-8 {
    order: 8;
  }
  .order-sm-9 {
    order: 9;
  }
  .order-sm-10 {
    order: 10;
  }
  .order-sm-11 {
    order: 11;
  }
  .order-sm-12 {
    order: 12;
  }
  .offset-sm-0 {
    margin-left: 0;
  }
  .offset-sm-1 {
    margin-left: 8.33333%;
  }
  .offset-sm-2 {
    margin-left: 16.66667%;
  }
  .offset-sm-3 {
    margin-left: 25%;
  }
  .offset-sm-4 {
    margin-left: 33.33333%;
  }
  .offset-sm-5 {
    margin-left: 41.66667%;
  }
  .offset-sm-6 {
    margin-left: 50%;
  }
  .offset-sm-7 {
    margin-left: 58.33333%;
  }
  .offset-sm-8 {
    margin-left: 66.66667%;
  }
  .offset-sm-9 {
    margin-left: 75%;
  }
  .offset-sm-10 {
    margin-left: 83.33333%;
  }
  .offset-sm-11 {
    margin-left: 91.66667%;
  }
}

@media (min-width: 768px) {
  .col-md {
    flex-basis: 0;
    flex-grow: 1;
    max-width: 100%;
  }
  .row-cols-md-1 > * {
    flex: 0 0 100%;
    max-width: 100%;
  }
  .row-cols-md-2 > * {
    flex: 0 0 50%;
    max-width: 50%;
  }
  .row-cols-md-3 > * {
    flex: 0 0 33.33333%;
    max-width: 33.33333%;
  }
  .row-cols-md-4 > * {
    flex: 0 0 25%;
    max-width: 25%;
  }
  .row-cols-md-5 > * {
    flex: 0 0 20%;
    max-width: 20%;
  }
  .row-cols-md-6 > * {
    flex: 0 0 16.66667%;
    max-width: 16.66667%;
  }
  .col-md-auto {
    flex: 0 0 auto;
    width: auto;
    max-width: 100%;
  }
  .col-md-1 {
    flex: 0 0 8.33333%;
    max-width: 8.33333%;
  }
  .col-md-2 {
    flex: 0 0 16.66667%;
    max-width: 16.66667%;
  }
  .col-md-3 {
    flex: 0 0 25%;
    max-width: 25%;
  }
  .col-md-4 {
    flex: 0 0 33.33333%;
    max-width: 33.33333%;
  }
  .col-md-5 {
    flex: 0 0 41.66667%;
    max-width: 41.66667%;
  }
  .col-md-6 {
    flex: 0 0 50%;
    max-width: 50%;
  }
  .col-md-7 {
    flex: 0 0 58.33333%;
    max-width: 58.33333%;
  }
  .col-md-8 {
    flex: 0 0 66.66667%;
    max-width: 66.66667%;
  }
  .col-md-9 {
    flex: 0 0 75%;
    max-width: 75%;
  }
  .col-md-10 {
    flex: 0 0 83.33333%;
    max-width: 83.33333%;
  }
  .col-md-11 {
    flex: 0 0 91.66667%;
    max-width: 91.66667%;
  }
  .col-md-12 {
    flex: 0 0 100%;
    max-width: 100%;
  }
  .order-md-first {
    order: -1;
  }
  .order-md-last {
    order: 13;
  }
  .order-md-0 {
    order: 0;
  }
  .order-md-1 {
    order: 1;
  }
  .order-md-2 {
    order: 2;
  }
  .order-md-3 {
    order: 3;
  }
  .order-md-4 {
    order: 4;
  }
  .order-md-5 {
    order: 5;
  }
  .order-md-6 {
    order: 6;
  }
  .order-md-7 {
    order: 7;
  }
  .order-md-8 {
    order: 8;
  }
  .order-md-9 {
    order: 9;
  }
  .order-md-10 {
    order: 10;
  }
  .order-md-11 {
    order: 11;
  }
  .order-md-12 {
    order: 12;
  }
  .offset-md-0 {
    margin-left: 0;
  }
  .offset-md-1 {
    margin-left: 8.33333%;
  }
  .offset-md-2 {
    margin-left: 16.66667%;
  }
  .offset-md-3 {
    margin-left: 25%;
  }
  .offset-md-4 {
    margin-left: 33.33333%;
  }
  .offset-md-5 {
    margin-left: 41.66667%;
  }
  .offset-md-6 {
    margin-left: 50%;
  }
  .offset-md-7 {
    margin-left: 58.33333%;
  }
  .offset-md-8 {
    margin-left: 66.66667%;
  }
  .offset-md-9 {
    margin-left: 75%;
  }
  .offset-md-10 {
    margin-left: 83.33333%;
  }
  .offset-md-11 {
    margin-left: 91.66667%;
  }
}

@media (min-width: 992px) {
  .col-lg {
    flex-basis: 0;
    flex-grow: 1;
    max-width: 100%;
  }
  .row-cols-lg-1 > * {
    flex: 0 0 100%;
    max-width: 100%;
  }
  .row-cols-lg-2 > * {
    flex: 0 0 50%;
    max-width: 50%;
  }
  .row-cols-lg-3 > * {
    flex: 0 0 33.33333%;
    max-width: 33.33333%;
  }
  .row-cols-lg-4 > * {
    flex: 0 0 25%;
    max-width: 25%;
  }
  .row-cols-lg-5 > * {
    flex: 0 0 20%;
    max-width: 20%;
  }
  .row-cols-lg-6 > * {
    flex: 0 0 16.66667%;
    max-width: 16.66667%;
  }
  .col-lg-auto {
    flex: 0 0 auto;
    width: auto;
    max-width: 100%;
  }
  .col-lg-1 {
    flex: 0 0 8.33333%;
    max-width: 8.33333%;
  }
  .col-lg-2 {
    flex: 0 0 16.66667%;
    max-width: 16.66667%;
  }
  .col-lg-3 {
    flex: 0 0 25%;
    max-width: 25%;
  }
  .col-lg-4 {
    flex: 0 0 33.33333%;
    max-width: 33.33333%;
  }
  .col-lg-5 {
    flex: 0 0 41.66667%;
    max-width: 41.66667%;
  }
  .col-lg-6 {
    flex: 0 0 50%;
    max-width: 50%;
  }
  .col-lg-7 {
    flex: 0 0 58.33333%;
    max-width: 58.33333%;
  }
  .col-lg-8 {
    flex: 0 0 66.66667%;
    max-width: 66.66667%;
  }
  .col-lg-9 {
    flex: 0 0 75%;
    max-width: 75%;
  }
  .col-lg-10 {
    flex: 0 0 83.33333%;
    max-width: 83.33333%;
  }
  .col-lg-11 {
    flex: 0 0 91.66667%;
    max-width: 91.66667%;
  }
  .col-lg-12 {
    flex: 0 0 100%;
    max-width: 100%;
  }
  .order-lg-first {
    order: -1;
  }
  .order-lg-last {
    order: 13;
  }
  .order-lg-0 {
    order: 0;
  }
  .order-lg-1 {
    order: 1;
  }
  .order-lg-2 {
    order: 2;
  }
  .order-lg-3 {
    order: 3;
  }
  .order-lg-4 {
    order: 4;
  }
  .order-lg-5 {
    order: 5;
  }
  .order-lg-6 {
    order: 6;
  }
  .order-lg-7 {
    order: 7;
  }
  .order-lg-8 {
    order: 8;
  }
  .order-lg-9 {
    order: 9;
  }
  .order-lg-10 {
    order: 10;
  }
  .order-lg-11 {
    order: 11;
  }
  .order-lg-12 {
    order: 12;
  }
  .offset-lg-0 {
    margin-left: 0;
  }
  .offset-lg-1 {
    margin-left: 8.33333%;
  }
  .offset-lg-2 {
    margin-left: 16.66667%;
  }
  .offset-lg-3 {
    margin-left: 25%;
  }
  .offset-lg-4 {
    margin-left: 33.33333%;
  }
  .offset-lg-5 {
    margin-left: 41.66667%;
  }
  .offset-lg-6 {
    margin-left: 50%;
  }
  .offset-lg-7 {
    margin-left: 58.33333%;
  }
  .offset-lg-8 {
    margin-left: 66.66667%;
  }
  .offset-lg-9 {
    margin-left: 75%;
  }
  .offset-lg-10 {
    margin-left: 83.33333%;
  }
  .offset-lg-11 {
    margin-left: 91.66667%;
  }
}

@media (min-width: 1200px) {
  .col-xl {
    flex-basis: 0;
    flex-grow: 1;
    max-width: 100%;
  }
  .row-cols-xl-1 > * {
    flex: 0 0 100%;
    max-width: 100%;
  }
  .row-cols-xl-2 > * {
    flex: 0 0 50%;
    max-width: 50%;
  }
  .row-cols-xl-3 > * {
    flex: 0 0 33.33333%;
    max-width: 33.33333%;
  }
  .row-cols-xl-4 > * {
    flex: 0 0 25%;
    max-width: 25%;
  }
  .row-cols-xl-5 > * {
    flex: 0 0 20%;
    max-width: 20%;
  }
  .row-cols-xl-6 > * {
    flex: 0 0 16.66667%;
    max-width: 16.66667%;
  }
  .col-xl-auto {
    flex: 0 0 auto;
    width: auto;
    max-width: 100%;
  }
  .col-xl-1 {
    flex: 0 0 8.33333%;
    max-width: 8.33333%;
  }
  .col-xl-2 {
    flex: 0 0 16.66667%;
    max-width: 16.66667%;
  }
  .col-xl-3 {
    flex: 0 0 25%;
    max-width: 25%;
  }
  .col-xl-4 {
    flex: 0 0 33.33333%;
    max-width: 33.33333%;
  }
  .col-xl-5 {
    flex: 0 0 41.66667%;
    max-width: 41.66667%;
  }
  .col-xl-6 {
    flex: 0 0 50%;
    max-width: 50%;
  }
  .col-xl-7 {
    flex: 0 0 58.33333%;
    max-width: 58.33333%;
  }
  .col-xl-8 {
    flex: 0 0 66.66667%;
    max-width: 66.66667%;
  }
  .col-xl-9 {
    flex: 0 0 75%;
    max-width: 75%;
  }
  .col-xl-10 {
    flex: 0 0 83.33333%;
    max-width: 83.33333%;
  }
  .col-xl-11 {
    flex: 0 0 91.66667%;
    max-width: 91.66667%;
  }
  .col-xl-12 {
    flex: 0 0 100%;
    max-width: 100%;
  }
  .order-xl-first {
    order: -1;
  }
  .order-xl-last {
    order: 13;
  }
  .order-xl-0 {
    order: 0;
  }
  .order-xl-1 {
    order: 1;
  }
  .order-xl-2 {
    order: 2;
  }
  .order-xl-3 {
    order: 3;
  }
  .order-xl-4 {
    order: 4;
  }
  .order-xl-5 {
    order: 5;
  }
  .order-xl-6 {
    order: 6;
  }
  .order-xl-7 {
    order: 7;
  }
  .order-xl-8 {
    order: 8;
  }
  .order-xl-9 {
    order: 9;
  }
  .order-xl-10 {
    order: 10;
  }
  .order-xl-11 {
    order: 11;
  }
  .order-xl-12 {
    order: 12;
  }
  .offset-xl-0 {
    margin-left: 0;
  }
  .offset-xl-1 {
    margin-left: 8.33333%;
  }
  .offset-xl-2 {
    margin-left: 16.66667%;
  }
  .offset-xl-3 {
    margin-left: 25%;
  }
  .offset-xl-4 {
    margin-left: 33.33333%;
  }
  .offset-xl-5 {
    margin-left: 41.66667%;
  }
  .offset-xl-6 {
    margin-left: 50%;
  }
  .offset-xl-7 {
    margin-left: 58.33333%;
  }
  .offset-xl-8 {
    margin-left: 66.66667%;
  }
  .offset-xl-9 {
    margin-left: 75%;
  }
  .offset-xl-10 {
    margin-left: 83.33333%;
  }
  .offset-xl-11 {
    margin-left: 91.66667%;
  }
}

.table {
  width: 100%;
  margin-bottom: 1rem;
  color: var(--color-texts-opacity);
}

.table th,
.table td {
  padding: 0.75rem;
  vertical-align: top;
  border-top: 1px solid var(--border-color);
}

.table thead th {
  vertical-align: bottom;
  border-bottom: 2px solid var(--border-color);
}

.table tbody + tbody {
  border-top: 2px solid var(--border-color);
}

.table-sm th,
.table-sm td {
  padding: 0.3rem;
}

.table-bordered {
  border: 1px solid var(--border-color);
}

.table-bordered th,
.table-bordered td {
  border: 1px solid var(--border-color);
}

.table-bordered thead th,
.table-bordered thead td {
  border-bottom-width: 2px;
}

.table-borderless th,
.table-borderless td,
.table-borderless thead th,
.table-borderless tbody + tbody {
  border: 0;
}

.table-striped tbody tr:nth-of-type(odd) {
  background-color: rgba(0, 0, 0, 0.05);
}

.table-hover tbody tr:hover {
  color: var(--color-texts-opacity);
  background-color: rgba(0, 0, 0, 0.075);
}

.table-primary,
.table-primary > th,
.table-primary > td {
  background-color: #cbc8fb;
}

.table-primary th,
.table-primary td,
.table-primary thead th,
.table-primary tbody + tbody {
  border-color: #9f99f7;
}

.table-hover .table-primary:hover {
  background-color: #b5b0f9;
}

.table-hover .table-primary:hover > td,
.table-hover .table-primary:hover > th {
  background-color: #b5b0f9;
}

.table-secondary,
.table-secondary > th,
.table-secondary > td {
  background-color: #d5f3dd;
}

.table-secondary th,
.table-secondary td,
.table-secondary thead th,
.table-secondary tbody + tbody {
  border-color: #b0e9c0;
}

.table-hover .table-secondary:hover {
  background-color: #c1edcd;
}

.table-hover .table-secondary:hover > td,
.table-hover .table-secondary:hover > th {
  background-color: #c1edcd;
}

.table-success,
.table-success > th,
.table-success > td {
  background-color: #d5f3dd;
}

.table-success th,
.table-success td,
.table-success thead th,
.table-success tbody + tbody {
  border-color: #b0e9c0;
}

.table-hover .table-success:hover {
  background-color: #c1edcd;
}

.table-hover .table-success:hover > td,
.table-hover .table-success:hover > th {
  background-color: #c1edcd;
}

.table-info,
.table-info > th,
.table-info > td {
  background-color: #bee5eb;
}

.table-info th,
.table-info td,
.table-info thead th,
.table-info tbody + tbody {
  border-color: #86cfda;
}

.table-hover .table-info:hover {
  background-color: #abdde5;
}

.table-hover .table-info:hover > td,
.table-hover .table-info:hover > th {
  background-color: #abdde5;
}

.table-warning,
.table-warning > th,
.table-warning > td {
  background-color: #fdf7d6;
}

.table-warning th,
.table-warning td,
.table-warning thead th,
.table-warning tbody + tbody {
  border-color: #fbf0b3;
}

.table-hover .table-warning:hover {
  background-color: #fcf2be;
}

.table-hover .table-warning:hover > td,
.table-hover .table-warning:hover > th {
  background-color: #fcf2be;
}

.table-danger,
.table-danger > th,
.table-danger > td {
  background-color: #fccdcd;
}

.table-danger th,
.table-danger td,
.table-danger thead th,
.table-danger tbody + tbody {
  border-color: #faa1a1;
}

.table-hover .table-danger:hover {
  background-color: #fbb5b5;
}

.table-hover .table-danger:hover > td,
.table-hover .table-danger:hover > th {
  background-color: #fbb5b5;
}

.table-light,
.table-light > th,
.table-light > td {
  background-color: #fdfdfe;
}

.table-light th,
.table-light td,
.table-light thead th,
.table-light tbody + tbody {
  border-color: #fbfcfc;
}

.table-hover .table-light:hover {
  background-color: #ececf6;
}

.table-hover .table-light:hover > td,
.table-hover .table-light:hover > th {
  background-color: #ececf6;
}

.table-dark,
.table-dark > th,
.table-dark > td {
  background-color: #c6c8ca;
}

.table-dark th,
.table-dark td,
.table-dark thead th,
.table-dark tbody + tbody {
  border-color: #95999c;
}

.table-hover .table-dark:hover {
  background-color: #b9bbbe;
}

.table-hover .table-dark:hover > td,
.table-hover .table-dark:hover > th {
  background-color: #b9bbbe;
}

.table-red,
.table-red > th,
.table-red > td {
  background-color: #fccdcd;
}

.table-red th,
.table-red td,
.table-red thead th,
.table-red tbody + tbody {
  border-color: #faa1a1;
}

.table-hover .table-red:hover {
  background-color: #fbb5b5;
}

.table-hover .table-red:hover > td,
.table-hover .table-red:hover > th {
  background-color: #fbb5b5;
}

.table-green,
.table-green > th,
.table-green > td {
  background-color: #d5f3dd;
}

.table-green th,
.table-green td,
.table-green thead th,
.table-green tbody + tbody {
  border-color: #b0e9c0;
}

.table-hover .table-green:hover {
  background-color: #c1edcd;
}

.table-hover .table-green:hover > td,
.table-hover .table-green:hover > th {
  background-color: #c1edcd;
}

.table-green-shamrock,
.table-green-shamrock > th,
.table-green-shamrock > td {
  background-color: #c4f4da;
}

.table-green-shamrock th,
.table-green-shamrock td,
.table-green-shamrock thead th,
.table-green-shamrock tbody + tbody {
  border-color: #91eaba;
}

.table-hover .table-green-shamrock:hover {
  background-color: #aff0cd;
}

.table-hover .table-green-shamrock:hover > td,
.table-hover .table-green-shamrock:hover > th {
  background-color: #aff0cd;
}

.table-blue,
.table-blue > th,
.table-blue > td {
  background-color: #cbc8fb;
}

.table-blue th,
.table-blue td,
.table-blue thead th,
.table-blue tbody + tbody {
  border-color: #9f99f7;
}

.table-hover .table-blue:hover {
  background-color: #b5b0f9;
}

.table-hover .table-blue:hover > td,
.table-hover .table-blue:hover > th {
  background-color: #b5b0f9;
}

.table-sky-blue,
.table-sky-blue > th,
.table-sky-blue > td {
  background-color: #bcdcf9;
}

.table-sky-blue th,
.table-sky-blue td,
.table-sky-blue thead th,
.table-sky-blue tbody + tbody {
  border-color: #83bef4;
}

.table-hover .table-sky-blue:hover {
  background-color: #a5d0f7;
}

.table-hover .table-sky-blue:hover > td,
.table-hover .table-sky-blue:hover > th {
  background-color: #a5d0f7;
}

.table-yellow,
.table-yellow > th,
.table-yellow > td {
  background-color: #fdf7d6;
}

.table-yellow th,
.table-yellow td,
.table-yellow thead th,
.table-yellow tbody + tbody {
  border-color: #fbf0b3;
}

.table-hover .table-yellow:hover {
  background-color: #fcf2be;
}

.table-hover .table-yellow:hover > td,
.table-hover .table-yellow:hover > th {
  background-color: #fcf2be;
}

.table-yellow-orange,
.table-yellow-orange > th,
.table-yellow-orange > td {
  background-color: #fee8c7;
}

.table-yellow-orange th,
.table-yellow-orange td,
.table-yellow-orange thead th,
.table-yellow-orange tbody + tbody {
  border-color: #fdd498;
}

.table-hover .table-yellow-orange:hover {
  background-color: #fedeae;
}

.table-hover .table-yellow-orange:hover > td,
.table-hover .table-yellow-orange:hover > th {
  background-color: #fedeae;
}

.table-blackish-blue,
.table-blackish-blue > th,
.table-blackish-blue > td {
  background-color: #bdbdbf;
}

.table-blackish-blue th,
.table-blackish-blue td,
.table-blackish-blue thead th,
.table-blackish-blue tbody + tbody {
  border-color: #848589;
}

.table-hover .table-blackish-blue:hover {
  background-color: #b0b0b2;
}

.table-hover .table-blackish-blue:hover > td,
.table-hover .table-blackish-blue:hover > th {
  background-color: #b0b0b2;
}

.table-black,
.table-black > th,
.table-black > td {
  background-color: #b8b8b8;
}

.table-black th,
.table-black td,
.table-black thead th,
.table-black tbody + tbody {
  border-color: #7a7a7a;
}

.table-hover .table-black:hover {
  background-color: #ababab;
}

.table-hover .table-black:hover > td,
.table-hover .table-black:hover > th {
  background-color: #ababab;
}

.table-mirage,
.table-mirage > th,
.table-mirage > td {
  background-color: #bdbec3;
}

.table-mirage th,
.table-mirage td,
.table-mirage thead th,
.table-mirage tbody + tbody {
  border-color: #848790;
}

.table-hover .table-mirage:hover {
  background-color: #b0b1b7;
}

.table-hover .table-mirage:hover > td,
.table-hover .table-mirage:hover > th {
  background-color: #b0b1b7;
}

.table-mirage-2,
.table-mirage-2 > th,
.table-mirage-2 > td {
  background-color: #bebfc4;
}

.table-mirage-2 th,
.table-mirage-2 td,
.table-mirage-2 thead th,
.table-mirage-2 tbody + tbody {
  border-color: #868992;
}

.table-hover .table-mirage-2:hover {
  background-color: #b1b2b8;
}

.table-hover .table-mirage-2:hover > td,
.table-hover .table-mirage-2:hover > th {
  background-color: #b1b2b8;
}

.table-white,
.table-white > th,
.table-white > td {
  background-color: white;
}

.table-white th,
.table-white td,
.table-white thead th,
.table-white tbody + tbody {
  border-color: white;
}

.table-hover .table-white:hover {
  background-color: #f2f2f2;
}

.table-hover .table-white:hover > td,
.table-hover .table-white:hover > th {
  background-color: #f2f2f2;
}

.table-smoke,
.table-smoke > th,
.table-smoke > td {
  background-color: #fdfdfd;
}

.table-smoke th,
.table-smoke td,
.table-smoke thead th,
.table-smoke tbody + tbody {
  border-color: #fbfbfb;
}

.table-hover .table-smoke:hover {
  background-color: #f0f0f0;
}

.table-hover .table-smoke:hover > td,
.table-hover .table-smoke:hover > th {
  background-color: #f0f0f0;
}

.table-storm,
.table-storm > th,
.table-storm > td {
  background-color: #dbdcdf;
}

.table-storm th,
.table-storm td,
.table-storm thead th,
.table-storm tbody + tbody {
  border-color: #bbbdc4;
}

.table-hover .table-storm:hover {
  background-color: #cecfd3;
}

.table-hover .table-storm:hover > td,
.table-hover .table-storm:hover > th {
  background-color: #cecfd3;
}

.table-ghost,
.table-ghost > th,
.table-ghost > td {
  background-color: #fefeff;
}

.table-ghost th,
.table-ghost td,
.table-ghost thead th,
.table-ghost tbody + tbody {
  border-color: #fefeff;
}

.table-hover .table-ghost:hover {
  background-color: #e5e5ff;
}

.table-hover .table-ghost:hover > td,
.table-hover .table-ghost:hover > th {
  background-color: #e5e5ff;
}

.table-gray-1,
.table-gray-1 > th,
.table-gray-1 > td {
  background-color: #fefeff;
}

.table-gray-1 th,
.table-gray-1 td,
.table-gray-1 thead th,
.table-gray-1 tbody + tbody {
  border-color: #fdfefe;
}

.table-hover .table-gray-1:hover {
  background-color: #e5e5ff;
}

.table-hover .table-gray-1:hover > td,
.table-hover .table-gray-1:hover > th {
  background-color: #e5e5ff;
}

.table-gray-2,
.table-gray-2 > th,
.table-gray-2 > td {
  background-color: #fcfdfe;
}

.table-gray-2 th,
.table-gray-2 td,
.table-gray-2 thead th,
.table-gray-2 tbody + tbody {
  border-color: #f9fbfc;
}

.table-hover .table-gray-2:hover {
  background-color: #e9f0f8;
}

.table-hover .table-gray-2:hover > td,
.table-hover .table-gray-2:hover > th {
  background-color: #e9f0f8;
}

.table-gray-3,
.table-gray-3 > th,
.table-gray-3 > td {
  background-color: #f8f9fa;
}

.table-gray-3 th,
.table-gray-3 td,
.table-gray-3 thead th,
.table-gray-3 tbody + tbody {
  border-color: #f3f4f6;
}

.table-hover .table-gray-3:hover {
  background-color: #e9ecef;
}

.table-hover .table-gray-3:hover > td,
.table-hover .table-gray-3:hover > th {
  background-color: #e9ecef;
}

.table-gray-310,
.table-gray-310 > th,
.table-gray-310 > td {
  background-color: #f3f4f5;
}

.table-gray-310 th,
.table-gray-310 td,
.table-gray-310 thead th,
.table-gray-310 tbody + tbody {
  border-color: #e9eaed;
}

.table-hover .table-gray-310:hover {
  background-color: #e5e7e9;
}

.table-hover .table-gray-310:hover > td,
.table-hover .table-gray-310:hover > th {
  background-color: #e5e7e9;
}

.table-gray-opacity,
.table-gray-opacity > th,
.table-gray-opacity > td {
  background-color: rgba(254, 254, 255, 0.7564);
}

.table-gray-opacity th,
.table-gray-opacity td,
.table-gray-opacity thead th,
.table-gray-opacity tbody + tbody {
  border-color: rgba(253, 253, 254, 0.5476);
}

.table-hover .table-gray-opacity:hover {
  background-color: rgba(229, 229, 255, 0.7564);
}

.table-hover .table-gray-opacity:hover > td,
.table-hover .table-gray-opacity:hover > th {
  background-color: rgba(229, 229, 255, 0.7564);
}

.table-blackish-blue-opacity,
.table-blackish-blue-opacity > th,
.table-blackish-blue-opacity > td {
  background-color: rgba(215, 216, 219, 0.916);
}

.table-blackish-blue-opacity th,
.table-blackish-blue-opacity td,
.table-blackish-blue-opacity thead th,
.table-blackish-blue-opacity tbody + tbody {
  border-color: rgba(169, 171, 178, 0.844);
}

.table-hover .table-blackish-blue-opacity:hover {
  background-color: rgba(202, 203, 207, 0.916);
}

.table-hover .table-blackish-blue-opacity:hover > td,
.table-hover .table-blackish-blue-opacity:hover > th {
  background-color: rgba(202, 203, 207, 0.916);
}

.table-narvik,
.table-narvik > th,
.table-narvik > td {
  background-color: #fafdfb;
}

.table-narvik th,
.table-narvik td,
.table-narvik thead th,
.table-narvik tbody + tbody {
  border-color: #f6fcf8;
}

.table-hover .table-narvik:hover {
  background-color: #e8f6ec;
}

.table-hover .table-narvik:hover > td,
.table-hover .table-narvik:hover > th {
  background-color: #e8f6ec;
}

.table-active,
.table-active > th,
.table-active > td {
  background-color: rgba(0, 0, 0, 0.075);
}

.table-hover .table-active:hover {
  background-color: rgba(0, 0, 0, 0.075);
}

.table-hover .table-active:hover > td,
.table-hover .table-active:hover > th {
  background-color: rgba(0, 0, 0, 0.075);
}

.table .thead-dark th {
  color: #fff;
  background-color: #343a40;
  border-color: #454d55;
}

.table .thead-light th {
  color: #495057;
  background-color: #e9ecef;
  border-color: var(--border-color);
}

.table-dark {
  color: #fff;
  background-color: #343a40;
}

.table-dark th,
.table-dark td,
.table-dark thead th {
  border-color: #454d55;
}

.table-dark.table-bordered {
  border: 0;
}

.table-dark.table-striped tbody tr:nth-of-type(odd) {
  background-color: rgba(255, 255, 255, 0.05);
}

.table-dark.table-hover tbody tr:hover {
  color: #fff;
  background-color: rgba(255, 255, 255, 0.075);
}

@media (max-width: 479.98px) {
  .table-responsive-xs {
    display: block;
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
  .table-responsive-xs > .table-bordered {
    border: 0;
  }
}

@media (max-width: 575.98px) {
  .table-responsive-sm {
    display: block;
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
  .table-responsive-sm > .table-bordered {
    border: 0;
  }
}

@media (max-width: 767.98px) {
  .table-responsive-md {
    display: block;
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
  .table-responsive-md > .table-bordered {
    border: 0;
  }
}

@media (max-width: 991.98px) {
  .table-responsive-lg {
    display: block;
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
  .table-responsive-lg > .table-bordered {
    border: 0;
  }
}

@media (max-width: 1199.98px) {
  .table-responsive-xl {
    display: block;
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
  .table-responsive-xl > .table-bordered {
    border: 0;
  }
}

.table-responsive {
  display: block;
  width: 100%;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}

.table-responsive > .table-bordered {
  border: 0;
}

.form-control {
  display: block;
  width: 100%;
  height: calc(1.88em + 1.5rem + 2px);
  padding: 0.75rem 1.5625rem;
  font-size: 1.1rem;
  font-weight: 400;
  line-height: 1.88;
  color: #495057;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid #ced4da;
  border-radius: 8px;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

@media (prefers-reduced-motion: reduce) {
  .form-control {
    transition: none;
  }
}

.form-control::-ms-expand {
  background-color: transparent;
  border: 0;
}

.form-control:-moz-focusring {
  color: transparent;
  text-shadow: 0 0 0 #495057;
}

.form-control:focus {
  color: #495057;
  background-color: #fff;
  border-color: #b6b1f9;
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(71, 59, 240, 0.25);
}

.form-control::-moz-placeholder {
  color: #6c757d;
  opacity: 1;
}

.form-control:-ms-input-placeholder {
  color: #6c757d;
  opacity: 1;
}

.form-control::placeholder {
  color: #6c757d;
  opacity: 1;
}

.form-control:disabled,
.form-control[readonly] {
  background-color: #e9ecef;
  opacity: 1;
}

input[type='date'].form-control,
input[type='time'].form-control,
input[type='datetime-local'].form-control,
input[type='month'].form-control {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

select.form-control:focus::-ms-value {
  color: #495057;
  background-color: #fff;
}

.form-control-file,
.form-control-range {
  display: block;
  width: 100%;
}

.col-form-label {
  padding-top: calc(0.75rem + 1px);
  padding-bottom: calc(0.75rem + 1px);
  margin-bottom: 0;
  font-size: inherit;
  line-height: 1.88;
}

.col-form-label-lg {
  padding-top: calc(0.5rem + 1px);
  padding-bottom: calc(0.5rem + 1px);
  font-size: 1.25rem;
  line-height: 1.5;
}

.col-form-label-sm {
  padding-top: calc(0.25rem + 1px);
  padding-bottom: calc(0.25rem + 1px);
  font-size: 0.875rem;
  line-height: 1.2;
}

.form-control-plaintext {
  display: block;
  width: 100%;
  padding: 0.75rem 0;
  margin-bottom: 0;
  font-size: 1.1rem;
  line-height: 1.88;
  color: var(--color-texts-opacity);
  background-color: transparent;
  border: solid transparent;
  border-width: 1px 0;
}

.form-control-plaintext.form-control-sm,
.form-control-plaintext.form-control-lg {
  padding-right: 0;
  padding-left: 0;
}

.form-control-sm {
  height: calc(1.2em + 0.5rem + 2px);
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
  line-height: 1.2;
  border-radius: 0.2rem;
}

.form-control-lg {
  height: calc(1.5em + 1rem + 2px);
  padding: 0.5rem 1rem;
  font-size: 1.25rem;
  line-height: 1.5;
  border-radius: 0.3rem;
}

select.form-control[size],
select.form-control[multiple] {
  height: auto;
}

textarea.form-control {
  height: auto;
}

.form-group {
  margin-bottom: 1rem;
}

.form-text {
  display: block;
  margin-top: 0.25rem;
}

.form-row {
  display: flex;
  flex-wrap: wrap;
  margin-right: -5px;
  margin-left: -5px;
}

.form-row > .col,
.form-row > [class*='col-'] {
  padding-right: 5px;
  padding-left: 5px;
}

.form-check {
  position: relative;
  display: block;
  padding-left: 1.25rem;
}

.form-check-input {
  position: absolute;
  margin-top: 0.3rem;
  margin-left: -1.25rem;
}

.form-check-input[disabled] ~ .form-check-label,
.form-check-input:disabled ~ .form-check-label {
  color: #6c757d;
}

.form-check-label {
  margin-bottom: 0;
}

.form-check-inline {
  display: inline-flex;
  align-items: center;
  padding-left: 0;
  margin-right: 0.75rem;
}

.form-check-inline .form-check-input {
  position: static;
  margin-top: 0;
  margin-right: 0.3125rem;
  margin-left: 0;
}

.valid-feedback {
  display: none;
  width: 100%;
  margin-top: 0.25rem;
  font-size: 80%;
  color: #68d585;
}

.valid-tooltip {
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 5;
  display: none;
  max-width: 100%;
  padding: 0.25rem 0.5rem;
  margin-top: 0.1rem;
  font-size: 0.875rem;
  line-height: 1.5;
  color: #212529;
  background-color: rgba(104, 213, 133, 0.9);
  border-radius: 0.25rem;
}

.was-validated :valid ~ .valid-feedback,
.was-validated :valid ~ .valid-tooltip,
.is-valid ~ .valid-feedback,
.is-valid ~ .valid-tooltip {
  display: block;
}

.was-validated .form-control:valid,
.form-control.is-valid {
  border-color: #68d585;
  padding-right: calc(1.88em + 1.5rem);
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='8' height='8' viewBox='0 0 8 8'%3e%3cpath fill='%2368d585' d='M2.3 6.73L.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right calc(0.47em + 0.375rem) center;
  background-size: calc(0.94em + 0.75rem) calc(0.94em + 0.75rem);
}

.was-validated .form-control:valid:focus,
.form-control.is-valid:focus {
  border-color: #68d585;
  box-shadow: 0 0 0 0.2rem rgba(104, 213, 133, 0.25);
}

.was-validated textarea.form-control:valid,
textarea.form-control.is-valid {
  padding-right: calc(1.88em + 1.5rem);
  background-position: top calc(0.47em + 0.375rem) right calc(0.47em + 0.375rem);
}

.was-validated .custom-select:valid,
.custom-select.is-valid {
  border-color: #68d585;
  padding-right: calc(0.75em + 3.6875rem);
  background: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='4' height='5' viewBox='0 0 4 5'%3e%3cpath fill='%23343a40' d='M2 0L0 2h4zm0 5L0 3h4z'/%3e%3c/svg%3e")
      no-repeat right 1.5625rem center/8px 10px,
    url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='8' height='8' viewBox='0 0 8 8'%3e%3cpath fill='%2368d585' d='M2.3 6.73L.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/%3e%3c/svg%3e")
      #fff no-repeat center right 2.5625rem / calc(0.94em + 0.75rem)
      calc(0.94em + 0.75rem);
}

.was-validated .custom-select:valid:focus,
.custom-select.is-valid:focus {
  border-color: #68d585;
  box-shadow: 0 0 0 0.2rem rgba(104, 213, 133, 0.25);
}

.was-validated .form-check-input:valid ~ .form-check-label,
.form-check-input.is-valid ~ .form-check-label {
  color: #68d585;
}

.was-validated .form-check-input:valid ~ .valid-feedback,
.was-validated .form-check-input:valid ~ .valid-tooltip,
.form-check-input.is-valid ~ .valid-feedback,
.form-check-input.is-valid ~ .valid-tooltip {
  display: block;
}

.was-validated .custom-control-input:valid ~ .custom-control-label,
.custom-control-input.is-valid ~ .custom-control-label {
  color: #68d585;
}

.was-validated .custom-control-input:valid ~ .custom-control-label::before,
.custom-control-input.is-valid ~ .custom-control-label::before {
  border-color: #68d585;
}

.was-validated
  .custom-control-input:valid:checked
  ~ .custom-control-label::before,
.custom-control-input.is-valid:checked ~ .custom-control-label::before {
  border-color: #90e0a5;
  background-color: #90e0a5;
}

.was-validated
  .custom-control-input:valid:focus
  ~ .custom-control-label::before,
.custom-control-input.is-valid:focus ~ .custom-control-label::before {
  box-shadow: 0 0 0 0.2rem rgba(104, 213, 133, 0.25);
}

.was-validated
  .custom-control-input:valid:focus:not(:checked)
  ~ .custom-control-label::before,
.custom-control-input.is-valid:focus:not(:checked)
  ~ .custom-control-label::before {
  border-color: #68d585;
}

.was-validated .custom-file-input:valid ~ .custom-file-label,
.custom-file-input.is-valid ~ .custom-file-label {
  border-color: #68d585;
}

.was-validated .custom-file-input:valid:focus ~ .custom-file-label,
.custom-file-input.is-valid:focus ~ .custom-file-label {
  border-color: #68d585;
  box-shadow: 0 0 0 0.2rem rgba(104, 213, 133, 0.25);
}

.invalid-feedback {
  display: none;
  width: 100%;
  margin-top: 0.25rem;
  font-size: 80%;
  color: #f64b4b;
}

.invalid-tooltip {
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 5;
  display: none;
  max-width: 100%;
  padding: 0.25rem 0.5rem;
  margin-top: 0.1rem;
  font-size: 0.875rem;
  line-height: 1.5;
  color: #fff;
  background-color: rgba(246, 75, 75, 0.9);
  border-radius: 0.25rem;
}

.was-validated :invalid ~ .invalid-feedback,
.was-validated :invalid ~ .invalid-tooltip,
.is-invalid ~ .invalid-feedback,
.is-invalid ~ .invalid-tooltip {
  display: block;
}

.was-validated .form-control:invalid,
.form-control.is-invalid {
  border-color: #f64b4b;
  padding-right: calc(1.88em + 1.5rem);
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' fill='none' stroke='%23f64b4b' viewBox='0 0 12 12'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23f64b4b' stroke='none'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right calc(0.47em + 0.375rem) center;
  background-size: calc(0.94em + 0.75rem) calc(0.94em + 0.75rem);
}

.was-validated .form-control:invalid:focus,
.form-control.is-invalid:focus {
  border-color: #f64b4b;
  box-shadow: 0 0 0 0.2rem rgba(246, 75, 75, 0.25);
}

.was-validated textarea.form-control:invalid,
textarea.form-control.is-invalid {
  padding-right: calc(1.88em + 1.5rem);
  background-position: top calc(0.47em + 0.375rem) right calc(0.47em + 0.375rem);
}

.was-validated .custom-select:invalid,
.custom-select.is-invalid {
  border-color: #f64b4b;
  padding-right: calc(0.75em + 3.6875rem);
  background: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='4' height='5' viewBox='0 0 4 5'%3e%3cpath fill='%23343a40' d='M2 0L0 2h4zm0 5L0 3h4z'/%3e%3c/svg%3e")
      no-repeat right 1.5625rem center/8px 10px,
    url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' fill='none' stroke='%23f64b4b' viewBox='0 0 12 12'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23f64b4b' stroke='none'/%3e%3c/svg%3e")
      #fff no-repeat center right 2.5625rem / calc(0.94em + 0.75rem)
      calc(0.94em + 0.75rem);
}

.was-validated .custom-select:invalid:focus,
.custom-select.is-invalid:focus {
  border-color: #f64b4b;
  box-shadow: 0 0 0 0.2rem rgba(246, 75, 75, 0.25);
}

.was-validated .form-check-input:invalid ~ .form-check-label,
.form-check-input.is-invalid ~ .form-check-label {
  color: #f64b4b;
}

.was-validated .form-check-input:invalid ~ .invalid-feedback,
.was-validated .form-check-input:invalid ~ .invalid-tooltip,
.form-check-input.is-invalid ~ .invalid-feedback,
.form-check-input.is-invalid ~ .invalid-tooltip {
  display: block;
}

.was-validated .custom-control-input:invalid ~ .custom-control-label,
.custom-control-input.is-invalid ~ .custom-control-label {
  color: #f64b4b;
}

.was-validated .custom-control-input:invalid ~ .custom-control-label::before,
.custom-control-input.is-invalid ~ .custom-control-label::before {
  border-color: #f64b4b;
}

.was-validated
  .custom-control-input:invalid:checked
  ~ .custom-control-label::before,
.custom-control-input.is-invalid:checked ~ .custom-control-label::before {
  border-color: #f87c7c;
  background-color: #f87c7c;
}

.was-validated
  .custom-control-input:invalid:focus
  ~ .custom-control-label::before,
.custom-control-input.is-invalid:focus ~ .custom-control-label::before {
  box-shadow: 0 0 0 0.2rem rgba(246, 75, 75, 0.25);
}

.was-validated
  .custom-control-input:invalid:focus:not(:checked)
  ~ .custom-control-label::before,
.custom-control-input.is-invalid:focus:not(:checked)
  ~ .custom-control-label::before {
  border-color: #f64b4b;
}

.was-validated .custom-file-input:invalid ~ .custom-file-label,
.custom-file-input.is-invalid ~ .custom-file-label {
  border-color: #f64b4b;
}

.was-validated .custom-file-input:invalid:focus ~ .custom-file-label,
.custom-file-input.is-invalid:focus ~ .custom-file-label {
  border-color: #f64b4b;
  box-shadow: 0 0 0 0.2rem rgba(246, 75, 75, 0.25);
}

.form-inline {
  display: flex;
  flex-flow: row wrap;
  align-items: center;
}

.form-inline .form-check {
  width: 100%;
}

@media (min-width: 576px) {
  .form-inline label {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 0;
  }
  .form-inline .form-group {
    display: flex;
    flex: 0 0 auto;
    flex-flow: row wrap;
    align-items: center;
    margin-bottom: 0;
  }
  .form-inline .form-control {
    display: inline-block;
    width: auto;
    vertical-align: middle;
  }
  .form-inline .form-control-plaintext {
    display: inline-block;
  }
  .form-inline .input-group,
  .form-inline .custom-select {
    width: auto;
  }
  .form-inline .form-check {
    display: flex;
    align-items: center;
    justify-content: center;
    width: auto;
    padding-left: 0;
  }
  .form-inline .form-check-input {
    position: relative;
    flex-shrink: 0;
    margin-top: 0;
    margin-right: 0.25rem;
    margin-left: 0;
  }
  .form-inline .custom-control {
    align-items: center;
    justify-content: center;
  }
  .form-inline .custom-control-label {
    margin-bottom: 0;
  }
}

.btn {
  display: inline-block;
  font-weight: 700;
  color: var(--color-texts-opacity);
  text-align: center;
  vertical-align: middle;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  background-color: transparent;
  border: 1px solid transparent;
  padding: 0.75rem 1.5625rem;
  font-size: 1.1rem;
  line-height: 1.88;
  border-radius: 8px;
  transition: 0.4s cubic-bezier(0.39, 0.575, 0.565, 1);
}

@media (prefers-reduced-motion: reduce) {
  .btn {
    transition: none;
  }
}

.btn:hover {
  color: var(--color-texts-opacity);
  text-decoration: none;
}

.btn:focus,
.btn.focus {
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(71, 59, 240, 0.25);
}

.btn.disabled,
.btn:disabled {
  opacity: 0.65;
}

.btn:not(:disabled):not(.disabled) {
  cursor: pointer;
}

a.btn.disabled,
fieldset:disabled a.btn {
  pointer-events: none;
}

.btn-primary {
  color: #fff;
  background-color: #473bf0;
  border-color: #473bf0;
}

.btn-primary:hover {
  color: #fff;
  background-color: #2617ed;
  border-color: #2012e6;
}

.btn-primary:focus,
.btn-primary.focus {
  color: #fff;
  background-color: #2617ed;
  border-color: #2012e6;
  box-shadow: 0 0 0 0.2rem rgba(99, 88, 242, 0.5);
}

.btn-primary.disabled,
.btn-primary:disabled {
  color: #fff;
  background-color: #473bf0;
  border-color: #473bf0;
}

.btn-primary:not(:disabled):not(.disabled):active,
.btn-primary:not(:disabled):not(.disabled).active,
.show > .btn-primary.dropdown-toggle {
  color: #fff;
  background-color: #2012e6;
  border-color: #1e11db;
}

.btn-primary:not(:disabled):not(.disabled):active:focus,
.btn-primary:not(:disabled):not(.disabled).active:focus,
.show > .btn-primary.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(99, 88, 242, 0.5);
}

.btn-secondary {
  color: #212529;
  background-color: #68d585;
  border-color: #68d585;
}

.btn-secondary:hover {
  color: #212529;
  background-color: #4acd6d;
  border-color: #40ca65;
}

.btn-secondary:focus,
.btn-secondary.focus {
  color: #212529;
  background-color: #4acd6d;
  border-color: #40ca65;
  box-shadow: 0 0 0 0.2rem rgba(93, 187, 119, 0.5);
}

.btn-secondary.disabled,
.btn-secondary:disabled {
  color: #212529;
  background-color: #68d585;
  border-color: #68d585;
}

.btn-secondary:not(:disabled):not(.disabled):active,
.btn-secondary:not(:disabled):not(.disabled).active,
.show > .btn-secondary.dropdown-toggle {
  color: #fff;
  background-color: #40ca65;
  border-color: #37c65d;
}

.btn-secondary:not(:disabled):not(.disabled):active:focus,
.btn-secondary:not(:disabled):not(.disabled).active:focus,
.show > .btn-secondary.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(93, 187, 119, 0.5);
}

.btn-success {
  color: #212529;
  background-color: #68d585;
  border-color: #68d585;
}

.btn-success:hover {
  color: #212529;
  background-color: #4acd6d;
  border-color: #40ca65;
}

.btn-success:focus,
.btn-success.focus {
  color: #212529;
  background-color: #4acd6d;
  border-color: #40ca65;
  box-shadow: 0 0 0 0.2rem rgba(93, 187, 119, 0.5);
}

.btn-success.disabled,
.btn-success:disabled {
  color: #212529;
  background-color: #68d585;
  border-color: #68d585;
}

.btn-success:not(:disabled):not(.disabled):active,
.btn-success:not(:disabled):not(.disabled).active,
.show > .btn-success.dropdown-toggle {
  color: #fff;
  background-color: #40ca65;
  border-color: #37c65d;
}

.btn-success:not(:disabled):not(.disabled):active:focus,
.btn-success:not(:disabled):not(.disabled).active:focus,
.show > .btn-success.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(93, 187, 119, 0.5);
}

.btn-info {
  color: #fff;
  background-color: #17a2b8;
  border-color: #17a2b8;
}

.btn-info:hover {
  color: #fff;
  background-color: #138496;
  border-color: #117a8b;
}

.btn-info:focus,
.btn-info.focus {
  color: #fff;
  background-color: #138496;
  border-color: #117a8b;
  box-shadow: 0 0 0 0.2rem rgba(58, 176, 195, 0.5);
}

.btn-info.disabled,
.btn-info:disabled {
  color: #fff;
  background-color: #17a2b8;
  border-color: #17a2b8;
}

.btn-info:not(:disabled):not(.disabled):active,
.btn-info:not(:disabled):not(.disabled).active,
.show > .btn-info.dropdown-toggle {
  color: #fff;
  background-color: #117a8b;
  border-color: #10707f;
}

.btn-info:not(:disabled):not(.disabled):active:focus,
.btn-info:not(:disabled):not(.disabled).active:focus,
.show > .btn-info.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(58, 176, 195, 0.5);
}

.btn-warning {
  color: #212529;
  background-color: #f7e36d;
  border-color: #f7e36d;
}

.btn-warning:hover {
  color: #212529;
  background-color: #f5dc49;
  border-color: #f4da3d;
}

.btn-warning:focus,
.btn-warning.focus {
  color: #212529;
  background-color: #f5dc49;
  border-color: #f4da3d;
  box-shadow: 0 0 0 0.2rem rgba(215, 199, 99, 0.5);
}

.btn-warning.disabled,
.btn-warning:disabled {
  color: #212529;
  background-color: #f7e36d;
  border-color: #f7e36d;
}

.btn-warning:not(:disabled):not(.disabled):active,
.btn-warning:not(:disabled):not(.disabled).active,
.show > .btn-warning.dropdown-toggle {
  color: #212529;
  background-color: #f4da3d;
  border-color: #f4d731;
}

.btn-warning:not(:disabled):not(.disabled):active:focus,
.btn-warning:not(:disabled):not(.disabled).active:focus,
.show > .btn-warning.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(215, 199, 99, 0.5);
}

.btn-danger {
  color: #fff;
  background-color: #f64b4b;
  border-color: #f64b4b;
}

.btn-danger:hover {
  color: #fff;
  background-color: #f42727;
  border-color: #f41a1a;
}

.btn-danger:focus,
.btn-danger.focus {
  color: #fff;
  background-color: #f42727;
  border-color: #f41a1a;
  box-shadow: 0 0 0 0.2rem rgba(247, 102, 102, 0.5);
}

.btn-danger.disabled,
.btn-danger:disabled {
  color: #fff;
  background-color: #f64b4b;
  border-color: #f64b4b;
}

.btn-danger:not(:disabled):not(.disabled):active,
.btn-danger:not(:disabled):not(.disabled).active,
.show > .btn-danger.dropdown-toggle {
  color: #fff;
  background-color: #f41a1a;
  border-color: #f30e0e;
}

.btn-danger:not(:disabled):not(.disabled):active:focus,
.btn-danger:not(:disabled):not(.disabled).active:focus,
.show > .btn-danger.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(247, 102, 102, 0.5);
}

.btn-light {
  color: #212529;
  background-color: #f8f9fa;
  border-color: #f8f9fa;
}

.btn-light:hover {
  color: #212529;
  background-color: #e2e6ea;
  border-color: #dae0e5;
}

.btn-light:focus,
.btn-light.focus {
  color: #212529;
  background-color: #e2e6ea;
  border-color: #dae0e5;
  box-shadow: 0 0 0 0.2rem rgba(216, 217, 219, 0.5);
}

.btn-light.disabled,
.btn-light:disabled {
  color: #212529;
  background-color: #f8f9fa;
  border-color: #f8f9fa;
}

.btn-light:not(:disabled):not(.disabled):active,
.btn-light:not(:disabled):not(.disabled).active,
.show > .btn-light.dropdown-toggle {
  color: #212529;
  background-color: #dae0e5;
  border-color: #d3d9df;
}

.btn-light:not(:disabled):not(.disabled):active:focus,
.btn-light:not(:disabled):not(.disabled).active:focus,
.show > .btn-light.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(216, 217, 219, 0.5);
}

.btn-dark {
  color: #fff;
  background-color: #343a40;
  border-color: #343a40;
}

.btn-dark:hover {
  color: #fff;
  background-color: #23272b;
  border-color: #1d2124;
}

.btn-dark:focus,
.btn-dark.focus {
  color: #fff;
  background-color: #23272b;
  border-color: #1d2124;
  box-shadow: 0 0 0 0.2rem rgba(82, 88, 93, 0.5);
}

.btn-dark.disabled,
.btn-dark:disabled {
  color: #fff;
  background-color: #343a40;
  border-color: #343a40;
}

.btn-dark:not(:disabled):not(.disabled):active,
.btn-dark:not(:disabled):not(.disabled).active,
.show > .btn-dark.dropdown-toggle {
  color: #fff;
  background-color: #1d2124;
  border-color: #171a1d;
}

.btn-dark:not(:disabled):not(.disabled):active:focus,
.btn-dark:not(:disabled):not(.disabled).active:focus,
.show > .btn-dark.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(82, 88, 93, 0.5);
}

.btn-red {
  color: #fff;
  background-color: #f64b4b;
  border-color: #f64b4b;
}

.btn-red:hover {
  color: #fff;
  background-color: #f42727;
  border-color: #f41a1a;
}

.btn-red:focus,
.btn-red.focus {
  color: #fff;
  background-color: #f42727;
  border-color: #f41a1a;
  box-shadow: 0 0 0 0.2rem rgba(247, 102, 102, 0.5);
}

.btn-red.disabled,
.btn-red:disabled {
  color: #fff;
  background-color: #f64b4b;
  border-color: #f64b4b;
}

.btn-red:not(:disabled):not(.disabled):active,
.btn-red:not(:disabled):not(.disabled).active,
.show > .btn-red.dropdown-toggle {
  color: #fff;
  background-color: #f41a1a;
  border-color: #f30e0e;
}

.btn-red:not(:disabled):not(.disabled):active:focus,
.btn-red:not(:disabled):not(.disabled).active:focus,
.show > .btn-red.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(247, 102, 102, 0.5);
}

.btn-green {
  color: #212529;
  background-color: #68d585;
  border-color: #68d585;
}

.btn-green:hover {
  color: #212529;
  background-color: #4acd6d;
  border-color: #40ca65;
}

.btn-green:focus,
.btn-green.focus {
  color: #212529;
  background-color: #4acd6d;
  border-color: #40ca65;
  box-shadow: 0 0 0 0.2rem rgba(93, 187, 119, 0.5);
}

.btn-green.disabled,
.btn-green:disabled {
  color: #212529;
  background-color: #68d585;
  border-color: #68d585;
}

.btn-green:not(:disabled):not(.disabled):active,
.btn-green:not(:disabled):not(.disabled).active,
.show > .btn-green.dropdown-toggle {
  color: #fff;
  background-color: #40ca65;
  border-color: #37c65d;
}

.btn-green:not(:disabled):not(.disabled):active:focus,
.btn-green:not(:disabled):not(.disabled).active:focus,
.show > .btn-green.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(93, 187, 119, 0.5);
}

.btn-green-shamrock {
  color: #212529;
  background-color: #2bd67b;
  border-color: #2bd67b;
}

.btn-green-shamrock:hover {
  color: #fff;
  background-color: #23b769;
  border-color: #21ad63;
}

.btn-green-shamrock:focus,
.btn-green-shamrock.focus {
  color: #fff;
  background-color: #23b769;
  border-color: #21ad63;
  box-shadow: 0 0 0 0.2rem rgba(42, 187, 111, 0.5);
}

.btn-green-shamrock.disabled,
.btn-green-shamrock:disabled {
  color: #212529;
  background-color: #2bd67b;
  border-color: #2bd67b;
}

.btn-green-shamrock:not(:disabled):not(.disabled):active,
.btn-green-shamrock:not(:disabled):not(.disabled).active,
.show > .btn-green-shamrock.dropdown-toggle {
  color: #fff;
  background-color: #21ad63;
  border-color: #1fa25c;
}

.btn-green-shamrock:not(:disabled):not(.disabled):active:focus,
.btn-green-shamrock:not(:disabled):not(.disabled).active:focus,
.show > .btn-green-shamrock.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(42, 187, 111, 0.5);
}

.btn-blue {
  color: #fff;
  background-color: #473bf0;
  border-color: #473bf0;
}

.btn-blue:hover {
  color: #fff;
  background-color: #2617ed;
  border-color: #2012e6;
}

.btn-blue:focus,
.btn-blue.focus {
  color: #fff;
  background-color: #2617ed;
  border-color: #2012e6;
  box-shadow: 0 0 0 0.2rem rgba(99, 88, 242, 0.5);
}

.btn-blue.disabled,
.btn-blue:disabled {
  color: #fff;
  background-color: #473bf0;
  border-color: #473bf0;
}

.btn-blue:not(:disabled):not(.disabled):active,
.btn-blue:not(:disabled):not(.disabled).active,
.show > .btn-blue.dropdown-toggle {
  color: #fff;
  background-color: #2012e6;
  border-color: #1e11db;
}

.btn-blue:not(:disabled):not(.disabled):active:focus,
.btn-blue:not(:disabled):not(.disabled).active:focus,
.show > .btn-blue.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(99, 88, 242, 0.5);
}

.btn-sky-blue {
  color: #fff;
  background-color: #1082e9;
  border-color: #1082e9;
}

.btn-sky-blue:hover {
  color: #fff;
  background-color: #0e6ec5;
  border-color: #0d67b9;
}

.btn-sky-blue:focus,
.btn-sky-blue.focus {
  color: #fff;
  background-color: #0e6ec5;
  border-color: #0d67b9;
  box-shadow: 0 0 0 0.2rem rgba(52, 149, 236, 0.5);
}

.btn-sky-blue.disabled,
.btn-sky-blue:disabled {
  color: #fff;
  background-color: #1082e9;
  border-color: #1082e9;
}

.btn-sky-blue:not(:disabled):not(.disabled):active,
.btn-sky-blue:not(:disabled):not(.disabled).active,
.show > .btn-sky-blue.dropdown-toggle {
  color: #fff;
  background-color: #0d67b9;
  border-color: #0c61ad;
}

.btn-sky-blue:not(:disabled):not(.disabled):active:focus,
.btn-sky-blue:not(:disabled):not(.disabled).active:focus,
.show > .btn-sky-blue.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(52, 149, 236, 0.5);
}

.btn-yellow {
  color: #212529;
  background-color: #f7e36d;
  border-color: #f7e36d;
}

.btn-yellow:hover {
  color: #212529;
  background-color: #f5dc49;
  border-color: #f4da3d;
}

.btn-yellow:focus,
.btn-yellow.focus {
  color: #212529;
  background-color: #f5dc49;
  border-color: #f4da3d;
  box-shadow: 0 0 0 0.2rem rgba(215, 199, 99, 0.5);
}

.btn-yellow.disabled,
.btn-yellow:disabled {
  color: #212529;
  background-color: #f7e36d;
  border-color: #f7e36d;
}

.btn-yellow:not(:disabled):not(.disabled):active,
.btn-yellow:not(:disabled):not(.disabled).active,
.show > .btn-yellow.dropdown-toggle {
  color: #212529;
  background-color: #f4da3d;
  border-color: #f4d731;
}

.btn-yellow:not(:disabled):not(.disabled):active:focus,
.btn-yellow:not(:disabled):not(.disabled).active:focus,
.show > .btn-yellow.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(215, 199, 99, 0.5);
}

.btn-yellow-orange {
  color: #212529;
  background-color: #fcad38;
  border-color: #fcad38;
}

.btn-yellow-orange:hover {
  color: #212529;
  background-color: #fb9d12;
  border-color: #fb9806;
}

.btn-yellow-orange:focus,
.btn-yellow-orange.focus {
  color: #212529;
  background-color: #fb9d12;
  border-color: #fb9806;
  box-shadow: 0 0 0 0.2rem rgba(219, 153, 54, 0.5);
}

.btn-yellow-orange.disabled,
.btn-yellow-orange:disabled {
  color: #212529;
  background-color: #fcad38;
  border-color: #fcad38;
}

.btn-yellow-orange:not(:disabled):not(.disabled):active,
.btn-yellow-orange:not(:disabled):not(.disabled).active,
.show > .btn-yellow-orange.dropdown-toggle {
  color: #212529;
  background-color: #fb9806;
  border-color: #f19104;
}

.btn-yellow-orange:not(:disabled):not(.disabled):active:focus,
.btn-yellow-orange:not(:disabled):not(.disabled).active:focus,
.show > .btn-yellow-orange.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(219, 153, 54, 0.5);
}

.btn-blackish-blue {
  color: #fff;
  background-color: #13151c;
  border-color: #13151c;
}

.btn-blackish-blue:hover {
  color: #fff;
  background-color: #040405;
  border-color: black;
}

.btn-blackish-blue:focus,
.btn-blackish-blue.focus {
  color: #fff;
  background-color: #040405;
  border-color: black;
  box-shadow: 0 0 0 0.2rem rgba(54, 56, 62, 0.5);
}

.btn-blackish-blue.disabled,
.btn-blackish-blue:disabled {
  color: #fff;
  background-color: #13151c;
  border-color: #13151c;
}

.btn-blackish-blue:not(:disabled):not(.disabled):active,
.btn-blackish-blue:not(:disabled):not(.disabled).active,
.show > .btn-blackish-blue.dropdown-toggle {
  color: #fff;
  background-color: black;
  border-color: black;
}

.btn-blackish-blue:not(:disabled):not(.disabled):active:focus,
.btn-blackish-blue:not(:disabled):not(.disabled).active:focus,
.show > .btn-blackish-blue.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(54, 56, 62, 0.5);
}

.btn-black {
  color: #fff;
  background-color: #000;
  border-color: #000;
}

.btn-black:hover {
  color: #fff;
  background-color: black;
  border-color: black;
}

.btn-black:focus,
.btn-black.focus {
  color: #fff;
  background-color: black;
  border-color: black;
  box-shadow: 0 0 0 0.2rem rgba(38, 38, 38, 0.5);
}

.btn-black.disabled,
.btn-black:disabled {
  color: #fff;
  background-color: #000;
  border-color: #000;
}

.btn-black:not(:disabled):not(.disabled):active,
.btn-black:not(:disabled):not(.disabled).active,
.show > .btn-black.dropdown-toggle {
  color: #fff;
  background-color: black;
  border-color: black;
}

.btn-black:not(:disabled):not(.disabled):active:focus,
.btn-black:not(:disabled):not(.disabled).active:focus,
.show > .btn-black.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(38, 38, 38, 0.5);
}

.btn-mirage {
  color: #fff;
  background-color: #131829;
  border-color: #131829;
}

.btn-mirage:hover {
  color: #fff;
  background-color: #07090f;
  border-color: #030406;
}

.btn-mirage:focus,
.btn-mirage.focus {
  color: #fff;
  background-color: #07090f;
  border-color: #030406;
  box-shadow: 0 0 0 0.2rem rgba(54, 59, 73, 0.5);
}

.btn-mirage.disabled,
.btn-mirage:disabled {
  color: #fff;
  background-color: #131829;
  border-color: #131829;
}

.btn-mirage:not(:disabled):not(.disabled):active,
.btn-mirage:not(:disabled):not(.disabled).active,
.show > .btn-mirage.dropdown-toggle {
  color: #fff;
  background-color: #030406;
  border-color: black;
}

.btn-mirage:not(:disabled):not(.disabled):active:focus,
.btn-mirage:not(:disabled):not(.disabled).active:focus,
.show > .btn-mirage.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(54, 59, 73, 0.5);
}

.btn-mirage-2 {
  color: #fff;
  background-color: #161c2d;
  border-color: #161c2d;
}

.btn-mirage-2:hover {
  color: #fff;
  background-color: #090c13;
  border-color: #05070b;
}

.btn-mirage-2:focus,
.btn-mirage-2.focus {
  color: #fff;
  background-color: #090c13;
  border-color: #05070b;
  box-shadow: 0 0 0 0.2rem rgba(57, 62, 77, 0.5);
}

.btn-mirage-2.disabled,
.btn-mirage-2:disabled {
  color: #fff;
  background-color: #161c2d;
  border-color: #161c2d;
}

.btn-mirage-2:not(:disabled):not(.disabled):active,
.btn-mirage-2:not(:disabled):not(.disabled).active,
.show > .btn-mirage-2.dropdown-toggle {
  color: #fff;
  background-color: #05070b;
  border-color: #010102;
}

.btn-mirage-2:not(:disabled):not(.disabled):active:focus,
.btn-mirage-2:not(:disabled):not(.disabled).active:focus,
.show > .btn-mirage-2.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(57, 62, 77, 0.5);
}

.btn-white {
  color: #212529;
  background-color: #fff;
  border-color: #fff;
}

.btn-white:hover {
  color: #212529;
  background-color: #ececec;
  border-color: #e6e6e6;
}

.btn-white:focus,
.btn-white.focus {
  color: #212529;
  background-color: #ececec;
  border-color: #e6e6e6;
  box-shadow: 0 0 0 0.2rem rgba(222, 222, 223, 0.5);
}

.btn-white.disabled,
.btn-white:disabled {
  color: #212529;
  background-color: #fff;
  border-color: #fff;
}

.btn-white:not(:disabled):not(.disabled):active,
.btn-white:not(:disabled):not(.disabled).active,
.show > .btn-white.dropdown-toggle {
  color: #212529;
  background-color: #e6e6e6;
  border-color: #dfdfdf;
}

.btn-white:not(:disabled):not(.disabled):active:focus,
.btn-white:not(:disabled):not(.disabled).active:focus,
.show > .btn-white.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(222, 222, 223, 0.5);
}

.btn-smoke {
  color: #212529;
  background-color: #f8f8f8;
  border-color: #f8f8f8;
}

.btn-smoke:hover {
  color: #212529;
  background-color: #e5e5e5;
  border-color: #dfdfdf;
}

.btn-smoke:focus,
.btn-smoke.focus {
  color: #212529;
  background-color: #e5e5e5;
  border-color: #dfdfdf;
  box-shadow: 0 0 0 0.2rem rgba(216, 216, 217, 0.5);
}

.btn-smoke.disabled,
.btn-smoke:disabled {
  color: #212529;
  background-color: #f8f8f8;
  border-color: #f8f8f8;
}

.btn-smoke:not(:disabled):not(.disabled):active,
.btn-smoke:not(:disabled):not(.disabled).active,
.show > .btn-smoke.dropdown-toggle {
  color: #212529;
  background-color: #dfdfdf;
  border-color: #d8d8d8;
}

.btn-smoke:not(:disabled):not(.disabled):active:focus,
.btn-smoke:not(:disabled):not(.disabled).active:focus,
.show > .btn-smoke.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(216, 216, 217, 0.5);
}

.btn-storm {
  color: #fff;
  background-color: #7d818d;
  border-color: #7d818d;
}

.btn-storm:hover {
  color: #fff;
  background-color: #6a6e79;
  border-color: #646873;
}

.btn-storm:focus,
.btn-storm.focus {
  color: #fff;
  background-color: #6a6e79;
  border-color: #646873;
  box-shadow: 0 0 0 0.2rem rgba(145, 148, 158, 0.5);
}

.btn-storm.disabled,
.btn-storm:disabled {
  color: #fff;
  background-color: #7d818d;
  border-color: #7d818d;
}

.btn-storm:not(:disabled):not(.disabled):active,
.btn-storm:not(:disabled):not(.disabled).active,
.show > .btn-storm.dropdown-toggle {
  color: #fff;
  background-color: #646873;
  border-color: #5e626c;
}

.btn-storm:not(:disabled):not(.disabled):active:focus,
.btn-storm:not(:disabled):not(.disabled).active:focus,
.show > .btn-storm.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(145, 148, 158, 0.5);
}

.btn-ghost {
  color: #212529;
  background-color: #fdfdff;
  border-color: #fdfdff;
}

.btn-ghost:hover {
  color: #212529;
  background-color: #d7d7ff;
  border-color: #cacaff;
}

.btn-ghost:focus,
.btn-ghost.focus {
  color: #212529;
  background-color: #d7d7ff;
  border-color: #cacaff;
  box-shadow: 0 0 0 0.2rem rgba(220, 221, 223, 0.5);
}

.btn-ghost.disabled,
.btn-ghost:disabled {
  color: #212529;
  background-color: #fdfdff;
  border-color: #fdfdff;
}

.btn-ghost:not(:disabled):not(.disabled):active,
.btn-ghost:not(:disabled):not(.disabled).active,
.show > .btn-ghost.dropdown-toggle {
  color: #212529;
  background-color: #cacaff;
  border-color: #bdbdff;
}

.btn-ghost:not(:disabled):not(.disabled):active:focus,
.btn-ghost:not(:disabled):not(.disabled).active:focus,
.show > .btn-ghost.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(220, 221, 223, 0.5);
}

.btn-gray-1 {
  color: #212529;
  background-color: #fcfdfe;
  border-color: #fcfdfe;
}

.btn-gray-1:hover {
  color: #212529;
  background-color: #dfeaf4;
  border-color: #d6e4f1;
}

.btn-gray-1:focus,
.btn-gray-1.focus {
  color: #212529;
  background-color: #dfeaf4;
  border-color: #d6e4f1;
  box-shadow: 0 0 0 0.2rem rgba(219, 221, 222, 0.5);
}

.btn-gray-1.disabled,
.btn-gray-1:disabled {
  color: #212529;
  background-color: #fcfdfe;
  border-color: #fcfdfe;
}

.btn-gray-1:not(:disabled):not(.disabled):active,
.btn-gray-1:not(:disabled):not(.disabled).active,
.show > .btn-gray-1.dropdown-toggle {
  color: #212529;
  background-color: #d6e4f1;
  border-color: #ccddee;
}

.btn-gray-1:not(:disabled):not(.disabled):active:focus,
.btn-gray-1:not(:disabled):not(.disabled).active:focus,
.show > .btn-gray-1.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(219, 221, 222, 0.5);
}

.btn-gray-2 {
  color: #212529;
  background-color: #f4f7fa;
  border-color: #f4f7fa;
}

.btn-gray-2:hover {
  color: #212529;
  background-color: #dae4ee;
  border-color: #d1deea;
}

.btn-gray-2:focus,
.btn-gray-2.focus {
  color: #212529;
  background-color: #dae4ee;
  border-color: #d1deea;
  box-shadow: 0 0 0 0.2rem rgba(212, 216, 219, 0.5);
}

.btn-gray-2.disabled,
.btn-gray-2:disabled {
  color: #212529;
  background-color: #f4f7fa;
  border-color: #f4f7fa;
}

.btn-gray-2:not(:disabled):not(.disabled):active,
.btn-gray-2:not(:disabled):not(.disabled).active,
.show > .btn-gray-2.dropdown-toggle {
  color: #212529;
  background-color: #d1deea;
  border-color: #c8d7e6;
}

.btn-gray-2:not(:disabled):not(.disabled):active:focus,
.btn-gray-2:not(:disabled):not(.disabled).active:focus,
.show > .btn-gray-2.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(212, 216, 219, 0.5);
}

.btn-gray-3 {
  color: #212529;
  background-color: #e7e9ed;
  border-color: #e7e9ed;
}

.btn-gray-3:hover {
  color: #212529;
  background-color: #d1d5dd;
  border-color: #caced7;
}

.btn-gray-3:focus,
.btn-gray-3.focus {
  color: #212529;
  background-color: #d1d5dd;
  border-color: #caced7;
  box-shadow: 0 0 0 0.2rem rgba(201, 204, 208, 0.5);
}

.btn-gray-3.disabled,
.btn-gray-3:disabled {
  color: #212529;
  background-color: #e7e9ed;
  border-color: #e7e9ed;
}

.btn-gray-3:not(:disabled):not(.disabled):active,
.btn-gray-3:not(:disabled):not(.disabled).active,
.show > .btn-gray-3.dropdown-toggle {
  color: #212529;
  background-color: #caced7;
  border-color: #c3c8d2;
}

.btn-gray-3:not(:disabled):not(.disabled):active:focus,
.btn-gray-3:not(:disabled):not(.disabled).active:focus,
.show > .btn-gray-3.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(201, 204, 208, 0.5);
}

.btn-gray-310 {
  color: #212529;
  background-color: #d5d7dd;
  border-color: #d5d7dd;
}

.btn-gray-310:hover {
  color: #212529;
  background-color: #c0c3cc;
  border-color: #b9bcc6;
}

.btn-gray-310:focus,
.btn-gray-310.focus {
  color: #212529;
  background-color: #c0c3cc;
  border-color: #b9bcc6;
  box-shadow: 0 0 0 0.2rem rgba(186, 188, 194, 0.5);
}

.btn-gray-310.disabled,
.btn-gray-310:disabled {
  color: #212529;
  background-color: #d5d7dd;
  border-color: #d5d7dd;
}

.btn-gray-310:not(:disabled):not(.disabled):active,
.btn-gray-310:not(:disabled):not(.disabled).active,
.show > .btn-gray-310.dropdown-toggle {
  color: #212529;
  background-color: #b9bcc6;
  border-color: #b2b5c0;
}

.btn-gray-310:not(:disabled):not(.disabled):active:focus,
.btn-gray-310:not(:disabled):not(.disabled).active:focus,
.show > .btn-gray-310.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(186, 188, 194, 0.5);
}

.btn-gray-opacity {
  color: #212529;
  background-color: rgba(231, 233, 237, 0.13);
  border-color: rgba(231, 233, 237, 0.13);
}

.btn-gray-opacity:hover {
  color: #212529;
  background-color: rgba(209, 213, 221, 0.13);
  border-color: rgba(202, 206, 215, 0.13);
}

.btn-gray-opacity:focus,
.btn-gray-opacity.focus {
  color: #212529;
  background-color: rgba(209, 213, 221, 0.13);
  border-color: rgba(202, 206, 215, 0.13);
  box-shadow: 0 0 0 0.2rem rgba(89, 92, 96, 0.5);
}

.btn-gray-opacity.disabled,
.btn-gray-opacity:disabled {
  color: #212529;
  background-color: rgba(231, 233, 237, 0.13);
  border-color: rgba(231, 233, 237, 0.13);
}

.btn-gray-opacity:not(:disabled):not(.disabled):active,
.btn-gray-opacity:not(:disabled):not(.disabled).active,
.show > .btn-gray-opacity.dropdown-toggle {
  color: #212529;
  background-color: rgba(202, 206, 215, 0.13);
  border-color: rgba(195, 200, 210, 0.13);
}

.btn-gray-opacity:not(:disabled):not(.disabled):active:focus,
.btn-gray-opacity:not(:disabled):not(.disabled).active:focus,
.show > .btn-gray-opacity.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(89, 92, 96, 0.5);
}

.btn-blackish-blue-opacity {
  color: #fff;
  background-color: rgba(22, 28, 45, 0.7);
  border-color: rgba(22, 28, 45, 0.7);
}

.btn-blackish-blue-opacity:hover {
  color: #fff;
  background-color: rgba(9, 12, 19, 0.7);
  border-color: rgba(5, 7, 11, 0.7);
}

.btn-blackish-blue-opacity:focus,
.btn-blackish-blue-opacity.focus {
  color: #fff;
  background-color: rgba(9, 12, 19, 0.7);
  border-color: rgba(5, 7, 11, 0.7);
  box-shadow: 0 0 0 0.2rem rgba(80, 84, 97, 0.5);
}

.btn-blackish-blue-opacity.disabled,
.btn-blackish-blue-opacity:disabled {
  color: #fff;
  background-color: rgba(22, 28, 45, 0.7);
  border-color: rgba(22, 28, 45, 0.7);
}

.btn-blackish-blue-opacity:not(:disabled):not(.disabled):active,
.btn-blackish-blue-opacity:not(:disabled):not(.disabled).active,
.show > .btn-blackish-blue-opacity.dropdown-toggle {
  color: #fff;
  background-color: rgba(5, 7, 11, 0.7);
  border-color: rgba(1, 1, 2, 0.7);
}

.btn-blackish-blue-opacity:not(:disabled):not(.disabled):active:focus,
.btn-blackish-blue-opacity:not(:disabled):not(.disabled).active:focus,
.show > .btn-blackish-blue-opacity.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(80, 84, 97, 0.5);
}

.btn-narvik {
  color: #212529;
  background-color: #edf9f2;
  border-color: #edf9f2;
}

.btn-narvik:hover {
  color: #212529;
  background-color: #d0efdd;
  border-color: #c7ecd6;
}

.btn-narvik:focus,
.btn-narvik.focus {
  color: #212529;
  background-color: #d0efdd;
  border-color: #c7ecd6;
  box-shadow: 0 0 0 0.2rem rgba(206, 217, 212, 0.5);
}

.btn-narvik.disabled,
.btn-narvik:disabled {
  color: #212529;
  background-color: #edf9f2;
  border-color: #edf9f2;
}

.btn-narvik:not(:disabled):not(.disabled):active,
.btn-narvik:not(:disabled):not(.disabled).active,
.show > .btn-narvik.dropdown-toggle {
  color: #212529;
  background-color: #c7ecd6;
  border-color: #bde9cf;
}

.btn-narvik:not(:disabled):not(.disabled):active:focus,
.btn-narvik:not(:disabled):not(.disabled).active:focus,
.show > .btn-narvik.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(206, 217, 212, 0.5);
}

.btn-outline-primary {
  color: #473bf0;
  border-color: #473bf0;
}

.btn-outline-primary:hover {
  color: #fff;
  background-color: #473bf0;
  border-color: #473bf0;
}

.btn-outline-primary:focus,
.btn-outline-primary.focus {
  box-shadow: 0 0 0 0.2rem rgba(71, 59, 240, 0.5);
}

.btn-outline-primary.disabled,
.btn-outline-primary:disabled {
  color: #473bf0;
  background-color: transparent;
}

.btn-outline-primary:not(:disabled):not(.disabled):active,
.btn-outline-primary:not(:disabled):not(.disabled).active,
.show > .btn-outline-primary.dropdown-toggle {
  color: #fff;
  background-color: #473bf0;
  border-color: #473bf0;
}

.btn-outline-primary:not(:disabled):not(.disabled):active:focus,
.btn-outline-primary:not(:disabled):not(.disabled).active:focus,
.show > .btn-outline-primary.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(71, 59, 240, 0.5);
}

.btn-outline-secondary {
  color: #68d585;
  border-color: #68d585;
}

.btn-outline-secondary:hover {
  color: #212529;
  background-color: #68d585;
  border-color: #68d585;
}

.btn-outline-secondary:focus,
.btn-outline-secondary.focus {
  box-shadow: 0 0 0 0.2rem rgba(104, 213, 133, 0.5);
}

.btn-outline-secondary.disabled,
.btn-outline-secondary:disabled {
  color: #68d585;
  background-color: transparent;
}

.btn-outline-secondary:not(:disabled):not(.disabled):active,
.btn-outline-secondary:not(:disabled):not(.disabled).active,
.show > .btn-outline-secondary.dropdown-toggle {
  color: #212529;
  background-color: #68d585;
  border-color: #68d585;
}

.btn-outline-secondary:not(:disabled):not(.disabled):active:focus,
.btn-outline-secondary:not(:disabled):not(.disabled).active:focus,
.show > .btn-outline-secondary.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(104, 213, 133, 0.5);
}

.btn-outline-success {
  color: #68d585;
  border-color: #68d585;
}

.btn-outline-success:hover {
  color: #212529;
  background-color: #68d585;
  border-color: #68d585;
}

.btn-outline-success:focus,
.btn-outline-success.focus {
  box-shadow: 0 0 0 0.2rem rgba(104, 213, 133, 0.5);
}

.btn-outline-success.disabled,
.btn-outline-success:disabled {
  color: #68d585;
  background-color: transparent;
}

.btn-outline-success:not(:disabled):not(.disabled):active,
.btn-outline-success:not(:disabled):not(.disabled).active,
.show > .btn-outline-success.dropdown-toggle {
  color: #212529;
  background-color: #68d585;
  border-color: #68d585;
}

.btn-outline-success:not(:disabled):not(.disabled):active:focus,
.btn-outline-success:not(:disabled):not(.disabled).active:focus,
.show > .btn-outline-success.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(104, 213, 133, 0.5);
}

.btn-outline-info {
  color: #17a2b8;
  border-color: #17a2b8;
}

.btn-outline-info:hover {
  color: #fff;
  background-color: #17a2b8;
  border-color: #17a2b8;
}

.btn-outline-info:focus,
.btn-outline-info.focus {
  box-shadow: 0 0 0 0.2rem rgba(23, 162, 184, 0.5);
}

.btn-outline-info.disabled,
.btn-outline-info:disabled {
  color: #17a2b8;
  background-color: transparent;
}

.btn-outline-info:not(:disabled):not(.disabled):active,
.btn-outline-info:not(:disabled):not(.disabled).active,
.show > .btn-outline-info.dropdown-toggle {
  color: #fff;
  background-color: #17a2b8;
  border-color: #17a2b8;
}

.btn-outline-info:not(:disabled):not(.disabled):active:focus,
.btn-outline-info:not(:disabled):not(.disabled).active:focus,
.show > .btn-outline-info.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(23, 162, 184, 0.5);
}

.btn-outline-warning {
  color: #f7e36d;
  border-color: #f7e36d;
}

.btn-outline-warning:hover {
  color: #212529;
  background-color: #f7e36d;
  border-color: #f7e36d;
}

.btn-outline-warning:focus,
.btn-outline-warning.focus {
  box-shadow: 0 0 0 0.2rem rgba(247, 227, 109, 0.5);
}

.btn-outline-warning.disabled,
.btn-outline-warning:disabled {
  color: #f7e36d;
  background-color: transparent;
}

.btn-outline-warning:not(:disabled):not(.disabled):active,
.btn-outline-warning:not(:disabled):not(.disabled).active,
.show > .btn-outline-warning.dropdown-toggle {
  color: #212529;
  background-color: #f7e36d;
  border-color: #f7e36d;
}

.btn-outline-warning:not(:disabled):not(.disabled):active:focus,
.btn-outline-warning:not(:disabled):not(.disabled).active:focus,
.show > .btn-outline-warning.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(247, 227, 109, 0.5);
}

.btn-outline-danger {
  color: #f64b4b;
  border-color: #f64b4b;
}

.btn-outline-danger:hover {
  color: #fff;
  background-color: #f64b4b;
  border-color: #f64b4b;
}

.btn-outline-danger:focus,
.btn-outline-danger.focus {
  box-shadow: 0 0 0 0.2rem rgba(246, 75, 75, 0.5);
}

.btn-outline-danger.disabled,
.btn-outline-danger:disabled {
  color: #f64b4b;
  background-color: transparent;
}

.btn-outline-danger:not(:disabled):not(.disabled):active,
.btn-outline-danger:not(:disabled):not(.disabled).active,
.show > .btn-outline-danger.dropdown-toggle {
  color: #fff;
  background-color: #f64b4b;
  border-color: #f64b4b;
}

.btn-outline-danger:not(:disabled):not(.disabled):active:focus,
.btn-outline-danger:not(:disabled):not(.disabled).active:focus,
.show > .btn-outline-danger.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(246, 75, 75, 0.5);
}

.btn-outline-light {
  color: #f8f9fa;
  border-color: #f8f9fa;
}

.btn-outline-light:hover {
  color: #212529;
  background-color: #f8f9fa;
  border-color: #f8f9fa;
}

.btn-outline-light:focus,
.btn-outline-light.focus {
  box-shadow: 0 0 0 0.2rem rgba(248, 249, 250, 0.5);
}

.btn-outline-light.disabled,
.btn-outline-light:disabled {
  color: #f8f9fa;
  background-color: transparent;
}

.btn-outline-light:not(:disabled):not(.disabled):active,
.btn-outline-light:not(:disabled):not(.disabled).active,
.show > .btn-outline-light.dropdown-toggle {
  color: #212529;
  background-color: #f8f9fa;
  border-color: #f8f9fa;
}

.btn-outline-light:not(:disabled):not(.disabled):active:focus,
.btn-outline-light:not(:disabled):not(.disabled).active:focus,
.show > .btn-outline-light.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(248, 249, 250, 0.5);
}

.btn-outline-dark {
  color: #343a40;
  border-color: #343a40;
}

.btn-outline-dark:hover {
  color: #fff;
  background-color: #343a40;
  border-color: #343a40;
}

.btn-outline-dark:focus,
.btn-outline-dark.focus {
  box-shadow: 0 0 0 0.2rem rgba(52, 58, 64, 0.5);
}

.btn-outline-dark.disabled,
.btn-outline-dark:disabled {
  color: #343a40;
  background-color: transparent;
}

.btn-outline-dark:not(:disabled):not(.disabled):active,
.btn-outline-dark:not(:disabled):not(.disabled).active,
.show > .btn-outline-dark.dropdown-toggle {
  color: #fff;
  background-color: #343a40;
  border-color: #343a40;
}

.btn-outline-dark:not(:disabled):not(.disabled):active:focus,
.btn-outline-dark:not(:disabled):not(.disabled).active:focus,
.show > .btn-outline-dark.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(52, 58, 64, 0.5);
}

.btn-outline-red {
  color: #f64b4b;
  border-color: #f64b4b;
}

.btn-outline-red:hover {
  color: #fff;
  background-color: #f64b4b;
  border-color: #f64b4b;
}

.btn-outline-red:focus,
.btn-outline-red.focus {
  box-shadow: 0 0 0 0.2rem rgba(246, 75, 75, 0.5);
}

.btn-outline-red.disabled,
.btn-outline-red:disabled {
  color: #f64b4b;
  background-color: transparent;
}

.btn-outline-red:not(:disabled):not(.disabled):active,
.btn-outline-red:not(:disabled):not(.disabled).active,
.show > .btn-outline-red.dropdown-toggle {
  color: #fff;
  background-color: #f64b4b;
  border-color: #f64b4b;
}

.btn-outline-red:not(:disabled):not(.disabled):active:focus,
.btn-outline-red:not(:disabled):not(.disabled).active:focus,
.show > .btn-outline-red.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(246, 75, 75, 0.5);
}

.btn-outline-green {
  color: #68d585;
  border-color: #68d585;
}

.btn-outline-green:hover {
  color: #212529;
  background-color: #68d585;
  border-color: #68d585;
}

.btn-outline-green:focus,
.btn-outline-green.focus {
  box-shadow: 0 0 0 0.2rem rgba(104, 213, 133, 0.5);
}

.btn-outline-green.disabled,
.btn-outline-green:disabled {
  color: #68d585;
  background-color: transparent;
}

.btn-outline-green:not(:disabled):not(.disabled):active,
.btn-outline-green:not(:disabled):not(.disabled).active,
.show > .btn-outline-green.dropdown-toggle {
  color: #212529;
  background-color: #68d585;
  border-color: #68d585;
}

.btn-outline-green:not(:disabled):not(.disabled):active:focus,
.btn-outline-green:not(:disabled):not(.disabled).active:focus,
.show > .btn-outline-green.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(104, 213, 133, 0.5);
}

.btn-outline-green-shamrock {
  color: #2bd67b;
  border-color: #2bd67b;
}

.btn-outline-green-shamrock:hover {
  color: #212529;
  background-color: #2bd67b;
  border-color: #2bd67b;
}

.btn-outline-green-shamrock:focus,
.btn-outline-green-shamrock.focus {
  box-shadow: 0 0 0 0.2rem rgba(43, 214, 123, 0.5);
}

.btn-outline-green-shamrock.disabled,
.btn-outline-green-shamrock:disabled {
  color: #2bd67b;
  background-color: transparent;
}

.btn-outline-green-shamrock:not(:disabled):not(.disabled):active,
.btn-outline-green-shamrock:not(:disabled):not(.disabled).active,
.show > .btn-outline-green-shamrock.dropdown-toggle {
  color: #212529;
  background-color: #2bd67b;
  border-color: #2bd67b;
}

.btn-outline-green-shamrock:not(:disabled):not(.disabled):active:focus,
.btn-outline-green-shamrock:not(:disabled):not(.disabled).active:focus,
.show > .btn-outline-green-shamrock.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(43, 214, 123, 0.5);
}

.btn-outline-blue {
  color: #473bf0;
  border-color: #473bf0;
}

.btn-outline-blue:hover {
  color: #fff;
  background-color: #473bf0;
  border-color: #473bf0;
}

.btn-outline-blue:focus,
.btn-outline-blue.focus {
  box-shadow: 0 0 0 0.2rem rgba(71, 59, 240, 0.5);
}

.btn-outline-blue.disabled,
.btn-outline-blue:disabled {
  color: #473bf0;
  background-color: transparent;
}

.btn-outline-blue:not(:disabled):not(.disabled):active,
.btn-outline-blue:not(:disabled):not(.disabled).active,
.show > .btn-outline-blue.dropdown-toggle {
  color: #fff;
  background-color: #473bf0;
  border-color: #473bf0;
}

.btn-outline-blue:not(:disabled):not(.disabled):active:focus,
.btn-outline-blue:not(:disabled):not(.disabled).active:focus,
.show > .btn-outline-blue.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(71, 59, 240, 0.5);
}

.btn-outline-sky-blue {
  color: #1082e9;
  border-color: #1082e9;
}

.btn-outline-sky-blue:hover {
  color: #fff;
  background-color: #1082e9;
  border-color: #1082e9;
}

.btn-outline-sky-blue:focus,
.btn-outline-sky-blue.focus {
  box-shadow: 0 0 0 0.2rem rgba(16, 130, 233, 0.5);
}

.btn-outline-sky-blue.disabled,
.btn-outline-sky-blue:disabled {
  color: #1082e9;
  background-color: transparent;
}

.btn-outline-sky-blue:not(:disabled):not(.disabled):active,
.btn-outline-sky-blue:not(:disabled):not(.disabled).active,
.show > .btn-outline-sky-blue.dropdown-toggle {
  color: #fff;
  background-color: #1082e9;
  border-color: #1082e9;
}

.btn-outline-sky-blue:not(:disabled):not(.disabled):active:focus,
.btn-outline-sky-blue:not(:disabled):not(.disabled).active:focus,
.show > .btn-outline-sky-blue.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(16, 130, 233, 0.5);
}

.btn-outline-yellow {
  color: #f7e36d;
  border-color: #f7e36d;
}

.btn-outline-yellow:hover {
  color: #212529;
  background-color: #f7e36d;
  border-color: #f7e36d;
}

.btn-outline-yellow:focus,
.btn-outline-yellow.focus {
  box-shadow: 0 0 0 0.2rem rgba(247, 227, 109, 0.5);
}

.btn-outline-yellow.disabled,
.btn-outline-yellow:disabled {
  color: #f7e36d;
  background-color: transparent;
}

.btn-outline-yellow:not(:disabled):not(.disabled):active,
.btn-outline-yellow:not(:disabled):not(.disabled).active,
.show > .btn-outline-yellow.dropdown-toggle {
  color: #212529;
  background-color: #f7e36d;
  border-color: #f7e36d;
}

.btn-outline-yellow:not(:disabled):not(.disabled):active:focus,
.btn-outline-yellow:not(:disabled):not(.disabled).active:focus,
.show > .btn-outline-yellow.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(247, 227, 109, 0.5);
}

.btn-outline-yellow-orange {
  color: #fcad38;
  border-color: #fcad38;
}

.btn-outline-yellow-orange:hover {
  color: #212529;
  background-color: #fcad38;
  border-color: #fcad38;
}

.btn-outline-yellow-orange:focus,
.btn-outline-yellow-orange.focus {
  box-shadow: 0 0 0 0.2rem rgba(252, 173, 56, 0.5);
}

.btn-outline-yellow-orange.disabled,
.btn-outline-yellow-orange:disabled {
  color: #fcad38;
  background-color: transparent;
}

.btn-outline-yellow-orange:not(:disabled):not(.disabled):active,
.btn-outline-yellow-orange:not(:disabled):not(.disabled).active,
.show > .btn-outline-yellow-orange.dropdown-toggle {
  color: #212529;
  background-color: #fcad38;
  border-color: #fcad38;
}

.btn-outline-yellow-orange:not(:disabled):not(.disabled):active:focus,
.btn-outline-yellow-orange:not(:disabled):not(.disabled).active:focus,
.show > .btn-outline-yellow-orange.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(252, 173, 56, 0.5);
}

.btn-outline-blackish-blue {
  color: #13151c;
  border-color: #13151c;
}

.btn-outline-blackish-blue:hover {
  color: #fff;
  background-color: #13151c;
  border-color: #13151c;
}

.btn-outline-blackish-blue:focus,
.btn-outline-blackish-blue.focus {
  box-shadow: 0 0 0 0.2rem rgba(19, 21, 28, 0.5);
}

.btn-outline-blackish-blue.disabled,
.btn-outline-blackish-blue:disabled {
  color: #13151c;
  background-color: transparent;
}

.btn-outline-blackish-blue:not(:disabled):not(.disabled):active,
.btn-outline-blackish-blue:not(:disabled):not(.disabled).active,
.show > .btn-outline-blackish-blue.dropdown-toggle {
  color: #fff;
  background-color: #13151c;
  border-color: #13151c;
}

.btn-outline-blackish-blue:not(:disabled):not(.disabled):active:focus,
.btn-outline-blackish-blue:not(:disabled):not(.disabled).active:focus,
.show > .btn-outline-blackish-blue.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(19, 21, 28, 0.5);
}

.btn-outline-black {
  color: #000;
  border-color: #000;
}

.btn-outline-black:hover {
  color: #fff;
  background-color: #000;
  border-color: #000;
}

.btn-outline-black:focus,
.btn-outline-black.focus {
  box-shadow: 0 0 0 0.2rem rgba(0, 0, 0, 0.5);
}

.btn-outline-black.disabled,
.btn-outline-black:disabled {
  color: #000;
  background-color: transparent;
}

.btn-outline-black:not(:disabled):not(.disabled):active,
.btn-outline-black:not(:disabled):not(.disabled).active,
.show > .btn-outline-black.dropdown-toggle {
  color: #fff;
  background-color: #000;
  border-color: #000;
}

.btn-outline-black:not(:disabled):not(.disabled):active:focus,
.btn-outline-black:not(:disabled):not(.disabled).active:focus,
.show > .btn-outline-black.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(0, 0, 0, 0.5);
}

.btn-outline-mirage {
  color: #131829;
  border-color: #131829;
}

.btn-outline-mirage:hover {
  color: #fff;
  background-color: #131829;
  border-color: #131829;
}

.btn-outline-mirage:focus,
.btn-outline-mirage.focus {
  box-shadow: 0 0 0 0.2rem rgba(19, 24, 41, 0.5);
}

.btn-outline-mirage.disabled,
.btn-outline-mirage:disabled {
  color: #131829;
  background-color: transparent;
}

.btn-outline-mirage:not(:disabled):not(.disabled):active,
.btn-outline-mirage:not(:disabled):not(.disabled).active,
.show > .btn-outline-mirage.dropdown-toggle {
  color: #fff;
  background-color: #131829;
  border-color: #131829;
}

.btn-outline-mirage:not(:disabled):not(.disabled):active:focus,
.btn-outline-mirage:not(:disabled):not(.disabled).active:focus,
.show > .btn-outline-mirage.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(19, 24, 41, 0.5);
}

.btn-outline-mirage-2 {
  color: #161c2d;
  border-color: #161c2d;
}

.btn-outline-mirage-2:hover {
  color: #fff;
  background-color: #161c2d;
  border-color: #161c2d;
}

.btn-outline-mirage-2:focus,
.btn-outline-mirage-2.focus {
  box-shadow: 0 0 0 0.2rem rgba(22, 28, 45, 0.5);
}

.btn-outline-mirage-2.disabled,
.btn-outline-mirage-2:disabled {
  color: #161c2d;
  background-color: transparent;
}

.btn-outline-mirage-2:not(:disabled):not(.disabled):active,
.btn-outline-mirage-2:not(:disabled):not(.disabled).active,
.show > .btn-outline-mirage-2.dropdown-toggle {
  color: #fff;
  background-color: #161c2d;
  border-color: #161c2d;
}

.btn-outline-mirage-2:not(:disabled):not(.disabled):active:focus,
.btn-outline-mirage-2:not(:disabled):not(.disabled).active:focus,
.show > .btn-outline-mirage-2.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(22, 28, 45, 0.5);
}

.btn-outline-white {
  color: #fff;
  border-color: #fff;
}

.btn-outline-white:hover {
  color: #212529;
  background-color: #fff;
  border-color: #fff;
}

.btn-outline-white:focus,
.btn-outline-white.focus {
  box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.5);
}

.btn-outline-white.disabled,
.btn-outline-white:disabled {
  color: #fff;
  background-color: transparent;
}

.btn-outline-white:not(:disabled):not(.disabled):active,
.btn-outline-white:not(:disabled):not(.disabled).active,
.show > .btn-outline-white.dropdown-toggle {
  color: #212529;
  background-color: #fff;
  border-color: #fff;
}

.btn-outline-white:not(:disabled):not(.disabled):active:focus,
.btn-outline-white:not(:disabled):not(.disabled).active:focus,
.show > .btn-outline-white.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.5);
}

.btn-outline-smoke {
  color: #f8f8f8;
  border-color: #f8f8f8;
}

.btn-outline-smoke:hover {
  color: #212529;
  background-color: #f8f8f8;
  border-color: #f8f8f8;
}

.btn-outline-smoke:focus,
.btn-outline-smoke.focus {
  box-shadow: 0 0 0 0.2rem rgba(248, 248, 248, 0.5);
}

.btn-outline-smoke.disabled,
.btn-outline-smoke:disabled {
  color: #f8f8f8;
  background-color: transparent;
}

.btn-outline-smoke:not(:disabled):not(.disabled):active,
.btn-outline-smoke:not(:disabled):not(.disabled).active,
.show > .btn-outline-smoke.dropdown-toggle {
  color: #212529;
  background-color: #f8f8f8;
  border-color: #f8f8f8;
}

.btn-outline-smoke:not(:disabled):not(.disabled):active:focus,
.btn-outline-smoke:not(:disabled):not(.disabled).active:focus,
.show > .btn-outline-smoke.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(248, 248, 248, 0.5);
}

.btn-outline-storm {
  color: #7d818d;
  border-color: #7d818d;
}

.btn-outline-storm:hover {
  color: #fff;
  background-color: #7d818d;
  border-color: #7d818d;
}

.btn-outline-storm:focus,
.btn-outline-storm.focus {
  box-shadow: 0 0 0 0.2rem rgba(125, 129, 141, 0.5);
}

.btn-outline-storm.disabled,
.btn-outline-storm:disabled {
  color: #7d818d;
  background-color: transparent;
}

.btn-outline-storm:not(:disabled):not(.disabled):active,
.btn-outline-storm:not(:disabled):not(.disabled).active,
.show > .btn-outline-storm.dropdown-toggle {
  color: #fff;
  background-color: #7d818d;
  border-color: #7d818d;
}

.btn-outline-storm:not(:disabled):not(.disabled):active:focus,
.btn-outline-storm:not(:disabled):not(.disabled).active:focus,
.show > .btn-outline-storm.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(125, 129, 141, 0.5);
}

.btn-outline-ghost {
  color: #fdfdff;
  border-color: #fdfdff;
}

.btn-outline-ghost:hover {
  color: #212529;
  background-color: #fdfdff;
  border-color: #fdfdff;
}

.btn-outline-ghost:focus,
.btn-outline-ghost.focus {
  box-shadow: 0 0 0 0.2rem rgba(253, 253, 255, 0.5);
}

.btn-outline-ghost.disabled,
.btn-outline-ghost:disabled {
  color: #fdfdff;
  background-color: transparent;
}

.btn-outline-ghost:not(:disabled):not(.disabled):active,
.btn-outline-ghost:not(:disabled):not(.disabled).active,
.show > .btn-outline-ghost.dropdown-toggle {
  color: #212529;
  background-color: #fdfdff;
  border-color: #fdfdff;
}

.btn-outline-ghost:not(:disabled):not(.disabled):active:focus,
.btn-outline-ghost:not(:disabled):not(.disabled).active:focus,
.show > .btn-outline-ghost.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(253, 253, 255, 0.5);
}

.btn-outline-gray-1 {
  color: #fcfdfe;
  border-color: #fcfdfe;
}

.btn-outline-gray-1:hover {
  color: #212529;
  background-color: #fcfdfe;
  border-color: #fcfdfe;
}

.btn-outline-gray-1:focus,
.btn-outline-gray-1.focus {
  box-shadow: 0 0 0 0.2rem rgba(252, 253, 254, 0.5);
}

.btn-outline-gray-1.disabled,
.btn-outline-gray-1:disabled {
  color: #fcfdfe;
  background-color: transparent;
}

.btn-outline-gray-1:not(:disabled):not(.disabled):active,
.btn-outline-gray-1:not(:disabled):not(.disabled).active,
.show > .btn-outline-gray-1.dropdown-toggle {
  color: #212529;
  background-color: #fcfdfe;
  border-color: #fcfdfe;
}

.btn-outline-gray-1:not(:disabled):not(.disabled):active:focus,
.btn-outline-gray-1:not(:disabled):not(.disabled).active:focus,
.show > .btn-outline-gray-1.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(252, 253, 254, 0.5);
}

.btn-outline-gray-2 {
  color: #f4f7fa;
  border-color: #f4f7fa;
}

.btn-outline-gray-2:hover {
  color: #212529;
  background-color: #f4f7fa;
  border-color: #f4f7fa;
}

.btn-outline-gray-2:focus,
.btn-outline-gray-2.focus {
  box-shadow: 0 0 0 0.2rem rgba(244, 247, 250, 0.5);
}

.btn-outline-gray-2.disabled,
.btn-outline-gray-2:disabled {
  color: #f4f7fa;
  background-color: transparent;
}

.btn-outline-gray-2:not(:disabled):not(.disabled):active,
.btn-outline-gray-2:not(:disabled):not(.disabled).active,
.show > .btn-outline-gray-2.dropdown-toggle {
  color: #212529;
  background-color: #f4f7fa;
  border-color: #f4f7fa;
}

.btn-outline-gray-2:not(:disabled):not(.disabled):active:focus,
.btn-outline-gray-2:not(:disabled):not(.disabled).active:focus,
.show > .btn-outline-gray-2.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(244, 247, 250, 0.5);
}

.btn-outline-gray-3 {
  color: #e7e9ed;
  border-color: #e7e9ed;
}

.btn-outline-gray-3:hover {
  color: #212529;
  background-color: #e7e9ed;
  border-color: #e7e9ed;
}

.btn-outline-gray-3:focus,
.btn-outline-gray-3.focus {
  box-shadow: 0 0 0 0.2rem rgba(231, 233, 237, 0.5);
}

.btn-outline-gray-3.disabled,
.btn-outline-gray-3:disabled {
  color: #e7e9ed;
  background-color: transparent;
}

.btn-outline-gray-3:not(:disabled):not(.disabled):active,
.btn-outline-gray-3:not(:disabled):not(.disabled).active,
.show > .btn-outline-gray-3.dropdown-toggle {
  color: #212529;
  background-color: #e7e9ed;
  border-color: #e7e9ed;
}

.btn-outline-gray-3:not(:disabled):not(.disabled):active:focus,
.btn-outline-gray-3:not(:disabled):not(.disabled).active:focus,
.show > .btn-outline-gray-3.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(231, 233, 237, 0.5);
}

.btn-outline-gray-310 {
  color: #d5d7dd;
  border-color: #d5d7dd;
}

.btn-outline-gray-310:hover {
  color: #212529;
  background-color: #d5d7dd;
  border-color: #d5d7dd;
}

.btn-outline-gray-310:focus,
.btn-outline-gray-310.focus {
  box-shadow: 0 0 0 0.2rem rgba(213, 215, 221, 0.5);
}

.btn-outline-gray-310.disabled,
.btn-outline-gray-310:disabled {
  color: #d5d7dd;
  background-color: transparent;
}

.btn-outline-gray-310:not(:disabled):not(.disabled):active,
.btn-outline-gray-310:not(:disabled):not(.disabled).active,
.show > .btn-outline-gray-310.dropdown-toggle {
  color: #212529;
  background-color: #d5d7dd;
  border-color: #d5d7dd;
}

.btn-outline-gray-310:not(:disabled):not(.disabled):active:focus,
.btn-outline-gray-310:not(:disabled):not(.disabled).active:focus,
.show > .btn-outline-gray-310.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(213, 215, 221, 0.5);
}

.btn-outline-gray-opacity {
  color: rgba(231, 233, 237, 0.13);
  border-color: rgba(231, 233, 237, 0.13);
}

.btn-outline-gray-opacity:hover {
  color: #212529;
  background-color: rgba(231, 233, 237, 0.13);
  border-color: rgba(231, 233, 237, 0.13);
}

.btn-outline-gray-opacity:focus,
.btn-outline-gray-opacity.focus {
  box-shadow: 0 0 0 0.2rem rgba(231, 233, 237, 0.5);
}

.btn-outline-gray-opacity.disabled,
.btn-outline-gray-opacity:disabled {
  color: rgba(231, 233, 237, 0.13);
  background-color: transparent;
}

.btn-outline-gray-opacity:not(:disabled):not(.disabled):active,
.btn-outline-gray-opacity:not(:disabled):not(.disabled).active,
.show > .btn-outline-gray-opacity.dropdown-toggle {
  color: #212529;
  background-color: rgba(231, 233, 237, 0.13);
  border-color: rgba(231, 233, 237, 0.13);
}

.btn-outline-gray-opacity:not(:disabled):not(.disabled):active:focus,
.btn-outline-gray-opacity:not(:disabled):not(.disabled).active:focus,
.show > .btn-outline-gray-opacity.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(231, 233, 237, 0.5);
}

.btn-outline-blackish-blue-opacity {
  color: rgba(22, 28, 45, 0.7);
  border-color: rgba(22, 28, 45, 0.7);
}

.btn-outline-blackish-blue-opacity:hover {
  color: #fff;
  background-color: rgba(22, 28, 45, 0.7);
  border-color: rgba(22, 28, 45, 0.7);
}

.btn-outline-blackish-blue-opacity:focus,
.btn-outline-blackish-blue-opacity.focus {
  box-shadow: 0 0 0 0.2rem rgba(22, 28, 45, 0.5);
}

.btn-outline-blackish-blue-opacity.disabled,
.btn-outline-blackish-blue-opacity:disabled {
  color: rgba(22, 28, 45, 0.7);
  background-color: transparent;
}

.btn-outline-blackish-blue-opacity:not(:disabled):not(.disabled):active,
.btn-outline-blackish-blue-opacity:not(:disabled):not(.disabled).active,
.show > .btn-outline-blackish-blue-opacity.dropdown-toggle {
  color: #fff;
  background-color: rgba(22, 28, 45, 0.7);
  border-color: rgba(22, 28, 45, 0.7);
}

.btn-outline-blackish-blue-opacity:not(:disabled):not(.disabled):active:focus,
.btn-outline-blackish-blue-opacity:not(:disabled):not(.disabled).active:focus,
.show > .btn-outline-blackish-blue-opacity.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(22, 28, 45, 0.5);
}

.btn-outline-narvik {
  color: #edf9f2;
  border-color: #edf9f2;
}

.btn-outline-narvik:hover {
  color: #212529;
  background-color: #edf9f2;
  border-color: #edf9f2;
}

.btn-outline-narvik:focus,
.btn-outline-narvik.focus {
  box-shadow: 0 0 0 0.2rem rgba(237, 249, 242, 0.5);
}

.btn-outline-narvik.disabled,
.btn-outline-narvik:disabled {
  color: #edf9f2;
  background-color: transparent;
}

.btn-outline-narvik:not(:disabled):not(.disabled):active,
.btn-outline-narvik:not(:disabled):not(.disabled).active,
.show > .btn-outline-narvik.dropdown-toggle {
  color: #212529;
  background-color: #edf9f2;
  border-color: #edf9f2;
}

.btn-outline-narvik:not(:disabled):not(.disabled):active:focus,
.btn-outline-narvik:not(:disabled):not(.disabled).active:focus,
.show > .btn-outline-narvik.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(237, 249, 242, 0.5);
}

.btn-link {
  font-weight: 400;
  color: #473bf0;
  text-decoration: none;
}

.btn-link:hover {
  color: #1c10cf;
  text-decoration: none;
}

.btn-link:focus,
.btn-link.focus {
  text-decoration: none;
}

.btn-link:disabled,
.btn-link.disabled {
  color: #6c757d;
  pointer-events: none;
}

.btn-lg,
.btn-group-lg > .btn {
  padding: 0.5rem 1rem;
  font-size: 1.25rem;
  line-height: 1.5;
  border-radius: 0.3rem;
}

.btn-sm,
.btn-group-sm > .btn {
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
  line-height: 1.2;
  border-radius: 0.2rem;
}

.btn-block {
  display: block;
  width: 100%;
}

.btn-block + .btn-block {
  margin-top: 0.5rem;
}

input[type='submit'].btn-block,
input[type='reset'].btn-block,
input[type='button'].btn-block {
  width: 100%;
}

.fade {
  transition: opacity 0.15s linear;
}

@media (prefers-reduced-motion: reduce) {
  .fade {
    transition: none;
  }
}

.fade:not(.show) {
  opacity: 0;
}

.collapse:not(.show) {
  display: none;
}

.collapsing {
  position: relative;
  height: 0;
  overflow: hidden;
  transition: height 0.35s ease;
}

@media (prefers-reduced-motion: reduce) {
  .collapsing {
    transition: none;
  }
}

.dropup,
.dropright,
.dropdown,
.dropleft {
  position: relative;
}

.dropdown-toggle {
  white-space: nowrap;
}

.dropdown-toggle::after {
  display: inline-block;
  margin-left: 0.255em;
  vertical-align: 0.255em;
  content: '';
  border-top: 0.3em solid;
  border-right: 0.3em solid transparent;
  border-bottom: 0;
  border-left: 0.3em solid transparent;
}

.dropdown-toggle:empty::after {
  margin-left: 0;
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 1000;
  display: none;
  float: left;
  min-width: 10rem;
  padding: 0.5rem 0;
  margin: 0.125rem 0 0;
  font-size: 1rem;
  color: var(--color-texts-opacity);
  text-align: left;
  list-style: none;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid rgba(0, 0, 0, 0.15);
  border-radius: 0.25rem;
}

.dropdown-menu-left {
  right: auto;
  left: 0;
}

.dropdown-menu-right {
  right: 0;
  left: auto;
}

@media (min-width: 480px) {
  .dropdown-menu-xs-left {
    right: auto;
    left: 0;
  }
  .dropdown-menu-xs-right {
    right: 0;
    left: auto;
  }
}

@media (min-width: 576px) {
  .dropdown-menu-sm-left {
    right: auto;
    left: 0;
  }
  .dropdown-menu-sm-right {
    right: 0;
    left: auto;
  }
}

@media (min-width: 768px) {
  .dropdown-menu-md-left {
    right: auto;
    left: 0;
  }
  .dropdown-menu-md-right {
    right: 0;
    left: auto;
  }
}

@media (min-width: 992px) {
  .dropdown-menu-lg-left {
    right: auto;
    left: 0;
  }
  .dropdown-menu-lg-right {
    right: 0;
    left: auto;
  }
}

@media (min-width: 1200px) {
  .dropdown-menu-xl-left {
    right: auto;
    left: 0;
  }
  .dropdown-menu-xl-right {
    right: 0;
    left: auto;
  }
}

.dropup .dropdown-menu {
  top: auto;
  bottom: 100%;
  margin-top: 0;
  margin-bottom: 0.125rem;
}

.dropup .dropdown-toggle::after {
  display: inline-block;
  margin-left: 0.255em;
  vertical-align: 0.255em;
  content: '';
  border-top: 0;
  border-right: 0.3em solid transparent;
  border-bottom: 0.3em solid;
  border-left: 0.3em solid transparent;
}

.dropup .dropdown-toggle:empty::after {
  margin-left: 0;
}

.dropright .dropdown-menu {
  top: 0;
  right: auto;
  left: 100%;
  margin-top: 0;
  margin-left: 0.125rem;
}

.dropright .dropdown-toggle::after {
  display: inline-block;
  margin-left: 0.255em;
  vertical-align: 0.255em;
  content: '';
  border-top: 0.3em solid transparent;
  border-right: 0;
  border-bottom: 0.3em solid transparent;
  border-left: 0.3em solid;
}

.dropright .dropdown-toggle:empty::after {
  margin-left: 0;
}

.dropright .dropdown-toggle::after {
  vertical-align: 0;
}

.dropleft .dropdown-menu {
  top: 0;
  right: 100%;
  left: auto;
  margin-top: 0;
  margin-right: 0.125rem;
}

.dropleft .dropdown-toggle::after {
  display: inline-block;
  margin-left: 0.255em;
  vertical-align: 0.255em;
  content: '';
}

.dropleft .dropdown-toggle::after {
  display: none;
}

.dropleft .dropdown-toggle::before {
  display: inline-block;
  margin-right: 0.255em;
  vertical-align: 0.255em;
  content: '';
  border-top: 0.3em solid transparent;
  border-right: 0.3em solid;
  border-bottom: 0.3em solid transparent;
}

.dropleft .dropdown-toggle:empty::after {
  margin-left: 0;
}

.dropleft .dropdown-toggle::before {
  vertical-align: 0;
}

.dropdown-menu[x-placement^='top'],
.dropdown-menu[x-placement^='right'],
.dropdown-menu[x-placement^='bottom'],
.dropdown-menu[x-placement^='left'] {
  right: auto;
  bottom: auto;
}

.dropdown-divider {
  height: 0;
  margin: 0.5rem 0;
  overflow: hidden;
  border-top: 1px solid #e9ecef;
}

.dropdown-item {
  display: block;
  width: 100%;
  padding: 0.25rem 1.5rem;
  clear: both;
  font-weight: 400;
  color: #212529;
  text-align: inherit;
  white-space: nowrap;
  background-color: transparent;
  border: 0;
}

.dropdown-item:hover,
.dropdown-item:focus {
  color: #16181b;
  text-decoration: none;
  background-color: #f8f9fa;
}

.dropdown-item.active,
.dropdown-item:active {
  color: #fff;
  text-decoration: none;
  background-color: #473bf0;
}

.dropdown-item.disabled,
.dropdown-item:disabled {
  color: #6c757d;
  pointer-events: none;
  background-color: transparent;
}

.dropdown-menu.show {
  display: block;
}

.dropdown-header {
  display: block;
  padding: 0.5rem 1.5rem;
  margin-bottom: 0;
  font-size: 0.875rem;
  color: #6c757d;
  white-space: nowrap;
}

.dropdown-item-text {
  display: block;
  padding: 0.25rem 1.5rem;
  color: #212529;
}

.btn-group,
.btn-group-vertical {
  position: relative;
  display: inline-flex;
  vertical-align: middle;
}

.btn-group > .btn,
.btn-group-vertical > .btn {
  position: relative;
  flex: 1 1 auto;
}

.btn-group > .btn:hover,
.btn-group-vertical > .btn:hover {
  z-index: 1;
}

.btn-group > .btn:focus,
.btn-group > .btn:active,
.btn-group > .btn.active,
.btn-group-vertical > .btn:focus,
.btn-group-vertical > .btn:active,
.btn-group-vertical > .btn.active {
  z-index: 1;
}

.btn-toolbar {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
}

.btn-toolbar .input-group {
  width: auto;
}

.btn-group > .btn:not(:first-child),
.btn-group > .btn-group:not(:first-child) {
  margin-left: -1px;
}

.btn-group > .btn:not(:last-child):not(.dropdown-toggle),
.btn-group > .btn-group:not(:last-child) > .btn {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

.btn-group > .btn:not(:first-child),
.btn-group > .btn-group:not(:first-child) > .btn {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

.dropdown-toggle-split {
  padding-right: 1.17188rem;
  padding-left: 1.17188rem;
}

.dropdown-toggle-split::after,
.dropup .dropdown-toggle-split::after,
.dropright .dropdown-toggle-split::after {
  margin-left: 0;
}

.dropleft .dropdown-toggle-split::before {
  margin-right: 0;
}

.btn-sm + .dropdown-toggle-split,
.btn-group-sm > .btn + .dropdown-toggle-split {
  padding-right: 0.375rem;
  padding-left: 0.375rem;
}

.btn-lg + .dropdown-toggle-split,
.btn-group-lg > .btn + .dropdown-toggle-split {
  padding-right: 0.75rem;
  padding-left: 0.75rem;
}

.btn-group-vertical {
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
}

.btn-group-vertical > .btn,
.btn-group-vertical > .btn-group {
  width: 100%;
}

.btn-group-vertical > .btn:not(:first-child),
.btn-group-vertical > .btn-group:not(:first-child) {
  margin-top: -1px;
}

.btn-group-vertical > .btn:not(:last-child):not(.dropdown-toggle),
.btn-group-vertical > .btn-group:not(:last-child) > .btn {
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
}

.btn-group-vertical > .btn:not(:first-child),
.btn-group-vertical > .btn-group:not(:first-child) > .btn {
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}

.btn-group-toggle > .btn,
.btn-group-toggle > .btn-group > .btn {
  margin-bottom: 0;
}

.btn-group-toggle > .btn input[type='radio'],
.btn-group-toggle > .btn input[type='checkbox'],
.btn-group-toggle > .btn-group > .btn input[type='radio'],
.btn-group-toggle > .btn-group > .btn input[type='checkbox'] {
  position: absolute;
  clip: rect(0, 0, 0, 0);
  pointer-events: none;
}

.input-group {
  position: relative;
  display: flex;
  flex-wrap: wrap;
  align-items: stretch;
  width: 100%;
}

.input-group > .form-control,
.input-group > .form-control-plaintext,
.input-group > .custom-select,
.input-group > .custom-file {
  position: relative;
  flex: 1 1 auto;
  width: 1%;
  min-width: 0;
  margin-bottom: 0;
}

.input-group > .form-control + .form-control,
.input-group > .form-control + .custom-select,
.input-group > .form-control + .custom-file,
.input-group > .form-control-plaintext + .form-control,
.input-group > .form-control-plaintext + .custom-select,
.input-group > .form-control-plaintext + .custom-file,
.input-group > .custom-select + .form-control,
.input-group > .custom-select + .custom-select,
.input-group > .custom-select + .custom-file,
.input-group > .custom-file + .form-control,
.input-group > .custom-file + .custom-select,
.input-group > .custom-file + .custom-file {
  margin-left: -1px;
}

.input-group > .form-control:focus,
.input-group > .custom-select:focus,
.input-group > .custom-file .custom-file-input:focus ~ .custom-file-label {
  z-index: 3;
}

.input-group > .custom-file .custom-file-input:focus {
  z-index: 4;
}

.input-group > .form-control:not(:last-child),
.input-group > .custom-select:not(:last-child) {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

.input-group > .form-control:not(:first-child),
.input-group > .custom-select:not(:first-child) {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

.input-group > .custom-file {
  display: flex;
  align-items: center;
}

.input-group > .custom-file:not(:last-child) .custom-file-label,
.input-group > .custom-file:not(:last-child) .custom-file-label::after {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

.input-group > .custom-file:not(:first-child) .custom-file-label {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

.input-group-prepend,
.input-group-append {
  display: flex;
}

.input-group-prepend .btn,
.input-group-append .btn {
  position: relative;
  z-index: 2;
}

.input-group-prepend .btn:focus,
.input-group-append .btn:focus {
  z-index: 3;
}

.input-group-prepend .btn + .btn,
.input-group-prepend .btn + .input-group-text,
.input-group-prepend .input-group-text + .input-group-text,
.input-group-prepend .input-group-text + .btn,
.input-group-append .btn + .btn,
.input-group-append .btn + .input-group-text,
.input-group-append .input-group-text + .input-group-text,
.input-group-append .input-group-text + .btn {
  margin-left: -1px;
}

.input-group-prepend {
  margin-right: -1px;
}

.input-group-append {
  margin-left: -1px;
}

.input-group-text {
  display: flex;
  align-items: center;
  padding: 0.75rem 1.5625rem;
  margin-bottom: 0;
  font-size: 1.1rem;
  font-weight: 400;
  line-height: 1.88;
  color: #495057;
  text-align: center;
  white-space: nowrap;
  background-color: #e9ecef;
  border: 1px solid #ced4da;
  border-radius: 8px;
}

.input-group-text input[type='radio'],
.input-group-text input[type='checkbox'] {
  margin-top: 0;
}

.input-group-lg > .form-control:not(textarea),
.input-group-lg > .custom-select {
  height: calc(1.5em + 1rem + 2px);
}

.input-group-lg > .form-control,
.input-group-lg > .custom-select,
.input-group-lg > .input-group-prepend > .input-group-text,
.input-group-lg > .input-group-append > .input-group-text,
.input-group-lg > .input-group-prepend > .btn,
.input-group-lg > .input-group-append > .btn {
  padding: 0.5rem 1rem;
  font-size: 1.25rem;
  line-height: 1.5;
  border-radius: 0.3rem;
}

.input-group-sm > .form-control:not(textarea),
.input-group-sm > .custom-select {
  height: calc(1.2em + 0.5rem + 2px);
}

.input-group-sm > .form-control,
.input-group-sm > .custom-select,
.input-group-sm > .input-group-prepend > .input-group-text,
.input-group-sm > .input-group-append > .input-group-text,
.input-group-sm > .input-group-prepend > .btn,
.input-group-sm > .input-group-append > .btn {
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
  line-height: 1.2;
  border-radius: 0.2rem;
}

.input-group-lg > .custom-select,
.input-group-sm > .custom-select {
  padding-right: 2.5625rem;
}

.input-group > .input-group-prepend > .btn,
.input-group > .input-group-prepend > .input-group-text,
.input-group > .input-group-append:not(:last-child) > .btn,
.input-group > .input-group-append:not(:last-child) > .input-group-text,
.input-group
  > .input-group-append:last-child
  > .btn:not(:last-child):not(.dropdown-toggle),
.input-group
  > .input-group-append:last-child
  > .input-group-text:not(:last-child) {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

.input-group > .input-group-append > .btn,
.input-group > .input-group-append > .input-group-text,
.input-group > .input-group-prepend:not(:first-child) > .btn,
.input-group > .input-group-prepend:not(:first-child) > .input-group-text,
.input-group > .input-group-prepend:first-child > .btn:not(:first-child),
.input-group
  > .input-group-prepend:first-child
  > .input-group-text:not(:first-child) {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

.custom-control {
  position: relative;
  z-index: 1;
  display: block;
  min-height: 1.5rem;
  padding-left: 1.5rem;
  -webkit-print-color-adjust: exact;
  color-adjust: exact;
}

.custom-control-inline {
  display: inline-flex;
  margin-right: 1rem;
}

.custom-control-input {
  position: absolute;
  left: 0;
  z-index: -1;
  width: 1rem;
  height: 1.25rem;
  opacity: 0;
}

.custom-control-input:checked ~ .custom-control-label::before {
  color: #fff;
  border-color: #473bf0;
  background-color: #473bf0;
}

.custom-control-input:focus ~ .custom-control-label::before {
  box-shadow: 0 0 0 0.2rem rgba(71, 59, 240, 0.25);
}

.custom-control-input:focus:not(:checked) ~ .custom-control-label::before {
  border-color: #b6b1f9;
}

.custom-control-input:not(:disabled):active ~ .custom-control-label::before {
  color: #fff;
  background-color: #e3e1fd;
  border-color: #e3e1fd;
}

.custom-control-input[disabled] ~ .custom-control-label,
.custom-control-input:disabled ~ .custom-control-label {
  color: #6c757d;
}

.custom-control-input[disabled] ~ .custom-control-label::before,
.custom-control-input:disabled ~ .custom-control-label::before {
  background-color: #e9ecef;
}

.custom-control-label {
  position: relative;
  margin-bottom: 0;
  vertical-align: top;
}

.custom-control-label::before {
  position: absolute;
  top: 0.25rem;
  left: -1.5rem;
  display: block;
  width: 1rem;
  height: 1rem;
  pointer-events: none;
  content: '';
  background-color: #fff;
  border: #adb5bd solid 1px;
}

.custom-control-label::after {
  position: absolute;
  top: 0.25rem;
  left: -1.5rem;
  display: block;
  width: 1rem;
  height: 1rem;
  content: '';
  background: no-repeat 50% / 50% 50%;
}

.custom-checkbox .custom-control-label::before {
  border-radius: 0.25rem;
}

.custom-checkbox .custom-control-input:checked ~ .custom-control-label::after {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='8' height='8' viewBox='0 0 8 8'%3e%3cpath fill='%23fff' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26l2.974 2.99L8 2.193z'/%3e%3c/svg%3e");
}

.custom-checkbox
  .custom-control-input:indeterminate
  ~ .custom-control-label::before {
  border-color: #473bf0;
  background-color: #473bf0;
}

.custom-checkbox
  .custom-control-input:indeterminate
  ~ .custom-control-label::after {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='4' height='4' viewBox='0 0 4 4'%3e%3cpath stroke='%23fff' d='M0 2h4'/%3e%3c/svg%3e");
}

.custom-checkbox
  .custom-control-input:disabled:checked
  ~ .custom-control-label::before {
  background-color: rgba(71, 59, 240, 0.5);
}

.custom-checkbox
  .custom-control-input:disabled:indeterminate
  ~ .custom-control-label::before {
  background-color: rgba(71, 59, 240, 0.5);
}

.custom-radio .custom-control-label::before {
  border-radius: 50%;
}

.custom-radio .custom-control-input:checked ~ .custom-control-label::after {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%23fff'/%3e%3c/svg%3e");
}

.custom-radio
  .custom-control-input:disabled:checked
  ~ .custom-control-label::before {
  background-color: rgba(71, 59, 240, 0.5);
}

.custom-switch {
  padding-left: 2.25rem;
}

.custom-switch .custom-control-label::before {
  left: -2.25rem;
  width: 1.75rem;
  pointer-events: all;
  border-radius: 0.5rem;
}

.custom-switch .custom-control-label::after {
  top: calc(0.25rem + 2px);
  left: calc(-2.25rem + 2px);
  width: calc(1rem - 4px);
  height: calc(1rem - 4px);
  background-color: #adb5bd;
  border-radius: 0.5rem;
  transition: transform 0.15s ease-in-out, background-color 0.15s ease-in-out,
    border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

@media (prefers-reduced-motion: reduce) {
  .custom-switch .custom-control-label::after {
    transition: none;
  }
}

.custom-switch .custom-control-input:checked ~ .custom-control-label::after {
  background-color: #fff;
  transform: translateX(0.75rem);
}

.custom-switch
  .custom-control-input:disabled:checked
  ~ .custom-control-label::before {
  background-color: rgba(71, 59, 240, 0.5);
}

.custom-select {
  display: inline-block;
  width: 100%;
  height: calc(1.88em + 1.5rem + 2px);
  padding: 0.75rem 2.5625rem 0.75rem 1.5625rem;
  font-size: 1.1rem;
  font-weight: 400;
  line-height: 1.88;
  color: #495057;
  vertical-align: middle;
  background: #fff
    url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='4' height='5' viewBox='0 0 4 5'%3e%3cpath fill='%23343a40' d='M2 0L0 2h4zm0 5L0 3h4z'/%3e%3c/svg%3e")
    no-repeat right 1.5625rem center/8px 10px;
  border: 1px solid #ced4da;
  border-radius: 0.25rem;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

.custom-select:focus {
  border-color: #b6b1f9;
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(71, 59, 240, 0.25);
}

.custom-select:focus::-ms-value {
  color: #495057;
  background-color: #fff;
}

.custom-select[multiple],
.custom-select[size]:not([size='1']) {
  height: auto;
  padding-right: 1.5625rem;
  background-image: none;
}

.custom-select:disabled {
  color: #6c757d;
  background-color: #e9ecef;
}

.custom-select::-ms-expand {
  display: none;
}

.custom-select:-moz-focusring {
  color: transparent;
  text-shadow: 0 0 0 #495057;
}

.custom-select-sm {
  height: calc(1.2em + 0.5rem + 2px);
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
  padding-left: 0.5rem;
  font-size: 0.875rem;
}

.custom-select-lg {
  height: calc(1.5em + 1rem + 2px);
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  padding-left: 1rem;
  font-size: 1.25rem;
}

.custom-file {
  position: relative;
  display: inline-block;
  width: 100%;
  height: calc(1.88em + 1.5rem + 2px);
  margin-bottom: 0;
}

.custom-file-input {
  position: relative;
  z-index: 2;
  width: 100%;
  height: calc(1.88em + 1.5rem + 2px);
  margin: 0;
  opacity: 0;
}

.custom-file-input:focus ~ .custom-file-label {
  border-color: #b6b1f9;
  box-shadow: 0 0 0 0.2rem rgba(71, 59, 240, 0.25);
}

.custom-file-input[disabled] ~ .custom-file-label,
.custom-file-input:disabled ~ .custom-file-label {
  background-color: #e9ecef;
}

.custom-file-input:lang(en) ~ .custom-file-label::after {
  content: 'Browse';
}

.custom-file-input ~ .custom-file-label[data-browse]::after {
  content: attr(data-browse);
}

.custom-file-label {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  z-index: 1;
  height: calc(1.88em + 1.5rem + 2px);
  padding: 0.75rem 1.5625rem;
  font-weight: 400;
  line-height: 1.88;
  color: #495057;
  background-color: #fff;
  border: 1px solid #ced4da;
  border-radius: 8px;
}

.custom-file-label::after {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  z-index: 3;
  display: block;
  height: calc(1.88em + 1.5rem);
  padding: 0.75rem 1.5625rem;
  line-height: 1.88;
  color: #495057;
  content: 'Browse';
  background-color: #e9ecef;
  border-left: inherit;
  border-radius: 0 8px 8px 0;
}

.custom-range {
  width: 100%;
  height: 1.4rem;
  padding: 0;
  background-color: transparent;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

.custom-range:focus {
  outline: none;
}

.custom-range:focus::-webkit-slider-thumb {
  box-shadow: 0 0 0 1px var(--bg), 0 0 0 0.2rem rgba(71, 59, 240, 0.25);
}

.custom-range:focus::-moz-range-thumb {
  box-shadow: 0 0 0 1px var(--bg), 0 0 0 0.2rem rgba(71, 59, 240, 0.25);
}

.custom-range:focus::-ms-thumb {
  box-shadow: 0 0 0 1px var(--bg), 0 0 0 0.2rem rgba(71, 59, 240, 0.25);
}

.custom-range::-moz-focus-outer {
  border: 0;
}

.custom-range::-webkit-slider-thumb {
  width: 1rem;
  height: 1rem;
  margin-top: -0.25rem;
  background-color: #473bf0;
  border: 0;
  border-radius: 1rem;
  -webkit-transition: background-color 0.15s ease-in-out,
    border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out,
    box-shadow 0.15s ease-in-out;
  -webkit-appearance: none;
  appearance: none;
}

@media (prefers-reduced-motion: reduce) {
  .custom-range::-webkit-slider-thumb {
    -webkit-transition: none;
    transition: none;
  }
}

.custom-range::-webkit-slider-thumb:active {
  background-color: #e3e1fd;
}

.custom-range::-webkit-slider-runnable-track {
  width: 100%;
  height: 0.5rem;
  color: transparent;
  cursor: pointer;
  background-color: #dee2e6;
  border-color: transparent;
  border-radius: 1rem;
}

.custom-range::-moz-range-thumb {
  width: 1rem;
  height: 1rem;
  background-color: #473bf0;
  border: 0;
  border-radius: 1rem;
  -moz-transition: background-color 0.15s ease-in-out,
    border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out,
    box-shadow 0.15s ease-in-out;
  -moz-appearance: none;
  appearance: none;
}

@media (prefers-reduced-motion: reduce) {
  .custom-range::-moz-range-thumb {
    -moz-transition: none;
    transition: none;
  }
}

.custom-range::-moz-range-thumb:active {
  background-color: #e3e1fd;
}

.custom-range::-moz-range-track {
  width: 100%;
  height: 0.5rem;
  color: transparent;
  cursor: pointer;
  background-color: #dee2e6;
  border-color: transparent;
  border-radius: 1rem;
}

.custom-range::-ms-thumb {
  width: 1rem;
  height: 1rem;
  margin-top: 0;
  margin-right: 0.2rem;
  margin-left: 0.2rem;
  background-color: #473bf0;
  border: 0;
  border-radius: 1rem;
  -ms-transition: background-color 0.15s ease-in-out,
    border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out,
    box-shadow 0.15s ease-in-out;
  appearance: none;
}

@media (prefers-reduced-motion: reduce) {
  .custom-range::-ms-thumb {
    -ms-transition: none;
    transition: none;
  }
}

.custom-range::-ms-thumb:active {
  background-color: #e3e1fd;
}

.custom-range::-ms-track {
  width: 100%;
  height: 0.5rem;
  color: transparent;
  cursor: pointer;
  background-color: transparent;
  border-color: transparent;
  border-width: 0.5rem;
}

.custom-range::-ms-fill-lower {
  background-color: #dee2e6;
  border-radius: 1rem;
}

.custom-range::-ms-fill-upper {
  margin-right: 15px;
  background-color: #dee2e6;
  border-radius: 1rem;
}

.custom-range:disabled::-webkit-slider-thumb {
  background-color: #adb5bd;
}

.custom-range:disabled::-webkit-slider-runnable-track {
  cursor: default;
}

.custom-range:disabled::-moz-range-thumb {
  background-color: #adb5bd;
}

.custom-range:disabled::-moz-range-track {
  cursor: default;
}

.custom-range:disabled::-ms-thumb {
  background-color: #adb5bd;
}

.custom-control-label::before,
.custom-file-label,
.custom-select {
  transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out,
    box-shadow 0.15s ease-in-out;
}

@media (prefers-reduced-motion: reduce) {
  .custom-control-label::before,
  .custom-file-label,
  .custom-select {
    transition: none;
  }
}

.nav {
  display: flex;
  flex-wrap: wrap;
  padding-left: 0;
  margin-bottom: 0;
  list-style: none;
}

.nav-link {
  display: block;
  padding: 0.5rem 1rem;
}

.nav-link:hover,
.nav-link:focus {
  text-decoration: none;
}

.nav-link.disabled {
  color: #feffff;
  pointer-events: none;
  cursor: default;
}

.nav-tabs {
  border-bottom: 1px solid #dee2e6;
}

.nav-tabs .nav-item {
  margin-bottom: -1px;
}

.nav-tabs .nav-link {
  border: 1px solid transparent;
  border-top-left-radius: 0.25rem;
  border-top-right-radius: 0.25rem;
}

.nav-tabs .nav-link:hover,
.nav-tabs .nav-link:focus {
  border-color: #e9ecef #e9ecef #dee2e6;
}

.nav-tabs .nav-link.disabled {
  color: #6c757d;
  background-color: transparent;
  border-color: transparent;
}

.nav-tabs .nav-link.active,
.nav-tabs .nav-item.show .nav-link {
  color: #495057;
  background-color: var(--bg);
  border-color: #dee2e6 #dee2e6 var(--bg);
}

.nav-tabs .dropdown-menu {
  margin-top: -1px;
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}

.nav-pills .nav-link {
  border-radius: 0.25rem;
}

.nav-pills .nav-link.active,
.nav-pills .show > .nav-link {
  color: #fff;
  background-color: #473bf0;
}

.nav-fill > .nav-link,
.nav-fill .nav-item {
  flex: 1 1 auto;
  text-align: center;
}

.nav-justified > .nav-link,
.nav-justified .nav-item {
  flex-basis: 0;
  flex-grow: 1;
  text-align: center;
}



.tab-content > .tab-pane {
  display: none;
}

.tab-content > .active {
  display: block;
}

.navbar {
  position: relative;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: space-between;
  padding: 0.5rem 1rem;
}

.navbar .container,
.navbar .container-fluid,
.navbar .container-sm,
.navbar .container-md,
.navbar .container-lg,
.navbar .container-xl {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: space-between;
}

.navbar-brand {
  display: inline-block;
  padding-top: 0.3125rem;
  padding-bottom: 0.3125rem;
  margin-right: 1rem;
  font-size: 1.25rem;
  line-height: inherit;
  white-space: nowrap;
}

.navbar-brand:hover,
.navbar-brand:focus {
  text-decoration: none;
}

.navbar-nav {
  display: flex;
  flex-direction: column;
  padding-left: 0;
  margin-bottom: 0;
  list-style: none;
}

.navbar-nav .nav-link {
  padding-right: 0;
  padding-left: 0;
}

.navbar-nav .dropdown-menu {
  position: static;
  float: none;
}

.navbar-text {
  display: inline-block;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}

.navbar-collapse {
  flex-basis: 100%;
  flex-grow: 1;
  align-items: center;
}

.navbar-toggler {
  padding: 0.25rem 0.75rem;
  font-size: 1.25rem;
  line-height: 1;
  background-color: transparent;
  border: 1px solid transparent;
  border-radius: 8px;
}

.navbar-toggler:hover,
.navbar-toggler:focus {
  text-decoration: none;
}

.navbar-toggler-icon {
  display: inline-block;
  width: 1.5em;
  height: 1.5em;
  vertical-align: middle;
  content: '';
  background: no-repeat center center;
  background-size: 100% 100%;
}

@media (max-width: 479.98px) {
  .navbar-expand-xs > .container,
  .navbar-expand-xs > .container-fluid,
  .navbar-expand-xs > .container-sm,
  .navbar-expand-xs > .container-md,
  .navbar-expand-xs > .container-lg,
  .navbar-expand-xs > .container-xl {
    padding-right: 0;
    padding-left: 0;
  }
}

@media (min-width: 480px) {
  .navbar-expand-xs {
    flex-flow: row nowrap;
    justify-content: flex-start;
  }
  .navbar-expand-xs .navbar-nav {
    flex-direction: row;
  }
  .navbar-expand-xs .navbar-nav .dropdown-menu {
    position: absolute;
  }
  .navbar-expand-xs .navbar-nav .nav-link {
    padding-right: 0.5rem;
    padding-left: 0.5rem;
  }
  .navbar-expand-xs > .container,
  .navbar-expand-xs > .container-fluid,
  .navbar-expand-xs > .container-sm,
  .navbar-expand-xs > .container-md,
  .navbar-expand-xs > .container-lg,
  .navbar-expand-xs > .container-xl {
    flex-wrap: nowrap;
  }
  .navbar-expand-xs .navbar-collapse {
    display: flex !important;
    flex-basis: auto;
  }
  .navbar-expand-xs .navbar-toggler {
    display: none;
  }
}

@media (max-width: 575.98px) {
  .navbar-expand-sm > .container,
  .navbar-expand-sm > .container-fluid,
  .navbar-expand-sm > .container-sm,
  .navbar-expand-sm > .container-md,
  .navbar-expand-sm > .container-lg,
  .navbar-expand-sm > .container-xl {
    padding-right: 0;
    padding-left: 0;
  }
}

@media (min-width: 576px) {
  .navbar-expand-sm {
    flex-flow: row nowrap;
    justify-content: flex-start;
  }
  .navbar-expand-sm .navbar-nav {
    flex-direction: row;
  }
  .navbar-expand-sm .navbar-nav .dropdown-menu {
    position: absolute;
  }
  .navbar-expand-sm .navbar-nav .nav-link {
    padding-right: 0.5rem;
    padding-left: 0.5rem;
  }
  .navbar-expand-sm > .container,
  .navbar-expand-sm > .container-fluid,
  .navbar-expand-sm > .container-sm,
  .navbar-expand-sm > .container-md,
  .navbar-expand-sm > .container-lg,
  .navbar-expand-sm > .container-xl {
    flex-wrap: nowrap;
  }
  .navbar-expand-sm .navbar-collapse {
    display: flex !important;
    flex-basis: auto;
  }
  .navbar-expand-sm .navbar-toggler {
    display: none;
  }
}

@media (max-width: 767.98px) {
  .navbar-expand-md > .container,
  .navbar-expand-md > .container-fluid,
  .navbar-expand-md > .container-sm,
  .navbar-expand-md > .container-md,
  .navbar-expand-md > .container-lg,
  .navbar-expand-md > .container-xl {
    padding-right: 0;
    padding-left: 0;
  }
}

@media (min-width: 768px) {
  .navbar-expand-md {
    flex-flow: row nowrap;
    justify-content: flex-start;
  }
  .navbar-expand-md .navbar-nav {
    flex-direction: row;
  }
  .navbar-expand-md .navbar-nav .dropdown-menu {
    position: absolute;
  }
  .navbar-expand-md .navbar-nav .nav-link {
    padding-right: 0.5rem;
    padding-left: 0.5rem;
  }
  .navbar-expand-md > .container,
  .navbar-expand-md > .container-fluid,
  .navbar-expand-md > .container-sm,
  .navbar-expand-md > .container-md,
  .navbar-expand-md > .container-lg,
  .navbar-expand-md > .container-xl {
    flex-wrap: nowrap;
  }
  .navbar-expand-md .navbar-collapse {
    display: flex !important;
    flex-basis: auto;
  }
  .navbar-expand-md .navbar-toggler {
    display: none;
  }
}

@media (max-width: 991.98px) {
  .navbar-expand-lg > .container,
  .navbar-expand-lg > .container-fluid,
  .navbar-expand-lg > .container-sm,
  .navbar-expand-lg > .container-md,
  .navbar-expand-lg > .container-lg,
  .navbar-expand-lg > .container-xl {
    padding-right: 0;
    padding-left: 0;
  }
}

@media (min-width: 992px) {
  .navbar-expand-lg {
    flex-flow: row nowrap;
    justify-content: flex-start;
  }
  .navbar-expand-lg .navbar-nav {
    flex-direction: row;
  }
  .navbar-expand-lg .navbar-nav .dropdown-menu {
    position: absolute;
  }
  .navbar-expand-lg .navbar-nav .nav-link {
    padding-right: 0.5rem;
    padding-left: 0.5rem;
  }
  .navbar-expand-lg > .container,
  .navbar-expand-lg > .container-fluid,
  .navbar-expand-lg > .container-sm,
  .navbar-expand-lg > .container-md,
  .navbar-expand-lg > .container-lg,
  .navbar-expand-lg > .container-xl {
    flex-wrap: nowrap;
  }
  .navbar-expand-lg .navbar-collapse {
    display: flex !important;
    flex-basis: auto;
  }
  .navbar-expand-lg .navbar-toggler {
    display: none;
  }
}

@media (max-width: 1199.98px) {
  .navbar-expand-xl > .container,
  .navbar-expand-xl > .container-fluid,
  .navbar-expand-xl > .container-sm,
  .navbar-expand-xl > .container-md,
  .navbar-expand-xl > .container-lg,
  .navbar-expand-xl > .container-xl {
    padding-right: 0;
    padding-left: 0;
  }
}

@media (min-width: 1200px) {
  .navbar-expand-xl {
    flex-flow: row nowrap;
    justify-content: flex-start;
  }
  .navbar-expand-xl .navbar-nav {
    flex-direction: row;
  }
  .navbar-expand-xl .navbar-nav .dropdown-menu {
    position: absolute;
  }
  .navbar-expand-xl .navbar-nav .nav-link {
    padding-right: 0.5rem;
    padding-left: 0.5rem;
  }
  .navbar-expand-xl > .container,
  .navbar-expand-xl > .container-fluid,
  .navbar-expand-xl > .container-sm,
  .navbar-expand-xl > .container-md,
  .navbar-expand-xl > .container-lg,
  .navbar-expand-xl > .container-xl {
    flex-wrap: nowrap;
  }
  .navbar-expand-xl .navbar-collapse {
    display: flex !important;
    flex-basis: auto;
  }
  .navbar-expand-xl .navbar-toggler {
    display: none;
  }
}

.navbar-expand {
  flex-flow: row nowrap;
  justify-content: flex-start;
}

.navbar-expand > .container,
.navbar-expand > .container-fluid,
.navbar-expand > .container-sm,
.navbar-expand > .container-md,
.navbar-expand > .container-lg,
.navbar-expand > .container-xl {
  padding-right: 0;
  padding-left: 0;
}

.navbar-expand .navbar-nav {
  flex-direction: row;
}

.navbar-expand .navbar-nav .dropdown-menu {
  position: absolute;
}

.navbar-expand .navbar-nav .nav-link {
  padding-right: 0.5rem;
  padding-left: 0.5rem;
}

.navbar-expand > .container,
.navbar-expand > .container-fluid,
.navbar-expand > .container-sm,
.navbar-expand > .container-md,
.navbar-expand > .container-lg,
.navbar-expand > .container-xl {
  flex-wrap: nowrap;
}

.navbar-expand .navbar-collapse {
  display: flex !important;
  flex-basis: auto;
}

.navbar-expand .navbar-toggler {
  display: none;
}

.navbar-light .navbar-brand {
  color: rgba(0, 0, 0, 0.9);
}

.navbar-light .navbar-brand:hover,
.navbar-light .navbar-brand:focus {
  color: rgba(0, 0, 0, 0.9);
}

.navbar-light .navbar-nav .nav-link {
  color: rgba(0, 0, 0, 0.5);
}

.navbar-light .navbar-nav .nav-link:hover,
.navbar-light .navbar-nav .nav-link:focus {
  color: rgba(0, 0, 0, 0.7);
}

.navbar-light .navbar-nav .nav-link.disabled {
  color: rgba(0, 0, 0, 0.3);
}

.navbar-light .navbar-nav .show > .nav-link,
.navbar-light .navbar-nav .active > .nav-link,
.navbar-light .navbar-nav .nav-link.show,
.navbar-light .navbar-nav .nav-link.active {
  color: rgba(0, 0, 0, 0.9);
}

.navbar-light .navbar-toggler {
  color: rgba(0, 0, 0, 0.5);
  border-color: rgba(0, 0, 0, 0.1);
}

.navbar-light .navbar-toggler-icon {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='30' height='30' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%280, 0, 0, 0.5%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
}

.navbar-light .navbar-text {
  color: rgba(0, 0, 0, 0.5);
}

.navbar-light .navbar-text a {
  color: rgba(0, 0, 0, 0.9);
}

.navbar-light .navbar-text a:hover,
.navbar-light .navbar-text a:focus {
  color: rgba(0, 0, 0, 0.9);
}

.navbar-dark .navbar-brand {
  color: #fff;
}

.navbar-dark .navbar-brand:hover,
.navbar-dark .navbar-brand:focus {
  color: #fff;
}

.navbar-dark .navbar-nav .nav-link {
  color: rgba(255, 255, 255, 0.5);
}

.navbar-dark .navbar-nav .nav-link:hover,
.navbar-dark .navbar-nav .nav-link:focus {
  color: rgba(255, 255, 255, 0.75);
}

.navbar-dark .navbar-nav .nav-link.disabled {
  color: rgba(255, 255, 255, 0.25);
}

.navbar-dark .navbar-nav .show > .nav-link,
.navbar-dark .navbar-nav .active > .nav-link,
.navbar-dark .navbar-nav .nav-link.show,
.navbar-dark .navbar-nav .nav-link.active {
  color: #fff;
}

.navbar-dark .navbar-toggler {
  color: rgba(255, 255, 255, 0.5);
  border-color: rgba(255, 255, 255, 0.1);
}

.navbar-dark .navbar-toggler-icon {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='30' height='30' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 255, 255, 0.5%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
}

.navbar-dark .navbar-text {
  color: rgba(255, 255, 255, 0.5);
}

.navbar-dark .navbar-text a {
  color: #fff;
}

.navbar-dark .navbar-text a:hover,
.navbar-dark .navbar-text a:focus {
  color: #fff;
}

.card {
  position: relative;
  display: flex;
  flex-direction: column;
  min-width: 0;
  word-wrap: break-word;
  background-color: #fff;
  background-clip: border-box;
  border: 1px solid rgba(0, 0, 0, 0.125);
  border-radius: 0.25rem;
}

.card > hr {
  margin-right: 0;
  margin-left: 0;
}

.card > .list-group {
  border-top: inherit;
  border-bottom: inherit;
}

.card > .list-group:first-child {
  border-top-width: 0;
  border-top-left-radius: calc(0.25rem - 1px);
  border-top-right-radius: calc(0.25rem - 1px);
}

.card > .list-group:last-child {
  border-bottom-width: 0;
  border-bottom-right-radius: calc(0.25rem - 1px);
  border-bottom-left-radius: calc(0.25rem - 1px);
}

.card > .card-header + .list-group,
.card > .list-group + .card-footer {
  border-top: 0;
}

.card-body {
  flex: 1 1 auto;
  min-height: 1px;
  padding: 1.25rem;
}

.card-title {
  margin-bottom: 0.75rem;
}

.card-subtitle {
  margin-top: -0.375rem;
  margin-bottom: 0;
}

.card-text:last-child {
  margin-bottom: 0;
}

.card-link:hover {
  text-decoration: none;
}

.card-link + .card-link {
  margin-left: 1.25rem;
}

.card-header {
  padding: 0.75rem 1.25rem;
  margin-bottom: 0;
  background-color: rgba(0, 0, 0, 0.03);
  border-bottom: 1px solid rgba(0, 0, 0, 0.125);
}

.card-header:first-child {
  border-radius: calc(0.25rem - 1px) calc(0.25rem - 1px) 0 0;
}

.card-footer {
  padding: 0.75rem 1.25rem;
  background-color: rgba(0, 0, 0, 0.03);
  border-top: 1px solid rgba(0, 0, 0, 0.125);
}

.card-footer:last-child {
  border-radius: 0 0 calc(0.25rem - 1px) calc(0.25rem - 1px);
}

.card-header-tabs {
  margin-right: -0.625rem;
  margin-bottom: -0.75rem;
  margin-left: -0.625rem;
  border-bottom: 0;
}

.card-header-pills {
  margin-right: -0.625rem;
  margin-left: -0.625rem;
}

.card-img-overlay {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  padding: 1.25rem;
  border-radius: calc(0.25rem - 1px);
}

.card-img,
.card-img-top,
.card-img-bottom {
  flex-shrink: 0;
  width: 100%;
}

.card-img,
.card-img-top {
  border-top-left-radius: calc(0.25rem - 1px);
  border-top-right-radius: calc(0.25rem - 1px);
}

.card-img,
.card-img-bottom {
  border-bottom-right-radius: calc(0.25rem - 1px);
  border-bottom-left-radius: calc(0.25rem - 1px);
}

.card-deck .card {
  margin-bottom: 15px;
}

@media (min-width: 576px) {
  .card-deck {
    display: flex;
    flex-flow: row wrap;
    margin-right: -15px;
    margin-left: -15px;
  }
  .card-deck .card {
    flex: 1 0 0%;
    margin-right: 15px;
    margin-bottom: 0;
    margin-left: 15px;
  }
}

.card-group > .card {
  margin-bottom: 15px;
}

@media (min-width: 576px) {
  .card-group {
    display: flex;
    flex-flow: row wrap;
  }
  .card-group > .card {
    flex: 1 0 0%;
    margin-bottom: 0;
  }
  .card-group > .card + .card {
    margin-left: 0;
    border-left: 0;
  }
  .card-group > .card:not(:last-child) {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
  }
  .card-group > .card:not(:last-child) .card-img-top,
  .card-group > .card:not(:last-child) .card-header {
    border-top-right-radius: 0;
  }
  .card-group > .card:not(:last-child) .card-img-bottom,
  .card-group > .card:not(:last-child) .card-footer {
    border-bottom-right-radius: 0;
  }
  .card-group > .card:not(:first-child) {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
  }
  .card-group > .card:not(:first-child) .card-img-top,
  .card-group > .card:not(:first-child) .card-header {
    border-top-left-radius: 0;
  }
  .card-group > .card:not(:first-child) .card-img-bottom,
  .card-group > .card:not(:first-child) .card-footer {
    border-bottom-left-radius: 0;
  }
}

.card-columns .card {
  margin-bottom: 0.75rem;
}

@media (min-width: 576px) {
  .card-columns {
    -moz-column-count: 3;
    column-count: 3;
    -moz-column-gap: 1.25rem;
    column-gap: 1.25rem;
    orphans: 1;
    widows: 1;
  }
  .card-columns .card {
    display: inline-block;
    width: 100%;
  }
}

.accordion {
  overflow-anchor: none;
}

.accordion > .card {
  overflow: hidden;
}

.accordion > .card:not(:last-of-type) {
  border-bottom: 0;
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
}

.accordion > .card:not(:first-of-type) {
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}

.accordion > .card > .card-header {
  border-radius: 0;
  margin-bottom: -1px;
}

.breadcrumb {
  display: flex;
  flex-wrap: wrap;
  padding: 0.75rem 1rem;
  margin-bottom: 1rem;
  list-style: none;
  background-color: #e9ecef;
  border-radius: 0.25rem;
}

.breadcrumb-item {
  display: flex;
}

.breadcrumb-item + .breadcrumb-item {
  padding-left: 0.5rem;
}

.breadcrumb-item + .breadcrumb-item::before {
  display: inline-block;
  padding-right: 0.5rem;
  color: #6c757d;
  content: '/';
}

.breadcrumb-item + .breadcrumb-item:hover::before {
  text-decoration: underline;
}

.breadcrumb-item + .breadcrumb-item:hover::before {
  text-decoration: none;
}

.breadcrumb-item.active {
  color: #6c757d;
}

.pagination {
  display: flex;
  padding-left: 0;
  list-style: none;
  border-radius: 0.25rem;
}

.page-link {
  position: relative;
  display: block;
  padding: 0.5rem 0.75rem;
  margin-left: -1px;
  line-height: 1.25;
  color: #473bf0;
  background-color: #fff;
  border: 1px solid #dee2e6;
}

.page-link:hover {
  z-index: 2;
  color: #1c10cf;
  text-decoration: none;
  background-color: #e9ecef;
  border-color: #dee2e6;
}

.page-link:focus {
  z-index: 3;
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(71, 59, 240, 0.25);
}

.page-item:first-child .page-link {
  margin-left: 0;
  border-top-left-radius: 0.25rem;
  border-bottom-left-radius: 0.25rem;
}

.page-item:last-child .page-link {
  border-top-right-radius: 0.25rem;
  border-bottom-right-radius: 0.25rem;
}

.page-item.active .page-link {
  z-index: 3;
  color: #fff;
  background-color: #473bf0;
  border-color: #473bf0;
}

.page-item.disabled .page-link {
  color: #6c757d;
  pointer-events: none;
  cursor: auto;
  background-color: #fff;
  border-color: #dee2e6;
}

.pagination-lg .page-link {
  padding: 0.75rem 1.5rem;
  font-size: 1.25rem;
  line-height: 1.5;
}

.pagination-lg .page-item:first-child .page-link {
  border-top-left-radius: 0.3rem;
  border-bottom-left-radius: 0.3rem;
}

.pagination-lg .page-item:last-child .page-link {
  border-top-right-radius: 0.3rem;
  border-bottom-right-radius: 0.3rem;
}

.pagination-sm .page-link {
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
  line-height: 1.5;
}

.pagination-sm .page-item:first-child .page-link {
  border-top-left-radius: 0.2rem;
  border-bottom-left-radius: 0.2rem;
}

.pagination-sm .page-item:last-child .page-link {
  border-top-right-radius: 0.2rem;
  border-bottom-right-radius: 0.2rem;
}

.badge {
  display: inline-block;
  padding: 0.25em 0.4em;
  font-size: 75%;
  font-weight: 700;
  line-height: 1;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: 0.25rem;
  transition: 0.4s cubic-bezier(0.39, 0.575, 0.565, 1);
}

@media (prefers-reduced-motion: reduce) {
  .badge {
    transition: none;
  }
}

a.badge:hover,
a.badge:focus {
  text-decoration: none;
}

.badge:empty {
  display: none;
}

.btn .badge {
  position: relative;
  top: -1px;
}

.badge-pill {
  padding-right: 0.6em;
  padding-left: 0.6em;
  border-radius: 10rem;
}

.badge-primary {
  color: #fff;
  background-color: #473bf0;
}

a.badge-primary:hover,
a.badge-primary:focus {
  color: #fff;
  background-color: #2012e6;
}

a.badge-primary:focus,
a.badge-primary.focus {
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(71, 59, 240, 0.5);
}

.badge-secondary {
  color: #212529;
  background-color: #68d585;
}

a.badge-secondary:hover,
a.badge-secondary:focus {
  color: #212529;
  background-color: #40ca65;
}

a.badge-secondary:focus,
a.badge-secondary.focus {
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(104, 213, 133, 0.5);
}

.badge-success {
  color: #212529;
  background-color: #68d585;
}

a.badge-success:hover,
a.badge-success:focus {
  color: #212529;
  background-color: #40ca65;
}

a.badge-success:focus,
a.badge-success.focus {
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(104, 213, 133, 0.5);
}

.badge-info {
  color: #fff;
  background-color: #17a2b8;
}

a.badge-info:hover,
a.badge-info:focus {
  color: #fff;
  background-color: #117a8b;
}

a.badge-info:focus,
a.badge-info.focus {
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(23, 162, 184, 0.5);
}

.badge-warning {
  color: #212529;
  background-color: #f7e36d;
}

a.badge-warning:hover,
a.badge-warning:focus {
  color: #212529;
  background-color: #f4da3d;
}

a.badge-warning:focus,
a.badge-warning.focus {
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(247, 227, 109, 0.5);
}

.badge-danger {
  color: #fff;
  background-color: #f64b4b;
}

a.badge-danger:hover,
a.badge-danger:focus {
  color: #fff;
  background-color: #f41a1a;
}

a.badge-danger:focus,
a.badge-danger.focus {
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(246, 75, 75, 0.5);
}

.badge-light {
  color: #212529;
  background-color: #f8f9fa;
}

a.badge-light:hover,
a.badge-light:focus {
  color: #212529;
  background-color: #dae0e5;
}

a.badge-light:focus,
a.badge-light.focus {
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(248, 249, 250, 0.5);
}

.badge-dark {
  color: #fff;
  background-color: #343a40;
}

a.badge-dark:hover,
a.badge-dark:focus {
  color: #fff;
  background-color: #1d2124;
}

a.badge-dark:focus,
a.badge-dark.focus {
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(52, 58, 64, 0.5);
}

.badge-red {
  color: #fff;
  background-color: #f64b4b;
}

a.badge-red:hover,
a.badge-red:focus {
  color: #fff;
  background-color: #f41a1a;
}

a.badge-red:focus,
a.badge-red.focus {
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(246, 75, 75, 0.5);
}

.badge-green {
  color: #212529;
  background-color: #68d585;
}

a.badge-green:hover,
a.badge-green:focus {
  color: #212529;
  background-color: #40ca65;
}

a.badge-green:focus,
a.badge-green.focus {
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(104, 213, 133, 0.5);
}

.badge-green-shamrock {
  color: #212529;
  background-color: #2bd67b;
}

a.badge-green-shamrock:hover,
a.badge-green-shamrock:focus {
  color: #212529;
  background-color: #21ad63;
}

a.badge-green-shamrock:focus,
a.badge-green-shamrock.focus {
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(43, 214, 123, 0.5);
}

.badge-blue {
  color: #fff;
  background-color: #473bf0;
}

a.badge-blue:hover,
a.badge-blue:focus {
  color: #fff;
  background-color: #2012e6;
}

a.badge-blue:focus,
a.badge-blue.focus {
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(71, 59, 240, 0.5);
}

.badge-sky-blue {
  color: #fff;
  background-color: #1082e9;
}

a.badge-sky-blue:hover,
a.badge-sky-blue:focus {
  color: #fff;
  background-color: #0d67b9;
}

a.badge-sky-blue:focus,
a.badge-sky-blue.focus {
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(16, 130, 233, 0.5);
}

.badge-yellow {
  color: #212529;
  background-color: #f7e36d;
}

a.badge-yellow:hover,
a.badge-yellow:focus {
  color: #212529;
  background-color: #f4da3d;
}

a.badge-yellow:focus,
a.badge-yellow.focus {
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(247, 227, 109, 0.5);
}

.badge-yellow-orange {
  color: #212529;
  background-color: #fcad38;
}

a.badge-yellow-orange:hover,
a.badge-yellow-orange:focus {
  color: #212529;
  background-color: #fb9806;
}

a.badge-yellow-orange:focus,
a.badge-yellow-orange.focus {
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(252, 173, 56, 0.5);
}

.badge-blackish-blue {
  color: #fff;
  background-color: #13151c;
}

a.badge-blackish-blue:hover,
a.badge-blackish-blue:focus {
  color: #fff;
  background-color: black;
}

a.badge-blackish-blue:focus,
a.badge-blackish-blue.focus {
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(19, 21, 28, 0.5);
}

.badge-black {
  color: #fff;
  background-color: #000;
}

a.badge-black:hover,
a.badge-black:focus {
  color: #fff;
  background-color: black;
}

a.badge-black:focus,
a.badge-black.focus {
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(0, 0, 0, 0.5);
}

.badge-mirage {
  color: #fff;
  background-color: #131829;
}

a.badge-mirage:hover,
a.badge-mirage:focus {
  color: #fff;
  background-color: #030406;
}

a.badge-mirage:focus,
a.badge-mirage.focus {
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(19, 24, 41, 0.5);
}

.badge-mirage-2 {
  color: #fff;
  background-color: #161c2d;
}

a.badge-mirage-2:hover,
a.badge-mirage-2:focus {
  color: #fff;
  background-color: #05070b;
}

a.badge-mirage-2:focus,
a.badge-mirage-2.focus {
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(22, 28, 45, 0.5);
}

.badge-white {
  color: #212529;
  background-color: #fff;
}

a.badge-white:hover,
a.badge-white:focus {
  color: #212529;
  background-color: #e6e6e6;
}

a.badge-white:focus,
a.badge-white.focus {
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.5);
}

.badge-smoke {
  color: #212529;
  background-color: #f8f8f8;
}

a.badge-smoke:hover,
a.badge-smoke:focus {
  color: #212529;
  background-color: #dfdfdf;
}

a.badge-smoke:focus,
a.badge-smoke.focus {
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(248, 248, 248, 0.5);
}

.badge-storm {
  color: #fff;
  background-color: #7d818d;
}

a.badge-storm:hover,
a.badge-storm:focus {
  color: #fff;
  background-color: #646873;
}

a.badge-storm:focus,
a.badge-storm.focus {
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(125, 129, 141, 0.5);
}

.badge-ghost {
  color: #212529;
  background-color: #fdfdff;
}

a.badge-ghost:hover,
a.badge-ghost:focus {
  color: #212529;
  background-color: #cacaff;
}

a.badge-ghost:focus,
a.badge-ghost.focus {
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(253, 253, 255, 0.5);
}

.badge-gray-1 {
  color: #212529;
  background-color: #fcfdfe;
}

a.badge-gray-1:hover,
a.badge-gray-1:focus {
  color: #212529;
  background-color: #d6e4f1;
}

a.badge-gray-1:focus,
a.badge-gray-1.focus {
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(252, 253, 254, 0.5);
}

.badge-gray-2 {
  color: #212529;
  background-color: #f4f7fa;
}

a.badge-gray-2:hover,
a.badge-gray-2:focus {
  color: #212529;
  background-color: #d1deea;
}

a.badge-gray-2:focus,
a.badge-gray-2.focus {
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(244, 247, 250, 0.5);
}

.badge-gray-3 {
  color: #212529;
  background-color: #e7e9ed;
}

a.badge-gray-3:hover,
a.badge-gray-3:focus {
  color: #212529;
  background-color: #caced7;
}

a.badge-gray-3:focus,
a.badge-gray-3.focus {
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(231, 233, 237, 0.5);
}

.badge-gray-310 {
  color: #212529;
  background-color: #d5d7dd;
}

a.badge-gray-310:hover,
a.badge-gray-310:focus {
  color: #212529;
  background-color: #b9bcc6;
}

a.badge-gray-310:focus,
a.badge-gray-310.focus {
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(213, 215, 221, 0.5);
}

.badge-gray-opacity {
  color: #212529;
  background-color: rgba(231, 233, 237, 0.13);
}

a.badge-gray-opacity:hover,
a.badge-gray-opacity:focus {
  color: #212529;
  background-color: rgba(202, 206, 215, 0.13);
}

a.badge-gray-opacity:focus,
a.badge-gray-opacity.focus {
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(231, 233, 237, 0.5);
}

.badge-blackish-blue-opacity {
  color: #fff;
  background-color: rgba(22, 28, 45, 0.7);
}

a.badge-blackish-blue-opacity:hover,
a.badge-blackish-blue-opacity:focus {
  color: #fff;
  background-color: rgba(5, 7, 11, 0.7);
}

a.badge-blackish-blue-opacity:focus,
a.badge-blackish-blue-opacity.focus {
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(22, 28, 45, 0.5);
}

.badge-narvik {
  color: #212529;
  background-color: #edf9f2;
}

a.badge-narvik:hover,
a.badge-narvik:focus {
  color: #212529;
  background-color: #c7ecd6;
}

a.badge-narvik:focus,
a.badge-narvik.focus {
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(237, 249, 242, 0.5);
}

.jumbotron {
  padding: 2rem 1rem;
  margin-bottom: 2rem;
  background-color: #e9ecef;
  border-radius: 0.3rem;
}

@media (min-width: 576px) {
  .jumbotron {
    padding: 4rem 2rem;
  }
}

.jumbotron-fluid {
  padding-right: 0;
  padding-left: 0;
  border-radius: 0;
}

.alert {
  position: relative;
  padding: 0.75rem 1.25rem;
  margin-bottom: 1rem;
  border: 1px solid transparent;
  border-radius: 0.25rem;
}

.alert-heading {
  color: inherit;
}

.alert-link {
  font-weight: 700;
}

.alert-dismissible {
  padding-right: 4rem;
}

.alert-dismissible .close {
  position: absolute;
  top: 0;
  right: 0;
  z-index: 2;
  padding: 0.75rem 1.25rem;
  color: inherit;
}

.alert-primary {
  color: #251f7d;
  background-color: #dad8fc;
  border-color: #cbc8fb;
}

.alert-primary hr {
  border-top-color: #b5b0f9;
}

.alert-primary .alert-link {
  color: #191554;
}

.alert-secondary {
  color: #366f45;
  background-color: #e1f7e7;
  border-color: #d5f3dd;
}

.alert-secondary hr {
  border-top-color: #c1edcd;
}

.alert-secondary .alert-link {
  color: #254d30;
}

.alert-success {
  color: #366f45;
  background-color: #e1f7e7;
  border-color: #d5f3dd;
}

.alert-success hr {
  border-top-color: #c1edcd;
}

.alert-success .alert-link {
  color: #254d30;
}

.alert-info {
  color: #0c5460;
  background-color: #d1ecf1;
  border-color: #bee5eb;
}

.alert-info hr {
  border-top-color: #abdde5;
}

.alert-info .alert-link {
  color: #062c33;
}

.alert-warning {
  color: #807639;
  background-color: #fdf9e2;
  border-color: #fdf7d6;
}

.alert-warning hr {
  border-top-color: #fcf2be;
}

.alert-warning .alert-link {
  color: #5d5529;
}

.alert-danger {
  color: #802727;
  background-color: #fddbdb;
  border-color: #fccdcd;
}

.alert-danger hr {
  border-top-color: #fbb5b5;
}

.alert-danger .alert-link {
  color: #591b1b;
}

.alert-light {
  color: #818182;
  background-color: #fefefe;
  border-color: #fdfdfe;
}

.alert-light hr {
  border-top-color: #ececf6;
}

.alert-light .alert-link {
  color: #686868;
}

.alert-dark {
  color: #1b1e21;
  background-color: #d6d8d9;
  border-color: #c6c8ca;
}

.alert-dark hr {
  border-top-color: #b9bbbe;
}

.alert-dark .alert-link {
  color: #040505;
}

.alert-red {
  color: #802727;
  background-color: #fddbdb;
  border-color: #fccdcd;
}

.alert-red hr {
  border-top-color: #fbb5b5;
}

.alert-red .alert-link {
  color: #591b1b;
}

.alert-green {
  color: #366f45;
  background-color: #e1f7e7;
  border-color: #d5f3dd;
}

.alert-green hr {
  border-top-color: #c1edcd;
}

.alert-green .alert-link {
  color: #254d30;
}

.alert-green-shamrock {
  color: #166f40;
  background-color: #d5f7e5;
  border-color: #c4f4da;
}

.alert-green-shamrock hr {
  border-top-color: #aff0cd;
}

.alert-green-shamrock .alert-link {
  color: #0e4427;
}

.alert-blue {
  color: #251f7d;
  background-color: #dad8fc;
  border-color: #cbc8fb;
}

.alert-blue hr {
  border-top-color: #b5b0f9;
}

.alert-blue .alert-link {
  color: #191554;
}

.alert-sky-blue {
  color: #084479;
  background-color: #cfe6fb;
  border-color: #bcdcf9;
}

.alert-sky-blue hr {
  border-top-color: #a5d0f7;
}

.alert-sky-blue .alert-link {
  color: #052949;
}

.alert-yellow {
  color: #807639;
  background-color: #fdf9e2;
  border-color: #fdf7d6;
}

.alert-yellow hr {
  border-top-color: #fcf2be;
}

.alert-yellow .alert-link {
  color: #5d5529;
}

.alert-yellow-orange {
  color: #835a1d;
  background-color: #feefd7;
  border-color: #fee8c7;
}

.alert-yellow-orange hr {
  border-top-color: #fedeae;
}

.alert-yellow-orange .alert-link {
  color: #593d14;
}

.alert-blackish-blue {
  color: #0a0b0f;
  background-color: #d0d0d2;
  border-color: #bdbdbf;
}

.alert-blackish-blue hr {
  border-top-color: #b0b0b2;
}

.alert-blackish-blue .alert-link {
  color: black;
}

.alert-black {
  color: black;
  background-color: #cccccc;
  border-color: #b8b8b8;
}

.alert-black hr {
  border-top-color: #ababab;
}

.alert-black .alert-link {
  color: black;
}

.alert-mirage {
  color: #0a0c15;
  background-color: #d0d1d4;
  border-color: #bdbec3;
}

.alert-mirage hr {
  border-top-color: #b0b1b7;
}

.alert-mirage .alert-link {
  color: black;
}

.alert-mirage-2 {
  color: #0b0f17;
  background-color: #d0d2d5;
  border-color: #bebfc4;
}

.alert-mirage-2 hr {
  border-top-color: #b1b2b8;
}

.alert-mirage-2 .alert-link {
  color: black;
}

.alert-white {
  color: #858585;
  background-color: white;
  border-color: white;
}

.alert-white hr {
  border-top-color: #f2f2f2;
}

.alert-white .alert-link {
  color: #6c6c6c;
}

.alert-smoke {
  color: #818181;
  background-color: #fefefe;
  border-color: #fdfdfd;
}

.alert-smoke hr {
  border-top-color: #f0f0f0;
}

.alert-smoke .alert-link {
  color: #686868;
}

.alert-storm {
  color: #414349;
  background-color: #e5e6e8;
  border-color: #dbdcdf;
}

.alert-storm hr {
  border-top-color: #cecfd3;
}

.alert-storm .alert-link {
  color: #292a2e;
}

.alert-ghost {
  color: #848485;
  background-color: white;
  border-color: #fefeff;
}

.alert-ghost hr {
  border-top-color: #e5e5ff;
}

.alert-ghost .alert-link {
  color: #6b6b6b;
}

.alert-gray-1 {
  color: #838484;
  background-color: #feffff;
  border-color: #fefeff;
}

.alert-gray-1 hr {
  border-top-color: #e5e5ff;
}

.alert-gray-1 .alert-link {
  color: #6a6a6a;
}

.alert-gray-2 {
  color: #7f8082;
  background-color: #fdfdfe;
  border-color: #fcfdfe;
}

.alert-gray-2 hr {
  border-top-color: #e9f0f8;
}

.alert-gray-2 .alert-link {
  color: #666768;
}

.alert-gray-3 {
  color: #78797b;
  background-color: #fafbfb;
  border-color: #f8f9fa;
}

.alert-gray-3 hr {
  border-top-color: #e9ecef;
}

.alert-gray-3 .alert-link {
  color: #5f6061;
}

.alert-gray-310 {
  color: #6f7073;
  background-color: #f7f7f8;
  border-color: #f3f4f5;
}

.alert-gray-310 hr {
  border-top-color: #e5e7e9;
}

.alert-gray-310 .alert-link {
  color: #565759;
}

.alert-gray-opacity {
  color: rgba(16, 16, 17, 0.5476);
  background-color: rgba(255, 255, 255, 0.826);
  border-color: rgba(254, 254, 255, 0.7564);
}

.alert-gray-opacity hr {
  border-top-color: rgba(229, 229, 255, 0.7564);
}

.alert-gray-opacity .alert-link {
  color: rgba(0, 0, 0, 0.5476);
}

.alert-blackish-blue-opacity {
  color: rgba(8, 10, 17, 0.844);
  background-color: rgba(227, 228, 230, 0.94);
  border-color: rgba(215, 216, 219, 0.916);
}

.alert-blackish-blue-opacity hr {
  border-top-color: rgba(202, 203, 207, 0.916);
}

.alert-blackish-blue-opacity .alert-link {
  color: rgba(0, 0, 0, 0.844);
}

.alert-narvik {
  color: #7b817e;
  background-color: #fbfefc;
  border-color: #fafdfb;
}

.alert-narvik hr {
  border-top-color: #e8f6ec;
}

.alert-narvik .alert-link {
  color: #626765;
}

@-webkit-keyframes progress-bar-stripes {
  from {
    background-position: 1rem 0;
  }
  to {
    background-position: 0 0;
  }
}

@keyframes progress-bar-stripes {
  from {
    background-position: 1rem 0;
  }
  to {
    background-position: 0 0;
  }
}

.progress {
  display: flex;
  height: 1rem;
  overflow: hidden;
  line-height: 0;
  font-size: 0.75rem;
  background-color: #e9ecef;
  border-radius: 0.25rem;
}

.progress-bar {
  display: flex;
  flex-direction: column;
  justify-content: center;
  overflow: hidden;
  color: #fff;
  text-align: center;
  white-space: nowrap;
  background-color: #473bf0;
  transition: width 0.6s ease;
}

@media (prefers-reduced-motion: reduce) {
  .progress-bar {
    transition: none;
  }
}

.progress-bar-striped {
  background-image: linear-gradient(
    45deg,
    rgba(255, 255, 255, 0.15) 25%,
    transparent 25%,
    transparent 50%,
    rgba(255, 255, 255, 0.15) 50%,
    rgba(255, 255, 255, 0.15) 75%,
    transparent 75%,
    transparent
  );
  background-size: 1rem 1rem;
}

.progress-bar-animated {
  -webkit-animation: progress-bar-stripes 1s linear infinite;
  animation: progress-bar-stripes 1s linear infinite;
}

@media (prefers-reduced-motion: reduce) {
  .progress-bar-animated {
    -webkit-animation: none;
    animation: none;
  }
}

.media {
  display: flex;
  align-items: flex-start;
}

.media-body {
  flex: 1;
}

.list-group {
  display: flex;
  flex-direction: column;
  padding-left: 0;
  margin-bottom: 0;
  border-radius: 0.25rem;
}

.list-group-item-action {
  width: 100%;
  color: #495057;
  text-align: inherit;
}

.list-group-item-action:hover,
.list-group-item-action:focus {
  z-index: 1;
  color: #495057;
  text-decoration: none;
  background-color: #f8f9fa;
}

.list-group-item-action:active {
  color: var(--color-texts-opacity);
  background-color: #e9ecef;
}

.list-group-item {
  position: relative;
  display: block;
  padding: 0.75rem 1.25rem;
  background-color: #fff;
  border: 1px solid rgba(0, 0, 0, 0.125);
}

.list-group-item:first-child {
  border-top-left-radius: inherit;
  border-top-right-radius: inherit;
}

.list-group-item:last-child {
  border-bottom-right-radius: inherit;
  border-bottom-left-radius: inherit;
}

.list-group-item.disabled,
.list-group-item:disabled {
  color: #6c757d;
  pointer-events: none;
  background-color: #fff;
}

.list-group-item.active {
  z-index: 2;
  color: #fff;
  background-color: #473bf0;
  border-color: #473bf0;
}

.list-group-item + .list-group-item {
  border-top-width: 0;
}

.list-group-item + .list-group-item.active {
  margin-top: -1px;
  border-top-width: 1px;
}

.list-group-horizontal {
  flex-direction: row;
}

.list-group-horizontal > .list-group-item:first-child {
  border-bottom-left-radius: 0.25rem;
  border-top-right-radius: 0;
}

.list-group-horizontal > .list-group-item:last-child {
  border-top-right-radius: 0.25rem;
  border-bottom-left-radius: 0;
}

.list-group-horizontal > .list-group-item.active {
  margin-top: 0;
}

.list-group-horizontal > .list-group-item + .list-group-item {
  border-top-width: 1px;
  border-left-width: 0;
}

.list-group-horizontal > .list-group-item + .list-group-item.active {
  margin-left: -1px;
  border-left-width: 1px;
}

@media (min-width: 480px) {
  .list-group-horizontal-xs {
    flex-direction: row;
  }
  .list-group-horizontal-xs > .list-group-item:first-child {
    border-bottom-left-radius: 0.25rem;
    border-top-right-radius: 0;
  }
  .list-group-horizontal-xs > .list-group-item:last-child {
    border-top-right-radius: 0.25rem;
    border-bottom-left-radius: 0;
  }
  .list-group-horizontal-xs > .list-group-item.active {
    margin-top: 0;
  }
  .list-group-horizontal-xs > .list-group-item + .list-group-item {
    border-top-width: 1px;
    border-left-width: 0;
  }
  .list-group-horizontal-xs > .list-group-item + .list-group-item.active {
    margin-left: -1px;
    border-left-width: 1px;
  }
}

@media (min-width: 576px) {
  .list-group-horizontal-sm {
    flex-direction: row;
  }
  .list-group-horizontal-sm > .list-group-item:first-child {
    border-bottom-left-radius: 0.25rem;
    border-top-right-radius: 0;
  }
  .list-group-horizontal-sm > .list-group-item:last-child {
    border-top-right-radius: 0.25rem;
    border-bottom-left-radius: 0;
  }
  .list-group-horizontal-sm > .list-group-item.active {
    margin-top: 0;
  }
  .list-group-horizontal-sm > .list-group-item + .list-group-item {
    border-top-width: 1px;
    border-left-width: 0;
  }
  .list-group-horizontal-sm > .list-group-item + .list-group-item.active {
    margin-left: -1px;
    border-left-width: 1px;
  }
}

@media (min-width: 768px) {
  .list-group-horizontal-md {
    flex-direction: row;
  }
  .list-group-horizontal-md > .list-group-item:first-child {
    border-bottom-left-radius: 0.25rem;
    border-top-right-radius: 0;
  }
  .list-group-horizontal-md > .list-group-item:last-child {
    border-top-right-radius: 0.25rem;
    border-bottom-left-radius: 0;
  }
  .list-group-horizontal-md > .list-group-item.active {
    margin-top: 0;
  }
  .list-group-horizontal-md > .list-group-item + .list-group-item {
    border-top-width: 1px;
    border-left-width: 0;
  }
  .list-group-horizontal-md > .list-group-item + .list-group-item.active {
    margin-left: -1px;
    border-left-width: 1px;
  }
}

@media (min-width: 992px) {
  .list-group-horizontal-lg {
    flex-direction: row;
  }
  .list-group-horizontal-lg > .list-group-item:first-child {
    border-bottom-left-radius: 0.25rem;
    border-top-right-radius: 0;
  }
  .list-group-horizontal-lg > .list-group-item:last-child {
    border-top-right-radius: 0.25rem;
    border-bottom-left-radius: 0;
  }
  .list-group-horizontal-lg > .list-group-item.active {
    margin-top: 0;
  }
  .list-group-horizontal-lg > .list-group-item + .list-group-item {
    border-top-width: 1px;
    border-left-width: 0;
  }
  .list-group-horizontal-lg > .list-group-item + .list-group-item.active {
    margin-left: -1px;
    border-left-width: 1px;
  }
}

@media (min-width: 1200px) {
  .list-group-horizontal-xl {
    flex-direction: row;
  }
  .list-group-horizontal-xl > .list-group-item:first-child {
    border-bottom-left-radius: 0.25rem;
    border-top-right-radius: 0;
  }
  .list-group-horizontal-xl > .list-group-item:last-child {
    border-top-right-radius: 0.25rem;
    border-bottom-left-radius: 0;
  }
  .list-group-horizontal-xl > .list-group-item.active {
    margin-top: 0;
  }
  .list-group-horizontal-xl > .list-group-item + .list-group-item {
    border-top-width: 1px;
    border-left-width: 0;
  }
  .list-group-horizontal-xl > .list-group-item + .list-group-item.active {
    margin-left: -1px;
    border-left-width: 1px;
  }
}

.list-group-flush {
  border-radius: 0;
}

.list-group-flush > .list-group-item {
  border-width: 0 0 1px;
}

.list-group-flush > .list-group-item:last-child {
  border-bottom-width: 0;
}

.list-group-item-primary {
  color: #251f7d;
  background-color: #cbc8fb;
}

.list-group-item-primary.list-group-item-action:hover,
.list-group-item-primary.list-group-item-action:focus {
  color: #251f7d;
  background-color: #b5b0f9;
}

.list-group-item-primary.list-group-item-action.active {
  color: #fff;
  background-color: #251f7d;
  border-color: #251f7d;
}

.list-group-item-secondary {
  color: #366f45;
  background-color: #d5f3dd;
}

.list-group-item-secondary.list-group-item-action:hover,
.list-group-item-secondary.list-group-item-action:focus {
  color: #366f45;
  background-color: #c1edcd;
}

.list-group-item-secondary.list-group-item-action.active {
  color: #fff;
  background-color: #366f45;
  border-color: #366f45;
}

.list-group-item-success {
  color: #366f45;
  background-color: #d5f3dd;
}

.list-group-item-success.list-group-item-action:hover,
.list-group-item-success.list-group-item-action:focus {
  color: #366f45;
  background-color: #c1edcd;
}

.list-group-item-success.list-group-item-action.active {
  color: #fff;
  background-color: #366f45;
  border-color: #366f45;
}

.list-group-item-info {
  color: #0c5460;
  background-color: #bee5eb;
}

.list-group-item-info.list-group-item-action:hover,
.list-group-item-info.list-group-item-action:focus {
  color: #0c5460;
  background-color: #abdde5;
}

.list-group-item-info.list-group-item-action.active {
  color: #fff;
  background-color: #0c5460;
  border-color: #0c5460;
}

.list-group-item-warning {
  color: #807639;
  background-color: #fdf7d6;
}

.list-group-item-warning.list-group-item-action:hover,
.list-group-item-warning.list-group-item-action:focus {
  color: #807639;
  background-color: #fcf2be;
}

.list-group-item-warning.list-group-item-action.active {
  color: #fff;
  background-color: #807639;
  border-color: #807639;
}

.list-group-item-danger {
  color: #802727;
  background-color: #fccdcd;
}

.list-group-item-danger.list-group-item-action:hover,
.list-group-item-danger.list-group-item-action:focus {
  color: #802727;
  background-color: #fbb5b5;
}

.list-group-item-danger.list-group-item-action.active {
  color: #fff;
  background-color: #802727;
  border-color: #802727;
}

.list-group-item-light {
  color: #818182;
  background-color: #fdfdfe;
}

.list-group-item-light.list-group-item-action:hover,
.list-group-item-light.list-group-item-action:focus {
  color: #818182;
  background-color: #ececf6;
}

.list-group-item-light.list-group-item-action.active {
  color: #fff;
  background-color: #818182;
  border-color: #818182;
}

.list-group-item-dark {
  color: #1b1e21;
  background-color: #c6c8ca;
}

.list-group-item-dark.list-group-item-action:hover,
.list-group-item-dark.list-group-item-action:focus {
  color: #1b1e21;
  background-color: #b9bbbe;
}

.list-group-item-dark.list-group-item-action.active {
  color: #fff;
  background-color: #1b1e21;
  border-color: #1b1e21;
}

.list-group-item-red {
  color: #802727;
  background-color: #fccdcd;
}

.list-group-item-red.list-group-item-action:hover,
.list-group-item-red.list-group-item-action:focus {
  color: #802727;
  background-color: #fbb5b5;
}

.list-group-item-red.list-group-item-action.active {
  color: #fff;
  background-color: #802727;
  border-color: #802727;
}

.list-group-item-green {
  color: #366f45;
  background-color: #d5f3dd;
}

.list-group-item-green.list-group-item-action:hover,
.list-group-item-green.list-group-item-action:focus {
  color: #366f45;
  background-color: #c1edcd;
}

.list-group-item-green.list-group-item-action.active {
  color: #fff;
  background-color: #366f45;
  border-color: #366f45;
}

.list-group-item-green-shamrock {
  color: #166f40;
  background-color: #c4f4da;
}

.list-group-item-green-shamrock.list-group-item-action:hover,
.list-group-item-green-shamrock.list-group-item-action:focus {
  color: #166f40;
  background-color: #aff0cd;
}

.list-group-item-green-shamrock.list-group-item-action.active {
  color: #fff;
  background-color: #166f40;
  border-color: #166f40;
}

.list-group-item-blue {
  color: #251f7d;
  background-color: #cbc8fb;
}

.list-group-item-blue.list-group-item-action:hover,
.list-group-item-blue.list-group-item-action:focus {
  color: #251f7d;
  background-color: #b5b0f9;
}

.list-group-item-blue.list-group-item-action.active {
  color: #fff;
  background-color: #251f7d;
  border-color: #251f7d;
}

.list-group-item-sky-blue {
  color: #084479;
  background-color: #bcdcf9;
}

.list-group-item-sky-blue.list-group-item-action:hover,
.list-group-item-sky-blue.list-group-item-action:focus {
  color: #084479;
  background-color: #a5d0f7;
}

.list-group-item-sky-blue.list-group-item-action.active {
  color: #fff;
  background-color: #084479;
  border-color: #084479;
}

.list-group-item-yellow {
  color: #807639;
  background-color: #fdf7d6;
}

.list-group-item-yellow.list-group-item-action:hover,
.list-group-item-yellow.list-group-item-action:focus {
  color: #807639;
  background-color: #fcf2be;
}

.list-group-item-yellow.list-group-item-action.active {
  color: #fff;
  background-color: #807639;
  border-color: #807639;
}

.list-group-item-yellow-orange {
  color: #835a1d;
  background-color: #fee8c7;
}

.list-group-item-yellow-orange.list-group-item-action:hover,
.list-group-item-yellow-orange.list-group-item-action:focus {
  color: #835a1d;
  background-color: #fedeae;
}

.list-group-item-yellow-orange.list-group-item-action.active {
  color: #fff;
  background-color: #835a1d;
  border-color: #835a1d;
}

.list-group-item-blackish-blue {
  color: #0a0b0f;
  background-color: #bdbdbf;
}

.list-group-item-blackish-blue.list-group-item-action:hover,
.list-group-item-blackish-blue.list-group-item-action:focus {
  color: #0a0b0f;
  background-color: #b0b0b2;
}

.list-group-item-blackish-blue.list-group-item-action.active {
  color: #fff;
  background-color: #0a0b0f;
  border-color: #0a0b0f;
}

.list-group-item-black {
  color: black;
  background-color: #b8b8b8;
}

.list-group-item-black.list-group-item-action:hover,
.list-group-item-black.list-group-item-action:focus {
  color: black;
  background-color: #ababab;
}

.list-group-item-black.list-group-item-action.active {
  color: #fff;
  background-color: black;
  border-color: black;
}

.list-group-item-mirage {
  color: #0a0c15;
  background-color: #bdbec3;
}

.list-group-item-mirage.list-group-item-action:hover,
.list-group-item-mirage.list-group-item-action:focus {
  color: #0a0c15;
  background-color: #b0b1b7;
}

.list-group-item-mirage.list-group-item-action.active {
  color: #fff;
  background-color: #0a0c15;
  border-color: #0a0c15;
}

.list-group-item-mirage-2 {
  color: #0b0f17;
  background-color: #bebfc4;
}

.list-group-item-mirage-2.list-group-item-action:hover,
.list-group-item-mirage-2.list-group-item-action:focus {
  color: #0b0f17;
  background-color: #b1b2b8;
}

.list-group-item-mirage-2.list-group-item-action.active {
  color: #fff;
  background-color: #0b0f17;
  border-color: #0b0f17;
}

.list-group-item-white {
  color: #858585;
  background-color: white;
}

.list-group-item-white.list-group-item-action:hover,
.list-group-item-white.list-group-item-action:focus {
  color: #858585;
  background-color: #f2f2f2;
}

.list-group-item-white.list-group-item-action.active {
  color: #fff;
  background-color: #858585;
  border-color: #858585;
}

.list-group-item-smoke {
  color: #818181;
  background-color: #fdfdfd;
}

.list-group-item-smoke.list-group-item-action:hover,
.list-group-item-smoke.list-group-item-action:focus {
  color: #818181;
  background-color: #f0f0f0;
}

.list-group-item-smoke.list-group-item-action.active {
  color: #fff;
  background-color: #818181;
  border-color: #818181;
}

.list-group-item-storm {
  color: #414349;
  background-color: #dbdcdf;
}

.list-group-item-storm.list-group-item-action:hover,
.list-group-item-storm.list-group-item-action:focus {
  color: #414349;
  background-color: #cecfd3;
}

.list-group-item-storm.list-group-item-action.active {
  color: #fff;
  background-color: #414349;
  border-color: #414349;
}

.list-group-item-ghost {
  color: #848485;
  background-color: #fefeff;
}

.list-group-item-ghost.list-group-item-action:hover,
.list-group-item-ghost.list-group-item-action:focus {
  color: #848485;
  background-color: #e5e5ff;
}

.list-group-item-ghost.list-group-item-action.active {
  color: #fff;
  background-color: #848485;
  border-color: #848485;
}

.list-group-item-gray-1 {
  color: #838484;
  background-color: #fefeff;
}

.list-group-item-gray-1.list-group-item-action:hover,
.list-group-item-gray-1.list-group-item-action:focus {
  color: #838484;
  background-color: #e5e5ff;
}

.list-group-item-gray-1.list-group-item-action.active {
  color: #fff;
  background-color: #838484;
  border-color: #838484;
}

.list-group-item-gray-2 {
  color: #7f8082;
  background-color: #fcfdfe;
}

.list-group-item-gray-2.list-group-item-action:hover,
.list-group-item-gray-2.list-group-item-action:focus {
  color: #7f8082;
  background-color: #e9f0f8;
}

.list-group-item-gray-2.list-group-item-action.active {
  color: #fff;
  background-color: #7f8082;
  border-color: #7f8082;
}

.list-group-item-gray-3 {
  color: #78797b;
  background-color: #f8f9fa;
}

.list-group-item-gray-3.list-group-item-action:hover,
.list-group-item-gray-3.list-group-item-action:focus {
  color: #78797b;
  background-color: #e9ecef;
}

.list-group-item-gray-3.list-group-item-action.active {
  color: #fff;
  background-color: #78797b;
  border-color: #78797b;
}

.list-group-item-gray-310 {
  color: #6f7073;
  background-color: #f3f4f5;
}

.list-group-item-gray-310.list-group-item-action:hover,
.list-group-item-gray-310.list-group-item-action:focus {
  color: #6f7073;
  background-color: #e5e7e9;
}

.list-group-item-gray-310.list-group-item-action.active {
  color: #fff;
  background-color: #6f7073;
  border-color: #6f7073;
}

.list-group-item-gray-opacity {
  color: rgba(16, 16, 17, 0.5476);
  background-color: rgba(254, 254, 255, 0.7564);
}

.list-group-item-gray-opacity.list-group-item-action:hover,
.list-group-item-gray-opacity.list-group-item-action:focus {
  color: rgba(16, 16, 17, 0.5476);
  background-color: rgba(229, 229, 255, 0.7564);
}

.list-group-item-gray-opacity.list-group-item-action.active {
  color: #fff;
  background-color: rgba(16, 16, 17, 0.5476);
  border-color: rgba(16, 16, 17, 0.5476);
}

.list-group-item-blackish-blue-opacity {
  color: rgba(8, 10, 17, 0.844);
  background-color: rgba(215, 216, 219, 0.916);
}

.list-group-item-blackish-blue-opacity.list-group-item-action:hover,
.list-group-item-blackish-blue-opacity.list-group-item-action:focus {
  color: rgba(8, 10, 17, 0.844);
  background-color: rgba(202, 203, 207, 0.916);
}

.list-group-item-blackish-blue-opacity.list-group-item-action.active {
  color: #fff;
  background-color: rgba(8, 10, 17, 0.844);
  border-color: rgba(8, 10, 17, 0.844);
}

.list-group-item-narvik {
  color: #7b817e;
  background-color: #fafdfb;
}

.list-group-item-narvik.list-group-item-action:hover,
.list-group-item-narvik.list-group-item-action:focus {
  color: #7b817e;
  background-color: #e8f6ec;
}

.list-group-item-narvik.list-group-item-action.active {
  color: #fff;
  background-color: #7b817e;
  border-color: #7b817e;
}

.close {
  float: right;
  font-size: 1.5rem;
  font-weight: 700;
  line-height: 1;
  color: #000;
  text-shadow: 0 1px 0 #fff;
  opacity: 0.5;
}

@media (max-width: 1200px) {
  .close {
    font-size: calc(1.275rem + 0.3vw);
  }
}

.close:hover {
  color: #000;
  text-decoration: none;
}

.close:not(:disabled):not(.disabled):hover,
.close:not(:disabled):not(.disabled):focus {
  opacity: 0.75;
}

button.close {
  padding: 0;
  background-color: transparent;
  border: 0;
}

a.close.disabled {
  pointer-events: none;
}

.toast {
  flex-basis: 350px;
  max-width: 350px;
  font-size: 0.875rem;
  background-color: rgba(255, 255, 255, 0.85);
  background-clip: padding-box;
  border: 1px solid rgba(0, 0, 0, 0.1);
  box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.1);
  opacity: 0;
  border-radius: 0.25rem;
}

.toast:not(:last-child) {
  margin-bottom: 0.75rem;
}

.toast.showing {
  opacity: 1;
}

.toast.show {
  display: block;
  opacity: 1;
}

.toast.hide {
  display: none;
}

.toast-header {
  display: flex;
  align-items: center;
  padding: 0.25rem 0.75rem;
  color: #6c757d;
  background-color: rgba(255, 255, 255, 0.85);
  background-clip: padding-box;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  border-top-left-radius: calc(0.25rem - 1px);
  border-top-right-radius: calc(0.25rem - 1px);
}

.toast-body {
  padding: 0.75rem;
}

.modal-open {
  overflow: hidden;
}

.modal-open .modal {
  overflow-x: hidden;
  overflow-y: auto;
}

.modal {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1050;
  display: none;
  width: 100%;
  height: 100%;
  overflow: hidden;
  outline: 0;
}

.modal-dialog {
  position: relative;
  width: auto;
  margin: 0.5rem;
  pointer-events: none;
}

.modal.fade .modal-dialog {
  transition: transform 0.3s ease-out;
  transform: translate(0, -50px);
}

@media (prefers-reduced-motion: reduce) {
  .modal.fade .modal-dialog {
    transition: none;
  }
}

.modal.show .modal-dialog {
  transform: none;
}

.modal.modal-static .modal-dialog {
  transform: scale(1.02);
}

.modal-dialog-scrollable {
  display: flex;
  max-height: calc(100% - 1rem);
}

.modal-dialog-scrollable .modal-content {
  max-height: calc(100vh - 1rem);
  overflow: hidden;
}

.modal-dialog-scrollable .modal-header,
.modal-dialog-scrollable .modal-footer {
  flex-shrink: 0;
}

.modal-dialog-scrollable .modal-body {
  overflow-y: auto;
}

.modal-dialog-centered {
  display: flex;
  align-items: center;
  min-height: calc(100% - 1rem);
}

.modal-dialog-centered::before {
  display: block;
  height: calc(100vh - 1rem);
  height: -webkit-min-content;
  height: -moz-min-content;
  height: min-content;
  content: '';
}

.modal-dialog-centered.modal-dialog-scrollable {
  flex-direction: column;
  justify-content: center;
  height: 100%;
}

.modal-dialog-centered.modal-dialog-scrollable .modal-content {
  max-height: none;
}

.modal-dialog-centered.modal-dialog-scrollable::before {
  content: none;
}

.modal-content {
  position: relative;
  display: flex;
  flex-direction: column;
  width: 100%;
  pointer-events: auto;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid rgba(0, 0, 0, 0.2);
  border-radius: 0.3rem;
  outline: 0;
}

.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1040;
  width: 100vw;
  height: 100vh;
  background-color: #000;
}

.modal-backdrop.fade {
  opacity: 0;
}

.modal-backdrop.show {
  opacity: 0.5;
}

.modal-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  padding: 1rem 1rem;
  border-bottom: 1px solid var(--border-color);
  border-top-left-radius: calc(0.3rem - 1px);
  border-top-right-radius: calc(0.3rem - 1px);
}

.modal-header .close {
  padding: 1rem 1rem;
  margin: -1rem -1rem -1rem auto;
}

.modal-title {
  margin-bottom: 0;
  line-height: 1.5;
}

.modal-body {
  position: relative;
  flex: 1 1 auto;
  padding: 1rem;
}

.modal-footer {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: flex-end;
  padding: 0.75rem;
  border-top: 1px solid var(--border-color);
  border-bottom-right-radius: calc(0.3rem - 1px);
  border-bottom-left-radius: calc(0.3rem - 1px);
}

.modal-footer > * {
  margin: 0.25rem;
}

.modal-scrollbar-measure {
  position: absolute;
  top: -9999px;
  width: 50px;
  height: 50px;
  overflow: scroll;
}

@media (min-width: 576px) {
  .modal-dialog {
    max-width: 500px;
    margin: 1.75rem auto;
  }
  .modal-dialog-scrollable {
    max-height: calc(100% - 3.5rem);
  }
  .modal-dialog-scrollable .modal-content {
    max-height: calc(100vh - 3.5rem);
  }
  .modal-dialog-centered {
    min-height: calc(100% - 3.5rem);
  }
  .modal-dialog-centered::before {
    height: calc(100vh - 3.5rem);
    height: -webkit-min-content;
    height: -moz-min-content;
    height: min-content;
  }
  .modal-sm {
    max-width: 300px;
  }
}

@media (min-width: 992px) {
  .modal-lg,
  .modal-xl {
    max-width: 800px;
  }
}

@media (min-width: 1200px) {
  .modal-xl {
    max-width: 1140px;
  }
}

.tooltip {
  position: absolute;
  z-index: 1070;
  display: block;
  margin: 0;
  font-family: 'Circular Std', sans-serif;
  font-style: normal;
  font-weight: 400;
  line-height: 1.5;
  text-align: left;
  text-align: start;
  text-decoration: none;
  text-shadow: none;
  text-transform: none;
  letter-spacing: normal;
  word-break: normal;
  word-spacing: normal;
  white-space: normal;
  line-break: auto;
  font-size: 0.875rem;
  word-wrap: break-word;
  opacity: 0;
}

.tooltip.show {
  opacity: 0.9;
}

.tooltip .arrow {
  position: absolute;
  display: block;
  width: 0.8rem;
  height: 0.4rem;
}

.tooltip .arrow::before {
  position: absolute;
  content: '';
  border-color: transparent;
  border-style: solid;
}

.bs-tooltip-top,
.bs-tooltip-auto[x-placement^='top'] {
  padding: 0.4rem 0;
}

.bs-tooltip-top .arrow,
.bs-tooltip-auto[x-placement^='top'] .arrow {
  bottom: 0;
}

.bs-tooltip-top .arrow::before,
.bs-tooltip-auto[x-placement^='top'] .arrow::before {
  top: 0;
  border-width: 0.4rem 0.4rem 0;
  border-top-color: #000;
}

.bs-tooltip-right,
.bs-tooltip-auto[x-placement^='right'] {
  padding: 0 0.4rem;
}

.bs-tooltip-right .arrow,
.bs-tooltip-auto[x-placement^='right'] .arrow {
  left: 0;
  width: 0.4rem;
  height: 0.8rem;
}

.bs-tooltip-right .arrow::before,
.bs-tooltip-auto[x-placement^='right'] .arrow::before {
  right: 0;
  border-width: 0.4rem 0.4rem 0.4rem 0;
  border-right-color: #000;
}

.bs-tooltip-bottom,
.bs-tooltip-auto[x-placement^='bottom'] {
  padding: 0.4rem 0;
}

.bs-tooltip-bottom .arrow,
.bs-tooltip-auto[x-placement^='bottom'] .arrow {
  top: 0;
}

.bs-tooltip-bottom .arrow::before,
.bs-tooltip-auto[x-placement^='bottom'] .arrow::before {
  bottom: 0;
  border-width: 0 0.4rem 0.4rem;
  border-bottom-color: #000;
}

.bs-tooltip-left,
.bs-tooltip-auto[x-placement^='left'] {
  padding: 0 0.4rem;
}

.bs-tooltip-left .arrow,
.bs-tooltip-auto[x-placement^='left'] .arrow {
  right: 0;
  width: 0.4rem;
  height: 0.8rem;
}

.bs-tooltip-left .arrow::before,
.bs-tooltip-auto[x-placement^='left'] .arrow::before {
  left: 0;
  border-width: 0.4rem 0 0.4rem 0.4rem;
  border-left-color: #000;
}

.tooltip-inner {
  max-width: 200px;
  padding: 0.25rem 0.5rem;
  color: #fff;
  text-align: center;
  background-color: #000;
  border-radius: 0.25rem;
}

.popover {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1060;
  display: block;
  max-width: 276px;
  font-family: 'Circular Std', sans-serif;
  font-style: normal;
  font-weight: 400;
  line-height: 1.5;
  text-align: left;
  text-align: start;
  text-decoration: none;
  text-shadow: none;
  text-transform: none;
  letter-spacing: normal;
  word-break: normal;
  word-spacing: normal;
  white-space: normal;
  line-break: auto;
  font-size: 0.875rem;
  word-wrap: break-word;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid rgba(0, 0, 0, 0.2);
  border-radius: 0.3rem;
}

.popover .arrow {
  position: absolute;
  display: block;
  width: 1rem;
  height: 0.5rem;
  margin: 0 0.3rem;
}

.popover .arrow::before,
.popover .arrow::after {
  position: absolute;
  display: block;
  content: '';
  border-color: transparent;
  border-style: solid;
}

.bs-popover-top,
.bs-popover-auto[x-placement^='top'] {
  margin-bottom: 0.5rem;
}

.bs-popover-top > .arrow,
.bs-popover-auto[x-placement^='top'] > .arrow {
  bottom: calc(-0.5rem - 1px);
}

.bs-popover-top > .arrow::before,
.bs-popover-auto[x-placement^='top'] > .arrow::before {
  bottom: 0;
  border-width: 0.5rem 0.5rem 0;
  border-top-color: rgba(0, 0, 0, 0.25);
}

.bs-popover-top > .arrow::after,
.bs-popover-auto[x-placement^='top'] > .arrow::after {
  bottom: 1px;
  border-width: 0.5rem 0.5rem 0;
  border-top-color: #fff;
}

.bs-popover-right,
.bs-popover-auto[x-placement^='right'] {
  margin-left: 0.5rem;
}

.bs-popover-right > .arrow,
.bs-popover-auto[x-placement^='right'] > .arrow {
  left: calc(-0.5rem - 1px);
  width: 0.5rem;
  height: 1rem;
  margin: 0.3rem 0;
}

.bs-popover-right > .arrow::before,
.bs-popover-auto[x-placement^='right'] > .arrow::before {
  left: 0;
  border-width: 0.5rem 0.5rem 0.5rem 0;
  border-right-color: rgba(0, 0, 0, 0.25);
}

.bs-popover-right > .arrow::after,
.bs-popover-auto[x-placement^='right'] > .arrow::after {
  left: 1px;
  border-width: 0.5rem 0.5rem 0.5rem 0;
  border-right-color: #fff;
}

.bs-popover-bottom,
.bs-popover-auto[x-placement^='bottom'] {
  margin-top: 0.5rem;
}

.bs-popover-bottom > .arrow,
.bs-popover-auto[x-placement^='bottom'] > .arrow {
  top: calc(-0.5rem - 1px);
}

.bs-popover-bottom > .arrow::before,
.bs-popover-auto[x-placement^='bottom'] > .arrow::before {
  top: 0;
  border-width: 0 0.5rem 0.5rem 0.5rem;
  border-bottom-color: rgba(0, 0, 0, 0.25);
}

.bs-popover-bottom > .arrow::after,
.bs-popover-auto[x-placement^='bottom'] > .arrow::after {
  top: 1px;
  border-width: 0 0.5rem 0.5rem 0.5rem;
  border-bottom-color: #fff;
}

.bs-popover-bottom .popover-header::before,
.bs-popover-auto[x-placement^='bottom'] .popover-header::before {
  position: absolute;
  top: 0;
  left: 50%;
  display: block;
  width: 1rem;
  margin-left: -0.5rem;
  content: '';
  border-bottom: 1px solid #f7f7f7;
}

.bs-popover-left,
.bs-popover-auto[x-placement^='left'] {
  margin-right: 0.5rem;
}

.bs-popover-left > .arrow,
.bs-popover-auto[x-placement^='left'] > .arrow {
  right: calc(-0.5rem - 1px);
  width: 0.5rem;
  height: 1rem;
  margin: 0.3rem 0;
}

.bs-popover-left > .arrow::before,
.bs-popover-auto[x-placement^='left'] > .arrow::before {
  right: 0;
  border-width: 0.5rem 0 0.5rem 0.5rem;
  border-left-color: rgba(0, 0, 0, 0.25);
}

.bs-popover-left > .arrow::after,
.bs-popover-auto[x-placement^='left'] > .arrow::after {
  right: 1px;
  border-width: 0.5rem 0 0.5rem 0.5rem;
  border-left-color: #fff;
}

.popover-header {
  padding: 0.5rem 0.75rem;
  margin-bottom: 0;
  font-size: 1rem;
  color: var(--color-headings);
  background-color: #f7f7f7;
  border-bottom: 1px solid #ebebeb;
  border-top-left-radius: calc(0.3rem - 1px);
  border-top-right-radius: calc(0.3rem - 1px);
}

.popover-header:empty {
  display: none;
}

.popover-body {
  padding: 0.5rem 0.75rem;
  color: var(--color-texts-opacity);
}

.carousel {
  position: relative;
}

.carousel.pointer-event {
  touch-action: pan-y;
}

.carousel-inner {
  position: relative;
  width: 100%;
  overflow: hidden;
}

.carousel-inner::after {
  display: block;
  clear: both;
  content: '';
}

.carousel-item {
  position: relative;
  display: none;
  float: left;
  width: 100%;
  margin-right: -100%;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  transition: transform 0.6s ease-in-out;
}

@media (prefers-reduced-motion: reduce) {
  .carousel-item {
    transition: none;
  }
}

.carousel-item.active,
.carousel-item-next,
.carousel-item-prev {
  display: block;
}

.carousel-item-next:not(.carousel-item-left),
.active.carousel-item-right {
  transform: translateX(100%);
}

.carousel-item-prev:not(.carousel-item-right),
.active.carousel-item-left {
  transform: translateX(-100%);
}

.carousel-fade .carousel-item {
  opacity: 0;
  transition-property: opacity;
  transform: none;
}

.carousel-fade .carousel-item.active,
.carousel-fade .carousel-item-next.carousel-item-left,
.carousel-fade .carousel-item-prev.carousel-item-right {
  z-index: 1;
  opacity: 1;
}

.carousel-fade .active.carousel-item-left,
.carousel-fade .active.carousel-item-right {
  z-index: 0;
  opacity: 0;
  transition: opacity 0s 0.6s;
}

@media (prefers-reduced-motion: reduce) {
  .carousel-fade .active.carousel-item-left,
  .carousel-fade .active.carousel-item-right {
    transition: none;
  }
}

.carousel-control-prev,
.carousel-control-next {
  position: absolute;
  top: 0;
  bottom: 0;
  z-index: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 15%;
  color: #fff;
  text-align: center;
  opacity: 0.5;
  transition: opacity 0.15s ease;
}

@media (prefers-reduced-motion: reduce) {
  .carousel-control-prev,
  .carousel-control-next {
    transition: none;
  }
}

.carousel-control-prev:hover,
.carousel-control-prev:focus,
.carousel-control-next:hover,
.carousel-control-next:focus {
  color: #fff;
  text-decoration: none;
  outline: 0;
  opacity: 0.9;
}

.carousel-control-prev {
  left: 0;
}

.carousel-control-next {
  right: 0;
}

.carousel-control-prev-icon,
.carousel-control-next-icon {
  display: inline-block;
  width: 20px;
  height: 20px;
  background: no-repeat 50% / 100% 100%;
}

.carousel-control-prev-icon {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='%23fff' width='8' height='8' viewBox='0 0 8 8'%3e%3cpath d='M5.25 0l-4 4 4 4 1.5-1.5L4.25 4l2.5-2.5L5.25 0z'/%3e%3c/svg%3e");
}

.carousel-control-next-icon {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='%23fff' width='8' height='8' viewBox='0 0 8 8'%3e%3cpath d='M2.75 0l-1.5 1.5L3.75 4l-2.5 2.5L2.75 8l4-4-4-4z'/%3e%3c/svg%3e");
}

.carousel-indicators {
  position: absolute;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 15;
  display: flex;
  justify-content: center;
  padding-left: 0;
  margin-right: 15%;
  margin-left: 15%;
  list-style: none;
}

.carousel-indicators li {
  box-sizing: content-box;
  flex: 0 1 auto;
  width: 30px;
  height: 3px;
  margin-right: 3px;
  margin-left: 3px;
  text-indent: -999px;
  cursor: pointer;
  background-color: #fff;
  background-clip: padding-box;
  border-top: 10px solid transparent;
  border-bottom: 10px solid transparent;
  opacity: 0.5;
  transition: opacity 0.6s ease;
}

@media (prefers-reduced-motion: reduce) {
  .carousel-indicators li {
    transition: none;
  }
}

.carousel-indicators .active {
  opacity: 1;
}

.carousel-caption {
  position: absolute;
  right: 15%;
  bottom: 20px;
  left: 15%;
  z-index: 10;
  padding-top: 20px;
  padding-bottom: 20px;
  color: #fff;
  text-align: center;
}

@-webkit-keyframes spinner-border {
  to {
    transform: rotate(360deg);
  }
}

@keyframes spinner-border {
  to {
    transform: rotate(360deg);
  }
}

.spinner-border {
  display: inline-block;
  width: 2rem;
  height: 2rem;
  vertical-align: text-bottom;
  border: 0.25em solid currentColor;
  border-right-color: transparent;
  border-radius: 50%;
  -webkit-animation: spinner-border 0.75s linear infinite;
  animation: spinner-border 0.75s linear infinite;
}

.spinner-border-sm {
  width: 1rem;
  height: 1rem;
  border-width: 0.2em;
}

@-webkit-keyframes spinner-grow {
  0% {
    transform: scale(0);
  }
  50% {
    opacity: 1;
    transform: none;
  }
}

@keyframes spinner-grow {
  0% {
    transform: scale(0);
  }
  50% {
    opacity: 1;
    transform: none;
  }
}

.spinner-grow {
  display: inline-block;
  width: 2rem;
  height: 2rem;
  vertical-align: text-bottom;
  background-color: currentColor;
  border-radius: 50%;
  opacity: 0;
  -webkit-animation: spinner-grow 0.75s linear infinite;
  animation: spinner-grow 0.75s linear infinite;
}

.spinner-grow-sm {
  width: 1rem;
  height: 1rem;
}

.align-baseline {
  vertical-align: baseline !important;
}

.align-top {
  vertical-align: top !important;
}

.align-middle {
  vertical-align: middle !important;
}

.align-bottom {
  vertical-align: bottom !important;
}

.align-text-bottom {
  vertical-align: text-bottom !important;
}

.align-text-top {
  vertical-align: text-top !important;
}

.bg-primary {
  background-color: #473bf0 !important;
}

a.bg-primary:hover,
a.bg-primary:focus,
button.bg-primary:hover,
button.bg-primary:focus {
  background-color: #2012e6 !important;
}

.bg-secondary {
  background-color: #68d585 !important;
}

a.bg-secondary:hover,
a.bg-secondary:focus,
button.bg-secondary:hover,
button.bg-secondary:focus {
  background-color: #40ca65 !important;
}

.bg-success {
  background-color: #68d585 !important;
}

a.bg-success:hover,
a.bg-success:focus,
button.bg-success:hover,
button.bg-success:focus {
  background-color: #40ca65 !important;
}

.bg-info {
  background-color: #17a2b8 !important;
}

a.bg-info:hover,
a.bg-info:focus,
button.bg-info:hover,
button.bg-info:focus {
  background-color: #117a8b !important;
}

.bg-warning {
  background-color: #f7e36d !important;
}

a.bg-warning:hover,
a.bg-warning:focus,
button.bg-warning:hover,
button.bg-warning:focus {
  background-color: #f4da3d !important;
}

.bg-danger {
  background-color: #f64b4b !important;
}

a.bg-danger:hover,
a.bg-danger:focus,
button.bg-danger:hover,
button.bg-danger:focus {
  background-color: #f41a1a !important;
}

.bg-light {
  background-color: #f8f9fa !important;
}

a.bg-light:hover,
a.bg-light:focus,
button.bg-light:hover,
button.bg-light:focus {
  background-color: #dae0e5 !important;
}

.bg-dark {
  background-color: #343a40 !important;
}

a.bg-dark:hover,
a.bg-dark:focus,
button.bg-dark:hover,
button.bg-dark:focus {
  background-color: #1d2124 !important;
}

.bg-red {
  background-color: #f64b4b !important;
}

a.bg-red:hover,
a.bg-red:focus,
button.bg-red:hover,
button.bg-red:focus {
  background-color: #f41a1a !important;
}

.bg-green {
  background-color: #68d585 !important;
}

a.bg-green:hover,
a.bg-green:focus,
button.bg-green:hover,
button.bg-green:focus {
  background-color: #40ca65 !important;
}

.bg-green-shamrock {
  background-color: #2bd67b !important;
}

a.bg-green-shamrock:hover,
a.bg-green-shamrock:focus,
button.bg-green-shamrock:hover,
button.bg-green-shamrock:focus {
  background-color: #21ad63 !important;
}

.bg-blue {
  background-color: #473bf0 !important;
}

a.bg-blue:hover,
a.bg-blue:focus,
button.bg-blue:hover,
button.bg-blue:focus {
  background-color: #2012e6 !important;
}

.bg-sky-blue {
  background-color: #1082e9 !important;
}

a.bg-sky-blue:hover,
a.bg-sky-blue:focus,
button.bg-sky-blue:hover,
button.bg-sky-blue:focus {
  background-color: #0d67b9 !important;
}

.bg-yellow {
  background-color: #f7e36d !important;
}

a.bg-yellow:hover,
a.bg-yellow:focus,
button.bg-yellow:hover,
button.bg-yellow:focus {
  background-color: #f4da3d !important;
}

.bg-yellow-orange {
  background-color: #fcad38 !important;
}

a.bg-yellow-orange:hover,
a.bg-yellow-orange:focus,
button.bg-yellow-orange:hover,
button.bg-yellow-orange:focus {
  background-color: #fb9806 !important;
}

.bg-blackish-blue {
  background-color: #13151c !important;
}

a.bg-blackish-blue:hover,
a.bg-blackish-blue:focus,
button.bg-blackish-blue:hover,
button.bg-blackish-blue:focus {
  background-color: black !important;
}

.bg-black {
  background-color: #000 !important;
}

a.bg-black:hover,
a.bg-black:focus,
button.bg-black:hover,
button.bg-black:focus {
  background-color: black !important;
}

.bg-mirage {
  background-color: #131829 !important;
}

a.bg-mirage:hover,
a.bg-mirage:focus,
button.bg-mirage:hover,
button.bg-mirage:focus {
  background-color: #030406 !important;
}

.bg-mirage-2 {
  background-color: #161c2d !important;
}

a.bg-mirage-2:hover,
a.bg-mirage-2:focus,
button.bg-mirage-2:hover,
button.bg-mirage-2:focus {
  background-color: #05070b !important;
}

.bg-white {
  background-color: #fff !important;
}

a.bg-white:hover,
a.bg-white:focus,
button.bg-white:hover,
button.bg-white:focus {
  background-color: #e6e6e6 !important;
}

.bg-smoke {
  background-color: #f8f8f8 !important;
}

a.bg-smoke:hover,
a.bg-smoke:focus,
button.bg-smoke:hover,
button.bg-smoke:focus {
  background-color: #dfdfdf !important;
}

.bg-storm {
  background-color: #7d818d !important;
}

a.bg-storm:hover,
a.bg-storm:focus,
button.bg-storm:hover,
button.bg-storm:focus {
  background-color: #646873 !important;
}

.bg-ghost {
  background-color: #fdfdff !important;
}

a.bg-ghost:hover,
a.bg-ghost:focus,
button.bg-ghost:hover,
button.bg-ghost:focus {
  background-color: #cacaff !important;
}

.bg-gray-1 {
  background-color: #fcfdfe !important;
}

a.bg-gray-1:hover,
a.bg-gray-1:focus,
button.bg-gray-1:hover,
button.bg-gray-1:focus {
  background-color: #d6e4f1 !important;
}

.bg-gray-2 {
  background-color: #f4f7fa !important;
}

a.bg-gray-2:hover,
a.bg-gray-2:focus,
button.bg-gray-2:hover,
button.bg-gray-2:focus {
  background-color: #d1deea !important;
}

.bg-gray-3 {
  background-color: #e7e9ed !important;
}

a.bg-gray-3:hover,
a.bg-gray-3:focus,
button.bg-gray-3:hover,
button.bg-gray-3:focus {
  background-color: #caced7 !important;
}

.bg-gray-310 {
  background-color: #d5d7dd !important;
}

a.bg-gray-310:hover,
a.bg-gray-310:focus,
button.bg-gray-310:hover,
button.bg-gray-310:focus {
  background-color: #b9bcc6 !important;
}

.bg-gray-opacity {
  background-color: rgba(231, 233, 237, 0.13) !important;
}

a.bg-gray-opacity:hover,
a.bg-gray-opacity:focus,
button.bg-gray-opacity:hover,
button.bg-gray-opacity:focus {
  background-color: rgba(202, 206, 215, 0.13) !important;
}

.bg-blackish-blue-opacity {
  background-color: rgba(22, 28, 45, 0.7) !important;
}

a.bg-blackish-blue-opacity:hover,
a.bg-blackish-blue-opacity:focus,
button.bg-blackish-blue-opacity:hover,
button.bg-blackish-blue-opacity:focus {
  background-color: rgba(5, 7, 11, 0.7) !important;
}

.bg-narvik {
  background-color: #edf9f2 !important;
}

a.bg-narvik:hover,
a.bg-narvik:focus,
button.bg-narvik:hover,
button.bg-narvik:focus {
  background-color: #c7ecd6 !important;
}

.bg-white {
  background-color: #fff !important;
}

.bg-transparent {
  background-color: transparent !important;
}

.border {
  border: 1px solid var(--border-color) !important;
}

.border-top {
  border-top: 1px solid var(--border-color) !important;
}

.border-right {
  border-right: 1px solid var(--border-color) !important;
}

.border-bottom {
  border-bottom: 1px solid var(--border-color) !important;
}

.border-left {
  border-left: 1px solid var(--border-color) !important;
}

.border-0 {
  border: 0 !important;
}

.border-top-0 {
  border-top: 0 !important;
}

.border-right-0 {
  border-right: 0 !important;
}

.border-bottom-0 {
  border-bottom: 0 !important;
}

.border-left-0 {
  border-left: 0 !important;
}

.border-primary {
  border-color: #473bf0 !important;
}

.border-secondary {
  border-color: #68d585 !important;
}

.border-success {
  border-color: #68d585 !important;
}

.border-info {
  border-color: #17a2b8 !important;
}

.border-warning {
  border-color: #f7e36d !important;
}

.border-danger {
  border-color: #f64b4b !important;
}

.border-light {
  border-color: #f8f9fa !important;
}

.border-dark {
  border-color: #343a40 !important;
}

.border-red {
  border-color: #f64b4b !important;
}

.border-green {
  border-color: #68d585 !important;
}

.border-green-shamrock {
  border-color: #2bd67b !important;
}

.border-blue {
  border-color: #473bf0 !important;
}

.border-sky-blue {
  border-color: #1082e9 !important;
}

.border-yellow {
  border-color: #f7e36d !important;
}

.border-yellow-orange {
  border-color: #fcad38 !important;
}

.border-blackish-blue {
  border-color: #13151c !important;
}

.border-black {
  border-color: #000 !important;
}

.border-mirage {
  border-color: #131829 !important;
}

.border-mirage-2 {
  border-color: #161c2d !important;
}

.border-white {
  border-color: #fff !important;
}

.border-smoke {
  border-color: #f8f8f8 !important;
}

.border-storm {
  border-color: #7d818d !important;
}

.border-ghost {
  border-color: #fdfdff !important;
}

.border-gray-1 {
  border-color: #fcfdfe !important;
}

.border-gray-2 {
  border-color: #f4f7fa !important;
}

.border-gray-3 {
  border-color: #e7e9ed !important;
}

.border-gray-310 {
  border-color: #d5d7dd !important;
}

.border-gray-opacity {
  border-color: rgba(231, 233, 237, 0.13) !important;
}

.border-blackish-blue-opacity {
  border-color: rgba(22, 28, 45, 0.7) !important;
}

.border-narvik {
  border-color: #edf9f2 !important;
}

.border-white {
  border-color: #fff !important;
}

.rounded-sm {
  border-radius: 0.2rem !important;
}

.rounded {
  border-radius: 0.25rem !important;
}

.rounded-top {
  border-top-left-radius: 0.25rem !important;
  border-top-right-radius: 0.25rem !important;
}

.rounded-right {
  border-top-right-radius: 0.25rem !important;
  border-bottom-right-radius: 0.25rem !important;
}

.rounded-bottom {
  border-bottom-right-radius: 0.25rem !important;
  border-bottom-left-radius: 0.25rem !important;
}

.rounded-left {
  border-top-left-radius: 0.25rem !important;
  border-bottom-left-radius: 0.25rem !important;
}

.rounded-lg {
  border-radius: 0.3rem !important;
}

.rounded-circle {
  border-radius: 50% !important;
}

.rounded-pill {
  border-radius: 50rem !important;
}

.rounded-0 {
  border-radius: 0 !important;
}

.clearfix::after {
  display: block;
  clear: both;
  content: '';
}

.d-none {
  display: none !important;
}

.d-inline {
  display: inline !important;
}

.d-inline-block {
  display: inline-block !important;
}

.d-block {
  display: block !important;
}

.d-table {
  display: table !important;
}

.d-table-row {
  display: table-row !important;
}

.d-table-cell {
  display: table-cell !important;
}

.d-flex {
  display: flex !important;
}

.d-inline-flex {
  display: inline-flex !important;
}

@media (min-width: 480px) {
  .d-xs-none {
    display: none !important;
  }
  .d-xs-inline {
    display: inline !important;
  }
  .d-xs-inline-block {
    display: inline-block !important;
  }
  .d-xs-block {
    display: block !important;
  }
  .d-xs-table {
    display: table !important;
  }
  .d-xs-table-row {
    display: table-row !important;
  }
  .d-xs-table-cell {
    display: table-cell !important;
  }
  .d-xs-flex {
    display: flex !important;
  }
  .d-xs-inline-flex {
    display: inline-flex !important;
  }
}

@media (min-width: 576px) {
  .d-sm-none {
    display: none !important;
  }
  .d-sm-inline {
    display: inline !important;
  }
  .d-sm-inline-block {
    display: inline-block !important;
  }
  .d-sm-block {
    display: block !important;
  }
  .d-sm-table {
    display: table !important;
  }
  .d-sm-table-row {
    display: table-row !important;
  }
  .d-sm-table-cell {
    display: table-cell !important;
  }
  .d-sm-flex {
    display: flex !important;
  }
  .d-sm-inline-flex {
    display: inline-flex !important;
  }
}

@media (min-width: 768px) {
  .d-md-none {
    display: none !important;
  }
  .d-md-inline {
    display: inline !important;
  }
  .d-md-inline-block {
    display: inline-block !important;
  }
  .d-md-block {
    display: block !important;
  }
  .d-md-table {
    display: table !important;
  }
  .d-md-table-row {
    display: table-row !important;
  }
  .d-md-table-cell {
    display: table-cell !important;
  }
  .d-md-flex {
    display: flex !important;
  }
  .d-md-inline-flex {
    display: inline-flex !important;
  }
}

@media (min-width: 992px) {
  .d-lg-none {
    display: none !important;
  }
  .d-lg-inline {
    display: inline !important;
  }
  .d-lg-inline-block {
    display: inline-block !important;
  }
  .d-lg-block {
    display: block !important;
  }
  .d-lg-table {
    display: table !important;
  }
  .d-lg-table-row {
    display: table-row !important;
  }
  .d-lg-table-cell {
    display: table-cell !important;
  }
  .d-lg-flex {
    display: flex !important;
  }
  .d-lg-inline-flex {
    display: inline-flex !important;
  }
}

@media (min-width: 1200px) {
  .d-xl-none {
    display: none !important;
  }
  .d-xl-inline {
    display: inline !important;
  }
  .d-xl-inline-block {
    display: inline-block !important;
  }
  .d-xl-block {
    display: block !important;
  }
  .d-xl-table {
    display: table !important;
  }
  .d-xl-table-row {
    display: table-row !important;
  }
  .d-xl-table-cell {
    display: table-cell !important;
  }
  .d-xl-flex {
    display: flex !important;
  }
  .d-xl-inline-flex {
    display: inline-flex !important;
  }
}

@media print {
  .d-print-none {
    display: none !important;
  }
  .d-print-inline {
    display: inline !important;
  }
  .d-print-inline-block {
    display: inline-block !important;
  }
  .d-print-block {
    display: block !important;
  }
  .d-print-table {
    display: table !important;
  }
  .d-print-table-row {
    display: table-row !important;
  }
  .d-print-table-cell {
    display: table-cell !important;
  }
  .d-print-flex {
    display: flex !important;
  }
  .d-print-inline-flex {
    display: inline-flex !important;
  }
}

.embed-responsive {
  position: relative;
  display: block;
  width: 100%;
  padding: 0;
  overflow: hidden;
}

.embed-responsive::before {
  display: block;
  content: '';
}

.embed-responsive .embed-responsive-item,
.embed-responsive iframe,
.embed-responsive embed,
.embed-responsive object,
.embed-responsive video {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 0;
}

.embed-responsive-21by9::before {
  padding-top: 42.85714%;
}

.embed-responsive-16by9::before {
  padding-top: 56.25%;
}

.embed-responsive-4by3::before {
  padding-top: 75%;
}

.embed-responsive-1by1::before {
  padding-top: 100%;
}

.flex-row {
  flex-direction: row !important;
}

.flex-column {
  flex-direction: column !important;
}

.flex-row-reverse {
  flex-direction: row-reverse !important;
}

.flex-column-reverse {
  flex-direction: column-reverse !important;
}

.flex-wrap {
  flex-wrap: wrap !important;
}

.flex-nowrap {
  flex-wrap: nowrap !important;
}

.flex-wrap-reverse {
  flex-wrap: wrap-reverse !important;
}

.flex-fill {
  flex: 1 1 auto !important;
}

.flex-grow-0 {
  flex-grow: 0 !important;
}

.flex-grow-1 {
  flex-grow: 1 !important;
}

.flex-shrink-0 {
  flex-shrink: 0 !important;
}

.flex-shrink-1 {
  flex-shrink: 1 !important;
}

.justify-content-start {
  justify-content: flex-start !important;
}

.justify-content-end {
  justify-content: flex-end !important;
}

.justify-content-center {
  justify-content: center !important;
}

.justify-content-between {
  justify-content: space-between !important;
}

.justify-content-around {
  justify-content: space-around !important;
}

.align-items-start {
  align-items: flex-start !important;
}

.align-items-end {
  align-items: flex-end !important;
}

.align-items-center {
  align-items: center !important;
}

.align-items-baseline {
  align-items: baseline !important;
}

.align-items-stretch {
  align-items: stretch !important;
}

.align-content-start {
  align-content: flex-start !important;
}

.align-content-end {
  align-content: flex-end !important;
}

.align-content-center {
  align-content: center !important;
}

.align-content-between {
  align-content: space-between !important;
}

.align-content-around {
  align-content: space-around !important;
}

.align-content-stretch {
  align-content: stretch !important;
}

.align-self-auto {
  align-self: auto !important;
}

.align-self-start {
  align-self: flex-start !important;
}

.align-self-end {
  align-self: flex-end !important;
}

.align-self-center {
  align-self: center !important;
}

.align-self-baseline {
  align-self: baseline !important;
}

.align-self-stretch {
  align-self: stretch !important;
}

@media (min-width: 480px) {
  .flex-xs-row {
    flex-direction: row !important;
  }
  .flex-xs-column {
    flex-direction: column !important;
  }
  .flex-xs-row-reverse {
    flex-direction: row-reverse !important;
  }
  .flex-xs-column-reverse {
    flex-direction: column-reverse !important;
  }
  .flex-xs-wrap {
    flex-wrap: wrap !important;
  }
  .flex-xs-nowrap {
    flex-wrap: nowrap !important;
  }
  .flex-xs-wrap-reverse {
    flex-wrap: wrap-reverse !important;
  }
  .flex-xs-fill {
    flex: 1 1 auto !important;
  }
  .flex-xs-grow-0 {
    flex-grow: 0 !important;
  }
  .flex-xs-grow-1 {
    flex-grow: 1 !important;
  }
  .flex-xs-shrink-0 {
    flex-shrink: 0 !important;
  }
  .flex-xs-shrink-1 {
    flex-shrink: 1 !important;
  }
  .justify-content-xs-start {
    justify-content: flex-start !important;
  }
  .justify-content-xs-end {
    justify-content: flex-end !important;
  }
  .justify-content-xs-center {
    justify-content: center !important;
  }
  .justify-content-xs-between {
    justify-content: space-between !important;
  }
  .justify-content-xs-around {
    justify-content: space-around !important;
  }
  .align-items-xs-start {
    align-items: flex-start !important;
  }
  .align-items-xs-end {
    align-items: flex-end !important;
  }
  .align-items-xs-center {
    align-items: center !important;
  }
  .align-items-xs-baseline {
    align-items: baseline !important;
  }
  .align-items-xs-stretch {
    align-items: stretch !important;
  }
  .align-content-xs-start {
    align-content: flex-start !important;
  }
  .align-content-xs-end {
    align-content: flex-end !important;
  }
  .align-content-xs-center {
    align-content: center !important;
  }
  .align-content-xs-between {
    align-content: space-between !important;
  }
  .align-content-xs-around {
    align-content: space-around !important;
  }
  .align-content-xs-stretch {
    align-content: stretch !important;
  }
  .align-self-xs-auto {
    align-self: auto !important;
  }
  .align-self-xs-start {
    align-self: flex-start !important;
  }
  .align-self-xs-end {
    align-self: flex-end !important;
  }
  .align-self-xs-center {
    align-self: center !important;
  }
  .align-self-xs-baseline {
    align-self: baseline !important;
  }
  .align-self-xs-stretch {
    align-self: stretch !important;
  }
}

@media (min-width: 576px) {
  .flex-sm-row {
    flex-direction: row !important;
  }
  .flex-sm-column {
    flex-direction: column !important;
  }
  .flex-sm-row-reverse {
    flex-direction: row-reverse !important;
  }
  .flex-sm-column-reverse {
    flex-direction: column-reverse !important;
  }
  .flex-sm-wrap {
    flex-wrap: wrap !important;
  }
  .flex-sm-nowrap {
    flex-wrap: nowrap !important;
  }
  .flex-sm-wrap-reverse {
    flex-wrap: wrap-reverse !important;
  }
  .flex-sm-fill {
    flex: 1 1 auto !important;
  }
  .flex-sm-grow-0 {
    flex-grow: 0 !important;
  }
  .flex-sm-grow-1 {
    flex-grow: 1 !important;
  }
  .flex-sm-shrink-0 {
    flex-shrink: 0 !important;
  }
  .flex-sm-shrink-1 {
    flex-shrink: 1 !important;
  }
  .justify-content-sm-start {
    justify-content: flex-start !important;
  }
  .justify-content-sm-end {
    justify-content: flex-end !important;
  }
  .justify-content-sm-center {
    justify-content: center !important;
  }
  .justify-content-sm-between {
    justify-content: space-between !important;
  }
  .justify-content-sm-around {
    justify-content: space-around !important;
  }
  .align-items-sm-start {
    align-items: flex-start !important;
  }
  .align-items-sm-end {
    align-items: flex-end !important;
  }
  .align-items-sm-center {
    align-items: center !important;
  }
  .align-items-sm-baseline {
    align-items: baseline !important;
  }
  .align-items-sm-stretch {
    align-items: stretch !important;
  }
  .align-content-sm-start {
    align-content: flex-start !important;
  }
  .align-content-sm-end {
    align-content: flex-end !important;
  }
  .align-content-sm-center {
    align-content: center !important;
  }
  .align-content-sm-between {
    align-content: space-between !important;
  }
  .align-content-sm-around {
    align-content: space-around !important;
  }
  .align-content-sm-stretch {
    align-content: stretch !important;
  }
  .align-self-sm-auto {
    align-self: auto !important;
  }
  .align-self-sm-start {
    align-self: flex-start !important;
  }
  .align-self-sm-end {
    align-self: flex-end !important;
  }
  .align-self-sm-center {
    align-self: center !important;
  }
  .align-self-sm-baseline {
    align-self: baseline !important;
  }
  .align-self-sm-stretch {
    align-self: stretch !important;
  }
}

@media (min-width: 768px) {
  .flex-md-row {
    flex-direction: row !important;
  }
  .flex-md-column {
    flex-direction: column !important;
  }
  .flex-md-row-reverse {
    flex-direction: row-reverse !important;
  }
  .flex-md-column-reverse {
    flex-direction: column-reverse !important;
  }
  .flex-md-wrap {
    flex-wrap: wrap !important;
  }
  .flex-md-nowrap {
    flex-wrap: nowrap !important;
  }
  .flex-md-wrap-reverse {
    flex-wrap: wrap-reverse !important;
  }
  .flex-md-fill {
    flex: 1 1 auto !important;
  }
  .flex-md-grow-0 {
    flex-grow: 0 !important;
  }
  .flex-md-grow-1 {
    flex-grow: 1 !important;
  }
  .flex-md-shrink-0 {
    flex-shrink: 0 !important;
  }
  .flex-md-shrink-1 {
    flex-shrink: 1 !important;
  }
  .justify-content-md-start {
    justify-content: flex-start !important;
  }
  .justify-content-md-end {
    justify-content: flex-end !important;
  }
  .justify-content-md-center {
    justify-content: center !important;
  }
  .justify-content-md-between {
    justify-content: space-between !important;
  }
  .justify-content-md-around {
    justify-content: space-around !important;
  }
  .align-items-md-start {
    align-items: flex-start !important;
  }
  .align-items-md-end {
    align-items: flex-end !important;
  }
  .align-items-md-center {
    align-items: center !important;
  }
  .align-items-md-baseline {
    align-items: baseline !important;
  }
  .align-items-md-stretch {
    align-items: stretch !important;
  }
  .align-content-md-start {
    align-content: flex-start !important;
  }
  .align-content-md-end {
    align-content: flex-end !important;
  }
  .align-content-md-center {
    align-content: center !important;
  }
  .align-content-md-between {
    align-content: space-between !important;
  }
  .align-content-md-around {
    align-content: space-around !important;
  }
  .align-content-md-stretch {
    align-content: stretch !important;
  }
  .align-self-md-auto {
    align-self: auto !important;
  }
  .align-self-md-start {
    align-self: flex-start !important;
  }
  .align-self-md-end {
    align-self: flex-end !important;
  }
  .align-self-md-center {
    align-self: center !important;
  }
  .align-self-md-baseline {
    align-self: baseline !important;
  }
  .align-self-md-stretch {
    align-self: stretch !important;
  }
}

@media (min-width: 992px) {
  .flex-lg-row {
    flex-direction: row !important;
  }
  .flex-lg-column {
    flex-direction: column !important;
  }
  .flex-lg-row-reverse {
    flex-direction: row-reverse !important;
  }
  .flex-lg-column-reverse {
    flex-direction: column-reverse !important;
  }
  .flex-lg-wrap {
    flex-wrap: wrap !important;
  }
  .flex-lg-nowrap {
    flex-wrap: nowrap !important;
  }
  .flex-lg-wrap-reverse {
    flex-wrap: wrap-reverse !important;
  }
  .flex-lg-fill {
    flex: 1 1 auto !important;
  }
  .flex-lg-grow-0 {
    flex-grow: 0 !important;
  }
  .flex-lg-grow-1 {
    flex-grow: 1 !important;
  }
  .flex-lg-shrink-0 {
    flex-shrink: 0 !important;
  }
  .flex-lg-shrink-1 {
    flex-shrink: 1 !important;
  }
  .justify-content-lg-start {
    justify-content: flex-start !important;
  }
  .justify-content-lg-end {
    justify-content: flex-end !important;
  }
  .justify-content-lg-center {
    justify-content: center !important;
  }
  .justify-content-lg-between {
    justify-content: space-between !important;
  }
  .justify-content-lg-around {
    justify-content: space-around !important;
  }
  .align-items-lg-start {
    align-items: flex-start !important;
  }
  .align-items-lg-end {
    align-items: flex-end !important;
  }
  .align-items-lg-center {
    align-items: center !important;
  }
  .align-items-lg-baseline {
    align-items: baseline !important;
  }
  .align-items-lg-stretch {
    align-items: stretch !important;
  }
  .align-content-lg-start {
    align-content: flex-start !important;
  }
  .align-content-lg-end {
    align-content: flex-end !important;
  }
  .align-content-lg-center {
    align-content: center !important;
  }
  .align-content-lg-between {
    align-content: space-between !important;
  }
  .align-content-lg-around {
    align-content: space-around !important;
  }
  .align-content-lg-stretch {
    align-content: stretch !important;
  }
  .align-self-lg-auto {
    align-self: auto !important;
  }
  .align-self-lg-start {
    align-self: flex-start !important;
  }
  .align-self-lg-end {
    align-self: flex-end !important;
  }
  .align-self-lg-center {
    align-self: center !important;
  }
  .align-self-lg-baseline {
    align-self: baseline !important;
  }
  .align-self-lg-stretch {
    align-self: stretch !important;
  }
}

@media (min-width: 1200px) {
  .flex-xl-row {
    flex-direction: row !important;
  }
  .flex-xl-column {
    flex-direction: column !important;
  }
  .flex-xl-row-reverse {
    flex-direction: row-reverse !important;
  }
  .flex-xl-column-reverse {
    flex-direction: column-reverse !important;
  }
  .flex-xl-wrap {
    flex-wrap: wrap !important;
  }
  .flex-xl-nowrap {
    flex-wrap: nowrap !important;
  }
  .flex-xl-wrap-reverse {
    flex-wrap: wrap-reverse !important;
  }
  .flex-xl-fill {
    flex: 1 1 auto !important;
  }
  .flex-xl-grow-0 {
    flex-grow: 0 !important;
  }
  .flex-xl-grow-1 {
    flex-grow: 1 !important;
  }
  .flex-xl-shrink-0 {
    flex-shrink: 0 !important;
  }
  .flex-xl-shrink-1 {
    flex-shrink: 1 !important;
  }
  .justify-content-xl-start {
    justify-content: flex-start !important;
  }
  .justify-content-xl-end {
    justify-content: flex-end !important;
  }
  .justify-content-xl-center {
    justify-content: center !important;
  }
  .justify-content-xl-between {
    justify-content: space-between !important;
  }
  .justify-content-xl-around {
    justify-content: space-around !important;
  }
  .align-items-xl-start {
    align-items: flex-start !important;
  }
  .align-items-xl-end {
    align-items: flex-end !important;
  }
  .align-items-xl-center {
    align-items: center !important;
  }
  .align-items-xl-baseline {
    align-items: baseline !important;
  }
  .align-items-xl-stretch {
    align-items: stretch !important;
  }
  .align-content-xl-start {
    align-content: flex-start !important;
  }
  .align-content-xl-end {
    align-content: flex-end !important;
  }
  .align-content-xl-center {
    align-content: center !important;
  }
  .align-content-xl-between {
    align-content: space-between !important;
  }
  .align-content-xl-around {
    align-content: space-around !important;
  }
  .align-content-xl-stretch {
    align-content: stretch !important;
  }
  .align-self-xl-auto {
    align-self: auto !important;
  }
  .align-self-xl-start {
    align-self: flex-start !important;
  }
  .align-self-xl-end {
    align-self: flex-end !important;
  }
  .align-self-xl-center {
    align-self: center !important;
  }
  .align-self-xl-baseline {
    align-self: baseline !important;
  }
  .align-self-xl-stretch {
    align-self: stretch !important;
  }
}

.float-left {
  float: left !important;
}

.float-right {
  float: right !important;
}

.float-none {
  float: none !important;
}

@media (min-width: 480px) {
  .float-xs-left {
    float: left !important;
  }
  .float-xs-right {
    float: right !important;
  }
  .float-xs-none {
    float: none !important;
  }
}

@media (min-width: 576px) {
  .float-sm-left {
    float: left !important;
  }
  .float-sm-right {
    float: right !important;
  }
  .float-sm-none {
    float: none !important;
  }
}

@media (min-width: 768px) {
  .float-md-left {
    float: left !important;
  }
  .float-md-right {
    float: right !important;
  }
  .float-md-none {
    float: none !important;
  }
}

@media (min-width: 992px) {
  .float-lg-left {
    float: left !important;
  }
  .float-lg-right {
    float: right !important;
  }
  .float-lg-none {
    float: none !important;
  }
}

@media (min-width: 1200px) {
  .float-xl-left {
    float: left !important;
  }
  .float-xl-right {
    float: right !important;
  }
  .float-xl-none {
    float: none !important;
  }
}

.user-select-all {
  -webkit-user-select: all !important;
  -moz-user-select: all !important;
  -ms-user-select: all !important;
  user-select: all !important;
}

.user-select-auto {
  -webkit-user-select: auto !important;
  -moz-user-select: auto !important;
  -ms-user-select: auto !important;
  user-select: auto !important;
}

.user-select-none {
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
  user-select: none !important;
}

.overflow-auto {
  overflow: auto !important;
}

.overflow-hidden {
  overflow: hidden !important;
}

.position-static {
  position: static !important;
}

.position-relative {
  position: relative !important;
}

.position-absolute {
  position: absolute !important;
}

.position-fixed {
  position: fixed !important;
}

.position-sticky {
  position: -webkit-sticky !important;
  position: sticky !important;
}

.fixed-top {
  position: fixed;
  top: 0;
  right: 0;
  left: 0;
  z-index: 1030;
}

.fixed-bottom {
  position: fixed;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1030;
}

@supports ((position: -webkit-sticky) or (position: sticky)) {
  .sticky-top {
    position: -webkit-sticky;
    position: sticky;
    top: 0;
    z-index: 1020;
  }
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.sr-only-focusable:active,
.sr-only-focusable:focus {
  position: static;
  width: auto;
  height: auto;
  overflow: visible;
  clip: auto;
  white-space: normal;
}

.shadow-sm {
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important;
}

.shadow {
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
}

.shadow-lg {
  box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175) !important;
}

.shadow-none {
  box-shadow: none !important;
}

.w-25 {
  width: 25% !important;
}

.w-50 {
  width: 50% !important;
}

.w-75 {
  width: 75% !important;
}

.w-100 {
  width: 100% !important;
}

.w-auto {
  width: auto !important;
}

.w-20 {
  width: 20% !important;
}

.w-30 {
  width: 30% !important;
}

.w-35 {
  width: 35% !important;
}

.w-37 {
  width: 37% !important;
}

.w-40 {
  width: 35% !important;
}

.w-55 {
  width: 55% !important;
}

.w-60 {
  width: 60% !important;
}

.w-68 {
  width: 68% !important;
}

.w-70 {
  width: 70% !important;
}

.w-76 {
  width: 76% !important;
}

.w-80 {
  width: 80% !important;
}

.w-85 {
  width: 85% !important;
}

.h-25 {
  height: 25% !important;
}

.h-50 {
  height: 50% !important;
}

.h-75 {
  height: 75% !important;
}

.h-100 {
  height: 100% !important;
}

.h-auto {
  height: auto !important;
}

.h-20 {
  height: 20% !important;
}

.h-30 {
  height: 30% !important;
}

.h-35 {
  height: 35% !important;
}

.h-37 {
  height: 37% !important;
}

.h-40 {
  height: 35% !important;
}

.h-55 {
  height: 55% !important;
}

.h-60 {
  height: 60% !important;
}

.h-68 {
  height: 68% !important;
}

.h-70 {
  height: 70% !important;
}

.h-76 {
  height: 76% !important;
}

.h-80 {
  height: 80% !important;
}

.h-85 {
  height: 85% !important;
}

.mw-100 {
  max-width: 100% !important;
}

.mh-100 {
  max-height: 100% !important;
}

.min-vw-100 {
  min-width: 100vw !important;
}

.min-vh-100 {
  min-height: 100vh !important;
}

.vw-100 {
  width: 100vw !important;
}

.vh-100 {
  height: 100vh !important;
}

.m-0 {
  margin: 0 !important;
}

.mt-0,
.my-0 {
  margin-top: 0 !important;
}

.mr-0,
.mx-0 {
  margin-right: 0 !important;
}

.mb-0,
.my-0 {
  margin-bottom: 0 !important;
}

.ml-0,
.mx-0 {
  margin-left: 0 !important;
}

.m-1 {
  margin: 0.25rem !important;
}

.mt-1,
.my-1 {
  margin-top: 0.25rem !important;
}

.mr-1,
.mx-1 {
  margin-right: 0.25rem !important;
}

.mb-1,
.my-1 {
  margin-bottom: 0.25rem !important;
}

.ml-1,
.mx-1 {
  margin-left: 0.25rem !important;
}

.m-2 {
  margin: 0.375rem !important;
}

.mt-2,
.my-2 {
  margin-top: 0.375rem !important;
}

.mr-2,
.mx-2 {
  margin-right: 0.375rem !important;
}

.mb-2,
.my-2 {
  margin-bottom: 0.375rem !important;
}

.ml-2,
.mx-2 {
  margin-left: 0.375rem !important;
}

.m-3 {
  margin: 0.5rem !important;
}

.mt-3,
.my-3 {
  margin-top: 0.5rem !important;
}

.mr-3,
.mx-3 {
  margin-right: 0.5rem !important;
}

.mb-3,
.my-3 {
  margin-bottom: 0.5rem !important;
}

.ml-3,
.mx-3 {
  margin-left: 0.5rem !important;
}

.m-4 {
  margin: 0.625rem !important;
}

.mt-4,
.my-4 {
  margin-top: 0.625rem !important;
}

.mr-4,
.mx-4 {
  margin-right: 0.625rem !important;
}

.mb-4,
.my-4 {
  margin-bottom: 0.625rem !important;
}

.ml-4,
.mx-4 {
  margin-left: 0.625rem !important;
}

.m-5 {
  margin: 0.75rem !important;
}

.mt-5,
.my-5 {
  margin-top: 0.75rem !important;
}

.mr-5,
.mx-5 {
  margin-right: 0.75rem !important;
}

.mb-5,
.my-5 {
  margin-bottom: 0.75rem !important;
}

.ml-5,
.mx-5 {
  margin-left: 0.75rem !important;
}

.m-6 {
  margin: 1rem !important;
}

.mt-6,
.my-6 {
  margin-top: 1rem !important;
}

.mr-6,
.mx-6 {
  margin-right: 1rem !important;
}

.mb-6,
.my-6 {
  margin-bottom: 1rem !important;
}

.ml-6,
.mx-6 {
  margin-left: 1rem !important;
}

.m-7 {
  margin: 1.25rem !important;
}

.mt-7,
.my-7 {
  margin-top: 1.25rem !important;
}

.mr-7,
.mx-7 {
  margin-right: 1.25rem !important;
}

.mb-7,
.my-7 {
  margin-bottom: 1.25rem !important;
}

.ml-7,
.mx-7 {
  margin-left: 1.25rem !important;
}

.m-8 {
  margin: 1.5625rem !important;
}

.mt-8,
.my-8 {
  margin-top: 1.5625rem !important;
}

.mr-8,
.mx-8 {
  margin-right: 1.5625rem !important;
}

.mb-8,
.my-8 {
  margin-bottom: 1.5625rem !important;
}

.ml-8,
.mx-8 {
  margin-left: 1.5625rem !important;
}

.m-9 {
  margin: 1.875rem !important;
}

.mt-9,
.my-9 {
  margin-top: 1.875rem !important;
}

.mr-9,
.mx-9 {
  margin-right: 1.875rem !important;
}

.mb-9,
.my-9 {
  margin-bottom: 1.875rem !important;
}

.ml-9,
.mx-9 {
  margin-left: 1.875rem !important;
}

.m-10 {
  margin: 2.1875rem !important;
}

.mt-10,
.my-10 {
  margin-top: 2.1875rem !important;
}

.mr-10,
.mx-10 {
  margin-right: 2.1875rem !important;
}

.mb-10,
.my-10 {
  margin-bottom: 2.1875rem !important;
}

.ml-10,
.mx-10 {
  margin-left: 2.1875rem !important;
}

.m-11 {
  margin: 2.5rem !important;
}

.mt-11,
.my-11 {
  margin-top: 2.5rem !important;
}

.mr-11,
.mx-11 {
  margin-right: 2.5rem !important;
}

.mb-11,
.my-11 {
  margin-bottom: 2.5rem !important;
}

.ml-11,
.mx-11 {
  margin-left: 2.5rem !important;
}

.m-12 {
  margin: 2.8125rem !important;
}

.mt-12,
.my-12 {
  margin-top: 2.8125rem !important;
}

.mr-12,
.mx-12 {
  margin-right: 2.8125rem !important;
}

.mb-12,
.my-12 {
  margin-bottom: 2.8125rem !important;
}

.ml-12,
.mx-12 {
  margin-left: 2.8125rem !important;
}

.m-13 {
  margin: 3.125rem !important;
}

.mt-13,
.my-13 {
  margin-top: 3.125rem !important;
}

.mr-13,
.mx-13 {
  margin-right: 3.125rem !important;
}

.mb-13,
.my-13 {
  margin-bottom: 3.125rem !important;
}

.ml-13,
.mx-13 {
  margin-left: 3.125rem !important;
}

.m-14 {
  margin: 3.4375rem !important;
}

.mt-14,
.my-14 {
  margin-top: 3.4375rem !important;
}

.mr-14,
.mx-14 {
  margin-right: 3.4375rem !important;
}

.mb-14,
.my-14 {
  margin-bottom: 3.4375rem !important;
}

.ml-14,
.mx-14 {
  margin-left: 3.4375rem !important;
}

.m-15 {
  margin: 3.75rem !important;
}

.mt-15,
.my-15 {
  margin-top: 3.75rem !important;
}

.mr-15,
.mx-15 {
  margin-right: 3.75rem !important;
}

.mb-15,
.my-15 {
  margin-bottom: 3.75rem !important;
}

.ml-15,
.mx-15 {
  margin-left: 3.75rem !important;
}

.m-16 {
  margin: 4.0625rem !important;
}

.mt-16,
.my-16 {
  margin-top: 4.0625rem !important;
}

.mr-16,
.mx-16 {
  margin-right: 4.0625rem !important;
}

.mb-16,
.my-16 {
  margin-bottom: 4.0625rem !important;
}

.ml-16,
.mx-16 {
  margin-left: 4.0625rem !important;
}

.m-17 {
  margin: 4.375rem !important;
}

.mt-17,
.my-17 {
  margin-top: 4.375rem !important;
}

.mr-17,
.mx-17 {
  margin-right: 4.375rem !important;
}

.mb-17,
.my-17 {
  margin-bottom: 4.375rem !important;
}

.ml-17,
.mx-17 {
  margin-left: 4.375rem !important;
}

.m-18 {
  margin: 4.6875rem !important;
}

.mt-18,
.my-18 {
  margin-top: 4.6875rem !important;
}

.mr-18,
.mx-18 {
  margin-right: 4.6875rem !important;
}

.mb-18,
.my-18 {
  margin-bottom: 4.6875rem !important;
}

.ml-18,
.mx-18 {
  margin-left: 4.6875rem !important;
}

.m-19 {
  margin: 5rem !important;
}

.mt-19,
.my-19 {
  margin-top: 5rem !important;
}

.mr-19,
.mx-19 {
  margin-right: 5rem !important;
}

.mb-19,
.my-19 {
  margin-bottom: 5rem !important;
}

.ml-19,
.mx-19 {
  margin-left: 5rem !important;
}

.m-20 {
  margin: 5.3125rem !important;
}

.mt-20,
.my-20 {
  margin-top: 5.3125rem !important;
}

.mr-20,
.mx-20 {
  margin-right: 5.3125rem !important;
}

.mb-20,
.my-20 {
  margin-bottom: 5.3125rem !important;
}

.ml-20,
.mx-20 {
  margin-left: 5.3125rem !important;
}

.m-21 {
  margin: 5.625rem !important;
}

.mt-21,
.my-21 {
  margin-top: 5.625rem !important;
}

.mr-21,
.mx-21 {
  margin-right: 5.625rem !important;
}

.mb-21,
.my-21 {
  margin-bottom: 5.625rem !important;
}

.ml-21,
.mx-21 {
  margin-left: 5.625rem !important;
}

.m-22 {
  margin: 5.9375rem !important;
}

.mt-22,
.my-22 {
  margin-top: 5.9375rem !important;
}

.mr-22,
.mx-22 {
  margin-right: 5.9375rem !important;
}

.mb-22,
.my-22 {
  margin-bottom: 5.9375rem !important;
}

.ml-22,
.mx-22 {
  margin-left: 5.9375rem !important;
}

.m-23 {
  margin: 6.25rem !important;
}

.mt-23,
.my-23 {
  margin-top: 6.25rem !important;
}

.mr-23,
.mx-23 {
  margin-right: 6.25rem !important;
}

.mb-23,
.my-23 {
  margin-bottom: 6.25rem !important;
}

.ml-23,
.mx-23 {
  margin-left: 6.25rem !important;
}

.m-24 {
  margin: 6.875rem !important;
}

.mt-24,
.my-24 {
  margin-top: 6.875rem !important;
}

.mr-24,
.mx-24 {
  margin-right: 6.875rem !important;
}

.mb-24,
.my-24 {
  margin-bottom: 6.875rem !important;
}

.ml-24,
.mx-24 {
  margin-left: 6.875rem !important;
}

.m-25 {
  margin: 7.5rem !important;
}

.mt-25,
.my-25 {
  margin-top: 7.5rem !important;
}

.mr-25,
.mx-25 {
  margin-right: 7.5rem !important;
}

.mb-25,
.my-25 {
  margin-bottom: 7.5rem !important;
}

.ml-25,
.mx-25 {
  margin-left: 7.5rem !important;
}

.m-26 {
  margin: 8.125rem !important;
}

.mt-26,
.my-26 {
  margin-top: 8.125rem !important;
}

.mr-26,
.mx-26 {
  margin-right: 8.125rem !important;
}

.mb-26,
.my-26 {
  margin-bottom: 8.125rem !important;
}

.ml-26,
.mx-26 {
  margin-left: 8.125rem !important;
}

.m-27 {
  margin: 8.4375rem !important;
}

.mt-27,
.my-27 {
  margin-top: 8.4375rem !important;
}

.mr-27,
.mx-27 {
  margin-right: 8.4375rem !important;
}

.mb-27,
.my-27 {
  margin-bottom: 8.4375rem !important;
}

.ml-27,
.mx-27 {
  margin-left: 8.4375rem !important;
}

.m-28 {
  margin: 9.0625rem !important;
}

.mt-28,
.my-28 {
  margin-top: 9.0625rem !important;
}

.mr-28,
.mx-28 {
  margin-right: 9.0625rem !important;
}

.mb-28,
.my-28 {
  margin-bottom: 9.0625rem !important;
}

.ml-28,
.mx-28 {
  margin-left: 9.0625rem !important;
}

.m-29 {
  margin: 9.375rem !important;
}

.mt-29,
.my-29 {
  margin-top: 9.375rem !important;
}

.mr-29,
.mx-29 {
  margin-right: 9.375rem !important;
}

.mb-29,
.my-29 {
  margin-bottom: 9.375rem !important;
}

.ml-29,
.mx-29 {
  margin-left: 9.375rem !important;
}

.m-30 {
  margin: 9.6875rem !important;
}

.mt-30,
.my-30 {
  margin-top: 9.6875rem !important;
}

.mr-30,
.mx-30 {
  margin-right: 9.6875rem !important;
}

.mb-30,
.my-30 {
  margin-bottom: 9.6875rem !important;
}

.ml-30,
.mx-30 {
  margin-left: 9.6875rem !important;
}

.m-31 {
  margin: 10.625rem !important;
}

.mt-31,
.my-31 {
  margin-top: 10.625rem !important;
}

.mr-31,
.mx-31 {
  margin-right: 10.625rem !important;
}

.mb-31,
.my-31 {
  margin-bottom: 10.625rem !important;
}

.ml-31,
.mx-31 {
  margin-left: 10.625rem !important;
}

.m-32 {
  margin: 11.25rem !important;
}

.mt-32,
.my-32 {
  margin-top: 11.25rem !important;
}

.mr-32,
.mx-32 {
  margin-right: 11.25rem !important;
}

.mb-32,
.my-32 {
  margin-bottom: 11.25rem !important;
}

.ml-32,
.mx-32 {
  margin-left: 11.25rem !important;
}

.m-33 {
  margin: 12.5rem !important;
}

.mt-33,
.my-33 {
  margin-top: 12.5rem !important;
}

.mr-33,
.mx-33 {
  margin-right: 12.5rem !important;
}

.mb-33,
.my-33 {
  margin-bottom: 12.5rem !important;
}

.ml-33,
.mx-33 {
  margin-left: 12.5rem !important;
}

.m-34 {
  margin: 14.0625rem !important;
}

.mt-34,
.my-34 {
  margin-top: 14.0625rem !important;
}

.mr-34,
.mx-34 {
  margin-right: 14.0625rem !important;
}

.mb-34,
.my-34 {
  margin-bottom: 14.0625rem !important;
}

.ml-34,
.mx-34 {
  margin-left: 14.0625rem !important;
}

.m-35 {
  margin: 15.625rem !important;
}

.mt-35,
.my-35 {
  margin-top: 15.625rem !important;
}

.mr-35,
.mx-35 {
  margin-right: 15.625rem !important;
}

.mb-35,
.my-35 {
  margin-bottom: 15.625rem !important;
}

.ml-35,
.mx-35 {
  margin-left: 15.625rem !important;
}

.p-0 {
  padding: 0 !important;
}

.pt-0,
.py-0 {
  padding-top: 0 !important;
}

.pr-0,
.px-0 {
  padding-right: 0 !important;
}

.pb-0,
.py-0 {
  padding-bottom: 0 !important;
}

.pl-0,
.px-0 {
  padding-left: 0 !important;
}

.p-1 {
  padding: 0.25rem !important;
}

.pt-1,
.py-1 {
  padding-top: 0.25rem !important;
}

.pr-1,
.px-1 {
  padding-right: 0.25rem !important;
}

.pb-1,
.py-1 {
  padding-bottom: 0.25rem !important;
}

.pl-1,
.px-1 {
  padding-left: 0.25rem !important;
}

.p-2 {
  padding: 0.375rem !important;
}

.pt-2,
.py-2 {
  padding-top: 0.375rem !important;
}

.pr-2,
.px-2 {
  padding-right: 0.375rem !important;
}

.pb-2,
.py-2 {
  padding-bottom: 0.375rem !important;
}

.pl-2,
.px-2 {
  padding-left: 0.375rem !important;
}

.p-3 {
  padding: 0.5rem !important;
}

.pt-3,
.py-3 {
  padding-top: 0.5rem !important;
}

.pr-3,
.px-3 {
  padding-right: 0.5rem !important;
}

.pb-3,
.py-3 {
  padding-bottom: 0.5rem !important;
}

.pl-3,
.px-3 {
  padding-left: 0.5rem !important;
}

.p-4 {
  padding: 0.625rem !important;
}

.pt-4,
.py-4 {
  padding-top: 0.625rem !important;
}

.pr-4,
.px-4 {
  padding-right: 0.625rem !important;
}

.pb-4,
.py-4 {
  padding-bottom: 0.625rem !important;
}

.pl-4,
.px-4 {
  padding-left: 0.625rem !important;
}

.p-5 {
  padding: 0.75rem !important;
}

.pt-5,
.py-5 {
  padding-top: 0.75rem !important;
}

.pr-5,
.px-5 {
  padding-right: 0.75rem !important;
}

.pb-5,
.py-5 {
  padding-bottom: 0.75rem !important;
}

.pl-5,
.px-5 {
  padding-left: 0.75rem !important;
}

.p-6 {
  padding: 1rem !important;
}

.pt-6,
.py-6 {
  padding-top: 1rem !important;
}

.pr-6,
.px-6 {
  padding-right: 1rem !important;
}

.pb-6,
.py-6 {
  padding-bottom: 1rem !important;
}

.pl-6,
.px-6 {
  padding-left: 1rem !important;
}

.p-7 {
  padding: 1.25rem !important;
}

.pt-7,
.py-7 {
  padding-top: 1.25rem !important;
}

.pr-7,
.px-7 {
  padding-right: 1.25rem !important;
}

.pb-7,
.py-7 {
  padding-bottom: 1.25rem !important;
}

.pl-7,
.px-7 {
  padding-left: 1.25rem !important;
}

.p-8 {
  padding: 1.5625rem !important;
}

.pt-8,
.py-8 {
  padding-top: 1.5625rem !important;
}

.pr-8,
.px-8 {
  padding-right: 1.5625rem !important;
}

.pb-8,
.py-8 {
  padding-bottom: 1.5625rem !important;
}

.pl-8,
.px-8 {
  padding-left: 1.5625rem !important;
}

.p-9 {
  padding: 1.875rem !important;
}

.pt-9,
.py-9 {
  padding-top: 1.875rem !important;
}

.pr-9,
.px-9 {
  padding-right: 1.875rem !important;
}

.pb-9,
.py-9 {
  padding-bottom: 1.875rem !important;
}

.pl-9,
.px-9 {
  padding-left: 1.875rem !important;
}

.p-10 {
  padding: 2.1875rem !important;
}

.pt-10,
.py-10 {
  padding-top: 2.1875rem !important;
}

.pr-10,
.px-10 {
  padding-right: 2.1875rem !important;
}

.pb-10,
.py-10 {
  padding-bottom: 2.1875rem !important;
}

.pl-10,
.px-10 {
  padding-left: 2.1875rem !important;
}

.p-11 {
  padding: 2.5rem !important;
}

.pt-11,
.py-11 {
  padding-top: 2.5rem !important;
}

.pr-11,
.px-11 {
  padding-right: 2.5rem !important;
}

.pb-11,
.py-11 {
  padding-bottom: 2.5rem !important;
}

.pl-11,
.px-11 {
  padding-left: 2.5rem !important;
}

.p-12 {
  padding: 2.8125rem !important;
}

.pt-12,
.py-12 {
  padding-top: 2.8125rem !important;
}

.pr-12,
.px-12 {
  padding-right: 2.8125rem !important;
}

.pb-12,
.py-12 {
  padding-bottom: 2.8125rem !important;
}

.pl-12,
.px-12 {
  padding-left: 2.8125rem !important;
}

.p-13 {
  padding: 3.125rem !important;
}

.pt-13,
.py-13 {
  padding-top: 3.125rem !important;
}

.pr-13,
.px-13 {
  padding-right: 3.125rem !important;
}

.pb-13,
.py-13 {
  padding-bottom: 3.125rem !important;
}

.pl-13,
.px-13 {
  padding-left: 3.125rem !important;
}

.p-14 {
  padding: 3.4375rem !important;
}

.pt-14,
.py-14 {
  padding-top: 3.4375rem !important;
}

.pr-14,
.px-14 {
  padding-right: 3.4375rem !important;
}

.pb-14,
.py-14 {
  padding-bottom: 3.4375rem !important;
}

.pl-14,
.px-14 {
  padding-left: 3.4375rem !important;
}

.p-15 {
  padding: 3.75rem !important;
}

.pt-15,
.py-15 {
  padding-top: 3.75rem !important;
}

.pr-15,
.px-15 {
  padding-right: 3.75rem !important;
}

.pb-15,
.py-15 {
  padding-bottom: 3.75rem !important;
}

.pl-15,
.px-15 {
  padding-left: 3.75rem !important;
}

.p-16 {
  padding: 4.0625rem !important;
}

.pt-16,
.py-16 {
  padding-top: 4.0625rem !important;
}

.pr-16,
.px-16 {
  padding-right: 4.0625rem !important;
}

.pb-16,
.py-16 {
  padding-bottom: 4.0625rem !important;
}

.pl-16,
.px-16 {
  padding-left: 4.0625rem !important;
}

.p-17 {
  padding: 4.375rem !important;
}

.pt-17,
.py-17 {
  padding-top: 4.375rem !important;
}

.pr-17,
.px-17 {
  padding-right: 4.375rem !important;
}

.pb-17,
.py-17 {
  padding-bottom: 4.375rem !important;
}

.pl-17,
.px-17 {
  padding-left: 4.375rem !important;
}

.p-18 {
  padding: 4.6875rem !important;
}

.pt-18,
.py-18 {
  padding-top: 4.6875rem !important;
}

.pr-18,
.px-18 {
  padding-right: 4.6875rem !important;
}

.pb-18,
.py-18 {
  padding-bottom: 4.6875rem !important;
}

.pl-18,
.px-18 {
  padding-left: 4.6875rem !important;
}

.p-19 {
  padding: 5rem !important;
}

.pt-19,
.py-19 {
  padding-top: 5rem !important;
}

.pr-19,
.px-19 {
  padding-right: 5rem !important;
}

.pb-19,
.py-19 {
  padding-bottom: 5rem !important;
}

.pl-19,
.px-19 {
  padding-left: 5rem !important;
}

.p-20 {
  padding: 5.3125rem !important;
}

.pt-20,
.py-20 {
  padding-top: 5.3125rem !important;
}

.pr-20,
.px-20 {
  padding-right: 5.3125rem !important;
}

.pb-20,
.py-20 {
  padding-bottom: 5.3125rem !important;
}

.pl-20,
.px-20 {
  padding-left: 5.3125rem !important;
}

.p-21 {
  padding: 5.625rem !important;
}

.pt-21,
.py-21 {
  padding-top: 5.625rem !important;
}

.pr-21,
.px-21 {
  padding-right: 5.625rem !important;
}

.pb-21,
.py-21 {
  padding-bottom: 5.625rem !important;
}

.pl-21,
.px-21 {
  padding-left: 5.625rem !important;
}

.p-22 {
  padding: 5.9375rem !important;
}

.pt-22,
.py-22 {
  padding-top: 5.9375rem !important;
}

.pr-22,
.px-22 {
  padding-right: 5.9375rem !important;
}

.pb-22,
.py-22 {
  padding-bottom: 5.9375rem !important;
}

.pl-22,
.px-22 {
  padding-left: 5.9375rem !important;
}

.p-23 {
  padding: 6.25rem !important;
}

.pt-23,
.py-23 {
  padding-top: 6.25rem !important;
}

.pr-23,
.px-23 {
  padding-right: 6.25rem !important;
}

.pb-23,
.py-23 {
  padding-bottom: 6.25rem !important;
}

.pl-23,
.px-23 {
  padding-left: 6.25rem !important;
}

.p-24 {
  padding: 6.875rem !important;
}

.pt-24,
.py-24 {
  padding-top: 6.875rem !important;
}

.pr-24,
.px-24 {
  padding-right: 6.875rem !important;
}

.pb-24,
.py-24 {
  padding-bottom: 6.875rem !important;
}

.pl-24,
.px-24 {
  padding-left: 6.875rem !important;
}

.p-25 {
  padding: 7.5rem !important;
}

.pt-25,
.py-25 {
  padding-top: 7.5rem !important;
}

.pr-25,
.px-25 {
  padding-right: 7.5rem !important;
}

.pb-25,
.py-25 {
  padding-bottom: 7.5rem !important;
}

.pl-25,
.px-25 {
  padding-left: 7.5rem !important;
}

.p-26 {
  padding: 8.125rem !important;
}

.pt-26,
.py-26 {
  padding-top: 8.125rem !important;
}

.pr-26,
.px-26 {
  padding-right: 8.125rem !important;
}

.pb-26,
.py-26 {
  padding-bottom: 8.125rem !important;
}

.pl-26,
.px-26 {
  padding-left: 8.125rem !important;
}

.p-27 {
  padding: 8.4375rem !important;
}

.pt-27,
.py-27 {
  padding-top: 8.4375rem !important;
}

.pr-27,
.px-27 {
  padding-right: 8.4375rem !important;
}

.pb-27,
.py-27 {
  padding-bottom: 8.4375rem !important;
}

.pl-27,
.px-27 {
  padding-left: 8.4375rem !important;
}

.p-28 {
  padding: 9.0625rem !important;
}

.pt-28,
.py-28 {
  padding-top: 9.0625rem !important;
}

.pr-28,
.px-28 {
  padding-right: 9.0625rem !important;
}

.pb-28,
.py-28 {
  padding-bottom: 9.0625rem !important;
}

.pl-28,
.px-28 {
  padding-left: 9.0625rem !important;
}

.p-29 {
  padding: 9.375rem !important;
}

.pt-29,
.py-29 {
  padding-top: 9.375rem !important;
}

.pr-29,
.px-29 {
  padding-right: 9.375rem !important;
}

.pb-29,
.py-29 {
  padding-bottom: 9.375rem !important;
}

.pl-29,
.px-29 {
  padding-left: 9.375rem !important;
}

.p-30 {
  padding: 9.6875rem !important;
}

.pt-30,
.py-30 {
  padding-top: 9.6875rem !important;
}

.pr-30,
.px-30 {
  padding-right: 9.6875rem !important;
}

.pb-30,
.py-30 {
  padding-bottom: 9.6875rem !important;
}

.pl-30,
.px-30 {
  padding-left: 9.6875rem !important;
}

.p-31 {
  padding: 10.625rem !important;
}

.pt-31,
.py-31 {
  padding-top: 10.625rem !important;
}

.pr-31,
.px-31 {
  padding-right: 10.625rem !important;
}

.pb-31,
.py-31 {
  padding-bottom: 10.625rem !important;
}

.pl-31,
.px-31 {
  padding-left: 10.625rem !important;
}

.p-32 {
  padding: 11.25rem !important;
}

.pt-32,
.py-32 {
  padding-top: 11.25rem !important;
}

.pr-32,
.px-32 {
  padding-right: 11.25rem !important;
}

.pb-32,
.py-32 {
  padding-bottom: 11.25rem !important;
}

.pl-32,
.px-32 {
  padding-left: 11.25rem !important;
}

.p-33 {
  padding: 12.5rem !important;
}

.pt-33,
.py-33 {
  padding-top: 12.5rem !important;
}

.pr-33,
.px-33 {
  padding-right: 12.5rem !important;
}

.pb-33,
.py-33 {
  padding-bottom: 12.5rem !important;
}

.pl-33,
.px-33 {
  padding-left: 12.5rem !important;
}

.p-34 {
  padding: 14.0625rem !important;
}

.pt-34,
.py-34 {
  padding-top: 14.0625rem !important;
}

.pr-34,
.px-34 {
  padding-right: 14.0625rem !important;
}

.pb-34,
.py-34 {
  padding-bottom: 14.0625rem !important;
}

.pl-34,
.px-34 {
  padding-left: 14.0625rem !important;
}

.p-35 {
  padding: 15.625rem !important;
}

.pt-35,
.py-35 {
  padding-top: 15.625rem !important;
}

.pr-35,
.px-35 {
  padding-right: 15.625rem !important;
}

.pb-35,
.py-35 {
  padding-bottom: 15.625rem !important;
}

.pl-35,
.px-35 {
  padding-left: 15.625rem !important;
}

.m-n1 {
  margin: -0.25rem !important;
}

.mt-n1,
.my-n1 {
  margin-top: -0.25rem !important;
}

.mr-n1,
.mx-n1 {
  margin-right: -0.25rem !important;
}

.mb-n1,
.my-n1 {
  margin-bottom: -0.25rem !important;
}

.ml-n1,
.mx-n1 {
  margin-left: -0.25rem !important;
}

.m-n2 {
  margin: -0.375rem !important;
}

.mt-n2,
.my-n2 {
  margin-top: -0.375rem !important;
}

.mr-n2,
.mx-n2 {
  margin-right: -0.375rem !important;
}

.mb-n2,
.my-n2 {
  margin-bottom: -0.375rem !important;
}

.ml-n2,
.mx-n2 {
  margin-left: -0.375rem !important;
}

.m-n3 {
  margin: -0.5rem !important;
}

.mt-n3,
.my-n3 {
  margin-top: -0.5rem !important;
}

.mr-n3,
.mx-n3 {
  margin-right: -0.5rem !important;
}

.mb-n3,
.my-n3 {
  margin-bottom: -0.5rem !important;
}

.ml-n3,
.mx-n3 {
  margin-left: -0.5rem !important;
}

.m-n4 {
  margin: -0.625rem !important;
}

.mt-n4,
.my-n4 {
  margin-top: -0.625rem !important;
}

.mr-n4,
.mx-n4 {
  margin-right: -0.625rem !important;
}

.mb-n4,
.my-n4 {
  margin-bottom: -0.625rem !important;
}

.ml-n4,
.mx-n4 {
  margin-left: -0.625rem !important;
}

.m-n5 {
  margin: -0.75rem !important;
}

.mt-n5,
.my-n5 {
  margin-top: -0.75rem !important;
}

.mr-n5,
.mx-n5 {
  margin-right: -0.75rem !important;
}

.mb-n5,
.my-n5 {
  margin-bottom: -0.75rem !important;
}

.ml-n5,
.mx-n5 {
  margin-left: -0.75rem !important;
}

.m-n6 {
  margin: -1rem !important;
}

.mt-n6,
.my-n6 {
  margin-top: -1rem !important;
}

.mr-n6,
.mx-n6 {
  margin-right: -1rem !important;
}

.mb-n6,
.my-n6 {
  margin-bottom: -1rem !important;
}

.ml-n6,
.mx-n6 {
  margin-left: -1rem !important;
}

.m-n7 {
  margin: -1.25rem !important;
}

.mt-n7,
.my-n7 {
  margin-top: -1.25rem !important;
}

.mr-n7,
.mx-n7 {
  margin-right: -1.25rem !important;
}

.mb-n7,
.my-n7 {
  margin-bottom: -1.25rem !important;
}

.ml-n7,
.mx-n7 {
  margin-left: -1.25rem !important;
}

.m-n8 {
  margin: -1.5625rem !important;
}

.mt-n8,
.my-n8 {
  margin-top: -1.5625rem !important;
}

.mr-n8,
.mx-n8 {
  margin-right: -1.5625rem !important;
}

.mb-n8,
.my-n8 {
  margin-bottom: -1.5625rem !important;
}

.ml-n8,
.mx-n8 {
  margin-left: -1.5625rem !important;
}

.m-n9 {
  margin: -1.875rem !important;
}

.mt-n9,
.my-n9 {
  margin-top: -1.875rem !important;
}

.mr-n9,
.mx-n9 {
  margin-right: -1.875rem !important;
}

.mb-n9,
.my-n9 {
  margin-bottom: -1.875rem !important;
}

.ml-n9,
.mx-n9 {
  margin-left: -1.875rem !important;
}

.m-n10 {
  margin: -2.1875rem !important;
}

.mt-n10,
.my-n10 {
  margin-top: -2.1875rem !important;
}

.mr-n10,
.mx-n10 {
  margin-right: -2.1875rem !important;
}

.mb-n10,
.my-n10 {
  margin-bottom: -2.1875rem !important;
}

.ml-n10,
.mx-n10 {
  margin-left: -2.1875rem !important;
}

.m-n11 {
  margin: -2.5rem !important;
}

.mt-n11,
.my-n11 {
  margin-top: -2.5rem !important;
}

.mr-n11,
.mx-n11 {
  margin-right: -2.5rem !important;
}

.mb-n11,
.my-n11 {
  margin-bottom: -2.5rem !important;
}

.ml-n11,
.mx-n11 {
  margin-left: -2.5rem !important;
}

.m-n12 {
  margin: -2.8125rem !important;
}

.mt-n12,
.my-n12 {
  margin-top: -2.8125rem !important;
}

.mr-n12,
.mx-n12 {
  margin-right: -2.8125rem !important;
}

.mb-n12,
.my-n12 {
  margin-bottom: -2.8125rem !important;
}

.ml-n12,
.mx-n12 {
  margin-left: -2.8125rem !important;
}

.m-n13 {
  margin: -3.125rem !important;
}

.mt-n13,
.my-n13 {
  margin-top: -3.125rem !important;
}

.mr-n13,
.mx-n13 {
  margin-right: -3.125rem !important;
}

.mb-n13,
.my-n13 {
  margin-bottom: -3.125rem !important;
}

.ml-n13,
.mx-n13 {
  margin-left: -3.125rem !important;
}

.m-n14 {
  margin: -3.4375rem !important;
}

.mt-n14,
.my-n14 {
  margin-top: -3.4375rem !important;
}

.mr-n14,
.mx-n14 {
  margin-right: -3.4375rem !important;
}

.mb-n14,
.my-n14 {
  margin-bottom: -3.4375rem !important;
}

.ml-n14,
.mx-n14 {
  margin-left: -3.4375rem !important;
}

.m-n15 {
  margin: -3.75rem !important;
}

.mt-n15,
.my-n15 {
  margin-top: -3.75rem !important;
}

.mr-n15,
.mx-n15 {
  margin-right: -3.75rem !important;
}

.mb-n15,
.my-n15 {
  margin-bottom: -3.75rem !important;
}

.ml-n15,
.mx-n15 {
  margin-left: -3.75rem !important;
}

.m-n16 {
  margin: -4.0625rem !important;
}

.mt-n16,
.my-n16 {
  margin-top: -4.0625rem !important;
}

.mr-n16,
.mx-n16 {
  margin-right: -4.0625rem !important;
}

.mb-n16,
.my-n16 {
  margin-bottom: -4.0625rem !important;
}

.ml-n16,
.mx-n16 {
  margin-left: -4.0625rem !important;
}

.m-n17 {
  margin: -4.375rem !important;
}

.mt-n17,
.my-n17 {
  margin-top: -4.375rem !important;
}

.mr-n17,
.mx-n17 {
  margin-right: -4.375rem !important;
}

.mb-n17,
.my-n17 {
  margin-bottom: -4.375rem !important;
}

.ml-n17,
.mx-n17 {
  margin-left: -4.375rem !important;
}

.m-n18 {
  margin: -4.6875rem !important;
}

.mt-n18,
.my-n18 {
  margin-top: -4.6875rem !important;
}

.mr-n18,
.mx-n18 {
  margin-right: -4.6875rem !important;
}

.mb-n18,
.my-n18 {
  margin-bottom: -4.6875rem !important;
}

.ml-n18,
.mx-n18 {
  margin-left: -4.6875rem !important;
}

.m-n19 {
  margin: -5rem !important;
}

.mt-n19,
.my-n19 {
  margin-top: -5rem !important;
}

.mr-n19,
.mx-n19 {
  margin-right: -5rem !important;
}

.mb-n19,
.my-n19 {
  margin-bottom: -5rem !important;
}

.ml-n19,
.mx-n19 {
  margin-left: -5rem !important;
}

.m-n20 {
  margin: -5.3125rem !important;
}

.mt-n20,
.my-n20 {
  margin-top: -5.3125rem !important;
}

.mr-n20,
.mx-n20 {
  margin-right: -5.3125rem !important;
}

.mb-n20,
.my-n20 {
  margin-bottom: -5.3125rem !important;
}

.ml-n20,
.mx-n20 {
  margin-left: -5.3125rem !important;
}

.m-n21 {
  margin: -5.625rem !important;
}

.mt-n21,
.my-n21 {
  margin-top: -5.625rem !important;
}

.mr-n21,
.mx-n21 {
  margin-right: -5.625rem !important;
}

.mb-n21,
.my-n21 {
  margin-bottom: -5.625rem !important;
}

.ml-n21,
.mx-n21 {
  margin-left: -5.625rem !important;
}

.m-n22 {
  margin: -5.9375rem !important;
}

.mt-n22,
.my-n22 {
  margin-top: -5.9375rem !important;
}

.mr-n22,
.mx-n22 {
  margin-right: -5.9375rem !important;
}

.mb-n22,
.my-n22 {
  margin-bottom: -5.9375rem !important;
}

.ml-n22,
.mx-n22 {
  margin-left: -5.9375rem !important;
}

.m-n23 {
  margin: -6.25rem !important;
}

.mt-n23,
.my-n23 {
  margin-top: -6.25rem !important;
}

.mr-n23,
.mx-n23 {
  margin-right: -6.25rem !important;
}

.mb-n23,
.my-n23 {
  margin-bottom: -6.25rem !important;
}

.ml-n23,
.mx-n23 {
  margin-left: -6.25rem !important;
}

.m-n24 {
  margin: -6.875rem !important;
}

.mt-n24,
.my-n24 {
  margin-top: -6.875rem !important;
}

.mr-n24,
.mx-n24 {
  margin-right: -6.875rem !important;
}

.mb-n24,
.my-n24 {
  margin-bottom: -6.875rem !important;
}

.ml-n24,
.mx-n24 {
  margin-left: -6.875rem !important;
}

.m-n25 {
  margin: -7.5rem !important;
}

.mt-n25,
.my-n25 {
  margin-top: -7.5rem !important;
}

.mr-n25,
.mx-n25 {
  margin-right: -7.5rem !important;
}

.mb-n25,
.my-n25 {
  margin-bottom: -7.5rem !important;
}

.ml-n25,
.mx-n25 {
  margin-left: -7.5rem !important;
}

.m-n26 {
  margin: -8.125rem !important;
}

.mt-n26,
.my-n26 {
  margin-top: -8.125rem !important;
}

.mr-n26,
.mx-n26 {
  margin-right: -8.125rem !important;
}

.mb-n26,
.my-n26 {
  margin-bottom: -8.125rem !important;
}

.ml-n26,
.mx-n26 {
  margin-left: -8.125rem !important;
}

.m-n27 {
  margin: -8.4375rem !important;
}

.mt-n27,
.my-n27 {
  margin-top: -8.4375rem !important;
}

.mr-n27,
.mx-n27 {
  margin-right: -8.4375rem !important;
}

.mb-n27,
.my-n27 {
  margin-bottom: -8.4375rem !important;
}

.ml-n27,
.mx-n27 {
  margin-left: -8.4375rem !important;
}

.m-n28 {
  margin: -9.0625rem !important;
}

.mt-n28,
.my-n28 {
  margin-top: -9.0625rem !important;
}

.mr-n28,
.mx-n28 {
  margin-right: -9.0625rem !important;
}

.mb-n28,
.my-n28 {
  margin-bottom: -9.0625rem !important;
}

.ml-n28,
.mx-n28 {
  margin-left: -9.0625rem !important;
}

.m-n29 {
  margin: -9.375rem !important;
}

.mt-n29,
.my-n29 {
  margin-top: -9.375rem !important;
}

.mr-n29,
.mx-n29 {
  margin-right: -9.375rem !important;
}

.mb-n29,
.my-n29 {
  margin-bottom: -9.375rem !important;
}

.ml-n29,
.mx-n29 {
  margin-left: -9.375rem !important;
}

.m-n30 {
  margin: -9.6875rem !important;
}

.mt-n30,
.my-n30 {
  margin-top: -9.6875rem !important;
}

.mr-n30,
.mx-n30 {
  margin-right: -9.6875rem !important;
}

.mb-n30,
.my-n30 {
  margin-bottom: -9.6875rem !important;
}

.ml-n30,
.mx-n30 {
  margin-left: -9.6875rem !important;
}

.m-n31 {
  margin: -10.625rem !important;
}

.mt-n31,
.my-n31 {
  margin-top: -10.625rem !important;
}

.mr-n31,
.mx-n31 {
  margin-right: -10.625rem !important;
}

.mb-n31,
.my-n31 {
  margin-bottom: -10.625rem !important;
}

.ml-n31,
.mx-n31 {
  margin-left: -10.625rem !important;
}

.m-n32 {
  margin: -11.25rem !important;
}

.mt-n32,
.my-n32 {
  margin-top: -11.25rem !important;
}

.mr-n32,
.mx-n32 {
  margin-right: -11.25rem !important;
}

.mb-n32,
.my-n32 {
  margin-bottom: -11.25rem !important;
}

.ml-n32,
.mx-n32 {
  margin-left: -11.25rem !important;
}

.m-n33 {
  margin: -12.5rem !important;
}

.mt-n33,
.my-n33 {
  margin-top: -12.5rem !important;
}

.mr-n33,
.mx-n33 {
  margin-right: -12.5rem !important;
}

.mb-n33,
.my-n33 {
  margin-bottom: -12.5rem !important;
}

.ml-n33,
.mx-n33 {
  margin-left: -12.5rem !important;
}

.m-n34 {
  margin: -14.0625rem !important;
}

.mt-n34,
.my-n34 {
  margin-top: -14.0625rem !important;
}

.mr-n34,
.mx-n34 {
  margin-right: -14.0625rem !important;
}

.mb-n34,
.my-n34 {
  margin-bottom: -14.0625rem !important;
}

.ml-n34,
.mx-n34 {
  margin-left: -14.0625rem !important;
}

.m-n35 {
  margin: -15.625rem !important;
}

.mt-n35,
.my-n35 {
  margin-top: -15.625rem !important;
}

.mr-n35,
.mx-n35 {
  margin-right: -15.625rem !important;
}

.mb-n35,
.my-n35 {
  margin-bottom: -15.625rem !important;
}

.ml-n35,
.mx-n35 {
  margin-left: -15.625rem !important;
}

.m-auto {
  margin: auto !important;
}

.mt-auto,
.my-auto {
  margin-top: auto !important;
}

.mr-auto,
.mx-auto {
  margin-right: auto !important;
}

.mb-auto,
.my-auto {
  margin-bottom: auto !important;
}

.ml-auto,
.mx-auto {
  margin-left: auto !important;
}

@media (min-width: 480px) {
  .m-xs-0 {
    margin: 0 !important;
  }
  .mt-xs-0,
  .my-xs-0 {
    margin-top: 0 !important;
  }
  .mr-xs-0,
  .mx-xs-0 {
    margin-right: 0 !important;
  }
  .mb-xs-0,
  .my-xs-0 {
    margin-bottom: 0 !important;
  }
  .ml-xs-0,
  .mx-xs-0 {
    margin-left: 0 !important;
  }
  .m-xs-1 {
    margin: 0.25rem !important;
  }
  .mt-xs-1,
  .my-xs-1 {
    margin-top: 0.25rem !important;
  }
  .mr-xs-1,
  .mx-xs-1 {
    margin-right: 0.25rem !important;
  }
  .mb-xs-1,
  .my-xs-1 {
    margin-bottom: 0.25rem !important;
  }
  .ml-xs-1,
  .mx-xs-1 {
    margin-left: 0.25rem !important;
  }
  .m-xs-2 {
    margin: 0.375rem !important;
  }
  .mt-xs-2,
  .my-xs-2 {
    margin-top: 0.375rem !important;
  }
  .mr-xs-2,
  .mx-xs-2 {
    margin-right: 0.375rem !important;
  }
  .mb-xs-2,
  .my-xs-2 {
    margin-bottom: 0.375rem !important;
  }
  .ml-xs-2,
  .mx-xs-2 {
    margin-left: 0.375rem !important;
  }
  .m-xs-3 {
    margin: 0.5rem !important;
  }
  .mt-xs-3,
  .my-xs-3 {
    margin-top: 0.5rem !important;
  }
  .mr-xs-3,
  .mx-xs-3 {
    margin-right: 0.5rem !important;
  }
  .mb-xs-3,
  .my-xs-3 {
    margin-bottom: 0.5rem !important;
  }
  .ml-xs-3,
  .mx-xs-3 {
    margin-left: 0.5rem !important;
  }
  .m-xs-4 {
    margin: 0.625rem !important;
  }
  .mt-xs-4,
  .my-xs-4 {
    margin-top: 0.625rem !important;
  }
  .mr-xs-4,
  .mx-xs-4 {
    margin-right: 0.625rem !important;
  }
  .mb-xs-4,
  .my-xs-4 {
    margin-bottom: 0.625rem !important;
  }
  .ml-xs-4,
  .mx-xs-4 {
    margin-left: 0.625rem !important;
  }
  .m-xs-5 {
    margin: 0.75rem !important;
  }
  .mt-xs-5,
  .my-xs-5 {
    margin-top: 0.75rem !important;
  }
  .mr-xs-5,
  .mx-xs-5 {
    margin-right: 0.75rem !important;
  }
  .mb-xs-5,
  .my-xs-5 {
    margin-bottom: 0.75rem !important;
  }
  .ml-xs-5,
  .mx-xs-5 {
    margin-left: 0.75rem !important;
  }
  .m-xs-6 {
    margin: 1rem !important;
  }
  .mt-xs-6,
  .my-xs-6 {
    margin-top: 1rem !important;
  }
  .mr-xs-6,
  .mx-xs-6 {
    margin-right: 1rem !important;
  }
  .mb-xs-6,
  .my-xs-6 {
    margin-bottom: 1rem !important;
  }
  .ml-xs-6,
  .mx-xs-6 {
    margin-left: 1rem !important;
  }
  .m-xs-7 {
    margin: 1.25rem !important;
  }
  .mt-xs-7,
  .my-xs-7 {
    margin-top: 1.25rem !important;
  }
  .mr-xs-7,
  .mx-xs-7 {
    margin-right: 1.25rem !important;
  }
  .mb-xs-7,
  .my-xs-7 {
    margin-bottom: 1.25rem !important;
  }
  .ml-xs-7,
  .mx-xs-7 {
    margin-left: 1.25rem !important;
  }
  .m-xs-8 {
    margin: 1.5625rem !important;
  }
  .mt-xs-8,
  .my-xs-8 {
    margin-top: 1.5625rem !important;
  }
  .mr-xs-8,
  .mx-xs-8 {
    margin-right: 1.5625rem !important;
  }
  .mb-xs-8,
  .my-xs-8 {
    margin-bottom: 1.5625rem !important;
  }
  .ml-xs-8,
  .mx-xs-8 {
    margin-left: 1.5625rem !important;
  }
  .m-xs-9 {
    margin: 1.875rem !important;
  }
  .mt-xs-9,
  .my-xs-9 {
    margin-top: 1.875rem !important;
  }
  .mr-xs-9,
  .mx-xs-9 {
    margin-right: 1.875rem !important;
  }
  .mb-xs-9,
  .my-xs-9 {
    margin-bottom: 1.875rem !important;
  }
  .ml-xs-9,
  .mx-xs-9 {
    margin-left: 1.875rem !important;
  }
  .m-xs-10 {
    margin: 2.1875rem !important;
  }
  .mt-xs-10,
  .my-xs-10 {
    margin-top: 2.1875rem !important;
  }
  .mr-xs-10,
  .mx-xs-10 {
    margin-right: 2.1875rem !important;
  }
  .mb-xs-10,
  .my-xs-10 {
    margin-bottom: 2.1875rem !important;
  }
  .ml-xs-10,
  .mx-xs-10 {
    margin-left: 2.1875rem !important;
  }
  .m-xs-11 {
    margin: 2.5rem !important;
  }
  .mt-xs-11,
  .my-xs-11 {
    margin-top: 2.5rem !important;
  }
  .mr-xs-11,
  .mx-xs-11 {
    margin-right: 2.5rem !important;
  }
  .mb-xs-11,
  .my-xs-11 {
    margin-bottom: 2.5rem !important;
  }
  .ml-xs-11,
  .mx-xs-11 {
    margin-left: 2.5rem !important;
  }
  .m-xs-12 {
    margin: 2.8125rem !important;
  }
  .mt-xs-12,
  .my-xs-12 {
    margin-top: 2.8125rem !important;
  }
  .mr-xs-12,
  .mx-xs-12 {
    margin-right: 2.8125rem !important;
  }
  .mb-xs-12,
  .my-xs-12 {
    margin-bottom: 2.8125rem !important;
  }
  .ml-xs-12,
  .mx-xs-12 {
    margin-left: 2.8125rem !important;
  }
  .m-xs-13 {
    margin: 3.125rem !important;
  }
  .mt-xs-13,
  .my-xs-13 {
    margin-top: 3.125rem !important;
  }
  .mr-xs-13,
  .mx-xs-13 {
    margin-right: 3.125rem !important;
  }
  .mb-xs-13,
  .my-xs-13 {
    margin-bottom: 3.125rem !important;
  }
  .ml-xs-13,
  .mx-xs-13 {
    margin-left: 3.125rem !important;
  }
  .m-xs-14 {
    margin: 3.4375rem !important;
  }
  .mt-xs-14,
  .my-xs-14 {
    margin-top: 3.4375rem !important;
  }
  .mr-xs-14,
  .mx-xs-14 {
    margin-right: 3.4375rem !important;
  }
  .mb-xs-14,
  .my-xs-14 {
    margin-bottom: 3.4375rem !important;
  }
  .ml-xs-14,
  .mx-xs-14 {
    margin-left: 3.4375rem !important;
  }
  .m-xs-15 {
    margin: 3.75rem !important;
  }
  .mt-xs-15,
  .my-xs-15 {
    margin-top: 3.75rem !important;
  }
  .mr-xs-15,
  .mx-xs-15 {
    margin-right: 3.75rem !important;
  }
  .mb-xs-15,
  .my-xs-15 {
    margin-bottom: 3.75rem !important;
  }
  .ml-xs-15,
  .mx-xs-15 {
    margin-left: 3.75rem !important;
  }
  .m-xs-16 {
    margin: 4.0625rem !important;
  }
  .mt-xs-16,
  .my-xs-16 {
    margin-top: 4.0625rem !important;
  }
  .mr-xs-16,
  .mx-xs-16 {
    margin-right: 4.0625rem !important;
  }
  .mb-xs-16,
  .my-xs-16 {
    margin-bottom: 4.0625rem !important;
  }
  .ml-xs-16,
  .mx-xs-16 {
    margin-left: 4.0625rem !important;
  }
  .m-xs-17 {
    margin: 4.375rem !important;
  }
  .mt-xs-17,
  .my-xs-17 {
    margin-top: 4.375rem !important;
  }
  .mr-xs-17,
  .mx-xs-17 {
    margin-right: 4.375rem !important;
  }
  .mb-xs-17,
  .my-xs-17 {
    margin-bottom: 4.375rem !important;
  }
  .ml-xs-17,
  .mx-xs-17 {
    margin-left: 4.375rem !important;
  }
  .m-xs-18 {
    margin: 4.6875rem !important;
  }
  .mt-xs-18,
  .my-xs-18 {
    margin-top: 4.6875rem !important;
  }
  .mr-xs-18,
  .mx-xs-18 {
    margin-right: 4.6875rem !important;
  }
  .mb-xs-18,
  .my-xs-18 {
    margin-bottom: 4.6875rem !important;
  }
  .ml-xs-18,
  .mx-xs-18 {
    margin-left: 4.6875rem !important;
  }
  .m-xs-19 {
    margin: 5rem !important;
  }
  .mt-xs-19,
  .my-xs-19 {
    margin-top: 5rem !important;
  }
  .mr-xs-19,
  .mx-xs-19 {
    margin-right: 5rem !important;
  }
  .mb-xs-19,
  .my-xs-19 {
    margin-bottom: 5rem !important;
  }
  .ml-xs-19,
  .mx-xs-19 {
    margin-left: 5rem !important;
  }
  .m-xs-20 {
    margin: 5.3125rem !important;
  }
  .mt-xs-20,
  .my-xs-20 {
    margin-top: 5.3125rem !important;
  }
  .mr-xs-20,
  .mx-xs-20 {
    margin-right: 5.3125rem !important;
  }
  .mb-xs-20,
  .my-xs-20 {
    margin-bottom: 5.3125rem !important;
  }
  .ml-xs-20,
  .mx-xs-20 {
    margin-left: 5.3125rem !important;
  }
  .m-xs-21 {
    margin: 5.625rem !important;
  }
  .mt-xs-21,
  .my-xs-21 {
    margin-top: 5.625rem !important;
  }
  .mr-xs-21,
  .mx-xs-21 {
    margin-right: 5.625rem !important;
  }
  .mb-xs-21,
  .my-xs-21 {
    margin-bottom: 5.625rem !important;
  }
  .ml-xs-21,
  .mx-xs-21 {
    margin-left: 5.625rem !important;
  }
  .m-xs-22 {
    margin: 5.9375rem !important;
  }
  .mt-xs-22,
  .my-xs-22 {
    margin-top: 5.9375rem !important;
  }
  .mr-xs-22,
  .mx-xs-22 {
    margin-right: 5.9375rem !important;
  }
  .mb-xs-22,
  .my-xs-22 {
    margin-bottom: 5.9375rem !important;
  }
  .ml-xs-22,
  .mx-xs-22 {
    margin-left: 5.9375rem !important;
  }
  .m-xs-23 {
    margin: 6.25rem !important;
  }
  .mt-xs-23,
  .my-xs-23 {
    margin-top: 6.25rem !important;
  }
  .mr-xs-23,
  .mx-xs-23 {
    margin-right: 6.25rem !important;
  }
  .mb-xs-23,
  .my-xs-23 {
    margin-bottom: 6.25rem !important;
  }
  .ml-xs-23,
  .mx-xs-23 {
    margin-left: 6.25rem !important;
  }
  .m-xs-24 {
    margin: 6.875rem !important;
  }
  .mt-xs-24,
  .my-xs-24 {
    margin-top: 6.875rem !important;
  }
  .mr-xs-24,
  .mx-xs-24 {
    margin-right: 6.875rem !important;
  }
  .mb-xs-24,
  .my-xs-24 {
    margin-bottom: 6.875rem !important;
  }
  .ml-xs-24,
  .mx-xs-24 {
    margin-left: 6.875rem !important;
  }
  .m-xs-25 {
    margin: 7.5rem !important;
  }
  .mt-xs-25,
  .my-xs-25 {
    margin-top: 7.5rem !important;
  }
  .mr-xs-25,
  .mx-xs-25 {
    margin-right: 7.5rem !important;
  }
  .mb-xs-25,
  .my-xs-25 {
    margin-bottom: 7.5rem !important;
  }
  .ml-xs-25,
  .mx-xs-25 {
    margin-left: 7.5rem !important;
  }
  .m-xs-26 {
    margin: 8.125rem !important;
  }
  .mt-xs-26,
  .my-xs-26 {
    margin-top: 8.125rem !important;
  }
  .mr-xs-26,
  .mx-xs-26 {
    margin-right: 8.125rem !important;
  }
  .mb-xs-26,
  .my-xs-26 {
    margin-bottom: 8.125rem !important;
  }
  .ml-xs-26,
  .mx-xs-26 {
    margin-left: 8.125rem !important;
  }
  .m-xs-27 {
    margin: 8.4375rem !important;
  }
  .mt-xs-27,
  .my-xs-27 {
    margin-top: 8.4375rem !important;
  }
  .mr-xs-27,
  .mx-xs-27 {
    margin-right: 8.4375rem !important;
  }
  .mb-xs-27,
  .my-xs-27 {
    margin-bottom: 8.4375rem !important;
  }
  .ml-xs-27,
  .mx-xs-27 {
    margin-left: 8.4375rem !important;
  }
  .m-xs-28 {
    margin: 9.0625rem !important;
  }
  .mt-xs-28,
  .my-xs-28 {
    margin-top: 9.0625rem !important;
  }
  .mr-xs-28,
  .mx-xs-28 {
    margin-right: 9.0625rem !important;
  }
  .mb-xs-28,
  .my-xs-28 {
    margin-bottom: 9.0625rem !important;
  }
  .ml-xs-28,
  .mx-xs-28 {
    margin-left: 9.0625rem !important;
  }
  .m-xs-29 {
    margin: 9.375rem !important;
  }
  .mt-xs-29,
  .my-xs-29 {
    margin-top: 9.375rem !important;
  }
  .mr-xs-29,
  .mx-xs-29 {
    margin-right: 9.375rem !important;
  }
  .mb-xs-29,
  .my-xs-29 {
    margin-bottom: 9.375rem !important;
  }
  .ml-xs-29,
  .mx-xs-29 {
    margin-left: 9.375rem !important;
  }
  .m-xs-30 {
    margin: 9.6875rem !important;
  }
  .mt-xs-30,
  .my-xs-30 {
    margin-top: 9.6875rem !important;
  }
  .mr-xs-30,
  .mx-xs-30 {
    margin-right: 9.6875rem !important;
  }
  .mb-xs-30,
  .my-xs-30 {
    margin-bottom: 9.6875rem !important;
  }
  .ml-xs-30,
  .mx-xs-30 {
    margin-left: 9.6875rem !important;
  }
  .m-xs-31 {
    margin: 10.625rem !important;
  }
  .mt-xs-31,
  .my-xs-31 {
    margin-top: 10.625rem !important;
  }
  .mr-xs-31,
  .mx-xs-31 {
    margin-right: 10.625rem !important;
  }
  .mb-xs-31,
  .my-xs-31 {
    margin-bottom: 10.625rem !important;
  }
  .ml-xs-31,
  .mx-xs-31 {
    margin-left: 10.625rem !important;
  }
  .m-xs-32 {
    margin: 11.25rem !important;
  }
  .mt-xs-32,
  .my-xs-32 {
    margin-top: 11.25rem !important;
  }
  .mr-xs-32,
  .mx-xs-32 {
    margin-right: 11.25rem !important;
  }
  .mb-xs-32,
  .my-xs-32 {
    margin-bottom: 11.25rem !important;
  }
  .ml-xs-32,
  .mx-xs-32 {
    margin-left: 11.25rem !important;
  }
  .m-xs-33 {
    margin: 12.5rem !important;
  }
  .mt-xs-33,
  .my-xs-33 {
    margin-top: 12.5rem !important;
  }
  .mr-xs-33,
  .mx-xs-33 {
    margin-right: 12.5rem !important;
  }
  .mb-xs-33,
  .my-xs-33 {
    margin-bottom: 12.5rem !important;
  }
  .ml-xs-33,
  .mx-xs-33 {
    margin-left: 12.5rem !important;
  }
  .m-xs-34 {
    margin: 14.0625rem !important;
  }
  .mt-xs-34,
  .my-xs-34 {
    margin-top: 14.0625rem !important;
  }
  .mr-xs-34,
  .mx-xs-34 {
    margin-right: 14.0625rem !important;
  }
  .mb-xs-34,
  .my-xs-34 {
    margin-bottom: 14.0625rem !important;
  }
  .ml-xs-34,
  .mx-xs-34 {
    margin-left: 14.0625rem !important;
  }
  .m-xs-35 {
    margin: 15.625rem !important;
  }
  .mt-xs-35,
  .my-xs-35 {
    margin-top: 15.625rem !important;
  }
  .mr-xs-35,
  .mx-xs-35 {
    margin-right: 15.625rem !important;
  }
  .mb-xs-35,
  .my-xs-35 {
    margin-bottom: 15.625rem !important;
  }
  .ml-xs-35,
  .mx-xs-35 {
    margin-left: 15.625rem !important;
  }
  .p-xs-0 {
    padding: 0 !important;
  }
  .pt-xs-0,
  .py-xs-0 {
    padding-top: 0 !important;
  }
  .pr-xs-0,
  .px-xs-0 {
    padding-right: 0 !important;
  }
  .pb-xs-0,
  .py-xs-0 {
    padding-bottom: 0 !important;
  }
  .pl-xs-0,
  .px-xs-0 {
    padding-left: 0 !important;
  }
  .p-xs-1 {
    padding: 0.25rem !important;
  }
  .pt-xs-1,
  .py-xs-1 {
    padding-top: 0.25rem !important;
  }
  .pr-xs-1,
  .px-xs-1 {
    padding-right: 0.25rem !important;
  }
  .pb-xs-1,
  .py-xs-1 {
    padding-bottom: 0.25rem !important;
  }
  .pl-xs-1,
  .px-xs-1 {
    padding-left: 0.25rem !important;
  }
  .p-xs-2 {
    padding: 0.375rem !important;
  }
  .pt-xs-2,
  .py-xs-2 {
    padding-top: 0.375rem !important;
  }
  .pr-xs-2,
  .px-xs-2 {
    padding-right: 0.375rem !important;
  }
  .pb-xs-2,
  .py-xs-2 {
    padding-bottom: 0.375rem !important;
  }
  .pl-xs-2,
  .px-xs-2 {
    padding-left: 0.375rem !important;
  }
  .p-xs-3 {
    padding: 0.5rem !important;
  }
  .pt-xs-3,
  .py-xs-3 {
    padding-top: 0.5rem !important;
  }
  .pr-xs-3,
  .px-xs-3 {
    padding-right: 0.5rem !important;
  }
  .pb-xs-3,
  .py-xs-3 {
    padding-bottom: 0.5rem !important;
  }
  .pl-xs-3,
  .px-xs-3 {
    padding-left: 0.5rem !important;
  }
  .p-xs-4 {
    padding: 0.625rem !important;
  }
  .pt-xs-4,
  .py-xs-4 {
    padding-top: 0.625rem !important;
  }
  .pr-xs-4,
  .px-xs-4 {
    padding-right: 0.625rem !important;
  }
  .pb-xs-4,
  .py-xs-4 {
    padding-bottom: 0.625rem !important;
  }
  .pl-xs-4,
  .px-xs-4 {
    padding-left: 0.625rem !important;
  }
  .p-xs-5 {
    padding: 0.75rem !important;
  }
  .pt-xs-5,
  .py-xs-5 {
    padding-top: 0.75rem !important;
  }
  .pr-xs-5,
  .px-xs-5 {
    padding-right: 0.75rem !important;
  }
  .pb-xs-5,
  .py-xs-5 {
    padding-bottom: 0.75rem !important;
  }
  .pl-xs-5,
  .px-xs-5 {
    padding-left: 0.75rem !important;
  }
  .p-xs-6 {
    padding: 1rem !important;
  }
  .pt-xs-6,
  .py-xs-6 {
    padding-top: 1rem !important;
  }
  .pr-xs-6,
  .px-xs-6 {
    padding-right: 1rem !important;
  }
  .pb-xs-6,
  .py-xs-6 {
    padding-bottom: 1rem !important;
  }
  .pl-xs-6,
  .px-xs-6 {
    padding-left: 1rem !important;
  }
  .p-xs-7 {
    padding: 1.25rem !important;
  }
  .pt-xs-7,
  .py-xs-7 {
    padding-top: 1.25rem !important;
  }
  .pr-xs-7,
  .px-xs-7 {
    padding-right: 1.25rem !important;
  }
  .pb-xs-7,
  .py-xs-7 {
    padding-bottom: 1.25rem !important;
  }
  .pl-xs-7,
  .px-xs-7 {
    padding-left: 1.25rem !important;
  }
  .p-xs-8 {
    padding: 1.5625rem !important;
  }
  .pt-xs-8,
  .py-xs-8 {
    padding-top: 1.5625rem !important;
  }
  .pr-xs-8,
  .px-xs-8 {
    padding-right: 1.5625rem !important;
  }
  .pb-xs-8,
  .py-xs-8 {
    padding-bottom: 1.5625rem !important;
  }
  .pl-xs-8,
  .px-xs-8 {
    padding-left: 1.5625rem !important;
  }
  .p-xs-9 {
    padding: 1.875rem !important;
  }
  .pt-xs-9,
  .py-xs-9 {
    padding-top: 1.875rem !important;
  }
  .pr-xs-9,
  .px-xs-9 {
    padding-right: 1.875rem !important;
  }
  .pb-xs-9,
  .py-xs-9 {
    padding-bottom: 1.875rem !important;
  }
  .pl-xs-9,
  .px-xs-9 {
    padding-left: 1.875rem !important;
  }
  .p-xs-10 {
    padding: 2.1875rem !important;
  }
  .pt-xs-10,
  .py-xs-10 {
    padding-top: 2.1875rem !important;
  }
  .pr-xs-10,
  .px-xs-10 {
    padding-right: 2.1875rem !important;
  }
  .pb-xs-10,
  .py-xs-10 {
    padding-bottom: 2.1875rem !important;
  }
  .pl-xs-10,
  .px-xs-10 {
    padding-left: 2.1875rem !important;
  }
  .p-xs-11 {
    padding: 2.5rem !important;
  }
  .pt-xs-11,
  .py-xs-11 {
    padding-top: 2.5rem !important;
  }
  .pr-xs-11,
  .px-xs-11 {
    padding-right: 2.5rem !important;
  }
  .pb-xs-11,
  .py-xs-11 {
    padding-bottom: 2.5rem !important;
  }
  .pl-xs-11,
  .px-xs-11 {
    padding-left: 2.5rem !important;
  }
  .p-xs-12 {
    padding: 2.8125rem !important;
  }
  .pt-xs-12,
  .py-xs-12 {
    padding-top: 2.8125rem !important;
  }
  .pr-xs-12,
  .px-xs-12 {
    padding-right: 2.8125rem !important;
  }
  .pb-xs-12,
  .py-xs-12 {
    padding-bottom: 2.8125rem !important;
  }
  .pl-xs-12,
  .px-xs-12 {
    padding-left: 2.8125rem !important;
  }
  .p-xs-13 {
    padding: 3.125rem !important;
  }
  .pt-xs-13,
  .py-xs-13 {
    padding-top: 3.125rem !important;
  }
  .pr-xs-13,
  .px-xs-13 {
    padding-right: 3.125rem !important;
  }
  .pb-xs-13,
  .py-xs-13 {
    padding-bottom: 3.125rem !important;
  }
  .pl-xs-13,
  .px-xs-13 {
    padding-left: 3.125rem !important;
  }
  .p-xs-14 {
    padding: 3.4375rem !important;
  }
  .pt-xs-14,
  .py-xs-14 {
    padding-top: 3.4375rem !important;
  }
  .pr-xs-14,
  .px-xs-14 {
    padding-right: 3.4375rem !important;
  }
  .pb-xs-14,
  .py-xs-14 {
    padding-bottom: 3.4375rem !important;
  }
  .pl-xs-14,
  .px-xs-14 {
    padding-left: 3.4375rem !important;
  }
  .p-xs-15 {
    padding: 3.75rem !important;
  }
  .pt-xs-15,
  .py-xs-15 {
    padding-top: 3.75rem !important;
  }
  .pr-xs-15,
  .px-xs-15 {
    padding-right: 3.75rem !important;
  }
  .pb-xs-15,
  .py-xs-15 {
    padding-bottom: 3.75rem !important;
  }
  .pl-xs-15,
  .px-xs-15 {
    padding-left: 3.75rem !important;
  }
  .p-xs-16 {
    padding: 4.0625rem !important;
  }
  .pt-xs-16,
  .py-xs-16 {
    padding-top: 4.0625rem !important;
  }
  .pr-xs-16,
  .px-xs-16 {
    padding-right: 4.0625rem !important;
  }
  .pb-xs-16,
  .py-xs-16 {
    padding-bottom: 4.0625rem !important;
  }
  .pl-xs-16,
  .px-xs-16 {
    padding-left: 4.0625rem !important;
  }
  .p-xs-17 {
    padding: 4.375rem !important;
  }
  .pt-xs-17,
  .py-xs-17 {
    padding-top: 4.375rem !important;
  }
  .pr-xs-17,
  .px-xs-17 {
    padding-right: 4.375rem !important;
  }
  .pb-xs-17,
  .py-xs-17 {
    padding-bottom: 4.375rem !important;
  }
  .pl-xs-17,
  .px-xs-17 {
    padding-left: 4.375rem !important;
  }
  .p-xs-18 {
    padding: 4.6875rem !important;
  }
  .pt-xs-18,
  .py-xs-18 {
    padding-top: 4.6875rem !important;
  }
  .pr-xs-18,
  .px-xs-18 {
    padding-right: 4.6875rem !important;
  }
  .pb-xs-18,
  .py-xs-18 {
    padding-bottom: 4.6875rem !important;
  }
  .pl-xs-18,
  .px-xs-18 {
    padding-left: 4.6875rem !important;
  }
  .p-xs-19 {
    padding: 5rem !important;
  }
  .pt-xs-19,
  .py-xs-19 {
    padding-top: 5rem !important;
  }
  .pr-xs-19,
  .px-xs-19 {
    padding-right: 5rem !important;
  }
  .pb-xs-19,
  .py-xs-19 {
    padding-bottom: 5rem !important;
  }
  .pl-xs-19,
  .px-xs-19 {
    padding-left: 5rem !important;
  }
  .p-xs-20 {
    padding: 5.3125rem !important;
  }
  .pt-xs-20,
  .py-xs-20 {
    padding-top: 5.3125rem !important;
  }
  .pr-xs-20,
  .px-xs-20 {
    padding-right: 5.3125rem !important;
  }
  .pb-xs-20,
  .py-xs-20 {
    padding-bottom: 5.3125rem !important;
  }
  .pl-xs-20,
  .px-xs-20 {
    padding-left: 5.3125rem !important;
  }
  .p-xs-21 {
    padding: 5.625rem !important;
  }
  .pt-xs-21,
  .py-xs-21 {
    padding-top: 5.625rem !important;
  }
  .pr-xs-21,
  .px-xs-21 {
    padding-right: 5.625rem !important;
  }
  .pb-xs-21,
  .py-xs-21 {
    padding-bottom: 5.625rem !important;
  }
  .pl-xs-21,
  .px-xs-21 {
    padding-left: 5.625rem !important;
  }
  .p-xs-22 {
    padding: 5.9375rem !important;
  }
  .pt-xs-22,
  .py-xs-22 {
    padding-top: 5.9375rem !important;
  }
  .pr-xs-22,
  .px-xs-22 {
    padding-right: 5.9375rem !important;
  }
  .pb-xs-22,
  .py-xs-22 {
    padding-bottom: 5.9375rem !important;
  }
  .pl-xs-22,
  .px-xs-22 {
    padding-left: 5.9375rem !important;
  }
  .p-xs-23 {
    padding: 6.25rem !important;
  }
  .pt-xs-23,
  .py-xs-23 {
    padding-top: 6.25rem !important;
  }
  .pr-xs-23,
  .px-xs-23 {
    padding-right: 6.25rem !important;
  }
  .pb-xs-23,
  .py-xs-23 {
    padding-bottom: 6.25rem !important;
  }
  .pl-xs-23,
  .px-xs-23 {
    padding-left: 6.25rem !important;
  }
  .p-xs-24 {
    padding: 6.875rem !important;
  }
  .pt-xs-24,
  .py-xs-24 {
    padding-top: 6.875rem !important;
  }
  .pr-xs-24,
  .px-xs-24 {
    padding-right: 6.875rem !important;
  }
  .pb-xs-24,
  .py-xs-24 {
    padding-bottom: 6.875rem !important;
  }
  .pl-xs-24,
  .px-xs-24 {
    padding-left: 6.875rem !important;
  }
  .p-xs-25 {
    padding: 7.5rem !important;
  }
  .pt-xs-25,
  .py-xs-25 {
    padding-top: 7.5rem !important;
  }
  .pr-xs-25,
  .px-xs-25 {
    padding-right: 7.5rem !important;
  }
  .pb-xs-25,
  .py-xs-25 {
    padding-bottom: 7.5rem !important;
  }
  .pl-xs-25,
  .px-xs-25 {
    padding-left: 7.5rem !important;
  }
  .p-xs-26 {
    padding: 8.125rem !important;
  }
  .pt-xs-26,
  .py-xs-26 {
    padding-top: 8.125rem !important;
  }
  .pr-xs-26,
  .px-xs-26 {
    padding-right: 8.125rem !important;
  }
  .pb-xs-26,
  .py-xs-26 {
    padding-bottom: 8.125rem !important;
  }
  .pl-xs-26,
  .px-xs-26 {
    padding-left: 8.125rem !important;
  }
  .p-xs-27 {
    padding: 8.4375rem !important;
  }
  .pt-xs-27,
  .py-xs-27 {
    padding-top: 8.4375rem !important;
  }
  .pr-xs-27,
  .px-xs-27 {
    padding-right: 8.4375rem !important;
  }
  .pb-xs-27,
  .py-xs-27 {
    padding-bottom: 8.4375rem !important;
  }
  .pl-xs-27,
  .px-xs-27 {
    padding-left: 8.4375rem !important;
  }
  .p-xs-28 {
    padding: 9.0625rem !important;
  }
  .pt-xs-28,
  .py-xs-28 {
    padding-top: 9.0625rem !important;
  }
  .pr-xs-28,
  .px-xs-28 {
    padding-right: 9.0625rem !important;
  }
  .pb-xs-28,
  .py-xs-28 {
    padding-bottom: 9.0625rem !important;
  }
  .pl-xs-28,
  .px-xs-28 {
    padding-left: 9.0625rem !important;
  }
  .p-xs-29 {
    padding: 9.375rem !important;
  }
  .pt-xs-29,
  .py-xs-29 {
    padding-top: 9.375rem !important;
  }
  .pr-xs-29,
  .px-xs-29 {
    padding-right: 9.375rem !important;
  }
  .pb-xs-29,
  .py-xs-29 {
    padding-bottom: 9.375rem !important;
  }
  .pl-xs-29,
  .px-xs-29 {
    padding-left: 9.375rem !important;
  }
  .p-xs-30 {
    padding: 9.6875rem !important;
  }
  .pt-xs-30,
  .py-xs-30 {
    padding-top: 9.6875rem !important;
  }
  .pr-xs-30,
  .px-xs-30 {
    padding-right: 9.6875rem !important;
  }
  .pb-xs-30,
  .py-xs-30 {
    padding-bottom: 9.6875rem !important;
  }
  .pl-xs-30,
  .px-xs-30 {
    padding-left: 9.6875rem !important;
  }
  .p-xs-31 {
    padding: 10.625rem !important;
  }
  .pt-xs-31,
  .py-xs-31 {
    padding-top: 10.625rem !important;
  }
  .pr-xs-31,
  .px-xs-31 {
    padding-right: 10.625rem !important;
  }
  .pb-xs-31,
  .py-xs-31 {
    padding-bottom: 10.625rem !important;
  }
  .pl-xs-31,
  .px-xs-31 {
    padding-left: 10.625rem !important;
  }
  .p-xs-32 {
    padding: 11.25rem !important;
  }
  .pt-xs-32,
  .py-xs-32 {
    padding-top: 11.25rem !important;
  }
  .pr-xs-32,
  .px-xs-32 {
    padding-right: 11.25rem !important;
  }
  .pb-xs-32,
  .py-xs-32 {
    padding-bottom: 11.25rem !important;
  }
  .pl-xs-32,
  .px-xs-32 {
    padding-left: 11.25rem !important;
  }
  .p-xs-33 {
    padding: 12.5rem !important;
  }
  .pt-xs-33,
  .py-xs-33 {
    padding-top: 12.5rem !important;
  }
  .pr-xs-33,
  .px-xs-33 {
    padding-right: 12.5rem !important;
  }
  .pb-xs-33,
  .py-xs-33 {
    padding-bottom: 12.5rem !important;
  }
  .pl-xs-33,
  .px-xs-33 {
    padding-left: 12.5rem !important;
  }
  .p-xs-34 {
    padding: 14.0625rem !important;
  }
  .pt-xs-34,
  .py-xs-34 {
    padding-top: 14.0625rem !important;
  }
  .pr-xs-34,
  .px-xs-34 {
    padding-right: 14.0625rem !important;
  }
  .pb-xs-34,
  .py-xs-34 {
    padding-bottom: 14.0625rem !important;
  }
  .pl-xs-34,
  .px-xs-34 {
    padding-left: 14.0625rem !important;
  }
  .p-xs-35 {
    padding: 15.625rem !important;
  }
  .pt-xs-35,
  .py-xs-35 {
    padding-top: 15.625rem !important;
  }
  .pr-xs-35,
  .px-xs-35 {
    padding-right: 15.625rem !important;
  }
  .pb-xs-35,
  .py-xs-35 {
    padding-bottom: 15.625rem !important;
  }
  .pl-xs-35,
  .px-xs-35 {
    padding-left: 15.625rem !important;
  }
  .m-xs-n1 {
    margin: -0.25rem !important;
  }
  .mt-xs-n1,
  .my-xs-n1 {
    margin-top: -0.25rem !important;
  }
  .mr-xs-n1,
  .mx-xs-n1 {
    margin-right: -0.25rem !important;
  }
  .mb-xs-n1,
  .my-xs-n1 {
    margin-bottom: -0.25rem !important;
  }
  .ml-xs-n1,
  .mx-xs-n1 {
    margin-left: -0.25rem !important;
  }
  .m-xs-n2 {
    margin: -0.375rem !important;
  }
  .mt-xs-n2,
  .my-xs-n2 {
    margin-top: -0.375rem !important;
  }
  .mr-xs-n2,
  .mx-xs-n2 {
    margin-right: -0.375rem !important;
  }
  .mb-xs-n2,
  .my-xs-n2 {
    margin-bottom: -0.375rem !important;
  }
  .ml-xs-n2,
  .mx-xs-n2 {
    margin-left: -0.375rem !important;
  }
  .m-xs-n3 {
    margin: -0.5rem !important;
  }
  .mt-xs-n3,
  .my-xs-n3 {
    margin-top: -0.5rem !important;
  }
  .mr-xs-n3,
  .mx-xs-n3 {
    margin-right: -0.5rem !important;
  }
  .mb-xs-n3,
  .my-xs-n3 {
    margin-bottom: -0.5rem !important;
  }
  .ml-xs-n3,
  .mx-xs-n3 {
    margin-left: -0.5rem !important;
  }
  .m-xs-n4 {
    margin: -0.625rem !important;
  }
  .mt-xs-n4,
  .my-xs-n4 {
    margin-top: -0.625rem !important;
  }
  .mr-xs-n4,
  .mx-xs-n4 {
    margin-right: -0.625rem !important;
  }
  .mb-xs-n4,
  .my-xs-n4 {
    margin-bottom: -0.625rem !important;
  }
  .ml-xs-n4,
  .mx-xs-n4 {
    margin-left: -0.625rem !important;
  }
  .m-xs-n5 {
    margin: -0.75rem !important;
  }
  .mt-xs-n5,
  .my-xs-n5 {
    margin-top: -0.75rem !important;
  }
  .mr-xs-n5,
  .mx-xs-n5 {
    margin-right: -0.75rem !important;
  }
  .mb-xs-n5,
  .my-xs-n5 {
    margin-bottom: -0.75rem !important;
  }
  .ml-xs-n5,
  .mx-xs-n5 {
    margin-left: -0.75rem !important;
  }
  .m-xs-n6 {
    margin: -1rem !important;
  }
  .mt-xs-n6,
  .my-xs-n6 {
    margin-top: -1rem !important;
  }
  .mr-xs-n6,
  .mx-xs-n6 {
    margin-right: -1rem !important;
  }
  .mb-xs-n6,
  .my-xs-n6 {
    margin-bottom: -1rem !important;
  }
  .ml-xs-n6,
  .mx-xs-n6 {
    margin-left: -1rem !important;
  }
  .m-xs-n7 {
    margin: -1.25rem !important;
  }
  .mt-xs-n7,
  .my-xs-n7 {
    margin-top: -1.25rem !important;
  }
  .mr-xs-n7,
  .mx-xs-n7 {
    margin-right: -1.25rem !important;
  }
  .mb-xs-n7,
  .my-xs-n7 {
    margin-bottom: -1.25rem !important;
  }
  .ml-xs-n7,
  .mx-xs-n7 {
    margin-left: -1.25rem !important;
  }
  .m-xs-n8 {
    margin: -1.5625rem !important;
  }
  .mt-xs-n8,
  .my-xs-n8 {
    margin-top: -1.5625rem !important;
  }
  .mr-xs-n8,
  .mx-xs-n8 {
    margin-right: -1.5625rem !important;
  }
  .mb-xs-n8,
  .my-xs-n8 {
    margin-bottom: -1.5625rem !important;
  }
  .ml-xs-n8,
  .mx-xs-n8 {
    margin-left: -1.5625rem !important;
  }
  .m-xs-n9 {
    margin: -1.875rem !important;
  }
  .mt-xs-n9,
  .my-xs-n9 {
    margin-top: -1.875rem !important;
  }
  .mr-xs-n9,
  .mx-xs-n9 {
    margin-right: -1.875rem !important;
  }
  .mb-xs-n9,
  .my-xs-n9 {
    margin-bottom: -1.875rem !important;
  }
  .ml-xs-n9,
  .mx-xs-n9 {
    margin-left: -1.875rem !important;
  }
  .m-xs-n10 {
    margin: -2.1875rem !important;
  }
  .mt-xs-n10,
  .my-xs-n10 {
    margin-top: -2.1875rem !important;
  }
  .mr-xs-n10,
  .mx-xs-n10 {
    margin-right: -2.1875rem !important;
  }
  .mb-xs-n10,
  .my-xs-n10 {
    margin-bottom: -2.1875rem !important;
  }
  .ml-xs-n10,
  .mx-xs-n10 {
    margin-left: -2.1875rem !important;
  }
  .m-xs-n11 {
    margin: -2.5rem !important;
  }
  .mt-xs-n11,
  .my-xs-n11 {
    margin-top: -2.5rem !important;
  }
  .mr-xs-n11,
  .mx-xs-n11 {
    margin-right: -2.5rem !important;
  }
  .mb-xs-n11,
  .my-xs-n11 {
    margin-bottom: -2.5rem !important;
  }
  .ml-xs-n11,
  .mx-xs-n11 {
    margin-left: -2.5rem !important;
  }
  .m-xs-n12 {
    margin: -2.8125rem !important;
  }
  .mt-xs-n12,
  .my-xs-n12 {
    margin-top: -2.8125rem !important;
  }
  .mr-xs-n12,
  .mx-xs-n12 {
    margin-right: -2.8125rem !important;
  }
  .mb-xs-n12,
  .my-xs-n12 {
    margin-bottom: -2.8125rem !important;
  }
  .ml-xs-n12,
  .mx-xs-n12 {
    margin-left: -2.8125rem !important;
  }
  .m-xs-n13 {
    margin: -3.125rem !important;
  }
  .mt-xs-n13,
  .my-xs-n13 {
    margin-top: -3.125rem !important;
  }
  .mr-xs-n13,
  .mx-xs-n13 {
    margin-right: -3.125rem !important;
  }
  .mb-xs-n13,
  .my-xs-n13 {
    margin-bottom: -3.125rem !important;
  }
  .ml-xs-n13,
  .mx-xs-n13 {
    margin-left: -3.125rem !important;
  }
  .m-xs-n14 {
    margin: -3.4375rem !important;
  }
  .mt-xs-n14,
  .my-xs-n14 {
    margin-top: -3.4375rem !important;
  }
  .mr-xs-n14,
  .mx-xs-n14 {
    margin-right: -3.4375rem !important;
  }
  .mb-xs-n14,
  .my-xs-n14 {
    margin-bottom: -3.4375rem !important;
  }
  .ml-xs-n14,
  .mx-xs-n14 {
    margin-left: -3.4375rem !important;
  }
  .m-xs-n15 {
    margin: -3.75rem !important;
  }
  .mt-xs-n15,
  .my-xs-n15 {
    margin-top: -3.75rem !important;
  }
  .mr-xs-n15,
  .mx-xs-n15 {
    margin-right: -3.75rem !important;
  }
  .mb-xs-n15,
  .my-xs-n15 {
    margin-bottom: -3.75rem !important;
  }
  .ml-xs-n15,
  .mx-xs-n15 {
    margin-left: -3.75rem !important;
  }
  .m-xs-n16 {
    margin: -4.0625rem !important;
  }
  .mt-xs-n16,
  .my-xs-n16 {
    margin-top: -4.0625rem !important;
  }
  .mr-xs-n16,
  .mx-xs-n16 {
    margin-right: -4.0625rem !important;
  }
  .mb-xs-n16,
  .my-xs-n16 {
    margin-bottom: -4.0625rem !important;
  }
  .ml-xs-n16,
  .mx-xs-n16 {
    margin-left: -4.0625rem !important;
  }
  .m-xs-n17 {
    margin: -4.375rem !important;
  }
  .mt-xs-n17,
  .my-xs-n17 {
    margin-top: -4.375rem !important;
  }
  .mr-xs-n17,
  .mx-xs-n17 {
    margin-right: -4.375rem !important;
  }
  .mb-xs-n17,
  .my-xs-n17 {
    margin-bottom: -4.375rem !important;
  }
  .ml-xs-n17,
  .mx-xs-n17 {
    margin-left: -4.375rem !important;
  }
  .m-xs-n18 {
    margin: -4.6875rem !important;
  }
  .mt-xs-n18,
  .my-xs-n18 {
    margin-top: -4.6875rem !important;
  }
  .mr-xs-n18,
  .mx-xs-n18 {
    margin-right: -4.6875rem !important;
  }
  .mb-xs-n18,
  .my-xs-n18 {
    margin-bottom: -4.6875rem !important;
  }
  .ml-xs-n18,
  .mx-xs-n18 {
    margin-left: -4.6875rem !important;
  }
  .m-xs-n19 {
    margin: -5rem !important;
  }
  .mt-xs-n19,
  .my-xs-n19 {
    margin-top: -5rem !important;
  }
  .mr-xs-n19,
  .mx-xs-n19 {
    margin-right: -5rem !important;
  }
  .mb-xs-n19,
  .my-xs-n19 {
    margin-bottom: -5rem !important;
  }
  .ml-xs-n19,
  .mx-xs-n19 {
    margin-left: -5rem !important;
  }
  .m-xs-n20 {
    margin: -5.3125rem !important;
  }
  .mt-xs-n20,
  .my-xs-n20 {
    margin-top: -5.3125rem !important;
  }
  .mr-xs-n20,
  .mx-xs-n20 {
    margin-right: -5.3125rem !important;
  }
  .mb-xs-n20,
  .my-xs-n20 {
    margin-bottom: -5.3125rem !important;
  }
  .ml-xs-n20,
  .mx-xs-n20 {
    margin-left: -5.3125rem !important;
  }
  .m-xs-n21 {
    margin: -5.625rem !important;
  }
  .mt-xs-n21,
  .my-xs-n21 {
    margin-top: -5.625rem !important;
  }
  .mr-xs-n21,
  .mx-xs-n21 {
    margin-right: -5.625rem !important;
  }
  .mb-xs-n21,
  .my-xs-n21 {
    margin-bottom: -5.625rem !important;
  }
  .ml-xs-n21,
  .mx-xs-n21 {
    margin-left: -5.625rem !important;
  }
  .m-xs-n22 {
    margin: -5.9375rem !important;
  }
  .mt-xs-n22,
  .my-xs-n22 {
    margin-top: -5.9375rem !important;
  }
  .mr-xs-n22,
  .mx-xs-n22 {
    margin-right: -5.9375rem !important;
  }
  .mb-xs-n22,
  .my-xs-n22 {
    margin-bottom: -5.9375rem !important;
  }
  .ml-xs-n22,
  .mx-xs-n22 {
    margin-left: -5.9375rem !important;
  }
  .m-xs-n23 {
    margin: -6.25rem !important;
  }
  .mt-xs-n23,
  .my-xs-n23 {
    margin-top: -6.25rem !important;
  }
  .mr-xs-n23,
  .mx-xs-n23 {
    margin-right: -6.25rem !important;
  }
  .mb-xs-n23,
  .my-xs-n23 {
    margin-bottom: -6.25rem !important;
  }
  .ml-xs-n23,
  .mx-xs-n23 {
    margin-left: -6.25rem !important;
  }
  .m-xs-n24 {
    margin: -6.875rem !important;
  }
  .mt-xs-n24,
  .my-xs-n24 {
    margin-top: -6.875rem !important;
  }
  .mr-xs-n24,
  .mx-xs-n24 {
    margin-right: -6.875rem !important;
  }
  .mb-xs-n24,
  .my-xs-n24 {
    margin-bottom: -6.875rem !important;
  }
  .ml-xs-n24,
  .mx-xs-n24 {
    margin-left: -6.875rem !important;
  }
  .m-xs-n25 {
    margin: -7.5rem !important;
  }
  .mt-xs-n25,
  .my-xs-n25 {
    margin-top: -7.5rem !important;
  }
  .mr-xs-n25,
  .mx-xs-n25 {
    margin-right: -7.5rem !important;
  }
  .mb-xs-n25,
  .my-xs-n25 {
    margin-bottom: -7.5rem !important;
  }
  .ml-xs-n25,
  .mx-xs-n25 {
    margin-left: -7.5rem !important;
  }
  .m-xs-n26 {
    margin: -8.125rem !important;
  }
  .mt-xs-n26,
  .my-xs-n26 {
    margin-top: -8.125rem !important;
  }
  .mr-xs-n26,
  .mx-xs-n26 {
    margin-right: -8.125rem !important;
  }
  .mb-xs-n26,
  .my-xs-n26 {
    margin-bottom: -8.125rem !important;
  }
  .ml-xs-n26,
  .mx-xs-n26 {
    margin-left: -8.125rem !important;
  }
  .m-xs-n27 {
    margin: -8.4375rem !important;
  }
  .mt-xs-n27,
  .my-xs-n27 {
    margin-top: -8.4375rem !important;
  }
  .mr-xs-n27,
  .mx-xs-n27 {
    margin-right: -8.4375rem !important;
  }
  .mb-xs-n27,
  .my-xs-n27 {
    margin-bottom: -8.4375rem !important;
  }
  .ml-xs-n27,
  .mx-xs-n27 {
    margin-left: -8.4375rem !important;
  }
  .m-xs-n28 {
    margin: -9.0625rem !important;
  }
  .mt-xs-n28,
  .my-xs-n28 {
    margin-top: -9.0625rem !important;
  }
  .mr-xs-n28,
  .mx-xs-n28 {
    margin-right: -9.0625rem !important;
  }
  .mb-xs-n28,
  .my-xs-n28 {
    margin-bottom: -9.0625rem !important;
  }
  .ml-xs-n28,
  .mx-xs-n28 {
    margin-left: -9.0625rem !important;
  }
  .m-xs-n29 {
    margin: -9.375rem !important;
  }
  .mt-xs-n29,
  .my-xs-n29 {
    margin-top: -9.375rem !important;
  }
  .mr-xs-n29,
  .mx-xs-n29 {
    margin-right: -9.375rem !important;
  }
  .mb-xs-n29,
  .my-xs-n29 {
    margin-bottom: -9.375rem !important;
  }
  .ml-xs-n29,
  .mx-xs-n29 {
    margin-left: -9.375rem !important;
  }
  .m-xs-n30 {
    margin: -9.6875rem !important;
  }
  .mt-xs-n30,
  .my-xs-n30 {
    margin-top: -9.6875rem !important;
  }
  .mr-xs-n30,
  .mx-xs-n30 {
    margin-right: -9.6875rem !important;
  }
  .mb-xs-n30,
  .my-xs-n30 {
    margin-bottom: -9.6875rem !important;
  }
  .ml-xs-n30,
  .mx-xs-n30 {
    margin-left: -9.6875rem !important;
  }
  .m-xs-n31 {
    margin: -10.625rem !important;
  }
  .mt-xs-n31,
  .my-xs-n31 {
    margin-top: -10.625rem !important;
  }
  .mr-xs-n31,
  .mx-xs-n31 {
    margin-right: -10.625rem !important;
  }
  .mb-xs-n31,
  .my-xs-n31 {
    margin-bottom: -10.625rem !important;
  }
  .ml-xs-n31,
  .mx-xs-n31 {
    margin-left: -10.625rem !important;
  }
  .m-xs-n32 {
    margin: -11.25rem !important;
  }
  .mt-xs-n32,
  .my-xs-n32 {
    margin-top: -11.25rem !important;
  }
  .mr-xs-n32,
  .mx-xs-n32 {
    margin-right: -11.25rem !important;
  }
  .mb-xs-n32,
  .my-xs-n32 {
    margin-bottom: -11.25rem !important;
  }
  .ml-xs-n32,
  .mx-xs-n32 {
    margin-left: -11.25rem !important;
  }
  .m-xs-n33 {
    margin: -12.5rem !important;
  }
  .mt-xs-n33,
  .my-xs-n33 {
    margin-top: -12.5rem !important;
  }
  .mr-xs-n33,
  .mx-xs-n33 {
    margin-right: -12.5rem !important;
  }
  .mb-xs-n33,
  .my-xs-n33 {
    margin-bottom: -12.5rem !important;
  }
  .ml-xs-n33,
  .mx-xs-n33 {
    margin-left: -12.5rem !important;
  }
  .m-xs-n34 {
    margin: -14.0625rem !important;
  }
  .mt-xs-n34,
  .my-xs-n34 {
    margin-top: -14.0625rem !important;
  }
  .mr-xs-n34,
  .mx-xs-n34 {
    margin-right: -14.0625rem !important;
  }
  .mb-xs-n34,
  .my-xs-n34 {
    margin-bottom: -14.0625rem !important;
  }
  .ml-xs-n34,
  .mx-xs-n34 {
    margin-left: -14.0625rem !important;
  }
  .m-xs-n35 {
    margin: -15.625rem !important;
  }
  .mt-xs-n35,
  .my-xs-n35 {
    margin-top: -15.625rem !important;
  }
  .mr-xs-n35,
  .mx-xs-n35 {
    margin-right: -15.625rem !important;
  }
  .mb-xs-n35,
  .my-xs-n35 {
    margin-bottom: -15.625rem !important;
  }
  .ml-xs-n35,
  .mx-xs-n35 {
    margin-left: -15.625rem !important;
  }
  .m-xs-auto {
    margin: auto !important;
  }
  .mt-xs-auto,
  .my-xs-auto {
    margin-top: auto !important;
  }
  .mr-xs-auto,
  .mx-xs-auto {
    margin-right: auto !important;
  }
  .mb-xs-auto,
  .my-xs-auto {
    margin-bottom: auto !important;
  }
  .ml-xs-auto,
  .mx-xs-auto {
    margin-left: auto !important;
  }
}

@media (min-width: 576px) {
  .m-sm-0 {
    margin: 0 !important;
  }
  .mt-sm-0,
  .my-sm-0 {
    margin-top: 0 !important;
  }
  .mr-sm-0,
  .mx-sm-0 {
    margin-right: 0 !important;
  }
  .mb-sm-0,
  .my-sm-0 {
    margin-bottom: 0 !important;
  }
  .ml-sm-0,
  .mx-sm-0 {
    margin-left: 0 !important;
  }
  .m-sm-1 {
    margin: 0.25rem !important;
  }
  .mt-sm-1,
  .my-sm-1 {
    margin-top: 0.25rem !important;
  }
  .mr-sm-1,
  .mx-sm-1 {
    margin-right: 0.25rem !important;
  }
  .mb-sm-1,
  .my-sm-1 {
    margin-bottom: 0.25rem !important;
  }
  .ml-sm-1,
  .mx-sm-1 {
    margin-left: 0.25rem !important;
  }
  .m-sm-2 {
    margin: 0.375rem !important;
  }
  .mt-sm-2,
  .my-sm-2 {
    margin-top: 0.375rem !important;
  }
  .mr-sm-2,
  .mx-sm-2 {
    margin-right: 0.375rem !important;
  }
  .mb-sm-2,
  .my-sm-2 {
    margin-bottom: 0.375rem !important;
  }
  .ml-sm-2,
  .mx-sm-2 {
    margin-left: 0.375rem !important;
  }
  .m-sm-3 {
    margin: 0.5rem !important;
  }
  .mt-sm-3,
  .my-sm-3 {
    margin-top: 0.5rem !important;
  }
  .mr-sm-3,
  .mx-sm-3 {
    margin-right: 0.5rem !important;
  }
  .mb-sm-3,
  .my-sm-3 {
    margin-bottom: 0.5rem !important;
  }
  .ml-sm-3,
  .mx-sm-3 {
    margin-left: 0.5rem !important;
  }
  .m-sm-4 {
    margin: 0.625rem !important;
  }
  .mt-sm-4,
  .my-sm-4 {
    margin-top: 0.625rem !important;
  }
  .mr-sm-4,
  .mx-sm-4 {
    margin-right: 0.625rem !important;
  }
  .mb-sm-4,
  .my-sm-4 {
    margin-bottom: 0.625rem !important;
  }
  .ml-sm-4,
  .mx-sm-4 {
    margin-left: 0.625rem !important;
  }
  .m-sm-5 {
    margin: 0.75rem !important;
  }
  .mt-sm-5,
  .my-sm-5 {
    margin-top: 0.75rem !important;
  }
  .mr-sm-5,
  .mx-sm-5 {
    margin-right: 0.75rem !important;
  }
  .mb-sm-5,
  .my-sm-5 {
    margin-bottom: 0.75rem !important;
  }
  .ml-sm-5,
  .mx-sm-5 {
    margin-left: 0.75rem !important;
  }
  .m-sm-6 {
    margin: 1rem !important;
  }
  .mt-sm-6,
  .my-sm-6 {
    margin-top: 1rem !important;
  }
  .mr-sm-6,
  .mx-sm-6 {
    margin-right: 1rem !important;
  }
  .mb-sm-6,
  .my-sm-6 {
    margin-bottom: 1rem !important;
  }
  .ml-sm-6,
  .mx-sm-6 {
    margin-left: 1rem !important;
  }
  .m-sm-7 {
    margin: 1.25rem !important;
  }
  .mt-sm-7,
  .my-sm-7 {
    margin-top: 1.25rem !important;
  }
  .mr-sm-7,
  .mx-sm-7 {
    margin-right: 1.25rem !important;
  }
  .mb-sm-7,
  .my-sm-7 {
    margin-bottom: 1.25rem !important;
  }
  .ml-sm-7,
  .mx-sm-7 {
    margin-left: 1.25rem !important;
  }
  .m-sm-8 {
    margin: 1.5625rem !important;
  }
  .mt-sm-8,
  .my-sm-8 {
    margin-top: 1.5625rem !important;
  }
  .mr-sm-8,
  .mx-sm-8 {
    margin-right: 1.5625rem !important;
  }
  .mb-sm-8,
  .my-sm-8 {
    margin-bottom: 1.5625rem !important;
  }
  .ml-sm-8,
  .mx-sm-8 {
    margin-left: 1.5625rem !important;
  }
  .m-sm-9 {
    margin: 1.875rem !important;
  }
  .mt-sm-9,
  .my-sm-9 {
    margin-top: 1.875rem !important;
  }
  .mr-sm-9,
  .mx-sm-9 {
    margin-right: 1.875rem !important;
  }
  .mb-sm-9,
  .my-sm-9 {
    margin-bottom: 1.875rem !important;
  }
  .ml-sm-9,
  .mx-sm-9 {
    margin-left: 1.875rem !important;
  }
  .m-sm-10 {
    margin: 2.1875rem !important;
  }
  .mt-sm-10,
  .my-sm-10 {
    margin-top: 2.1875rem !important;
  }
  .mr-sm-10,
  .mx-sm-10 {
    margin-right: 2.1875rem !important;
  }
  .mb-sm-10,
  .my-sm-10 {
    margin-bottom: 2.1875rem !important;
  }
  .ml-sm-10,
  .mx-sm-10 {
    margin-left: 2.1875rem !important;
  }
  .m-sm-11 {
    margin: 2.5rem !important;
  }
  .mt-sm-11,
  .my-sm-11 {
    margin-top: 2.5rem !important;
  }
  .mr-sm-11,
  .mx-sm-11 {
    margin-right: 2.5rem !important;
  }
  .mb-sm-11,
  .my-sm-11 {
    margin-bottom: 2.5rem !important;
  }
  .ml-sm-11,
  .mx-sm-11 {
    margin-left: 2.5rem !important;
  }
  .m-sm-12 {
    margin: 2.8125rem !important;
  }
  .mt-sm-12,
  .my-sm-12 {
    margin-top: 2.8125rem !important;
  }
  .mr-sm-12,
  .mx-sm-12 {
    margin-right: 2.8125rem !important;
  }
  .mb-sm-12,
  .my-sm-12 {
    margin-bottom: 2.8125rem !important;
  }
  .ml-sm-12,
  .mx-sm-12 {
    margin-left: 2.8125rem !important;
  }
  .m-sm-13 {
    margin: 3.125rem !important;
  }
  .mt-sm-13,
  .my-sm-13 {
    margin-top: 3.125rem !important;
  }
  .mr-sm-13,
  .mx-sm-13 {
    margin-right: 3.125rem !important;
  }
  .mb-sm-13,
  .my-sm-13 {
    margin-bottom: 3.125rem !important;
  }
  .ml-sm-13,
  .mx-sm-13 {
    margin-left: 3.125rem !important;
  }
  .m-sm-14 {
    margin: 3.4375rem !important;
  }
  .mt-sm-14,
  .my-sm-14 {
    margin-top: 3.4375rem !important;
  }
  .mr-sm-14,
  .mx-sm-14 {
    margin-right: 3.4375rem !important;
  }
  .mb-sm-14,
  .my-sm-14 {
    margin-bottom: 3.4375rem !important;
  }
  .ml-sm-14,
  .mx-sm-14 {
    margin-left: 3.4375rem !important;
  }
  .m-sm-15 {
    margin: 3.75rem !important;
  }
  .mt-sm-15,
  .my-sm-15 {
    margin-top: 3.75rem !important;
  }
  .mr-sm-15,
  .mx-sm-15 {
    margin-right: 3.75rem !important;
  }
  .mb-sm-15,
  .my-sm-15 {
    margin-bottom: 3.75rem !important;
  }
  .ml-sm-15,
  .mx-sm-15 {
    margin-left: 3.75rem !important;
  }
  .m-sm-16 {
    margin: 4.0625rem !important;
  }
  .mt-sm-16,
  .my-sm-16 {
    margin-top: 4.0625rem !important;
  }
  .mr-sm-16,
  .mx-sm-16 {
    margin-right: 4.0625rem !important;
  }
  .mb-sm-16,
  .my-sm-16 {
    margin-bottom: 4.0625rem !important;
  }
  .ml-sm-16,
  .mx-sm-16 {
    margin-left: 4.0625rem !important;
  }
  .m-sm-17 {
    margin: 4.375rem !important;
  }
  .mt-sm-17,
  .my-sm-17 {
    margin-top: 4.375rem !important;
  }
  .mr-sm-17,
  .mx-sm-17 {
    margin-right: 4.375rem !important;
  }
  .mb-sm-17,
  .my-sm-17 {
    margin-bottom: 4.375rem !important;
  }
  .ml-sm-17,
  .mx-sm-17 {
    margin-left: 4.375rem !important;
  }
  .m-sm-18 {
    margin: 4.6875rem !important;
  }
  .mt-sm-18,
  .my-sm-18 {
    margin-top: 4.6875rem !important;
  }
  .mr-sm-18,
  .mx-sm-18 {
    margin-right: 4.6875rem !important;
  }
  .mb-sm-18,
  .my-sm-18 {
    margin-bottom: 4.6875rem !important;
  }
  .ml-sm-18,
  .mx-sm-18 {
    margin-left: 4.6875rem !important;
  }
  .m-sm-19 {
    margin: 5rem !important;
  }
  .mt-sm-19,
  .my-sm-19 {
    margin-top: 5rem !important;
  }
  .mr-sm-19,
  .mx-sm-19 {
    margin-right: 5rem !important;
  }
  .mb-sm-19,
  .my-sm-19 {
    margin-bottom: 5rem !important;
  }
  .ml-sm-19,
  .mx-sm-19 {
    margin-left: 5rem !important;
  }
  .m-sm-20 {
    margin: 5.3125rem !important;
  }
  .mt-sm-20,
  .my-sm-20 {
    margin-top: 5.3125rem !important;
  }
  .mr-sm-20,
  .mx-sm-20 {
    margin-right: 5.3125rem !important;
  }
  .mb-sm-20,
  .my-sm-20 {
    margin-bottom: 5.3125rem !important;
  }
  .ml-sm-20,
  .mx-sm-20 {
    margin-left: 5.3125rem !important;
  }
  .m-sm-21 {
    margin: 5.625rem !important;
  }
  .mt-sm-21,
  .my-sm-21 {
    margin-top: 5.625rem !important;
  }
  .mr-sm-21,
  .mx-sm-21 {
    margin-right: 5.625rem !important;
  }
  .mb-sm-21,
  .my-sm-21 {
    margin-bottom: 5.625rem !important;
  }
  .ml-sm-21,
  .mx-sm-21 {
    margin-left: 5.625rem !important;
  }
  .m-sm-22 {
    margin: 5.9375rem !important;
  }
  .mt-sm-22,
  .my-sm-22 {
    margin-top: 5.9375rem !important;
  }
  .mr-sm-22,
  .mx-sm-22 {
    margin-right: 5.9375rem !important;
  }
  .mb-sm-22,
  .my-sm-22 {
    margin-bottom: 5.9375rem !important;
  }
  .ml-sm-22,
  .mx-sm-22 {
    margin-left: 5.9375rem !important;
  }
  .m-sm-23 {
    margin: 6.25rem !important;
  }
  .mt-sm-23,
  .my-sm-23 {
    margin-top: 6.25rem !important;
  }
  .mr-sm-23,
  .mx-sm-23 {
    margin-right: 6.25rem !important;
  }
  .mb-sm-23,
  .my-sm-23 {
    margin-bottom: 6.25rem !important;
  }
  .ml-sm-23,
  .mx-sm-23 {
    margin-left: 6.25rem !important;
  }
  .m-sm-24 {
    margin: 6.875rem !important;
  }
  .mt-sm-24,
  .my-sm-24 {
    margin-top: 6.875rem !important;
  }
  .mr-sm-24,
  .mx-sm-24 {
    margin-right: 6.875rem !important;
  }
  .mb-sm-24,
  .my-sm-24 {
    margin-bottom: 6.875rem !important;
  }
  .ml-sm-24,
  .mx-sm-24 {
    margin-left: 6.875rem !important;
  }
  .m-sm-25 {
    margin: 7.5rem !important;
  }
  .mt-sm-25,
  .my-sm-25 {
    margin-top: 7.5rem !important;
  }
  .mr-sm-25,
  .mx-sm-25 {
    margin-right: 7.5rem !important;
  }
  .mb-sm-25,
  .my-sm-25 {
    margin-bottom: 7.5rem !important;
  }
  .ml-sm-25,
  .mx-sm-25 {
    margin-left: 7.5rem !important;
  }
  .m-sm-26 {
    margin: 8.125rem !important;
  }
  .mt-sm-26,
  .my-sm-26 {
    margin-top: 8.125rem !important;
  }
  .mr-sm-26,
  .mx-sm-26 {
    margin-right: 8.125rem !important;
  }
  .mb-sm-26,
  .my-sm-26 {
    margin-bottom: 8.125rem !important;
  }
  .ml-sm-26,
  .mx-sm-26 {
    margin-left: 8.125rem !important;
  }
  .m-sm-27 {
    margin: 8.4375rem !important;
  }
  .mt-sm-27,
  .my-sm-27 {
    margin-top: 8.4375rem !important;
  }
  .mr-sm-27,
  .mx-sm-27 {
    margin-right: 8.4375rem !important;
  }
  .mb-sm-27,
  .my-sm-27 {
    margin-bottom: 8.4375rem !important;
  }
  .ml-sm-27,
  .mx-sm-27 {
    margin-left: 8.4375rem !important;
  }
  .m-sm-28 {
    margin: 9.0625rem !important;
  }
  .mt-sm-28,
  .my-sm-28 {
    margin-top: 9.0625rem !important;
  }
  .mr-sm-28,
  .mx-sm-28 {
    margin-right: 9.0625rem !important;
  }
  .mb-sm-28,
  .my-sm-28 {
    margin-bottom: 9.0625rem !important;
  }
  .ml-sm-28,
  .mx-sm-28 {
    margin-left: 9.0625rem !important;
  }
  .m-sm-29 {
    margin: 9.375rem !important;
  }
  .mt-sm-29,
  .my-sm-29 {
    margin-top: 9.375rem !important;
  }
  .mr-sm-29,
  .mx-sm-29 {
    margin-right: 9.375rem !important;
  }
  .mb-sm-29,
  .my-sm-29 {
    margin-bottom: 9.375rem !important;
  }
  .ml-sm-29,
  .mx-sm-29 {
    margin-left: 9.375rem !important;
  }
  .m-sm-30 {
    margin: 9.6875rem !important;
  }
  .mt-sm-30,
  .my-sm-30 {
    margin-top: 9.6875rem !important;
  }
  .mr-sm-30,
  .mx-sm-30 {
    margin-right: 9.6875rem !important;
  }
  .mb-sm-30,
  .my-sm-30 {
    margin-bottom: 9.6875rem !important;
  }
  .ml-sm-30,
  .mx-sm-30 {
    margin-left: 9.6875rem !important;
  }
  .m-sm-31 {
    margin: 10.625rem !important;
  }
  .mt-sm-31,
  .my-sm-31 {
    margin-top: 10.625rem !important;
  }
  .mr-sm-31,
  .mx-sm-31 {
    margin-right: 10.625rem !important;
  }
  .mb-sm-31,
  .my-sm-31 {
    margin-bottom: 10.625rem !important;
  }
  .ml-sm-31,
  .mx-sm-31 {
    margin-left: 10.625rem !important;
  }
  .m-sm-32 {
    margin: 11.25rem !important;
  }
  .mt-sm-32,
  .my-sm-32 {
    margin-top: 11.25rem !important;
  }
  .mr-sm-32,
  .mx-sm-32 {
    margin-right: 11.25rem !important;
  }
  .mb-sm-32,
  .my-sm-32 {
    margin-bottom: 11.25rem !important;
  }
  .ml-sm-32,
  .mx-sm-32 {
    margin-left: 11.25rem !important;
  }
  .m-sm-33 {
    margin: 12.5rem !important;
  }
  .mt-sm-33,
  .my-sm-33 {
    margin-top: 12.5rem !important;
  }
  .mr-sm-33,
  .mx-sm-33 {
    margin-right: 12.5rem !important;
  }
  .mb-sm-33,
  .my-sm-33 {
    margin-bottom: 12.5rem !important;
  }
  .ml-sm-33,
  .mx-sm-33 {
    margin-left: 12.5rem !important;
  }
  .m-sm-34 {
    margin: 14.0625rem !important;
  }
  .mt-sm-34,
  .my-sm-34 {
    margin-top: 14.0625rem !important;
  }
  .mr-sm-34,
  .mx-sm-34 {
    margin-right: 14.0625rem !important;
  }
  .mb-sm-34,
  .my-sm-34 {
    margin-bottom: 14.0625rem !important;
  }
  .ml-sm-34,
  .mx-sm-34 {
    margin-left: 14.0625rem !important;
  }
  .m-sm-35 {
    margin: 15.625rem !important;
  }
  .mt-sm-35,
  .my-sm-35 {
    margin-top: 15.625rem !important;
  }
  .mr-sm-35,
  .mx-sm-35 {
    margin-right: 15.625rem !important;
  }
  .mb-sm-35,
  .my-sm-35 {
    margin-bottom: 15.625rem !important;
  }
  .ml-sm-35,
  .mx-sm-35 {
    margin-left: 15.625rem !important;
  }
  .p-sm-0 {
    padding: 0 !important;
  }
  .pt-sm-0,
  .py-sm-0 {
    padding-top: 0 !important;
  }
  .pr-sm-0,
  .px-sm-0 {
    padding-right: 0 !important;
  }
  .pb-sm-0,
  .py-sm-0 {
    padding-bottom: 0 !important;
  }
  .pl-sm-0,
  .px-sm-0 {
    padding-left: 0 !important;
  }
  .p-sm-1 {
    padding: 0.25rem !important;
  }
  .pt-sm-1,
  .py-sm-1 {
    padding-top: 0.25rem !important;
  }
  .pr-sm-1,
  .px-sm-1 {
    padding-right: 0.25rem !important;
  }
  .pb-sm-1,
  .py-sm-1 {
    padding-bottom: 0.25rem !important;
  }
  .pl-sm-1,
  .px-sm-1 {
    padding-left: 0.25rem !important;
  }
  .p-sm-2 {
    padding: 0.375rem !important;
  }
  .pt-sm-2,
  .py-sm-2 {
    padding-top: 0.375rem !important;
  }
  .pr-sm-2,
  .px-sm-2 {
    padding-right: 0.375rem !important;
  }
  .pb-sm-2,
  .py-sm-2 {
    padding-bottom: 0.375rem !important;
  }
  .pl-sm-2,
  .px-sm-2 {
    padding-left: 0.375rem !important;
  }
  .p-sm-3 {
    padding: 0.5rem !important;
  }
  .pt-sm-3,
  .py-sm-3 {
    padding-top: 0.5rem !important;
  }
  .pr-sm-3,
  .px-sm-3 {
    padding-right: 0.5rem !important;
  }
  .pb-sm-3,
  .py-sm-3 {
    padding-bottom: 0.5rem !important;
  }
  .pl-sm-3,
  .px-sm-3 {
    padding-left: 0.5rem !important;
  }
  .p-sm-4 {
    padding: 0.625rem !important;
  }
  .pt-sm-4,
  .py-sm-4 {
    padding-top: 0.625rem !important;
  }
  .pr-sm-4,
  .px-sm-4 {
    padding-right: 0.625rem !important;
  }
  .pb-sm-4,
  .py-sm-4 {
    padding-bottom: 0.625rem !important;
  }
  .pl-sm-4,
  .px-sm-4 {
    padding-left: 0.625rem !important;
  }
  .p-sm-5 {
    padding: 0.75rem !important;
  }
  .pt-sm-5,
  .py-sm-5 {
    padding-top: 0.75rem !important;
  }
  .pr-sm-5,
  .px-sm-5 {
    padding-right: 0.75rem !important;
  }
  .pb-sm-5,
  .py-sm-5 {
    padding-bottom: 0.75rem !important;
  }
  .pl-sm-5,
  .px-sm-5 {
    padding-left: 0.75rem !important;
  }
  .p-sm-6 {
    padding: 1rem !important;
  }
  .pt-sm-6,
  .py-sm-6 {
    padding-top: 1rem !important;
  }
  .pr-sm-6,
  .px-sm-6 {
    padding-right: 1rem !important;
  }
  .pb-sm-6,
  .py-sm-6 {
    padding-bottom: 1rem !important;
  }
  .pl-sm-6,
  .px-sm-6 {
    padding-left: 1rem !important;
  }
  .p-sm-7 {
    padding: 1.25rem !important;
  }
  .pt-sm-7,
  .py-sm-7 {
    padding-top: 1.25rem !important;
  }
  .pr-sm-7,
  .px-sm-7 {
    padding-right: 1.25rem !important;
  }
  .pb-sm-7,
  .py-sm-7 {
    padding-bottom: 1.25rem !important;
  }
  .pl-sm-7,
  .px-sm-7 {
    padding-left: 1.25rem !important;
  }
  .p-sm-8 {
    padding: 1.5625rem !important;
  }
  .pt-sm-8,
  .py-sm-8 {
    padding-top: 1.5625rem !important;
  }
  .pr-sm-8,
  .px-sm-8 {
    padding-right: 1.5625rem !important;
  }
  .pb-sm-8,
  .py-sm-8 {
    padding-bottom: 1.5625rem !important;
  }
  .pl-sm-8,
  .px-sm-8 {
    padding-left: 1.5625rem !important;
  }
  .p-sm-9 {
    padding: 1.875rem !important;
  }
  .pt-sm-9,
  .py-sm-9 {
    padding-top: 1.875rem !important;
  }
  .pr-sm-9,
  .px-sm-9 {
    padding-right: 1.875rem !important;
  }
  .pb-sm-9,
  .py-sm-9 {
    padding-bottom: 1.875rem !important;
  }
  .pl-sm-9,
  .px-sm-9 {
    padding-left: 1.875rem !important;
  }
  .p-sm-10 {
    padding: 2.1875rem !important;
  }
  .pt-sm-10,
  .py-sm-10 {
    padding-top: 2.1875rem !important;
  }
  .pr-sm-10,
  .px-sm-10 {
    padding-right: 2.1875rem !important;
  }
  .pb-sm-10,
  .py-sm-10 {
    padding-bottom: 2.1875rem !important;
  }
  .pl-sm-10,
  .px-sm-10 {
    padding-left: 2.1875rem !important;
  }
  .p-sm-11 {
    padding: 2.5rem !important;
  }
  .pt-sm-11,
  .py-sm-11 {
    padding-top: 2.5rem !important;
  }
  .pr-sm-11,
  .px-sm-11 {
    padding-right: 2.5rem !important;
  }
  .pb-sm-11,
  .py-sm-11 {
    padding-bottom: 2.5rem !important;
  }
  .pl-sm-11,
  .px-sm-11 {
    padding-left: 2.5rem !important;
  }
  .p-sm-12 {
    padding: 2.8125rem !important;
  }
  .pt-sm-12,
  .py-sm-12 {
    padding-top: 2.8125rem !important;
  }
  .pr-sm-12,
  .px-sm-12 {
    padding-right: 2.8125rem !important;
  }
  .pb-sm-12,
  .py-sm-12 {
    padding-bottom: 2.8125rem !important;
  }
  .pl-sm-12,
  .px-sm-12 {
    padding-left: 2.8125rem !important;
  }
  .p-sm-13 {
    padding: 3.125rem !important;
  }
  .pt-sm-13,
  .py-sm-13 {
    padding-top: 3.125rem !important;
  }
  .pr-sm-13,
  .px-sm-13 {
    padding-right: 3.125rem !important;
  }
  .pb-sm-13,
  .py-sm-13 {
    padding-bottom: 3.125rem !important;
  }
  .pl-sm-13,
  .px-sm-13 {
    padding-left: 3.125rem !important;
  }
  .p-sm-14 {
    padding: 3.4375rem !important;
  }
  .pt-sm-14,
  .py-sm-14 {
    padding-top: 3.4375rem !important;
  }
  .pr-sm-14,
  .px-sm-14 {
    padding-right: 3.4375rem !important;
  }
  .pb-sm-14,
  .py-sm-14 {
    padding-bottom: 3.4375rem !important;
  }
  .pl-sm-14,
  .px-sm-14 {
    padding-left: 3.4375rem !important;
  }
  .p-sm-15 {
    padding: 3.75rem !important;
  }
  .pt-sm-15,
  .py-sm-15 {
    padding-top: 3.75rem !important;
  }
  .pr-sm-15,
  .px-sm-15 {
    padding-right: 3.75rem !important;
  }
  .pb-sm-15,
  .py-sm-15 {
    padding-bottom: 3.75rem !important;
  }
  .pl-sm-15,
  .px-sm-15 {
    padding-left: 3.75rem !important;
  }
  .p-sm-16 {
    padding: 4.0625rem !important;
  }
  .pt-sm-16,
  .py-sm-16 {
    padding-top: 4.0625rem !important;
  }
  .pr-sm-16,
  .px-sm-16 {
    padding-right: 4.0625rem !important;
  }
  .pb-sm-16,
  .py-sm-16 {
    padding-bottom: 4.0625rem !important;
  }
  .pl-sm-16,
  .px-sm-16 {
    padding-left: 4.0625rem !important;
  }
  .p-sm-17 {
    padding: 4.375rem !important;
  }
  .pt-sm-17,
  .py-sm-17 {
    padding-top: 4.375rem !important;
  }
  .pr-sm-17,
  .px-sm-17 {
    padding-right: 4.375rem !important;
  }
  .pb-sm-17,
  .py-sm-17 {
    padding-bottom: 4.375rem !important;
  }
  .pl-sm-17,
  .px-sm-17 {
    padding-left: 4.375rem !important;
  }
  .p-sm-18 {
    padding: 4.6875rem !important;
  }
  .pt-sm-18,
  .py-sm-18 {
    padding-top: 4.6875rem !important;
  }
  .pr-sm-18,
  .px-sm-18 {
    padding-right: 4.6875rem !important;
  }
  .pb-sm-18,
  .py-sm-18 {
    padding-bottom: 4.6875rem !important;
  }
  .pl-sm-18,
  .px-sm-18 {
    padding-left: 4.6875rem !important;
  }
  .p-sm-19 {
    padding: 5rem !important;
  }
  .pt-sm-19,
  .py-sm-19 {
    padding-top: 5rem !important;
  }
  .pr-sm-19,
  .px-sm-19 {
    padding-right: 5rem !important;
  }
  .pb-sm-19,
  .py-sm-19 {
    padding-bottom: 5rem !important;
  }
  .pl-sm-19,
  .px-sm-19 {
    padding-left: 5rem !important;
  }
  .p-sm-20 {
    padding: 5.3125rem !important;
  }
  .pt-sm-20,
  .py-sm-20 {
    padding-top: 5.3125rem !important;
  }
  .pr-sm-20,
  .px-sm-20 {
    padding-right: 5.3125rem !important;
  }
  .pb-sm-20,
  .py-sm-20 {
    padding-bottom: 5.3125rem !important;
  }
  .pl-sm-20,
  .px-sm-20 {
    padding-left: 5.3125rem !important;
  }
  .p-sm-21 {
    padding: 5.625rem !important;
  }
  .pt-sm-21,
  .py-sm-21 {
    padding-top: 5.625rem !important;
  }
  .pr-sm-21,
  .px-sm-21 {
    padding-right: 5.625rem !important;
  }
  .pb-sm-21,
  .py-sm-21 {
    padding-bottom: 5.625rem !important;
  }
  .pl-sm-21,
  .px-sm-21 {
    padding-left: 5.625rem !important;
  }
  .p-sm-22 {
    padding: 5.9375rem !important;
  }
  .pt-sm-22,
  .py-sm-22 {
    padding-top: 5.9375rem !important;
  }
  .pr-sm-22,
  .px-sm-22 {
    padding-right: 5.9375rem !important;
  }
  .pb-sm-22,
  .py-sm-22 {
    padding-bottom: 5.9375rem !important;
  }
  .pl-sm-22,
  .px-sm-22 {
    padding-left: 5.9375rem !important;
  }
  .p-sm-23 {
    padding: 6.25rem !important;
  }
  .pt-sm-23,
  .py-sm-23 {
    padding-top: 6.25rem !important;
  }
  .pr-sm-23,
  .px-sm-23 {
    padding-right: 6.25rem !important;
  }
  .pb-sm-23,
  .py-sm-23 {
    padding-bottom: 6.25rem !important;
  }
  .pl-sm-23,
  .px-sm-23 {
    padding-left: 6.25rem !important;
  }
  .p-sm-24 {
    padding: 6.875rem !important;
  }
  .pt-sm-24,
  .py-sm-24 {
    padding-top: 6.875rem !important;
  }
  .pr-sm-24,
  .px-sm-24 {
    padding-right: 6.875rem !important;
  }
  .pb-sm-24,
  .py-sm-24 {
    padding-bottom: 6.875rem !important;
  }
  .pl-sm-24,
  .px-sm-24 {
    padding-left: 6.875rem !important;
  }
  .p-sm-25 {
    padding: 7.5rem !important;
  }
  .pt-sm-25,
  .py-sm-25 {
    padding-top: 7.5rem !important;
  }
  .pr-sm-25,
  .px-sm-25 {
    padding-right: 7.5rem !important;
  }
  .pb-sm-25,
  .py-sm-25 {
    padding-bottom: 7.5rem !important;
  }
  .pl-sm-25,
  .px-sm-25 {
    padding-left: 7.5rem !important;
  }
  .p-sm-26 {
    padding: 8.125rem !important;
  }
  .pt-sm-26,
  .py-sm-26 {
    padding-top: 8.125rem !important;
  }
  .pr-sm-26,
  .px-sm-26 {
    padding-right: 8.125rem !important;
  }
  .pb-sm-26,
  .py-sm-26 {
    padding-bottom: 8.125rem !important;
  }
  .pl-sm-26,
  .px-sm-26 {
    padding-left: 8.125rem !important;
  }
  .p-sm-27 {
    padding: 8.4375rem !important;
  }
  .pt-sm-27,
  .py-sm-27 {
    padding-top: 8.4375rem !important;
  }
  .pr-sm-27,
  .px-sm-27 {
    padding-right: 8.4375rem !important;
  }
  .pb-sm-27,
  .py-sm-27 {
    padding-bottom: 8.4375rem !important;
  }
  .pl-sm-27,
  .px-sm-27 {
    padding-left: 8.4375rem !important;
  }
  .p-sm-28 {
    padding: 9.0625rem !important;
  }
  .pt-sm-28,
  .py-sm-28 {
    padding-top: 9.0625rem !important;
  }
  .pr-sm-28,
  .px-sm-28 {
    padding-right: 9.0625rem !important;
  }
  .pb-sm-28,
  .py-sm-28 {
    padding-bottom: 9.0625rem !important;
  }
  .pl-sm-28,
  .px-sm-28 {
    padding-left: 9.0625rem !important;
  }
  .p-sm-29 {
    padding: 9.375rem !important;
  }
  .pt-sm-29,
  .py-sm-29 {
    padding-top: 9.375rem !important;
  }
  .pr-sm-29,
  .px-sm-29 {
    padding-right: 9.375rem !important;
  }
  .pb-sm-29,
  .py-sm-29 {
    padding-bottom: 9.375rem !important;
  }
  .pl-sm-29,
  .px-sm-29 {
    padding-left: 9.375rem !important;
  }
  .p-sm-30 {
    padding: 9.6875rem !important;
  }
  .pt-sm-30,
  .py-sm-30 {
    padding-top: 9.6875rem !important;
  }
  .pr-sm-30,
  .px-sm-30 {
    padding-right: 9.6875rem !important;
  }
  .pb-sm-30,
  .py-sm-30 {
    padding-bottom: 9.6875rem !important;
  }
  .pl-sm-30,
  .px-sm-30 {
    padding-left: 9.6875rem !important;
  }
  .p-sm-31 {
    padding: 10.625rem !important;
  }
  .pt-sm-31,
  .py-sm-31 {
    padding-top: 10.625rem !important;
  }
  .pr-sm-31,
  .px-sm-31 {
    padding-right: 10.625rem !important;
  }
  .pb-sm-31,
  .py-sm-31 {
    padding-bottom: 10.625rem !important;
  }
  .pl-sm-31,
  .px-sm-31 {
    padding-left: 10.625rem !important;
  }
  .p-sm-32 {
    padding: 11.25rem !important;
  }
  .pt-sm-32,
  .py-sm-32 {
    padding-top: 11.25rem !important;
  }
  .pr-sm-32,
  .px-sm-32 {
    padding-right: 11.25rem !important;
  }
  .pb-sm-32,
  .py-sm-32 {
    padding-bottom: 11.25rem !important;
  }
  .pl-sm-32,
  .px-sm-32 {
    padding-left: 11.25rem !important;
  }
  .p-sm-33 {
    padding: 12.5rem !important;
  }
  .pt-sm-33,
  .py-sm-33 {
    padding-top: 12.5rem !important;
  }
  .pr-sm-33,
  .px-sm-33 {
    padding-right: 12.5rem !important;
  }
  .pb-sm-33,
  .py-sm-33 {
    padding-bottom: 12.5rem !important;
  }
  .pl-sm-33,
  .px-sm-33 {
    padding-left: 12.5rem !important;
  }
  .p-sm-34 {
    padding: 14.0625rem !important;
  }
  .pt-sm-34,
  .py-sm-34 {
    padding-top: 14.0625rem !important;
  }
  .pr-sm-34,
  .px-sm-34 {
    padding-right: 14.0625rem !important;
  }
  .pb-sm-34,
  .py-sm-34 {
    padding-bottom: 14.0625rem !important;
  }
  .pl-sm-34,
  .px-sm-34 {
    padding-left: 14.0625rem !important;
  }
  .p-sm-35 {
    padding: 15.625rem !important;
  }
  .pt-sm-35,
  .py-sm-35 {
    padding-top: 15.625rem !important;
  }
  .pr-sm-35,
  .px-sm-35 {
    padding-right: 15.625rem !important;
  }
  .pb-sm-35,
  .py-sm-35 {
    padding-bottom: 15.625rem !important;
  }
  .pl-sm-35,
  .px-sm-35 {
    padding-left: 15.625rem !important;
  }
  .m-sm-n1 {
    margin: -0.25rem !important;
  }
  .mt-sm-n1,
  .my-sm-n1 {
    margin-top: -0.25rem !important;
  }
  .mr-sm-n1,
  .mx-sm-n1 {
    margin-right: -0.25rem !important;
  }
  .mb-sm-n1,
  .my-sm-n1 {
    margin-bottom: -0.25rem !important;
  }
  .ml-sm-n1,
  .mx-sm-n1 {
    margin-left: -0.25rem !important;
  }
  .m-sm-n2 {
    margin: -0.375rem !important;
  }
  .mt-sm-n2,
  .my-sm-n2 {
    margin-top: -0.375rem !important;
  }
  .mr-sm-n2,
  .mx-sm-n2 {
    margin-right: -0.375rem !important;
  }
  .mb-sm-n2,
  .my-sm-n2 {
    margin-bottom: -0.375rem !important;
  }
  .ml-sm-n2,
  .mx-sm-n2 {
    margin-left: -0.375rem !important;
  }
  .m-sm-n3 {
    margin: -0.5rem !important;
  }
  .mt-sm-n3,
  .my-sm-n3 {
    margin-top: -0.5rem !important;
  }
  .mr-sm-n3,
  .mx-sm-n3 {
    margin-right: -0.5rem !important;
  }
  .mb-sm-n3,
  .my-sm-n3 {
    margin-bottom: -0.5rem !important;
  }
  .ml-sm-n3,
  .mx-sm-n3 {
    margin-left: -0.5rem !important;
  }
  .m-sm-n4 {
    margin: -0.625rem !important;
  }
  .mt-sm-n4,
  .my-sm-n4 {
    margin-top: -0.625rem !important;
  }
  .mr-sm-n4,
  .mx-sm-n4 {
    margin-right: -0.625rem !important;
  }
  .mb-sm-n4,
  .my-sm-n4 {
    margin-bottom: -0.625rem !important;
  }
  .ml-sm-n4,
  .mx-sm-n4 {
    margin-left: -0.625rem !important;
  }
  .m-sm-n5 {
    margin: -0.75rem !important;
  }
  .mt-sm-n5,
  .my-sm-n5 {
    margin-top: -0.75rem !important;
  }
  .mr-sm-n5,
  .mx-sm-n5 {
    margin-right: -0.75rem !important;
  }
  .mb-sm-n5,
  .my-sm-n5 {
    margin-bottom: -0.75rem !important;
  }
  .ml-sm-n5,
  .mx-sm-n5 {
    margin-left: -0.75rem !important;
  }
  .m-sm-n6 {
    margin: -1rem !important;
  }
  .mt-sm-n6,
  .my-sm-n6 {
    margin-top: -1rem !important;
  }
  .mr-sm-n6,
  .mx-sm-n6 {
    margin-right: -1rem !important;
  }
  .mb-sm-n6,
  .my-sm-n6 {
    margin-bottom: -1rem !important;
  }
  .ml-sm-n6,
  .mx-sm-n6 {
    margin-left: -1rem !important;
  }
  .m-sm-n7 {
    margin: -1.25rem !important;
  }
  .mt-sm-n7,
  .my-sm-n7 {
    margin-top: -1.25rem !important;
  }
  .mr-sm-n7,
  .mx-sm-n7 {
    margin-right: -1.25rem !important;
  }
  .mb-sm-n7,
  .my-sm-n7 {
    margin-bottom: -1.25rem !important;
  }
  .ml-sm-n7,
  .mx-sm-n7 {
    margin-left: -1.25rem !important;
  }
  .m-sm-n8 {
    margin: -1.5625rem !important;
  }
  .mt-sm-n8,
  .my-sm-n8 {
    margin-top: -1.5625rem !important;
  }
  .mr-sm-n8,
  .mx-sm-n8 {
    margin-right: -1.5625rem !important;
  }
  .mb-sm-n8,
  .my-sm-n8 {
    margin-bottom: -1.5625rem !important;
  }
  .ml-sm-n8,
  .mx-sm-n8 {
    margin-left: -1.5625rem !important;
  }
  .m-sm-n9 {
    margin: -1.875rem !important;
  }
  .mt-sm-n9,
  .my-sm-n9 {
    margin-top: -1.875rem !important;
  }
  .mr-sm-n9,
  .mx-sm-n9 {
    margin-right: -1.875rem !important;
  }
  .mb-sm-n9,
  .my-sm-n9 {
    margin-bottom: -1.875rem !important;
  }
  .ml-sm-n9,
  .mx-sm-n9 {
    margin-left: -1.875rem !important;
  }
  .m-sm-n10 {
    margin: -2.1875rem !important;
  }
  .mt-sm-n10,
  .my-sm-n10 {
    margin-top: -2.1875rem !important;
  }
  .mr-sm-n10,
  .mx-sm-n10 {
    margin-right: -2.1875rem !important;
  }
  .mb-sm-n10,
  .my-sm-n10 {
    margin-bottom: -2.1875rem !important;
  }
  .ml-sm-n10,
  .mx-sm-n10 {
    margin-left: -2.1875rem !important;
  }
  .m-sm-n11 {
    margin: -2.5rem !important;
  }
  .mt-sm-n11,
  .my-sm-n11 {
    margin-top: -2.5rem !important;
  }
  .mr-sm-n11,
  .mx-sm-n11 {
    margin-right: -2.5rem !important;
  }
  .mb-sm-n11,
  .my-sm-n11 {
    margin-bottom: -2.5rem !important;
  }
  .ml-sm-n11,
  .mx-sm-n11 {
    margin-left: -2.5rem !important;
  }
  .m-sm-n12 {
    margin: -2.8125rem !important;
  }
  .mt-sm-n12,
  .my-sm-n12 {
    margin-top: -2.8125rem !important;
  }
  .mr-sm-n12,
  .mx-sm-n12 {
    margin-right: -2.8125rem !important;
  }
  .mb-sm-n12,
  .my-sm-n12 {
    margin-bottom: -2.8125rem !important;
  }
  .ml-sm-n12,
  .mx-sm-n12 {
    margin-left: -2.8125rem !important;
  }
  .m-sm-n13 {
    margin: -3.125rem !important;
  }
  .mt-sm-n13,
  .my-sm-n13 {
    margin-top: -3.125rem !important;
  }
  .mr-sm-n13,
  .mx-sm-n13 {
    margin-right: -3.125rem !important;
  }
  .mb-sm-n13,
  .my-sm-n13 {
    margin-bottom: -3.125rem !important;
  }
  .ml-sm-n13,
  .mx-sm-n13 {
    margin-left: -3.125rem !important;
  }
  .m-sm-n14 {
    margin: -3.4375rem !important;
  }
  .mt-sm-n14,
  .my-sm-n14 {
    margin-top: -3.4375rem !important;
  }
  .mr-sm-n14,
  .mx-sm-n14 {
    margin-right: -3.4375rem !important;
  }
  .mb-sm-n14,
  .my-sm-n14 {
    margin-bottom: -3.4375rem !important;
  }
  .ml-sm-n14,
  .mx-sm-n14 {
    margin-left: -3.4375rem !important;
  }
  .m-sm-n15 {
    margin: -3.75rem !important;
  }
  .mt-sm-n15,
  .my-sm-n15 {
    margin-top: -3.75rem !important;
  }
  .mr-sm-n15,
  .mx-sm-n15 {
    margin-right: -3.75rem !important;
  }
  .mb-sm-n15,
  .my-sm-n15 {
    margin-bottom: -3.75rem !important;
  }
  .ml-sm-n15,
  .mx-sm-n15 {
    margin-left: -3.75rem !important;
  }
  .m-sm-n16 {
    margin: -4.0625rem !important;
  }
  .mt-sm-n16,
  .my-sm-n16 {
    margin-top: -4.0625rem !important;
  }
  .mr-sm-n16,
  .mx-sm-n16 {
    margin-right: -4.0625rem !important;
  }
  .mb-sm-n16,
  .my-sm-n16 {
    margin-bottom: -4.0625rem !important;
  }
  .ml-sm-n16,
  .mx-sm-n16 {
    margin-left: -4.0625rem !important;
  }
  .m-sm-n17 {
    margin: -4.375rem !important;
  }
  .mt-sm-n17,
  .my-sm-n17 {
    margin-top: -4.375rem !important;
  }
  .mr-sm-n17,
  .mx-sm-n17 {
    margin-right: -4.375rem !important;
  }
  .mb-sm-n17,
  .my-sm-n17 {
    margin-bottom: -4.375rem !important;
  }
  .ml-sm-n17,
  .mx-sm-n17 {
    margin-left: -4.375rem !important;
  }
  .m-sm-n18 {
    margin: -4.6875rem !important;
  }
  .mt-sm-n18,
  .my-sm-n18 {
    margin-top: -4.6875rem !important;
  }
  .mr-sm-n18,
  .mx-sm-n18 {
    margin-right: -4.6875rem !important;
  }
  .mb-sm-n18,
  .my-sm-n18 {
    margin-bottom: -4.6875rem !important;
  }
  .ml-sm-n18,
  .mx-sm-n18 {
    margin-left: -4.6875rem !important;
  }
  .m-sm-n19 {
    margin: -5rem !important;
  }
  .mt-sm-n19,
  .my-sm-n19 {
    margin-top: -5rem !important;
  }
  .mr-sm-n19,
  .mx-sm-n19 {
    margin-right: -5rem !important;
  }
  .mb-sm-n19,
  .my-sm-n19 {
    margin-bottom: -5rem !important;
  }
  .ml-sm-n19,
  .mx-sm-n19 {
    margin-left: -5rem !important;
  }
  .m-sm-n20 {
    margin: -5.3125rem !important;
  }
  .mt-sm-n20,
  .my-sm-n20 {
    margin-top: -5.3125rem !important;
  }
  .mr-sm-n20,
  .mx-sm-n20 {
    margin-right: -5.3125rem !important;
  }
  .mb-sm-n20,
  .my-sm-n20 {
    margin-bottom: -5.3125rem !important;
  }
  .ml-sm-n20,
  .mx-sm-n20 {
    margin-left: -5.3125rem !important;
  }
  .m-sm-n21 {
    margin: -5.625rem !important;
  }
  .mt-sm-n21,
  .my-sm-n21 {
    margin-top: -5.625rem !important;
  }
  .mr-sm-n21,
  .mx-sm-n21 {
    margin-right: -5.625rem !important;
  }
  .mb-sm-n21,
  .my-sm-n21 {
    margin-bottom: -5.625rem !important;
  }
  .ml-sm-n21,
  .mx-sm-n21 {
    margin-left: -5.625rem !important;
  }
  .m-sm-n22 {
    margin: -5.9375rem !important;
  }
  .mt-sm-n22,
  .my-sm-n22 {
    margin-top: -5.9375rem !important;
  }
  .mr-sm-n22,
  .mx-sm-n22 {
    margin-right: -5.9375rem !important;
  }
  .mb-sm-n22,
  .my-sm-n22 {
    margin-bottom: -5.9375rem !important;
  }
  .ml-sm-n22,
  .mx-sm-n22 {
    margin-left: -5.9375rem !important;
  }
  .m-sm-n23 {
    margin: -6.25rem !important;
  }
  .mt-sm-n23,
  .my-sm-n23 {
    margin-top: -6.25rem !important;
  }
  .mr-sm-n23,
  .mx-sm-n23 {
    margin-right: -6.25rem !important;
  }
  .mb-sm-n23,
  .my-sm-n23 {
    margin-bottom: -6.25rem !important;
  }
  .ml-sm-n23,
  .mx-sm-n23 {
    margin-left: -6.25rem !important;
  }
  .m-sm-n24 {
    margin: -6.875rem !important;
  }
  .mt-sm-n24,
  .my-sm-n24 {
    margin-top: -6.875rem !important;
  }
  .mr-sm-n24,
  .mx-sm-n24 {
    margin-right: -6.875rem !important;
  }
  .mb-sm-n24,
  .my-sm-n24 {
    margin-bottom: -6.875rem !important;
  }
  .ml-sm-n24,
  .mx-sm-n24 {
    margin-left: -6.875rem !important;
  }
  .m-sm-n25 {
    margin: -7.5rem !important;
  }
  .mt-sm-n25,
  .my-sm-n25 {
    margin-top: -7.5rem !important;
  }
  .mr-sm-n25,
  .mx-sm-n25 {
    margin-right: -7.5rem !important;
  }
  .mb-sm-n25,
  .my-sm-n25 {
    margin-bottom: -7.5rem !important;
  }
  .ml-sm-n25,
  .mx-sm-n25 {
    margin-left: -7.5rem !important;
  }
  .m-sm-n26 {
    margin: -8.125rem !important;
  }
  .mt-sm-n26,
  .my-sm-n26 {
    margin-top: -8.125rem !important;
  }
  .mr-sm-n26,
  .mx-sm-n26 {
    margin-right: -8.125rem !important;
  }
  .mb-sm-n26,
  .my-sm-n26 {
    margin-bottom: -8.125rem !important;
  }
  .ml-sm-n26,
  .mx-sm-n26 {
    margin-left: -8.125rem !important;
  }
  .m-sm-n27 {
    margin: -8.4375rem !important;
  }
  .mt-sm-n27,
  .my-sm-n27 {
    margin-top: -8.4375rem !important;
  }
  .mr-sm-n27,
  .mx-sm-n27 {
    margin-right: -8.4375rem !important;
  }
  .mb-sm-n27,
  .my-sm-n27 {
    margin-bottom: -8.4375rem !important;
  }
  .ml-sm-n27,
  .mx-sm-n27 {
    margin-left: -8.4375rem !important;
  }
  .m-sm-n28 {
    margin: -9.0625rem !important;
  }
  .mt-sm-n28,
  .my-sm-n28 {
    margin-top: -9.0625rem !important;
  }
  .mr-sm-n28,
  .mx-sm-n28 {
    margin-right: -9.0625rem !important;
  }
  .mb-sm-n28,
  .my-sm-n28 {
    margin-bottom: -9.0625rem !important;
  }
  .ml-sm-n28,
  .mx-sm-n28 {
    margin-left: -9.0625rem !important;
  }
  .m-sm-n29 {
    margin: -9.375rem !important;
  }
  .mt-sm-n29,
  .my-sm-n29 {
    margin-top: -9.375rem !important;
  }
  .mr-sm-n29,
  .mx-sm-n29 {
    margin-right: -9.375rem !important;
  }
  .mb-sm-n29,
  .my-sm-n29 {
    margin-bottom: -9.375rem !important;
  }
  .ml-sm-n29,
  .mx-sm-n29 {
    margin-left: -9.375rem !important;
  }
  .m-sm-n30 {
    margin: -9.6875rem !important;
  }
  .mt-sm-n30,
  .my-sm-n30 {
    margin-top: -9.6875rem !important;
  }
  .mr-sm-n30,
  .mx-sm-n30 {
    margin-right: -9.6875rem !important;
  }
  .mb-sm-n30,
  .my-sm-n30 {
    margin-bottom: -9.6875rem !important;
  }
  .ml-sm-n30,
  .mx-sm-n30 {
    margin-left: -9.6875rem !important;
  }
  .m-sm-n31 {
    margin: -10.625rem !important;
  }
  .mt-sm-n31,
  .my-sm-n31 {
    margin-top: -10.625rem !important;
  }
  .mr-sm-n31,
  .mx-sm-n31 {
    margin-right: -10.625rem !important;
  }
  .mb-sm-n31,
  .my-sm-n31 {
    margin-bottom: -10.625rem !important;
  }
  .ml-sm-n31,
  .mx-sm-n31 {
    margin-left: -10.625rem !important;
  }
  .m-sm-n32 {
    margin: -11.25rem !important;
  }
  .mt-sm-n32,
  .my-sm-n32 {
    margin-top: -11.25rem !important;
  }
  .mr-sm-n32,
  .mx-sm-n32 {
    margin-right: -11.25rem !important;
  }
  .mb-sm-n32,
  .my-sm-n32 {
    margin-bottom: -11.25rem !important;
  }
  .ml-sm-n32,
  .mx-sm-n32 {
    margin-left: -11.25rem !important;
  }
  .m-sm-n33 {
    margin: -12.5rem !important;
  }
  .mt-sm-n33,
  .my-sm-n33 {
    margin-top: -12.5rem !important;
  }
  .mr-sm-n33,
  .mx-sm-n33 {
    margin-right: -12.5rem !important;
  }
  .mb-sm-n33,
  .my-sm-n33 {
    margin-bottom: -12.5rem !important;
  }
  .ml-sm-n33,
  .mx-sm-n33 {
    margin-left: -12.5rem !important;
  }
  .m-sm-n34 {
    margin: -14.0625rem !important;
  }
  .mt-sm-n34,
  .my-sm-n34 {
    margin-top: -14.0625rem !important;
  }
  .mr-sm-n34,
  .mx-sm-n34 {
    margin-right: -14.0625rem !important;
  }
  .mb-sm-n34,
  .my-sm-n34 {
    margin-bottom: -14.0625rem !important;
  }
  .ml-sm-n34,
  .mx-sm-n34 {
    margin-left: -14.0625rem !important;
  }
  .m-sm-n35 {
    margin: -15.625rem !important;
  }
  .mt-sm-n35,
  .my-sm-n35 {
    margin-top: -15.625rem !important;
  }
  .mr-sm-n35,
  .mx-sm-n35 {
    margin-right: -15.625rem !important;
  }
  .mb-sm-n35,
  .my-sm-n35 {
    margin-bottom: -15.625rem !important;
  }
  .ml-sm-n35,
  .mx-sm-n35 {
    margin-left: -15.625rem !important;
  }
  .m-sm-auto {
    margin: auto !important;
  }
  .mt-sm-auto,
  .my-sm-auto {
    margin-top: auto !important;
  }
  .mr-sm-auto,
  .mx-sm-auto {
    margin-right: auto !important;
  }
  .mb-sm-auto,
  .my-sm-auto {
    margin-bottom: auto !important;
  }
  .ml-sm-auto,
  .mx-sm-auto {
    margin-left: auto !important;
  }
}

@media (min-width: 768px) {
  .m-md-0 {
    margin: 0 !important;
  }
  .mt-md-0,
  .my-md-0 {
    margin-top: 0 !important;
  }
  .mr-md-0,
  .mx-md-0 {
    margin-right: 0 !important;
  }
  .mb-md-0,
  .my-md-0 {
    margin-bottom: 0 !important;
  }
  .ml-md-0,
  .mx-md-0 {
    margin-left: 0 !important;
  }
  .m-md-1 {
    margin: 0.25rem !important;
  }
  .mt-md-1,
  .my-md-1 {
    margin-top: 0.25rem !important;
  }
  .mr-md-1,
  .mx-md-1 {
    margin-right: 0.25rem !important;
  }
  .mb-md-1,
  .my-md-1 {
    margin-bottom: 0.25rem !important;
  }
  .ml-md-1,
  .mx-md-1 {
    margin-left: 0.25rem !important;
  }
  .m-md-2 {
    margin: 0.375rem !important;
  }
  .mt-md-2,
  .my-md-2 {
    margin-top: 0.375rem !important;
  }
  .mr-md-2,
  .mx-md-2 {
    margin-right: 0.375rem !important;
  }
  .mb-md-2,
  .my-md-2 {
    margin-bottom: 0.375rem !important;
  }
  .ml-md-2,
  .mx-md-2 {
    margin-left: 0.375rem !important;
  }
  .m-md-3 {
    margin: 0.5rem !important;
  }
  .mt-md-3,
  .my-md-3 {
    margin-top: 0.5rem !important;
  }
  .mr-md-3,
  .mx-md-3 {
    margin-right: 0.5rem !important;
  }
  .mb-md-3,
  .my-md-3 {
    margin-bottom: 0.5rem !important;
  }
  .ml-md-3,
  .mx-md-3 {
    margin-left: 0.5rem !important;
  }
  .m-md-4 {
    margin: 0.625rem !important;
  }
  .mt-md-4,
  .my-md-4 {
    margin-top: 0.625rem !important;
  }
  .mr-md-4,
  .mx-md-4 {
    margin-right: 0.625rem !important;
  }
  .mb-md-4,
  .my-md-4 {
    margin-bottom: 0.625rem !important;
  }
  .ml-md-4,
  .mx-md-4 {
    margin-left: 0.625rem !important;
  }
  .m-md-5 {
    margin: 0.75rem !important;
  }
  .mt-md-5,
  .my-md-5 {
    margin-top: 0.75rem !important;
  }
  .mr-md-5,
  .mx-md-5 {
    margin-right: 0.75rem !important;
  }
  .mb-md-5,
  .my-md-5 {
    margin-bottom: 0.75rem !important;
  }
  .ml-md-5,
  .mx-md-5 {
    margin-left: 0.75rem !important;
  }
  .m-md-6 {
    margin: 1rem !important;
  }
  .mt-md-6,
  .my-md-6 {
    margin-top: 1rem !important;
  }
  .mr-md-6,
  .mx-md-6 {
    margin-right: 1rem !important;
  }
  .mb-md-6,
  .my-md-6 {
    margin-bottom: 1rem !important;
  }
  .ml-md-6,
  .mx-md-6 {
    margin-left: 1rem !important;
  }
  .m-md-7 {
    margin: 1.25rem !important;
  }
  .mt-md-7,
  .my-md-7 {
    margin-top: 1.25rem !important;
  }
  .mr-md-7,
  .mx-md-7 {
    margin-right: 1.25rem !important;
  }
  .mb-md-7,
  .my-md-7 {
    margin-bottom: 1.25rem !important;
  }
  .ml-md-7,
  .mx-md-7 {
    margin-left: 1.25rem !important;
  }
  .m-md-8 {
    margin: 1.5625rem !important;
  }
  .mt-md-8,
  .my-md-8 {
    margin-top: 1.5625rem !important;
  }
  .mr-md-8,
  .mx-md-8 {
    margin-right: 1.5625rem !important;
  }
  .mb-md-8,
  .my-md-8 {
    margin-bottom: 1.5625rem !important;
  }
  .ml-md-8,
  .mx-md-8 {
    margin-left: 1.5625rem !important;
  }
  .m-md-9 {
    margin: 1.875rem !important;
  }
  .mt-md-9,
  .my-md-9 {
    margin-top: 1.875rem !important;
  }
  .mr-md-9,
  .mx-md-9 {
    margin-right: 1.875rem !important;
  }
  .mb-md-9,
  .my-md-9 {
    margin-bottom: 1.875rem !important;
  }
  .ml-md-9,
  .mx-md-9 {
    margin-left: 1.875rem !important;
  }
  .m-md-10 {
    margin: 2.1875rem !important;
  }
  .mt-md-10,
  .my-md-10 {
    margin-top: 2.1875rem !important;
  }
  .mr-md-10,
  .mx-md-10 {
    margin-right: 2.1875rem !important;
  }
  .mb-md-10,
  .my-md-10 {
    margin-bottom: 2.1875rem !important;
  }
  .ml-md-10,
  .mx-md-10 {
    margin-left: 2.1875rem !important;
  }
  .m-md-11 {
    margin: 2.5rem !important;
  }
  .mt-md-11,
  .my-md-11 {
    margin-top: 2.5rem !important;
  }
  .mr-md-11,
  .mx-md-11 {
    margin-right: 2.5rem !important;
  }
  .mb-md-11,
  .my-md-11 {
    margin-bottom: 2.5rem !important;
  }
  .ml-md-11,
  .mx-md-11 {
    margin-left: 2.5rem !important;
  }
  .m-md-12 {
    margin: 2.8125rem !important;
  }
  .mt-md-12,
  .my-md-12 {
    margin-top: 2.8125rem !important;
  }
  .mr-md-12,
  .mx-md-12 {
    margin-right: 2.8125rem !important;
  }
  .mb-md-12,
  .my-md-12 {
    margin-bottom: 2.8125rem !important;
  }
  .ml-md-12,
  .mx-md-12 {
    margin-left: 2.8125rem !important;
  }
  .m-md-13 {
    margin: 3.125rem !important;
  }
  .mt-md-13,
  .my-md-13 {
    margin-top: 3.125rem !important;
  }
  .mr-md-13,
  .mx-md-13 {
    margin-right: 3.125rem !important;
  }
  .mb-md-13,
  .my-md-13 {
    margin-bottom: 3.125rem !important;
  }
  .ml-md-13,
  .mx-md-13 {
    margin-left: 3.125rem !important;
  }
  .m-md-14 {
    margin: 3.4375rem !important;
  }
  .mt-md-14,
  .my-md-14 {
    margin-top: 3.4375rem !important;
  }
  .mr-md-14,
  .mx-md-14 {
    margin-right: 3.4375rem !important;
  }
  .mb-md-14,
  .my-md-14 {
    margin-bottom: 3.4375rem !important;
  }
  .ml-md-14,
  .mx-md-14 {
    margin-left: 3.4375rem !important;
  }
  .m-md-15 {
    margin: 3.75rem !important;
  }
  .mt-md-15,
  .my-md-15 {
    margin-top: 3.75rem !important;
  }
  .mr-md-15,
  .mx-md-15 {
    margin-right: 3.75rem !important;
  }
  .mb-md-15,
  .my-md-15 {
    margin-bottom: 3.75rem !important;
  }
  .ml-md-15,
  .mx-md-15 {
    margin-left: 3.75rem !important;
  }
  .m-md-16 {
    margin: 4.0625rem !important;
  }
  .mt-md-16,
  .my-md-16 {
    margin-top: 4.0625rem !important;
  }
  .mr-md-16,
  .mx-md-16 {
    margin-right: 4.0625rem !important;
  }
  .mb-md-16,
  .my-md-16 {
    margin-bottom: 4.0625rem !important;
  }
  .ml-md-16,
  .mx-md-16 {
    margin-left: 4.0625rem !important;
  }
  .m-md-17 {
    margin: 4.375rem !important;
  }
  .mt-md-17,
  .my-md-17 {
    margin-top: 4.375rem !important;
  }
  .mr-md-17,
  .mx-md-17 {
    margin-right: 4.375rem !important;
  }
  .mb-md-17,
  .my-md-17 {
    margin-bottom: 4.375rem !important;
  }
  .ml-md-17,
  .mx-md-17 {
    margin-left: 4.375rem !important;
  }
  .m-md-18 {
    margin: 4.6875rem !important;
  }
  .mt-md-18,
  .my-md-18 {
    margin-top: 4.6875rem !important;
  }
  .mr-md-18,
  .mx-md-18 {
    margin-right: 4.6875rem !important;
  }
  .mb-md-18,
  .my-md-18 {
    margin-bottom: 4.6875rem !important;
  }
  .ml-md-18,
  .mx-md-18 {
    margin-left: 4.6875rem !important;
  }
  .m-md-19 {
    margin: 5rem !important;
  }
  .mt-md-19,
  .my-md-19 {
    margin-top: 5rem !important;
  }
  .mr-md-19,
  .mx-md-19 {
    margin-right: 5rem !important;
  }
  .mb-md-19,
  .my-md-19 {
    margin-bottom: 5rem !important;
  }
  .ml-md-19,
  .mx-md-19 {
    margin-left: 5rem !important;
  }
  .m-md-20 {
    margin: 5.3125rem !important;
  }
  .mt-md-20,
  .my-md-20 {
    margin-top: 5.3125rem !important;
  }
  .mr-md-20,
  .mx-md-20 {
    margin-right: 5.3125rem !important;
  }
  .mb-md-20,
  .my-md-20 {
    margin-bottom: 5.3125rem !important;
  }
  .ml-md-20,
  .mx-md-20 {
    margin-left: 5.3125rem !important;
  }
  .m-md-21 {
    margin: 5.625rem !important;
  }
  .mt-md-21,
  .my-md-21 {
    margin-top: 5.625rem !important;
  }
  .mr-md-21,
  .mx-md-21 {
    margin-right: 5.625rem !important;
  }
  .mb-md-21,
  .my-md-21 {
    margin-bottom: 5.625rem !important;
  }
  .ml-md-21,
  .mx-md-21 {
    margin-left: 5.625rem !important;
  }
  .m-md-22 {
    margin: 5.9375rem !important;
  }
  .mt-md-22,
  .my-md-22 {
    margin-top: 5.9375rem !important;
  }
  .mr-md-22,
  .mx-md-22 {
    margin-right: 5.9375rem !important;
  }
  .mb-md-22,
  .my-md-22 {
    margin-bottom: 5.9375rem !important;
  }
  .ml-md-22,
  .mx-md-22 {
    margin-left: 5.9375rem !important;
  }
  .m-md-23 {
    margin: 6.25rem !important;
  }
  .mt-md-23,
  .my-md-23 {
    margin-top: 6.25rem !important;
  }
  .mr-md-23,
  .mx-md-23 {
    margin-right: 6.25rem !important;
  }
  .mb-md-23,
  .my-md-23 {
    margin-bottom: 6.25rem !important;
  }
  .ml-md-23,
  .mx-md-23 {
    margin-left: 6.25rem !important;
  }
  .m-md-24 {
    margin: 6.875rem !important;
  }
  .mt-md-24,
  .my-md-24 {
    margin-top: 6.875rem !important;
  }
  .mr-md-24,
  .mx-md-24 {
    margin-right: 6.875rem !important;
  }
  .mb-md-24,
  .my-md-24 {
    margin-bottom: 6.875rem !important;
  }
  .ml-md-24,
  .mx-md-24 {
    margin-left: 6.875rem !important;
  }
  .m-md-25 {
    margin: 7.5rem !important;
  }
  .mt-md-25,
  .my-md-25 {
    margin-top: 7.5rem !important;
  }
  .mr-md-25,
  .mx-md-25 {
    margin-right: 7.5rem !important;
  }
  .mb-md-25,
  .my-md-25 {
    margin-bottom: 7.5rem !important;
  }
  .ml-md-25,
  .mx-md-25 {
    margin-left: 7.5rem !important;
  }
  .m-md-26 {
    margin: 8.125rem !important;
  }
  .mt-md-26,
  .my-md-26 {
    margin-top: 8.125rem !important;
  }
  .mr-md-26,
  .mx-md-26 {
    margin-right: 8.125rem !important;
  }
  .mb-md-26,
  .my-md-26 {
    margin-bottom: 8.125rem !important;
  }
  .ml-md-26,
  .mx-md-26 {
    margin-left: 8.125rem !important;
  }
  .m-md-27 {
    margin: 8.4375rem !important;
  }
  .mt-md-27,
  .my-md-27 {
    margin-top: 8.4375rem !important;
  }
  .mr-md-27,
  .mx-md-27 {
    margin-right: 8.4375rem !important;
  }
  .mb-md-27,
  .my-md-27 {
    margin-bottom: 8.4375rem !important;
  }
  .ml-md-27,
  .mx-md-27 {
    margin-left: 8.4375rem !important;
  }
  .m-md-28 {
    margin: 9.0625rem !important;
  }
  .mt-md-28,
  .my-md-28 {
    margin-top: 9.0625rem !important;
  }
  .mr-md-28,
  .mx-md-28 {
    margin-right: 9.0625rem !important;
  }
  .mb-md-28,
  .my-md-28 {
    margin-bottom: 9.0625rem !important;
  }
  .ml-md-28,
  .mx-md-28 {
    margin-left: 9.0625rem !important;
  }
  .m-md-29 {
    margin: 9.375rem !important;
  }
  .mt-md-29,
  .my-md-29 {
    margin-top: 9.375rem !important;
  }
  .mr-md-29,
  .mx-md-29 {
    margin-right: 9.375rem !important;
  }
  .mb-md-29,
  .my-md-29 {
    margin-bottom: 9.375rem !important;
  }
  .ml-md-29,
  .mx-md-29 {
    margin-left: 9.375rem !important;
  }
  .m-md-30 {
    margin: 9.6875rem !important;
  }
  .mt-md-30,
  .my-md-30 {
    margin-top: 9.6875rem !important;
  }
  .mr-md-30,
  .mx-md-30 {
    margin-right: 9.6875rem !important;
  }
  .mb-md-30,
  .my-md-30 {
    margin-bottom: 9.6875rem !important;
  }
  .ml-md-30,
  .mx-md-30 {
    margin-left: 9.6875rem !important;
  }
  .m-md-31 {
    margin: 10.625rem !important;
  }
  .mt-md-31,
  .my-md-31 {
    margin-top: 10.625rem !important;
  }
  .mr-md-31,
  .mx-md-31 {
    margin-right: 10.625rem !important;
  }
  .mb-md-31,
  .my-md-31 {
    margin-bottom: 10.625rem !important;
  }
  .ml-md-31,
  .mx-md-31 {
    margin-left: 10.625rem !important;
  }
  .m-md-32 {
    margin: 11.25rem !important;
  }
  .mt-md-32,
  .my-md-32 {
    margin-top: 11.25rem !important;
  }
  .mr-md-32,
  .mx-md-32 {
    margin-right: 11.25rem !important;
  }
  .mb-md-32,
  .my-md-32 {
    margin-bottom: 11.25rem !important;
  }
  .ml-md-32,
  .mx-md-32 {
    margin-left: 11.25rem !important;
  }
  .m-md-33 {
    margin: 12.5rem !important;
  }
  .mt-md-33,
  .my-md-33 {
    margin-top: 12.5rem !important;
  }
  .mr-md-33,
  .mx-md-33 {
    margin-right: 12.5rem !important;
  }
  .mb-md-33,
  .my-md-33 {
    margin-bottom: 12.5rem !important;
  }
  .ml-md-33,
  .mx-md-33 {
    margin-left: 12.5rem !important;
  }
  .m-md-34 {
    margin: 14.0625rem !important;
  }
  .mt-md-34,
  .my-md-34 {
    margin-top: 14.0625rem !important;
  }
  .mr-md-34,
  .mx-md-34 {
    margin-right: 14.0625rem !important;
  }
  .mb-md-34,
  .my-md-34 {
    margin-bottom: 14.0625rem !important;
  }
  .ml-md-34,
  .mx-md-34 {
    margin-left: 14.0625rem !important;
  }
  .m-md-35 {
    margin: 15.625rem !important;
  }
  .mt-md-35,
  .my-md-35 {
    margin-top: 15.625rem !important;
  }
  .mr-md-35,
  .mx-md-35 {
    margin-right: 15.625rem !important;
  }
  .mb-md-35,
  .my-md-35 {
    margin-bottom: 15.625rem !important;
  }
  .ml-md-35,
  .mx-md-35 {
    margin-left: 15.625rem !important;
  }
  .p-md-0 {
    padding: 0 !important;
  }
  .pt-md-0,
  .py-md-0 {
    padding-top: 0 !important;
  }
  .pr-md-0,
  .px-md-0 {
    padding-right: 0 !important;
  }
  .pb-md-0,
  .py-md-0 {
    padding-bottom: 0 !important;
  }
  .pl-md-0,
  .px-md-0 {
    padding-left: 0 !important;
  }
  .p-md-1 {
    padding: 0.25rem !important;
  }
  .pt-md-1,
  .py-md-1 {
    padding-top: 0.25rem !important;
  }
  .pr-md-1,
  .px-md-1 {
    padding-right: 0.25rem !important;
  }
  .pb-md-1,
  .py-md-1 {
    padding-bottom: 0.25rem !important;
  }
  .pl-md-1,
  .px-md-1 {
    padding-left: 0.25rem !important;
  }
  .p-md-2 {
    padding: 0.375rem !important;
  }
  .pt-md-2,
  .py-md-2 {
    padding-top: 0.375rem !important;
  }
  .pr-md-2,
  .px-md-2 {
    padding-right: 0.375rem !important;
  }
  .pb-md-2,
  .py-md-2 {
    padding-bottom: 0.375rem !important;
  }
  .pl-md-2,
  .px-md-2 {
    padding-left: 0.375rem !important;
  }
  .p-md-3 {
    padding: 0.5rem !important;
  }
  .pt-md-3,
  .py-md-3 {
    padding-top: 0.5rem !important;
  }
  .pr-md-3,
  .px-md-3 {
    padding-right: 0.5rem !important;
  }
  .pb-md-3,
  .py-md-3 {
    padding-bottom: 0.5rem !important;
  }
  .pl-md-3,
  .px-md-3 {
    padding-left: 0.5rem !important;
  }
  .p-md-4 {
    padding: 0.625rem !important;
  }
  .pt-md-4,
  .py-md-4 {
    padding-top: 0.625rem !important;
  }
  .pr-md-4,
  .px-md-4 {
    padding-right: 0.625rem !important;
  }
  .pb-md-4,
  .py-md-4 {
    padding-bottom: 0.625rem !important;
  }
  .pl-md-4,
  .px-md-4 {
    padding-left: 0.625rem !important;
  }
  .p-md-5 {
    padding: 0.75rem !important;
  }
  .pt-md-5,
  .py-md-5 {
    padding-top: 0.75rem !important;
  }
  .pr-md-5,
  .px-md-5 {
    padding-right: 0.75rem !important;
  }
  .pb-md-5,
  .py-md-5 {
    padding-bottom: 0.75rem !important;
  }
  .pl-md-5,
  .px-md-5 {
    padding-left: 0.75rem !important;
  }
  .p-md-6 {
    padding: 1rem !important;
  }
  .pt-md-6,
  .py-md-6 {
    padding-top: 1rem !important;
  }
  .pr-md-6,
  .px-md-6 {
    padding-right: 1rem !important;
  }
  .pb-md-6,
  .py-md-6 {
    padding-bottom: 1rem !important;
  }
  .pl-md-6,
  .px-md-6 {
    padding-left: 1rem !important;
  }
  .p-md-7 {
    padding: 1.25rem !important;
  }
  .pt-md-7,
  .py-md-7 {
    padding-top: 1.25rem !important;
  }
  .pr-md-7,
  .px-md-7 {
    padding-right: 1.25rem !important;
  }
  .pb-md-7,
  .py-md-7 {
    padding-bottom: 1.25rem !important;
  }
  .pl-md-7,
  .px-md-7 {
    padding-left: 1.25rem !important;
  }
  .p-md-8 {
    padding: 1.5625rem !important;
  }
  .pt-md-8,
  .py-md-8 {
    padding-top: 1.5625rem !important;
  }
  .pr-md-8,
  .px-md-8 {
    padding-right: 1.5625rem !important;
  }
  .pb-md-8,
  .py-md-8 {
    padding-bottom: 1.5625rem !important;
  }
  .pl-md-8,
  .px-md-8 {
    padding-left: 1.5625rem !important;
  }
  .p-md-9 {
    padding: 1.875rem !important;
  }
  .pt-md-9,
  .py-md-9 {
    padding-top: 1.875rem !important;
  }
  .pr-md-9,
  .px-md-9 {
    padding-right: 1.875rem !important;
  }
  .pb-md-9,
  .py-md-9 {
    padding-bottom: 1.875rem !important;
  }
  .pl-md-9,
  .px-md-9 {
    padding-left: 1.875rem !important;
  }
  .p-md-10 {
    padding: 2.1875rem !important;
  }
  .pt-md-10,
  .py-md-10 {
    padding-top: 2.1875rem !important;
  }
  .pr-md-10,
  .px-md-10 {
    padding-right: 2.1875rem !important;
  }
  .pb-md-10,
  .py-md-10 {
    padding-bottom: 2.1875rem !important;
  }
  .pl-md-10,
  .px-md-10 {
    padding-left: 2.1875rem !important;
  }
  .p-md-11 {
    padding: 2.5rem !important;
  }
  .pt-md-11,
  .py-md-11 {
    padding-top: 2.5rem !important;
  }
  .pr-md-11,
  .px-md-11 {
    padding-right: 2.5rem !important;
  }
  .pb-md-11,
  .py-md-11 {
    padding-bottom: 2.5rem !important;
  }
  .pl-md-11,
  .px-md-11 {
    padding-left: 2.5rem !important;
  }
  .p-md-12 {
    padding: 2.8125rem !important;
  }
  .pt-md-12,
  .py-md-12 {
    padding-top: 2.8125rem !important;
  }
  .pr-md-12,
  .px-md-12 {
    padding-right: 2.8125rem !important;
  }
  .pb-md-12,
  .py-md-12 {
    padding-bottom: 2.8125rem !important;
  }
  .pl-md-12,
  .px-md-12 {
    padding-left: 2.8125rem !important;
  }
  .p-md-13 {
    padding: 3.125rem !important;
  }
  .pt-md-13,
  .py-md-13 {
    padding-top: 3.125rem !important;
  }
  .pr-md-13,
  .px-md-13 {
    padding-right: 3.125rem !important;
  }
  .pb-md-13,
  .py-md-13 {
    padding-bottom: 3.125rem !important;
  }
  .pl-md-13,
  .px-md-13 {
    padding-left: 3.125rem !important;
  }
  .p-md-14 {
    padding: 3.4375rem !important;
  }
  .pt-md-14,
  .py-md-14 {
    padding-top: 3.4375rem !important;
  }
  .pr-md-14,
  .px-md-14 {
    padding-right: 3.4375rem !important;
  }
  .pb-md-14,
  .py-md-14 {
    padding-bottom: 3.4375rem !important;
  }
  .pl-md-14,
  .px-md-14 {
    padding-left: 3.4375rem !important;
  }
  .p-md-15 {
    padding: 3.75rem !important;
  }
  .pt-md-15,
  .py-md-15 {
    padding-top: 3.75rem !important;
  }
  .pr-md-15,
  .px-md-15 {
    padding-right: 3.75rem !important;
  }
  .pb-md-15,
  .py-md-15 {
    padding-bottom: 3.75rem !important;
  }
  .pl-md-15,
  .px-md-15 {
    padding-left: 3.75rem !important;
  }
  .p-md-16 {
    padding: 4.0625rem !important;
  }
  .pt-md-16,
  .py-md-16 {
    padding-top: 4.0625rem !important;
  }
  .pr-md-16,
  .px-md-16 {
    padding-right: 4.0625rem !important;
  }
  .pb-md-16,
  .py-md-16 {
    padding-bottom: 4.0625rem !important;
  }
  .pl-md-16,
  .px-md-16 {
    padding-left: 4.0625rem !important;
  }
  .p-md-17 {
    padding: 4.375rem !important;
  }
  .pt-md-17,
  .py-md-17 {
    padding-top: 4.375rem !important;
  }
  .pr-md-17,
  .px-md-17 {
    padding-right: 4.375rem !important;
  }
  .pb-md-17,
  .py-md-17 {
    padding-bottom: 4.375rem !important;
  }
  .pl-md-17,
  .px-md-17 {
    padding-left: 4.375rem !important;
  }
  .p-md-18 {
    padding: 4.6875rem !important;
  }
  .pt-md-18,
  .py-md-18 {
    padding-top: 4.6875rem !important;
  }
  .pr-md-18,
  .px-md-18 {
    padding-right: 4.6875rem !important;
  }
  .pb-md-18,
  .py-md-18 {
    padding-bottom: 4.6875rem !important;
  }
  .pl-md-18,
  .px-md-18 {
    padding-left: 4.6875rem !important;
  }
  .p-md-19 {
    padding: 5rem !important;
  }
  .pt-md-19,
  .py-md-19 {
    padding-top: 5rem !important;
  }
  .pr-md-19,
  .px-md-19 {
    padding-right: 5rem !important;
  }
  .pb-md-19,
  .py-md-19 {
    padding-bottom: 5rem !important;
  }
  .pl-md-19,
  .px-md-19 {
    padding-left: 5rem !important;
  }
  .p-md-20 {
    padding: 5.3125rem !important;
  }
  .pt-md-20,
  .py-md-20 {
    padding-top: 5.3125rem !important;
  }
  .pr-md-20,
  .px-md-20 {
    padding-right: 5.3125rem !important;
  }
  .pb-md-20,
  .py-md-20 {
    padding-bottom: 5.3125rem !important;
  }
  .pl-md-20,
  .px-md-20 {
    padding-left: 5.3125rem !important;
  }
  .p-md-21 {
    padding: 5.625rem !important;
  }
  .pt-md-21,
  .py-md-21 {
    padding-top: 5.625rem !important;
  }
  .pr-md-21,
  .px-md-21 {
    padding-right: 5.625rem !important;
  }
  .pb-md-21,
  .py-md-21 {
    padding-bottom: 5.625rem !important;
  }
  .pl-md-21,
  .px-md-21 {
    padding-left: 5.625rem !important;
  }
  .p-md-22 {
    padding: 5.9375rem !important;
  }
  .pt-md-22,
  .py-md-22 {
    padding-top: 5.9375rem !important;
  }
  .pr-md-22,
  .px-md-22 {
    padding-right: 5.9375rem !important;
  }
  .pb-md-22,
  .py-md-22 {
    padding-bottom: 5.9375rem !important;
  }
  .pl-md-22,
  .px-md-22 {
    padding-left: 5.9375rem !important;
  }
  .p-md-23 {
    padding: 6.25rem !important;
  }
  .pt-md-23,
  .py-md-23 {
    padding-top: 6.25rem !important;
  }
  .pr-md-23,
  .px-md-23 {
    padding-right: 6.25rem !important;
  }
  .pb-md-23,
  .py-md-23 {
    padding-bottom: 6.25rem !important;
  }
  .pl-md-23,
  .px-md-23 {
    padding-left: 6.25rem !important;
  }
  .p-md-24 {
    padding: 6.875rem !important;
  }
  .pt-md-24,
  .py-md-24 {
    padding-top: 6.875rem !important;
  }
  .pr-md-24,
  .px-md-24 {
    padding-right: 6.875rem !important;
  }
  .pb-md-24,
  .py-md-24 {
    padding-bottom: 6.875rem !important;
  }
  .pl-md-24,
  .px-md-24 {
    padding-left: 6.875rem !important;
  }
  .p-md-25 {
    padding: 7.5rem !important;
  }
  .pt-md-25,
  .py-md-25 {
    padding-top: 7.5rem !important;
  }
  .pr-md-25,
  .px-md-25 {
    padding-right: 7.5rem !important;
  }
  .pb-md-25,
  .py-md-25 {
    padding-bottom: 7.5rem !important;
  }
  .pl-md-25,
  .px-md-25 {
    padding-left: 7.5rem !important;
  }
  .p-md-26 {
    padding: 8.125rem !important;
  }
  .pt-md-26,
  .py-md-26 {
    padding-top: 8.125rem !important;
  }
  .pr-md-26,
  .px-md-26 {
    padding-right: 8.125rem !important;
  }
  .pb-md-26,
  .py-md-26 {
    padding-bottom: 8.125rem !important;
  }
  .pl-md-26,
  .px-md-26 {
    padding-left: 8.125rem !important;
  }
  .p-md-27 {
    padding: 8.4375rem !important;
  }
  .pt-md-27,
  .py-md-27 {
    padding-top: 8.4375rem !important;
  }
  .pr-md-27,
  .px-md-27 {
    padding-right: 8.4375rem !important;
  }
  .pb-md-27,
  .py-md-27 {
    padding-bottom: 8.4375rem !important;
  }
  .pl-md-27,
  .px-md-27 {
    padding-left: 8.4375rem !important;
  }
  .p-md-28 {
    padding: 9.0625rem !important;
  }
  .pt-md-28,
  .py-md-28 {
    padding-top: 9.0625rem !important;
  }
  .pr-md-28,
  .px-md-28 {
    padding-right: 9.0625rem !important;
  }
  .pb-md-28,
  .py-md-28 {
    padding-bottom: 9.0625rem !important;
  }
  .pl-md-28,
  .px-md-28 {
    padding-left: 9.0625rem !important;
  }
  .p-md-29 {
    padding: 9.375rem !important;
  }
  .pt-md-29,
  .py-md-29 {
    padding-top: 9.375rem !important;
  }
  .pr-md-29,
  .px-md-29 {
    padding-right: 9.375rem !important;
  }
  .pb-md-29,
  .py-md-29 {
    padding-bottom: 9.375rem !important;
  }
  .pl-md-29,
  .px-md-29 {
    padding-left: 9.375rem !important;
  }
  .p-md-30 {
    padding: 9.6875rem !important;
  }
  .pt-md-30,
  .py-md-30 {
    padding-top: 9.6875rem !important;
  }
  .pr-md-30,
  .px-md-30 {
    padding-right: 9.6875rem !important;
  }
  .pb-md-30,
  .py-md-30 {
    padding-bottom: 9.6875rem !important;
  }
  .pl-md-30,
  .px-md-30 {
    padding-left: 9.6875rem !important;
  }
  .p-md-31 {
    padding: 10.625rem !important;
  }
  .pt-md-31,
  .py-md-31 {
    padding-top: 10.625rem !important;
  }
  .pr-md-31,
  .px-md-31 {
    padding-right: 10.625rem !important;
  }
  .pb-md-31,
  .py-md-31 {
    padding-bottom: 10.625rem !important;
  }
  .pl-md-31,
  .px-md-31 {
    padding-left: 10.625rem !important;
  }
  .p-md-32 {
    padding: 11.25rem !important;
  }
  .pt-md-32,
  .py-md-32 {
    padding-top: 11.25rem !important;
  }
  .pr-md-32,
  .px-md-32 {
    padding-right: 11.25rem !important;
  }
  .pb-md-32,
  .py-md-32 {
    padding-bottom: 11.25rem !important;
  }
  .pl-md-32,
  .px-md-32 {
    padding-left: 11.25rem !important;
  }
  .p-md-33 {
    padding: 12.5rem !important;
  }
  .pt-md-33,
  .py-md-33 {
    padding-top: 12.5rem !important;
  }
  .pr-md-33,
  .px-md-33 {
    padding-right: 12.5rem !important;
  }
  .pb-md-33,
  .py-md-33 {
    padding-bottom: 12.5rem !important;
  }
  .pl-md-33,
  .px-md-33 {
    padding-left: 12.5rem !important;
  }
  .p-md-34 {
    padding: 14.0625rem !important;
  }
  .pt-md-34,
  .py-md-34 {
    padding-top: 14.0625rem !important;
  }
  .pr-md-34,
  .px-md-34 {
    padding-right: 14.0625rem !important;
  }
  .pb-md-34,
  .py-md-34 {
    padding-bottom: 14.0625rem !important;
  }
  .pl-md-34,
  .px-md-34 {
    padding-left: 14.0625rem !important;
  }
  .p-md-35 {
    padding: 15.625rem !important;
  }
  .pt-md-35,
  .py-md-35 {
    padding-top: 15.625rem !important;
  }
  .pr-md-35,
  .px-md-35 {
    padding-right: 15.625rem !important;
  }
  .pb-md-35,
  .py-md-35 {
    padding-bottom: 15.625rem !important;
  }
  .pl-md-35,
  .px-md-35 {
    padding-left: 15.625rem !important;
  }
  .m-md-n1 {
    margin: -0.25rem !important;
  }
  .mt-md-n1,
  .my-md-n1 {
    margin-top: -0.25rem !important;
  }
  .mr-md-n1,
  .mx-md-n1 {
    margin-right: -0.25rem !important;
  }
  .mb-md-n1,
  .my-md-n1 {
    margin-bottom: -0.25rem !important;
  }
  .ml-md-n1,
  .mx-md-n1 {
    margin-left: -0.25rem !important;
  }
  .m-md-n2 {
    margin: -0.375rem !important;
  }
  .mt-md-n2,
  .my-md-n2 {
    margin-top: -0.375rem !important;
  }
  .mr-md-n2,
  .mx-md-n2 {
    margin-right: -0.375rem !important;
  }
  .mb-md-n2,
  .my-md-n2 {
    margin-bottom: -0.375rem !important;
  }
  .ml-md-n2,
  .mx-md-n2 {
    margin-left: -0.375rem !important;
  }
  .m-md-n3 {
    margin: -0.5rem !important;
  }
  .mt-md-n3,
  .my-md-n3 {
    margin-top: -0.5rem !important;
  }
  .mr-md-n3,
  .mx-md-n3 {
    margin-right: -0.5rem !important;
  }
  .mb-md-n3,
  .my-md-n3 {
    margin-bottom: -0.5rem !important;
  }
  .ml-md-n3,
  .mx-md-n3 {
    margin-left: -0.5rem !important;
  }
  .m-md-n4 {
    margin: -0.625rem !important;
  }
  .mt-md-n4,
  .my-md-n4 {
    margin-top: -0.625rem !important;
  }
  .mr-md-n4,
  .mx-md-n4 {
    margin-right: -0.625rem !important;
  }
  .mb-md-n4,
  .my-md-n4 {
    margin-bottom: -0.625rem !important;
  }
  .ml-md-n4,
  .mx-md-n4 {
    margin-left: -0.625rem !important;
  }
  .m-md-n5 {
    margin: -0.75rem !important;
  }
  .mt-md-n5,
  .my-md-n5 {
    margin-top: -0.75rem !important;
  }
  .mr-md-n5,
  .mx-md-n5 {
    margin-right: -0.75rem !important;
  }
  .mb-md-n5,
  .my-md-n5 {
    margin-bottom: -0.75rem !important;
  }
  .ml-md-n5,
  .mx-md-n5 {
    margin-left: -0.75rem !important;
  }
  .m-md-n6 {
    margin: -1rem !important;
  }
  .mt-md-n6,
  .my-md-n6 {
    margin-top: -1rem !important;
  }
  .mr-md-n6,
  .mx-md-n6 {
    margin-right: -1rem !important;
  }
  .mb-md-n6,
  .my-md-n6 {
    margin-bottom: -1rem !important;
  }
  .ml-md-n6,
  .mx-md-n6 {
    margin-left: -1rem !important;
  }
  .m-md-n7 {
    margin: -1.25rem !important;
  }
  .mt-md-n7,
  .my-md-n7 {
    margin-top: -1.25rem !important;
  }
  .mr-md-n7,
  .mx-md-n7 {
    margin-right: -1.25rem !important;
  }
  .mb-md-n7,
  .my-md-n7 {
    margin-bottom: -1.25rem !important;
  }
  .ml-md-n7,
  .mx-md-n7 {
    margin-left: -1.25rem !important;
  }
  .m-md-n8 {
    margin: -1.5625rem !important;
  }
  .mt-md-n8,
  .my-md-n8 {
    margin-top: -1.5625rem !important;
  }
  .mr-md-n8,
  .mx-md-n8 {
    margin-right: -1.5625rem !important;
  }
  .mb-md-n8,
  .my-md-n8 {
    margin-bottom: -1.5625rem !important;
  }
  .ml-md-n8,
  .mx-md-n8 {
    margin-left: -1.5625rem !important;
  }
  .m-md-n9 {
    margin: -1.875rem !important;
  }
  .mt-md-n9,
  .my-md-n9 {
    margin-top: -1.875rem !important;
  }
  .mr-md-n9,
  .mx-md-n9 {
    margin-right: -1.875rem !important;
  }
  .mb-md-n9,
  .my-md-n9 {
    margin-bottom: -1.875rem !important;
  }
  .ml-md-n9,
  .mx-md-n9 {
    margin-left: -1.875rem !important;
  }
  .m-md-n10 {
    margin: -2.1875rem !important;
  }
  .mt-md-n10,
  .my-md-n10 {
    margin-top: -2.1875rem !important;
  }
  .mr-md-n10,
  .mx-md-n10 {
    margin-right: -2.1875rem !important;
  }
  .mb-md-n10,
  .my-md-n10 {
    margin-bottom: -2.1875rem !important;
  }
  .ml-md-n10,
  .mx-md-n10 {
    margin-left: -2.1875rem !important;
  }
  .m-md-n11 {
    margin: -2.5rem !important;
  }
  .mt-md-n11,
  .my-md-n11 {
    margin-top: -2.5rem !important;
  }
  .mr-md-n11,
  .mx-md-n11 {
    margin-right: -2.5rem !important;
  }
  .mb-md-n11,
  .my-md-n11 {
    margin-bottom: -2.5rem !important;
  }
  .ml-md-n11,
  .mx-md-n11 {
    margin-left: -2.5rem !important;
  }
  .m-md-n12 {
    margin: -2.8125rem !important;
  }
  .mt-md-n12,
  .my-md-n12 {
    margin-top: -2.8125rem !important;
  }
  .mr-md-n12,
  .mx-md-n12 {
    margin-right: -2.8125rem !important;
  }
  .mb-md-n12,
  .my-md-n12 {
    margin-bottom: -2.8125rem !important;
  }
  .ml-md-n12,
  .mx-md-n12 {
    margin-left: -2.8125rem !important;
  }
  .m-md-n13 {
    margin: -3.125rem !important;
  }
  .mt-md-n13,
  .my-md-n13 {
    margin-top: -3.125rem !important;
  }
  .mr-md-n13,
  .mx-md-n13 {
    margin-right: -3.125rem !important;
  }
  .mb-md-n13,
  .my-md-n13 {
    margin-bottom: -3.125rem !important;
  }
  .ml-md-n13,
  .mx-md-n13 {
    margin-left: -3.125rem !important;
  }
  .m-md-n14 {
    margin: -3.4375rem !important;
  }
  .mt-md-n14,
  .my-md-n14 {
    margin-top: -3.4375rem !important;
  }
  .mr-md-n14,
  .mx-md-n14 {
    margin-right: -3.4375rem !important;
  }
  .mb-md-n14,
  .my-md-n14 {
    margin-bottom: -3.4375rem !important;
  }
  .ml-md-n14,
  .mx-md-n14 {
    margin-left: -3.4375rem !important;
  }
  .m-md-n15 {
    margin: -3.75rem !important;
  }
  .mt-md-n15,
  .my-md-n15 {
    margin-top: -3.75rem !important;
  }
  .mr-md-n15,
  .mx-md-n15 {
    margin-right: -3.75rem !important;
  }
  .mb-md-n15,
  .my-md-n15 {
    margin-bottom: -3.75rem !important;
  }
  .ml-md-n15,
  .mx-md-n15 {
    margin-left: -3.75rem !important;
  }
  .m-md-n16 {
    margin: -4.0625rem !important;
  }
  .mt-md-n16,
  .my-md-n16 {
    margin-top: -4.0625rem !important;
  }
  .mr-md-n16,
  .mx-md-n16 {
    margin-right: -4.0625rem !important;
  }
  .mb-md-n16,
  .my-md-n16 {
    margin-bottom: -4.0625rem !important;
  }
  .ml-md-n16,
  .mx-md-n16 {
    margin-left: -4.0625rem !important;
  }
  .m-md-n17 {
    margin: -4.375rem !important;
  }
  .mt-md-n17,
  .my-md-n17 {
    margin-top: -4.375rem !important;
  }
  .mr-md-n17,
  .mx-md-n17 {
    margin-right: -4.375rem !important;
  }
  .mb-md-n17,
  .my-md-n17 {
    margin-bottom: -4.375rem !important;
  }
  .ml-md-n17,
  .mx-md-n17 {
    margin-left: -4.375rem !important;
  }
  .m-md-n18 {
    margin: -4.6875rem !important;
  }
  .mt-md-n18,
  .my-md-n18 {
    margin-top: -4.6875rem !important;
  }
  .mr-md-n18,
  .mx-md-n18 {
    margin-right: -4.6875rem !important;
  }
  .mb-md-n18,
  .my-md-n18 {
    margin-bottom: -4.6875rem !important;
  }
  .ml-md-n18,
  .mx-md-n18 {
    margin-left: -4.6875rem !important;
  }
  .m-md-n19 {
    margin: -5rem !important;
  }
  .mt-md-n19,
  .my-md-n19 {
    margin-top: -5rem !important;
  }
  .mr-md-n19,
  .mx-md-n19 {
    margin-right: -5rem !important;
  }
  .mb-md-n19,
  .my-md-n19 {
    margin-bottom: -5rem !important;
  }
  .ml-md-n19,
  .mx-md-n19 {
    margin-left: -5rem !important;
  }
  .m-md-n20 {
    margin: -5.3125rem !important;
  }
  .mt-md-n20,
  .my-md-n20 {
    margin-top: -5.3125rem !important;
  }
  .mr-md-n20,
  .mx-md-n20 {
    margin-right: -5.3125rem !important;
  }
  .mb-md-n20,
  .my-md-n20 {
    margin-bottom: -5.3125rem !important;
  }
  .ml-md-n20,
  .mx-md-n20 {
    margin-left: -5.3125rem !important;
  }
  .m-md-n21 {
    margin: -5.625rem !important;
  }
  .mt-md-n21,
  .my-md-n21 {
    margin-top: -5.625rem !important;
  }
  .mr-md-n21,
  .mx-md-n21 {
    margin-right: -5.625rem !important;
  }
  .mb-md-n21,
  .my-md-n21 {
    margin-bottom: -5.625rem !important;
  }
  .ml-md-n21,
  .mx-md-n21 {
    margin-left: -5.625rem !important;
  }
  .m-md-n22 {
    margin: -5.9375rem !important;
  }
  .mt-md-n22,
  .my-md-n22 {
    margin-top: -5.9375rem !important;
  }
  .mr-md-n22,
  .mx-md-n22 {
    margin-right: -5.9375rem !important;
  }
  .mb-md-n22,
  .my-md-n22 {
    margin-bottom: -5.9375rem !important;
  }
  .ml-md-n22,
  .mx-md-n22 {
    margin-left: -5.9375rem !important;
  }
  .m-md-n23 {
    margin: -6.25rem !important;
  }
  .mt-md-n23,
  .my-md-n23 {
    margin-top: -6.25rem !important;
  }
  .mr-md-n23,
  .mx-md-n23 {
    margin-right: -6.25rem !important;
  }
  .mb-md-n23,
  .my-md-n23 {
    margin-bottom: -6.25rem !important;
  }
  .ml-md-n23,
  .mx-md-n23 {
    margin-left: -6.25rem !important;
  }
  .m-md-n24 {
    margin: -6.875rem !important;
  }
  .mt-md-n24,
  .my-md-n24 {
    margin-top: -6.875rem !important;
  }
  .mr-md-n24,
  .mx-md-n24 {
    margin-right: -6.875rem !important;
  }
  .mb-md-n24,
  .my-md-n24 {
    margin-bottom: -6.875rem !important;
  }
  .ml-md-n24,
  .mx-md-n24 {
    margin-left: -6.875rem !important;
  }
  .m-md-n25 {
    margin: -7.5rem !important;
  }
  .mt-md-n25,
  .my-md-n25 {
    margin-top: -7.5rem !important;
  }
  .mr-md-n25,
  .mx-md-n25 {
    margin-right: -7.5rem !important;
  }
  .mb-md-n25,
  .my-md-n25 {
    margin-bottom: -7.5rem !important;
  }
  .ml-md-n25,
  .mx-md-n25 {
    margin-left: -7.5rem !important;
  }
  .m-md-n26 {
    margin: -8.125rem !important;
  }
  .mt-md-n26,
  .my-md-n26 {
    margin-top: -8.125rem !important;
  }
  .mr-md-n26,
  .mx-md-n26 {
    margin-right: -8.125rem !important;
  }
  .mb-md-n26,
  .my-md-n26 {
    margin-bottom: -8.125rem !important;
  }
  .ml-md-n26,
  .mx-md-n26 {
    margin-left: -8.125rem !important;
  }
  .m-md-n27 {
    margin: -8.4375rem !important;
  }
  .mt-md-n27,
  .my-md-n27 {
    margin-top: -8.4375rem !important;
  }
  .mr-md-n27,
  .mx-md-n27 {
    margin-right: -8.4375rem !important;
  }
  .mb-md-n27,
  .my-md-n27 {
    margin-bottom: -8.4375rem !important;
  }
  .ml-md-n27,
  .mx-md-n27 {
    margin-left: -8.4375rem !important;
  }
  .m-md-n28 {
    margin: -9.0625rem !important;
  }
  .mt-md-n28,
  .my-md-n28 {
    margin-top: -9.0625rem !important;
  }
  .mr-md-n28,
  .mx-md-n28 {
    margin-right: -9.0625rem !important;
  }
  .mb-md-n28,
  .my-md-n28 {
    margin-bottom: -9.0625rem !important;
  }
  .ml-md-n28,
  .mx-md-n28 {
    margin-left: -9.0625rem !important;
  }
  .m-md-n29 {
    margin: -9.375rem !important;
  }
  .mt-md-n29,
  .my-md-n29 {
    margin-top: -9.375rem !important;
  }
  .mr-md-n29,
  .mx-md-n29 {
    margin-right: -9.375rem !important;
  }
  .mb-md-n29,
  .my-md-n29 {
    margin-bottom: -9.375rem !important;
  }
  .ml-md-n29,
  .mx-md-n29 {
    margin-left: -9.375rem !important;
  }
  .m-md-n30 {
    margin: -9.6875rem !important;
  }
  .mt-md-n30,
  .my-md-n30 {
    margin-top: -9.6875rem !important;
  }
  .mr-md-n30,
  .mx-md-n30 {
    margin-right: -9.6875rem !important;
  }
  .mb-md-n30,
  .my-md-n30 {
    margin-bottom: -9.6875rem !important;
  }
  .ml-md-n30,
  .mx-md-n30 {
    margin-left: -9.6875rem !important;
  }
  .m-md-n31 {
    margin: -10.625rem !important;
  }
  .mt-md-n31,
  .my-md-n31 {
    margin-top: -10.625rem !important;
  }
  .mr-md-n31,
  .mx-md-n31 {
    margin-right: -10.625rem !important;
  }
  .mb-md-n31,
  .my-md-n31 {
    margin-bottom: -10.625rem !important;
  }
  .ml-md-n31,
  .mx-md-n31 {
    margin-left: -10.625rem !important;
  }
  .m-md-n32 {
    margin: -11.25rem !important;
  }
  .mt-md-n32,
  .my-md-n32 {
    margin-top: -11.25rem !important;
  }
  .mr-md-n32,
  .mx-md-n32 {
    margin-right: -11.25rem !important;
  }
  .mb-md-n32,
  .my-md-n32 {
    margin-bottom: -11.25rem !important;
  }
  .ml-md-n32,
  .mx-md-n32 {
    margin-left: -11.25rem !important;
  }
  .m-md-n33 {
    margin: -12.5rem !important;
  }
  .mt-md-n33,
  .my-md-n33 {
    margin-top: -12.5rem !important;
  }
  .mr-md-n33,
  .mx-md-n33 {
    margin-right: -12.5rem !important;
  }
  .mb-md-n33,
  .my-md-n33 {
    margin-bottom: -12.5rem !important;
  }
  .ml-md-n33,
  .mx-md-n33 {
    margin-left: -12.5rem !important;
  }
  .m-md-n34 {
    margin: -14.0625rem !important;
  }
  .mt-md-n34,
  .my-md-n34 {
    margin-top: -14.0625rem !important;
  }
  .mr-md-n34,
  .mx-md-n34 {
    margin-right: -14.0625rem !important;
  }
  .mb-md-n34,
  .my-md-n34 {
    margin-bottom: -14.0625rem !important;
  }
  .ml-md-n34,
  .mx-md-n34 {
    margin-left: -14.0625rem !important;
  }
  .m-md-n35 {
    margin: -15.625rem !important;
  }
  .mt-md-n35,
  .my-md-n35 {
    margin-top: -15.625rem !important;
  }
  .mr-md-n35,
  .mx-md-n35 {
    margin-right: -15.625rem !important;
  }
  .mb-md-n35,
  .my-md-n35 {
    margin-bottom: -15.625rem !important;
  }
  .ml-md-n35,
  .mx-md-n35 {
    margin-left: -15.625rem !important;
  }
  .m-md-auto {
    margin: auto !important;
  }
  .mt-md-auto,
  .my-md-auto {
    margin-top: auto !important;
  }
  .mr-md-auto,
  .mx-md-auto {
    margin-right: auto !important;
  }
  .mb-md-auto,
  .my-md-auto {
    margin-bottom: auto !important;
  }
  .ml-md-auto,
  .mx-md-auto {
    margin-left: auto !important;
  }
}

@media (min-width: 992px) {
  .m-lg-0 {
    margin: 0 !important;
  }
  .mt-lg-0,
  .my-lg-0 {
    margin-top: 0 !important;
  }
  .mr-lg-0,
  .mx-lg-0 {
    margin-right: 0 !important;
  }
  .mb-lg-0,
  .my-lg-0 {
    margin-bottom: 0 !important;
  }
  .ml-lg-0,
  .mx-lg-0 {
    margin-left: 0 !important;
  }
  .m-lg-1 {
    margin: 0.25rem !important;
  }
  .mt-lg-1,
  .my-lg-1 {
    margin-top: 0.25rem !important;
  }
  .mr-lg-1,
  .mx-lg-1 {
    margin-right: 0.25rem !important;
  }
  .mb-lg-1,
  .my-lg-1 {
    margin-bottom: 0.25rem !important;
  }
  .ml-lg-1,
  .mx-lg-1 {
    margin-left: 0.25rem !important;
  }
  .m-lg-2 {
    margin: 0.375rem !important;
  }
  .mt-lg-2,
  .my-lg-2 {
    margin-top: 0.375rem !important;
  }
  .mr-lg-2,
  .mx-lg-2 {
    margin-right: 0.375rem !important;
  }
  .mb-lg-2,
  .my-lg-2 {
    margin-bottom: 0.375rem !important;
  }
  .ml-lg-2,
  .mx-lg-2 {
    margin-left: 0.375rem !important;
  }
  .m-lg-3 {
    margin: 0.5rem !important;
  }
  .mt-lg-3,
  .my-lg-3 {
    margin-top: 0.5rem !important;
  }
  .mr-lg-3,
  .mx-lg-3 {
    margin-right: 0.5rem !important;
  }
  .mb-lg-3,
  .my-lg-3 {
    margin-bottom: 0.5rem !important;
  }
  .ml-lg-3,
  .mx-lg-3 {
    margin-left: 0.5rem !important;
  }
  .m-lg-4 {
    margin: 0.625rem !important;
  }
  .mt-lg-4,
  .my-lg-4 {
    margin-top: 0.625rem !important;
  }
  .mr-lg-4,
  .mx-lg-4 {
    margin-right: 0.625rem !important;
  }
  .mb-lg-4,
  .my-lg-4 {
    margin-bottom: 0.625rem !important;
  }
  .ml-lg-4,
  .mx-lg-4 {
    margin-left: 0.625rem !important;
  }
  .m-lg-5 {
    margin: 0.75rem !important;
  }
  .mt-lg-5,
  .my-lg-5 {
    margin-top: 0.75rem !important;
  }
  .mr-lg-5,
  .mx-lg-5 {
    margin-right: 0.75rem !important;
  }
  .mb-lg-5,
  .my-lg-5 {
    margin-bottom: 0.75rem !important;
  }
  .ml-lg-5,
  .mx-lg-5 {
    margin-left: 0.75rem !important;
  }
  .m-lg-6 {
    margin: 1rem !important;
  }
  .mt-lg-6,
  .my-lg-6 {
    margin-top: 1rem !important;
  }
  .mr-lg-6,
  .mx-lg-6 {
    margin-right: 1rem !important;
  }
  .mb-lg-6,
  .my-lg-6 {
    margin-bottom: 1rem !important;
  }
  .ml-lg-6,
  .mx-lg-6 {
    margin-left: 1rem !important;
  }
  .m-lg-7 {
    margin: 1.25rem !important;
  }
  .mt-lg-7,
  .my-lg-7 {
    margin-top: 1.25rem !important;
  }
  .mr-lg-7,
  .mx-lg-7 {
    margin-right: 1.25rem !important;
  }
  .mb-lg-7,
  .my-lg-7 {
    margin-bottom: 1.25rem !important;
  }
  .ml-lg-7,
  .mx-lg-7 {
    margin-left: 1.25rem !important;
  }
  .m-lg-8 {
    margin: 1.5625rem !important;
  }
  .mt-lg-8,
  .my-lg-8 {
    margin-top: 1.5625rem !important;
  }
  .mr-lg-8,
  .mx-lg-8 {
    margin-right: 1.5625rem !important;
  }
  .mb-lg-8,
  .my-lg-8 {
    margin-bottom: 1.5625rem !important;
  }
  .ml-lg-8,
  .mx-lg-8 {
    margin-left: 1.5625rem !important;
  }
  .m-lg-9 {
    margin: 1.875rem !important;
  }
  .mt-lg-9,
  .my-lg-9 {
    margin-top: 1.875rem !important;
  }
  .mr-lg-9,
  .mx-lg-9 {
    margin-right: 1.875rem !important;
  }
  .mb-lg-9,
  .my-lg-9 {
    margin-bottom: 1.875rem !important;
  }
  .ml-lg-9,
  .mx-lg-9 {
    margin-left: 1.875rem !important;
  }
  .m-lg-10 {
    margin: 2.1875rem !important;
  }
  .mt-lg-10,
  .my-lg-10 {
    margin-top: 2.1875rem !important;
  }
  .mr-lg-10,
  .mx-lg-10 {
    margin-right: 2.1875rem !important;
  }
  .mb-lg-10,
  .my-lg-10 {
    margin-bottom: 2.1875rem !important;
  }
  .ml-lg-10,
  .mx-lg-10 {
    margin-left: 2.1875rem !important;
  }
  .m-lg-11 {
    margin: 2.5rem !important;
  }
  .mt-lg-11,
  .my-lg-11 {
    margin-top: 2.5rem !important;
  }
  .mr-lg-11,
  .mx-lg-11 {
    margin-right: 2.5rem !important;
  }
  .mb-lg-11,
  .my-lg-11 {
    margin-bottom: 2.5rem !important;
  }
  .ml-lg-11,
  .mx-lg-11 {
    margin-left: 2.5rem !important;
  }
  .m-lg-12 {
    margin: 2.8125rem !important;
  }
  .mt-lg-12,
  .my-lg-12 {
    margin-top: 2.8125rem !important;
  }
  .mr-lg-12,
  .mx-lg-12 {
    margin-right: 2.8125rem !important;
  }
  .mb-lg-12,
  .my-lg-12 {
    margin-bottom: 2.8125rem !important;
  }
  .ml-lg-12,
  .mx-lg-12 {
    margin-left: 2.8125rem !important;
  }
  .m-lg-13 {
    margin: 3.125rem !important;
  }
  .mt-lg-13,
  .my-lg-13 {
    margin-top: 3.125rem !important;
  }
  .mr-lg-13,
  .mx-lg-13 {
    margin-right: 3.125rem !important;
  }
  .mb-lg-13,
  .my-lg-13 {
    margin-bottom: 3.125rem !important;
  }
  .ml-lg-13,
  .mx-lg-13 {
    margin-left: 3.125rem !important;
  }
  .m-lg-14 {
    margin: 3.4375rem !important;
  }
  .mt-lg-14,
  .my-lg-14 {
    margin-top: 3.4375rem !important;
  }
  .mr-lg-14,
  .mx-lg-14 {
    margin-right: 3.4375rem !important;
  }
  .mb-lg-14,
  .my-lg-14 {
    margin-bottom: 3.4375rem !important;
  }
  .ml-lg-14,
  .mx-lg-14 {
    margin-left: 3.4375rem !important;
  }
  .m-lg-15 {
    margin: 3.75rem !important;
  }
  .mt-lg-15,
  .my-lg-15 {
    margin-top: 3.75rem !important;
  }
  .mr-lg-15,
  .mx-lg-15 {
    margin-right: 3.75rem !important;
  }
  .mb-lg-15,
  .my-lg-15 {
    margin-bottom: 3.75rem !important;
  }
  .ml-lg-15,
  .mx-lg-15 {
    margin-left: 3.75rem !important;
  }
  .m-lg-16 {
    margin: 4.0625rem !important;
  }
  .mt-lg-16,
  .my-lg-16 {
    margin-top: 4.0625rem !important;
  }
  .mr-lg-16,
  .mx-lg-16 {
    margin-right: 4.0625rem !important;
  }
  .mb-lg-16,
  .my-lg-16 {
    margin-bottom: 4.0625rem !important;
  }
  .ml-lg-16,
  .mx-lg-16 {
    margin-left: 4.0625rem !important;
  }
  .m-lg-17 {
    margin: 4.375rem !important;
  }
  .mt-lg-17,
  .my-lg-17 {
    margin-top: 4.375rem !important;
  }
  .mr-lg-17,
  .mx-lg-17 {
    margin-right: 4.375rem !important;
  }
  .mb-lg-17,
  .my-lg-17 {
    margin-bottom: 4.375rem !important;
  }
  .ml-lg-17,
  .mx-lg-17 {
    margin-left: 4.375rem !important;
  }
  .m-lg-18 {
    margin: 4.6875rem !important;
  }
  .mt-lg-18,
  .my-lg-18 {
    margin-top: 4.6875rem !important;
  }
  .mr-lg-18,
  .mx-lg-18 {
    margin-right: 4.6875rem !important;
  }
  .mb-lg-18,
  .my-lg-18 {
    margin-bottom: 4.6875rem !important;
  }
  .ml-lg-18,
  .mx-lg-18 {
    margin-left: 4.6875rem !important;
  }
  .m-lg-19 {
    margin: 5rem !important;
  }
  .mt-lg-19,
  .my-lg-19 {
    margin-top: 5rem !important;
  }
  .mr-lg-19,
  .mx-lg-19 {
    margin-right: 5rem !important;
  }
  .mb-lg-19,
  .my-lg-19 {
    margin-bottom: 5rem !important;
  }
  .ml-lg-19,
  .mx-lg-19 {
    margin-left: 5rem !important;
  }
  .m-lg-20 {
    margin: 5.3125rem !important;
  }
  .mt-lg-20,
  .my-lg-20 {
    margin-top: 5.3125rem !important;
  }
  .mr-lg-20,
  .mx-lg-20 {
    margin-right: 5.3125rem !important;
  }
  .mb-lg-20,
  .my-lg-20 {
    margin-bottom: 5.3125rem !important;
  }
  .ml-lg-20,
  .mx-lg-20 {
    margin-left: 5.3125rem !important;
  }
  .m-lg-21 {
    margin: 5.625rem !important;
  }
  .mt-lg-21,
  .my-lg-21 {
    margin-top: 5.625rem !important;
  }
  .mr-lg-21,
  .mx-lg-21 {
    margin-right: 5.625rem !important;
  }
  .mb-lg-21,
  .my-lg-21 {
    margin-bottom: 5.625rem !important;
  }
  .ml-lg-21,
  .mx-lg-21 {
    margin-left: 5.625rem !important;
  }
  .m-lg-22 {
    margin: 5.9375rem !important;
  }
  .mt-lg-22,
  .my-lg-22 {
    margin-top: 5.9375rem !important;
  }
  .mr-lg-22,
  .mx-lg-22 {
    margin-right: 5.9375rem !important;
  }
  .mb-lg-22,
  .my-lg-22 {
    margin-bottom: 5.9375rem !important;
  }
  .ml-lg-22,
  .mx-lg-22 {
    margin-left: 5.9375rem !important;
  }
  .m-lg-23 {
    margin: 6.25rem !important;
  }
  .mt-lg-23,
  .my-lg-23 {
    margin-top: 6.25rem !important;
  }
  .mr-lg-23,
  .mx-lg-23 {
    margin-right: 6.25rem !important;
  }
  .mb-lg-23,
  .my-lg-23 {
    margin-bottom: 6.25rem !important;
  }
  .ml-lg-23,
  .mx-lg-23 {
    margin-left: 6.25rem !important;
  }
  .m-lg-24 {
    margin: 6.875rem !important;
  }
  .mt-lg-24,
  .my-lg-24 {
    margin-top: 6.875rem !important;
  }
  .mr-lg-24,
  .mx-lg-24 {
    margin-right: 6.875rem !important;
  }
  .mb-lg-24,
  .my-lg-24 {
    margin-bottom: 6.875rem !important;
  }
  .ml-lg-24,
  .mx-lg-24 {
    margin-left: 6.875rem !important;
  }
  .m-lg-25 {
    margin: 7.5rem !important;
  }
  .mt-lg-25,
  .my-lg-25 {
    margin-top: 7.5rem !important;
  }
  .mr-lg-25,
  .mx-lg-25 {
    margin-right: 7.5rem !important;
  }
  .mb-lg-25,
  .my-lg-25 {
    margin-bottom: 7.5rem !important;
  }
  .ml-lg-25,
  .mx-lg-25 {
    margin-left: 7.5rem !important;
  }
  .m-lg-26 {
    margin: 8.125rem !important;
  }
  .mt-lg-26,
  .my-lg-26 {
    margin-top: 8.125rem !important;
  }
  .mr-lg-26,
  .mx-lg-26 {
    margin-right: 8.125rem !important;
  }
  .mb-lg-26,
  .my-lg-26 {
    margin-bottom: 8.125rem !important;
  }
  .ml-lg-26,
  .mx-lg-26 {
    margin-left: 8.125rem !important;
  }
  .m-lg-27 {
    margin: 8.4375rem !important;
  }
  .mt-lg-27,
  .my-lg-27 {
    margin-top: 8.4375rem !important;
  }
  .mr-lg-27,
  .mx-lg-27 {
    margin-right: 8.4375rem !important;
  }
  .mb-lg-27,
  .my-lg-27 {
    margin-bottom: 8.4375rem !important;
  }
  .ml-lg-27,
  .mx-lg-27 {
    margin-left: 8.4375rem !important;
  }
  .m-lg-28 {
    margin: 9.0625rem !important;
  }
  .mt-lg-28,
  .my-lg-28 {
    margin-top: 9.0625rem !important;
  }
  .mr-lg-28,
  .mx-lg-28 {
    margin-right: 9.0625rem !important;
  }
  .mb-lg-28,
  .my-lg-28 {
    margin-bottom: 9.0625rem !important;
  }
  .ml-lg-28,
  .mx-lg-28 {
    margin-left: 9.0625rem !important;
  }
  .m-lg-29 {
    margin: 9.375rem !important;
  }
  .mt-lg-29,
  .my-lg-29 {
    margin-top: 9.375rem !important;
  }
  .mr-lg-29,
  .mx-lg-29 {
    margin-right: 9.375rem !important;
  }
  .mb-lg-29,
  .my-lg-29 {
    margin-bottom: 9.375rem !important;
  }
  .ml-lg-29,
  .mx-lg-29 {
    margin-left: 9.375rem !important;
  }
  .m-lg-30 {
    margin: 9.6875rem !important;
  }
  .mt-lg-30,
  .my-lg-30 {
    margin-top: 9.6875rem !important;
  }
  .mr-lg-30,
  .mx-lg-30 {
    margin-right: 9.6875rem !important;
  }
  .mb-lg-30,
  .my-lg-30 {
    margin-bottom: 9.6875rem !important;
  }
  .ml-lg-30,
  .mx-lg-30 {
    margin-left: 9.6875rem !important;
  }
  .m-lg-31 {
    margin: 10.625rem !important;
  }
  .mt-lg-31,
  .my-lg-31 {
    margin-top: 10.625rem !important;
  }
  .mr-lg-31,
  .mx-lg-31 {
    margin-right: 10.625rem !important;
  }
  .mb-lg-31,
  .my-lg-31 {
    margin-bottom: 10.625rem !important;
  }
  .ml-lg-31,
  .mx-lg-31 {
    margin-left: 10.625rem !important;
  }
  .m-lg-32 {
    margin: 11.25rem !important;
  }
  .mt-lg-32,
  .my-lg-32 {
    margin-top: 11.25rem !important;
  }
  .mr-lg-32,
  .mx-lg-32 {
    margin-right: 11.25rem !important;
  }
  .mb-lg-32,
  .my-lg-32 {
    margin-bottom: 11.25rem !important;
  }
  .ml-lg-32,
  .mx-lg-32 {
    margin-left: 11.25rem !important;
  }
  .m-lg-33 {
    margin: 12.5rem !important;
  }
  .mt-lg-33,
  .my-lg-33 {
    margin-top: 12.5rem !important;
  }
  .mr-lg-33,
  .mx-lg-33 {
    margin-right: 12.5rem !important;
  }
  .mb-lg-33,
  .my-lg-33 {
    margin-bottom: 12.5rem !important;
  }
  .ml-lg-33,
  .mx-lg-33 {
    margin-left: 12.5rem !important;
  }
  .m-lg-34 {
    margin: 14.0625rem !important;
  }
  .mt-lg-34,
  .my-lg-34 {
    margin-top: 14.0625rem !important;
  }
  .mr-lg-34,
  .mx-lg-34 {
    margin-right: 14.0625rem !important;
  }
  .mb-lg-34,
  .my-lg-34 {
    margin-bottom: 14.0625rem !important;
  }
  .ml-lg-34,
  .mx-lg-34 {
    margin-left: 14.0625rem !important;
  }
  .m-lg-35 {
    margin: 15.625rem !important;
  }
  .mt-lg-35,
  .my-lg-35 {
    margin-top: 15.625rem !important;
  }
  .mr-lg-35,
  .mx-lg-35 {
    margin-right: 15.625rem !important;
  }
  .mb-lg-35,
  .my-lg-35 {
    margin-bottom: 15.625rem !important;
  }
  .ml-lg-35,
  .mx-lg-35 {
    margin-left: 15.625rem !important;
  }
  .p-lg-0 {
    padding: 0 !important;
  }
  .pt-lg-0,
  .py-lg-0 {
    padding-top: 0 !important;
  }
  .pr-lg-0,
  .px-lg-0 {
    padding-right: 0 !important;
  }
  .pb-lg-0,
  .py-lg-0 {
    padding-bottom: 0 !important;
  }
  .pl-lg-0,
  .px-lg-0 {
    padding-left: 0 !important;
  }
  .p-lg-1 {
    padding: 0.25rem !important;
  }
  .pt-lg-1,
  .py-lg-1 {
    padding-top: 0.25rem !important;
  }
  .pr-lg-1,
  .px-lg-1 {
    padding-right: 0.25rem !important;
  }
  .pb-lg-1,
  .py-lg-1 {
    padding-bottom: 0.25rem !important;
  }
  .pl-lg-1,
  .px-lg-1 {
    padding-left: 0.25rem !important;
  }
  .p-lg-2 {
    padding: 0.375rem !important;
  }
  .pt-lg-2,
  .py-lg-2 {
    padding-top: 0.375rem !important;
  }
  .pr-lg-2,
  .px-lg-2 {
    padding-right: 0.375rem !important;
  }
  .pb-lg-2,
  .py-lg-2 {
    padding-bottom: 0.375rem !important;
  }
  .pl-lg-2,
  .px-lg-2 {
    padding-left: 0.375rem !important;
  }
  .p-lg-3 {
    padding: 0.5rem !important;
  }
  .pt-lg-3,
  .py-lg-3 {
    padding-top: 0.5rem !important;
  }
  .pr-lg-3,
  .px-lg-3 {
    padding-right: 0.5rem !important;
  }
  .pb-lg-3,
  .py-lg-3 {
    padding-bottom: 0.5rem !important;
  }
  .pl-lg-3,
  .px-lg-3 {
    padding-left: 0.5rem !important;
  }
  .p-lg-4 {
    padding: 0.625rem !important;
  }
  .pt-lg-4,
  .py-lg-4 {
    padding-top: 0.625rem !important;
  }
  .pr-lg-4,
  .px-lg-4 {
    padding-right: 0.625rem !important;
  }
  .pb-lg-4,
  .py-lg-4 {
    padding-bottom: 0.625rem !important;
  }
  .pl-lg-4,
  .px-lg-4 {
    padding-left: 0.625rem !important;
  }
  .p-lg-5 {
    padding: 0.75rem !important;
  }
  .pt-lg-5,
  .py-lg-5 {
    padding-top: 0.75rem !important;
  }
  .pr-lg-5,
  .px-lg-5 {
    padding-right: 0.75rem !important;
  }
  .pb-lg-5,
  .py-lg-5 {
    padding-bottom: 0.75rem !important;
  }
  .pl-lg-5,
  .px-lg-5 {
    padding-left: 0.75rem !important;
  }
  .p-lg-6 {
    padding: 1rem !important;
  }
  .pt-lg-6,
  .py-lg-6 {
    padding-top: 1rem !important;
  }
  .pr-lg-6,
  .px-lg-6 {
    padding-right: 1rem !important;
  }
  .pb-lg-6,
  .py-lg-6 {
    padding-bottom: 1rem !important;
  }
  .pl-lg-6,
  .px-lg-6 {
    padding-left: 1rem !important;
  }
  .p-lg-7 {
    padding: 1.25rem !important;
  }
  .pt-lg-7,
  .py-lg-7 {
    padding-top: 1.25rem !important;
  }
  .pr-lg-7,
  .px-lg-7 {
    padding-right: 1.25rem !important;
  }
  .pb-lg-7,
  .py-lg-7 {
    padding-bottom: 1.25rem !important;
  }
  .pl-lg-7,
  .px-lg-7 {
    padding-left: 1.25rem !important;
  }
  .p-lg-8 {
    padding: 1.5625rem !important;
  }
  .pt-lg-8,
  .py-lg-8 {
    padding-top: 1.5625rem !important;
  }
  .pr-lg-8,
  .px-lg-8 {
    padding-right: 1.5625rem !important;
  }
  .pb-lg-8,
  .py-lg-8 {
    padding-bottom: 1.5625rem !important;
  }
  .pl-lg-8,
  .px-lg-8 {
    padding-left: 1.5625rem !important;
  }
  .p-lg-9 {
    padding: 1.875rem !important;
  }
  .pt-lg-9,
  .py-lg-9 {
    padding-top: 1.875rem !important;
  }
  .pr-lg-9,
  .px-lg-9 {
    padding-right: 1.875rem !important;
  }
  .pb-lg-9,
  .py-lg-9 {
    padding-bottom: 1.875rem !important;
  }
  .pl-lg-9,
  .px-lg-9 {
    padding-left: 1.875rem !important;
  }
  .p-lg-10 {
    padding: 2.1875rem !important;
  }
  .pt-lg-10,
  .py-lg-10 {
    padding-top: 2.1875rem !important;
  }
  .pr-lg-10,
  .px-lg-10 {
    padding-right: 2.1875rem !important;
  }
  .pb-lg-10,
  .py-lg-10 {
    padding-bottom: 2.1875rem !important;
  }
  .pl-lg-10,
  .px-lg-10 {
    padding-left: 2.1875rem !important;
  }
  .p-lg-11 {
    padding: 2.5rem !important;
  }
  .pt-lg-11,
  .py-lg-11 {
    padding-top: 2.5rem !important;
  }
  .pr-lg-11,
  .px-lg-11 {
    padding-right: 2.5rem !important;
  }
  .pb-lg-11,
  .py-lg-11 {
    padding-bottom: 2.5rem !important;
  }
  .pl-lg-11,
  .px-lg-11 {
    padding-left: 2.5rem !important;
  }
  .p-lg-12 {
    padding: 2.8125rem !important;
  }
  .pt-lg-12,
  .py-lg-12 {
    padding-top: 2.8125rem !important;
  }
  .pr-lg-12,
  .px-lg-12 {
    padding-right: 2.8125rem !important;
  }
  .pb-lg-12,
  .py-lg-12 {
    padding-bottom: 2.8125rem !important;
  }
  .pl-lg-12,
  .px-lg-12 {
    padding-left: 2.8125rem !important;
  }
  .p-lg-13 {
    padding: 3.125rem !important;
  }
  .pt-lg-13,
  .py-lg-13 {
    padding-top: 3.125rem !important;
  }
  .pr-lg-13,
  .px-lg-13 {
    padding-right: 3.125rem !important;
  }
  .pb-lg-13,
  .py-lg-13 {
    padding-bottom: 3.125rem !important;
  }
  .pl-lg-13,
  .px-lg-13 {
    padding-left: 3.125rem !important;
  }
  .p-lg-14 {
    padding: 3.4375rem !important;
  }
  .pt-lg-14,
  .py-lg-14 {
    padding-top: 3.4375rem !important;
  }
  .pr-lg-14,
  .px-lg-14 {
    padding-right: 3.4375rem !important;
  }
  .pb-lg-14,
  .py-lg-14 {
    padding-bottom: 3.4375rem !important;
  }
  .pl-lg-14,
  .px-lg-14 {
    padding-left: 3.4375rem !important;
  }
  .p-lg-15 {
    padding: 3.75rem !important;
  }
  .pt-lg-15,
  .py-lg-15 {
    padding-top: 3.75rem !important;
  }
  .pr-lg-15,
  .px-lg-15 {
    padding-right: 3.75rem !important;
  }
  .pb-lg-15,
  .py-lg-15 {
    padding-bottom: 3.75rem !important;
  }
  .pl-lg-15,
  .px-lg-15 {
    padding-left: 3.75rem !important;
  }
  .p-lg-16 {
    padding: 4.0625rem !important;
  }
  .pt-lg-16,
  .py-lg-16 {
    padding-top: 4.0625rem !important;
  }
  .pr-lg-16,
  .px-lg-16 {
    padding-right: 4.0625rem !important;
  }
  .pb-lg-16,
  .py-lg-16 {
    padding-bottom: 4.0625rem !important;
  }
  .pl-lg-16,
  .px-lg-16 {
    padding-left: 4.0625rem !important;
  }
  .p-lg-17 {
    padding: 4.375rem !important;
  }
  .pt-lg-17,
  .py-lg-17 {
    padding-top: 4.375rem !important;
  }
  .pr-lg-17,
  .px-lg-17 {
    padding-right: 4.375rem !important;
  }
  .pb-lg-17,
  .py-lg-17 {
    padding-bottom: 4.375rem !important;
  }
  .pl-lg-17,
  .px-lg-17 {
    padding-left: 4.375rem !important;
  }
  .p-lg-18 {
    padding: 4.6875rem !important;
  }
  .pt-lg-18,
  .py-lg-18 {
    padding-top: 4.6875rem !important;
  }
  .pr-lg-18,
  .px-lg-18 {
    padding-right: 4.6875rem !important;
  }
  .pb-lg-18,
  .py-lg-18 {
    padding-bottom: 4.6875rem !important;
  }
  .pl-lg-18,
  .px-lg-18 {
    padding-left: 4.6875rem !important;
  }
  .p-lg-19 {
    padding: 5rem !important;
  }
  .pt-lg-19,
  .py-lg-19 {
    padding-top: 5rem !important;
  }
  .pr-lg-19,
  .px-lg-19 {
    padding-right: 5rem !important;
  }
  .pb-lg-19,
  .py-lg-19 {
    padding-bottom: 5rem !important;
  }
  .pl-lg-19,
  .px-lg-19 {
    padding-left: 5rem !important;
  }
  .p-lg-20 {
    padding: 5.3125rem !important;
  }
  .pt-lg-20,
  .py-lg-20 {
    padding-top: 5.3125rem !important;
  }
  .pr-lg-20,
  .px-lg-20 {
    padding-right: 5.3125rem !important;
  }
  .pb-lg-20,
  .py-lg-20 {
    padding-bottom: 5.3125rem !important;
  }
  .pl-lg-20,
  .px-lg-20 {
    padding-left: 5.3125rem !important;
  }
  .p-lg-21 {
    padding: 5.625rem !important;
  }
  .pt-lg-21,
  .py-lg-21 {
    padding-top: 5.625rem !important;
  }
  .pr-lg-21,
  .px-lg-21 {
    padding-right: 5.625rem !important;
  }
  .pb-lg-21,
  .py-lg-21 {
    padding-bottom: 5.625rem !important;
  }
  .pl-lg-21,
  .px-lg-21 {
    padding-left: 5.625rem !important;
  }
  .p-lg-22 {
    padding: 5.9375rem !important;
  }
  .pt-lg-22,
  .py-lg-22 {
    padding-top: 5.9375rem !important;
  }
  .pr-lg-22,
  .px-lg-22 {
    padding-right: 5.9375rem !important;
  }
  .pb-lg-22,
  .py-lg-22 {
    padding-bottom: 5.9375rem !important;
  }
  .pl-lg-22,
  .px-lg-22 {
    padding-left: 5.9375rem !important;
  }
  .p-lg-23 {
    padding: 6.25rem !important;
  }
  .pt-lg-23,
  .py-lg-23 {
    padding-top: 6.25rem !important;
  }
  .pr-lg-23,
  .px-lg-23 {
    padding-right: 6.25rem !important;
  }
  .pb-lg-23,
  .py-lg-23 {
    padding-bottom: 6.25rem !important;
  }
  .pl-lg-23,
  .px-lg-23 {
    padding-left: 6.25rem !important;
  }
  .p-lg-24 {
    padding: 6.875rem !important;
  }
  .pt-lg-24,
  .py-lg-24 {
    padding-top: 6.875rem !important;
  }
  .pr-lg-24,
  .px-lg-24 {
    padding-right: 6.875rem !important;
  }
  .pb-lg-24,
  .py-lg-24 {
    padding-bottom: 6.875rem !important;
  }
  .pl-lg-24,
  .px-lg-24 {
    padding-left: 6.875rem !important;
  }
  .p-lg-25 {
    padding: 7.5rem !important;
  }
  .pt-lg-25,
  .py-lg-25 {
    padding-top: 7.5rem !important;
  }
  .pr-lg-25,
  .px-lg-25 {
    padding-right: 7.5rem !important;
  }
  .pb-lg-25,
  .py-lg-25 {
    padding-bottom: 7.5rem !important;
  }
  .pl-lg-25,
  .px-lg-25 {
    padding-left: 7.5rem !important;
  }
  .p-lg-26 {
    padding: 8.125rem !important;
  }
  .pt-lg-26,
  .py-lg-26 {
    padding-top: 8.125rem !important;
  }
  .pr-lg-26,
  .px-lg-26 {
    padding-right: 8.125rem !important;
  }
  .pb-lg-26,
  .py-lg-26 {
    padding-bottom: 8.125rem !important;
  }
  .pl-lg-26,
  .px-lg-26 {
    padding-left: 8.125rem !important;
  }
  .p-lg-27 {
    padding: 8.4375rem !important;
  }
  .pt-lg-27,
  .py-lg-27 {
    padding-top: 8.4375rem !important;
  }
  .pr-lg-27,
  .px-lg-27 {
    padding-right: 8.4375rem !important;
  }
  .pb-lg-27,
  .py-lg-27 {
    padding-bottom: 8.4375rem !important;
  }
  .pl-lg-27,
  .px-lg-27 {
    padding-left: 8.4375rem !important;
  }
  .p-lg-28 {
    padding: 9.0625rem !important;
  }
  .pt-lg-28,
  .py-lg-28 {
    padding-top: 9.0625rem !important;
  }
  .pr-lg-28,
  .px-lg-28 {
    padding-right: 9.0625rem !important;
  }
  .pb-lg-28,
  .py-lg-28 {
    padding-bottom: 9.0625rem !important;
  }
  .pl-lg-28,
  .px-lg-28 {
    padding-left: 9.0625rem !important;
  }
  .p-lg-29 {
    padding: 9.375rem !important;
  }
  .pt-lg-29,
  .py-lg-29 {
    padding-top: 9.375rem !important;
  }
  .pr-lg-29,
  .px-lg-29 {
    padding-right: 9.375rem !important;
  }
  .pb-lg-29,
  .py-lg-29 {
    padding-bottom: 9.375rem !important;
  }
  .pl-lg-29,
  .px-lg-29 {
    padding-left: 9.375rem !important;
  }
  .p-lg-30 {
    padding: 9.6875rem !important;
  }
  .pt-lg-30,
  .py-lg-30 {
    padding-top: 9.6875rem !important;
  }
  .pr-lg-30,
  .px-lg-30 {
    padding-right: 9.6875rem !important;
  }
  .pb-lg-30,
  .py-lg-30 {
    padding-bottom: 9.6875rem !important;
  }
  .pl-lg-30,
  .px-lg-30 {
    padding-left: 9.6875rem !important;
  }
  .p-lg-31 {
    padding: 10.625rem !important;
  }
  .pt-lg-31,
  .py-lg-31 {
    padding-top: 10.625rem !important;
  }
  .pr-lg-31,
  .px-lg-31 {
    padding-right: 10.625rem !important;
  }
  .pb-lg-31,
  .py-lg-31 {
    padding-bottom: 10.625rem !important;
  }
  .pl-lg-31,
  .px-lg-31 {
    padding-left: 10.625rem !important;
  }
  .p-lg-32 {
    padding: 11.25rem !important;
  }
  .pt-lg-32,
  .py-lg-32 {
    padding-top: 11.25rem !important;
  }
  .pr-lg-32,
  .px-lg-32 {
    padding-right: 11.25rem !important;
  }
  .pb-lg-32,
  .py-lg-32 {
    padding-bottom: 11.25rem !important;
  }
  .pl-lg-32,
  .px-lg-32 {
    padding-left: 11.25rem !important;
  }
  .p-lg-33 {
    padding: 12.5rem !important;
  }
  .pt-lg-33,
  .py-lg-33 {
    padding-top: 12.5rem !important;
  }
  .pr-lg-33,
  .px-lg-33 {
    padding-right: 12.5rem !important;
  }
  .pb-lg-33,
  .py-lg-33 {
    padding-bottom: 12.5rem !important;
  }
  .pl-lg-33,
  .px-lg-33 {
    padding-left: 12.5rem !important;
  }
  .p-lg-34 {
    padding: 14.0625rem !important;
  }
  .pt-lg-34,
  .py-lg-34 {
    padding-top: 14.0625rem !important;
  }
  .pr-lg-34,
  .px-lg-34 {
    padding-right: 14.0625rem !important;
  }
  .pb-lg-34,
  .py-lg-34 {
    padding-bottom: 14.0625rem !important;
  }
  .pl-lg-34,
  .px-lg-34 {
    padding-left: 14.0625rem !important;
  }
  .p-lg-35 {
    padding: 15.625rem !important;
  }
  .pt-lg-35,
  .py-lg-35 {
    padding-top: 15.625rem !important;
  }
  .pr-lg-35,
  .px-lg-35 {
    padding-right: 15.625rem !important;
  }
  .pb-lg-35,
  .py-lg-35 {
    padding-bottom: 15.625rem !important;
  }
  .pl-lg-35,
  .px-lg-35 {
    padding-left: 15.625rem !important;
  }
  .m-lg-n1 {
    margin: -0.25rem !important;
  }
  .mt-lg-n1,
  .my-lg-n1 {
    margin-top: -0.25rem !important;
  }
  .mr-lg-n1,
  .mx-lg-n1 {
    margin-right: -0.25rem !important;
  }
  .mb-lg-n1,
  .my-lg-n1 {
    margin-bottom: -0.25rem !important;
  }
  .ml-lg-n1,
  .mx-lg-n1 {
    margin-left: -0.25rem !important;
  }
  .m-lg-n2 {
    margin: -0.375rem !important;
  }
  .mt-lg-n2,
  .my-lg-n2 {
    margin-top: -0.375rem !important;
  }
  .mr-lg-n2,
  .mx-lg-n2 {
    margin-right: -0.375rem !important;
  }
  .mb-lg-n2,
  .my-lg-n2 {
    margin-bottom: -0.375rem !important;
  }
  .ml-lg-n2,
  .mx-lg-n2 {
    margin-left: -0.375rem !important;
  }
  .m-lg-n3 {
    margin: -0.5rem !important;
  }
  .mt-lg-n3,
  .my-lg-n3 {
    margin-top: -0.5rem !important;
  }
  .mr-lg-n3,
  .mx-lg-n3 {
    margin-right: -0.5rem !important;
  }
  .mb-lg-n3,
  .my-lg-n3 {
    margin-bottom: -0.5rem !important;
  }
  .ml-lg-n3,
  .mx-lg-n3 {
    margin-left: -0.5rem !important;
  }
  .m-lg-n4 {
    margin: -0.625rem !important;
  }
  .mt-lg-n4,
  .my-lg-n4 {
    margin-top: -0.625rem !important;
  }
  .mr-lg-n4,
  .mx-lg-n4 {
    margin-right: -0.625rem !important;
  }
  .mb-lg-n4,
  .my-lg-n4 {
    margin-bottom: -0.625rem !important;
  }
  .ml-lg-n4,
  .mx-lg-n4 {
    margin-left: -0.625rem !important;
  }
  .m-lg-n5 {
    margin: -0.75rem !important;
  }
  .mt-lg-n5,
  .my-lg-n5 {
    margin-top: -0.75rem !important;
  }
  .mr-lg-n5,
  .mx-lg-n5 {
    margin-right: -0.75rem !important;
  }
  .mb-lg-n5,
  .my-lg-n5 {
    margin-bottom: -0.75rem !important;
  }
  .ml-lg-n5,
  .mx-lg-n5 {
    margin-left: -0.75rem !important;
  }
  .m-lg-n6 {
    margin: -1rem !important;
  }
  .mt-lg-n6,
  .my-lg-n6 {
    margin-top: -1rem !important;
  }
  .mr-lg-n6,
  .mx-lg-n6 {
    margin-right: -1rem !important;
  }
  .mb-lg-n6,
  .my-lg-n6 {
    margin-bottom: -1rem !important;
  }
  .ml-lg-n6,
  .mx-lg-n6 {
    margin-left: -1rem !important;
  }
  .m-lg-n7 {
    margin: -1.25rem !important;
  }
  .mt-lg-n7,
  .my-lg-n7 {
    margin-top: -1.25rem !important;
  }
  .mr-lg-n7,
  .mx-lg-n7 {
    margin-right: -1.25rem !important;
  }
  .mb-lg-n7,
  .my-lg-n7 {
    margin-bottom: -1.25rem !important;
  }
  .ml-lg-n7,
  .mx-lg-n7 {
    margin-left: -1.25rem !important;
  }
  .m-lg-n8 {
    margin: -1.5625rem !important;
  }
  .mt-lg-n8,
  .my-lg-n8 {
    margin-top: -1.5625rem !important;
  }
  .mr-lg-n8,
  .mx-lg-n8 {
    margin-right: -1.5625rem !important;
  }
  .mb-lg-n8,
  .my-lg-n8 {
    margin-bottom: -1.5625rem !important;
  }
  .ml-lg-n8,
  .mx-lg-n8 {
    margin-left: -1.5625rem !important;
  }
  .m-lg-n9 {
    margin: -1.875rem !important;
  }
  .mt-lg-n9,
  .my-lg-n9 {
    margin-top: -1.875rem !important;
  }
  .mr-lg-n9,
  .mx-lg-n9 {
    margin-right: -1.875rem !important;
  }
  .mb-lg-n9,
  .my-lg-n9 {
    margin-bottom: -1.875rem !important;
  }
  .ml-lg-n9,
  .mx-lg-n9 {
    margin-left: -1.875rem !important;
  }
  .m-lg-n10 {
    margin: -2.1875rem !important;
  }
  .mt-lg-n10,
  .my-lg-n10 {
    margin-top: -2.1875rem !important;
  }
  .mr-lg-n10,
  .mx-lg-n10 {
    margin-right: -2.1875rem !important;
  }
  .mb-lg-n10,
  .my-lg-n10 {
    margin-bottom: -2.1875rem !important;
  }
  .ml-lg-n10,
  .mx-lg-n10 {
    margin-left: -2.1875rem !important;
  }
  .m-lg-n11 {
    margin: -2.5rem !important;
  }
  .mt-lg-n11,
  .my-lg-n11 {
    margin-top: -2.5rem !important;
  }
  .mr-lg-n11,
  .mx-lg-n11 {
    margin-right: -2.5rem !important;
  }
  .mb-lg-n11,
  .my-lg-n11 {
    margin-bottom: -2.5rem !important;
  }
  .ml-lg-n11,
  .mx-lg-n11 {
    margin-left: -2.5rem !important;
  }
  .m-lg-n12 {
    margin: -2.8125rem !important;
  }
  .mt-lg-n12,
  .my-lg-n12 {
    margin-top: -2.8125rem !important;
  }
  .mr-lg-n12,
  .mx-lg-n12 {
    margin-right: -2.8125rem !important;
  }
  .mb-lg-n12,
  .my-lg-n12 {
    margin-bottom: -2.8125rem !important;
  }
  .ml-lg-n12,
  .mx-lg-n12 {
    margin-left: -2.8125rem !important;
  }
  .m-lg-n13 {
    margin: -3.125rem !important;
  }
  .mt-lg-n13,
  .my-lg-n13 {
    margin-top: -3.125rem !important;
  }
  .mr-lg-n13,
  .mx-lg-n13 {
    margin-right: -3.125rem !important;
  }
  .mb-lg-n13,
  .my-lg-n13 {
    margin-bottom: -3.125rem !important;
  }
  .ml-lg-n13,
  .mx-lg-n13 {
    margin-left: -3.125rem !important;
  }
  .m-lg-n14 {
    margin: -3.4375rem !important;
  }
  .mt-lg-n14,
  .my-lg-n14 {
    margin-top: -3.4375rem !important;
  }
  .mr-lg-n14,
  .mx-lg-n14 {
    margin-right: -3.4375rem !important;
  }
  .mb-lg-n14,
  .my-lg-n14 {
    margin-bottom: -3.4375rem !important;
  }
  .ml-lg-n14,
  .mx-lg-n14 {
    margin-left: -3.4375rem !important;
  }
  .m-lg-n15 {
    margin: -3.75rem !important;
  }
  .mt-lg-n15,
  .my-lg-n15 {
    margin-top: -3.75rem !important;
  }
  .mr-lg-n15,
  .mx-lg-n15 {
    margin-right: -3.75rem !important;
  }
  .mb-lg-n15,
  .my-lg-n15 {
    margin-bottom: -3.75rem !important;
  }
  .ml-lg-n15,
  .mx-lg-n15 {
    margin-left: -3.75rem !important;
  }
  .m-lg-n16 {
    margin: -4.0625rem !important;
  }
  .mt-lg-n16,
  .my-lg-n16 {
    margin-top: -4.0625rem !important;
  }
  .mr-lg-n16,
  .mx-lg-n16 {
    margin-right: -4.0625rem !important;
  }
  .mb-lg-n16,
  .my-lg-n16 {
    margin-bottom: -4.0625rem !important;
  }
  .ml-lg-n16,
  .mx-lg-n16 {
    margin-left: -4.0625rem !important;
  }
  .m-lg-n17 {
    margin: -4.375rem !important;
  }
  .mt-lg-n17,
  .my-lg-n17 {
    margin-top: -4.375rem !important;
  }
  .mr-lg-n17,
  .mx-lg-n17 {
    margin-right: -4.375rem !important;
  }
  .mb-lg-n17,
  .my-lg-n17 {
    margin-bottom: -4.375rem !important;
  }
  .ml-lg-n17,
  .mx-lg-n17 {
    margin-left: -4.375rem !important;
  }
  .m-lg-n18 {
    margin: -4.6875rem !important;
  }
  .mt-lg-n18,
  .my-lg-n18 {
    margin-top: -4.6875rem !important;
  }
  .mr-lg-n18,
  .mx-lg-n18 {
    margin-right: -4.6875rem !important;
  }
  .mb-lg-n18,
  .my-lg-n18 {
    margin-bottom: -4.6875rem !important;
  }
  .ml-lg-n18,
  .mx-lg-n18 {
    margin-left: -4.6875rem !important;
  }
  .m-lg-n19 {
    margin: -5rem !important;
  }
  .mt-lg-n19,
  .my-lg-n19 {
    margin-top: -5rem !important;
  }
  .mr-lg-n19,
  .mx-lg-n19 {
    margin-right: -5rem !important;
  }
  .mb-lg-n19,
  .my-lg-n19 {
    margin-bottom: -5rem !important;
  }
  .ml-lg-n19,
  .mx-lg-n19 {
    margin-left: -5rem !important;
  }
  .m-lg-n20 {
    margin: -5.3125rem !important;
  }
  .mt-lg-n20,
  .my-lg-n20 {
    margin-top: -5.3125rem !important;
  }
  .mr-lg-n20,
  .mx-lg-n20 {
    margin-right: -5.3125rem !important;
  }
  .mb-lg-n20,
  .my-lg-n20 {
    margin-bottom: -5.3125rem !important;
  }
  .ml-lg-n20,
  .mx-lg-n20 {
    margin-left: -5.3125rem !important;
  }
  .m-lg-n21 {
    margin: -5.625rem !important;
  }
  .mt-lg-n21,
  .my-lg-n21 {
    margin-top: -5.625rem !important;
  }
  .mr-lg-n21,
  .mx-lg-n21 {
    margin-right: -5.625rem !important;
  }
  .mb-lg-n21,
  .my-lg-n21 {
    margin-bottom: -5.625rem !important;
  }
  .ml-lg-n21,
  .mx-lg-n21 {
    margin-left: -5.625rem !important;
  }
  .m-lg-n22 {
    margin: -5.9375rem !important;
  }
  .mt-lg-n22,
  .my-lg-n22 {
    margin-top: -5.9375rem !important;
  }
  .mr-lg-n22,
  .mx-lg-n22 {
    margin-right: -5.9375rem !important;
  }
  .mb-lg-n22,
  .my-lg-n22 {
    margin-bottom: -5.9375rem !important;
  }
  .ml-lg-n22,
  .mx-lg-n22 {
    margin-left: -5.9375rem !important;
  }
  .m-lg-n23 {
    margin: -6.25rem !important;
  }
  .mt-lg-n23,
  .my-lg-n23 {
    margin-top: -6.25rem !important;
  }
  .mr-lg-n23,
  .mx-lg-n23 {
    margin-right: -6.25rem !important;
  }
  .mb-lg-n23,
  .my-lg-n23 {
    margin-bottom: -6.25rem !important;
  }
  .ml-lg-n23,
  .mx-lg-n23 {
    margin-left: -6.25rem !important;
  }
  .m-lg-n24 {
    margin: -6.875rem !important;
  }
  .mt-lg-n24,
  .my-lg-n24 {
    margin-top: -6.875rem !important;
  }
  .mr-lg-n24,
  .mx-lg-n24 {
    margin-right: -6.875rem !important;
  }
  .mb-lg-n24,
  .my-lg-n24 {
    margin-bottom: -6.875rem !important;
  }
  .ml-lg-n24,
  .mx-lg-n24 {
    margin-left: -6.875rem !important;
  }
  .m-lg-n25 {
    margin: -7.5rem !important;
  }
  .mt-lg-n25,
  .my-lg-n25 {
    margin-top: -7.5rem !important;
  }
  .mr-lg-n25,
  .mx-lg-n25 {
    margin-right: -7.5rem !important;
  }
  .mb-lg-n25,
  .my-lg-n25 {
    margin-bottom: -7.5rem !important;
  }
  .ml-lg-n25,
  .mx-lg-n25 {
    margin-left: -7.5rem !important;
  }
  .m-lg-n26 {
    margin: -8.125rem !important;
  }
  .mt-lg-n26,
  .my-lg-n26 {
    margin-top: -8.125rem !important;
  }
  .mr-lg-n26,
  .mx-lg-n26 {
    margin-right: -8.125rem !important;
  }
  .mb-lg-n26,
  .my-lg-n26 {
    margin-bottom: -8.125rem !important;
  }
  .ml-lg-n26,
  .mx-lg-n26 {
    margin-left: -8.125rem !important;
  }
  .m-lg-n27 {
    margin: -8.4375rem !important;
  }
  .mt-lg-n27,
  .my-lg-n27 {
    margin-top: -8.4375rem !important;
  }
  .mr-lg-n27,
  .mx-lg-n27 {
    margin-right: -8.4375rem !important;
  }
  .mb-lg-n27,
  .my-lg-n27 {
    margin-bottom: -8.4375rem !important;
  }
  .ml-lg-n27,
  .mx-lg-n27 {
    margin-left: -8.4375rem !important;
  }
  .m-lg-n28 {
    margin: -9.0625rem !important;
  }
  .mt-lg-n28,
  .my-lg-n28 {
    margin-top: -9.0625rem !important;
  }
  .mr-lg-n28,
  .mx-lg-n28 {
    margin-right: -9.0625rem !important;
  }
  .mb-lg-n28,
  .my-lg-n28 {
    margin-bottom: -9.0625rem !important;
  }
  .ml-lg-n28,
  .mx-lg-n28 {
    margin-left: -9.0625rem !important;
  }
  .m-lg-n29 {
    margin: -9.375rem !important;
  }
  .mt-lg-n29,
  .my-lg-n29 {
    margin-top: -9.375rem !important;
  }
  .mr-lg-n29,
  .mx-lg-n29 {
    margin-right: -9.375rem !important;
  }
  .mb-lg-n29,
  .my-lg-n29 {
    margin-bottom: -9.375rem !important;
  }
  .ml-lg-n29,
  .mx-lg-n29 {
    margin-left: -9.375rem !important;
  }
  .m-lg-n30 {
    margin: -9.6875rem !important;
  }
  .mt-lg-n30,
  .my-lg-n30 {
    margin-top: -9.6875rem !important;
  }
  .mr-lg-n30,
  .mx-lg-n30 {
    margin-right: -9.6875rem !important;
  }
  .mb-lg-n30,
  .my-lg-n30 {
    margin-bottom: -9.6875rem !important;
  }
  .ml-lg-n30,
  .mx-lg-n30 {
    margin-left: -9.6875rem !important;
  }
  .m-lg-n31 {
    margin: -10.625rem !important;
  }
  .mt-lg-n31,
  .my-lg-n31 {
    margin-top: -10.625rem !important;
  }
  .mr-lg-n31,
  .mx-lg-n31 {
    margin-right: -10.625rem !important;
  }
  .mb-lg-n31,
  .my-lg-n31 {
    margin-bottom: -10.625rem !important;
  }
  .ml-lg-n31,
  .mx-lg-n31 {
    margin-left: -10.625rem !important;
  }
  .m-lg-n32 {
    margin: -11.25rem !important;
  }
  .mt-lg-n32,
  .my-lg-n32 {
    margin-top: -11.25rem !important;
  }
  .mr-lg-n32,
  .mx-lg-n32 {
    margin-right: -11.25rem !important;
  }
  .mb-lg-n32,
  .my-lg-n32 {
    margin-bottom: -11.25rem !important;
  }
  .ml-lg-n32,
  .mx-lg-n32 {
    margin-left: -11.25rem !important;
  }
  .m-lg-n33 {
    margin: -12.5rem !important;
  }
  .mt-lg-n33,
  .my-lg-n33 {
    margin-top: -12.5rem !important;
  }
  .mr-lg-n33,
  .mx-lg-n33 {
    margin-right: -12.5rem !important;
  }
  .mb-lg-n33,
  .my-lg-n33 {
    margin-bottom: -12.5rem !important;
  }
  .ml-lg-n33,
  .mx-lg-n33 {
    margin-left: -12.5rem !important;
  }
  .m-lg-n34 {
    margin: -14.0625rem !important;
  }
  .mt-lg-n34,
  .my-lg-n34 {
    margin-top: -14.0625rem !important;
  }
  .mr-lg-n34,
  .mx-lg-n34 {
    margin-right: -14.0625rem !important;
  }
  .mb-lg-n34,
  .my-lg-n34 {
    margin-bottom: -14.0625rem !important;
  }
  .ml-lg-n34,
  .mx-lg-n34 {
    margin-left: -14.0625rem !important;
  }
  .m-lg-n35 {
    margin: -15.625rem !important;
  }
  .mt-lg-n35,
  .my-lg-n35 {
    margin-top: -15.625rem !important;
  }
  .mr-lg-n35,
  .mx-lg-n35 {
    margin-right: -15.625rem !important;
  }
  .mb-lg-n35,
  .my-lg-n35 {
    margin-bottom: -15.625rem !important;
  }
  .ml-lg-n35,
  .mx-lg-n35 {
    margin-left: -15.625rem !important;
  }
  .m-lg-auto {
    margin: auto !important;
  }
  .mt-lg-auto,
  .my-lg-auto {
    margin-top: auto !important;
  }
  .mr-lg-auto,
  .mx-lg-auto {
    margin-right: auto !important;
  }
  .mb-lg-auto,
  .my-lg-auto {
    margin-bottom: auto !important;
  }
  .ml-lg-auto,
  .mx-lg-auto {
    margin-left: auto !important;
  }
}

@media (min-width: 1200px) {
  .m-xl-0 {
    margin: 0 !important;
  }
  .mt-xl-0,
  .my-xl-0 {
    margin-top: 0 !important;
  }
  .mr-xl-0,
  .mx-xl-0 {
    margin-right: 0 !important;
  }
  .mb-xl-0,
  .my-xl-0 {
    margin-bottom: 0 !important;
  }
  .ml-xl-0,
  .mx-xl-0 {
    margin-left: 0 !important;
  }
  .m-xl-1 {
    margin: 0.25rem !important;
  }
  .mt-xl-1,
  .my-xl-1 {
    margin-top: 0.25rem !important;
  }
  .mr-xl-1,
  .mx-xl-1 {
    margin-right: 0.25rem !important;
  }
  .mb-xl-1,
  .my-xl-1 {
    margin-bottom: 0.25rem !important;
  }
  .ml-xl-1,
  .mx-xl-1 {
    margin-left: 0.25rem !important;
  }
  .m-xl-2 {
    margin: 0.375rem !important;
  }
  .mt-xl-2,
  .my-xl-2 {
    margin-top: 0.375rem !important;
  }
  .mr-xl-2,
  .mx-xl-2 {
    margin-right: 0.375rem !important;
  }
  .mb-xl-2,
  .my-xl-2 {
    margin-bottom: 0.375rem !important;
  }
  .ml-xl-2,
  .mx-xl-2 {
    margin-left: 0.375rem !important;
  }
  .m-xl-3 {
    margin: 0.5rem !important;
  }
  .mt-xl-3,
  .my-xl-3 {
    margin-top: 0.5rem !important;
  }
  .mr-xl-3,
  .mx-xl-3 {
    margin-right: 0.5rem !important;
  }
  .mb-xl-3,
  .my-xl-3 {
    margin-bottom: 0.5rem !important;
  }
  .ml-xl-3,
  .mx-xl-3 {
    margin-left: 0.5rem !important;
  }
  .m-xl-4 {
    margin: 0.625rem !important;
  }
  .mt-xl-4,
  .my-xl-4 {
    margin-top: 0.625rem !important;
  }
  .mr-xl-4,
  .mx-xl-4 {
    margin-right: 0.625rem !important;
  }
  .mb-xl-4,
  .my-xl-4 {
    margin-bottom: 0.625rem !important;
  }
  .ml-xl-4,
  .mx-xl-4 {
    margin-left: 0.625rem !important;
  }
  .m-xl-5 {
    margin: 0.75rem !important;
  }
  .mt-xl-5,
  .my-xl-5 {
    margin-top: 0.75rem !important;
  }
  .mr-xl-5,
  .mx-xl-5 {
    margin-right: 0.75rem !important;
  }
  .mb-xl-5,
  .my-xl-5 {
    margin-bottom: 0.75rem !important;
  }
  .ml-xl-5,
  .mx-xl-5 {
    margin-left: 0.75rem !important;
  }
  .m-xl-6 {
    margin: 1rem !important;
  }
  .mt-xl-6,
  .my-xl-6 {
    margin-top: 1rem !important;
  }
  .mr-xl-6,
  .mx-xl-6 {
    margin-right: 1rem !important;
  }
  .mb-xl-6,
  .my-xl-6 {
    margin-bottom: 1rem !important;
  }
  .ml-xl-6,
  .mx-xl-6 {
    margin-left: 1rem !important;
  }
  .m-xl-7 {
    margin: 1.25rem !important;
  }
  .mt-xl-7,
  .my-xl-7 {
    margin-top: 1.25rem !important;
  }
  .mr-xl-7,
  .mx-xl-7 {
    margin-right: 1.25rem !important;
  }
  .mb-xl-7,
  .my-xl-7 {
    margin-bottom: 1.25rem !important;
  }
  .ml-xl-7,
  .mx-xl-7 {
    margin-left: 1.25rem !important;
  }
  .m-xl-8 {
    margin: 1.5625rem !important;
  }
  .mt-xl-8,
  .my-xl-8 {
    margin-top: 1.5625rem !important;
  }
  .mr-xl-8,
  .mx-xl-8 {
    margin-right: 1.5625rem !important;
  }
  .mb-xl-8,
  .my-xl-8 {
    margin-bottom: 1.5625rem !important;
  }
  .ml-xl-8,
  .mx-xl-8 {
    margin-left: 1.5625rem !important;
  }
  .m-xl-9 {
    margin: 1.875rem !important;
  }
  .mt-xl-9,
  .my-xl-9 {
    margin-top: 1.875rem !important;
  }
  .mr-xl-9,
  .mx-xl-9 {
    margin-right: 1.875rem !important;
  }
  .mb-xl-9,
  .my-xl-9 {
    margin-bottom: 1.875rem !important;
  }
  .ml-xl-9,
  .mx-xl-9 {
    margin-left: 1.875rem !important;
  }
  .m-xl-10 {
    margin: 2.1875rem !important;
  }
  .mt-xl-10,
  .my-xl-10 {
    margin-top: 2.1875rem !important;
  }
  .mr-xl-10,
  .mx-xl-10 {
    margin-right: 2.1875rem !important;
  }
  .mb-xl-10,
  .my-xl-10 {
    margin-bottom: 2.1875rem !important;
  }
  .ml-xl-10,
  .mx-xl-10 {
    margin-left: 2.1875rem !important;
  }
  .m-xl-11 {
    margin: 2.5rem !important;
  }
  .mt-xl-11,
  .my-xl-11 {
    margin-top: 2.5rem !important;
  }
  .mr-xl-11,
  .mx-xl-11 {
    margin-right: 2.5rem !important;
  }
  .mb-xl-11,
  .my-xl-11 {
    margin-bottom: 2.5rem !important;
  }
  .ml-xl-11,
  .mx-xl-11 {
    margin-left: 2.5rem !important;
  }
  .m-xl-12 {
    margin: 2.8125rem !important;
  }
  .mt-xl-12,
  .my-xl-12 {
    margin-top: 2.8125rem !important;
  }
  .mr-xl-12,
  .mx-xl-12 {
    margin-right: 2.8125rem !important;
  }
  .mb-xl-12,
  .my-xl-12 {
    margin-bottom: 2.8125rem !important;
  }
  .ml-xl-12,
  .mx-xl-12 {
    margin-left: 2.8125rem !important;
  }
  .m-xl-13 {
    margin: 3.125rem !important;
  }
  .mt-xl-13,
  .my-xl-13 {
    margin-top: 3.125rem !important;
  }
  .mr-xl-13,
  .mx-xl-13 {
    margin-right: 3.125rem !important;
  }
  .mb-xl-13,
  .my-xl-13 {
    margin-bottom: 3.125rem !important;
  }
  .ml-xl-13,
  .mx-xl-13 {
    margin-left: 3.125rem !important;
  }
  .m-xl-14 {
    margin: 3.4375rem !important;
  }
  .mt-xl-14,
  .my-xl-14 {
    margin-top: 3.4375rem !important;
  }
  .mr-xl-14,
  .mx-xl-14 {
    margin-right: 3.4375rem !important;
  }
  .mb-xl-14,
  .my-xl-14 {
    margin-bottom: 3.4375rem !important;
  }
  .ml-xl-14,
  .mx-xl-14 {
    margin-left: 3.4375rem !important;
  }
  .m-xl-15 {
    margin: 3.75rem !important;
  }
  .mt-xl-15,
  .my-xl-15 {
    margin-top: 3.75rem !important;
  }
  .mr-xl-15,
  .mx-xl-15 {
    margin-right: 3.75rem !important;
  }
  .mb-xl-15,
  .my-xl-15 {
    margin-bottom: 3.75rem !important;
  }
  .ml-xl-15,
  .mx-xl-15 {
    margin-left: 3.75rem !important;
  }
  .m-xl-16 {
    margin: 4.0625rem !important;
  }
  .mt-xl-16,
  .my-xl-16 {
    margin-top: 4.0625rem !important;
  }
  .mr-xl-16,
  .mx-xl-16 {
    margin-right: 4.0625rem !important;
  }
  .mb-xl-16,
  .my-xl-16 {
    margin-bottom: 4.0625rem !important;
  }
  .ml-xl-16,
  .mx-xl-16 {
    margin-left: 4.0625rem !important;
  }
  .m-xl-17 {
    margin: 4.375rem !important;
  }
  .mt-xl-17,
  .my-xl-17 {
    margin-top: 4.375rem !important;
  }
  .mr-xl-17,
  .mx-xl-17 {
    margin-right: 4.375rem !important;
  }
  .mb-xl-17,
  .my-xl-17 {
    margin-bottom: 4.375rem !important;
  }
  .ml-xl-17,
  .mx-xl-17 {
    margin-left: 4.375rem !important;
  }
  .m-xl-18 {
    margin: 4.6875rem !important;
  }
  .mt-xl-18,
  .my-xl-18 {
    margin-top: 4.6875rem !important;
  }
  .mr-xl-18,
  .mx-xl-18 {
    margin-right: 4.6875rem !important;
  }
  .mb-xl-18,
  .my-xl-18 {
    margin-bottom: 4.6875rem !important;
  }
  .ml-xl-18,
  .mx-xl-18 {
    margin-left: 4.6875rem !important;
  }
  .m-xl-19 {
    margin: 5rem !important;
  }
  .mt-xl-19,
  .my-xl-19 {
    margin-top: 5rem !important;
  }
  .mr-xl-19,
  .mx-xl-19 {
    margin-right: 5rem !important;
  }
  .mb-xl-19,
  .my-xl-19 {
    margin-bottom: 5rem !important;
  }
  .ml-xl-19,
  .mx-xl-19 {
    margin-left: 5rem !important;
  }
  .m-xl-20 {
    margin: 5.3125rem !important;
  }
  .mt-xl-20,
  .my-xl-20 {
    margin-top: 5.3125rem !important;
  }
  .mr-xl-20,
  .mx-xl-20 {
    margin-right: 5.3125rem !important;
  }
  .mb-xl-20,
  .my-xl-20 {
    margin-bottom: 5.3125rem !important;
  }
  .ml-xl-20,
  .mx-xl-20 {
    margin-left: 5.3125rem !important;
  }
  .m-xl-21 {
    margin: 5.625rem !important;
  }
  .mt-xl-21,
  .my-xl-21 {
    margin-top: 5.625rem !important;
  }
  .mr-xl-21,
  .mx-xl-21 {
    margin-right: 5.625rem !important;
  }
  .mb-xl-21,
  .my-xl-21 {
    margin-bottom: 5.625rem !important;
  }
  .ml-xl-21,
  .mx-xl-21 {
    margin-left: 5.625rem !important;
  }
  .m-xl-22 {
    margin: 5.9375rem !important;
  }
  .mt-xl-22,
  .my-xl-22 {
    margin-top: 5.9375rem !important;
  }
  .mr-xl-22,
  .mx-xl-22 {
    margin-right: 5.9375rem !important;
  }
  .mb-xl-22,
  .my-xl-22 {
    margin-bottom: 5.9375rem !important;
  }
  .ml-xl-22,
  .mx-xl-22 {
    margin-left: 5.9375rem !important;
  }
  .m-xl-23 {
    margin: 6.25rem !important;
  }
  .mt-xl-23,
  .my-xl-23 {
    margin-top: 6.25rem !important;
  }
  .mr-xl-23,
  .mx-xl-23 {
    margin-right: 6.25rem !important;
  }
  .mb-xl-23,
  .my-xl-23 {
    margin-bottom: 6.25rem !important;
  }
  .ml-xl-23,
  .mx-xl-23 {
    margin-left: 6.25rem !important;
  }
  .m-xl-24 {
    margin: 6.875rem !important;
  }
  .mt-xl-24,
  .my-xl-24 {
    margin-top: 6.875rem !important;
  }
  .mr-xl-24,
  .mx-xl-24 {
    margin-right: 6.875rem !important;
  }
  .mb-xl-24,
  .my-xl-24 {
    margin-bottom: 6.875rem !important;
  }
  .ml-xl-24,
  .mx-xl-24 {
    margin-left: 6.875rem !important;
  }
  .m-xl-25 {
    margin: 7.5rem !important;
  }
  .mt-xl-25,
  .my-xl-25 {
    margin-top: 7.5rem !important;
  }
  .mr-xl-25,
  .mx-xl-25 {
    margin-right: 7.5rem !important;
  }
  .mb-xl-25,
  .my-xl-25 {
    margin-bottom: 7.5rem !important;
  }
  .ml-xl-25,
  .mx-xl-25 {
    margin-left: 7.5rem !important;
  }
  .m-xl-26 {
    margin: 8.125rem !important;
  }
  .mt-xl-26,
  .my-xl-26 {
    margin-top: 8.125rem !important;
  }
  .mr-xl-26,
  .mx-xl-26 {
    margin-right: 8.125rem !important;
  }
  .mb-xl-26,
  .my-xl-26 {
    margin-bottom: 8.125rem !important;
  }
  .ml-xl-26,
  .mx-xl-26 {
    margin-left: 8.125rem !important;
  }
  .m-xl-27 {
    margin: 8.4375rem !important;
  }
  .mt-xl-27,
  .my-xl-27 {
    margin-top: 8.4375rem !important;
  }
  .mr-xl-27,
  .mx-xl-27 {
    margin-right: 8.4375rem !important;
  }
  .mb-xl-27,
  .my-xl-27 {
    margin-bottom: 8.4375rem !important;
  }
  .ml-xl-27,
  .mx-xl-27 {
    margin-left: 8.4375rem !important;
  }
  .m-xl-28 {
    margin: 9.0625rem !important;
  }
  .mt-xl-28,
  .my-xl-28 {
    margin-top: 9.0625rem !important;
  }
  .mr-xl-28,
  .mx-xl-28 {
    margin-right: 9.0625rem !important;
  }
  .mb-xl-28,
  .my-xl-28 {
    margin-bottom: 9.0625rem !important;
  }
  .ml-xl-28,
  .mx-xl-28 {
    margin-left: 9.0625rem !important;
  }
  .m-xl-29 {
    margin: 9.375rem !important;
  }
  .mt-xl-29,
  .my-xl-29 {
    margin-top: 9.375rem !important;
  }
  .mr-xl-29,
  .mx-xl-29 {
    margin-right: 9.375rem !important;
  }
  .mb-xl-29,
  .my-xl-29 {
    margin-bottom: 9.375rem !important;
  }
  .ml-xl-29,
  .mx-xl-29 {
    margin-left: 9.375rem !important;
  }
  .m-xl-30 {
    margin: 9.6875rem !important;
  }
  .mt-xl-30,
  .my-xl-30 {
    margin-top: 9.6875rem !important;
  }
  .mr-xl-30,
  .mx-xl-30 {
    margin-right: 9.6875rem !important;
  }
  .mb-xl-30,
  .my-xl-30 {
    margin-bottom: 9.6875rem !important;
  }
  .ml-xl-30,
  .mx-xl-30 {
    margin-left: 9.6875rem !important;
  }
  .m-xl-31 {
    margin: 10.625rem !important;
  }
  .mt-xl-31,
  .my-xl-31 {
    margin-top: 10.625rem !important;
  }
  .mr-xl-31,
  .mx-xl-31 {
    margin-right: 10.625rem !important;
  }
  .mb-xl-31,
  .my-xl-31 {
    margin-bottom: 10.625rem !important;
  }
  .ml-xl-31,
  .mx-xl-31 {
    margin-left: 10.625rem !important;
  }
  .m-xl-32 {
    margin: 11.25rem !important;
  }
  .mt-xl-32,
  .my-xl-32 {
    margin-top: 11.25rem !important;
  }
  .mr-xl-32,
  .mx-xl-32 {
    margin-right: 11.25rem !important;
  }
  .mb-xl-32,
  .my-xl-32 {
    margin-bottom: 11.25rem !important;
  }
  .ml-xl-32,
  .mx-xl-32 {
    margin-left: 11.25rem !important;
  }
  .m-xl-33 {
    margin: 12.5rem !important;
  }
  .mt-xl-33,
  .my-xl-33 {
    margin-top: 12.5rem !important;
  }
  .mr-xl-33,
  .mx-xl-33 {
    margin-right: 12.5rem !important;
  }
  .mb-xl-33,
  .my-xl-33 {
    margin-bottom: 12.5rem !important;
  }
  .ml-xl-33,
  .mx-xl-33 {
    margin-left: 12.5rem !important;
  }
  .m-xl-34 {
    margin: 14.0625rem !important;
  }
  .mt-xl-34,
  .my-xl-34 {
    margin-top: 14.0625rem !important;
  }
  .mr-xl-34,
  .mx-xl-34 {
    margin-right: 14.0625rem !important;
  }
  .mb-xl-34,
  .my-xl-34 {
    margin-bottom: 14.0625rem !important;
  }
  .ml-xl-34,
  .mx-xl-34 {
    margin-left: 14.0625rem !important;
  }
  .m-xl-35 {
    margin: 15.625rem !important;
  }
  .mt-xl-35,
  .my-xl-35 {
    margin-top: 15.625rem !important;
  }
  .mr-xl-35,
  .mx-xl-35 {
    margin-right: 15.625rem !important;
  }
  .mb-xl-35,
  .my-xl-35 {
    margin-bottom: 15.625rem !important;
  }
  .ml-xl-35,
  .mx-xl-35 {
    margin-left: 15.625rem !important;
  }
  .p-xl-0 {
    padding: 0 !important;
  }
  .pt-xl-0,
  .py-xl-0 {
    padding-top: 0 !important;
  }
  .pr-xl-0,
  .px-xl-0 {
    padding-right: 0 !important;
  }
  .pb-xl-0,
  .py-xl-0 {
    padding-bottom: 0 !important;
  }
  .pl-xl-0,
  .px-xl-0 {
    padding-left: 0 !important;
  }
  .p-xl-1 {
    padding: 0.25rem !important;
  }
  .pt-xl-1,
  .py-xl-1 {
    padding-top: 0.25rem !important;
  }
  .pr-xl-1,
  .px-xl-1 {
    padding-right: 0.25rem !important;
  }
  .pb-xl-1,
  .py-xl-1 {
    padding-bottom: 0.25rem !important;
  }
  .pl-xl-1,
  .px-xl-1 {
    padding-left: 0.25rem !important;
  }
  .p-xl-2 {
    padding: 0.375rem !important;
  }
  .pt-xl-2,
  .py-xl-2 {
    padding-top: 0.375rem !important;
  }
  .pr-xl-2,
  .px-xl-2 {
    padding-right: 0.375rem !important;
  }
  .pb-xl-2,
  .py-xl-2 {
    padding-bottom: 0.375rem !important;
  }
  .pl-xl-2,
  .px-xl-2 {
    padding-left: 0.375rem !important;
  }
  .p-xl-3 {
    padding: 0.5rem !important;
  }
  .pt-xl-3,
  .py-xl-3 {
    padding-top: 0.5rem !important;
  }
  .pr-xl-3,
  .px-xl-3 {
    padding-right: 0.5rem !important;
  }
  .pb-xl-3,
  .py-xl-3 {
    padding-bottom: 0.5rem !important;
  }
  .pl-xl-3,
  .px-xl-3 {
    padding-left: 0.5rem !important;
  }
  .p-xl-4 {
    padding: 0.625rem !important;
  }
  .pt-xl-4,
  .py-xl-4 {
    padding-top: 0.625rem !important;
  }
  .pr-xl-4,
  .px-xl-4 {
    padding-right: 0.625rem !important;
  }
  .pb-xl-4,
  .py-xl-4 {
    padding-bottom: 0.625rem !important;
  }
  .pl-xl-4,
  .px-xl-4 {
    padding-left: 0.625rem !important;
  }
  .p-xl-5 {
    padding: 0.75rem !important;
  }
  .pt-xl-5,
  .py-xl-5 {
    padding-top: 0.75rem !important;
  }
  .pr-xl-5,
  .px-xl-5 {
    padding-right: 0.75rem !important;
  }
  .pb-xl-5,
  .py-xl-5 {
    padding-bottom: 0.75rem !important;
  }
  .pl-xl-5,
  .px-xl-5 {
    padding-left: 0.75rem !important;
  }
  .p-xl-6 {
    padding: 1rem !important;
  }
  .pt-xl-6,
  .py-xl-6 {
    padding-top: 1rem !important;
  }
  .pr-xl-6,
  .px-xl-6 {
    padding-right: 1rem !important;
  }
  .pb-xl-6,
  .py-xl-6 {
    padding-bottom: 1rem !important;
  }
  .pl-xl-6,
  .px-xl-6 {
    padding-left: 1rem !important;
  }
  .p-xl-7 {
    padding: 1.25rem !important;
  }
  .pt-xl-7,
  .py-xl-7 {
    padding-top: 1.25rem !important;
  }
  .pr-xl-7,
  .px-xl-7 {
    padding-right: 1.25rem !important;
  }
  .pb-xl-7,
  .py-xl-7 {
    padding-bottom: 1.25rem !important;
  }
  .pl-xl-7,
  .px-xl-7 {
    padding-left: 1.25rem !important;
  }
  .p-xl-8 {
    padding: 1.5625rem !important;
  }
  .pt-xl-8,
  .py-xl-8 {
    padding-top: 1.5625rem !important;
  }
  .pr-xl-8,
  .px-xl-8 {
    padding-right: 1.5625rem !important;
  }
  .pb-xl-8,
  .py-xl-8 {
    padding-bottom: 1.5625rem !important;
  }
  .pl-xl-8,
  .px-xl-8 {
    padding-left: 1.5625rem !important;
  }
  .p-xl-9 {
    padding: 1.875rem !important;
  }
  .pt-xl-9,
  .py-xl-9 {
    padding-top: 1.875rem !important;
  }
  .pr-xl-9,
  .px-xl-9 {
    padding-right: 1.875rem !important;
  }
  .pb-xl-9,
  .py-xl-9 {
    padding-bottom: 1.875rem !important;
  }
  .pl-xl-9,
  .px-xl-9 {
    padding-left: 1.875rem !important;
  }
  .p-xl-10 {
    padding: 2.1875rem !important;
  }
  .pt-xl-10,
  .py-xl-10 {
    padding-top: 2.1875rem !important;
  }
  .pr-xl-10,
  .px-xl-10 {
    padding-right: 2.1875rem !important;
  }
  .pb-xl-10,
  .py-xl-10 {
    padding-bottom: 2.1875rem !important;
  }
  .pl-xl-10,
  .px-xl-10 {
    padding-left: 2.1875rem !important;
  }
  .p-xl-11 {
    padding: 2.5rem !important;
  }
  .pt-xl-11,
  .py-xl-11 {
    padding-top: 2.5rem !important;
  }
  .pr-xl-11,
  .px-xl-11 {
    padding-right: 2.5rem !important;
  }
  .pb-xl-11,
  .py-xl-11 {
    padding-bottom: 2.5rem !important;
  }
  .pl-xl-11,
  .px-xl-11 {
    padding-left: 2.5rem !important;
  }
  .p-xl-12 {
    padding: 2.8125rem !important;
  }
  .pt-xl-12,
  .py-xl-12 {
    padding-top: 2.8125rem !important;
  }
  .pr-xl-12,
  .px-xl-12 {
    padding-right: 2.8125rem !important;
  }
  .pb-xl-12,
  .py-xl-12 {
    padding-bottom: 2.8125rem !important;
  }
  .pl-xl-12,
  .px-xl-12 {
    padding-left: 2.8125rem !important;
  }
  .p-xl-13 {
    padding: 3.125rem !important;
  }
  .pt-xl-13,
  .py-xl-13 {
    padding-top: 3.125rem !important;
  }
  .pr-xl-13,
  .px-xl-13 {
    padding-right: 3.125rem !important;
  }
  .pb-xl-13,
  .py-xl-13 {
    padding-bottom: 3.125rem !important;
  }
  .pl-xl-13,
  .px-xl-13 {
    padding-left: 3.125rem !important;
  }
  .p-xl-14 {
    padding: 3.4375rem !important;
  }
  .pt-xl-14,
  .py-xl-14 {
    padding-top: 3.4375rem !important;
  }
  .pr-xl-14,
  .px-xl-14 {
    padding-right: 3.4375rem !important;
  }
  .pb-xl-14,
  .py-xl-14 {
    padding-bottom: 3.4375rem !important;
  }
  .pl-xl-14,
  .px-xl-14 {
    padding-left: 3.4375rem !important;
  }
  .p-xl-15 {
    padding: 3.75rem !important;
  }
  .pt-xl-15,
  .py-xl-15 {
    padding-top: 3.75rem !important;
  }
  .pr-xl-15,
  .px-xl-15 {
    padding-right: 3.75rem !important;
  }
  .pb-xl-15,
  .py-xl-15 {
    padding-bottom: 3.75rem !important;
  }
  .pl-xl-15,
  .px-xl-15 {
    padding-left: 3.75rem !important;
  }
  .p-xl-16 {
    padding: 4.0625rem !important;
  }
  .pt-xl-16,
  .py-xl-16 {
    padding-top: 4.0625rem !important;
  }
  .pr-xl-16,
  .px-xl-16 {
    padding-right: 4.0625rem !important;
  }
  .pb-xl-16,
  .py-xl-16 {
    padding-bottom: 4.0625rem !important;
  }
  .pl-xl-16,
  .px-xl-16 {
    padding-left: 4.0625rem !important;
  }
  .p-xl-17 {
    padding: 4.375rem !important;
  }
  .pt-xl-17,
  .py-xl-17 {
    padding-top: 4.375rem !important;
  }
  .pr-xl-17,
  .px-xl-17 {
    padding-right: 4.375rem !important;
  }
  .pb-xl-17,
  .py-xl-17 {
    padding-bottom: 4.375rem !important;
  }
  .pl-xl-17,
  .px-xl-17 {
    padding-left: 4.375rem !important;
  }
  .p-xl-18 {
    padding: 4.6875rem !important;
  }
  .pt-xl-18,
  .py-xl-18 {
    padding-top: 4.6875rem !important;
  }
  .pr-xl-18,
  .px-xl-18 {
    padding-right: 4.6875rem !important;
  }
  .pb-xl-18,
  .py-xl-18 {
    padding-bottom: 4.6875rem !important;
  }
  .pl-xl-18,
  .px-xl-18 {
    padding-left: 4.6875rem !important;
  }
  .p-xl-19 {
    padding: 5rem !important;
  }
  .pt-xl-19,
  .py-xl-19 {
    padding-top: 5rem !important;
  }
  .pr-xl-19,
  .px-xl-19 {
    padding-right: 5rem !important;
  }
  .pb-xl-19,
  .py-xl-19 {
    padding-bottom: 5rem !important;
  }
  .pl-xl-19,
  .px-xl-19 {
    padding-left: 5rem !important;
  }
  .p-xl-20 {
    padding: 5.3125rem !important;
  }
  .pt-xl-20,
  .py-xl-20 {
    padding-top: 5.3125rem !important;
  }
  .pr-xl-20,
  .px-xl-20 {
    padding-right: 5.3125rem !important;
  }
  .pb-xl-20,
  .py-xl-20 {
    padding-bottom: 5.3125rem !important;
  }
  .pl-xl-20,
  .px-xl-20 {
    padding-left: 5.3125rem !important;
  }
  .p-xl-21 {
    padding: 5.625rem !important;
  }
  .pt-xl-21,
  .py-xl-21 {
    padding-top: 5.625rem !important;
  }
  .pr-xl-21,
  .px-xl-21 {
    padding-right: 5.625rem !important;
  }
  .pb-xl-21,
  .py-xl-21 {
    padding-bottom: 5.625rem !important;
  }
  .pl-xl-21,
  .px-xl-21 {
    padding-left: 5.625rem !important;
  }
  .p-xl-22 {
    padding: 5.9375rem !important;
  }
  .pt-xl-22,
  .py-xl-22 {
    padding-top: 5.9375rem !important;
  }
  .pr-xl-22,
  .px-xl-22 {
    padding-right: 5.9375rem !important;
  }
  .pb-xl-22,
  .py-xl-22 {
    padding-bottom: 5.9375rem !important;
  }
  .pl-xl-22,
  .px-xl-22 {
    padding-left: 5.9375rem !important;
  }
  .p-xl-23 {
    padding: 6.25rem !important;
  }
  .pt-xl-23,
  .py-xl-23 {
    padding-top: 6.25rem !important;
  }
  .pr-xl-23,
  .px-xl-23 {
    padding-right: 6.25rem !important;
  }
  .pb-xl-23,
  .py-xl-23 {
    padding-bottom: 6.25rem !important;
  }
  .pl-xl-23,
  .px-xl-23 {
    padding-left: 6.25rem !important;
  }
  .p-xl-24 {
    padding: 6.875rem !important;
  }
  .pt-xl-24,
  .py-xl-24 {
    padding-top: 6.875rem !important;
  }
  .pr-xl-24,
  .px-xl-24 {
    padding-right: 6.875rem !important;
  }
  .pb-xl-24,
  .py-xl-24 {
    padding-bottom: 6.875rem !important;
  }
  .pl-xl-24,
  .px-xl-24 {
    padding-left: 6.875rem !important;
  }
  .p-xl-25 {
    padding: 7.5rem !important;
  }
  .pt-xl-25,
  .py-xl-25 {
    padding-top: 7.5rem !important;
  }
  .pr-xl-25,
  .px-xl-25 {
    padding-right: 7.5rem !important;
  }
  .pb-xl-25,
  .py-xl-25 {
    padding-bottom: 7.5rem !important;
  }
  .pl-xl-25,
  .px-xl-25 {
    padding-left: 7.5rem !important;
  }
  .p-xl-26 {
    padding: 8.125rem !important;
  }
  .pt-xl-26,
  .py-xl-26 {
    padding-top: 8.125rem !important;
  }
  .pr-xl-26,
  .px-xl-26 {
    padding-right: 8.125rem !important;
  }
  .pb-xl-26,
  .py-xl-26 {
    padding-bottom: 8.125rem !important;
  }
  .pl-xl-26,
  .px-xl-26 {
    padding-left: 8.125rem !important;
  }
  .p-xl-27 {
    padding: 8.4375rem !important;
  }
  .pt-xl-27,
  .py-xl-27 {
    padding-top: 8.4375rem !important;
  }
  .pr-xl-27,
  .px-xl-27 {
    padding-right: 8.4375rem !important;
  }
  .pb-xl-27,
  .py-xl-27 {
    padding-bottom: 8.4375rem !important;
  }
  .pl-xl-27,
  .px-xl-27 {
    padding-left: 8.4375rem !important;
  }
  .p-xl-28 {
    padding: 9.0625rem !important;
  }
  .pt-xl-28,
  .py-xl-28 {
    padding-top: 9.0625rem !important;
  }
  .pr-xl-28,
  .px-xl-28 {
    padding-right: 9.0625rem !important;
  }
  .pb-xl-28,
  .py-xl-28 {
    padding-bottom: 9.0625rem !important;
  }
  .pl-xl-28,
  .px-xl-28 {
    padding-left: 9.0625rem !important;
  }
  .p-xl-29 {
    padding: 9.375rem !important;
  }
  .pt-xl-29,
  .py-xl-29 {
    padding-top: 9.375rem !important;
  }
  .pr-xl-29,
  .px-xl-29 {
    padding-right: 9.375rem !important;
  }
  .pb-xl-29,
  .py-xl-29 {
    padding-bottom: 9.375rem !important;
  }
  .pl-xl-29,
  .px-xl-29 {
    padding-left: 9.375rem !important;
  }
  .p-xl-30 {
    padding: 9.6875rem !important;
  }
  .pt-xl-30,
  .py-xl-30 {
    padding-top: 9.6875rem !important;
  }
  .pr-xl-30,
  .px-xl-30 {
    padding-right: 9.6875rem !important;
  }
  .pb-xl-30,
  .py-xl-30 {
    padding-bottom: 9.6875rem !important;
  }
  .pl-xl-30,
  .px-xl-30 {
    padding-left: 9.6875rem !important;
  }
  .p-xl-31 {
    padding: 10.625rem !important;
  }
  .pt-xl-31,
  .py-xl-31 {
    padding-top: 10.625rem !important;
  }
  .pr-xl-31,
  .px-xl-31 {
    padding-right: 10.625rem !important;
  }
  .pb-xl-31,
  .py-xl-31 {
    padding-bottom: 10.625rem !important;
  }
  .pl-xl-31,
  .px-xl-31 {
    padding-left: 10.625rem !important;
  }
  .p-xl-32 {
    padding: 11.25rem !important;
  }
  .pt-xl-32,
  .py-xl-32 {
    padding-top: 11.25rem !important;
  }
  .pr-xl-32,
  .px-xl-32 {
    padding-right: 11.25rem !important;
  }
  .pb-xl-32,
  .py-xl-32 {
    padding-bottom: 11.25rem !important;
  }
  .pl-xl-32,
  .px-xl-32 {
    padding-left: 11.25rem !important;
  }
  .p-xl-33 {
    padding: 12.5rem !important;
  }
  .pt-xl-33,
  .py-xl-33 {
    padding-top: 12.5rem !important;
  }
  .pr-xl-33,
  .px-xl-33 {
    padding-right: 12.5rem !important;
  }
  .pb-xl-33,
  .py-xl-33 {
    padding-bottom: 12.5rem !important;
  }
  .pl-xl-33,
  .px-xl-33 {
    padding-left: 12.5rem !important;
  }
  .p-xl-34 {
    padding: 14.0625rem !important;
  }
  .pt-xl-34,
  .py-xl-34 {
    padding-top: 14.0625rem !important;
  }
  .pr-xl-34,
  .px-xl-34 {
    padding-right: 14.0625rem !important;
  }
  .pb-xl-34,
  .py-xl-34 {
    padding-bottom: 14.0625rem !important;
  }
  .pl-xl-34,
  .px-xl-34 {
    padding-left: 14.0625rem !important;
  }
  .p-xl-35 {
    padding: 15.625rem !important;
  }
  .pt-xl-35,
  .py-xl-35 {
    padding-top: 15.625rem !important;
  }
  .pr-xl-35,
  .px-xl-35 {
    padding-right: 15.625rem !important;
  }
  .pb-xl-35,
  .py-xl-35 {
    padding-bottom: 15.625rem !important;
  }
  .pl-xl-35,
  .px-xl-35 {
    padding-left: 15.625rem !important;
  }
  .m-xl-n1 {
    margin: -0.25rem !important;
  }
  .mt-xl-n1,
  .my-xl-n1 {
    margin-top: -0.25rem !important;
  }
  .mr-xl-n1,
  .mx-xl-n1 {
    margin-right: -0.25rem !important;
  }
  .mb-xl-n1,
  .my-xl-n1 {
    margin-bottom: -0.25rem !important;
  }
  .ml-xl-n1,
  .mx-xl-n1 {
    margin-left: -0.25rem !important;
  }
  .m-xl-n2 {
    margin: -0.375rem !important;
  }
  .mt-xl-n2,
  .my-xl-n2 {
    margin-top: -0.375rem !important;
  }
  .mr-xl-n2,
  .mx-xl-n2 {
    margin-right: -0.375rem !important;
  }
  .mb-xl-n2,
  .my-xl-n2 {
    margin-bottom: -0.375rem !important;
  }
  .ml-xl-n2,
  .mx-xl-n2 {
    margin-left: -0.375rem !important;
  }
  .m-xl-n3 {
    margin: -0.5rem !important;
  }
  .mt-xl-n3,
  .my-xl-n3 {
    margin-top: -0.5rem !important;
  }
  .mr-xl-n3,
  .mx-xl-n3 {
    margin-right: -0.5rem !important;
  }
  .mb-xl-n3,
  .my-xl-n3 {
    margin-bottom: -0.5rem !important;
  }
  .ml-xl-n3,
  .mx-xl-n3 {
    margin-left: -0.5rem !important;
  }
  .m-xl-n4 {
    margin: -0.625rem !important;
  }
  .mt-xl-n4,
  .my-xl-n4 {
    margin-top: -0.625rem !important;
  }
  .mr-xl-n4,
  .mx-xl-n4 {
    margin-right: -0.625rem !important;
  }
  .mb-xl-n4,
  .my-xl-n4 {
    margin-bottom: -0.625rem !important;
  }
  .ml-xl-n4,
  .mx-xl-n4 {
    margin-left: -0.625rem !important;
  }
  .m-xl-n5 {
    margin: -0.75rem !important;
  }
  .mt-xl-n5,
  .my-xl-n5 {
    margin-top: -0.75rem !important;
  }
  .mr-xl-n5,
  .mx-xl-n5 {
    margin-right: -0.75rem !important;
  }
  .mb-xl-n5,
  .my-xl-n5 {
    margin-bottom: -0.75rem !important;
  }
  .ml-xl-n5,
  .mx-xl-n5 {
    margin-left: -0.75rem !important;
  }
  .m-xl-n6 {
    margin: -1rem !important;
  }
  .mt-xl-n6,
  .my-xl-n6 {
    margin-top: -1rem !important;
  }
  .mr-xl-n6,
  .mx-xl-n6 {
    margin-right: -1rem !important;
  }
  .mb-xl-n6,
  .my-xl-n6 {
    margin-bottom: -1rem !important;
  }
  .ml-xl-n6,
  .mx-xl-n6 {
    margin-left: -1rem !important;
  }
  .m-xl-n7 {
    margin: -1.25rem !important;
  }
  .mt-xl-n7,
  .my-xl-n7 {
    margin-top: -1.25rem !important;
  }
  .mr-xl-n7,
  .mx-xl-n7 {
    margin-right: -1.25rem !important;
  }
  .mb-xl-n7,
  .my-xl-n7 {
    margin-bottom: -1.25rem !important;
  }
  .ml-xl-n7,
  .mx-xl-n7 {
    margin-left: -1.25rem !important;
  }
  .m-xl-n8 {
    margin: -1.5625rem !important;
  }
  .mt-xl-n8,
  .my-xl-n8 {
    margin-top: -1.5625rem !important;
  }
  .mr-xl-n8,
  .mx-xl-n8 {
    margin-right: -1.5625rem !important;
  }
  .mb-xl-n8,
  .my-xl-n8 {
    margin-bottom: -1.5625rem !important;
  }
  .ml-xl-n8,
  .mx-xl-n8 {
    margin-left: -1.5625rem !important;
  }
  .m-xl-n9 {
    margin: -1.875rem !important;
  }
  .mt-xl-n9,
  .my-xl-n9 {
    margin-top: -1.875rem !important;
  }
  .mr-xl-n9,
  .mx-xl-n9 {
    margin-right: -1.875rem !important;
  }
  .mb-xl-n9,
  .my-xl-n9 {
    margin-bottom: -1.875rem !important;
  }
  .ml-xl-n9,
  .mx-xl-n9 {
    margin-left: -1.875rem !important;
  }
  .m-xl-n10 {
    margin: -2.1875rem !important;
  }
  .mt-xl-n10,
  .my-xl-n10 {
    margin-top: -2.1875rem !important;
  }
  .mr-xl-n10,
  .mx-xl-n10 {
    margin-right: -2.1875rem !important;
  }
  .mb-xl-n10,
  .my-xl-n10 {
    margin-bottom: -2.1875rem !important;
  }
  .ml-xl-n10,
  .mx-xl-n10 {
    margin-left: -2.1875rem !important;
  }
  .m-xl-n11 {
    margin: -2.5rem !important;
  }
  .mt-xl-n11,
  .my-xl-n11 {
    margin-top: -2.5rem !important;
  }
  .mr-xl-n11,
  .mx-xl-n11 {
    margin-right: -2.5rem !important;
  }
  .mb-xl-n11,
  .my-xl-n11 {
    margin-bottom: -2.5rem !important;
  }
  .ml-xl-n11,
  .mx-xl-n11 {
    margin-left: -2.5rem !important;
  }
  .m-xl-n12 {
    margin: -2.8125rem !important;
  }
  .mt-xl-n12,
  .my-xl-n12 {
    margin-top: -2.8125rem !important;
  }
  .mr-xl-n12,
  .mx-xl-n12 {
    margin-right: -2.8125rem !important;
  }
  .mb-xl-n12,
  .my-xl-n12 {
    margin-bottom: -2.8125rem !important;
  }
  .ml-xl-n12,
  .mx-xl-n12 {
    margin-left: -2.8125rem !important;
  }
  .m-xl-n13 {
    margin: -3.125rem !important;
  }
  .mt-xl-n13,
  .my-xl-n13 {
    margin-top: -3.125rem !important;
  }
  .mr-xl-n13,
  .mx-xl-n13 {
    margin-right: -3.125rem !important;
  }
  .mb-xl-n13,
  .my-xl-n13 {
    margin-bottom: -3.125rem !important;
  }
  .ml-xl-n13,
  .mx-xl-n13 {
    margin-left: -3.125rem !important;
  }
  .m-xl-n14 {
    margin: -3.4375rem !important;
  }
  .mt-xl-n14,
  .my-xl-n14 {
    margin-top: -3.4375rem !important;
  }
  .mr-xl-n14,
  .mx-xl-n14 {
    margin-right: -3.4375rem !important;
  }
  .mb-xl-n14,
  .my-xl-n14 {
    margin-bottom: -3.4375rem !important;
  }
  .ml-xl-n14,
  .mx-xl-n14 {
    margin-left: -3.4375rem !important;
  }
  .m-xl-n15 {
    margin: -3.75rem !important;
  }
  .mt-xl-n15,
  .my-xl-n15 {
    margin-top: -3.75rem !important;
  }
  .mr-xl-n15,
  .mx-xl-n15 {
    margin-right: -3.75rem !important;
  }
  .mb-xl-n15,
  .my-xl-n15 {
    margin-bottom: -3.75rem !important;
  }
  .ml-xl-n15,
  .mx-xl-n15 {
    margin-left: -3.75rem !important;
  }
  .m-xl-n16 {
    margin: -4.0625rem !important;
  }
  .mt-xl-n16,
  .my-xl-n16 {
    margin-top: -4.0625rem !important;
  }
  .mr-xl-n16,
  .mx-xl-n16 {
    margin-right: -4.0625rem !important;
  }
  .mb-xl-n16,
  .my-xl-n16 {
    margin-bottom: -4.0625rem !important;
  }
  .ml-xl-n16,
  .mx-xl-n16 {
    margin-left: -4.0625rem !important;
  }
  .m-xl-n17 {
    margin: -4.375rem !important;
  }
  .mt-xl-n17,
  .my-xl-n17 {
    margin-top: -4.375rem !important;
  }
  .mr-xl-n17,
  .mx-xl-n17 {
    margin-right: -4.375rem !important;
  }
  .mb-xl-n17,
  .my-xl-n17 {
    margin-bottom: -4.375rem !important;
  }
  .ml-xl-n17,
  .mx-xl-n17 {
    margin-left: -4.375rem !important;
  }
  .m-xl-n18 {
    margin: -4.6875rem !important;
  }
  .mt-xl-n18,
  .my-xl-n18 {
    margin-top: -4.6875rem !important;
  }
  .mr-xl-n18,
  .mx-xl-n18 {
    margin-right: -4.6875rem !important;
  }
  .mb-xl-n18,
  .my-xl-n18 {
    margin-bottom: -4.6875rem !important;
  }
  .ml-xl-n18,
  .mx-xl-n18 {
    margin-left: -4.6875rem !important;
  }
  .m-xl-n19 {
    margin: -5rem !important;
  }
  .mt-xl-n19,
  .my-xl-n19 {
    margin-top: -5rem !important;
  }
  .mr-xl-n19,
  .mx-xl-n19 {
    margin-right: -5rem !important;
  }
  .mb-xl-n19,
  .my-xl-n19 {
    margin-bottom: -5rem !important;
  }
  .ml-xl-n19,
  .mx-xl-n19 {
    margin-left: -5rem !important;
  }
  .m-xl-n20 {
    margin: -5.3125rem !important;
  }
  .mt-xl-n20,
  .my-xl-n20 {
    margin-top: -5.3125rem !important;
  }
  .mr-xl-n20,
  .mx-xl-n20 {
    margin-right: -5.3125rem !important;
  }
  .mb-xl-n20,
  .my-xl-n20 {
    margin-bottom: -5.3125rem !important;
  }
  .ml-xl-n20,
  .mx-xl-n20 {
    margin-left: -5.3125rem !important;
  }
  .m-xl-n21 {
    margin: -5.625rem !important;
  }
  .mt-xl-n21,
  .my-xl-n21 {
    margin-top: -5.625rem !important;
  }
  .mr-xl-n21,
  .mx-xl-n21 {
    margin-right: -5.625rem !important;
  }
  .mb-xl-n21,
  .my-xl-n21 {
    margin-bottom: -5.625rem !important;
  }
  .ml-xl-n21,
  .mx-xl-n21 {
    margin-left: -5.625rem !important;
  }
  .m-xl-n22 {
    margin: -5.9375rem !important;
  }
  .mt-xl-n22,
  .my-xl-n22 {
    margin-top: -5.9375rem !important;
  }
  .mr-xl-n22,
  .mx-xl-n22 {
    margin-right: -5.9375rem !important;
  }
  .mb-xl-n22,
  .my-xl-n22 {
    margin-bottom: -5.9375rem !important;
  }
  .ml-xl-n22,
  .mx-xl-n22 {
    margin-left: -5.9375rem !important;
  }
  .m-xl-n23 {
    margin: -6.25rem !important;
  }
  .mt-xl-n23,
  .my-xl-n23 {
    margin-top: -6.25rem !important;
  }
  .mr-xl-n23,
  .mx-xl-n23 {
    margin-right: -6.25rem !important;
  }
  .mb-xl-n23,
  .my-xl-n23 {
    margin-bottom: -6.25rem !important;
  }
  .ml-xl-n23,
  .mx-xl-n23 {
    margin-left: -6.25rem !important;
  }
  .m-xl-n24 {
    margin: -6.875rem !important;
  }
  .mt-xl-n24,
  .my-xl-n24 {
    margin-top: -6.875rem !important;
  }
  .mr-xl-n24,
  .mx-xl-n24 {
    margin-right: -6.875rem !important;
  }
  .mb-xl-n24,
  .my-xl-n24 {
    margin-bottom: -6.875rem !important;
  }
  .ml-xl-n24,
  .mx-xl-n24 {
    margin-left: -6.875rem !important;
  }
  .m-xl-n25 {
    margin: -7.5rem !important;
  }
  .mt-xl-n25,
  .my-xl-n25 {
    margin-top: -7.5rem !important;
  }
  .mr-xl-n25,
  .mx-xl-n25 {
    margin-right: -7.5rem !important;
  }
  .mb-xl-n25,
  .my-xl-n25 {
    margin-bottom: -7.5rem !important;
  }
  .ml-xl-n25,
  .mx-xl-n25 {
    margin-left: -7.5rem !important;
  }
  .m-xl-n26 {
    margin: -8.125rem !important;
  }
  .mt-xl-n26,
  .my-xl-n26 {
    margin-top: -8.125rem !important;
  }
  .mr-xl-n26,
  .mx-xl-n26 {
    margin-right: -8.125rem !important;
  }
  .mb-xl-n26,
  .my-xl-n26 {
    margin-bottom: -8.125rem !important;
  }
  .ml-xl-n26,
  .mx-xl-n26 {
    margin-left: -8.125rem !important;
  }
  .m-xl-n27 {
    margin: -8.4375rem !important;
  }
  .mt-xl-n27,
  .my-xl-n27 {
    margin-top: -8.4375rem !important;
  }
  .mr-xl-n27,
  .mx-xl-n27 {
    margin-right: -8.4375rem !important;
  }
  .mb-xl-n27,
  .my-xl-n27 {
    margin-bottom: -8.4375rem !important;
  }
  .ml-xl-n27,
  .mx-xl-n27 {
    margin-left: -8.4375rem !important;
  }
  .m-xl-n28 {
    margin: -9.0625rem !important;
  }
  .mt-xl-n28,
  .my-xl-n28 {
    margin-top: -9.0625rem !important;
  }
  .mr-xl-n28,
  .mx-xl-n28 {
    margin-right: -9.0625rem !important;
  }
  .mb-xl-n28,
  .my-xl-n28 {
    margin-bottom: -9.0625rem !important;
  }
  .ml-xl-n28,
  .mx-xl-n28 {
    margin-left: -9.0625rem !important;
  }
  .m-xl-n29 {
    margin: -9.375rem !important;
  }
  .mt-xl-n29,
  .my-xl-n29 {
    margin-top: -9.375rem !important;
  }
  .mr-xl-n29,
  .mx-xl-n29 {
    margin-right: -9.375rem !important;
  }
  .mb-xl-n29,
  .my-xl-n29 {
    margin-bottom: -9.375rem !important;
  }
  .ml-xl-n29,
  .mx-xl-n29 {
    margin-left: -9.375rem !important;
  }
  .m-xl-n30 {
    margin: -9.6875rem !important;
  }
  .mt-xl-n30,
  .my-xl-n30 {
    margin-top: -9.6875rem !important;
  }
  .mr-xl-n30,
  .mx-xl-n30 {
    margin-right: -9.6875rem !important;
  }
  .mb-xl-n30,
  .my-xl-n30 {
    margin-bottom: -9.6875rem !important;
  }
  .ml-xl-n30,
  .mx-xl-n30 {
    margin-left: -9.6875rem !important;
  }
  .m-xl-n31 {
    margin: -10.625rem !important;
  }
  .mt-xl-n31,
  .my-xl-n31 {
    margin-top: -10.625rem !important;
  }
  .mr-xl-n31,
  .mx-xl-n31 {
    margin-right: -10.625rem !important;
  }
  .mb-xl-n31,
  .my-xl-n31 {
    margin-bottom: -10.625rem !important;
  }
  .ml-xl-n31,
  .mx-xl-n31 {
    margin-left: -10.625rem !important;
  }
  .m-xl-n32 {
    margin: -11.25rem !important;
  }
  .mt-xl-n32,
  .my-xl-n32 {
    margin-top: -11.25rem !important;
  }
  .mr-xl-n32,
  .mx-xl-n32 {
    margin-right: -11.25rem !important;
  }
  .mb-xl-n32,
  .my-xl-n32 {
    margin-bottom: -11.25rem !important;
  }
  .ml-xl-n32,
  .mx-xl-n32 {
    margin-left: -11.25rem !important;
  }
  .m-xl-n33 {
    margin: -12.5rem !important;
  }
  .mt-xl-n33,
  .my-xl-n33 {
    margin-top: -12.5rem !important;
  }
  .mr-xl-n33,
  .mx-xl-n33 {
    margin-right: -12.5rem !important;
  }
  .mb-xl-n33,
  .my-xl-n33 {
    margin-bottom: -12.5rem !important;
  }
  .ml-xl-n33,
  .mx-xl-n33 {
    margin-left: -12.5rem !important;
  }
  .m-xl-n34 {
    margin: -14.0625rem !important;
  }
  .mt-xl-n34,
  .my-xl-n34 {
    margin-top: -14.0625rem !important;
  }
  .mr-xl-n34,
  .mx-xl-n34 {
    margin-right: -14.0625rem !important;
  }
  .mb-xl-n34,
  .my-xl-n34 {
    margin-bottom: -14.0625rem !important;
  }
  .ml-xl-n34,
  .mx-xl-n34 {
    margin-left: -14.0625rem !important;
  }
  .m-xl-n35 {
    margin: -15.625rem !important;
  }
  .mt-xl-n35,
  .my-xl-n35 {
    margin-top: -15.625rem !important;
  }
  .mr-xl-n35,
  .mx-xl-n35 {
    margin-right: -15.625rem !important;
  }
  .mb-xl-n35,
  .my-xl-n35 {
    margin-bottom: -15.625rem !important;
  }
  .ml-xl-n35,
  .mx-xl-n35 {
    margin-left: -15.625rem !important;
  }
  .m-xl-auto {
    margin: auto !important;
  }
  .mt-xl-auto,
  .my-xl-auto {
    margin-top: auto !important;
  }
  .mr-xl-auto,
  .mx-xl-auto {
    margin-right: auto !important;
  }
  .mb-xl-auto,
  .my-xl-auto {
    margin-bottom: auto !important;
  }
  .ml-xl-auto,
  .mx-xl-auto {
    margin-left: auto !important;
  }
}

.stretched-link::after {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1;
  pointer-events: auto;
  content: '';
  background-color: rgba(0, 0, 0, 0);
}

.text-monospace {
  font-family: SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono',
    'Courier New', monospace !important;
}

.text-justify {
  text-align: justify !important;
}

.text-wrap {
  white-space: normal !important;
}

.text-nowrap {
  white-space: nowrap !important;
}

.text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.text-left {
  text-align: left !important;
}

.text-right {
  text-align: right !important;
}

.text-center {
  text-align: center !important;
}

@media (min-width: 480px) {
  .text-xs-left {
    text-align: left !important;
  }
  .text-xs-right {
    text-align: right !important;
  }
  .text-xs-center {
    text-align: center !important;
  }
}

@media (min-width: 576px) {
  .text-sm-left {
    text-align: left !important;
  }
  .text-sm-right {
    text-align: right !important;
  }
  .text-sm-center {
    text-align: center !important;
  }
}

@media (min-width: 768px) {
  .text-md-left {
    text-align: left !important;
  }
  .text-md-right {
    text-align: right !important;
  }
  .text-md-center {
    text-align: center !important;
  }
}

@media (min-width: 992px) {
  .text-lg-left {
    text-align: left !important;
  }
  .text-lg-right {
    text-align: right !important;
  }
  .text-lg-center {
    text-align: center !important;
  }
}

@media (min-width: 1200px) {
  .text-xl-left {
    text-align: left !important;
  }
  .text-xl-right {
    text-align: right !important;
  }
  .text-xl-center {
    text-align: center !important;
  }
}

.text-lowercase {
  text-transform: lowercase !important;
}

.text-uppercase {
  text-transform: uppercase !important;
}

.text-capitalize {
  text-transform: capitalize !important;
}

.font-weight-light {
  font-weight: 300 !important;
}

.font-weight-lighter {
  font-weight: lighter !important;
}

.font-weight-normal {
  font-weight: 400 !important;
}

.font-weight-bold {
  font-weight: 700 !important;
}

.font-weight-bolder {
  font-weight: bolder !important;
}

.font-italic {
  font-style: italic !important;
}

.text-white {
  color: #fff !important;
}

.text-primary {
  color: #473bf0 !important;
}

a.text-primary:hover,
a.text-primary:focus {
  color: #1c10cf !important;
}

.text-secondary {
  color: #68d585 !important;
}

a.text-secondary:hover,
a.text-secondary:focus {
  color: #34bc58 !important;
}

.text-success {
  color: #68d585 !important;
}

a.text-success:hover,
a.text-success:focus {
  color: #34bc58 !important;
}

.text-info {
  color: #17a2b8 !important;
}

a.text-info:hover,
a.text-info:focus {
  color: #0f6674 !important;
}

.text-warning {
  color: #f7e36d !important;
}

a.text-warning:hover,
a.text-warning:focus {
  color: #f3d524 !important;
}

.text-danger {
  color: #f64b4b !important;
}

a.text-danger:hover,
a.text-danger:focus {
  color: #e90c0c !important;
}

.text-light {
  color: #f8f9fa !important;
}

a.text-light:hover,
a.text-light:focus {
  color: #cbd3da !important;
}

.text-dark {
  color: #343a40 !important;
}

a.text-dark:hover,
a.text-dark:focus {
  color: #121416 !important;
}

.text-red {
  color: #f64b4b !important;
}

a.text-red:hover,
a.text-red:focus {
  color: #e90c0c !important;
}

.text-green {
  color: #68d585 !important;
}

a.text-green:hover,
a.text-green:focus {
  color: #34bc58 !important;
}

.text-green-shamrock {
  color: #2bd67b !important;
}

a.text-green-shamrock:hover,
a.text-green-shamrock:focus {
  color: #1d9756 !important;
}

.text-blue {
  color: #473bf0 !important;
}

a.text-blue:hover,
a.text-blue:focus {
  color: #1c10cf !important;
}

.text-sky-blue {
  color: #1082e9 !important;
}

a.text-sky-blue:hover,
a.text-sky-blue:focus {
  color: #0b5aa1 !important;
}

.text-yellow {
  color: #f7e36d !important;
}

a.text-yellow:hover,
a.text-yellow:focus {
  color: #f3d524 !important;
}

.text-yellow-orange {
  color: #fcad38 !important;
}

a.text-yellow-orange:hover,
a.text-yellow-orange:focus {
  color: #e48a03 !important;
}

.text-blackish-blue {
  color: #13151c !important;
}

a.text-blackish-blue:hover,
a.text-blackish-blue:focus {
  color: black !important;
}

.text-black {
  color: #000 !important;
}

a.text-black:hover,
a.text-black:focus {
  color: black !important;
}

.text-mirage {
  color: #131829 !important;
}

a.text-mirage:hover,
a.text-mirage:focus {
  color: black !important;
}

.text-mirage-2 {
  color: #161c2d !important;
}

a.text-mirage-2:hover,
a.text-mirage-2:focus {
  color: black !important;
}

.text-white {
  color: #fff !important;
}

a.text-white:hover,
a.text-white:focus {
  color: #d9d9d9 !important;
}

.text-smoke {
  color: #f8f8f8 !important;
}

a.text-smoke:hover,
a.text-smoke:focus {
  color: #d2d2d2 !important;
}

.text-storm {
  color: #7d818d !important;
}

a.text-storm:hover,
a.text-storm:focus {
  color: #595c65 !important;
}

.text-ghost {
  color: #fdfdff !important;
}

a.text-ghost:hover,
a.text-ghost:focus {
  color: #b1b1ff !important;
}

.text-gray-1 {
  color: #fcfdfe !important;
}

a.text-gray-1:hover,
a.text-gray-1:focus {
  color: #c3d7eb !important;
}

.text-gray-2 {
  color: #f4f7fa !important;
}

a.text-gray-2:hover,
a.text-gray-2:focus {
  color: #bfd1e2 !important;
}

.text-gray-3 {
  color: #e7e9ed !important;
}

a.text-gray-3:hover,
a.text-gray-3:focus {
  color: #bbc1cc !important;
}

.text-gray-310 {
  color: #d5d7dd !important;
}

a.text-gray-310:hover,
a.text-gray-310:focus {
  color: #abafbb !important;
}

.text-gray-opacity {
  color: rgba(231, 233, 237, 0.13) !important;
}

a.text-gray-opacity:hover,
a.text-gray-opacity:focus {
  color: rgba(187, 193, 204, 0.13) !important;
}

.text-blackish-blue-opacity {
  color: rgba(22, 28, 45, 0.7) !important;
}

a.text-blackish-blue-opacity:hover,
a.text-blackish-blue-opacity:focus {
  color: rgba(0, 0, 0, 0.7) !important;
}

.text-narvik {
  color: #edf9f2 !important;
}

a.text-narvik:hover,
a.text-narvik:focus {
  color: #b4e6c9 !important;
}

.text-body {
  color: var(--color-texts-opacity) !important;
}

.text-muted {
  color: #6c757d !important;
}

.text-black-50 {
  color: rgba(0, 0, 0, 0.5) !important;
}

.text-white-50 {
  color: rgba(255, 255, 255, 0.5) !important;
}

.text-hide {
  font: 0/0 a;
  color: transparent;
  text-shadow: none;
  background-color: transparent;
  border: 0;
}

.text-decoration-none {
  text-decoration: none !important;
}

.text-break {
  word-break: break-word !important;
  word-wrap: break-word !important;
}

.text-reset {
  color: inherit !important;
}

.visible {
  visibility: visible !important;
}

.invisible {
  visibility: hidden !important;
}

@media print {
  *,
  *::before,
  *::after {
    text-shadow: none !important;
    box-shadow: none !important;
  }
  a:not(.btn) {
    text-decoration: underline;
  }
  abbr[title]::after {
    content: ' (' attr(title) ')';
  }
  pre {
    white-space: pre-wrap !important;
  }
  pre,
  blockquote {
    border: 1px solid #adb5bd;
    page-break-inside: avoid;
  }
  thead {
    display: table-header-group;
  }
  tr,
  img {
    page-break-inside: avoid;
  }
  p,
  h2,
  h3 {
    orphans: 3;
    widows: 3;
  }
  h2,
  h3 {
    page-break-after: avoid;
  }
  @page {
    size: a3;
  }
  body {
    min-width: 992px !important;
  }
  .container {
    min-width: 992px !important;
  }
  .navbar {
    display: none;
  }
  .badge {
    border: 1px solid #000;
  }
  .table {
    border-collapse: collapse !important;
  }
  .table td,
  .table th {
    background-color: #fff !important;
  }
  .table-bordered th,
  .table-bordered td {
    border: 1px solid #dee2e6 !important;
  }
  .table-dark {
    color: inherit;
  }
  .table-dark th,
  .table-dark td,
  .table-dark thead th,
  .table-dark tbody + tbody {
    border-color: var(--border-color);
  }
  .table .thead-dark th {
    color: inherit;
    border-color: var(--border-color);
  }
}

/*# sourceMappingURL=maps/bootstrap.css.map */
