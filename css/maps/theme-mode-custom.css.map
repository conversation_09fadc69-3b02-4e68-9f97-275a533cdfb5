{"version": 3, "sources": ["controls/_theme-media-query.scss", "theme-mode-custom.scss", "controls/_theme-color-control.scss", "theme-mode-custom.css"], "names": [], "mappings": "AAAA,sBAAA;ACGA;EAEI,cCiB2B;ACnB/B;;AFOA;EAGI,4CAA2C;AEN/C;;AFGA;EAMI,4EAAmF;AELvF;;AFDA;EAUI,6BAA4B;AELhC;;AFLA;EAaI,qDAAkD;AEJtD;;AFTA;EAkBI,yBAAyB;AEL7B;;AFbA;EAoBM,mBCRyB;ACK/B;;AFjBA;EAwBO,iBAAiB;AEHxB", "file": "../theme-mode-custom.css", "sourcesContent": ["/*=== Media Query ===*/\r\n// Screen Width\r\n$screen-xxs: 320px;\r\n$screen-xs: 480px;\r\n$screen-sm: 576px;\r\n$screen-md: 768px;\r\n$screen-lg: 992px;\r\n$screen-xl: 1200px;\r\n$screen-xxl: 1366px;\r\n$screen-xxxl: 1600px;\r\n\r\n@mixin brk-point($mw) {\r\n    @media(min-width: $mw) {\r\n        @content;\r\n    }\r\n}\r\n@mixin mobile-xs {\r\n    @media(min-width: $screen-xxs) {\r\n        @content;\r\n    }\r\n}\r\n\r\n@mixin mobile {\r\n    @media(min-width: $screen-xs) {\r\n        @content;\r\n    }\r\n}\r\n\r\n@mixin mobile-lg {\r\n    @media(min-width: $screen-sm) {\r\n        @content;\r\n    }\r\n}\r\n@mixin mobile-lg-only {\r\n    @media(min-width: $screen-sm) and (max-width:$screen-md) {\r\n        @content;\r\n    }\r\n}\r\n\r\n@mixin tablet {\r\n    @media(min-width: $screen-md) {\r\n        @content;\r\n    }\r\n}\r\n\r\n@mixin desktops {\r\n    @media(min-width: $screen-lg) {\r\n        @content;\r\n    }\r\n}\r\n\r\n@mixin desktops-only {\r\n    @media(min-width: $screen-lg) and (max-width:$screen-xl) {\r\n        @content;\r\n    }\r\n}\r\n@mixin till-desktop {\r\n    @media(min-width: $screen-xxs) and (max-width:$screen-lg) {\r\n        @content;\r\n    }\r\n}\r\n\r\n@mixin large-desktops {\r\n    @media(min-width: $screen-xl) {\r\n        @content;\r\n    }\r\n}\r\n\r\n@mixin large-desktops-mid {\r\n    @media(min-width: $screen-xxl) {\r\n        @content;\r\n    }\r\n}\r\n\r\n@mixin extra-large-desktops {\r\n    @media(min-width: $screen-xxxl) {\r\n        @content;\r\n    }\r\n}\r\n\r\n@mixin section-padding{\r\n    padding: 50px 0;\r\n    @include tablet {\r\n        padding: 120px 0;\r\n\r\n    }\r\n}\r\n", "@import \"controls/theme-variables\";\r\n@import \"controls/theme-media-query\";\r\n@import \"controls/theme-mixins\";\r\nbody[data-theme='light']{\r\n  .text-primary--light-only{\r\n    color: $primary;\r\n  }\r\n  \r\n\r\n}\r\nbody[data-theme='dark']{\r\n\r\n  .border-gray-3{\r\n    border-color: var(--border-color)!important;\r\n  }\r\n  .bg-gradient-2{\r\n    background-image: linear-gradient(270deg, rgba(0, 0, 0, 0) 0%, $blackish-blue 100%);\r\n  }\r\n\r\n  .border-blackish-blue{\r\n    border-color: #fff!important;\r\n  }\r\n  .gr-bg-blackish-blue-opacity-1{\r\n    background-color: rgba(255, 255, 255,.8)!important;\r\n  }\r\n\r\n\r\n  .btn-toggle{\r\n    background-color:#efefef ;\r\n    span{\r\n      background: $primary;\r\n    }\r\n    &.active {\r\n      span{\r\n       background: #ffff;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n", "\r\n$white:    #fff;\r\n$ghost:    #fdfdff;\r\n$alabaster: #fbfbfb;\r\n$smoke:    #f8f8f8;\r\n$gray-110: #fcfdfe;\r\n$gray-120: #f4f7fa;\r\n$gray-210: #e7e9ed;\r\n$gray-opacity:   rgba(231, 233, 237, 0.13);\r\n$gray-310: #d5d7dd;\r\n$storm:    #7d818d;\r\n$gray-610: #666666;\r\n$gray-710: #413e65;\r\n$gray-910: #1F1F1F;\r\n$gray-920: #1e1e20;\r\n$gray-930: #19191b;\r\n$gray-940: #161c2d;\r\n$black:    #000 ;\r\n\r\n\r\n\r\n$red:                   #f64b4b;\r\n$blue:                  #473bf0;\r\n$sky-blue:              #1082e9;\r\n$green:                 #68d585;\r\n$green-shamrock:        #2bd67b;\r\n$blackish-blue:         #13151C;\r\n$blackish-blue-opacity: rgba(#161c2d,.7);\r\n$mirage:                #131829;\r\n$mirage-2:              #161c2d;\r\n$yellow: #f7e36d;\r\n$yellow-orange: #FCAD38;\r\n$narvik: #EDF9F2;\r\n\r\n$primary:       $blue;\r\n$secondary:     $green;\r\n\r\n", "/*=== Media Query ===*/\nbody[data-theme='light'] .text-primary--light-only {\n  color: #473bf0;\n}\n\nbody[data-theme='dark'] .border-gray-3 {\n  border-color: var(--border-color) !important;\n}\n\nbody[data-theme='dark'] .bg-gradient-2 {\n  background-image: linear-gradient(270deg, rgba(0, 0, 0, 0) 0%, #13151C 100%);\n}\n\nbody[data-theme='dark'] .border-blackish-blue {\n  border-color: #fff !important;\n}\n\nbody[data-theme='dark'] .gr-bg-blackish-blue-opacity-1 {\n  background-color: rgba(255, 255, 255, 0.8) !important;\n}\n\nbody[data-theme='dark'] .btn-toggle {\n  background-color: #efefef;\n}\n\nbody[data-theme='dark'] .btn-toggle span {\n  background: #473bf0;\n}\n\nbody[data-theme='dark'] .btn-toggle.active span {\n  background: #ffff;\n}\n"]}