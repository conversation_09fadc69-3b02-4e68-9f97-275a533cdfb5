{"version": 3, "sources": ["theme-mode-control/_theme-mode-activation.scss", "theme-mode-control/_theme-mode-mixins.scss", "elements-adjustment.css", "controls/_theme-media-query.scss", "elements-adjustment.scss", "controls/_theme-font-control.scss", "controls/_theme-color-control.scss"], "names": [], "mappings": "AAYA;ECVE,cAAK;EACL,6BAAO;EACP,cAAO;EACP,cAAO;EACP,cAAO;EACP,cAAO;EACP,cAAO;EACP,cAAO;EACP,sBAAiB;EACjB,iDAAyB;EACzB,8CAAsB;EACtB,mBAAc;EACd,kCAAa;EACb,oCAAe;EACf,sCAAiB;EACjB,qBAAa;ACAf;;AFDA;EACE,aAAa;AEIf;;AFFA;EACE,aAAa;AEKf;;AFAA;ECLE,cAAK;EACL,cAAO;EACP,cAAO;EACP,cAAO;EACP,cAAO;EACP,WAAO;EACP,cAAO;EACP,cAAO;EACP,yBAAiB;EACjB,6CAAyB;EACzB,0CAAsB;EACtB,sBAAc;EACd,gCAAa;EACb,sBAAe;EACf,wBAAiB;ACSnB;;AFXA;ECZE,cAAK;EACL,cAAO;EACP,cAAO;EACP,cAAO;EACP,cAAO;EACP,WAAO;EACP,cAAO;EACP,cAAO;EACP,yBAAiB;EACjB,6CAAyB;EACzB,0CAAsB;EACtB,sBAAc;EACd,gCAAa;EACb,sBAAe;EACf,wBAAiB;AC2BnB;;AFpBA;EACE,cAAc;AEuBhB;;AFpBA;EAEI,8BAA6B;AEsBjC;;AFxBA;EAKI,cAAc;AEuBlB;;AF5BA;EAQI,wBAAuB;AEwB3B;;AFhCA;EAWI,yBAAwB;AEyB5B;;AFpCA;EAeM,aAAa;AEyBnB;;AFxCA;EAkBM,cAAc;AE0BpB;;AF5CA;EAuBI,aAAa;AEyBjB;;AFrBA;EAGI,cAAc;AEsBlB;;AFzBA;EAMI,yBAAwB;AEuB5B;;AF7BA;EASI,wBAAuB;AEwB3B;;AFjCA;EAaM,wBAAuB;AEwB7B;;AFrCA;EAgBM,yBAAwB;AEyB9B;;AFzCA;EAmBM,cAAc;AE0BpB;;AF7CA;EAsBM,aAAa;AE2BnB;;AFjDA;EA2BI,aAAa;AE0BjB;;AC7HA,sBAAA;ACUA;EACE,iBACF;AFsHA;;AEpHA;EAEI,iBAAiB;EACjB,gBAAe;EACf,eAAe;EACf,oBCYkD;EDXlD,gBAAgB;EAChB,yBAAyB;EACzB,cEI2B;EFH3B,mBAAmB;AFsHvB;;AE/HA;EAWM,kCEHyB;EFIzB,cEJyB;AJ4H/B;;AEnHA;EACE,iBAAiB;EACjB,gBAAgB;AFsHlB;;AExHA;EAII,SAAS;EACT,WAAW;AFwHf;;AE7HA;EAOM,WAAW;EACX,eAAe;EACf,eAAe;EACf,sBAAsB;EACtB,eAAe;EACf,YAAY;EACZ,yBAAyB;EACzB,gBAAgB;EAChB,kBAAkB;AF0HxB;;AErHA;EAEI,YAAY;AFuHhB;;AEzHA;EAKI,WAAW;AFwHf", "file": "../elements-adjustment.css", "sourcesContent": ["\r\n\r\n\r\n@import 'theme-mode-mixins';\r\n\r\n\r\n\r\n// Mode activation\r\n$theme-mode-active: 'light';\r\n\r\n\r\n\r\nbody[data-theme='dark'],.dark-bg,.dark-mode-texts{\r\n  @include dark-theme-mode;\r\n  \r\n}\r\n.light-version-logo,.light-shape{\r\n  display: none;\r\n}\r\n.dark-version-logo,.dark-shape{\r\n  display: none;\r\n}\r\n\r\n\r\n\r\nbody[data-theme='light'],.light-bg,.light-mode-texts {\r\n @include light-theme-mode;\r\n}\r\n\r\n\r\n\r\n// Setting The Default Theme Mode\r\nbody{\r\n  @if $theme-mode-active == 'dark' {\r\n    @include dark-theme-mode;\r\n  } @else {\r\n    @include light-theme-mode;\r\n  }\r\n \r\n}\r\n\r\n.default-logo,.default-shape{\r\n  display: block;\r\n}\r\n\r\nbody[data-theme='dark'],.dark-bg,.dark-mode-texts{\r\n  [data-force-dark-mode]{\r\n    background: #171a23!important;\r\n  }\r\n  .dark-version-logo{\r\n    display: block;\r\n  }\r\n  .light-shape{\r\n    display: none!important;\r\n  }\r\n  .dark-shape{\r\n    display: block!important;\r\n  }\r\n  .light-mode-texts{\r\n    .dark-version-logo{\r\n      display: none;\r\n    }\r\n    .light-version-logo{\r\n      display: block;\r\n    }\r\n  }\r\n\r\n  .default-logo,.default-shape{\r\n    display: none;\r\n  }\r\n}\r\n\r\nbody[data-theme='light'],.light-bg,.light-mode-texts {\r\n  \r\n  .light-version-logo{\r\n    display: block;\r\n  }\r\n  .light-shape{\r\n    display: block!important;\r\n  }\r\n  .dark-shape{\r\n    display: none!important;\r\n  }\r\n  .dark-mode-texts{\r\n    .light-shape{\r\n      display: none!important;\r\n    }\r\n    .dark-shape{\r\n      display: block!important;\r\n    }\r\n    .dark-version-logo{\r\n      display: block;\r\n    }\r\n    .light-version-logo{\r\n      display: none;\r\n    }\r\n  }\r\n  \r\n  .default-logo,.default-shape{\r\n    display: none;\r\n  }\r\n}\r\n", "\r\n@mixin dark-theme-mode{\r\n  --bg:  #0E1019;\r\n  --bg-2:rgba(255,255,255,0.01);\r\n  --bg-3:#0E1019;\r\n  --bg-4:#0E1019;\r\n  --bg-5:#0E1019;\r\n  --bg-6:#0E1019;\r\n  --bg-7:#13151C;\r\n  --bg-8:#0E1019;\r\n  --color-headings: #fff;\r\n  --color-headings-opacity:rgba(255, 255, 255, 0.4);\r\n  --color-texts-opacity: rgba(255, 255, 255,0.7);\r\n  --color-texts: #fff;\r\n  --btn-border: rgba(255,255,255,.3);\r\n  --border-color:rgba(255,255,255,.08);\r\n  --border-color-2:rgba(255,255,255,.08);\r\n  --force-dark: #171a23;\r\n}\r\n@mixin light-theme-mode{\r\n  --bg:  #fcfdfe;\r\n  --bg-2:#f4f7fa;\r\n  --bg-3:#f8f8f8;\r\n  --bg-4:#fdfdff;\r\n  --bg-5:#ecf2f7;\r\n  --bg-6:#fff;\r\n  --bg-7:#EDF9F2;\r\n  --bg-8:#fbfbfb;\r\n  --color-headings: #161c2d;\r\n  --color-headings-opacity: rgba(22,28,45, 0.4);\r\n  --color-texts-opacity: rgba(22,28,45, 0.7);\r\n  --color-texts: #161c2d;\r\n  --btn-border: rgba(3, 3, 3, 0.3);\r\n  --border-color:#e7e9ed;\r\n  --border-color-2:#eae9f2;\r\n\r\n}\r\n\r\n@mixin dark-mode {\r\n  [data-theme='dark'] & ,.dark-mode-texts & {\r\n      @content;\r\n  }\r\n}\r\n@mixin light-mode {\r\n  [data-theme='dark'] & ,.dark-mode-texts & {\r\n      @content;\r\n  }\r\n}\r\n\r\n// #FCFDFE, #F4F7FA, #F8F8F8, #ECF2F7, #FDFDFF, ", "body[data-theme='dark'], .dark-bg, .dark-mode-texts {\n  --bg:  #0E1019;\n  --bg-2:rgba(255,255,255,0.01);\n  --bg-3:#0E1019;\n  --bg-4:#0E1019;\n  --bg-5:#0E1019;\n  --bg-6:#0E1019;\n  --bg-7:#13151C;\n  --bg-8:#0E1019;\n  --color-headings: #fff;\n  --color-headings-opacity:rgba(255, 255, 255, 0.4);\n  --color-texts-opacity: rgba(255, 255, 255,0.7);\n  --color-texts: #fff;\n  --btn-border: rgba(255,255,255,.3);\n  --border-color:rgba(255,255,255,.08);\n  --border-color-2:rgba(255,255,255,.08);\n  --force-dark: #171a23;\n}\n\n.light-version-logo, .light-shape {\n  display: none;\n}\n\n.dark-version-logo, .dark-shape {\n  display: none;\n}\n\nbody[data-theme='light'], .light-bg, .light-mode-texts {\n  --bg:  #fcfdfe;\n  --bg-2:#f4f7fa;\n  --bg-3:#f8f8f8;\n  --bg-4:#fdfdff;\n  --bg-5:#ecf2f7;\n  --bg-6:#fff;\n  --bg-7:#EDF9F2;\n  --bg-8:#fbfbfb;\n  --color-headings: #161c2d;\n  --color-headings-opacity: rgba(22,28,45, 0.4);\n  --color-texts-opacity: rgba(22,28,45, 0.7);\n  --color-texts: #161c2d;\n  --btn-border: rgba(3, 3, 3, 0.3);\n  --border-color:#e7e9ed;\n  --border-color-2:#eae9f2;\n}\n\nbody {\n  --bg:  #fcfdfe;\n  --bg-2:#f4f7fa;\n  --bg-3:#f8f8f8;\n  --bg-4:#fdfdff;\n  --bg-5:#ecf2f7;\n  --bg-6:#fff;\n  --bg-7:#EDF9F2;\n  --bg-8:#fbfbfb;\n  --color-headings: #161c2d;\n  --color-headings-opacity: rgba(22,28,45, 0.4);\n  --color-texts-opacity: rgba(22,28,45, 0.7);\n  --color-texts: #161c2d;\n  --btn-border: rgba(3, 3, 3, 0.3);\n  --border-color:#e7e9ed;\n  --border-color-2:#eae9f2;\n}\n\n.default-logo, .default-shape {\n  display: block;\n}\n\nbody[data-theme='dark'] [data-force-dark-mode], .dark-bg [data-force-dark-mode], .dark-mode-texts [data-force-dark-mode] {\n  background: #171a23 !important;\n}\n\nbody[data-theme='dark'] .dark-version-logo, .dark-bg .dark-version-logo, .dark-mode-texts .dark-version-logo {\n  display: block;\n}\n\nbody[data-theme='dark'] .light-shape, .dark-bg .light-shape, .dark-mode-texts .light-shape {\n  display: none !important;\n}\n\nbody[data-theme='dark'] .dark-shape, .dark-bg .dark-shape, .dark-mode-texts .dark-shape {\n  display: block !important;\n}\n\nbody[data-theme='dark'] .light-mode-texts .dark-version-logo, .dark-bg .light-mode-texts .dark-version-logo, .dark-mode-texts .light-mode-texts .dark-version-logo {\n  display: none;\n}\n\nbody[data-theme='dark'] .light-mode-texts .light-version-logo, .dark-bg .light-mode-texts .light-version-logo, .dark-mode-texts .light-mode-texts .light-version-logo {\n  display: block;\n}\n\nbody[data-theme='dark'] .default-logo, body[data-theme='dark'] .default-shape, .dark-bg .default-logo, .dark-bg .default-shape, .dark-mode-texts .default-logo, .dark-mode-texts .default-shape {\n  display: none;\n}\n\nbody[data-theme='light'] .light-version-logo, .light-bg .light-version-logo, .light-mode-texts .light-version-logo {\n  display: block;\n}\n\nbody[data-theme='light'] .light-shape, .light-bg .light-shape, .light-mode-texts .light-shape {\n  display: block !important;\n}\n\nbody[data-theme='light'] .dark-shape, .light-bg .dark-shape, .light-mode-texts .dark-shape {\n  display: none !important;\n}\n\nbody[data-theme='light'] .dark-mode-texts .light-shape, .light-bg .dark-mode-texts .light-shape, .light-mode-texts .dark-mode-texts .light-shape {\n  display: none !important;\n}\n\nbody[data-theme='light'] .dark-mode-texts .dark-shape, .light-bg .dark-mode-texts .dark-shape, .light-mode-texts .dark-mode-texts .dark-shape {\n  display: block !important;\n}\n\nbody[data-theme='light'] .dark-mode-texts .dark-version-logo, .light-bg .dark-mode-texts .dark-version-logo, .light-mode-texts .dark-mode-texts .dark-version-logo {\n  display: block;\n}\n\nbody[data-theme='light'] .dark-mode-texts .light-version-logo, .light-bg .dark-mode-texts .light-version-logo, .light-mode-texts .dark-mode-texts .light-version-logo {\n  display: none;\n}\n\nbody[data-theme='light'] .default-logo, body[data-theme='light'] .default-shape, .light-bg .default-logo, .light-bg .default-shape, .light-mode-texts .default-logo, .light-mode-texts .default-shape {\n  display: none;\n}\n\n/*=== Media Query ===*/\n.container.container-1600 {\n  max-width: 1600px;\n}\n\n.element-preview-tab .nav-link {\n  padding: 7px 16px;\n  margin-left: 5px;\n  transition: .4s;\n  font-size: 0.8125rem;\n  font-weight: 700;\n  text-transform: uppercase;\n  color: #13151C;\n  border-radius: 16px;\n}\n\n.element-preview-tab .nav-link.active {\n  background: rgba(71, 59, 240, 0.2);\n  color: #473bf0;\n}\n\n.code-single-block {\n  max-height: 500px;\n  overflow-y: auto;\n}\n\n.code-single-block div.code-toolbar > .toolbar {\n  top: 15px;\n  right: 15px;\n}\n\n.code-single-block div.code-toolbar > .toolbar button {\n  color: #fff;\n  font-size: 13px;\n  padding: 0 13px;\n  background: var(--red);\n  min-width: 67px;\n  height: 34px;\n  text-transform: uppercase;\n  font-weight: 700;\n  border-radius: 5px;\n}\n\n.element-block .site-header--absolute {\n  z-index: 998;\n}\n\n.element-block .nav-item.dropdown {\n  z-index: 98;\n}\n", "/*=== Media Query ===*/\r\n// Screen Width\r\n$screen-xxs: 320px;\r\n$screen-xs: 480px;\r\n$screen-sm: 576px;\r\n$screen-md: 768px;\r\n$screen-lg: 992px;\r\n$screen-xl: 1200px;\r\n$screen-xxl: 1366px;\r\n$screen-xxxl: 1600px;\r\n\r\n@mixin brk-point($mw) {\r\n    @media(min-width: $mw) {\r\n        @content;\r\n    }\r\n}\r\n@mixin mobile-xs {\r\n    @media(min-width: $screen-xxs) {\r\n        @content;\r\n    }\r\n}\r\n\r\n@mixin mobile {\r\n    @media(min-width: $screen-xs) {\r\n        @content;\r\n    }\r\n}\r\n\r\n@mixin mobile-lg {\r\n    @media(min-width: $screen-sm) {\r\n        @content;\r\n    }\r\n}\r\n@mixin mobile-lg-only {\r\n    @media(min-width: $screen-sm) and (max-width:$screen-md) {\r\n        @content;\r\n    }\r\n}\r\n\r\n@mixin tablet {\r\n    @media(min-width: $screen-md) {\r\n        @content;\r\n    }\r\n}\r\n\r\n@mixin desktops {\r\n    @media(min-width: $screen-lg) {\r\n        @content;\r\n    }\r\n}\r\n\r\n@mixin desktops-only {\r\n    @media(min-width: $screen-lg) and (max-width:$screen-xl) {\r\n        @content;\r\n    }\r\n}\r\n@mixin till-desktop {\r\n    @media(min-width: $screen-xxs) and (max-width:$screen-lg) {\r\n        @content;\r\n    }\r\n}\r\n\r\n@mixin large-desktops {\r\n    @media(min-width: $screen-xl) {\r\n        @content;\r\n    }\r\n}\r\n\r\n@mixin large-desktops-mid {\r\n    @media(min-width: $screen-xxl) {\r\n        @content;\r\n    }\r\n}\r\n\r\n@mixin extra-large-desktops {\r\n    @media(min-width: $screen-xxxl) {\r\n        @content;\r\n    }\r\n}\r\n\r\n@mixin section-padding{\r\n    padding: 50px 0;\r\n    @include tablet {\r\n        padding: 120px 0;\r\n\r\n    }\r\n}\r\n", "@import \"theme-mode-control/theme-mode-activation\";\r\n@import \"controls/theme-functions\";\r\n@import \"controls/theme-variables\";\r\n@import \"controls/theme-media-query\";\r\n@import \"controls/theme-mixins\";\r\n//Bootstrap Required\r\n@import \"bootstrap/functions\";\r\n@import \"bootstrap/variables\";\r\n@import \"bootstrap/mixins\";\r\n\r\n.container.container-1600{\r\n  max-width:1600px\r\n}\r\n\r\n.element-preview-tab{\r\n  .nav-link {\r\n    padding: 7px 16px;\r\n    margin-left:5px;\r\n    transition: .4s;\r\n    font-size: $h8-font-size;//Approx 13 px\r\n    font-weight: 700;\r\n    text-transform: uppercase;\r\n    color: $blackish-blue;\r\n    border-radius: 16px;\r\n    &.active {\r\n      background: rgba($blue, 0.2);\r\n      color: $blue;\r\n    }\r\n  \r\n  }\r\n}\r\n.code-single-block{\r\n  max-height: 500px;\r\n  overflow-y: auto;\r\n  div.code-toolbar > .toolbar{\r\n    top: 15px;\r\n    right: 15px;\r\n    button{\r\n      color: #fff;\r\n      font-size: 13px;\r\n      padding: 0 13px;\r\n      background: var(--red);\r\n      min-width: 67px;\r\n      height: 34px;\r\n      text-transform: uppercase;\r\n      font-weight: 700;\r\n      border-radius: 5px;\r\n    }\r\n  } \r\n}\r\n\r\n.element-block{\r\n  .site-header--absolute{\r\n    z-index: 998;\r\n  }\r\n  .nav-item.dropdown{\r\n    z-index: 98;\r\n  }\r\n}", "//-------------------------\r\n//--- Theme Typography\r\n//-------------------------\r\n$enable-responsive-font-sizes: true;\r\n$font-family-base:            'Circular Std', sans-serif;\r\n\r\n$headings-font-weight:        700;\r\n// $headings-line-height:        1.5;\r\n$headings-color:            var(--color-headings);\r\n$text-color:                var(--color-texts-opacity);\r\n\r\n//- Display Sizes\r\n\r\n$display1-size:               6rem;\r\n$display2-size:               5.5rem;\r\n$display3-size:               5rem;\r\n$display4-size:               3.75rem;\r\n\r\n\r\n//- Font Sizes \r\n$font-size-base:              1rem; // Assumes the browser default, typically `16px`\r\n$font-size-lg:                $font-size-base * 1.25;\r\n$font-size-sm:                $font-size-base * .875;\r\n\r\n$h1-font-size:                $font-size-base * 3; //About 48px\r\n$h2-font-size:                $font-size-base * 2.25; //About 36px\r\n$h3-font-size:                $font-size-base * 2;  //About 32px\r\n$h4-font-size:                $font-size-base * 1.5; //About 24px\r\n$h5-font-size:                $font-size-base * 1.3125; //About 21px\r\n$h6-font-size:                $font-size-base * 1.1; //About 17px\r\n$h7-font-size:                $font-size-base;\r\n$h8-font-size:                $font-size-base * 0.8125; //About 13px\r\n\r\n$p2-font-size:                $font-size-base * 1.1875; //19px\r\n$p3-font-size:                $font-size-base * 1.0625; //17px\r\n$p4-font-size:                $font-size-base;//16px\r\n$p5-font-size:                $font-size-base * 0.9375;//15px\r\n\r\n$text-base:                   $p2-font-size;\r\n\r\n\r\n$line-height-base:            1.5;\r\n$line-height-lg:              1.5;\r\n$line-height-sm:              1.5;", "\r\n$white:    #fff;\r\n$ghost:    #fdfdff;\r\n$alabaster: #fbfbfb;\r\n$smoke:    #f8f8f8;\r\n$gray-110: #fcfdfe;\r\n$gray-120: #f4f7fa;\r\n$gray-210: #e7e9ed;\r\n$gray-opacity:   rgba(231, 233, 237, 0.13);\r\n$gray-310: #d5d7dd;\r\n$storm:    #7d818d;\r\n$gray-610: #666666;\r\n$gray-710: #413e65;\r\n$gray-910: #1F1F1F;\r\n$gray-920: #1e1e20;\r\n$gray-930: #19191b;\r\n$gray-940: #161c2d;\r\n$black:    #000 ;\r\n\r\n\r\n\r\n$red:                   #f64b4b;\r\n$blue:                  #473bf0;\r\n$sky-blue:              #1082e9;\r\n$green:                 #68d585;\r\n$green-shamrock:        #2bd67b;\r\n$blackish-blue:         #13151C;\r\n$blackish-blue-opacity: rgba(#161c2d,.7);\r\n$mirage:                #131829;\r\n$mirage-2:              #161c2d;\r\n$yellow: #f7e36d;\r\n$yellow-orange: #FCAD38;\r\n$narvik: #EDF9F2;\r\n\r\n$primary:       $blue;\r\n$secondary:     $green;\r\n\r\n"]}