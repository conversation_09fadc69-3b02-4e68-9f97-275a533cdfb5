{"version": 3, "sources": ["theme-mode-control/_theme-mode-activation.scss", "theme-mode-control/_theme-mode-mixins.scss", "main.css", "controls/_theme-media-query.scss", "components/_accordion.scss", "components/_badge.scss", "components/_buttons.scss", "controls/_theme-color-control.scss", "components/_cards.scss", "components/_cart.scss", "components/_forms.scss", "components/_header.scss", "bootstrap/mixins/_grid.scss", "components/_menu.scss", "components/_pricings.scss", "components/_slider.scss", "components/_timeline.scss", "components/_tabs.scss", "utilities/_color.scss", "bootstrap/_variables.scss", "controls/_theme-color-maping.scss", "utilities/_flex.scss", "utilities/_grid.scss", "utilities/_shadows.scss", "controls/_theme-shadow-control.scss", "utilities/_size.scss", "bootstrap/mixins/_breakpoints.scss", "utilities/_borders.scss", "controls/_theme-border-control.scss", "theme-utilities/_borders.scss", "theme-utilities/_gradients.scss", "theme-utilities/_hovers.scss", "theme-utilities/_patterns.scss", "theme-utilities/_positioning.scss", "theme-utilities/_shadows.scss", "theme-utilities/_size.scss", "theme-utilities/_typography.scss", "bootstrap/vendor/_rfs.scss", "controls/_theme-variables.scss", "controls/_theme-mixins.scss", "theme-utilities/_animations.scss", "_theme-custom-styles.scss", "sections/_hero-area.scss", "sections/_content-area.scss", "sections/_parallax-section.scss"], "names": [], "mappings": "AAYA;ECVE,cAAK;EACL,6BAAO;EACP,cAAO;EACP,cAAO;EACP,cAAO;EACP,cAAO;EACP,cAAO;EACP,cAAO;EACP,sBAAiB;EACjB,iDAAyB;EACzB,8CAAsB;EACtB,mBAAc;EACd,kCAAa;EACb,oCAAe;EACf,sCAAiB;EACjB,qBAAa;ACAf;;AFDA;EACE,aAAa;AEIf;;AFFA;EACE,aAAa;AEKf;;AFAA;ECLE,cAAK;EACL,cAAO;EACP,cAAO;EACP,cAAO;EACP,cAAO;EACP,WAAO;EACP,cAAO;EACP,cAAO;EACP,yBAAiB;EACjB,6CAAyB;EACzB,0CAAsB;EACtB,sBAAc;EACd,gCAAa;EACb,sBAAe;EACf,wBAAiB;ACSnB;;AFXA;ECZE,cAAK;EACL,cAAO;EACP,cAAO;EACP,cAAO;EACP,cAAO;EACP,WAAO;EACP,cAAO;EACP,cAAO;EACP,yBAAiB;EACjB,6CAAyB;EACzB,0CAAsB;EACtB,sBAAc;EACd,gCAAa;EACb,sBAAe;EACf,wBAAiB;AC2BnB;;AFpBA;EACE,cAAc;AEuBhB;;AFpBA;EAEI,8BAA6B;AEsBjC;;AFxBA;EAKI,cAAc;AEuBlB;;AF5BA;EAQI,wBAAuB;AEwB3B;;AFhCA;EAWI,yBAAwB;AEyB5B;;AFpCA;EAeM,aAAa;AEyBnB;;AFxCA;EAkBM,cAAc;AE0BpB;;AF5CA;EAuBI,aAAa;AEyBjB;;AFrBA;EAGI,cAAc;AEsBlB;;AFzBA;EAMI,yBAAwB;AEuB5B;;AF7BA;EASI,wBAAuB;AEwB3B;;AFjCA;EAaM,wBAAuB;AEwB7B;;AFrCA;EAgBM,yBAAwB;AEyB9B;;AFzCA;EAmBM,cAAc;AE0BpB;;AF7CA;EAsBM,aAAa;AE2BnB;;AFjDA;EA2BI,aAAa;AE0BjB;;AC7HA,sBAAA;ACAA;EAEI,kBAAkB;EAClB,aAAa;EACb,mBAAmB;EACnB,8BAA8B;AFgIlC;;AErIA;EAOM,gBAAgB;EAChB,qBAAqB;EACrB,eAAe;EACf,cAAc;EACd,cAAc;EACd,uBAAuB;EACvB,eAAe;AFkIrB;;AE/IA;EAiBQ,0BAA0B;AFkIlC;;AGnJA;EACE,eAAe;EACf,gBAAgB;EAChB,oBAAoB;EACpB,uBAAsB;EACtB,mBAAmB;AHsJrB;;AI3JA;EAEE,gBAAgB;EAChB,oBAAoB;EACpB,mBAAmB;EACnB,uBAAuB;AJ6JzB;;AIxJG;EAEG,wBAAwB;AJ0J9B;;AItKA;EAiBM,iBAAiB;AJyJvB;;AI1KA;EAqBQ,kBAAkB;AJyJ1B;;AIrJE;EACE,WAAW;AJwJf;;AIzJG;EAIK,iBAAiB;AJyJzB;;AI7JG;EAQG,mBCXyB;ALoK/B;;AItJE;EACE,yBAAyB;AJyJ7B;;AI1JG;EAGG,cCjByB;EDkBzB,gBAAgB;AJ2JtB;;AIxJG;EAEG,oBAAoB;EACpB,mBAAmB;EACnB,uBAAuB;AJ0J7B;;AI9JG;EAMK,iBAAiB;EACjB,eAAe;AJ4JvB;;AInKG;EAWO,iBAAiB;AJ4J3B;;AItJA;EACE,aAAa;EACb,8BAA8B;EAC9B,mBAAmB;AJyJrB;;AI5JA;EAKI,iBAAiB;EACjB,eAAe;AJ2JnB;;AIjKA;EAUM,kBAAkB;AJ2JxB;;AItJA;EACE,WAAW;EACX,YAAY;EACZ,mBAAmB;EACnB,wCCrD6B;EDsD7B,kBAAkB;EAClB,cAAc;AJyJhB;;AI/JA;EAQI,WAAW;EACX,YAAY;EACZ,yBAAyB;EACzB,kBAAkB;EAClB,OAAO;EACP,gBAAgB;EAChB,MAAM;EACN,eAAe;EACf,eAAe;EACf,oBAAoB;EACpB,oBAAmB;AJ2JvB;;AI7KA;EAqBI,mBC1E2B;ALsO/B;;AIjLA;EAuBM,uBAAuB;AJ8J7B;;AMhQA;EACE,gBAAgB;EAChB,cAAc;ANmQhB;;AMrQA;EAII,cDsB2B;ECnB3B,sBAAsB;EACtB,iBAAiB;EACjB,mBAAmB;EACnB,aAAa;ANmQjB;;AM7QA;EAYM,4BDcyB;ALuP/B;;AMjRA;EAoBQ,cAAc;ANiQtB;;AM3PA;EACE,oBAAe;OAAf,eAAe;AN8PjB;;AC5PI;EKHJ;IAGI,oBAAe;SAAf,eAAe;ENiQjB;AACF;;ACvPI;EKdJ;IAOM,oBAAe;SAAf,eAAe;ENmQnB;AACF;;AM9PA;EAGI,eAAe;AN+PnB;;AMlQA;EAOM,yBAAyB;AN+P/B;;AMxPA;EACE,eAAe;EACf,sBAAsB;AN2PxB;;AM7PA;EAII,cD/B2B;AL4R/B;;AMjQA;EAOI,4BDlC2B;ALgS/B;;AMrQA;EAWI,yBD1C2B;ALwS/B;;ACtRI;EKaJ;IAaM,eAAe;IACf,mBAAmB;IAEnB,0CAA2B;ENgQ/B;AACF;;AC9RI;EKaJ;IAsBM,yBDrDyB;ELqT7B;EMtRF;IAwBQ,WAAW;ENiQjB;EMzRF;IA2BQ,+BAAgB;ENiQtB;EM5RF;IA8BQ,mBAAmB;ENiQzB;AACF;;AOrVA;EAEI,aAAa;EACb,eAAe;APuVnB;;AO1VA;EAKM,gBAAe;APyVrB;;ACvUI;EMvBJ;IAOQ,eAAc;EP4VpB;AACF;;ACvUI;EM7BJ;IAUQ,eAAc;EP+VpB;AACF;;AClUI;EMxCJ;IAaQ,eAAc;EPkWpB;AACF;;AClUI;EM9CJ;IAgBQ,eAAe;EPqWrB;AACF;;ACvTI;EM/DJ;IAmBQ,eAAe;EPwWrB;AACF;;AC/VI;EM7BJ;IAwBQ,eACF;EPwWJ;AACF;;AC1VI;EMxCJ;IA2BQ,eACF;EP2WJ;AACF;;AC1VI;EM9CJ;IA8BQ,eACF;EP8WJ;AACF;;AC/UI;EM/DJ;IAiCQ,eACF;EPiXJ;AACF;;AC5WI;EMxCJ;IAsCQ,eACF;EPkXJ;AACF;;AC5WI;EM9CJ;IAyCQ,eACF;EPqXJ;AACF;;ACjWI;EM/DJ;IA4CQ,eACF;EPwXJ;AACF;;AOtaA;EAgDM,eAAe;AP0XrB;;ACnZI;EMvBJ;IAkDQ,eACF;EP4XJ;AACF;;ACnZI;EM7BJ;IAqDQ,eACF;EP+XJ;AACF;;AC9YI;EMxCJ;IAwDQ,eAAe;EPmYrB;AACF;;AC9YI;EM9CJ;IA2DQ,eAAe;EPsYrB;AACF;;ACnYI;EM/DJ;IA8DQ,eAAe;EPyYrB;AACF;;AC3aI;EM7BJ;IAoEQ,eAAe;EPyYrB;AACF;;ACtaI;EMxCJ;IAuEQ,eAAe;EP4YrB;AACF;;ACtaI;EM9CJ;IA0EQ,eAAe;EP+YrB;AACF;;AC3ZI;EM/DJ;IA6EQ,eAAe;EPkZrB;AACF;;AOheA;EAiFM,kBAAkB;EAClB,SAAS;EACT,WAAW;APmZjB;;AC9bI;EMxCJ;IAqFQ,gBAAgB;EPsZtB;AACF;;AQ5eA;EACE,gBAAgB;EAChB,mBAAmB;AR+erB;;AQjfA;EAKM,mBHiByB;AL+d/B;;AQrfA;EAUQ,UAAU;EACV,WAAW;AR+enB;;AQ1fA;EAgBI,kBAAkB;EAClB,cAAc;EACd,WAAW;EACX,YAAY;EACZ,kBAAkB;EAClB,yBAAyB;AR8e7B;;AQngBA;EAuBM,gBAAgB;EAChB,qBAAqB;EACrB,eAAe;EACf,mBAAmB;EACnB,WHVS;EGWT,kBAAkB;EAClB,MAAM;EACN,OAAO;EACP,QAAQ;EACR,SAAS;EACT,UAAU;EACV,aAAa;EACb,mBAAmB;EACnB,uBAAuB;ARgf7B;;AQ3eA;EAKU,yBHxBqB;EGyBrB,gBAAgB;AR0e1B;;AQhfA;EAQY,UAAU;AR4etB;;AQreA;EACE,kBAAkB;EAClB,qBAAqB;EACrB,yBAAyB;EACzB,yBAAyB;EACzB,eAAe;EACf,eAAe;EACf,gBAAgB;EAChB,gBAAgB;EAChB,oBAAoB;ARwetB;;AQjfA;EAWI,WAAW;EACX,yBH9C2B;EG+C3B,eAAe;EACf,eAAe;EACf,gBAAgB;EAChB,gBAAgB;EAChB,oBAAoB;EACpB,kBAAkB;EAClB,QAAQ;EACR,SAAS;EACT,UAAU;EACV,gCAA+B;AR0enC;;AQreA;EACE,mBAAmB;ARwerB;;AQzeA;EAGI,gCH/Ec;ALyjBlB;;ACniBI;EOsDJ;IAKM,+BHjFY;EL8jBhB;AACF;;ACxhBI;EOqCJ;IAQM,gBAAe;ERgfnB;AACF;;AQzfA;EAWM,eAAe;ARkfrB;;AQ7fA;EAeI,kBAAkB;EAClB,kBAAkB;EAClB,SAAS;EACT,eAAe;ARkfnB;;AQpgBA;EAoBM,gBAAgB;EAChB,kCAAkC;EAClC,gBAAgB;EAChB,OAAO;EACP,MAAM;EACN,YAAY;EACZ,kBAAkB;EAClB,WAAW;EACX,eAAe;EACf,aAAa;EACb,mBAAmB;EACnB,uBAAuB;ARof7B;;AQnhBA;EAkCM,aAAa;ARqfnB;;AQvhBA;EAuCI,gBAAgB;ARofpB;;AQ3hBA;EA4CE,aAAa;EACb,mBAAmB;ARmfrB;;AQhiBA;EA+CI,YAAY;EACZ,WAAW;ARqff;;AQriBA;EAoDM,kBAAkB;EAClB,eAAe;EACf,cH/GyB;EGgHzB,eAAe;ARqfrB;;AQ5iBA;EAyDQ,cHlHuB;ALymB/B;;AQhjBA;EAyDQ,cHlHuB;ALymB/B;;AQhjBA;EAyDQ,cHlHuB;ALymB/B;;AQhjBA;EA6DM,eAAe;EACf,WAAW;EACX,YAAY;EACZ,aAAa;EACb,mBAAmB;EACnB,uBAAuB;EACvB,cH5HyB;EG6HzB,kBAAkB;EAClB,MAAM;EACN,OAAO;ARufb;;AQ7jBA;EA0EM,gBAAgB;ARuftB;;ACtmBI;EQ7CF;IAEI,iBAAiB;ETspBrB;AACF;;ASzpBE;EAKI,aAAa;ATwpBnB;;AClpBI;EQXF;IAOM,oBAAoB;IACpB,gBAAgB;IAChB,gBAAgB;IAChB,eAAe;IACf,gBAAgB;IAChB,uBAAuB;ET2pB7B;AACF;;AC7pBI;EQXF;IAeM,gBAAgB;IAChB,gBAAgB;IAChB,eAAe;ET8pBrB;AACF;;ACppBI;EQ5BF;IAoBM,gBAAgB;ETiqBtB;AACF;;AS9pBE;EAEI,gBAAgB;EAChB,YAAY;EACZ,eAAe;ATgqBrB;;AS7pBE;EACE,kBAAkB;EAClB,MAAM;EACN,QAAQ;EACR,eAAe;EACf,WAAW;EACX,YAAY;EACZ,aAAa;EACb,uBAAuB;EACvB,YAAY;EACZ,uBAAuB;EACvB,gBAAgB;ATgqBpB;;AS3qBE;EAaI,cAAc;ATkqBpB;;AS3pBE;EAEI,gBAAgB;AT6pBtB;;AS3pBI;EACE,kBAAkB;EAClB,MAAM;EACN,OAAO;EACP,WAAW;EACX,YAAY;AT8pBlB;;AS5pBK;EAEG,6BAA4B;EAC5B,MAAM;EACN,QAAQ;EACR,WAAW;EACX,YAAY;AT8pBpB;;ACrrBI;EQiBC;IAQK,0BAAyB;IACzB,eAAe;ETiqBvB;ES1qBG;IAWO,4BAA4B;IAC5B,eAAe;ETkqBzB;ES9qBG;IAgBO,yBAAyB;IACzB,oDAAoD;IACpD,aAAa;ETiqBvB;AACF;;ASprBK;EAuBG,MAAM;EACN,QAAQ;EACR,WAAW;EACX,YAAY;EACZ,0BAAyB;EACzB,eAAe;ATiqBvB;;AS7rBK;EA8BK,4BAA4B;EAC5B,eAAe;ATmqBzB;;ASlsBK;EAkCK,yBAAyB;EACzB,oDAAoD;EACpD,aAAa;AToqBvB;;ACztBI;EQ0DG;IAGK,iBAAiB;IACjB,kBAAkB;ETiqB5B;AACF;;AC/sBI;EQiDG;IC3GL,WAAW;IACX,mBAA0B;IAC1B,kBAAyB;IACzB,kBAAkB;IAClB,iBAAiB;EV8wBjB;AACF;;ASxqBO;EAUO,QAAQ;EACR,0CAA0C;ATkqBxD;;AS7qBO;EAcO,2CAA2C;EAC3C,QAAQ;ATmqBtB;;ASlrBO;EAqBK,2BAA2B;EAC3B,kBAAkB;ATiqB9B;;ACxuBI;EQiDG;IAyBK,WAAW;ETmqBrB;AACF;;AShqBO;EAEG,iBAAiB;ATkqB3B;;ASpqBO;EAMK,UAAU;EACV,6CAA6C;ATkqBzD;;ASzqBO;EAUK,8CAA8C;EAC9C,UAAU;ATmqBtB;;AS3pBE;EAEI,kBAAkB;EAClB,QAAQ;EACR,SAAS;EACT,YAAY;EACZ,UAAU;EACV,WAAW;EACX,eAAe;AT6pBrB;;ASrqBE;EAYI,uBAAuB;AT6pB7B;;ASzpBE;EAGQ,kBAAkB;EAClB,OAAO;EACP,SAAS;EACT,YAAY;EACZ,UAAU;EACV,WAAW;EACX,eAAe;AT0pBzB;;ASppBE;EACE,kBAAkB;EAClB,eAAe;EACf,yBAAyB;EACzB,iBAAiB;EACjB,kBAAkB;EAClB,kBAAkB;ATupBtB;;AC9yBI;EQiJF;IAQI,iBAAiB;ET0pBrB;AACF;;ASnqBE;EAWI,YAAY;EACZ,WAAW;EACX,eAAe;EACf,oBAAoB;EACpB,aAAa;EACb,mBAAmB;EACnB,uBAAuB;EACvB,kBAAkB;EAClB,MAAM;EACN,QAAQ;EACR,+BAA8B;EAC9B,mBJ7LyB;EI8LzB,WAAW;AT4pBjB;;ASrpBE;EAEI,6BAA6B;EAC7B,MAAM;EACN,QAAQ;EACR,WAAW;EACX,YAAY;ATupBlB;;AC50BI;EQ+KF;IASM,0BAA0B;IAC1B,eAAe;ETypBrB;ESnqBA;IAaQ,4BAA4B;IAC5B,eAAe;ETypBvB;ESvqBA;IAmBQ,yBAAyB;IACzB,oDAAoD;IACpD,aAAa;IACb,gBAAgB;ETupBxB;AACF;;AS9qBE;EA4BI,MAAM;EACN,QAAQ;EACR,WAAW;EACX,YAAY;EACZ,0BAA0B;EAC1B,eAAe;ATspBrB;;ASvrBE;EAoCM,4BAA4B;EAC5B,eAAe;ATupBvB;;AS5rBE;EAyCM,yBAAyB;EACzB,oDAAoD;EACpD,aAAa;EACb,gBAAgB;ATupBxB;;AW95BA;EAYA;;yBXu5ByB;AACzB;;ACx3BI;EU5CJ;IAEI,aAAa;IACb,yBAAyB;EXu6B3B;AACF;;AW36BA;EAiBE,aAAa;EACb,mBAAmB;EACnB,8BAA8B;AX85BhC;;AWj7BA;EAqBI,aAAa;AXg6BjB;;AWr7BA;EAwBI,eAAe;EACf,WAAW;EACX,cAAc;EACd,YAAY;EACZ,gBAAgB;EAChB,gBAAgB;EAChB,eAAe;AXi6BnB;;AW/7BA;EAkCM,0BAA0B;AXi6BhC;;AWn8BA;EAwCM,oCAAmC;EACnC,eAAe;EACf,gBAAgB;AX+5BtB;;AC75BI;EU5CJ;IA4CQ,4BAA2B;IAC3B,+BAA8B;IAC9B,6BAA4B;IAC5B,8BAA6B;EXk6BnC;AACF;;AWl9BA;EAkDQ,yBAAyB;AXo6BjC;;AWt9BA;EA0DU,0BAA0B;AXg6BpC;;AWx5BA;0BX25B0B;ACj7BtB;EUwBJ;IAEI,kBAAkB;IAClB,gBAAgB;IAChB,gBAAgB;IAChB,8CAA8C;IAC9C,kBAAkB;IAClB,yBAAyB;IACzB,yBAAyB;IACzB,iBAAiB;IACjB,SAAS;IACT,YAAY;IACZ,UAAU;IACV,+BAA+B;IAC/B,oBAAoB;IAEpB,OAAO;IACP,WAAW;IACX,4BAA4B;IAC5B,yBAAyB;IACzB,yBAAyB;IACzB,cAAc;IACd,6BNtE2B;ELg+B7B;AACF;;AWj7BA;EAyBI,cAAc;EACd,eAAe;EACf,gBAAgB;EAChB,sBAAsB;EACtB,kBAAmB;EACnB,mBAAoB;EACpB,iBAAiB;EACjB,oBAAoB;AX45BxB;;AW57BA;EAkCM,cAAc;AX85BpB;;AWh8BA;EAsCM,cNtFyB;ALo/B/B;;AWp8BA;EA2CQ,yBNzFuB;ALs/B/B;;ACh+BI;EUwBJ;IA8CU,SAAS;IACT,UAAU;IACV,QAAQ;IACR,UAAU;IACV,2BAA2B;IAC3B,eAAe;IACf,oBAAoB;IACpB,sBAAsB;EX+5B9B;AACF;;AC/gCI;EU0DJ;IAyDY,kBAAkB;IAClB,mBAAmB;EXi6B7B;AACF;;ACp/BI;EUwBJ;IA6DY,kBAAmB;IACnB,mBAAoB;EXo6B9B;AACF;;AWn+BA;EAoEI,UAAU;EACV,WAAW;AXm6Bf;;AW95BA;kDXi6BkD;ACngC9C;EUoGJ;IAEG,kBAAkB;IAClB,WAAW;EXi6BZ;AACF;;ACzgCI;EUoGJ;IAQO,QAAQ;IACR,UAAU;IACV,uBAAuB;EXk6B5B;AACF;;ACjhCI;EUoGJ;IAeM,6CAA6C;IAC7C,WAAW;IACX,UAAU;IACV,uBAAuB;EXm6B3B;AACF;;AC1hCI;EUoGJ;IAuBM,6CAA6C;IAC7C,WAAW;IACX,UAAU;IACV,uBAAuB;EXo6B3B;AACF;;AW95BA;EAOE,kBAAkB;AX25BpB;;AWl6BA;EAIM,yBAAwB;AXk6B9B;;AC3iCI;EUqIJ;IAWU,SAAS;IACT,UAAU;IACV,uBAAuB;IACvB,2BAA2B;EXg6BnC;AACF;;AWz5BA;0BX45B0B;AW15B1B;EACE,UAAU;AX45BZ;;AC1jCI;EU6JJ;IAGE,gBAAgB;EX+5BhB;AACF;;AW55BA;EAEI,iBAAiB;AX85BrB;;ACzjCI;EUyJJ;IAKI,SAAS;EXg6BX;AACF;;AC1kCI;EUoKJ;IAQI,aAAa;IACb,gBAAgB;IAChB,SAAS;IACT,4CAA4C;IAE5C,sBAAsB;IAEtB,SAAS;IACT,YAAY;IACZ,UAAU;IACV,oBAAoB;IACpB,sCAAsC;IACtC,2CAA2C;IAC3C,yBAAyB;IACzB,kBAAkB;EXi6BpB;AACF;;AC3kCI;EUmJJ;IAyBM,iBAAiB;EXo6BrB;AACF;;AW97BA;EA6BM,iBAAiB;EACjB,oBAAoB;EACpB,cAAc;AXq6BpB;;AWp8BA;EAmCI,kBAAkB;EAClB,qBAAqB;EACrB,cN7N2B;EM8N3B,eAAe;EACf,gBAAgB;AXq6BpB;;ACrmCI;EUyJJ;IA0CQ,yCAAyC;IACzC,kBAAkB;IAClB,oBAAoB;IACpB,iBAAiB;EXu6BvB;AACF;;ACznCI;EUoKJ;IAgDM,kBAAmB;IACnB,mBAAoB;EX06BxB;EW39BF;IAmDQ,yBAAyB;EX26B/B;AACF;;ACxnCI;EUyJJ;IA2DM,eAAe;IACf,gBAAgB;EXy6BpB;EWr+BF;IA+DM,cAAc;IACd,eAAe;EXy6BnB;AACF;;ACnoCI;EU+NJ;IAEI,SAAS;EXu6BX;AACF;;ACppCI;EU0OJ;IAKI,2CAA2C;IAC3C,aAAa;IACb,gBAAgB;IAChB,SAAS;IAET,sBAAsB;IAEtB,SAAS;IACT,4CAA4C;IAC5C,YAAY;IACZ,UAAU;IACV,oBAAoB;IACpB,sCAAsC;IACtC,8CAA8C;IAC9C,yBAAyB;EXw6B3B;AACF;;ACrpCI;EUyNJ;IAsBM,iBAAiB;EX26BrB;AACF;;AWl8BA;EA0BM,iBAAiB;EACjB,oBAAoB;EACpB,yBAAwB;AX46B9B;;AWx8BA;EAgCI,kBAAkB;EAClB,qBAAqB;EACrB,cNhS2B;EMiS3B,eAAe;EACf,gBAAgB;AX46BpB;;AC/qCI;EU+NJ;IAsCQ,yCAAyC;IACzC,kBAAkB;IAClB,oBAAoB;IACpB,iBAAiB;EX+6BvB;AACF;;ACnsCI;EU0OJ;IA4CM,kBAAmB;IACnB,mBAAoB;IACpB,mBAAmB;IACnB,0BAAkB;IAAlB,uBAAkB;IAAlB,kBAAkB;EXk7BtB;EWj+BF;IAiDQ,mBAAmB;EXm7BzB;AACF;;AC/sCI;EU0OJ;IAwDQ,yBAAyB;EXk7B/B;AACF;;AW3+BA;EA4DM,cN1TyB;EM2TzB,kBAAkB;AXm7BxB;;AWh/BA;EAgEM,gBAAgB;EAChB,mBAAmB;AXo7BzB;;ACptCI;EU+NJ;IAuEM,eAAe;IACf,gBAAgB;EXm7BpB;EW3/BF;IA2EM,cAAc;IACd,eAAe;EXm7BnB;AACF;;AW/6BA;EACE,kBAAkB;AXk7BpB;;AWn7BA;EAGI,kBAAkB;EAClB,YAAY;EACZ,WAAW;EACX,kBAAiB;EACjB,sDAAsD;EACtD,4CAA4C;EAC5C,uBAAsB;EACtB,UAAU;EACV,eAAe;EACf,+BAA8B;AXo7BlC;;AC3vCI;EU2TJ;IAcM,WAAW;EXu7Bf;AACF;;AWt8BA;EAmBI,kBAAkB;EAClB,YAAY;EACZ,QAAQ;EACR,kBAAiB;EACjB,yDAAyD;EACzD,4CAA4C;EAC5C,sBAAqB;EACrB,UAAU;EACV,eAAe;EACf,+BAA8B;AXu7BlC;;AC9wCI;EU2TJ;IA8BM,WAAW;EX07Bf;AACF;;ACpxCI;EU+VJ;IAKQ,aAAa;EXq7BnB;AACF;;AC/wCI;EUoVJ;IAUQ,cAAc;IACd,eAAe;IACf,MAAM;IACN,qBAAqB;IACrB,WAAW;IACX,kBAAkB;IAClB,mBAAmB;IACnB,YAAY;IACZ,oBAAmB;IACnB,eAAe;IACf,0CAA0C;IAC1C,iBAAiB;IACjB,YAAY;IACZ,gBAAgB;EXs7BtB;AACF;;AC/0CI;EUiYJ;IAyBU,YAAY;EX07BpB;AACF;;ACp0CI;EUgXJ;IA4BU,YAAY;EX67BpB;AACF;;AC9yCI;EUoVJ;IA+BU,QAAQ;EXg8BhB;EW/9BF;IAkCc,cAAc;EXg8B1B;EWl+BF;IAwCU,qBAAoB;IACpB,YAAY;EX67BpB;EWt+BF;IA6CU,UAAU;EX47BlB;EWz+BF;IAiDU,gBACF;EX07BN;EW5+BF;IAqDU,yBN5aqB;IM6arB,4BAA4B;EX07BpC;EWh/BF;IA2DU,cAAc;EXw7BtB;EWn/BF;IAgEU,mBAAmB;IACnB,kBAAkB;IAChB,4CAA4C;EXs7BtD;EWx/BF;IAoEc,oBAAoB;IACpB,iBAAiB;EXu7B7B;EW5/BF;IAwEc,SAAS;IACT,gBAAgB;IAChB,eAAe;IACf,UAAU;EXu7BtB;EWlgCF;IA6EgB,cAAa;IACb,iBAAgB;IAChB,yCAAyC;IACzC,kBAAkB;EXw7BhC;EWxgCF;IAkFkB,iBAAiB;IACjB,oBAAoB;EXy7BpC;EW5gCF;IAwFc,gCAAgC;EXu7B5C;EW/gCF;IA6FY,gBAAgB;IAChB,kBAAkB;IAClB,QAAQ;EXq7BlB;EWphCF;IAmGkB,cN1da;EL84C7B;EWvhCF;IAwGY,aAAa;EXk7BvB;AACF;;AWx6BA;EACE,4CAA2C;EAC3C,mDAAiD;AX26BnD;;AWp6BA;EACE,kBAAkB;EAClB,iBAAiB;EACjB,iBAAiB;AXu6BnB;;AW16BA;EAKI,aAAa;EACb,qBAAqB;EACrB,iBAAiB;EACjB,SAAS;EAET,oBAAoB;EACpB,eAAe;EACf,kCAAkC;EAClC,yBAAyB;EACzB,mCAAmC;EACnC,oBAAoB;EACpB,cAAc;EACd,SAAS;EACT,6BAA6B;AXw6BjC;;AW17BA;EAoBM,kBAAkB;EAClB,qBAAqB;EACrB,WAAW;EACX,YAAY;AX06BlB;;AWj8BA;EA0BQ,kEAA2D;EAC3D,oCAAmC;EACnC,kBAAkB;EAClB,gCAAgC;EAChC,yBAAyB;EACzB,8BAA8B;EAC9B,kBAAkB;EAClB,4CAA4C;EAC5C,QAAQ;EACR,cAAc;EACd,gBAAgB;EAxCtB,WAAW;EACX,WAAW;AXo9Bb;;AWj9BA;EAuCY,SAxCI;EAyCJ,gDAAgD;AX86B5D;;AWt9BA;EA2CY,YA5CI;EA6CJ,yFAA+E;AX+6B3F;;AW39BA;EA+CY,cAAc;EACd,WAAW;EACX,kBAAkB;EArD5B,WAAW;EACX,WAAW;EAsDD,kBAAkB;EAClB,4CAA4C;AXi7BxD;;AWr+BA;EA0DI,SAAS;EACT,sFAA8E;EAC9E,yBAAyB;AX+6B7B;;AW3+BA;EA+DI,MAAM;EACN,6DAA0D;EAC1D,UAAU;AXg7Bd;;AWj/BA;EAoEI,sBAAsB;EACtB,+DAAyD;EACzD,wBAAwB;AXi7B5B;;AWv/BA;EAyEE,aAAa;EACb,gBAAgB;AXk7BlB;;AW96BA;EACE,kBAAkB;EAClB,MAAM;EACN,QAAQ;EACR,eAAe;EACf,WAAW;EACX,YAAY;EACZ,aAAa;EACb,uBAAuB;EACvB,YAAY;EACZ,uBAAuB;EACvB,gBAAgB;AXi7BlB;;AW57BA;EAaI,oCAAmC;AXm7BvC;;AWh7BA;EACE,kEAA2D;EAC3D,oCAAmC;EACnC,kBAAkB;EAClB,gCAAgC;EAChC,yBAAyB;EACzB,8BAA8B;EAC9B,kBAAkB;EAClB,4CAA4C;EAC5C,QAAQ;EACR,cAAc;EACd,gBAAgB;EAGhB,SAAS;EACT,QAAQ;EACR,SAAS;EACT,2CAA2C;AXi7B7C;;AWl8BA;EAoBM,MAAM;EACN,gDAAgD;AXk7BtD;;AWv8BA;EAwBM,YAAY;EACZ,yFAA+E;EAC/E,yBAAyB;AXm7B/B;;AW78BA;EA6BM,cAAc;EACd,WAAW;EACX,kBAAkB;EAjItB,WAAW;EACX,WAAW;EAkIP,kBAAkB;EAClB,4CAA4C;AXq7BlD;;AAEA;EY7jDI,qBAAqB;EACrB,0BAA0B;AZ+jD9B;;AAEA;EY7jDI,mBPc2B;ALijD/B;;AAEA;EY9jDM,uBAAuB;AZgkD7B;;AAEA;EY3jDM,qBAAqB;EACrB,2BAA2B;AZ6jDjC;;AYvjDA;EAEI,qBAAqB;EACrB,0BAA0B;AZyjD9B;;AYtjDA;EAEI,qBAAqB;EACrB,0BAA0B;AZwjD9B;;Aa1lDA;EAEI,cAAa;Ab4lDjB;;ACtjDI;EYxCJ;IAIM,cAAc;Eb+lDlB;AACF;;ACtjDI;EY9CJ;IAOM,cAAc;EbkmDlB;AACF;;Aa1mDA;EAWM,qCAAqC;EACrC,qBAAqB;EAErB,aAAY;EACZ,mBAAmB;AbkmDzB;;ACzkDI;EYxCJ;IAiBQ,aAAa;EbqmDnB;AACF;;ACzkDI;EY9CJ;IAoBQ,aAAa;EbwmDnB;AACF;;Aa7nDA;EAuBQ,WAAW;Ab0mDnB;;AarmDA;EAEI,eAAe;AbumDnB;;AazmDA;EAKI,qCAAqC;EACrC,qBAAqB;EACrB,aAAa;EACb,cAAa;EACb,mBAAmB;AbwmDvB;;AajnDA;EAWM,WAAW;Ab0mDjB;;AarmDA;EACE,aAAa;AbwmDf;;AalmDA;EAEI,eAAe;EACf,oBAAoB;AbomDxB;;AavmDA;EAMI,cAAc;AbqmDlB;;AanmDE;EACE,aAAa;EACb,mBAAmB;EACnB,yBAAyB;EACzB,8BAAsB;EAAtB,2BAAsB;EAAtB,sBAAsB;AbsmD1B;;Aa1mDG;EAMG,YAAY;EACZ,SAAS;EACT,uBAAuB;EACvB,kBAAkB;AbwmDxB;;AajnDG;EAWK,kCAAkC;EAClC,eAAe;EACf,YAAY;EACZ,WAAW;EACX,aAAa;EACb,gBAAgB;EAChB,uBAAuB;EACvB,mBAAmB;EACnB,WR7DO;ALuqDf;;Aa7nDG;EAwBG,gBAAgB;EAChB,YAAY;EACZ,eAAe;AbymDrB;;AanoDG;EA8BK,UAAU;AbymDlB;;AavoDG;EAoCK,gBAAgB;AbumDxB;;ActsDA;EACE,aAAa;EACb,uBAAuB;EACvB,mBAAmB;EACnB,eAAe;AdysDjB;;ACjsDI;EaZJ;IAMI,eAAe;Ed4sDjB;AACF;;ACrqDI;Ea9CJ;IASI,eAAe;Ed+sDjB;AACF;;AcztDA;EAYI,kBAAkB;EAClB,UAAU;EACV,mBAAmB;EACnB,kBAAkB;AditDtB;;ACptDI;EaZJ;IAiBM,kBAAkB;IAClB,mBAAmB;IACnB,2BAA2B;IAC3B,2BAA2B;EdotD/B;AACF;;AC7tDI;EaZJ;IAuBM,kBAAkB;IAClB,mBAAmB;IACnB,2BAA2B;IAC3B,2BAA2B;EdutD/B;AACF;;AC1sDI;EaxCJ;IA6BM,gBAAgB;IAChB,+BAA+B;IAC/B,+BAA+B;Ed0tDnC;AACF;;AC5sDI;Ea9CJ;IAkCM,kBAAkB;IAClB,mBAAmB;IACnB,+BAA+B;IAC/B,+BAA+B;Ed6tDnC;AACF;;AcnwDA;EA4CQ,aAAa;Ad2tDrB;;AC/tDI;EaxCJ;IA8CU,cAAc;Ed8tDtB;AACF;;Ac7wDA;EAmDM,gBAAgB;Ad8tDtB;;AcjxDA;EAqDQ,WAAW;AdguDnB;;AcrxDA;EAyDM,kBAAkB;EAClB,8CAA8C;EAC9C,SAAS;EACT,SAAS;EACT,WAAW;EACX,aAAa;AdguDnB;;AClxDI;EaZJ;IAgEQ,UAAU;IACV,gBAAgB;IAChB,cAAc;EdmuDpB;AACF;;AC9vDI;EaxCJ;IAqEQ,UAAU;IACV,gBAAgB;EdsuDtB;AACF;;AC9uDI;Ea/DJ;IAyEQ,WAAW;IACX,eAAe;EdyuDrB;AACF;;ACvxDI;Ec7BJ;IAEI,4CAA4C;EfuzD9C;AACF;;Ae1zDA;EAKM,iBAAgB;EAChB,kBAAkB;EAClB,kBAAkB;EAClB,oBAAoB;EACpB,4CAA4C;EAC5C,iBAAiB;EACjB,mBAAmB;EACnB,yBAAyB;AfyzD/B;;ACxyDI;Ec7BJ;IAcQ,mBAAmB;Ef4zDzB;AACF;;Ae30DA;EAiBQ,eAAe;Af8zDvB;;Ae/0DA;EAoBQ,qBVEuB;EUDvB,cVCuB;AL8zD/B;;AgBj1DI;EACE,yBXkByB;ALk0D/B;;AgBr1DI;EACE,wCXkByB;ALs0D/B;;AgBz1DI;EACE,wCXkByB;AL00D/B;;AgB71DI;EACE,wCXkByB;AL80D/B;;AgBj2DI;EACE,wCXkByB;ALk1D/B;;AgBr2DI;EACE,wCXkByB;ALs1D/B;;AgBz2DI;EACE,wCXkByB;AL01D/B;;AgB72DI;EACE,wCXkByB;AL81D/B;;AgBj3DI;EACE,wCXkByB;ALk2D/B;;AgBr3DI;EACE,wCXkByB;ALs2D/B;;AgBp3DI;EACE,cXayB;AL02D/B;;AgBx3DI;EACE,6BXayB;AL82D/B;;AgB53DI;EACE,6BXayB;ALk3D/B;;AgBh4DI;EACE,6BXayB;ALs3D/B;;AgBp4DI;EACE,6BXayB;AL03D/B;;AgBx4DI;EACE,6BXayB;AL83D/B;;AgB54DI;EACE,6BXayB;ALk4D/B;;AgBh5DI;EACE,6BXayB;ALs4D/B;;AgBp5DI;EACE,6BXayB;AL04D/B;;AgBx5DI;EACE,6BXayB;AL84D/B;;AgBj6DI;EACE,yBXoByB;ALg5D/B;;AgBr6DI;EACE,0CXoByB;ALo5D/B;;AgBz6DI;EACE,0CXoByB;ALw5D/B;;AgB76DI;EACE,0CXoByB;AL45D/B;;AgBj7DI;EACE,0CXoByB;ALg6D/B;;AgBr7DI;EACE,0CXoByB;ALo6D/B;;AgBz7DI;EACE,0CXoByB;ALw6D/B;;AgB77DI;EACE,0CXoByB;AL46D/B;;AgBj8DI;EACE,0CXoByB;ALg7D/B;;AgBr8DI;EACE,0CXoByB;ALo7D/B;;AgBp8DI;EACE,cXeyB;ALw7D/B;;AgBx8DI;EACE,+BXeyB;AL47D/B;;AgB58DI;EACE,+BXeyB;ALg8D/B;;AgBh9DI;EACE,+BXeyB;ALo8D/B;;AgBp9DI;EACE,+BXeyB;ALw8D/B;;AgBx9DI;EACE,+BXeyB;AL48D/B;;AgB59DI;EACE,+BXeyB;ALg9D/B;;AgBh+DI;EACE,+BXeyB;ALo9D/B;;AgBp+DI;EACE,+BXeyB;ALw9D/B;;AgBx+DI;EACE,+BXeyB;AL49D/B;;AgBj/DI;EACE,yBXoByB;ALg+D/B;;AgBr/DI;EACE,0CXoByB;ALo+D/B;;AgBz/DI;EACE,0CXoByB;ALw+D/B;;AgB7/DI;EACE,0CXoByB;AL4+D/B;;AgBjgEI;EACE,0CXoByB;ALg/D/B;;AgBrgEI;EACE,0CXoByB;ALo/D/B;;AgBzgEI;EACE,0CXoByB;ALw/D/B;;AgB7gEI;EACE,0CXoByB;AL4/D/B;;AgBjhEI;EACE,0CXoByB;ALggE/B;;AgBrhEI;EACE,0CXoByB;ALogE/B;;AgBphEI;EACE,cXeyB;ALwgE/B;;AgBxhEI;EACE,+BXeyB;AL4gE/B;;AgB5hEI;EACE,+BXeyB;ALghE/B;;AgBhiEI;EACE,+BXeyB;ALohE/B;;AgBpiEI;EACE,+BXeyB;ALwhE/B;;AgBxiEI;EACE,+BXeyB;AL4hE/B;;AgB5iEI;EACE,+BXeyB;ALgiE/B;;AgBhjEI;EACE,+BXeyB;ALoiE/B;;AgBpjEI;EACE,+BXeyB;ALwiE/B;;AgBxjEI;EACE,+BXeyB;AL4iE/B;;AgBjkEI;EACE,yBCwCW;AjB4hEjB;;AgBrkEI;EACE,yCCwCW;AjBgiEjB;;AgBzkEI;EACE,yCCwCW;AjBoiEjB;;AgB7kEI;EACE,yCCwCW;AjBwiEjB;;AgBjlEI;EACE,yCCwCW;AjB4iEjB;;AgBrlEI;EACE,yCCwCW;AjBgjEjB;;AgBzlEI;EACE,yCCwCW;AjBojEjB;;AgB7lEI;EACE,yCCwCW;AjBwjEjB;;AgBjmEI;EACE,yCCwCW;AjB4jEjB;;AgBrmEI;EACE,yCCwCW;AjBgkEjB;;AgBpmEI;EACE,cCmCW;AjBokEjB;;AgBxmEI;EACE,8BCmCW;AjBwkEjB;;AgB5mEI;EACE,8BCmCW;AjB4kEjB;;AgBhnEI;EACE,8BCmCW;AjBglEjB;;AgBpnEI;EACE,8BCmCW;AjBolEjB;;AgBxnEI;EACE,8BCmCW;AjBwlEjB;;AgB5nEI;EACE,8BCmCW;AjB4lEjB;;AgBhoEI;EACE,8BCmCW;AjBgmEjB;;AgBpoEI;EACE,8BCmCW;AjBomEjB;;AgBxoEI;EACE,8BCmCW;AjBwmEjB;;AgBjpEI;EACE,yBX0BU;AL0nEhB;;AgBrpEI;EACE,0CX0BU;AL8nEhB;;AgBzpEI;EACE,0CX0BU;ALkoEhB;;AgB7pEI;EACE,0CX0BU;ALsoEhB;;AgBjqEI;EACE,0CX0BU;AL0oEhB;;AgBrqEI;EACE,0CX0BU;AL8oEhB;;AgBzqEI;EACE,0CX0BU;ALkpEhB;;AgB7qEI;EACE,0CX0BU;ALspEhB;;AgBjrEI;EACE,0CX0BU;AL0pEhB;;AgBrrEI;EACE,0CX0BU;AL8pEhB;;AgBprEI;EACE,cXqBU;ALkqEhB;;AgBxrEI;EACE,+BXqBU;ALsqEhB;;AgB5rEI;EACE,+BXqBU;AL0qEhB;;AgBhsEI;EACE,+BXqBU;AL8qEhB;;AgBpsEI;EACE,+BXqBU;ALkrEhB;;AgBxsEI;EACE,+BXqBU;ALsrEhB;;AgB5sEI;EACE,+BXqBU;AL0rEhB;;AgBhtEI;EACE,+BXqBU;AL8rEhB;;AgBptEI;EACE,+BXqBU;ALksEhB;;AgBxtEI;EACE,+BXqBU;ALssEhB;;AgBjuEI;EACE,yBXiByB;ALmtE/B;;AgBruEI;EACE,wCXiByB;ALutE/B;;AgBzuEI;EACE,wCXiByB;AL2tE/B;;AgB7uEI;EACE,wCXiByB;AL+tE/B;;AgBjvEI;EACE,wCXiByB;ALmuE/B;;AgBrvEI;EACE,wCXiByB;ALuuE/B;;AgBzvEI;EACE,wCXiByB;AL2uE/B;;AgB7vEI;EACE,wCXiByB;AL+uE/B;;AgBjwEI;EACE,wCXiByB;ALmvE/B;;AgBrwEI;EACE,wCXiByB;ALuvE/B;;AgBpwEI;EACE,cXYyB;AL2vE/B;;AgBxwEI;EACE,6BXYyB;AL+vE/B;;AgB5wEI;EACE,6BXYyB;ALmwE/B;;AgBhxEI;EACE,6BXYyB;ALuwE/B;;AgBpxEI;EACE,6BXYyB;AL2wE/B;;AgBxxEI;EACE,6BXYyB;AL+wE/B;;AgB5xEI;EACE,6BXYyB;ALmxE/B;;AgBhyEI;EACE,6BXYyB;ALuxE/B;;AgBpyEI;EACE,6BXYyB;AL2xE/B;;AgBxyEI;EACE,6BXYyB;AL+xE/B;;AgBjzEI;EACE,yBCIY;AjBgzElB;;AgBrzEI;EACE,0CCIY;AjBozElB;;AgBzzEI;EACE,0CCIY;AjBwzElB;;AgB7zEI;EACE,0CCIY;AjB4zElB;;AgBj0EI;EACE,0CCIY;AjBg0ElB;;AgBr0EI;EACE,0CCIY;AjBo0ElB;;AgBz0EI;EACE,0CCIY;AjBw0ElB;;AgB70EI;EACE,0CCIY;AjB40ElB;;AgBj1EI;EACE,0CCIY;AjBg1ElB;;AgBr1EI;EACE,0CCIY;AjBo1ElB;;AgBp1EI;EACE,cCDY;AjBw1ElB;;AgBx1EI;EACE,+BCDY;AjB41ElB;;AgB51EI;EACE,+BCDY;AjBg2ElB;;AgBh2EI;EACE,+BCDY;AjBo2ElB;;AgBp2EI;EACE,+BCDY;AjBw2ElB;;AgBx2EI;EACE,+BCDY;AjB42ElB;;AgB52EI;EACE,+BCDY;AjBg3ElB;;AgBh3EI;EACE,+BCDY;AjBo3ElB;;AgBp3EI;EACE,+BCDY;AjBw3ElB;;AgBx3EI;EACE,+BCDY;AjB43ElB;;AgBj4EI;EACE,yBCWY;AjBy3ElB;;AgBr4EI;EACE,uCCWY;AjB63ElB;;AgBz4EI;EACE,uCCWY;AjBi4ElB;;AgB74EI;EACE,uCCWY;AjBq4ElB;;AgBj5EI;EACE,uCCWY;AjBy4ElB;;AgBr5EI;EACE,uCCWY;AjB64ElB;;AgBz5EI;EACE,uCCWY;AjBi5ElB;;AgB75EI;EACE,uCCWY;AjBq5ElB;;AgBj6EI;EACE,uCCWY;AjBy5ElB;;AgBr6EI;EACE,uCCWY;AjB65ElB;;AgBp6EI;EACE,cCMY;AjBi6ElB;;AgBx6EI;EACE,4BCMY;AjBq6ElB;;AgB56EI;EACE,4BCMY;AjBy6ElB;;AgBh7EI;EACE,4BCMY;AjB66ElB;;AgBp7EI;EACE,4BCMY;AjBi7ElB;;AgBx7EI;EACE,4BCMY;AjBq7ElB;;AgB57EI;EACE,4BCMY;AjBy7ElB;;AgBh8EI;EACE,4BCMY;AjB67ElB;;AgBp8EI;EACE,4BCMY;AjBi8ElB;;AgBx8EI;EACE,4BCMY;AjBq8ElB;;AgBj9EI;EACE,yBXiByB;ALm8E/B;;AgBr9EI;EACE,wCXiByB;ALu8E/B;;AgBz9EI;EACE,wCXiByB;AL28E/B;;AgB79EI;EACE,wCXiByB;AL+8E/B;;AgBj+EI;EACE,wCXiByB;ALm9E/B;;AgBr+EI;EACE,wCXiByB;ALu9E/B;;AgBz+EI;EACE,wCXiByB;AL29E/B;;AgB7+EI;EACE,wCXiByB;AL+9E/B;;AgBj/EI;EACE,wCXiByB;ALm+E/B;;AgBr/EI;EACE,wCXiByB;ALu+E/B;;AgBp/EI;EACE,cXYyB;AL2+E/B;;AgBx/EI;EACE,6BXYyB;AL++E/B;;AgB5/EI;EACE,6BXYyB;ALm/E/B;;AgBhgFI;EACE,6BXYyB;ALu/E/B;;AgBpgFI;EACE,6BXYyB;AL2/E/B;;AgBxgFI;EACE,6BXYyB;AL+/E/B;;AgB5gFI;EACE,6BXYyB;ALmgF/B;;AgBhhFI;EACE,6BXYyB;ALugF/B;;AgBphFI;EACE,6BXYyB;AL2gF/B;;AgBxhFI;EACE,6BXYyB;AL+gF/B;;AgBjiFI;EACE,yBXoByB;ALghF/B;;AgBriFI;EACE,0CXoByB;ALohF/B;;AgBziFI;EACE,0CXoByB;ALwhF/B;;AgB7iFI;EACE,0CXoByB;AL4hF/B;;AgBjjFI;EACE,0CXoByB;ALgiF/B;;AgBrjFI;EACE,0CXoByB;ALoiF/B;;AgBzjFI;EACE,0CXoByB;ALwiF/B;;AgB7jFI;EACE,0CXoByB;AL4iF/B;;AgBjkFI;EACE,0CXoByB;ALgjF/B;;AgBrkFI;EACE,0CXoByB;ALojF/B;;AgBpkFI;EACE,cXeyB;ALwjF/B;;AgBxkFI;EACE,+BXeyB;AL4jF/B;;AgB5kFI;EACE,+BXeyB;ALgkF/B;;AgBhlFI;EACE,+BXeyB;ALokF/B;;AgBplFI;EACE,+BXeyB;ALwkF/B;;AgBxlFI;EACE,+BXeyB;AL4kF/B;;AgB5lFI;EACE,+BXeyB;ALglF/B;;AgBhmFI;EACE,+BXeyB;ALolF/B;;AgBpmFI;EACE,+BXeyB;ALwlF/B;;AgBxmFI;EACE,+BXeyB;AL4lF/B;;AgBjnFI;EACE,yBXqByB;AL+lF/B;;AgBrnFI;EACE,yCXqByB;ALmmF/B;;AgBznFI;EACE,yCXqByB;ALumF/B;;AgB7nFI;EACE,yCXqByB;AL2mF/B;;AgBjoFI;EACE,yCXqByB;AL+mF/B;;AgBroFI;EACE,yCXqByB;ALmnF/B;;AgBzoFI;EACE,yCXqByB;ALunF/B;;AgB7oFI;EACE,yCXqByB;AL2nF/B;;AgBjpFI;EACE,yCXqByB;AL+nF/B;;AgBrpFI;EACE,yCXqByB;ALmoF/B;;AgBppFI;EACE,cXgByB;ALuoF/B;;AgBxpFI;EACE,8BXgByB;AL2oF/B;;AgB5pFI;EACE,8BXgByB;AL+oF/B;;AgBhqFI;EACE,8BXgByB;ALmpF/B;;AgBpqFI;EACE,8BXgByB;ALupF/B;;AgBxqFI;EACE,8BXgByB;AL2pF/B;;AgB5qFI;EACE,8BXgByB;AL+pF/B;;AgBhrFI;EACE,8BXgByB;ALmqF/B;;AgBprFI;EACE,8BXgByB;ALuqF/B;;AgBxrFI;EACE,8BXgByB;AL2qF/B;;AgBjsFI;EACE,yBXkByB;ALkrF/B;;AgBrsFI;EACE,wCXkByB;ALsrF/B;;AgBzsFI;EACE,wCXkByB;AL0rF/B;;AgB7sFI;EACE,wCXkByB;AL8rF/B;;AgBjtFI;EACE,wCXkByB;ALksF/B;;AgBrtFI;EACE,wCXkByB;ALssF/B;;AgBztFI;EACE,wCXkByB;AL0sF/B;;AgB7tFI;EACE,wCXkByB;AL8sF/B;;AgBjuFI;EACE,wCXkByB;ALktF/B;;AgBruFI;EACE,wCXkByB;ALstF/B;;AgBpuFI;EACE,cXayB;AL0tF/B;;AgBxuFI;EACE,6BXayB;AL8tF/B;;AgB5uFI;EACE,6BXayB;ALkuF/B;;AgBhvFI;EACE,6BXayB;ALsuF/B;;AgBpvFI;EACE,6BXayB;AL0uF/B;;AgBxvFI;EACE,6BXayB;AL8uF/B;;AgB5vFI;EACE,6BXayB;ALkvF/B;;AgBhwFI;EACE,6BXayB;ALsvF/B;;AgBpwFI;EACE,6BXayB;AL0vF/B;;AgBxwFI;EACE,6BXayB;AL8vF/B;;AgBjxFI;EACE,yBXmByB;ALiwF/B;;AgBrxFI;EACE,yCXmByB;ALqwF/B;;AgBzxFI;EACE,yCXmByB;ALywF/B;;AgB7xFI;EACE,yCXmByB;AL6wF/B;;AgBjyFI;EACE,yCXmByB;ALixF/B;;AgBryFI;EACE,yCXmByB;ALqxF/B;;AgBzyFI;EACE,yCXmByB;ALyxF/B;;AgB7yFI;EACE,yCXmByB;AL6xF/B;;AgBjzFI;EACE,yCXmByB;ALiyF/B;;AgBrzFI;EACE,yCXmByB;ALqyF/B;;AgBpzFI;EACE,cXcyB;ALyyF/B;;AgBxzFI;EACE,8BXcyB;AL6yF/B;;AgB5zFI;EACE,8BXcyB;ALizF/B;;AgBh0FI;EACE,8BXcyB;ALqzF/B;;AgBp0FI;EACE,8BXcyB;ALyzF/B;;AgBx0FI;EACE,8BXcyB;AL6zF/B;;AgB50FI;EACE,8BXcyB;ALi0F/B;;AgBh1FI;EACE,8BXcyB;ALq0F/B;;AgBp1FI;EACE,8BXcyB;ALy0F/B;;AgBx1FI;EACE,8BXcyB;AL60F/B;;AgBj2FI;EACE,yBX0BU;AL00FhB;;AgBr2FI;EACE,0CX0BU;AL80FhB;;AgBz2FI;EACE,0CX0BU;ALk1FhB;;AgB72FI;EACE,0CX0BU;ALs1FhB;;AgBj3FI;EACE,0CX0BU;AL01FhB;;AgBr3FI;EACE,0CX0BU;AL81FhB;;AgBz3FI;EACE,0CX0BU;ALk2FhB;;AgB73FI;EACE,0CX0BU;ALs2FhB;;AgBj4FI;EACE,0CX0BU;AL02FhB;;AgBr4FI;EACE,0CX0BU;AL82FhB;;AgBp4FI;EACE,cXqBU;ALk3FhB;;AgBx4FI;EACE,+BXqBU;ALs3FhB;;AgB54FI;EACE,+BXqBU;AL03FhB;;AgBh5FI;EACE,+BXqBU;AL83FhB;;AgBp5FI;EACE,+BXqBU;ALk4FhB;;AgBx5FI;EACE,+BXqBU;ALs4FhB;;AgB55FI;EACE,+BXqBU;AL04FhB;;AgBh6FI;EACE,+BXqBU;AL84FhB;;AgBp6FI;EACE,+BXqBU;ALk5FhB;;AgBx6FI;EACE,+BXqBU;ALs5FhB;;AgBj7FI;EACE,yBX2BiB;ALy5FvB;;AgBr7FI;EACE,yCX2BiB;AL65FvB;;AgBz7FI;EACE,yCX2BiB;ALi6FvB;;AgB77FI;EACE,yCX2BiB;ALq6FvB;;AgBj8FI;EACE,yCX2BiB;ALy6FvB;;AgBr8FI;EACE,yCX2BiB;AL66FvB;;AgBz8FI;EACE,yCX2BiB;ALi7FvB;;AgB78FI;EACE,yCX2BiB;ALq7FvB;;AgBj9FI;EACE,yCX2BiB;ALy7FvB;;AgBr9FI;EACE,yCX2BiB;AL67FvB;;AgBp9FI;EACE,cXsBiB;ALi8FvB;;AgBx9FI;EACE,8BXsBiB;ALq8FvB;;AgB59FI;EACE,8BXsBiB;ALy8FvB;;AgBh+FI;EACE,8BXsBiB;AL68FvB;;AgBp+FI;EACE,8BXsBiB;ALi9FvB;;AgBx+FI;EACE,8BXsBiB;ALq9FvB;;AgB5+FI;EACE,8BXsBiB;ALy9FvB;;AgBh/FI;EACE,8BXsBiB;AL69FvB;;AgBp/FI;EACE,8BXsBiB;ALi+FvB;;AgBx/FI;EACE,8BXsBiB;ALq+FvB;;AgBjgGI;EACE,yBXsByB;AL8+F/B;;AgBrgGI;EACE,uCXsByB;ALk/F/B;;AgBzgGI;EACE,uCXsByB;ALs/F/B;;AgB7gGI;EACE,uCXsByB;AL0/F/B;;AgBjhGI;EACE,uCXsByB;AL8/F/B;;AgBrhGI;EACE,uCXsByB;ALkgG/B;;AgBzhGI;EACE,uCXsByB;ALsgG/B;;AgB7hGI;EACE,uCXsByB;AL0gG/B;;AgBjiGI;EACE,uCXsByB;AL8gG/B;;AgBriGI;EACE,uCXsByB;ALkhG/B;;AgBpiGI;EACE,cXiByB;ALshG/B;;AgBxiGI;EACE,4BXiByB;AL0hG/B;;AgB5iGI;EACE,4BXiByB;AL8hG/B;;AgBhjGI;EACE,4BXiByB;ALkiG/B;;AgBpjGI;EACE,4BXiByB;ALsiG/B;;AgBxjGI;EACE,4BXiByB;AL0iG/B;;AgB5jGI;EACE,4BXiByB;AL8iG/B;;AgBhkGI;EACE,4BXiByB;ALkjG/B;;AgBpkGI;EACE,4BXiByB;ALsjG/B;;AgBxkGI;EACE,4BXiByB;AL0jG/B;;AgBjlGI;EACE,uBXaS;ALukGf;;AgBrlGI;EACE,oCXaS;AL2kGf;;AgBzlGI;EACE,oCXaS;AL+kGf;;AgB7lGI;EACE,oCXaS;ALmlGf;;AgBjmGI;EACE,oCXaS;ALulGf;;AgBrmGI;EACE,oCXaS;AL2lGf;;AgBzmGI;EACE,oCXaS;AL+lGf;;AgB7mGI;EACE,oCXaS;ALmmGf;;AgBjnGI;EACE,oCXaS;ALumGf;;AgBrnGI;EACE,oCXaS;AL2mGf;;AgBpnGI;EACE,YXQS;AL+mGf;;AgBxnGI;EACE,yBXQS;ALmnGf;;AgB5nGI;EACE,yBXQS;ALunGf;;AgBhoGI;EACE,yBXQS;AL2nGf;;AgBpoGI;EACE,yBXQS;AL+nGf;;AgBxoGI;EACE,yBXQS;ALmoGf;;AgB5oGI;EACE,yBXQS;ALuoGf;;AgBhpGI;EACE,yBXQS;AL2oGf;;AgBppGI;EACE,yBXQS;AL+oGf;;AgBxpGI;EACE,yBXQS;ALmpGf;;AgBjqGI;EACE,yBXwByB;AL4oG/B;;AgBrqGI;EACE,uCXwByB;ALgpG/B;;AgBzqGI;EACE,uCXwByB;ALopG/B;;AgB7qGI;EACE,uCXwByB;ALwpG/B;;AgBjrGI;EACE,uCXwByB;AL4pG/B;;AgBrrGI;EACE,uCXwByB;ALgqG/B;;AgBzrGI;EACE,uCXwByB;ALoqG/B;;AgB7rGI;EACE,uCXwByB;ALwqG/B;;AgBjsGI;EACE,uCXwByB;AL4qG/B;;AgBrsGI;EACE,uCXwByB;ALgrG/B;;AgBpsGI;EACE,cXmByB;ALorG/B;;AgBxsGI;EACE,4BXmByB;ALwrG/B;;AgB5sGI;EACE,4BXmByB;AL4rG/B;;AgBhtGI;EACE,4BXmByB;ALgsG/B;;AgBptGI;EACE,4BXmByB;ALosG/B;;AgBxtGI;EACE,4BXmByB;ALwsG/B;;AgB5tGI;EACE,4BXmByB;AL4sG/B;;AgBhuGI;EACE,4BXmByB;ALgtG/B;;AgBpuGI;EACE,4BXmByB;ALotG/B;;AgBxuGI;EACE,4BXmByB;ALwtG/B;;AgBjvGI;EACE,yBXyByB;AL2tG/B;;AgBrvGI;EACE,uCXyByB;AL+tG/B;;AgBzvGI;EACE,uCXyByB;ALmuG/B;;AgB7vGI;EACE,uCXyByB;ALuuG/B;;AgBjwGI;EACE,uCXyByB;AL2uG/B;;AgBrwGI;EACE,uCXyByB;AL+uG/B;;AgBzwGI;EACE,uCXyByB;ALmvG/B;;AgB7wGI;EACE,uCXyByB;ALuvG/B;;AgBjxGI;EACE,uCXyByB;AL2vG/B;;AgBrxGI;EACE,uCXyByB;AL+vG/B;;AgBpxGI;EACE,cXoByB;ALmwG/B;;AgBxxGI;EACE,4BXoByB;ALuwG/B;;AgB5xGI;EACE,4BXoByB;AL2wG/B;;AgBhyGI;EACE,4BXoByB;AL+wG/B;;AgBpyGI;EACE,4BXoByB;ALmxG/B;;AgBxyGI;EACE,4BXoByB;ALuxG/B;;AgB5yGI;EACE,4BXoByB;AL2xG/B;;AgBhzGI;EACE,4BXoByB;AL+xG/B;;AgBpzGI;EACE,4BXoByB;ALmyG/B;;AgBxzGI;EACE,4BXoByB;ALuyG/B;;AgBj0GI;EACE,uBXHS;ALu0Gf;;AgBr0GI;EACE,0CXHS;AL20Gf;;AgBz0GI;EACE,0CXHS;AL+0Gf;;AgB70GI;EACE,0CXHS;ALm1Gf;;AgBj1GI;EACE,0CXHS;ALu1Gf;;AgBr1GI;EACE,0CXHS;AL21Gf;;AgBz1GI;EACE,0CXHS;AL+1Gf;;AgB71GI;EACE,0CXHS;ALm2Gf;;AgBj2GI;EACE,0CXHS;ALu2Gf;;AgBr2GI;EACE,0CXHS;AL22Gf;;AgBp2GI;EACE,YXRS;AL+2Gf;;AgBx2GI;EACE,+BXRS;ALm3Gf;;AgB52GI;EACE,+BXRS;ALu3Gf;;AgBh3GI;EACE,+BXRS;AL23Gf;;AgBp3GI;EACE,+BXRS;AL+3Gf;;AgBx3GI;EACE,+BXRS;ALm4Gf;;AgB53GI;EACE,+BXRS;ALu4Gf;;AgBh4GI;EACE,+BXRS;AL24Gf;;AgBp4GI;EACE,+BXRS;AL+4Gf;;AgBx4GI;EACE,+BXRS;ALm5Gf;;AgBj5GI;EACE,yBXAY;ALo5GlB;;AgBr5GI;EACE,0CXAY;ALw5GlB;;AgBz5GI;EACE,0CXAY;AL45GlB;;AgB75GI;EACE,0CXAY;ALg6GlB;;AgBj6GI;EACE,0CXAY;ALo6GlB;;AgBr6GI;EACE,0CXAY;ALw6GlB;;AgBz6GI;EACE,0CXAY;AL46GlB;;AgB76GI;EACE,0CXAY;ALg7GlB;;AgBj7GI;EACE,0CXAY;ALo7GlB;;AgBr7GI;EACE,0CXAY;ALw7GlB;;AgBp7GI;EACE,cXLY;AL47GlB;;AgBx7GI;EACE,+BXLY;ALg8GlB;;AgB57GI;EACE,+BXLY;ALo8GlB;;AgBh8GI;EACE,+BXLY;ALw8GlB;;AgBp8GI;EACE,+BXLY;AL48GlB;;AgBx8GI;EACE,+BXLY;ALg9GlB;;AgB58GI;EACE,+BXLY;ALo9GlB;;AgBh9GI;EACE,+BXLY;ALw9GlB;;AgBp9GI;EACE,+BXLY;AL49GlB;;AgBx9GI;EACE,+BXLY;ALg+GlB;;AgBj+GI;EACE,yBXMY;AL89GlB;;AgBr+GI;EACE,0CXMY;ALk+GlB;;AgBz+GI;EACE,0CXMY;ALs+GlB;;AgB7+GI;EACE,0CXMY;AL0+GlB;;AgBj/GI;EACE,0CXMY;AL8+GlB;;AgBr/GI;EACE,0CXMY;ALk/GlB;;AgBz/GI;EACE,0CXMY;ALs/GlB;;AgB7/GI;EACE,0CXMY;AL0/GlB;;AgBjgHI;EACE,0CXMY;AL8/GlB;;AgBrgHI;EACE,0CXMY;ALkgHlB;;AgBpgHI;EACE,cXCY;ALsgHlB;;AgBxgHI;EACE,+BXCY;AL0gHlB;;AgB5gHI;EACE,+BXCY;AL8gHlB;;AgBhhHI;EACE,+BXCY;ALkhHlB;;AgBphHI;EACE,+BXCY;ALshHlB;;AgBxhHI;EACE,+BXCY;AL0hHlB;;AgB5hHI;EACE,+BXCY;AL8hHlB;;AgBhiHI;EACE,+BXCY;ALkiHlB;;AgBpiHI;EACE,+BXCY;ALsiHlB;;AgBxiHI;EACE,+BXCY;AL0iHlB;;AgBjjHI;EACE,yBXFY;ALsjHlB;;AgBrjHI;EACE,0CXFY;AL0jHlB;;AgBzjHI;EACE,0CXFY;AL8jHlB;;AgB7jHI;EACE,0CXFY;ALkkHlB;;AgBjkHI;EACE,0CXFY;ALskHlB;;AgBrkHI;EACE,0CXFY;AL0kHlB;;AgBzkHI;EACE,0CXFY;AL8kHlB;;AgB7kHI;EACE,0CXFY;ALklHlB;;AgBjlHI;EACE,0CXFY;ALslHlB;;AgBrlHI;EACE,0CXFY;AL0lHlB;;AgBplHI;EACE,cXPY;AL8lHlB;;AgBxlHI;EACE,+BXPY;ALkmHlB;;AgB5lHI;EACE,+BXPY;ALsmHlB;;AgBhmHI;EACE,+BXPY;AL0mHlB;;AgBpmHI;EACE,+BXPY;AL8mHlB;;AgBxmHI;EACE,+BXPY;ALknHlB;;AgB5mHI;EACE,+BXPY;ALsnHlB;;AgBhnHI;EACE,+BXPY;AL0nHlB;;AgBpnHI;EACE,+BXPY;AL8nHlB;;AgBxnHI;EACE,+BXPY;ALkoHlB;;AgBjoHI;EACE,yBXCY;ALmoHlB;;AgBroHI;EACE,0CXCY;ALuoHlB;;AgBzoHI;EACE,0CXCY;AL2oHlB;;AgB7oHI;EACE,0CXCY;AL+oHlB;;AgBjpHI;EACE,0CXCY;ALmpHlB;;AgBrpHI;EACE,0CXCY;ALupHlB;;AgBzpHI;EACE,0CXCY;AL2pHlB;;AgB7pHI;EACE,0CXCY;AL+pHlB;;AgBjqHI;EACE,0CXCY;ALmqHlB;;AgBrqHI;EACE,0CXCY;ALuqHlB;;AgBpqHI;EACE,cXJY;AL2qHlB;;AgBxqHI;EACE,+BXJY;AL+qHlB;;AgB5qHI;EACE,+BXJY;ALmrHlB;;AgBhrHI;EACE,+BXJY;ALurHlB;;AgBprHI;EACE,+BXJY;AL2rHlB;;AgBxrHI;EACE,+BXJY;AL+rHlB;;AgB5rHI;EACE,+BXJY;ALmsHlB;;AgBhsHI;EACE,+BXJY;ALusHlB;;AgBpsHI;EACE,+BXJY;AL2sHlB;;AgBxsHI;EACE,+BXJY;AL+sHlB;;AgBjtHI;EACE,yBXEY;ALktHlB;;AgBrtHI;EACE,0CXEY;ALstHlB;;AgBztHI;EACE,0CXEY;AL0tHlB;;AgB7tHI;EACE,0CXEY;AL8tHlB;;AgBjuHI;EACE,0CXEY;ALkuHlB;;AgBruHI;EACE,0CXEY;ALsuHlB;;AgBzuHI;EACE,0CXEY;AL0uHlB;;AgB7uHI;EACE,0CXEY;AL8uHlB;;AgBjvHI;EACE,0CXEY;ALkvHlB;;AgBrvHI;EACE,0CXEY;ALsvHlB;;AgBpvHI;EACE,cXHY;AL0vHlB;;AgBxvHI;EACE,+BXHY;AL8vHlB;;AgB5vHI;EACE,+BXHY;ALkwHlB;;AgBhwHI;EACE,+BXHY;ALswHlB;;AgBpwHI;EACE,+BXHY;AL0wHlB;;AgBxwHI;EACE,+BXHY;AL8wHlB;;AgB5wHI;EACE,+BXHY;ALkxHlB;;AgBhxHI;EACE,+BXHY;ALsxHlB;;AgBpxHI;EACE,+BXHY;AL0xHlB;;AgBxxHI;EACE,+BXHY;AL8xHlB;;AgBjyHI;EACE,yBXGY;ALiyHlB;;AgBryHI;EACE,0CXGY;ALqyHlB;;AgBzyHI;EACE,0CXGY;ALyyHlB;;AgB7yHI;EACE,0CXGY;AL6yHlB;;AgBjzHI;EACE,0CXGY;ALizHlB;;AgBrzHI;EACE,0CXGY;ALqzHlB;;AgBzzHI;EACE,0CXGY;ALyzHlB;;AgB7zHI;EACE,0CXGY;AL6zHlB;;AgBj0HI;EACE,0CXGY;ALi0HlB;;AgBr0HI;EACE,0CXGY;ALq0HlB;;AgBp0HI;EACE,cXFY;ALy0HlB;;AgBx0HI;EACE,+BXFY;AL60HlB;;AgB50HI;EACE,+BXFY;ALi1HlB;;AgBh1HI;EACE,+BXFY;ALq1HlB;;AgBp1HI;EACE,+BXFY;ALy1HlB;;AgBx1HI;EACE,+BXFY;AL61HlB;;AgB51HI;EACE,+BXFY;ALi2HlB;;AgBh2HI;EACE,+BXFY;ALq2HlB;;AgBp2HI;EACE,+BXFY;ALy2HlB;;AgBx2HI;EACE,+BXFY;AL62HlB;;AgBj3HI;EACE,yBXKY;AL+2HlB;;AgBr3HI;EACE,0CXKY;ALm3HlB;;AgBz3HI;EACE,0CXKY;ALu3HlB;;AgB73HI;EACE,0CXKY;AL23HlB;;AgBj4HI;EACE,0CXKY;AL+3HlB;;AgBr4HI;EACE,0CXKY;ALm4HlB;;AgBz4HI;EACE,0CXKY;ALu4HlB;;AgB74HI;EACE,0CXKY;AL24HlB;;AgBj5HI;EACE,0CXKY;AL+4HlB;;AgBr5HI;EACE,0CXKY;ALm5HlB;;AgBp5HI;EACE,cXAY;ALu5HlB;;AgBx5HI;EACE,+BXAY;AL25HlB;;AgB55HI;EACE,+BXAY;AL+5HlB;;AgBh6HI;EACE,+BXAY;ALm6HlB;;AgBp6HI;EACE,+BXAY;ALu6HlB;;AgBx6HI;EACE,+BXAY;AL26HlB;;AgB56HI;EACE,+BXAY;AL+6HlB;;AgBh7HI;EACE,+BXAY;ALm7HlB;;AgBp7HI;EACE,+BXAY;ALu7HlB;;AgBx7HI;EACE,+BXAY;AL27HlB;;AgBj8HI;EACE,yBXIoC;ALg8H1C;;AgBr8HI;EACE,0CXIoC;ALo8H1C;;AgBz8HI;EACE,0CXIoC;ALw8H1C;;AgB78HI;EACE,0CXIoC;AL48H1C;;AgBj9HI;EACE,0CXIoC;ALg9H1C;;AgBr9HI;EACE,0CXIoC;ALo9H1C;;AgBz9HI;EACE,0CXIoC;ALw9H1C;;AgB79HI;EACE,0CXIoC;AL49H1C;;AgBj+HI;EACE,0CXIoC;ALg+H1C;;AgBr+HI;EACE,0CXIoC;ALo+H1C;;AgBp+HI;EACE,cXDoC;ALw+H1C;;AgBx+HI;EACE,+BXDoC;AL4+H1C;;AgB5+HI;EACE,+BXDoC;ALg/H1C;;AgBh/HI;EACE,+BXDoC;ALo/H1C;;AgBp/HI;EACE,+BXDoC;ALw/H1C;;AgBx/HI;EACE,+BXDoC;AL4/H1C;;AgB5/HI;EACE,+BXDoC;ALggI1C;;AgBhgII;EACE,+BXDoC;ALogI1C;;AgBpgII;EACE,+BXDoC;ALwgI1C;;AgBxgII;EACE,+BXDoC;AL4gI1C;;AgBjhII;EACE,yBXuB8B;AL6/HpC;;AgBrhII;EACE,uCXuB8B;ALigIpC;;AgBzhII;EACE,uCXuB8B;ALqgIpC;;AgB7hII;EACE,uCXuB8B;ALygIpC;;AgBjiII;EACE,uCXuB8B;AL6gIpC;;AgBriII;EACE,uCXuB8B;ALihIpC;;AgBziII;EACE,uCXuB8B;ALqhIpC;;AgB7iII;EACE,uCXuB8B;ALyhIpC;;AgBjjII;EACE,uCXuB8B;AL6hIpC;;AgBrjII;EACE,uCXuB8B;ALiiIpC;;AgBpjII;EACE,cXkB8B;ALqiIpC;;AgBxjII;EACE,4BXkB8B;ALyiIpC;;AgB5jII;EACE,4BXkB8B;AL6iIpC;;AgBhkII;EACE,4BXkB8B;ALijIpC;;AgBpkII;EACE,4BXkB8B;ALqjIpC;;AgBxkII;EACE,4BXkB8B;ALyjIpC;;AgB5kII;EACE,4BXkB8B;AL6jIpC;;AgBhlII;EACE,4BXkB8B;ALikIpC;;AgBplII;EACE,4BXkB8B;ALqkIpC;;AgBxlII;EACE,4BXkB8B;ALykIpC;;AgBjmII;EACE,yBE0CkB;AlB0jIxB;;AgBrmII;EACE,0CE0CkB;AlB8jIxB;;AgBzmII;EACE,0CE0CkB;AlBkkIxB;;AgB7mII;EACE,0CE0CkB;AlBskIxB;;AgBjnII;EACE,0CE0CkB;AlB0kIxB;;AgBrnII;EACE,0CE0CkB;AlB8kIxB;;AgBznII;EACE,0CE0CkB;AlBklIxB;;AgB7nII;EACE,0CE0CkB;AlBslIxB;;AgBjoII;EACE,0CE0CkB;AlB0lIxB;;AgBroII;EACE,0CE0CkB;AlB8lIxB;;AgBpoII;EACE,cEqCkB;AlBkmIxB;;AgBxoII;EACE,+BEqCkB;AlBsmIxB;;AgB5oII;EACE,+BEqCkB;AlB0mIxB;;AgBhpII;EACE,+BEqCkB;AlB8mIxB;;AgBppII;EACE,+BEqCkB;AlBknIxB;;AgBxpII;EACE,+BEqCkB;AlBsnIxB;;AgB5pII;EACE,+BEqCkB;AlB0nIxB;;AgBhqII;EACE,+BEqCkB;AlB8nIxB;;AgBpqII;EACE,+BEqCkB;AlBkoIxB;;AgBxqII;EACE,+BEqCkB;AlBsoIxB;;AgBrqIE;EACE,UE0CW;AlB8nIf;;AgBzqIE;EACE,YE2CM;AlBioIV;;AgB7qIE;EACE,YE4CM;AlBooIV;;AgBjrIE;EACE,YE6CM;AlBuoIV;;AgBrrIE;EACE,YE8CM;AlB0oIV;;AgBzrIE;EACE,YE+CM;AlB6oIV;;AgB7rIE;EACE,YEgDM;AlBgpIV;;AgBjsIE;EACE,YEiDM;AlBmpIV;;AgBrsIE;EACE,YEkDM;AlBspIV;;AgBzsIE;EACE,YEmDM;AlBypIV;;AgBzsIA;EACE,oCAAmC;AhB4sIrC;;AgB1sIA;EACE,iCAAiC;AhB6sInC;;AgB3sIA;EACE,mCAAkC;AhB8sIpC;;AgB3sIA;EACE,qBAAqB;AhB8sIvB;;AgB7sIE;EACE,qBAAqB;AhBgtIzB;;AgB9sIE;EACE,uBAAuB;AhBitI3B;;AgB/sIE;EACE,uBAAuB;AhBktI3B;;AgBhtIE;EACE,uBAAuB;AhBmtI3B;;AgBjtIE;EACE,uBAAuB;AhBotI3B;;AgBltIE;EACE,uBAAuB;AhBqtI3B;;AgBntIE;EACE,uBAAuB;AhBstI3B;;AgBptIE;EACE,uBAAuB;AhButI3B;;AgBntIA;EACE,iBAAgB;AhBstIlB;;AgBntIA;EACE,8BAA8B;AhBstIhC;;AmBnxIA;EACE,aAAa;EACb,mBAAmB;EACnB,uBAAuB;AnBsxIzB;;AmBlxIA;EACE,aAAa;EACb,mBAAmB;AnBqxIrB;;AClwII;EmB7BJ;IVaE,aAAa;IACb,eAAe;IACf,mBAA0B;IAC1B,kBAAyB;EVuxIzB;AACF;;AC1vII;EmBzCJ;IVQE,aAAa;IACb,eAAe;IACf,mBAA0B;IAC1B,kBAAyB;EVgyIzB;AACF;;ACzwII;EmB9BJ;IVGE,aAAa;IACb,eAAe;IACf,mBAA0B;IAC1B,kBAAyB;EVyyIzB;AACF;;AC3vII;EmBhDJ;IVFE,aAAa;IACb,eAAe;IACf,mBAA0B;IAC1B,kBAAyB;EVkzIzB;AACF;;AqBl0IA,6BAAA;AAEE;EACI,oDCJ8C;AtBw0IpD;;AqBr0IE;EACI,iDCH2C;AtB20IjD;;AqBz0IE;EACI,mDCF6C;AtB80InD;;AqB70IE;EACI,8CCDwC;AtBi1I9C;;AqBj1IE;EACI,mDCA6C;AtBo1InD;;AqBr1IE;EACI,8CCCwC;AtBu1I9C;;AqBz1IE;EACI,oDCE8C;AtB01IpD;;AqB71IE;EACI,2CCGqC;AtB61I3C;;AqBj2IE;EACI,2CCIqC;AtBg2I3C;;AqBr2IE;EACI,2CCKsC;AtBm2I5C;;AqBz2IE;EACI,8CCM2C;AtBs2IjD;;AqB72IE;EACI,8CCO2C;AtBy2IjD;;AqBj3IE;EACI,gDCQ8C;AtB42IpD;;AuBx3IA,2BAAA;AAEE;EACE,eAAW;EACX,eAAW;EACX,gBAAY;EACZ,gBAAY;EACZ,oBAAoB;EACpB,aAAa;EACb,mBAAmB;EACnB,uBAAuB;AvB03I3B;;AuBl4IE;EACE,eAAW;EACX,eAAW;EACX,gBAAY;EACZ,gBAAY;EACZ,oBAAoB;EACpB,aAAa;EACb,mBAAmB;EACnB,uBAAuB;AvBq4I3B;;AuB74IE;EACE,eAAW;EACX,eAAW;EACX,gBAAY;EACZ,gBAAY;EACZ,oBAAoB;EACpB,aAAa;EACb,mBAAmB;EACnB,uBAAuB;EAErB,eAAe;AvB+4IrB;;AuBz5IE;EACE,eAAW;EACX,eAAW;EACX,gBAAY;EACZ,gBAAY;EACZ,oBAAoB;EACpB,aAAa;EACb,mBAAmB;EACnB,uBAAuB;AvB45I3B;;AuBp6IE;EACE,eAAW;EACX,eAAW;EACX,gBAAY;EACZ,gBAAY;EACZ,oBAAoB;EACpB,aAAa;EACb,mBAAmB;EACnB,uBAAuB;AvBu6I3B;;AuB/6IE;EACE,eAAW;EACX,eAAW;EACX,gBAAY;EACZ,gBAAY;EACZ,oBAAoB;EACpB,aAAa;EACb,mBAAmB;EACnB,uBAAuB;EAIrB,eAAe;AvB+6IrB;;AuB37IE;EACE,eAAW;EACX,eAAW;EACX,gBAAY;EACZ,gBAAY;EACZ,oBAAoB;EACpB,aAAa;EACb,mBAAmB;EACnB,uBAAuB;EAMrB,eAAe;AvBy7IrB;;AuBv8IE;EACE,eAAW;EACX,eAAW;EACX,gBAAY;EACZ,gBAAY;EACZ,oBAAoB;EACpB,aAAa;EACb,mBAAmB;EACnB,uBAAuB;AvB08I3B;;AuBl9IE;EACE,gBAAW;EACX,gBAAW;EACX,iBAAY;EACZ,iBAAY;EACZ,oBAAoB;EACpB,aAAa;EACb,mBAAmB;EACnB,uBAAuB;AvBq9I3B;;AuB79IE;EACE,gBAAW;EACX,gBAAW;EACX,iBAAY;EACZ,iBAAY;EACZ,oBAAoB;EACpB,aAAa;EACb,mBAAmB;EACnB,uBAAuB;AvBg+I3B;;AuBx+IE;EACE,eAAW;EACX,eAAW;EACX,gBAAY;EACZ,gBAAY;EACZ,oBAAoB;EACpB,aAAa;EACb,mBAAmB;EACnB,uBAAuB;AvB2+I3B;;AuBn/IE;EACE,eAAW;EACX,eAAW;EACX,gBAAY;EACZ,gBAAY;EACZ,oBAAoB;EACpB,aAAa;EACb,mBAAmB;EACnB,uBAAuB;AvBs/I3B;;AuB9/IE;EACE,eAAW;EACX,eAAW;EACX,gBAAY;EACZ,gBAAY;EACZ,oBAAoB;EACpB,aAAa;EACb,mBAAmB;EACnB,uBAAuB;AvBigJ3B;;AuBzgJE;EACE,eAAW;EACX,eAAW;EACX,gBAAY;EACZ,gBAAY;EACZ,oBAAoB;EACpB,aAAa;EACb,mBAAmB;EACnB,uBAAuB;AvB4gJ3B;;AuBphJE;EACE,eAAW;EACX,eAAW;EACX,gBAAY;EACZ,gBAAY;EACZ,oBAAoB;EACpB,aAAa;EACb,mBAAmB;EACnB,uBAAuB;AvBuhJ3B;;AuB/hJE;EACE,eAAW;EACX,eAAW;EACX,gBAAY;EACZ,gBAAY;EACZ,oBAAoB;EACpB,aAAa;EACb,mBAAmB;EACnB,uBAAuB;AvBkiJ3B;;AuB1iJE;EACE,eAAW;EACX,eAAW;EACX,gBAAY;EACZ,gBAAY;EACZ,oBAAoB;EACpB,aAAa;EACb,mBAAmB;EACnB,uBAAuB;AvB6iJ3B;;AuBrjJE;EACE,eAAW;EACX,eAAW;EACX,gBAAY;EACZ,gBAAY;EACZ,oBAAoB;EACpB,aAAa;EACb,mBAAmB;EACnB,uBAAuB;AvBwjJ3B;;AuBhkJE;EACE,gBAAW;EACX,gBAAW;EACX,iBAAY;EACZ,iBAAY;EACZ,oBAAoB;EACpB,aAAa;EACb,mBAAmB;EACnB,uBAAuB;AvBmkJ3B;;AuBxjJA,2BAAA;AAEE;EACE,eAAW;EACX,eAAW;EACX,gBAAY;EACZ,gBAAY;EACZ,aAAa;EACb,mBAAmB;EACnB,uBAAuB;AvB0jJ3B;;AuBjkJE;EACE,eAAW;EACX,eAAW;EACX,gBAAY;EACZ,gBAAY;EACZ,aAAa;EACb,mBAAmB;EACnB,uBAAuB;AvBokJ3B;;AuB9jJE,+BAAA;ACwBE;EDjBQ;IAAgC,qBAA4B;EvB6jJtE;EuB7jJU;IAAgC,qBAA4B;EvBgkJtE;EuBhkJU;IAAgC,qBAA4B;EvBmkJtE;EuBnkJU;IAAgC,sBAA4B;EvBskJtE;EuBtkJU;IAAgC,sBAA4B;EvBykJtE;EuBzkJU;IAAgC,qBAA4B;EvB4kJtE;EuB5kJU;IAAgC,qBAA4B;EvB+kJtE;EuB/kJU;IAAgC,qBAA4B;EvBklJtE;EuBllJU;IAAgC,qBAA4B;EvBqlJtE;EuBrlJU;IAAgC,qBAA4B;EvBwlJtE;EuBxlJU;IAAgC,qBAA4B;EvB2lJtE;EuB3lJU;IAAgC,qBAA4B;EvB8lJtE;EuB9lJU;IAAgC,qBAA4B;EvBimJtE;EuBjmJU;IAAgC,qBAA4B;EvBomJtE;EuBpmJU;IAAgC,qBAA4B;EvBumJtE;EuBvmJU;IAAgC,qBAA4B;EvB0mJtE;EuB1mJU;IAAgC,qBAA4B;EvB6mJtE;AACF;;AwB7lJI;EDjBQ;IAAgC,qBAA4B;EvBmnJtE;EuBnnJU;IAAgC,qBAA4B;EvBsnJtE;EuBtnJU;IAAgC,qBAA4B;EvBynJtE;EuBznJU;IAAgC,sBAA4B;EvB4nJtE;EuB5nJU;IAAgC,sBAA4B;EvB+nJtE;EuB/nJU;IAAgC,qBAA4B;EvBkoJtE;EuBloJU;IAAgC,qBAA4B;EvBqoJtE;EuBroJU;IAAgC,qBAA4B;EvBwoJtE;EuBxoJU;IAAgC,qBAA4B;EvB2oJtE;EuB3oJU;IAAgC,qBAA4B;EvB8oJtE;EuB9oJU;IAAgC,qBAA4B;EvBipJtE;EuBjpJU;IAAgC,qBAA4B;EvBopJtE;EuBppJU;IAAgC,qBAA4B;EvBupJtE;EuBvpJU;IAAgC,qBAA4B;EvB0pJtE;EuB1pJU;IAAgC,qBAA4B;EvB6pJtE;EuB7pJU;IAAgC,qBAA4B;EvBgqJtE;EuBhqJU;IAAgC,qBAA4B;EvBmqJtE;AACF;;AwBnpJI;EDjBQ;IAAgC,qBAA4B;EvByqJtE;EuBzqJU;IAAgC,qBAA4B;EvB4qJtE;EuB5qJU;IAAgC,qBAA4B;EvB+qJtE;EuB/qJU;IAAgC,sBAA4B;EvBkrJtE;EuBlrJU;IAAgC,sBAA4B;EvBqrJtE;EuBrrJU;IAAgC,qBAA4B;EvBwrJtE;EuBxrJU;IAAgC,qBAA4B;EvB2rJtE;EuB3rJU;IAAgC,qBAA4B;EvB8rJtE;EuB9rJU;IAAgC,qBAA4B;EvBisJtE;EuBjsJU;IAAgC,qBAA4B;EvBosJtE;EuBpsJU;IAAgC,qBAA4B;EvBusJtE;EuBvsJU;IAAgC,qBAA4B;EvB0sJtE;EuB1sJU;IAAgC,qBAA4B;EvB6sJtE;EuB7sJU;IAAgC,qBAA4B;EvBgtJtE;EuBhtJU;IAAgC,qBAA4B;EvBmtJtE;EuBntJU;IAAgC,qBAA4B;EvBstJtE;EuBttJU;IAAgC,qBAA4B;EvBytJtE;AACF;;AwBzsJI;EDjBQ;IAAgC,qBAA4B;EvB+tJtE;EuB/tJU;IAAgC,qBAA4B;EvBkuJtE;EuBluJU;IAAgC,qBAA4B;EvBquJtE;EuBruJU;IAAgC,sBAA4B;EvBwuJtE;EuBxuJU;IAAgC,sBAA4B;EvB2uJtE;EuB3uJU;IAAgC,qBAA4B;EvB8uJtE;EuB9uJU;IAAgC,qBAA4B;EvBivJtE;EuBjvJU;IAAgC,qBAA4B;EvBovJtE;EuBpvJU;IAAgC,qBAA4B;EvBuvJtE;EuBvvJU;IAAgC,qBAA4B;EvB0vJtE;EuB1vJU;IAAgC,qBAA4B;EvB6vJtE;EuB7vJU;IAAgC,qBAA4B;EvBgwJtE;EuBhwJU;IAAgC,qBAA4B;EvBmwJtE;EuBnwJU;IAAgC,qBAA4B;EvBswJtE;EuBtwJU;IAAgC,qBAA4B;EvBywJtE;EuBzwJU;IAAgC,qBAA4B;EvB4wJtE;EuB5wJU;IAAgC,qBAA4B;EvB+wJtE;AACF;;AwB/vJI;EDjBQ;IAAgC,qBAA4B;EvBqxJtE;EuBrxJU;IAAgC,qBAA4B;EvBwxJtE;EuBxxJU;IAAgC,qBAA4B;EvB2xJtE;EuB3xJU;IAAgC,sBAA4B;EvB8xJtE;EuB9xJU;IAAgC,sBAA4B;EvBiyJtE;EuBjyJU;IAAgC,qBAA4B;EvBoyJtE;EuBpyJU;IAAgC,qBAA4B;EvBuyJtE;EuBvyJU;IAAgC,qBAA4B;EvB0yJtE;EuB1yJU;IAAgC,qBAA4B;EvB6yJtE;EuB7yJU;IAAgC,qBAA4B;EvBgzJtE;EuBhzJU;IAAgC,qBAA4B;EvBmzJtE;EuBnzJU;IAAgC,qBAA4B;EvBszJtE;EuBtzJU;IAAgC,qBAA4B;EvByzJtE;EuBzzJU;IAAgC,qBAA4B;EvB4zJtE;EuB5zJU;IAAgC,qBAA4B;EvB+zJtE;EuB/zJU;IAAgC,qBAA4B;EvBk0JtE;EuBl0JU;IAAgC,qBAA4B;EvBq0JtE;AACF;;AyBh3JE;EACE,kBCE6B;A1Bi3JjC;;AyBj3JE;EACE,2BCD6B;EDE7B,4BCF6B;A1Bs3JjC;;AyBl3JE;EACE,8BCL6B;EDM7B,+BCN6B;A1B23JjC;;AyBn3JE;EACE,2BCT6B;EDU7B,8BCV6B;A1Bg4JjC;;AyBp3JE;EACE,4BCb6B;EDc7B,+BCd6B;A1Bq4JjC;;AyBr3JE;EACE,qBCjB6B;EDkB7B,uBAAuB;AzBw3J3B;;AyB74JE;EACE,qBCGgC;A1B64JpC;;AyB94JE;EACE,8BCAgC;EDChC,+BCDgC;A1Bk5JpC;;AyB/4JE;EACE,iCCJgC;EDKhC,kCCLgC;A1Bu5JpC;;AyBh5JE;EACE,8BCRgC;EDShC,iCCTgC;A1B45JpC;;AyBj5JE;EACE,+BCZgC;EDahC,kCCbgC;A1Bi6JpC;;AyBl5JE;EACE,wBChBgC;EDiBhC,uBAAuB;AzBq5J3B;;AyB16JE;EACE,sBCIiC;A1By6JrC;;AyB36JE;EACE,+BCCiC;EDAjC,gCCAiC;A1B86JrC;;AyB56JE;EACE,kCCHiC;EDIjC,mCCJiC;A1Bm7JrC;;AyB76JE;EACE,+BCPiC;EDQjC,kCCRiC;A1Bw7JrC;;AyB96JE;EACE,gCCXiC;EDYjC,mCCZiC;A1B67JrC;;AyB/6JE;EACE,yBCfiC;EDgBjC,uBAAuB;AzBk7J3B;;AyBv8JE;EACE,kBCoBI;A1Bs7JR;;AyBx8JE;EACE,2BCiBI;EDhBJ,4BCgBI;A1B27JR;;AyBz8JE;EACE,8BCaI;EDZJ,+BCYI;A1Bg8JR;;AyB18JE;EACE,2BCSI;EDRJ,8BCQI;A1Bq8JR;;AyB38JE;EACE,4BCKI;EDJJ,+BCII;A1B08JR;;AyB58JE;EACE,qBCCI;EDAJ,uBAAuB;AzB+8J3B;;AyBp+JE;EACE,kBCqBI;A1Bk9JR;;AyBr+JE;EACE,2BCkBI;EDjBJ,4BCiBI;A1Bu9JR;;AyBt+JE;EACE,8BCcI;EDbJ,+BCaI;A1B49JR;;AyBv+JE;EACE,2BCUI;EDTJ,8BCSI;A1Bi+JR;;AyBx+JE;EACE,4BCMI;EDLJ,+BCKI;A1Bs+JR;;AyBz+JE;EACE,qBCEI;EDDJ,uBAAuB;AzB4+J3B;;AyBjgKE;EACE,kBCsBI;A1B8+JR;;AyBlgKE;EACE,2BCmBI;EDlBJ,4BCkBI;A1Bm/JR;;AyBngKE;EACE,8BCeI;EDdJ,+BCcI;A1Bw/JR;;AyBpgKE;EACE,2BCWI;EDVJ,8BCUI;A1B6/JR;;AyBrgKE;EACE,4BCOI;EDNJ,+BCMI;A1BkgKR;;AyBtgKE;EACE,qBCGI;EDFJ,uBAAuB;AzBygK3B;;AyB9hKE;EACE,mBCuBM;A1B0gKV;;AyB/hKE;EACE,4BCoBM;EDnBN,6BCmBM;A1B+gKV;;AyBhiKE;EACE,+BCgBM;EDfN,gCCeM;A1BohKV;;AyBjiKE;EACE,4BCYM;EDXN,+BCWM;A1ByhKV;;AyBliKE;EACE,6BCQM;EDPN,gCCOM;A1B8hKV;;AyBniKE;EACE,sBCIM;EDHN,uBAAuB;AzBsiK3B;;AyB3jKE;EACE,mBCwBM;A1BsiKV;;AyB5jKE;EACE,4BCqBM;EDpBN,6BCoBM;A1B2iKV;;AyB7jKE;EACE,+BCiBM;EDhBN,gCCgBM;A1BgjKV;;AyB9jKE;EACE,4BCaM;EDZN,+BCYM;A1BqjKV;;AyB/jKE;EACE,6BCSM;EDRN,gCCQM;A1B0jKV;;AyBhkKE;EACE,sBCKM;EDJN,uBAAuB;AzBmkK3B;;AyBxlKE;EACE,mBCyBM;A1BkkKV;;AyBzlKE;EACE,4BCsBM;EDrBN,6BCqBM;A1BukKV;;AyB1lKE;EACE,+BCkBM;EDjBN,gCCiBM;A1B4kKV;;AyB3lKE;EACE,4BCcM;EDbN,+BCaM;A1BilKV;;AyB5lKE;EACE,6BCUM;EDTN,gCCSM;A1BslKV;;AyB7lKE;EACE,sBCMM;EDLN,uBAAuB;AzBgmK3B;;AyBrnKE;EACE,mBC0BM;A1B8lKV;;AyBtnKE;EACE,4BCuBM;EDtBN,6BCsBM;A1BmmKV;;AyBvnKE;EACE,+BCmBM;EDlBN,gCCkBM;A1BwmKV;;AyBxnKE;EACE,4BCeM;EDdN,+BCcM;A1B6mKV;;AyBznKE;EACE,6BCWM;EDVN,gCCUM;A1BknKV;;AyB1nKE;EACE,sBCOM;EDNN,uBAAuB;AzB6nK3B;;AyBlpKE;EACE,mBC2BM;A1B0nKV;;AyBnpKE;EACE,4BCwBM;EDvBN,6BCuBM;A1B+nKV;;AyBppKE;EACE,+BCoBM;EDnBN,gCCmBM;A1BooKV;;AyBrpKE;EACE,4BCgBM;EDfN,+BCeM;A1ByoKV;;AyBtpKE;EACE,6BCYM;EDXN,gCCWM;A1B8oKV;;AyBvpKE;EACE,sBCQM;EDPN,uBAAuB;AzB0pK3B;;ACxoKI;E0BxCJ;IAEI,sBV6OgC;EjBs8JlC;AACF;;ACxoKI;E0BvCJ;IAEI,0CAA0C;E3BkrK5C;AACF;;A2B/qKA;EACE,YAAY;EACZ,WAAW;EACX,yBtBU6B;EsBT7B,YAAY;EACZ,cAAc;A3BkrKhB;;A2BvrKA;EAOI,yBAAyB;A3BorK7B;;A2BhrKA;EACE,qBtBlBgB;ALqsKlB;;A2BhrKA;EACE,iBAAiB;EACjB,mBAAmB;A3BmrKrB;;A2BjrKA;EACE,kDAAkD;A3BorKpD;;A4BrtKA;EACE,kBAAkB;EAClB,UAAU;A5BwtKZ;;A4BvtKG;EAEG,4EAAmF;EACnF,kBAAkB;EAClB,WAAW;EACX,WAAW;EACX,YAAY;EACZ,MAAM;EACN,OAAO;EACP,WAAW;A5BytKjB;;A4BttKG;EAEG,4EAA4E;EAC5E,kBAAkB;EAClB,WAAW;EACX,WAAW;EACX,YAAY;EACZ,MAAM;EACN,OAAO;EACP,WAAW;A5BwtKjB;;A4BrtKE;EACE,kBAAkB;EAClB,6DAAoE;A5BwtKxE;;A4BttKE;EACE,qEAAqE;A5BytKzE;;A4BvtKE;EACE,gGAAgG;A5B0tKpG;;A4BrtKA;EACE,kBAAkB;EAClB,UAAU;A5BwtKZ;;A4B1tKA;EAII,kBAAkB;EAClB,WAAW;EACX,WAAW;EACX,YAAY;EACZ,MAAM;EACN,OAAO;EACP,WAAW;A5B0tKf;;A4BpuKA;EAcM,mBvB5ByB;EuB6BzB,aAAa;A5B0tKnB;;A4BzuKA;EAoBM,mBvBlCyB;EuBmCzB,YAAY;A5BytKlB;;A6BtxKA;EACE,eAAe;A7ByxKjB;;A6B1xKA;EAGI,cxBmB2B;ALwwK/B;;A6BvxKA;EACE,eAAe;A7B0xKjB;;A6B3xKA;EAGI,oDAAoD;A7B4xKxD;;A6BxxKA;EACE,eAAe;A7B2xKjB;;A6B5xKA;EAGI,iDAAiD;A7B6xKrD;;A6BzxKA;EACE,eAAe;A7B4xKjB;;A6B7xKA;EAGI,mDAAmD;A7B8xKvD;;A6B1xKA;EACE,eAAe;A7B6xKjB;;A6B9xKA;EAGI,8CAA8C;A7B+xKlD;;A6B5xKA;EACE,eAAe;A7B+xKjB;;A6BhyKA;EAGI,8CAA8C;A7BiyKlD;;A6B9xKA;EACE,eAAe;EACf,kBAAkB;EAClB,UAAU;A7BiyKZ;;A6BpyKA;EAKI,UAAU;EACV,mDAAmD;A7BmyKvD;;A6B/xKA;EAEI,sBAAsB;EACtB,oDAAoD;EACpD,gCAAgC;EAChC,gBAAgB;A7BiyKpB;;AChzKI;E4BUJ;IAUQ,mCAAmC;IACnC,WAAW;E7BiyKjB;AACF;;A6B5xKA;EACE,eAAe;A7B+xKjB;;AC3zKI;E4B2BJ;IAIM,UAAU;E7BiyKd;AACF;;A6B7xKA;EACE,eAAe;A7BgyKjB;;ACr0KI;E4BoCJ;IAIM,2BAA2B;E7BkyK/B;AACF;;A6B/xKA;EACE,eAAe;A7BkyKjB;;AC/0KI;E4B4CJ;IAIM,gCAAgC;E7BoyKpC;AACF;;A6BhyKA;EACE,eAAe;A7BmyKjB;;A6BpyKA;EAGI,yBAAuB;A7BqyK3B;;A6BxyKA;EAKM,yBAAuB;A7BuyK7B;;A6BjyKA;EACE,eAAe;A7BoyKjB;;A6BryKA;EAGI,yBAAqB;A7BsyKzB;;A6BzyKA;EAKM,yBAAqB;A7BwyK3B;;A6BnyKA;EACE,eAAe;A7BsyKjB;;A6BvyKA;EAGI,0BAA0B;A7BwyK9B;;A6BnyKA;EAEI,iCAAgC;A7BqyKpC;;A6BlyKA;EAEI,sBAAqB;A7BoyKzB;;A6BhyKA;EACE,eAAe;A7BmyKjB;;A6BpyKA;EAGI,yBAAuB;A7BqyK3B;;A6BxyKA;EAKM,yBAAuB;A7BuyK7B;;A6BlyKA;EACE,eAAe;A7BqyKjB;;A6BtyKA;EAGI,yBAAsB;A7BuyK1B;;A6B1yKA;EAKM,yBAAsB;A7ByyK5B;;A6BlyKA;EAEI,kBAAkB;EAClB,YAAY;EACZ,WAAW;EACX,OAAO;EACP,kBAAkB;EAClB,YAAY;EACZ,UAAU;EACV,eAAe;A7BoyKnB;;A6B7yKA;EAaM,YAAY;EACZ,UAAU;A7BoyKhB;;A6B/xKA;EAEI,eAAe;EACf,mBAAmB;EACnB,2CAA2C;EAC3C,sBAAsB;A7BiyK1B;;A6BtyKA;EASM,qBAAqB;EACrB,8CAA8C;A7BiyKpD;;A6BpxKA;EACE;IAAI,wBAAwB;E7BwxK5B;E6BvxKA;IAAK,uBAAuB;E7B0xK5B;E6BzxKA;IAAK,uBAAuB;E7B4xK5B;E6B3xKA;IAAK,wBAAwB;E7B8xK7B;E6B7xKA;IAAM,wBAAwB;E7BgyK9B;AACF;;A6BtyKA;EACE;IAAI,wBAAwB;E7BwxK5B;E6BvxKA;IAAK,uBAAuB;E7B0xK5B;E6BzxKA;IAAK,uBAAuB;E7B4xK5B;E6B3xKA;IAAK,wBAAwB;E7B8xK7B;E6B7xKA;IAAM,wBAAwB;E7BgyK9B;AACF;;A6B/xKA;EACE,sCAA8B;UAA9B,8BAA8B;EAC9B,8BAAsB;UAAtB,sBAAsB;EACtB,2BAAmB;UAAnB,mBAAmB;EACnB,yCAAiC;UAAjC,iCAAiC;EACjC,2CAAmC;UAAnC,mCAAmC;A7BkyKrC;;A8Bn/KA;EACE,kBAAkB;EAClB,UAAU;A9Bs/KZ;;A8Bx/KA;EAII,kBAAkB;EAClB,WAAW;EACX,WAAW;EACX,YAAY;EACZ,MAAM;EACN,OAAO;EACP,WAAW;A9Bw/Kf;;A8BlgLA;EAcM,iEAAiE;EACjE,sBAAsB;EACtB,aAAa;A9Bw/KnB;;A8BxgLA;EAuBM,iEAAiE;EACjE,sBAAsB;A9Bq/K5B;;A8B7gLA;EA6BM,mEAAmE;EACnE,sBAAsB;A9Bo/K5B;;A8BlhLA;EAmCM,mEAAmE;EACnE,sBAAsB;A9Bm/K5B;;A8BvhLA;EAyCM,sEAAsE;EACtE,wBAAwB;A9Bk/K9B;;A8B5hLA;EA+CM,oEAAoE;EACpE,qBAAqB;A9Bi/K3B;;A8BjiLA;EAqDM,oEAAoE;EACpE,qBAAqB;A9Bg/K3B;;AC9/KI;E8BxCJ;IAEI,kBAAkB;E/ByiLpB;AACF;;AC9/KI;E8BxCJ;IAEI,6BAA4B;E/ByiL9B;AACF;;ACpgLI;E8BlCJ;IAEI,kBAAkB;IAClB,QAAQ;IACR,OAAO;IACP,2BAA2B;E/ByiL7B;AACF;;AC7gLI;E8BzBJ;IAEI,kBAAkB;IAClB,QAAQ;IACR,QAAQ;IACR,2BAA2B;E/ByiL7B;AACF;;ACthLI;E8BjBJ;IAEI,kBAAkB;IAClB,YAAY;IACZ,UAAU;IACV,0BAA0B;E/B0iL5B;AACF;;A+BviLA;EACE,kBAAkB;EAClB,MAAM;EACN,OAAO;A/B0iLT;;A+BxiLA;EACE,kBAAkB;EAClB,MAAM;EACN,QAAQ;A/B2iLV;;A+BxiLA;EACE,kBAAkB;EAClB,SAAS;EACT,OAAO;A/B2iLT;;A+BziLA;EACE,kBAAkB;EAClB,SAAS;EACT,QAAQ;A/B4iLV;;A+B1iLA;EACE,kBAAkB;EAClB,QAAQ;EACR,QAAQ;EACR,2BAA2B;A/B6iL7B;;ACpkLI;E8ByBJ;IAEI,kBAAkB;IAClB,QAAQ;IACR,QAAQ;IACR,2BAA2B;E/B8iL7B;AACF;;A+B5iLA;EACE,kBAAkB;EAClB,QAAQ;EACR,SAAS;EACT,gCAA+B;A/B+iLjC;;A+B7iLA;EACE,kBAAkB;EAClB,aAAa;EACb,YAAY;A/BgjLd;;A+B9iLA;EACE,kBAAkB;EAClB,WAAW;EACX,UAAU;A/BijLZ;;A+B7iLA;EACE,kBAAkB;EAClB,aAAa;EACb,WAAW;EACX,WAAW;A/BgjLb;;A+B7iLA;EACE,kBAAkB;EAClB,YAAY;EACZ,WAAW;EACX,WAAW;A/BgjLb;;A+B9iLA;EACE,kBAAkB;EAClB,SAAS;EACT,UAAU;A/BijLZ;;A+B9iLA;EACE,kBAAkB;EAClB,aAAa;EACb,WAAW;A/BijLb;;AC1nLI;E8BsEJ;IAKI,aAAa;IACb,UAAU;E/BojLZ;AACF;;AC3nLI;E8BgEJ;IASI,aAAa;IACb,UAAU;E/BujLZ;AACF;;A+BpjLA;EACE,kBAAkB;EAClB,YAAY;EACZ,WAAW;A/BujLb;;AC9oLI;E8BoFJ;IAKI,YAAY;IACZ,WAAW;E/B0jLb;AACF;;AC/oLI;E8B8EJ;IASI,YAAY;IACZ,WAAW;E/B6jLb;AACF;;A+B1jLA;EACE,kBAAkB;EAClB,SAAS;EACT,UAAU;A/B6jLZ;;A+B1jLA;EACE,kBAAkB;EAClB,SAAS;EACT,UAAU;EACV,WAAW;A/B6jLb;;ACnqLI;E8BkGJ;IAMI,SAAS;IACT,UAAU;E/BgkLZ;AACF;;ACzpLI;E8BiFJ;IAUI,SAAS;IACT,UAAU;E/BmkLZ;AACF;;A+BhkLA;EACE,kBAAkB;EAClB,SAAS;EACT,WAAW;A/BmkLb;;AC9sLI;E8BwIJ;IAKI,SAAS;IACT,WAAW;E/BskLb;AACF;;AC7qLI;E8BgGJ;IASI,SAAS;IACT,WAAW;E/BykLb;AACF;;A+BvkLA;EACE,kBAAkB;EAClB,SAAS;EACT,WAAW;A/B0kLb;;AC3sLI;E8B8HJ;IAKI,SAAS;IACT,WAAW;E/B6kLb;AACF;;ACjsLI;E8B6GJ;IASI,SAAS;IACT,WAAW;E/BglLb;AACF;;A+B7kLA;EACE,kBAAkB;EAGlB,SAAS;EACT,WAAW;EACX,qBAAoB;A/B8kLtB;;ACvvLI;E8BmKJ;IAQI,qBAAoB;E/BilLtB;AACF;;AC5uLI;E8BkJJ;IAWI,WAAW;IACX,mBAAmB;E/BolLrB;AACF;;AC7uLI;E8B4IJ;IAgBI,UAAU;IACV,WAAW;E/BslLb;AACF;;ACnuLI;E8B2HJ;IAoBI,SAAS;IACT,WAAW;E/BylLb;AACF;;A+BtlLA;EACE,kBAAkB;EAClB,YAAY;EACZ,YAAY;EACZ,qBAAoB;A/BylLtB;;ACzxLI;E8B4LJ;IAOI,YAAY;E/B2lLd;AACF;;AC9wLI;E8B2KJ;IAUI,YAAY;IACZ,mBAAmB;E/B8lLrB;AACF;;AC/wLI;E8BqKJ;IAcI,YAAY;IACZ,YAAY;E/BimLd;AACF;;ACrwLI;E8BoJJ;IAkBI,YAAY;IACZ,YAAY;E/BomLd;AACF;;ACtwLI;E8B8IJ;IAsBI,YAAY;IACZ,YAAY;E/BumLd;AACF;;A+BnmLA;EACE,kBAAkB;EAClB,UAAU;EACV,YAAY;A/BsmLd;;A+BnmLA;EACE,kBAAkB;EAClB,QAAQ;EACR,UAAU;EACV,WAAW;A/BsmLb;;A+BjmLA;EACE,kBAAkB;EAClB,QAAQ;EACR,SAAS;EACT,4CAA4C;A/BomL9C;;A+BlmLA;EACE,kBAAkB;EAClB,SAAS;EACT,WAAW;EACX,YAAY;A/BqmLd;;A+BzmLA;EAMI,YAAY;A/BumLhB;;AC11LI;E8B6OJ;IASI,OAAO;IACP,UAAU;IACV,YAAY;E/BymLd;E+BpnLF;IAaM,qBAAqB;E/B0mLzB;AACF;;AC/1LI;E8BuOJ;IAiBI,QAAQ;IACR,UAAU;E/B4mLZ;E+B9nLF;IAoBM,mBAAmB;E/B6mLvB;AACF;;AC91LI;E8B4NJ;IAwBI,QAAQ;IACR,UAAU;E/B+mLZ;E+BxoLF;IA2BM,qBAAqB;E/BgnLzB;AACF;;ACl2LI;E8BsNJ;IA+BI,QAAQ;IACR,UAAU;E/BknLZ;E+BlpLF;IAkCM,mBAAmB;E/BmnLvB;AACF;;AC31LI;E8BqMJ;IAsCI,QAAQ;IACR,UAAU;E/BqnLZ;AACF;;A+BlnLA;EACE,WAAW;A/BqnLb;;A+BnnLA;EACE,UAAU;A/BsnLZ;;A+BpnLA;EACE,WAAW;A/BunLb;;A+BrnLA;EACE,aAAa;A/BwnLf;;A+BrnLA;EACE,kBAAkB;EAClB,QAAQ;EACR,QAAQ;A/BwnLV;;A+BpnLA;EACE,kBAAkB;EAClB,UAAU;EACV,OAAO;A/BunLT;;A+BrnLA;EACE,kBAAkB;EAClB,aAAa;EACb,QAAQ;A/BwnLV;;AgCl8LA;EACE,oDAAoD;AhCq8LtD;;AgCn8LA;EACE,iDAAiD;AhCs8LnD;;AgCn8LA;EACE,mDAAmD;AhCs8LrD;;AgCp8LA;EACE,8CAA8C;AhCu8LhD;;AgCr8LA;EACE,mDAAmD;AhCw8LrD;;AgCt8LA;EACE,mDAAmD;AhCy8LrD;;AgCt8LA;EACE,mDAAmD;AhCy8LrD;;AiC/9LA;EACE,0BAAkB;EAAlB,uBAAkB;EAAlB,kBAAkB;AjCk+LpB;;ACr7LI;EgC1CF;IAEI,iBAAiB;EjCk+LrB;AACF;;AiC/9LA;EACE,iBAAiB;AjCk+LnB;;ACt9LI;EgCTJ;IAEI,YAAY;EjCk+Ld;AACF;;ACt9LI;EgCVJ;IAEI,YAAY;EjCm+Ld;AACF;;AC59LI;EgCLJ;IAEI,uBAAsB;EjCo+LxB;AACF;;ACj9LI;EgCjBJ;IAEI,YAAY;EjCq+Ld;AACF;;AiCn+LA;EACE,gBAAgB;AjCs+LlB;;AiCp+LA;EACE,gBAAgB;AjCu+LlB;;AiCp+LA;EACE,iBAAiB;AjCu+LnB;;AiCr+LA;EACE,iBAAiB;EACjB,WAAW;AjCw+Lb;;ACz/LI;EgCeJ;IAII,iBAAiB;EjC2+LnB;AACF;;ACp/LI;EgCIJ;IAOI,iBAAiB;EjC8+LnB;AACF;;AkCxhMA;EACE,cAAc;AlC2hMhB;;AkCzhMA;ECkHM,oBAtCY;ED1EhB,iCEEoD;ApC0hMtD;;AkCxhME;ECsIM,eAhEU;EDpEd,sBAAsB;EACtB,iBAAiB;AlC2hMrB;;AmCl4LQ;ED5JN;ICoKU,kCA9D+C;EnC67LzD;AACF;;AqCtjME;EACE,WAAW;EACX,cAAc;EACd,SAAS;EACT,QAAQ;EACR,oCAA+C;ArCyjMnD;;AqCvjME;EACE,WAAW;EACX,cAAc;EACd,SAAS;EACT,QAAQ;EACR,uCAAkD;ArC0jMtD;;AkC5iME;EC8HM,kBAhEU;ED5Dd,oBAAoB;EACpB,kBAAkB;AlC+iMtB;;AmC95LQ;EDpJN;IC4JU,8BA9D+C;EnCy9LzD;AACF;;AqCllME;EACE,WAAW;EACX,cAAc;EACd,SAAS;EACT,QAAQ;EACR,qCAA+C;ArCqlMnD;;AqCnlME;EACE,WAAW;EACX,cAAc;EACd,SAAS;EACT,QAAQ;EACR,wCAAkD;ArCslMtD;;AkChkME;ECsHM,eAhEU;EDpDd,sBAAsB;EACtB,iBAAiB;AlCmkMrB;;AmC17LQ;ED5IN;ICoJU,kCA9D+C;EnCq/LzD;AACF;;AqC9mME;EACE,WAAW;EACX,cAAc;EACd,SAAS;EACT,QAAQ;EACR,oCAA+C;ArCinMnD;;AqC/mME;EACE,WAAW;EACX,cAAc;EACd,SAAS;EACT,QAAQ;EACR,uCAAkD;ArCknMtD;;AkCplME;EC8GM,kBAhEU;ED5Cd,sBAAsB;EACtB,gBAAgB;AlCulMpB;;AmCt9LQ;EDpIN;IC4IU,iCA9D+C;EnCihMzD;AACF;;AqC1oME;EACE,WAAW;EACX,cAAc;EACd,SAAS;EACT,QAAQ;EACR,mCAA+C;ArC6oMnD;;AqC3oME;EACE,WAAW;EACX,cAAc;EACd,SAAS;EACT,QAAQ;EACR,sCAAkD;ArC8oMtD;;AkCxmME;ECsGM,eAhEU;EDpCd,sBAAsB;EACtB,kBAAkB;AlC2mMtB;;AmCl/LQ;ED5HN;ICoIU,kCA9D+C;EnC6iMzD;AACF;;AqCtqME;EACE,WAAW;EACX,cAAc;EACd,SAAS;EACT,QAAQ;EACR,qCAA+C;ArCyqMnD;;AqCvqME;EACE,WAAW;EACX,cAAc;EACd,SAAS;EACT,QAAQ;EACR,wCAAkD;ArC0qMtD;;AkC5nME;EC8FM,iBAhEU;ED5Bd,sBAAsB;EACtB,iBAAiB;AlC+nMrB;;AmC9gMQ;EDpHN;IC4HU,kCA9D+C;EnCykMzD;AACF;;AqClsME;EACE,WAAW;EACX,cAAc;EACd,SAAS;EACT,QAAQ;EACR,oCAA+C;ArCqsMnD;;AqCnsME;EACE,WAAW;EACX,cAAc;EACd,SAAS;EACT,QAAQ;EACR,uCAAkD;ArCssMtD;;AkChpME;ECsFM,oBAhEU;EDpBd,sBAAsB;EACtB,gBAAgB;AlCmpMpB;;AmC1iMQ;ED5GN;ICoHU,sCA9D+C;EnCqmMzD;AACF;;AqC9tME;EACE,WAAW;EACX,cAAc;EACd,SAAS;EACT,QAAQ;EACR,mCAA+C;ArCiuMnD;;AqC/tME;EACE,WAAW;EACX,cAAc;EACd,SAAS;EACT,QAAQ;EACR,sCAAkD;ArCkuMtD;;AkCpqME;ECoDI,oBAtCY;EDZd,sBAAsB;EACtB,iBAAiB;AlCuqMrB;;AkCrqME;EC+CI,oBAtCY;EDPd,sBAAsB;EACtB,iBAAiB;AlCwqMrB;;AkCtqME;EC0CI,eAtCY;AnCsqMlB;;AkCvqME;ECuCI,oBAtCY;EDCd,sBAAsB;EACtB,iBAAiB;AlC0qMrB;;AkCxqME;ECkCI,oBAtCY;EDMd,iBAAiB;AlC2qMrB;;AqCzwME;EACE,WAAW;EACX,cAAc;EACd,SAAS;EACT,QAAQ;EACR,oCAA+C;ArC4wMnD;;AqC1wME;EACE,WAAW;EACX,cAAc;EACd,SAAS;EACT,QAAQ;EACR,uCAAkD;ArC6wMtD;;AkCtrME;EACE,mBAAkC;EAClC,iBAAiB;AlCyrMrB;;AkCvrME;EACE,iBAAgC;EAChC,iBAAiB;AlC0rMrB;;AkCtrMA;EACE,0BAA0B;AlCyrM5B;;AkCrrMA;EACE,6BAA6B;AlCwrM/B;;AsC5yMA;EACC,gDAAwC;UAAxC,wCAAwC;AtC+yMzC;;AsC7yMA;EACC,mDAA2C;UAA3C,2CAA2C;AtCgzM5C;;AsC7yMA;EACC;IACC,2CAA2C;IAC3C,0BAA0B;EtCgzM1B;EsC9yMD;IACC,0CAA0C;IAC1C,4BAA4B;EtCgzM5B;EsC9yMD;IACC,2CAA2C;IAC3C,0BAA0B;EtCgzM1B;AACF;;AsC5zMA;EACC;IACC,2CAA2C;IAC3C,0BAA0B;EtCgzM1B;EsC9yMD;IACC,0CAA0C;IAC1C,4BAA4B;EtCgzM5B;EsC9yMD;IACC,2CAA2C;IAC3C,0BAA0B;EtCgzM1B;AACF;;AsC9yMA;EACC;IACC,4BAA4B;EtCizM5B;EsC/yMD;IACC,0BAA0B;EtCizM1B;EsC/yMD;IACC,4BAA4B;EtCizM5B;AACF;;AsC1zMA;EACC;IACC,4BAA4B;EtCizM5B;EsC/yMD;IACC,0BAA0B;EtCizM1B;EsC/yMD;IACC,4BAA4B;EtCizM5B;AACF;;AsC9yMA;EACC;IACC,2CAA2C;IAC3C,oCAAiC;EtCizMjC;EsC/yMD;IACC,0CAA0C;IAC1C,qCAAiC;EtCizMjC;EsC/yMD;IACC,2CAA2C;IAC3C,oCAAiC;EtCizMjC;AACF;;AsC7zMA;EACC;IACC,2CAA2C;IAC3C,oCAAiC;EtCizMjC;EsC/yMD;IACC,0CAA0C;IAC1C,qCAAiC;EtCizMjC;EsC/yMD;IACC,2CAA2C;IAC3C,oCAAiC;EtCizMjC;AACF;;AsC/yMA;EAGK,qCAA6B;UAA7B,6BAA6B;AtCgzMlC;;AsC5yMA;EACE,0CAAmC;UAAnC,kCAAmC;EACnC,sBAAsB;EACtB,oCAA4B;UAA5B,4BAA4B;AtC+yM9B;;AsC7yMA;EACE;IACE,kFAAgF;EtCgzMlF;EsC9yMD;IACG,qFAAmF;EtCgzMrF;EsC9yMA;IACE,iFAA+E;EtCgzMjF;EsC9yMA;IACE,+EAA6E;EtCgzM/E;EsC9yMA;IACE,iFAA+E;EtCgzMjF;EsC9yMD;IACG,kFAAgF;EtCgzMlF;AACF;;AsCl0MA;EACE;IACE,kFAAgF;EtCgzMlF;EsC9yMD;IACG,qFAAmF;EtCgzMrF;EsC9yMA;IACE,iFAA+E;EtCgzMjF;EsC9yMA;IACE,+EAA6E;EtCgzM/E;EsC9yMA;IACE,iFAA+E;EtCgzMjF;EsC9yMD;IACG,kFAAgF;EtCgzMlF;AACF;;AuC73MA;EAEI,aAAa;EACb,gBAAgB;AvC+3MpB;;AuC53MA;EACE,uBAAuB;EACvB,YAAY;AvC+3Md;;AuC73MA;EACE,4BAA4B;EAC5B,2BAA2B;EAC3B,sBAAsB;AvCg4MxB;;AuC73MA;EACE,4BAA4B;EAC5B,0BAA0B;EAC1B,wBAAwB;AvCg4M1B;;AuC73MA;EACE,4BAA4B;EAC5B,sBAAsB;EACtB,4BAA4B;EAC5B,2BAA2B;AvCg4M7B;;AuC73MA;EACE,kBAAkB;EAClB,YAAY;EACZ,OAAO;AvCg4MT;;ACz4MI;EsCMJ;IAKI,aAAa;IACb,OAAO;EvCm4MT;AACF;;AC14MI;EsCAJ;IASI,YAAY;IACZ,OAAO;EvCs4MT;AACF;;ACt4MI;EsCXJ;IAaI,kBAAkB;IAClB,aAAa;IACb,OAAO;EvCy4MT;AACF;;ACx4MI;EsCjBJ;IAkBI,cAAc;IACd,YAAY;EvC44Md;AACF;;AC93MI;EsClCJ;IAsBI,cAAc;IACd,YAAY;EvC+4Md;AACF;;AuC54MA;EACE,oBAAoB;EACpB,mBAAmB;EACnB,eAAe;AvC+4MjB;;AuCl5MA;EAQM,kBAAkB;AvC84MxB;;ACt6MI;EsCgBJ;IAYQ,eAAe;EvC+4MrB;EuC35MF;IAgBU,2BAA2B;EvC84MnC;AACF;;AuC/5MA;EAuBQ,eAAe;AvC44MvB;;AuCt4MA;EACE,gBAAgB;AvCy4MlB;;AuC14MA;EAIM,eAAe;EACf,clClEyB;EkCmEzB,kBAAkB;EAClB,eAAe;AvC04MrB;;AuCr4MA;EACE,eAAe;AvCw4MjB;;AuCr4MA;EACE,YAAY;EACZ,kBAAkB;EAClB,WAAW;EACX,aAAa;EACb,mBAAmB;EACnB,eAAe;AvCw4MjB;;AuC94MA;EAQI,WAAW;AvC04Mf;;AuCt4MA;EACE,uBAAuB;EACvB,gBAAgB;EAChB,mBAAmB;AvCy4MrB;;AuCt4MA;EAEI,wBAAuB;EACvB,2BAA0B;AvCw4M9B;;AuCp4MA;EACE,UAAU;EACV,aAAa;EACb,qCAAqC;EACrC,cAAc;EACd,mBAAmB;AvCu4MrB;;AuCp4MA;EACE,qBAAqB;EACrB,kBAAkB;EAClB,UAAU;AvCu4MZ;;AuC14MA;EAKI,kBAAkB;EAClB,WAAW;EACX,WAAW;EACX,WAAW;EACX,OAAO;EACP,WAAW;EACX,iBAAiB;EACjB,WAAW;AvCy4Mf;;AuCr5MA;EAgBM,mBlC9HyB;ALugN/B;;AuCz5MA;EAqBM,mBlCtIyB;AL8gN/B;;AuC75MA;EA0BM,mBlC1IyB;ALihN/B;;AuCj6MA;EA+BM,iBAAiB;AvCs4MvB;;AuCj4MA;EAEI,gDAA2C;AvCm4M/C;;AuCr4MA;EAKI,6BAA4B;AvCo4MhC;;AuCz4MA;EASM,iBAAiB;EACjB,sDAAsD;EACtD,mBAAmB;EACnB,cAAc;EACd,WAAW;EACX,UAAU;EACV,kBAAkB;EAClB,yBAAyB;EACzB,uBAAuB;AvCo4M7B;;AuCr5MA;EAoBM,yBAA0B;AvCq4MhC;;AuCz5MA;EA2BM,WAAW;EACX,WAAW;EACX,UAAU;EACV,qBlC9KyB;ALgjN/B;;AuCh6MA;EAiCM,yBAA0B;AvCm4MhC;;AuC13MA;EAEI,iBAAiB;EACjB,sDAAsD;EACtD,mBAAmB;EACnB,cAAc;EACd,WAAW;EACX,UAAU;EACV,kBAAkB;EAClB,yBAAyB;EACzB,uBAAuB;EACvB,oBAAoB;EACpB,kBAAkB;EAClB,WAAW;EACX,QAAQ;EACR,WAAW;AvC43Mf;;AuC34MA;EAkBI,yBAA0B;AvC63M9B;;AuCz3MA;EAEI,iBAAiB;EACjB,sDAAsD;EACtD,mBAAmB;EACnB,cAAc;EACd,WAAW;EACX,WAAW;EACX,UAAU;EACV,kBAAkB;EAClB,yBAAyB;EACzB,uBAAuB;EACvB,oBAAoB;EACpB,kBAAkB;EAClB,WAAW;EACX,QAAQ;EACR,gCAAgC;AvC23MpC;;AuCt3MA;EACE,gBAAgB;AvCy3MlB;;AuCt3MA;EACE,YAAY;AvCy3Md;;AuCt3MA;EACE,iBAAiB;AvCy3MnB;;AuCt3MA;EACE,oBAAoB;AvCy3MtB;;AuCt3MA;EACE,qBAAqB;AvCy3MvB;;ACjmNI;EsCuOJ;IAGI,qBAAqB;EvC43MvB;AACF;;ACjmNI;EsCiOJ;IAMI,mBAAmB;EvC+3MrB;AACF;;AuC33MA;EACE,gCAAiC;AvC83MnC;;AAEA;EuC93MI,6BAA8B;AvCg4MlC;;AuC13MA;EAEI,kBAAkB;EAClB,QAAQ;EACR,SAAS;EACT,WAAW;EACX,4CAA4C;AvC43MhD;;ACvnNI;EsCqPJ;IAQM,4CAA4C;EvC+3MhD;AACF;;AC5mNI;EsCoOJ;IAWM,2CAA2C;EvCk4M/C;AACF;;AuC73MA;EACE,mBAAmB;AvCg4MrB;;AwCrrNA;EACE,kBAAkB;EAClB,YAAY;EACZ,UAAU;EACV,WAAW;AxCwrNb;;AwCtrNA;EACE,kBAAkB;EAClB,YAAY;EACZ,OAAO;EACP,WAAW;AxCyrNb;;ACpoNI;EuCzDJ;IAMI,WAAW;ExC4rNb;EwClsNF;IASM,WAAW;ExC4rNf;AACF;;AyC5sNA;EACE,iBAAiB;EACjB,WAAW;EACX,kBAAkB;AzC+sNpB;;ACrrNI;EwC7BJ;IAKI,gBAAgB;EzCktNlB;AACF;;AC1qNI;EwC9CJ;IAQI,eAAe;EzCqtNjB;AACF;;AyC9tNA;EAWI,kBAAkB;EAClB,MAAM;EACN,OAAO;EACP,eAAe;AzCutNnB;;AC9sNI;EwCvBJ;IAgBM,QAAQ;EzC0tNZ;AACF;;AC9sNI;EwC7BJ;IAmBM,OAAO;IACP,SAAS;EzC6tNb;AACF;;AC1sNI;EwCxCJ;IAuBM,QAAQ;IACR,QAAQ;EzCguNZ;AACF;;AC3sNI;EwC9CJ;IA2BM,QAAQ;IACR,WAAW;EzCmuNf;AACF;;ACjsNI;EwC/DJ;IA+BM,OAAO;EzCsuNX;AACF;;AyCtwNA;EAmCI,kBAAkB;EAClB,YAAY;EACZ,UAAU;EACV,eAAe;AzCuuNnB;;ACtvNI;EwCvBJ;IAwCM,WAAW;IACX,UAAU;EzC0uNd;AACF;;ACvvNI;EwC7BJ;IA4CM,YAAY;IACZ,WAAW;EzC6uNf;AACF;;ACnvNI;EwCxCJ;IAgDM,YAAW;IACX,UAAU;EzCgvNd;AACF;;ACpvNI;EwC9CJ;IAoDM,WAAW;IACX,UAAU;IACV,oBAAoB;EzCmvNxB;AACF;;AC3uNI;EwC/DJ;IAyDM,WAAW;IACX,oBAAoB;EzCsvNxB;AACF;;AyCjzNA;EA8DI,qBAAoB;AzCuvNxB;;AC9xNI;EwCvBJ;IAgEM,qBAAoB;EzC0vNxB;AACF;;AC9xNI;EwC7BJ;IAmEM,qBAAoB;EzC6vNxB;AACF;;ACzxNI;EwCxCJ;IAsEM,mBAAmB;EzCgwNvB;AACF;;ACzxNI;EwC9CJ;IAyEM,qBAAoB;EzCmwNxB;AACF;;AC9wNI;EwC/DJ;IA4EM,mBAAmB;EzCswNvB;AACF;;AyCjwNA;EACE,iBAAiB;EACjB,qBAAqB;EACrB,kBAAkB;EAClB,mBAAmB;AzCowNrB;;AC90NI;EwCsEJ;IAMI,mBAAmB;EzCuwNrB;AACF;;ACn0NI;EwCqDJ;IASI,mBAAmB;EzC0wNrB;AACF;;AyCpxNA;EAYI,WAAW;AzC4wNf;;ACn1NI;EwC2DJ;IAcM,WAAW;EzC+wNf;AACF;;AyC9xNA;EAmBK,kBAAkB;EAClB,YAAW;EACX,OAAO;EACP,UAAU;EACV,SAAS;AzC+wNd;;AC52NI;EwCsEJ;IAyBM,SAAS;EzCkxNb;AACF;;ACl3NI;EwCsEJ;IA4BM,UAAU;EzCqxNd;AACF;;AyClzNA;EAgCM,kBAAkB;EAClB,YAAY;EACZ,WAAW;EACX,QAAQ;EACR,UAAS;AzCsxNf;;AyC1zNA;EAsCQ,WAAW;AzCwxNnB;;ACp4NI;EwCsEJ;IAwCU,YAAY;EzC2xNpB;AACF;;AyCpxNA;EAGM,mDAAmD;AzCqxNzD;;AyCjxNA;EACE,mDAAmD;EAInD,eAAe;AzCixNjB;;ACj3NI;EwC2FJ;IAGI,gDAAgD;EzCwxNlD;AACF;;A0Cr6NA;EACE,aAAc;A1Cw6NhB;;ACj4NI;EyCxCJ;IAGM,aAAc;E1C26NlB;AACF;;ACj4NI;EyC9CJ;IAMI,aAAc;E1C86NhB;AACF", "file": "../main.css", "sourcesContent": ["\r\n\r\n\r\n@import 'theme-mode-mixins';\r\n\r\n\r\n\r\n// Mode activation\r\n$theme-mode-active: 'light';\r\n\r\n\r\n\r\nbody[data-theme='dark'],.dark-bg,.dark-mode-texts{\r\n  @include dark-theme-mode;\r\n  \r\n}\r\n.light-version-logo,.light-shape{\r\n  display: none;\r\n}\r\n.dark-version-logo,.dark-shape{\r\n  display: none;\r\n}\r\n\r\n\r\n\r\nbody[data-theme='light'],.light-bg,.light-mode-texts {\r\n @include light-theme-mode;\r\n}\r\n\r\n\r\n\r\n// Setting The Default Theme Mode\r\nbody{\r\n  @if $theme-mode-active == 'dark' {\r\n    @include dark-theme-mode;\r\n  } @else {\r\n    @include light-theme-mode;\r\n  }\r\n \r\n}\r\n\r\n.default-logo,.default-shape{\r\n  display: block;\r\n}\r\n\r\nbody[data-theme='dark'],.dark-bg,.dark-mode-texts{\r\n  [data-force-dark-mode]{\r\n    background: #171a23!important;\r\n  }\r\n  .dark-version-logo{\r\n    display: block;\r\n  }\r\n  .light-shape{\r\n    display: none!important;\r\n  }\r\n  .dark-shape{\r\n    display: block!important;\r\n  }\r\n  .light-mode-texts{\r\n    .dark-version-logo{\r\n      display: none;\r\n    }\r\n    .light-version-logo{\r\n      display: block;\r\n    }\r\n  }\r\n\r\n  .default-logo,.default-shape{\r\n    display: none;\r\n  }\r\n}\r\n\r\nbody[data-theme='light'],.light-bg,.light-mode-texts {\r\n  \r\n  .light-version-logo{\r\n    display: block;\r\n  }\r\n  .light-shape{\r\n    display: block!important;\r\n  }\r\n  .dark-shape{\r\n    display: none!important;\r\n  }\r\n  .dark-mode-texts{\r\n    .light-shape{\r\n      display: none!important;\r\n    }\r\n    .dark-shape{\r\n      display: block!important;\r\n    }\r\n    .dark-version-logo{\r\n      display: block;\r\n    }\r\n    .light-version-logo{\r\n      display: none;\r\n    }\r\n  }\r\n  \r\n  .default-logo,.default-shape{\r\n    display: none;\r\n  }\r\n}\r\n", "\r\n@mixin dark-theme-mode{\r\n  --bg:  #0E1019;\r\n  --bg-2:rgba(255,255,255,0.01);\r\n  --bg-3:#0E1019;\r\n  --bg-4:#0E1019;\r\n  --bg-5:#0E1019;\r\n  --bg-6:#0E1019;\r\n  --bg-7:#13151C;\r\n  --bg-8:#0E1019;\r\n  --color-headings: #fff;\r\n  --color-headings-opacity:rgba(255, 255, 255, 0.4);\r\n  --color-texts-opacity: rgba(255, 255, 255,0.7);\r\n  --color-texts: #fff;\r\n  --btn-border: rgba(255,255,255,.3);\r\n  --border-color:rgba(255,255,255,.08);\r\n  --border-color-2:rgba(255,255,255,.08);\r\n  --force-dark: #171a23;\r\n}\r\n@mixin light-theme-mode{\r\n  --bg:  #fcfdfe;\r\n  --bg-2:#f4f7fa;\r\n  --bg-3:#f8f8f8;\r\n  --bg-4:#fdfdff;\r\n  --bg-5:#ecf2f7;\r\n  --bg-6:#fff;\r\n  --bg-7:#EDF9F2;\r\n  --bg-8:#fbfbfb;\r\n  --color-headings: #161c2d;\r\n  --color-headings-opacity: rgba(22,28,45, 0.4);\r\n  --color-texts-opacity: rgba(22,28,45, 0.7);\r\n  --color-texts: #161c2d;\r\n  --btn-border: rgba(3, 3, 3, 0.3);\r\n  --border-color:#e7e9ed;\r\n  --border-color-2:#eae9f2;\r\n\r\n}\r\n\r\n@mixin dark-mode {\r\n  [data-theme='dark'] & ,.dark-mode-texts & {\r\n      @content;\r\n  }\r\n}\r\n@mixin light-mode {\r\n  [data-theme='dark'] & ,.dark-mode-texts & {\r\n      @content;\r\n  }\r\n}\r\n\r\n// #FCFDFE, #F4F7FA, #F8F8F8, #ECF2F7, #FDFDFF, ", "body[data-theme='dark'], .dark-bg, .dark-mode-texts {\n  --bg:  #0E1019;\n  --bg-2:rgba(255,255,255,0.01);\n  --bg-3:#0E1019;\n  --bg-4:#0E1019;\n  --bg-5:#0E1019;\n  --bg-6:#0E1019;\n  --bg-7:#13151C;\n  --bg-8:#0E1019;\n  --color-headings: #fff;\n  --color-headings-opacity:rgba(255, 255, 255, 0.4);\n  --color-texts-opacity: rgba(255, 255, 255,0.7);\n  --color-texts: #fff;\n  --btn-border: rgba(255,255,255,.3);\n  --border-color:rgba(255,255,255,.08);\n  --border-color-2:rgba(255,255,255,.08);\n  --force-dark: #171a23;\n}\n\n.light-version-logo, .light-shape {\n  display: none;\n}\n\n.dark-version-logo, .dark-shape {\n  display: none;\n}\n\nbody[data-theme='light'], .light-bg, .light-mode-texts {\n  --bg:  #fcfdfe;\n  --bg-2:#f4f7fa;\n  --bg-3:#f8f8f8;\n  --bg-4:#fdfdff;\n  --bg-5:#ecf2f7;\n  --bg-6:#fff;\n  --bg-7:#EDF9F2;\n  --bg-8:#fbfbfb;\n  --color-headings: #161c2d;\n  --color-headings-opacity: rgba(22,28,45, 0.4);\n  --color-texts-opacity: rgba(22,28,45, 0.7);\n  --color-texts: #161c2d;\n  --btn-border: rgba(3, 3, 3, 0.3);\n  --border-color:#e7e9ed;\n  --border-color-2:#eae9f2;\n}\n\nbody {\n  --bg:  #fcfdfe;\n  --bg-2:#f4f7fa;\n  --bg-3:#f8f8f8;\n  --bg-4:#fdfdff;\n  --bg-5:#ecf2f7;\n  --bg-6:#fff;\n  --bg-7:#EDF9F2;\n  --bg-8:#fbfbfb;\n  --color-headings: #161c2d;\n  --color-headings-opacity: rgba(22,28,45, 0.4);\n  --color-texts-opacity: rgba(22,28,45, 0.7);\n  --color-texts: #161c2d;\n  --btn-border: rgba(3, 3, 3, 0.3);\n  --border-color:#e7e9ed;\n  --border-color-2:#eae9f2;\n}\n\n.default-logo, .default-shape {\n  display: block;\n}\n\nbody[data-theme='dark'] [data-force-dark-mode], .dark-bg [data-force-dark-mode], .dark-mode-texts [data-force-dark-mode] {\n  background: #171a23 !important;\n}\n\nbody[data-theme='dark'] .dark-version-logo, .dark-bg .dark-version-logo, .dark-mode-texts .dark-version-logo {\n  display: block;\n}\n\nbody[data-theme='dark'] .light-shape, .dark-bg .light-shape, .dark-mode-texts .light-shape {\n  display: none !important;\n}\n\nbody[data-theme='dark'] .dark-shape, .dark-bg .dark-shape, .dark-mode-texts .dark-shape {\n  display: block !important;\n}\n\nbody[data-theme='dark'] .light-mode-texts .dark-version-logo, .dark-bg .light-mode-texts .dark-version-logo, .dark-mode-texts .light-mode-texts .dark-version-logo {\n  display: none;\n}\n\nbody[data-theme='dark'] .light-mode-texts .light-version-logo, .dark-bg .light-mode-texts .light-version-logo, .dark-mode-texts .light-mode-texts .light-version-logo {\n  display: block;\n}\n\nbody[data-theme='dark'] .default-logo, body[data-theme='dark'] .default-shape, .dark-bg .default-logo, .dark-bg .default-shape, .dark-mode-texts .default-logo, .dark-mode-texts .default-shape {\n  display: none;\n}\n\nbody[data-theme='light'] .light-version-logo, .light-bg .light-version-logo, .light-mode-texts .light-version-logo {\n  display: block;\n}\n\nbody[data-theme='light'] .light-shape, .light-bg .light-shape, .light-mode-texts .light-shape {\n  display: block !important;\n}\n\nbody[data-theme='light'] .dark-shape, .light-bg .dark-shape, .light-mode-texts .dark-shape {\n  display: none !important;\n}\n\nbody[data-theme='light'] .dark-mode-texts .light-shape, .light-bg .dark-mode-texts .light-shape, .light-mode-texts .dark-mode-texts .light-shape {\n  display: none !important;\n}\n\nbody[data-theme='light'] .dark-mode-texts .dark-shape, .light-bg .dark-mode-texts .dark-shape, .light-mode-texts .dark-mode-texts .dark-shape {\n  display: block !important;\n}\n\nbody[data-theme='light'] .dark-mode-texts .dark-version-logo, .light-bg .dark-mode-texts .dark-version-logo, .light-mode-texts .dark-mode-texts .dark-version-logo {\n  display: block;\n}\n\nbody[data-theme='light'] .dark-mode-texts .light-version-logo, .light-bg .dark-mode-texts .light-version-logo, .light-mode-texts .dark-mode-texts .light-version-logo {\n  display: none;\n}\n\nbody[data-theme='light'] .default-logo, body[data-theme='light'] .default-shape, .light-bg .default-logo, .light-bg .default-shape, .light-mode-texts .default-logo, .light-mode-texts .default-shape {\n  display: none;\n}\n\n/*=== Media Query ===*/\n.accordion-trigger.arrow-icon {\n  position: relative;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n}\n\n.accordion-trigger.arrow-icon:after {\n  content: \"\\ea05\";\n  font-family: \"Grayic\";\n  font-size: 32px;\n  display: block;\n  line-height: 1;\n  transform: rotate(0deg);\n  transition: .4s;\n}\n\n.accordion-trigger.arrow-icon[aria-expanded=\"true\"]:after {\n  transform: rotate(-180deg);\n}\n\n.gr-badge {\n  min-width: 95px;\n  min-height: 29px;\n  display: inline-flex;\n  justify-content: center;\n  align-items: center;\n}\n\n.btn {\n  min-width: 175px;\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.btn-hover-translate-none:hover {\n  transform: translateY(0);\n}\n\n.btn.with-icon i {\n  margin-left: 25px;\n}\n\n.btn.with-icon-left i {\n  margin-right: 15px;\n}\n\n.btn-primary {\n  color: #fff;\n}\n\n.btn-primary.with-icon i {\n  margin-left: 25px;\n}\n\n.btn-primary:hover {\n  background: #473bf0;\n}\n\n.btn-white {\n  border: 1px solid #d5d7dd;\n}\n\n.btn-white:hover {\n  color: #473bf0;\n  background: #fff;\n}\n\n.btn-link.with-icon {\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.btn-link.with-icon i {\n  margin-left: 16px;\n  transition: .4s;\n}\n\n.btn-link.with-icon:hover i {\n  margin-left: 25px;\n}\n\n.card-btn-link {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.card-btn-link i {\n  margin-left: auto;\n  transition: .4s;\n}\n\n.card-btn-link:hover i {\n  margin-right: -5px;\n}\n\n.btn-toggle {\n  width: 72px;\n  height: 33px;\n  border-radius: 17px;\n  background-color: rgba(19, 21, 28, 0.15);\n  position: relative;\n  margin: 0 15px;\n}\n\n.btn-toggle span {\n  width: 21px;\n  height: 21px;\n  background-color: #FFFFFF;\n  position: absolute;\n  left: 0;\n  margin-left: 6px;\n  top: 0;\n  margin-top: 6px;\n  transition: .4s;\n  border-radius: 500px;\n  pointer-events: none;\n}\n\n.btn-toggle.active {\n  background: #473bf0;\n}\n\n.btn-toggle.active span {\n  left: calc(100% - 33px);\n}\n\n.card-list {\n  max-width: 315px;\n  margin: 30px 0;\n}\n\n.card-list li {\n  color: #13151C;\n  letter-spacing: -0.2px;\n  line-height: 29px;\n  margin-bottom: 13px;\n  display: flex;\n}\n\n.card-list li.disabled {\n  color: rgba(19, 21, 28, 0.7);\n}\n\n.card-list li.disabled i {\n  color: #d5d7dd;\n}\n\n.card-columns {\n  column-count: 1;\n}\n\n@media (min-width: 576px) {\n  .card-columns {\n    column-count: 2;\n  }\n}\n\n@media (min-width: 768px) {\n  .card-columns.column-3 {\n    column-count: 3;\n  }\n}\n\n.job-card-hover i {\n  transition: .4s;\n}\n\n.job-card-hover:hover i {\n  color: #473bf0 !important;\n}\n\n.category-card {\n  transition: .4s;\n  background-color: #fff;\n}\n\n.category-card .title {\n  color: #13151C;\n}\n\n.category-card .sub-title {\n  color: rgba(19, 21, 28, 0.7);\n}\n\n.category-card .card-icon {\n  background-color: #473bf0;\n}\n\n@media (min-width: 768px) {\n  .category-card .card-icon {\n    transition: .3s;\n    transform: scale(0);\n    background-color: rgba(255, 255, 255, 0.2);\n  }\n}\n\n@media (min-width: 768px) {\n  .category-card:hover {\n    background-color: #473bf0;\n  }\n  .category-card:hover .card-title {\n    color: #fff;\n  }\n  .category-card:hover .sub-title {\n    color: rgba(255, 255, 255, 0.7);\n  }\n  .category-card:hover .card-icon {\n    transform: scale(1);\n  }\n}\n\n.cart-details-main-block .cart-product {\n  display: flex;\n  flex-wrap: wrap;\n}\n\n.cart-details-main-block .cart-product .product-block {\n  flex-basis: 100%;\n}\n\n@media (min-width: 480px) {\n  .cart-details-main-block .cart-product .product-block {\n    flex-basis: 85%;\n  }\n}\n\n@media (min-width: 576px) {\n  .cart-details-main-block .cart-product .product-block {\n    flex-basis: 75%;\n  }\n}\n\n@media (min-width: 768px) {\n  .cart-details-main-block .cart-product .product-block {\n    flex-basis: 60%;\n  }\n}\n\n@media (min-width: 992px) {\n  .cart-details-main-block .cart-product .product-block {\n    flex-basis: 50%;\n  }\n}\n\n@media (min-width: 1200px) {\n  .cart-details-main-block .cart-product .product-block {\n    flex-basis: 40%;\n  }\n}\n\n@media (min-width: 576px) {\n  .cart-details-main-block .cart-product .quantity-block {\n    flex-basis: 15%;\n  }\n}\n\n@media (min-width: 768px) {\n  .cart-details-main-block .cart-product .quantity-block {\n    flex-basis: 15%;\n  }\n}\n\n@media (min-width: 992px) {\n  .cart-details-main-block .cart-product .quantity-block {\n    flex-basis: 15%;\n  }\n}\n\n@media (min-width: 1200px) {\n  .cart-details-main-block .cart-product .quantity-block {\n    flex-basis: 15%;\n  }\n}\n\n@media (min-width: 768px) {\n  .cart-details-main-block .cart-product .price-block {\n    flex-basis: 15%;\n  }\n}\n\n@media (min-width: 992px) {\n  .cart-details-main-block .cart-product .price-block {\n    flex-basis: 15%;\n  }\n}\n\n@media (min-width: 1200px) {\n  .cart-details-main-block .cart-product .price-block {\n    flex-basis: 15%;\n  }\n}\n\n.cart-details-main-block .cart-product .quantity-block {\n  flex-basis: 40%;\n}\n\n@media (min-width: 480px) {\n  .cart-details-main-block .cart-product .quantity-block {\n    flex-basis: 40%;\n  }\n}\n\n@media (min-width: 576px) {\n  .cart-details-main-block .cart-product .quantity-block {\n    flex-basis: 40%;\n  }\n}\n\n@media (min-width: 768px) {\n  .cart-details-main-block .cart-product .quantity-block {\n    flex-basis: 25%;\n  }\n}\n\n@media (min-width: 992px) {\n  .cart-details-main-block .cart-product .quantity-block {\n    flex-basis: 25%;\n  }\n}\n\n@media (min-width: 1200px) {\n  .cart-details-main-block .cart-product .quantity-block {\n    flex-basis: 25%;\n  }\n}\n\n@media (min-width: 576px) {\n  .cart-details-main-block .cart-product .total-block {\n    flex-basis: 15%;\n  }\n}\n\n@media (min-width: 768px) {\n  .cart-details-main-block .cart-product .total-block {\n    flex-basis: 15%;\n  }\n}\n\n@media (min-width: 992px) {\n  .cart-details-main-block .cart-product .total-block {\n    flex-basis: 10%;\n  }\n}\n\n@media (min-width: 1200px) {\n  .cart-details-main-block .cart-product .total-block {\n    flex-basis: 20%;\n  }\n}\n\n.cart-details-main-block .cart-product .cross-btn-positioning {\n  position: absolute;\n  top: 40px;\n  right: 16px;\n}\n\n@media (min-width: 768px) {\n  .cart-details-main-block .cart-product .cross-btn-positioning {\n    position: static;\n  }\n}\n\n.gr-check-input {\n  padding-top: 5px;\n  margin-bottom: 20px;\n}\n\n.gr-check-input input:checked ~ .checkbox {\n  background: #473bf0;\n}\n\n.gr-check-input input:checked ~ .checkbox::after {\n  opacity: 1;\n  color: #fff;\n}\n\n.gr-check-input .checkbox {\n  position: relative;\n  line-height: 1;\n  width: 19px;\n  height: 19px;\n  border-radius: 3px;\n  background-color: #E7E9ED;\n}\n\n.gr-check-input .checkbox:after {\n  content: \"\\eaba\";\n  font-family: 'Grayic';\n  font-size: 15px;\n  font-weight: normal;\n  color: #000;\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  opacity: 0;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.gr-radio-input input:checked ~ label .round-indicator {\n  border: 1px solid #473bf0;\n  background: #fff;\n}\n\n.gr-radio-input input:checked ~ label .round-indicator:after {\n  opacity: 1;\n}\n\n.round-indicator {\n  position: relative;\n  display: inline-block;\n  border: 1px solid #E5E5E5;\n  background-color: #E5E5E5;\n  min-width: 20px;\n  max-width: 20px;\n  min-height: 20px;\n  max-height: 20px;\n  border-radius: 500px;\n}\n\n.round-indicator:after {\n  content: \"\";\n  background-color: #473bf0;\n  min-width: 12px;\n  max-width: 12px;\n  min-height: 12px;\n  max-height: 12px;\n  border-radius: 500px;\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  opacity: 0;\n  transform: translate(-50%, -50%);\n}\n\n.location-filter-form {\n  border-radius: 10px;\n}\n\n.location-filter-form .single-input {\n  border-bottom: 1px solid #e7e9ed;\n}\n\n@media (min-width: 576px) {\n  .location-filter-form .single-input {\n    border-right: 1px solid #e7e9ed;\n  }\n}\n\n@media (min-width: 992px) {\n  .location-filter-form .single-input {\n    border-bottom: 0;\n  }\n}\n\n.location-filter-form .single-input:last-child {\n  border-right: 0;\n}\n\n.location-filter-form .location-select {\n  position: relative;\n  padding-left: 50px;\n  border: 0;\n  font-size: 15px;\n}\n\n.location-filter-form .location-select:before {\n  content: '\\f3c5';\n  font-family: \"Font Awesome 5 Free\";\n  font-weight: 700;\n  left: 0;\n  top: 0;\n  height: 100%;\n  position: absolute;\n  width: 45px;\n  font-size: 18px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.location-filter-form .location-select .list {\n  margin-top: 0;\n}\n\n.location-filter-form .form-control:focus {\n  box-shadow: none;\n}\n\n.location-filter-form .date-picker {\n  display: flex;\n  align-items: center;\n}\n\n.location-filter-form .date-picker > div {\n  height: 100%;\n  width: 100%;\n}\n\n.location-filter-form .date-picker-input {\n  padding-left: 45px;\n  margin-top: 3px;\n  color: #13151C;\n  font-size: 15px;\n}\n\n.location-filter-form .date-picker-input::placeholder {\n  color: #13151C;\n}\n\n.location-filter-form .date-picker-icon {\n  font-size: 18px;\n  width: 45px;\n  height: 100%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: #13151C;\n  position: absolute;\n  top: 0;\n  left: 0;\n}\n\n.location-filter-form .search-btn {\n  min-width: 199px;\n}\n\n@media (min-width: 992px) {\n  .header-btn {\n    margin-left: 10px;\n  }\n}\n\n.header-btn a {\n  display: none;\n}\n\n@media (min-width: 360px) {\n  .header-btn a {\n    display: inline-flex;\n    min-height: 35px;\n    min-width: 120px;\n    font-size: 14px;\n    font-weight: 500;\n    border-radius: 0.625rem;\n  }\n}\n\n@media (min-width: 400px) {\n  .header-btn a {\n    min-height: 45px;\n    min-width: 141px;\n    font-size: 15px;\n  }\n}\n\n@media (min-width: 576px) {\n  .header-btn a {\n    min-height: 50px;\n  }\n}\n\n.header-btns .btn {\n  min-width: 124px;\n  height: 45px;\n  font-size: 15px;\n}\n\n.btn-close {\n  position: absolute;\n  top: 0;\n  right: 0;\n  font-size: 22px;\n  width: 60px;\n  height: 50px;\n  display: flex;\n  justify-content: center;\n  border: none;\n  background: transparent;\n  font-weight: 700;\n}\n\n.btn-close i {\n  color: #353638;\n}\n\n.site-header .brand-logo {\n  min-width: 150px;\n}\n\n.site-header--absolute {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  z-index: 999;\n}\n\n.site-header--sticky:not(.mobile-sticky-enable) {\n  position: absolute !important;\n  top: 0;\n  right: 0;\n  width: 100%;\n  z-index: 999;\n}\n\n@media (min-width: 992px) {\n  .site-header--sticky:not(.mobile-sticky-enable) {\n    position: fixed !important;\n    transition: .4s;\n  }\n  .site-header--sticky:not(.mobile-sticky-enable).scrolling {\n    transform: translateY(-100%);\n    transition: .4s;\n  }\n  .site-header--sticky:not(.mobile-sticky-enable).reveal-header {\n    transform: translateY(0%);\n    box-shadow: 0 12px 34px -11px rgba(65, 62, 101, 0.1);\n    z-index: 9999;\n  }\n}\n\n.site-header--sticky.mobile-sticky-enable {\n  top: 0;\n  right: 0;\n  width: 100%;\n  z-index: 999;\n  position: fixed !important;\n  transition: .4s;\n}\n\n.site-header--sticky.mobile-sticky-enable.scrolling {\n  transform: translateY(-100%);\n  transition: .4s;\n}\n\n.site-header--sticky.mobile-sticky-enable.reveal-header {\n  transform: translateY(0%);\n  box-shadow: 0 12px 34px -11px rgba(65, 62, 101, 0.1);\n  z-index: 9999;\n}\n\n@media (min-width: 992px) {\n  .site-header--menu-center .navbar-nav-wrapper {\n    margin-left: auto;\n    margin-right: auto;\n  }\n}\n\n@media (min-width: 1200px) {\n  .site-header--menu-left .container-fluid .navbar-nav-wrapper {\n    width: 100%;\n    padding-right: 15px;\n    padding-left: 15px;\n    margin-right: auto;\n    margin-left: auto;\n  }\n}\n\n.site-header--menu-left .container-fluid > .container-fluid .gr-megamenu-dropdown {\n  left: 0%;\n  transform: translateX(0%) translateY(10px);\n}\n\n.site-header--menu-left .container-fluid > .container-fluid .nav-item.dropdown:hover > .gr-megamenu-dropdown.center {\n  transform: translateX(0%) translateY(-10px);\n  left: 0%;\n}\n\n.site-header--menu-left .navbar-nav-wrapper .navbar-nav {\n  justify-content: flex-start;\n  padding-left: 15px;\n}\n\n@media (min-width: 1200px) {\n  .site-header--menu-left .navbar-nav-wrapper {\n    width: 100%;\n  }\n}\n\n.site-header--menu-right .navbar-nav-wrapper {\n  margin-left: auto;\n}\n\n.site-header--menu-right > .container-fluid .gr-megamenu-dropdown {\n  left: 100%;\n  transform: translateX(-100%) translateY(10px);\n}\n\n.site-header--menu-right > .container-fluid .nav-item.dropdown:hover > .gr-megamenu-dropdown.center {\n  transform: translateX(-100%) translateY(-10px);\n  left: 100%;\n}\n\n.single-div:after {\n  position: absolute;\n  right: 0;\n  top: 62px;\n  bottom: 62px;\n  width: 1px;\n  content: \"\";\n  background: red;\n}\n\n.single-div:last-child:after {\n  background: transparent;\n}\n\n.single-div + .single-div:after {\n  position: absolute;\n  left: 0;\n  top: 62px;\n  bottom: 62px;\n  width: 1px;\n  content: \"\";\n  background: red;\n}\n\n.header-cart {\n  position: relative;\n  font-size: 20px;\n  color: var(--color-texts);\n  margin-left: auto;\n  margin-right: 15px;\n  margin-right: 15px;\n}\n\n@media (min-width: 992px) {\n  .header-cart {\n    margin-left: 10px;\n  }\n}\n\n.header-cart span {\n  height: 20px;\n  width: 20px;\n  font-size: 12px;\n  border-radius: 500px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  position: absolute;\n  top: 0;\n  right: 0;\n  transform: translate(50%, -50%);\n  background: #68d585;\n  color: #fff;\n}\n\n.sticky-header:not(.mobile-sticky-enable) {\n  position: absolute !important;\n  top: 0;\n  right: 0;\n  width: 100%;\n  z-index: 999;\n}\n\n@media (min-width: 992px) {\n  .sticky-header:not(.mobile-sticky-enable) {\n    position: fixed !important;\n    transition: .4s;\n  }\n  .sticky-header:not(.mobile-sticky-enable).scrolling {\n    transform: translateY(-100%);\n    transition: .4s;\n  }\n  .sticky-header:not(.mobile-sticky-enable).reveal-header {\n    transform: translateY(0%);\n    box-shadow: 0 12px 34px -11px rgba(65, 62, 101, 0.1);\n    z-index: 1000;\n    background: #fff;\n  }\n}\n\n.sticky-header.mobile-sticky-enable {\n  top: 0;\n  right: 0;\n  width: 100%;\n  z-index: 999;\n  position: fixed !important;\n  transition: .4s;\n}\n\n.sticky-header.mobile-sticky-enable.scrolling {\n  transform: translateY(-100%);\n  transition: .4s;\n}\n\n.sticky-header.mobile-sticky-enable.reveal-header {\n  transform: translateY(0%);\n  box-shadow: 0 12px 34px -11px rgba(65, 62, 101, 0.1);\n  z-index: 9999;\n  background: #fff;\n}\n\n.main-menu {\n  /* ----------------------\r\n  Custom toggle arrow \r\n------------------------*/\n}\n\n@media (min-width: 992px) {\n  .main-menu {\n    display: flex;\n    justify-content: flex-end;\n  }\n}\n\n.main-menu .gr-toggle-arrow {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n}\n\n.main-menu .gr-toggle-arrow:after {\n  display: none;\n}\n\n.main-menu .gr-toggle-arrow i {\n  font-size: 24px;\n  width: auto;\n  line-height: 1;\n  height: auto;\n  font-weight: 900;\n  margin-left: 5px;\n  transition: .6s;\n}\n\n.main-menu .gr-toggle-arrow:hover i {\n  transform: rotate(-180deg);\n}\n\n.main-menu > li > .nav-link {\n  color: var(--color-texts) !important;\n  font-size: 15px;\n  font-weight: 700;\n}\n\n@media (min-width: 992px) {\n  .main-menu > li > .nav-link {\n    padding-top: 18px !important;\n    padding-bottom: 18px !important;\n    padding-left: 18px !important;\n    padding-right: 18px !important;\n  }\n}\n\n.main-menu > li > .nav-link:hover {\n  color: #473bf0 !important;\n}\n\n.main-menu > li:hover > .gr-toggle-arrow i {\n  transform: rotate(-180deg);\n}\n\n/*----- Dropdown styles\r\n-------------------------*/\n@media (min-width: 992px) {\n  .gr-menu-dropdown {\n    position: absolute;\n    min-width: 227px;\n    max-width: 227px;\n    box-shadow: 0 52px 54px rgba(65, 62, 101, 0.3);\n    border-radius: 8px;\n    border: 1px solid #E5E5E5;\n    background-color: #FFFFFF;\n    padding: 15px 0px;\n    top: 100%;\n    z-index: -99;\n    opacity: 0;\n    transition: opacity .4s,top .4s;\n    pointer-events: none;\n    left: 0;\n    right: auto;\n    border-radius: 0 0 10px 10px;\n    border: 1px solid #eae9f2;\n    background-color: #ffffff;\n    display: block;\n    border-top: 3px solid #473bf0;\n  }\n}\n\n.gr-menu-dropdown > .drop-menu-item {\n  color: #19191b;\n  font-size: 16px;\n  font-weight: 700;\n  letter-spacing: -0.5px;\n  padding-left: 30px;\n  padding-right: 30px;\n  padding-top: 10px;\n  padding-bottom: 10px;\n}\n\n.gr-menu-dropdown > .drop-menu-item > a {\n  color: inherit;\n}\n\n.gr-menu-dropdown > .drop-menu-item:hover > a {\n  color: #473bf0;\n}\n\n.gr-menu-dropdown > .drop-menu-item > .gr-menu-dropdown {\n  border-top-color: #68d585;\n}\n\n@media (min-width: 992px) {\n  .gr-menu-dropdown > .drop-menu-item > .gr-menu-dropdown {\n    top: 10px;\n    left: auto;\n    right: 0;\n    opacity: 0;\n    transform: translateX(110%);\n    transition: .4s;\n    pointer-events: none;\n    will-change: transform;\n  }\n}\n\n@media (min-width: 380px) {\n  .gr-menu-dropdown > .drop-menu-item > .gr-menu-dropdown > .drop-menu-item {\n    padding-left: 25px;\n    padding-right: 25px;\n  }\n}\n\n@media (min-width: 992px) {\n  .gr-menu-dropdown > .drop-menu-item > .gr-menu-dropdown > .drop-menu-item {\n    padding-left: 30px;\n    padding-right: 30px;\n  }\n}\n\n.gr-menu-dropdown.dropdown-right {\n  left: auto;\n  right: -90%;\n}\n\n/*----- Dropdown hover activation related styles\r\n-------------------------------------------------*/\n@media (min-width: 992px) {\n  .nav-item.dropdown {\n    position: relative;\n    z-index: 99;\n  }\n}\n\n@media (min-width: 992px) {\n  .nav-item.dropdown:hover > .gr-menu-dropdown {\n    top: 90%;\n    opacity: 1;\n    pointer-events: visible;\n  }\n}\n\n@media (min-width: 992px) {\n  .nav-item.dropdown:hover > .gr-megamenu-dropdown.center {\n    transform: translateX(-50%) translateY(-10px);\n    z-index: 99;\n    opacity: 1;\n    pointer-events: visible;\n  }\n}\n\n@media (min-width: 992px) {\n  .nav-item.dropdown:hover > .gr-megamenu-dropdown-2, .nav-item.dropdown:hover.triangle-shape:after, .nav-item.dropdown:hover.triangle-shape:before {\n    transform: translateX(-50%) translateY(-10px);\n    z-index: 99;\n    opacity: 1;\n    pointer-events: visible;\n  }\n}\n\n.drop-menu-item.dropdown {\n  position: relative;\n}\n\n.drop-menu-item.dropdown > .gr-toggle-arrow i {\n  transform: rotate(-90deg);\n}\n\n@media (min-width: 992px) {\n  .drop-menu-item.dropdown:hover > .gr-menu-dropdown {\n    top: 10px;\n    opacity: 1;\n    pointer-events: visible;\n    transform: translateX(100%);\n  }\n}\n\n/*-----Mega Dropdown styles\r\n-------------------------*/\n.nav-item.dropdown.dropdown-mega {\n  z-index: 1;\n}\n\n@media (min-width: 992px) {\n  .nav-item.dropdown.dropdown-mega {\n    position: static;\n  }\n}\n\n.gr-megamenu-dropdown .dropdown-image-block {\n  max-height: 336px;\n}\n\n@media (min-width: 320px) and (max-width: 992px) {\n  .gr-megamenu-dropdown {\n    border: 0;\n  }\n}\n\n@media (min-width: 992px) {\n  .gr-megamenu-dropdown {\n    padding: 15px;\n    min-width: 925px;\n    left: 50%;\n    transform: translateX(-50%) translateY(10px);\n    will-change: transform;\n    top: 100%;\n    z-index: -99;\n    opacity: 0;\n    pointer-events: none;\n    transition: .4s opacity ,.4s transform;\n    box-shadow: 0 42px 54px rgba(0, 0, 0, 0.09);\n    border: 1px solid #e7e9ed;\n    border-radius: 8px;\n  }\n}\n\n@media (min-width: 1200px) {\n  .gr-megamenu-dropdown {\n    min-width: 1100px;\n  }\n}\n\n.gr-megamenu-dropdown .single-dropdown-block .mega-drop-menu-item {\n  padding-top: 14px;\n  padding-bottom: 14px;\n  display: block;\n}\n\n.gr-megamenu-dropdown .mega-drop-menu-item {\n  padding-top: 2.5px;\n  padding-bottom: 2.5px;\n  color: #13151C;\n  font-size: 15px;\n  font-weight: 700;\n}\n\n@media (min-width: 320px) and (max-width: 992px) {\n  .gr-megamenu-dropdown .mega-drop-menu-item {\n    border-top: 1px solid var(--border-color);\n    padding-left: 20px;\n    padding-bottom: 13px;\n    padding-top: 13px;\n  }\n}\n\n@media (min-width: 992px) {\n  .gr-megamenu-dropdown .mega-drop-menu-item {\n    padding-left: 10px;\n    padding-right: 10px;\n  }\n  .gr-megamenu-dropdown .mega-drop-menu-item:hover {\n    color: #473bf0 !important;\n  }\n}\n\n@media (min-width: 320px) and (max-width: 992px) {\n  .gr-megamenu-dropdown [class*=\"col-\"] {\n    padding-left: 0;\n    padding-right: 0;\n  }\n  .gr-megamenu-dropdown [class*=\"row-\"] {\n    margin-left: 0;\n    margin-right: 0;\n  }\n}\n\n@media (min-width: 320px) and (max-width: 992px) {\n  .gr-megamenu-dropdown-2 {\n    border: 0;\n  }\n}\n\n@media (min-width: 992px) {\n  .gr-megamenu-dropdown-2 {\n    box-shadow: 0 42px 54px rgba(0, 0, 0, 0.09);\n    padding: 25px;\n    min-width: 956px;\n    left: 50%;\n    will-change: transform;\n    top: 100%;\n    transform: translateX(-50%) translateY(10px);\n    z-index: -99;\n    opacity: 0;\n    pointer-events: none;\n    transition: .4s opacity ,.4s transform;\n    box-shadow: 0 52px 54px rgba(65, 62, 101, 0.3);\n    border: 1px solid #e7e9ed;\n  }\n}\n\n@media (min-width: 1200px) {\n  .gr-megamenu-dropdown-2 {\n    min-width: 1100px;\n  }\n}\n\n.gr-megamenu-dropdown-2 .single-dropdown-block .mega-drop-menu-item {\n  padding-top: 10px;\n  padding-bottom: 10px;\n  display: block !important;\n}\n\n.gr-megamenu-dropdown-2 .mega-drop-menu-item {\n  padding-top: 2.5px;\n  padding-bottom: 2.5px;\n  color: #13151C;\n  font-size: 15px;\n  font-weight: 700;\n}\n\n@media (min-width: 320px) and (max-width: 992px) {\n  .gr-megamenu-dropdown-2 .mega-drop-menu-item {\n    border-top: 1px solid var(--border-color);\n    padding-left: 20px;\n    padding-bottom: 13px;\n    padding-top: 13px;\n  }\n}\n\n@media (min-width: 992px) {\n  .gr-megamenu-dropdown-2 .mega-drop-menu-item {\n    padding-left: 20px;\n    padding-right: 25px;\n    border-radius: 11px;\n    width: fit-content;\n  }\n  .gr-megamenu-dropdown-2 .mega-drop-menu-item:hover {\n    background: #f4f7fa;\n  }\n}\n\n@media (min-width: 992px) {\n  .gr-megamenu-dropdown-2 .mega-drop-menu-item:hover {\n    color: #473bf0 !important;\n  }\n}\n\n.gr-megamenu-dropdown-2 .mega-drop-menu-item .single-menu-title {\n  color: #13151C;\n  margin-bottom: 3px;\n}\n\n.gr-megamenu-dropdown-2 .mega-drop-menu-item p {\n  margin-bottom: 0;\n  font-weight: normal;\n}\n\n@media (min-width: 320px) and (max-width: 992px) {\n  .gr-megamenu-dropdown-2 [class*=\"col-\"] {\n    padding-left: 0;\n    padding-right: 0;\n  }\n  .gr-megamenu-dropdown-2 [class*=\"row-\"] {\n    margin-left: 0;\n    margin-right: 0;\n  }\n}\n\n.dropdown-mega.triangle-shape {\n  position: relative;\n}\n\n.dropdown-mega.triangle-shape:before {\n  position: absolute;\n  bottom: -7px;\n  right: -2px;\n  border: 15px solid;\n  border-color: transparent transparent #fff transparent;\n  transform: translateX(-50%) translateY(10px);\n  z-index: 100 !important;\n  opacity: 0;\n  transition: .4s;\n  pointer-events: none !important;\n}\n\n@media (min-width: 992px) {\n  .dropdown-mega.triangle-shape:before {\n    content: \"\";\n  }\n}\n\n.dropdown-mega.triangle-shape:after {\n  position: absolute;\n  bottom: -4px;\n  right: 0;\n  border: 14px solid;\n  border-color: transparent transparent #e7e9ed transparent;\n  transform: translateX(-50%) translateY(10px);\n  z-index: 99 !important;\n  opacity: 0;\n  transition: .4s;\n  pointer-events: none !important;\n}\n\n@media (min-width: 992px) {\n  .dropdown-mega.triangle-shape:after {\n    content: \"\";\n  }\n}\n\n@media (min-width: 992px) {\n  .offcanvas-active.navbar-expand-lg .btn-close-off-canvas {\n    display: none;\n  }\n}\n\n@media (min-width: 320px) and (max-width: 992px) {\n  .offcanvas-active.navbar-expand-lg .navbar-collapse {\n    display: block;\n    position: fixed;\n    top: 0;\n    background: var(--bg);\n    left: -100%;\n    padding-left: 20px;\n    padding-right: 20px;\n    height: 100%;\n    transition: left .4s;\n    z-index: 999999;\n    box-shadow: 0 0 87px 0 rgba(0, 0, 0, 0.09);\n    padding-top: 50px;\n    width: 250px;\n    overflow-y: auto;\n  }\n}\n\n@media (min-width: 320px) and (max-width: 992px) and (min-width: 380px) {\n  .offcanvas-active.navbar-expand-lg .navbar-collapse {\n    width: 300px;\n  }\n}\n\n@media (min-width: 320px) and (max-width: 992px) and (min-width: 576px) {\n  .offcanvas-active.navbar-expand-lg .navbar-collapse {\n    width: 350px;\n  }\n}\n\n@media (min-width: 320px) and (max-width: 992px) {\n  .offcanvas-active.navbar-expand-lg .navbar-collapse.show {\n    left: 0%;\n  }\n  .offcanvas-active.navbar-expand-lg .navbar-collapse.show ~ .btn-close-off-canvas .icon-burger {\n    display: block;\n  }\n  .offcanvas-active.navbar-expand-lg .navbar-collapse.collapsing {\n    transition: height 0s;\n    height: 100%;\n  }\n  .offcanvas-active.navbar-expand-lg .navbar-collapse::-webkit-scrollbar {\n    width: 8px;\n  }\n  .offcanvas-active.navbar-expand-lg .navbar-collapse::-webkit-scrollbar-track {\n    background: #fff;\n  }\n  .offcanvas-active.navbar-expand-lg .navbar-collapse::-webkit-scrollbar-thumb {\n    background-color: #473bf0;\n    outline: 1px solid slategrey;\n  }\n  .offcanvas-active.navbar-expand-lg .btn-close-off-canvas .icon-burger {\n    display: block;\n  }\n  .offcanvas-active.navbar-expand-lg .main-menu > li {\n    padding-bottom: 0px;\n    margin-bottom: 0px;\n    border-bottom: 1px solid var(--border-color);\n  }\n  .offcanvas-active.navbar-expand-lg .main-menu > li > .nav-link {\n    padding-bottom: 13px;\n    padding-top: 13px;\n  }\n  .offcanvas-active.navbar-expand-lg .main-menu > li .gr-menu-dropdown {\n    border: 0;\n    border-radius: 0;\n    min-width: auto;\n    padding: 0;\n  }\n  .offcanvas-active.navbar-expand-lg .main-menu > li .gr-menu-dropdown > li {\n    padding-top: 0;\n    padding-bottom: 0;\n    border-top: 1px solid var(--border-color);\n    padding-left: 20px;\n  }\n  .offcanvas-active.navbar-expand-lg .main-menu > li .gr-menu-dropdown > li a {\n    padding-top: 13px;\n    padding-bottom: 13px;\n  }\n  .offcanvas-active.navbar-expand-lg .main-menu > li:last-child {\n    border-bottom-color: transparent;\n  }\n  .offcanvas-active.navbar-expand-lg .main-menu li i {\n    margin-left: 8px;\n    position: relative;\n    top: 3px;\n  }\n  .offcanvas-active.navbar-expand-lg .main-menu li:hover > a {\n    color: #473bf0;\n  }\n  .offcanvas-active.navbar-expand-lg .main-menu a {\n    display: flex;\n  }\n}\n\n.navbar-toggler {\n  color: var(--color-texts-opacity) !important;\n  border-color: var(--color-texts-opacity) !important;\n}\n\n.hamburger-icon {\n  border-radius: 5px;\n  border-width: 2px;\n  padding: 3px 10px;\n}\n\n.hamburger-icon .hamburger {\n  font: inherit;\n  display: inline-block;\n  overflow: visible;\n  margin: 0;\n  padding: 2px 0px 0px;\n  cursor: pointer;\n  transition-timing-function: linear;\n  transition-duration: .15s;\n  transition-property: opacity,filter;\n  text-transform: none;\n  color: inherit;\n  border: 0;\n  background-color: transparent;\n}\n\n.hamburger-icon .hamburger .hamburger-box {\n  position: relative;\n  display: inline-block;\n  width: 20px;\n  height: 15px;\n}\n\n.hamburger-icon .hamburger .hamburger-box .hamburger-inner {\n  transition-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);\n  transition-duration: 75ms !important;\n  position: absolute;\n  transition-timing-function: ease;\n  transition-duration: .15s;\n  transition-property: transform;\n  border-radius: 4px;\n  background-color: var(--color-texts-opacity);\n  top: 50%;\n  display: block;\n  margin-top: -2px;\n  width: 20px;\n  height: 3px;\n}\n\n.hamburger-icon .hamburger .hamburger-box .hamburger-inner:before {\n  top: -6px;\n  transition: top 75ms ease .12s,opacity 75ms ease;\n}\n\n.hamburger-icon .hamburger .hamburger-box .hamburger-inner:after {\n  bottom: -6px;\n  transition: bottom 75ms ease 0.12s, transform 75ms cubic-bezier(0.55, 0.055, 0.675, 0.19);\n}\n\n.hamburger-icon .hamburger .hamburger-box .hamburger-inner:after, .hamburger-icon .hamburger .hamburger-box .hamburger-inner:before {\n  display: block;\n  content: \"\";\n  position: absolute;\n  width: 20px;\n  height: 3px;\n  border-radius: 4px;\n  background-color: var(--color-texts-opacity);\n}\n\n.hamburger-icon[aria-expanded=\"true\"] .hamburger .hamburger-box .hamburger-inner:after {\n  bottom: 0;\n  transition: bottom 75ms ease, transform 75ms cubic-bezier(0.215, 0.61, 0.355, 1) 0.12s;\n  transform: rotate(-90deg);\n}\n\n.hamburger-icon[aria-expanded=\"true\"] .hamburger .hamburger-box .hamburger-inner:before {\n  top: 0;\n  transition: top 75ms ease, opacity 75ms ease 0.12s !important;\n  opacity: 0;\n}\n\n.hamburger-icon[aria-expanded=\"true\"] .hamburger .hamburger-box .hamburger-inner {\n  transition-delay: .12s;\n  transition-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);\n  transform: rotate(45deg);\n}\n\n.hamburger-icon:focus {\n  outline: none;\n  box-shadow: none;\n}\n\n.offcanvas-btn-close {\n  position: absolute;\n  top: 0;\n  right: 0;\n  font-size: 22px;\n  width: 60px;\n  height: 50px;\n  display: flex;\n  justify-content: center;\n  border: none;\n  background: transparent;\n  font-weight: 700;\n}\n\n.offcanvas-btn-close i {\n  color: var(--color-texts) !important;\n}\n\n.gr-cross-icon {\n  transition-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);\n  transition-duration: 75ms !important;\n  position: absolute;\n  transition-timing-function: ease;\n  transition-duration: .15s;\n  transition-property: transform;\n  border-radius: 4px;\n  background-color: var(--color-texts-opacity);\n  top: 50%;\n  display: block;\n  margin-top: -2px;\n  height: 0;\n  width: 0;\n  left: 50%;\n  transform: translateX(-6.5px) rotate(45deg);\n}\n\n.gr-cross-icon:before {\n  top: 0;\n  transition: top 75ms ease .12s,opacity 75ms ease;\n}\n\n.gr-cross-icon:after {\n  bottom: -3px;\n  transition: bottom 75ms ease 0.12s, transform 75ms cubic-bezier(0.55, 0.055, 0.675, 0.19);\n  transform: rotate(-90deg);\n}\n\n.gr-cross-icon:after, .gr-cross-icon:before {\n  display: block;\n  content: \"\";\n  position: absolute;\n  width: 20px;\n  height: 3px;\n  border-radius: 4px;\n  background-color: var(--color-texts-opacity);\n}\n\n[data-pricing-dynamic][data-value-active=\"yearly\"] .dynamic-value:before {\n  display: inline-block;\n  content: attr(data-yearly);\n}\n\n[data-pricing-dynamic][data-value-active=\"yearly\"] [data-pricing-trigger] {\n  background: #473bf0;\n}\n\n[data-pricing-dynamic][data-value-active=\"yearly\"] [data-pricing-trigger] span {\n  left: calc(100% - 33px);\n}\n\n[data-pricing-dynamic][data-value-active=\"monthly\"] .dynamic-value:before {\n  display: inline-block;\n  content: attr(data-monthly);\n}\n\n.dynamic-value:before {\n  display: inline-block;\n  content: attr(data-active);\n}\n\n.static-value:before {\n  display: inline-block;\n  content: attr(data-active);\n}\n\n.product-details-v-slider .slick-list {\n  margin: 0 -5px;\n}\n\n@media (min-width: 768px) {\n  .product-details-v-slider .slick-list {\n    margin: -5px 0;\n  }\n}\n\n@media (min-width: 992px) {\n  .product-details-v-slider .slick-list {\n    margin: -5px 0;\n  }\n}\n\n.product-details-v-slider .single-slide {\n  border: 1px solid var(--border-color);\n  background: var(--bg);\n  margin: 0 5px;\n  border-radius: 10px;\n}\n\n@media (min-width: 768px) {\n  .product-details-v-slider .single-slide {\n    margin: 5px 0;\n  }\n}\n\n@media (min-width: 992px) {\n  .product-details-v-slider .single-slide {\n    margin: 5px 0;\n  }\n}\n\n.product-details-v-slider .single-slide img {\n  width: 100%;\n}\n\n.product-details-slider .slick-list {\n  margin: 0 -15px;\n}\n\n.product-details-slider .single-slide {\n  border: 1px solid var(--border-color);\n  background: var(--bg);\n  padding: 30px;\n  margin: 0 15px;\n  border-radius: 10px;\n}\n\n.product-details-slider .single-slide img {\n  width: 100%;\n}\n\n.slick-slide:focus {\n  outline: none;\n}\n\n.job-feature-slider .slick-list {\n  margin: 0 -15px;\n  padding-bottom: 45px;\n}\n\n.job-feature-slider .single-slide {\n  margin: 0 15px;\n}\n\n.job-feature-slider-arrows {\n  display: flex;\n  border-radius: 10px;\n  background-color: #f4f7fa;\n  max-width: fit-content;\n}\n\n.job-feature-slider-arrows .slick-arrow {\n  font-size: 0;\n  border: 0;\n  background: transparent;\n  position: relative;\n}\n\n.job-feature-slider-arrows .slick-arrow::before {\n  font-family: \"Font Awesome 5 Free\";\n  font-size: 22px;\n  height: 44px;\n  width: 34px;\n  display: flex;\n  font-weight: 900;\n  justify-content: center;\n  align-items: center;\n  color: #000;\n}\n\n.job-feature-slider-arrows .slick-prev:before {\n  content: \"\\f060\";\n  opacity: 0.3;\n  transition: .4s;\n}\n\n.job-feature-slider-arrows .slick-prev:hover:before {\n  opacity: 1;\n}\n\n.job-feature-slider-arrows .slick-next:before {\n  content: \"\\f061\";\n}\n\n.gr-timeline-wrapper {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  flex-wrap: wrap;\n}\n\n@media (min-width: 400px) {\n  .gr-timeline-wrapper {\n    margin: 0 -15px;\n  }\n}\n\n@media (min-width: 992px) {\n  .gr-timeline-wrapper {\n    margin: 0 -35px;\n  }\n}\n\n.gr-timeline-wrapper .single-timeline-feature {\n  position: relative;\n  z-index: 1;\n  margin-bottom: 40px;\n  text-align: center;\n}\n\n@media (min-width: 400px) {\n  .gr-timeline-wrapper .single-timeline-feature {\n    padding-left: 15px;\n    padding-right: 15px;\n    min-width: calc(80% - 15px);\n    max-width: calc(80% - 15px);\n  }\n}\n\n@media (min-width: 500px) {\n  .gr-timeline-wrapper .single-timeline-feature {\n    padding-left: 15px;\n    padding-right: 15px;\n    min-width: calc(50% - 15px);\n    max-width: calc(50% - 15px);\n  }\n}\n\n@media (min-width: 768px) {\n  .gr-timeline-wrapper .single-timeline-feature {\n    margin-bottom: 0;\n    min-width: calc(33.333% - 15px);\n    max-width: calc(33.333% - 15px);\n  }\n}\n\n@media (min-width: 992px) {\n  .gr-timeline-wrapper .single-timeline-feature {\n    padding-left: 35px;\n    padding-right: 35px;\n    min-width: calc(33.333% - 35px);\n    max-width: calc(33.333% - 35px);\n  }\n}\n\n.gr-timeline-wrapper .single-timeline-feature:nth-child(2):before {\n  display: none;\n}\n\n@media (min-width: 768px) {\n  .gr-timeline-wrapper .single-timeline-feature:nth-child(2):before {\n    display: block;\n  }\n}\n\n.gr-timeline-wrapper .single-timeline-feature:last-child {\n  margin-bottom: 0;\n}\n\n.gr-timeline-wrapper .single-timeline-feature:last-child:before {\n  content: \"\";\n}\n\n.gr-timeline-wrapper .single-timeline-feature:before {\n  position: absolute;\n  content: url(../image/svg/l6-how-timeline.svg);\n  top: 20px;\n  left: 50%;\n  z-index: -1;\n  display: none;\n}\n\n@media (min-width: 500px) {\n  .gr-timeline-wrapper .single-timeline-feature:before {\n    width: 95%;\n    overflow: hidden;\n    display: block;\n  }\n}\n\n@media (min-width: 768px) {\n  .gr-timeline-wrapper .single-timeline-feature:before {\n    width: 95%;\n    overflow: hidden;\n  }\n}\n\n@media (min-width: 1200px) {\n  .gr-timeline-wrapper .single-timeline-feature:before {\n    width: 100%;\n    overflow: unset;\n  }\n}\n\n@media (min-width: 576px) {\n  .gr-nav-tabs {\n    border-bottom: 1px solid var(--border-color);\n  }\n}\n\n.gr-nav-tabs .nav-link {\n  padding-left: 0px;\n  padding-right: 0px;\n  margin-right: 50px;\n  padding-bottom: 20px;\n  border-bottom: 1px solid var(--border-color);\n  padding-top: 20px;\n  margin-bottom: -1px;\n  color: var(--texts-color);\n}\n\n@media (min-width: 576px) {\n  .gr-nav-tabs .nav-link {\n    margin-right: 100px;\n  }\n}\n\n.gr-nav-tabs .nav-link:last-child {\n  margin-right: 0;\n}\n\n.gr-nav-tabs .nav-link.active {\n  border-color: #473bf0;\n  color: #473bf0;\n}\n\n.gr-bg-primary-opacity-visible {\n  background-color: #473bf0;\n}\n\n.gr-bg-primary-opacity-1 {\n  background-color: rgba(71, 59, 240, 0.1);\n}\n\n.gr-bg-primary-opacity-2 {\n  background-color: rgba(71, 59, 240, 0.2);\n}\n\n.gr-bg-primary-opacity-3 {\n  background-color: rgba(71, 59, 240, 0.3);\n}\n\n.gr-bg-primary-opacity-4 {\n  background-color: rgba(71, 59, 240, 0.4);\n}\n\n.gr-bg-primary-opacity-5 {\n  background-color: rgba(71, 59, 240, 0.5);\n}\n\n.gr-bg-primary-opacity-6 {\n  background-color: rgba(71, 59, 240, 0.6);\n}\n\n.gr-bg-primary-opacity-7 {\n  background-color: rgba(71, 59, 240, 0.7);\n}\n\n.gr-bg-primary-opacity-8 {\n  background-color: rgba(71, 59, 240, 0.8);\n}\n\n.gr-bg-primary-opacity-9 {\n  background-color: rgba(71, 59, 240, 0.9);\n}\n\n.gr-color-primary-opacity-visible {\n  color: #473bf0;\n}\n\n.gr-color-primary-opacity-1 {\n  color: rgba(71, 59, 240, 0.1);\n}\n\n.gr-color-primary-opacity-2 {\n  color: rgba(71, 59, 240, 0.2);\n}\n\n.gr-color-primary-opacity-3 {\n  color: rgba(71, 59, 240, 0.3);\n}\n\n.gr-color-primary-opacity-4 {\n  color: rgba(71, 59, 240, 0.4);\n}\n\n.gr-color-primary-opacity-5 {\n  color: rgba(71, 59, 240, 0.5);\n}\n\n.gr-color-primary-opacity-6 {\n  color: rgba(71, 59, 240, 0.6);\n}\n\n.gr-color-primary-opacity-7 {\n  color: rgba(71, 59, 240, 0.7);\n}\n\n.gr-color-primary-opacity-8 {\n  color: rgba(71, 59, 240, 0.8);\n}\n\n.gr-color-primary-opacity-9 {\n  color: rgba(71, 59, 240, 0.9);\n}\n\n.gr-bg-secondary-opacity-visible {\n  background-color: #68d585;\n}\n\n.gr-bg-secondary-opacity-1 {\n  background-color: rgba(104, 213, 133, 0.1);\n}\n\n.gr-bg-secondary-opacity-2 {\n  background-color: rgba(104, 213, 133, 0.2);\n}\n\n.gr-bg-secondary-opacity-3 {\n  background-color: rgba(104, 213, 133, 0.3);\n}\n\n.gr-bg-secondary-opacity-4 {\n  background-color: rgba(104, 213, 133, 0.4);\n}\n\n.gr-bg-secondary-opacity-5 {\n  background-color: rgba(104, 213, 133, 0.5);\n}\n\n.gr-bg-secondary-opacity-6 {\n  background-color: rgba(104, 213, 133, 0.6);\n}\n\n.gr-bg-secondary-opacity-7 {\n  background-color: rgba(104, 213, 133, 0.7);\n}\n\n.gr-bg-secondary-opacity-8 {\n  background-color: rgba(104, 213, 133, 0.8);\n}\n\n.gr-bg-secondary-opacity-9 {\n  background-color: rgba(104, 213, 133, 0.9);\n}\n\n.gr-color-secondary-opacity-visible {\n  color: #68d585;\n}\n\n.gr-color-secondary-opacity-1 {\n  color: rgba(104, 213, 133, 0.1);\n}\n\n.gr-color-secondary-opacity-2 {\n  color: rgba(104, 213, 133, 0.2);\n}\n\n.gr-color-secondary-opacity-3 {\n  color: rgba(104, 213, 133, 0.3);\n}\n\n.gr-color-secondary-opacity-4 {\n  color: rgba(104, 213, 133, 0.4);\n}\n\n.gr-color-secondary-opacity-5 {\n  color: rgba(104, 213, 133, 0.5);\n}\n\n.gr-color-secondary-opacity-6 {\n  color: rgba(104, 213, 133, 0.6);\n}\n\n.gr-color-secondary-opacity-7 {\n  color: rgba(104, 213, 133, 0.7);\n}\n\n.gr-color-secondary-opacity-8 {\n  color: rgba(104, 213, 133, 0.8);\n}\n\n.gr-color-secondary-opacity-9 {\n  color: rgba(104, 213, 133, 0.9);\n}\n\n.gr-bg-success-opacity-visible {\n  background-color: #68d585;\n}\n\n.gr-bg-success-opacity-1 {\n  background-color: rgba(104, 213, 133, 0.1);\n}\n\n.gr-bg-success-opacity-2 {\n  background-color: rgba(104, 213, 133, 0.2);\n}\n\n.gr-bg-success-opacity-3 {\n  background-color: rgba(104, 213, 133, 0.3);\n}\n\n.gr-bg-success-opacity-4 {\n  background-color: rgba(104, 213, 133, 0.4);\n}\n\n.gr-bg-success-opacity-5 {\n  background-color: rgba(104, 213, 133, 0.5);\n}\n\n.gr-bg-success-opacity-6 {\n  background-color: rgba(104, 213, 133, 0.6);\n}\n\n.gr-bg-success-opacity-7 {\n  background-color: rgba(104, 213, 133, 0.7);\n}\n\n.gr-bg-success-opacity-8 {\n  background-color: rgba(104, 213, 133, 0.8);\n}\n\n.gr-bg-success-opacity-9 {\n  background-color: rgba(104, 213, 133, 0.9);\n}\n\n.gr-color-success-opacity-visible {\n  color: #68d585;\n}\n\n.gr-color-success-opacity-1 {\n  color: rgba(104, 213, 133, 0.1);\n}\n\n.gr-color-success-opacity-2 {\n  color: rgba(104, 213, 133, 0.2);\n}\n\n.gr-color-success-opacity-3 {\n  color: rgba(104, 213, 133, 0.3);\n}\n\n.gr-color-success-opacity-4 {\n  color: rgba(104, 213, 133, 0.4);\n}\n\n.gr-color-success-opacity-5 {\n  color: rgba(104, 213, 133, 0.5);\n}\n\n.gr-color-success-opacity-6 {\n  color: rgba(104, 213, 133, 0.6);\n}\n\n.gr-color-success-opacity-7 {\n  color: rgba(104, 213, 133, 0.7);\n}\n\n.gr-color-success-opacity-8 {\n  color: rgba(104, 213, 133, 0.8);\n}\n\n.gr-color-success-opacity-9 {\n  color: rgba(104, 213, 133, 0.9);\n}\n\n.gr-bg-info-opacity-visible {\n  background-color: #17a2b8;\n}\n\n.gr-bg-info-opacity-1 {\n  background-color: rgba(23, 162, 184, 0.1);\n}\n\n.gr-bg-info-opacity-2 {\n  background-color: rgba(23, 162, 184, 0.2);\n}\n\n.gr-bg-info-opacity-3 {\n  background-color: rgba(23, 162, 184, 0.3);\n}\n\n.gr-bg-info-opacity-4 {\n  background-color: rgba(23, 162, 184, 0.4);\n}\n\n.gr-bg-info-opacity-5 {\n  background-color: rgba(23, 162, 184, 0.5);\n}\n\n.gr-bg-info-opacity-6 {\n  background-color: rgba(23, 162, 184, 0.6);\n}\n\n.gr-bg-info-opacity-7 {\n  background-color: rgba(23, 162, 184, 0.7);\n}\n\n.gr-bg-info-opacity-8 {\n  background-color: rgba(23, 162, 184, 0.8);\n}\n\n.gr-bg-info-opacity-9 {\n  background-color: rgba(23, 162, 184, 0.9);\n}\n\n.gr-color-info-opacity-visible {\n  color: #17a2b8;\n}\n\n.gr-color-info-opacity-1 {\n  color: rgba(23, 162, 184, 0.1);\n}\n\n.gr-color-info-opacity-2 {\n  color: rgba(23, 162, 184, 0.2);\n}\n\n.gr-color-info-opacity-3 {\n  color: rgba(23, 162, 184, 0.3);\n}\n\n.gr-color-info-opacity-4 {\n  color: rgba(23, 162, 184, 0.4);\n}\n\n.gr-color-info-opacity-5 {\n  color: rgba(23, 162, 184, 0.5);\n}\n\n.gr-color-info-opacity-6 {\n  color: rgba(23, 162, 184, 0.6);\n}\n\n.gr-color-info-opacity-7 {\n  color: rgba(23, 162, 184, 0.7);\n}\n\n.gr-color-info-opacity-8 {\n  color: rgba(23, 162, 184, 0.8);\n}\n\n.gr-color-info-opacity-9 {\n  color: rgba(23, 162, 184, 0.9);\n}\n\n.gr-bg-warning-opacity-visible {\n  background-color: #f7e36d;\n}\n\n.gr-bg-warning-opacity-1 {\n  background-color: rgba(247, 227, 109, 0.1);\n}\n\n.gr-bg-warning-opacity-2 {\n  background-color: rgba(247, 227, 109, 0.2);\n}\n\n.gr-bg-warning-opacity-3 {\n  background-color: rgba(247, 227, 109, 0.3);\n}\n\n.gr-bg-warning-opacity-4 {\n  background-color: rgba(247, 227, 109, 0.4);\n}\n\n.gr-bg-warning-opacity-5 {\n  background-color: rgba(247, 227, 109, 0.5);\n}\n\n.gr-bg-warning-opacity-6 {\n  background-color: rgba(247, 227, 109, 0.6);\n}\n\n.gr-bg-warning-opacity-7 {\n  background-color: rgba(247, 227, 109, 0.7);\n}\n\n.gr-bg-warning-opacity-8 {\n  background-color: rgba(247, 227, 109, 0.8);\n}\n\n.gr-bg-warning-opacity-9 {\n  background-color: rgba(247, 227, 109, 0.9);\n}\n\n.gr-color-warning-opacity-visible {\n  color: #f7e36d;\n}\n\n.gr-color-warning-opacity-1 {\n  color: rgba(247, 227, 109, 0.1);\n}\n\n.gr-color-warning-opacity-2 {\n  color: rgba(247, 227, 109, 0.2);\n}\n\n.gr-color-warning-opacity-3 {\n  color: rgba(247, 227, 109, 0.3);\n}\n\n.gr-color-warning-opacity-4 {\n  color: rgba(247, 227, 109, 0.4);\n}\n\n.gr-color-warning-opacity-5 {\n  color: rgba(247, 227, 109, 0.5);\n}\n\n.gr-color-warning-opacity-6 {\n  color: rgba(247, 227, 109, 0.6);\n}\n\n.gr-color-warning-opacity-7 {\n  color: rgba(247, 227, 109, 0.7);\n}\n\n.gr-color-warning-opacity-8 {\n  color: rgba(247, 227, 109, 0.8);\n}\n\n.gr-color-warning-opacity-9 {\n  color: rgba(247, 227, 109, 0.9);\n}\n\n.gr-bg-danger-opacity-visible {\n  background-color: #f64b4b;\n}\n\n.gr-bg-danger-opacity-1 {\n  background-color: rgba(246, 75, 75, 0.1);\n}\n\n.gr-bg-danger-opacity-2 {\n  background-color: rgba(246, 75, 75, 0.2);\n}\n\n.gr-bg-danger-opacity-3 {\n  background-color: rgba(246, 75, 75, 0.3);\n}\n\n.gr-bg-danger-opacity-4 {\n  background-color: rgba(246, 75, 75, 0.4);\n}\n\n.gr-bg-danger-opacity-5 {\n  background-color: rgba(246, 75, 75, 0.5);\n}\n\n.gr-bg-danger-opacity-6 {\n  background-color: rgba(246, 75, 75, 0.6);\n}\n\n.gr-bg-danger-opacity-7 {\n  background-color: rgba(246, 75, 75, 0.7);\n}\n\n.gr-bg-danger-opacity-8 {\n  background-color: rgba(246, 75, 75, 0.8);\n}\n\n.gr-bg-danger-opacity-9 {\n  background-color: rgba(246, 75, 75, 0.9);\n}\n\n.gr-color-danger-opacity-visible {\n  color: #f64b4b;\n}\n\n.gr-color-danger-opacity-1 {\n  color: rgba(246, 75, 75, 0.1);\n}\n\n.gr-color-danger-opacity-2 {\n  color: rgba(246, 75, 75, 0.2);\n}\n\n.gr-color-danger-opacity-3 {\n  color: rgba(246, 75, 75, 0.3);\n}\n\n.gr-color-danger-opacity-4 {\n  color: rgba(246, 75, 75, 0.4);\n}\n\n.gr-color-danger-opacity-5 {\n  color: rgba(246, 75, 75, 0.5);\n}\n\n.gr-color-danger-opacity-6 {\n  color: rgba(246, 75, 75, 0.6);\n}\n\n.gr-color-danger-opacity-7 {\n  color: rgba(246, 75, 75, 0.7);\n}\n\n.gr-color-danger-opacity-8 {\n  color: rgba(246, 75, 75, 0.8);\n}\n\n.gr-color-danger-opacity-9 {\n  color: rgba(246, 75, 75, 0.9);\n}\n\n.gr-bg-light-opacity-visible {\n  background-color: #f8f9fa;\n}\n\n.gr-bg-light-opacity-1 {\n  background-color: rgba(248, 249, 250, 0.1);\n}\n\n.gr-bg-light-opacity-2 {\n  background-color: rgba(248, 249, 250, 0.2);\n}\n\n.gr-bg-light-opacity-3 {\n  background-color: rgba(248, 249, 250, 0.3);\n}\n\n.gr-bg-light-opacity-4 {\n  background-color: rgba(248, 249, 250, 0.4);\n}\n\n.gr-bg-light-opacity-5 {\n  background-color: rgba(248, 249, 250, 0.5);\n}\n\n.gr-bg-light-opacity-6 {\n  background-color: rgba(248, 249, 250, 0.6);\n}\n\n.gr-bg-light-opacity-7 {\n  background-color: rgba(248, 249, 250, 0.7);\n}\n\n.gr-bg-light-opacity-8 {\n  background-color: rgba(248, 249, 250, 0.8);\n}\n\n.gr-bg-light-opacity-9 {\n  background-color: rgba(248, 249, 250, 0.9);\n}\n\n.gr-color-light-opacity-visible {\n  color: #f8f9fa;\n}\n\n.gr-color-light-opacity-1 {\n  color: rgba(248, 249, 250, 0.1);\n}\n\n.gr-color-light-opacity-2 {\n  color: rgba(248, 249, 250, 0.2);\n}\n\n.gr-color-light-opacity-3 {\n  color: rgba(248, 249, 250, 0.3);\n}\n\n.gr-color-light-opacity-4 {\n  color: rgba(248, 249, 250, 0.4);\n}\n\n.gr-color-light-opacity-5 {\n  color: rgba(248, 249, 250, 0.5);\n}\n\n.gr-color-light-opacity-6 {\n  color: rgba(248, 249, 250, 0.6);\n}\n\n.gr-color-light-opacity-7 {\n  color: rgba(248, 249, 250, 0.7);\n}\n\n.gr-color-light-opacity-8 {\n  color: rgba(248, 249, 250, 0.8);\n}\n\n.gr-color-light-opacity-9 {\n  color: rgba(248, 249, 250, 0.9);\n}\n\n.gr-bg-dark-opacity-visible {\n  background-color: #343a40;\n}\n\n.gr-bg-dark-opacity-1 {\n  background-color: rgba(52, 58, 64, 0.1);\n}\n\n.gr-bg-dark-opacity-2 {\n  background-color: rgba(52, 58, 64, 0.2);\n}\n\n.gr-bg-dark-opacity-3 {\n  background-color: rgba(52, 58, 64, 0.3);\n}\n\n.gr-bg-dark-opacity-4 {\n  background-color: rgba(52, 58, 64, 0.4);\n}\n\n.gr-bg-dark-opacity-5 {\n  background-color: rgba(52, 58, 64, 0.5);\n}\n\n.gr-bg-dark-opacity-6 {\n  background-color: rgba(52, 58, 64, 0.6);\n}\n\n.gr-bg-dark-opacity-7 {\n  background-color: rgba(52, 58, 64, 0.7);\n}\n\n.gr-bg-dark-opacity-8 {\n  background-color: rgba(52, 58, 64, 0.8);\n}\n\n.gr-bg-dark-opacity-9 {\n  background-color: rgba(52, 58, 64, 0.9);\n}\n\n.gr-color-dark-opacity-visible {\n  color: #343a40;\n}\n\n.gr-color-dark-opacity-1 {\n  color: rgba(52, 58, 64, 0.1);\n}\n\n.gr-color-dark-opacity-2 {\n  color: rgba(52, 58, 64, 0.2);\n}\n\n.gr-color-dark-opacity-3 {\n  color: rgba(52, 58, 64, 0.3);\n}\n\n.gr-color-dark-opacity-4 {\n  color: rgba(52, 58, 64, 0.4);\n}\n\n.gr-color-dark-opacity-5 {\n  color: rgba(52, 58, 64, 0.5);\n}\n\n.gr-color-dark-opacity-6 {\n  color: rgba(52, 58, 64, 0.6);\n}\n\n.gr-color-dark-opacity-7 {\n  color: rgba(52, 58, 64, 0.7);\n}\n\n.gr-color-dark-opacity-8 {\n  color: rgba(52, 58, 64, 0.8);\n}\n\n.gr-color-dark-opacity-9 {\n  color: rgba(52, 58, 64, 0.9);\n}\n\n.gr-bg-red-opacity-visible {\n  background-color: #f64b4b;\n}\n\n.gr-bg-red-opacity-1 {\n  background-color: rgba(246, 75, 75, 0.1);\n}\n\n.gr-bg-red-opacity-2 {\n  background-color: rgba(246, 75, 75, 0.2);\n}\n\n.gr-bg-red-opacity-3 {\n  background-color: rgba(246, 75, 75, 0.3);\n}\n\n.gr-bg-red-opacity-4 {\n  background-color: rgba(246, 75, 75, 0.4);\n}\n\n.gr-bg-red-opacity-5 {\n  background-color: rgba(246, 75, 75, 0.5);\n}\n\n.gr-bg-red-opacity-6 {\n  background-color: rgba(246, 75, 75, 0.6);\n}\n\n.gr-bg-red-opacity-7 {\n  background-color: rgba(246, 75, 75, 0.7);\n}\n\n.gr-bg-red-opacity-8 {\n  background-color: rgba(246, 75, 75, 0.8);\n}\n\n.gr-bg-red-opacity-9 {\n  background-color: rgba(246, 75, 75, 0.9);\n}\n\n.gr-color-red-opacity-visible {\n  color: #f64b4b;\n}\n\n.gr-color-red-opacity-1 {\n  color: rgba(246, 75, 75, 0.1);\n}\n\n.gr-color-red-opacity-2 {\n  color: rgba(246, 75, 75, 0.2);\n}\n\n.gr-color-red-opacity-3 {\n  color: rgba(246, 75, 75, 0.3);\n}\n\n.gr-color-red-opacity-4 {\n  color: rgba(246, 75, 75, 0.4);\n}\n\n.gr-color-red-opacity-5 {\n  color: rgba(246, 75, 75, 0.5);\n}\n\n.gr-color-red-opacity-6 {\n  color: rgba(246, 75, 75, 0.6);\n}\n\n.gr-color-red-opacity-7 {\n  color: rgba(246, 75, 75, 0.7);\n}\n\n.gr-color-red-opacity-8 {\n  color: rgba(246, 75, 75, 0.8);\n}\n\n.gr-color-red-opacity-9 {\n  color: rgba(246, 75, 75, 0.9);\n}\n\n.gr-bg-green-opacity-visible {\n  background-color: #68d585;\n}\n\n.gr-bg-green-opacity-1 {\n  background-color: rgba(104, 213, 133, 0.1);\n}\n\n.gr-bg-green-opacity-2 {\n  background-color: rgba(104, 213, 133, 0.2);\n}\n\n.gr-bg-green-opacity-3 {\n  background-color: rgba(104, 213, 133, 0.3);\n}\n\n.gr-bg-green-opacity-4 {\n  background-color: rgba(104, 213, 133, 0.4);\n}\n\n.gr-bg-green-opacity-5 {\n  background-color: rgba(104, 213, 133, 0.5);\n}\n\n.gr-bg-green-opacity-6 {\n  background-color: rgba(104, 213, 133, 0.6);\n}\n\n.gr-bg-green-opacity-7 {\n  background-color: rgba(104, 213, 133, 0.7);\n}\n\n.gr-bg-green-opacity-8 {\n  background-color: rgba(104, 213, 133, 0.8);\n}\n\n.gr-bg-green-opacity-9 {\n  background-color: rgba(104, 213, 133, 0.9);\n}\n\n.gr-color-green-opacity-visible {\n  color: #68d585;\n}\n\n.gr-color-green-opacity-1 {\n  color: rgba(104, 213, 133, 0.1);\n}\n\n.gr-color-green-opacity-2 {\n  color: rgba(104, 213, 133, 0.2);\n}\n\n.gr-color-green-opacity-3 {\n  color: rgba(104, 213, 133, 0.3);\n}\n\n.gr-color-green-opacity-4 {\n  color: rgba(104, 213, 133, 0.4);\n}\n\n.gr-color-green-opacity-5 {\n  color: rgba(104, 213, 133, 0.5);\n}\n\n.gr-color-green-opacity-6 {\n  color: rgba(104, 213, 133, 0.6);\n}\n\n.gr-color-green-opacity-7 {\n  color: rgba(104, 213, 133, 0.7);\n}\n\n.gr-color-green-opacity-8 {\n  color: rgba(104, 213, 133, 0.8);\n}\n\n.gr-color-green-opacity-9 {\n  color: rgba(104, 213, 133, 0.9);\n}\n\n.gr-bg-green-shamrock-opacity-visible {\n  background-color: #2bd67b;\n}\n\n.gr-bg-green-shamrock-opacity-1 {\n  background-color: rgba(43, 214, 123, 0.1);\n}\n\n.gr-bg-green-shamrock-opacity-2 {\n  background-color: rgba(43, 214, 123, 0.2);\n}\n\n.gr-bg-green-shamrock-opacity-3 {\n  background-color: rgba(43, 214, 123, 0.3);\n}\n\n.gr-bg-green-shamrock-opacity-4 {\n  background-color: rgba(43, 214, 123, 0.4);\n}\n\n.gr-bg-green-shamrock-opacity-5 {\n  background-color: rgba(43, 214, 123, 0.5);\n}\n\n.gr-bg-green-shamrock-opacity-6 {\n  background-color: rgba(43, 214, 123, 0.6);\n}\n\n.gr-bg-green-shamrock-opacity-7 {\n  background-color: rgba(43, 214, 123, 0.7);\n}\n\n.gr-bg-green-shamrock-opacity-8 {\n  background-color: rgba(43, 214, 123, 0.8);\n}\n\n.gr-bg-green-shamrock-opacity-9 {\n  background-color: rgba(43, 214, 123, 0.9);\n}\n\n.gr-color-green-shamrock-opacity-visible {\n  color: #2bd67b;\n}\n\n.gr-color-green-shamrock-opacity-1 {\n  color: rgba(43, 214, 123, 0.1);\n}\n\n.gr-color-green-shamrock-opacity-2 {\n  color: rgba(43, 214, 123, 0.2);\n}\n\n.gr-color-green-shamrock-opacity-3 {\n  color: rgba(43, 214, 123, 0.3);\n}\n\n.gr-color-green-shamrock-opacity-4 {\n  color: rgba(43, 214, 123, 0.4);\n}\n\n.gr-color-green-shamrock-opacity-5 {\n  color: rgba(43, 214, 123, 0.5);\n}\n\n.gr-color-green-shamrock-opacity-6 {\n  color: rgba(43, 214, 123, 0.6);\n}\n\n.gr-color-green-shamrock-opacity-7 {\n  color: rgba(43, 214, 123, 0.7);\n}\n\n.gr-color-green-shamrock-opacity-8 {\n  color: rgba(43, 214, 123, 0.8);\n}\n\n.gr-color-green-shamrock-opacity-9 {\n  color: rgba(43, 214, 123, 0.9);\n}\n\n.gr-bg-blue-opacity-visible {\n  background-color: #473bf0;\n}\n\n.gr-bg-blue-opacity-1 {\n  background-color: rgba(71, 59, 240, 0.1);\n}\n\n.gr-bg-blue-opacity-2 {\n  background-color: rgba(71, 59, 240, 0.2);\n}\n\n.gr-bg-blue-opacity-3 {\n  background-color: rgba(71, 59, 240, 0.3);\n}\n\n.gr-bg-blue-opacity-4 {\n  background-color: rgba(71, 59, 240, 0.4);\n}\n\n.gr-bg-blue-opacity-5 {\n  background-color: rgba(71, 59, 240, 0.5);\n}\n\n.gr-bg-blue-opacity-6 {\n  background-color: rgba(71, 59, 240, 0.6);\n}\n\n.gr-bg-blue-opacity-7 {\n  background-color: rgba(71, 59, 240, 0.7);\n}\n\n.gr-bg-blue-opacity-8 {\n  background-color: rgba(71, 59, 240, 0.8);\n}\n\n.gr-bg-blue-opacity-9 {\n  background-color: rgba(71, 59, 240, 0.9);\n}\n\n.gr-color-blue-opacity-visible {\n  color: #473bf0;\n}\n\n.gr-color-blue-opacity-1 {\n  color: rgba(71, 59, 240, 0.1);\n}\n\n.gr-color-blue-opacity-2 {\n  color: rgba(71, 59, 240, 0.2);\n}\n\n.gr-color-blue-opacity-3 {\n  color: rgba(71, 59, 240, 0.3);\n}\n\n.gr-color-blue-opacity-4 {\n  color: rgba(71, 59, 240, 0.4);\n}\n\n.gr-color-blue-opacity-5 {\n  color: rgba(71, 59, 240, 0.5);\n}\n\n.gr-color-blue-opacity-6 {\n  color: rgba(71, 59, 240, 0.6);\n}\n\n.gr-color-blue-opacity-7 {\n  color: rgba(71, 59, 240, 0.7);\n}\n\n.gr-color-blue-opacity-8 {\n  color: rgba(71, 59, 240, 0.8);\n}\n\n.gr-color-blue-opacity-9 {\n  color: rgba(71, 59, 240, 0.9);\n}\n\n.gr-bg-sky-blue-opacity-visible {\n  background-color: #1082e9;\n}\n\n.gr-bg-sky-blue-opacity-1 {\n  background-color: rgba(16, 130, 233, 0.1);\n}\n\n.gr-bg-sky-blue-opacity-2 {\n  background-color: rgba(16, 130, 233, 0.2);\n}\n\n.gr-bg-sky-blue-opacity-3 {\n  background-color: rgba(16, 130, 233, 0.3);\n}\n\n.gr-bg-sky-blue-opacity-4 {\n  background-color: rgba(16, 130, 233, 0.4);\n}\n\n.gr-bg-sky-blue-opacity-5 {\n  background-color: rgba(16, 130, 233, 0.5);\n}\n\n.gr-bg-sky-blue-opacity-6 {\n  background-color: rgba(16, 130, 233, 0.6);\n}\n\n.gr-bg-sky-blue-opacity-7 {\n  background-color: rgba(16, 130, 233, 0.7);\n}\n\n.gr-bg-sky-blue-opacity-8 {\n  background-color: rgba(16, 130, 233, 0.8);\n}\n\n.gr-bg-sky-blue-opacity-9 {\n  background-color: rgba(16, 130, 233, 0.9);\n}\n\n.gr-color-sky-blue-opacity-visible {\n  color: #1082e9;\n}\n\n.gr-color-sky-blue-opacity-1 {\n  color: rgba(16, 130, 233, 0.1);\n}\n\n.gr-color-sky-blue-opacity-2 {\n  color: rgba(16, 130, 233, 0.2);\n}\n\n.gr-color-sky-blue-opacity-3 {\n  color: rgba(16, 130, 233, 0.3);\n}\n\n.gr-color-sky-blue-opacity-4 {\n  color: rgba(16, 130, 233, 0.4);\n}\n\n.gr-color-sky-blue-opacity-5 {\n  color: rgba(16, 130, 233, 0.5);\n}\n\n.gr-color-sky-blue-opacity-6 {\n  color: rgba(16, 130, 233, 0.6);\n}\n\n.gr-color-sky-blue-opacity-7 {\n  color: rgba(16, 130, 233, 0.7);\n}\n\n.gr-color-sky-blue-opacity-8 {\n  color: rgba(16, 130, 233, 0.8);\n}\n\n.gr-color-sky-blue-opacity-9 {\n  color: rgba(16, 130, 233, 0.9);\n}\n\n.gr-bg-yellow-opacity-visible {\n  background-color: #f7e36d;\n}\n\n.gr-bg-yellow-opacity-1 {\n  background-color: rgba(247, 227, 109, 0.1);\n}\n\n.gr-bg-yellow-opacity-2 {\n  background-color: rgba(247, 227, 109, 0.2);\n}\n\n.gr-bg-yellow-opacity-3 {\n  background-color: rgba(247, 227, 109, 0.3);\n}\n\n.gr-bg-yellow-opacity-4 {\n  background-color: rgba(247, 227, 109, 0.4);\n}\n\n.gr-bg-yellow-opacity-5 {\n  background-color: rgba(247, 227, 109, 0.5);\n}\n\n.gr-bg-yellow-opacity-6 {\n  background-color: rgba(247, 227, 109, 0.6);\n}\n\n.gr-bg-yellow-opacity-7 {\n  background-color: rgba(247, 227, 109, 0.7);\n}\n\n.gr-bg-yellow-opacity-8 {\n  background-color: rgba(247, 227, 109, 0.8);\n}\n\n.gr-bg-yellow-opacity-9 {\n  background-color: rgba(247, 227, 109, 0.9);\n}\n\n.gr-color-yellow-opacity-visible {\n  color: #f7e36d;\n}\n\n.gr-color-yellow-opacity-1 {\n  color: rgba(247, 227, 109, 0.1);\n}\n\n.gr-color-yellow-opacity-2 {\n  color: rgba(247, 227, 109, 0.2);\n}\n\n.gr-color-yellow-opacity-3 {\n  color: rgba(247, 227, 109, 0.3);\n}\n\n.gr-color-yellow-opacity-4 {\n  color: rgba(247, 227, 109, 0.4);\n}\n\n.gr-color-yellow-opacity-5 {\n  color: rgba(247, 227, 109, 0.5);\n}\n\n.gr-color-yellow-opacity-6 {\n  color: rgba(247, 227, 109, 0.6);\n}\n\n.gr-color-yellow-opacity-7 {\n  color: rgba(247, 227, 109, 0.7);\n}\n\n.gr-color-yellow-opacity-8 {\n  color: rgba(247, 227, 109, 0.8);\n}\n\n.gr-color-yellow-opacity-9 {\n  color: rgba(247, 227, 109, 0.9);\n}\n\n.gr-bg-yellow-orange-opacity-visible {\n  background-color: #fcad38;\n}\n\n.gr-bg-yellow-orange-opacity-1 {\n  background-color: rgba(252, 173, 56, 0.1);\n}\n\n.gr-bg-yellow-orange-opacity-2 {\n  background-color: rgba(252, 173, 56, 0.2);\n}\n\n.gr-bg-yellow-orange-opacity-3 {\n  background-color: rgba(252, 173, 56, 0.3);\n}\n\n.gr-bg-yellow-orange-opacity-4 {\n  background-color: rgba(252, 173, 56, 0.4);\n}\n\n.gr-bg-yellow-orange-opacity-5 {\n  background-color: rgba(252, 173, 56, 0.5);\n}\n\n.gr-bg-yellow-orange-opacity-6 {\n  background-color: rgba(252, 173, 56, 0.6);\n}\n\n.gr-bg-yellow-orange-opacity-7 {\n  background-color: rgba(252, 173, 56, 0.7);\n}\n\n.gr-bg-yellow-orange-opacity-8 {\n  background-color: rgba(252, 173, 56, 0.8);\n}\n\n.gr-bg-yellow-orange-opacity-9 {\n  background-color: rgba(252, 173, 56, 0.9);\n}\n\n.gr-color-yellow-orange-opacity-visible {\n  color: #fcad38;\n}\n\n.gr-color-yellow-orange-opacity-1 {\n  color: rgba(252, 173, 56, 0.1);\n}\n\n.gr-color-yellow-orange-opacity-2 {\n  color: rgba(252, 173, 56, 0.2);\n}\n\n.gr-color-yellow-orange-opacity-3 {\n  color: rgba(252, 173, 56, 0.3);\n}\n\n.gr-color-yellow-orange-opacity-4 {\n  color: rgba(252, 173, 56, 0.4);\n}\n\n.gr-color-yellow-orange-opacity-5 {\n  color: rgba(252, 173, 56, 0.5);\n}\n\n.gr-color-yellow-orange-opacity-6 {\n  color: rgba(252, 173, 56, 0.6);\n}\n\n.gr-color-yellow-orange-opacity-7 {\n  color: rgba(252, 173, 56, 0.7);\n}\n\n.gr-color-yellow-orange-opacity-8 {\n  color: rgba(252, 173, 56, 0.8);\n}\n\n.gr-color-yellow-orange-opacity-9 {\n  color: rgba(252, 173, 56, 0.9);\n}\n\n.gr-bg-blackish-blue-opacity-visible {\n  background-color: #13151c;\n}\n\n.gr-bg-blackish-blue-opacity-1 {\n  background-color: rgba(19, 21, 28, 0.1);\n}\n\n.gr-bg-blackish-blue-opacity-2 {\n  background-color: rgba(19, 21, 28, 0.2);\n}\n\n.gr-bg-blackish-blue-opacity-3 {\n  background-color: rgba(19, 21, 28, 0.3);\n}\n\n.gr-bg-blackish-blue-opacity-4 {\n  background-color: rgba(19, 21, 28, 0.4);\n}\n\n.gr-bg-blackish-blue-opacity-5 {\n  background-color: rgba(19, 21, 28, 0.5);\n}\n\n.gr-bg-blackish-blue-opacity-6 {\n  background-color: rgba(19, 21, 28, 0.6);\n}\n\n.gr-bg-blackish-blue-opacity-7 {\n  background-color: rgba(19, 21, 28, 0.7);\n}\n\n.gr-bg-blackish-blue-opacity-8 {\n  background-color: rgba(19, 21, 28, 0.8);\n}\n\n.gr-bg-blackish-blue-opacity-9 {\n  background-color: rgba(19, 21, 28, 0.9);\n}\n\n.gr-color-blackish-blue-opacity-visible {\n  color: #13151c;\n}\n\n.gr-color-blackish-blue-opacity-1 {\n  color: rgba(19, 21, 28, 0.1);\n}\n\n.gr-color-blackish-blue-opacity-2 {\n  color: rgba(19, 21, 28, 0.2);\n}\n\n.gr-color-blackish-blue-opacity-3 {\n  color: rgba(19, 21, 28, 0.3);\n}\n\n.gr-color-blackish-blue-opacity-4 {\n  color: rgba(19, 21, 28, 0.4);\n}\n\n.gr-color-blackish-blue-opacity-5 {\n  color: rgba(19, 21, 28, 0.5);\n}\n\n.gr-color-blackish-blue-opacity-6 {\n  color: rgba(19, 21, 28, 0.6);\n}\n\n.gr-color-blackish-blue-opacity-7 {\n  color: rgba(19, 21, 28, 0.7);\n}\n\n.gr-color-blackish-blue-opacity-8 {\n  color: rgba(19, 21, 28, 0.8);\n}\n\n.gr-color-blackish-blue-opacity-9 {\n  color: rgba(19, 21, 28, 0.9);\n}\n\n.gr-bg-black-opacity-visible {\n  background-color: black;\n}\n\n.gr-bg-black-opacity-1 {\n  background-color: rgba(0, 0, 0, 0.1);\n}\n\n.gr-bg-black-opacity-2 {\n  background-color: rgba(0, 0, 0, 0.2);\n}\n\n.gr-bg-black-opacity-3 {\n  background-color: rgba(0, 0, 0, 0.3);\n}\n\n.gr-bg-black-opacity-4 {\n  background-color: rgba(0, 0, 0, 0.4);\n}\n\n.gr-bg-black-opacity-5 {\n  background-color: rgba(0, 0, 0, 0.5);\n}\n\n.gr-bg-black-opacity-6 {\n  background-color: rgba(0, 0, 0, 0.6);\n}\n\n.gr-bg-black-opacity-7 {\n  background-color: rgba(0, 0, 0, 0.7);\n}\n\n.gr-bg-black-opacity-8 {\n  background-color: rgba(0, 0, 0, 0.8);\n}\n\n.gr-bg-black-opacity-9 {\n  background-color: rgba(0, 0, 0, 0.9);\n}\n\n.gr-color-black-opacity-visible {\n  color: black;\n}\n\n.gr-color-black-opacity-1 {\n  color: rgba(0, 0, 0, 0.1);\n}\n\n.gr-color-black-opacity-2 {\n  color: rgba(0, 0, 0, 0.2);\n}\n\n.gr-color-black-opacity-3 {\n  color: rgba(0, 0, 0, 0.3);\n}\n\n.gr-color-black-opacity-4 {\n  color: rgba(0, 0, 0, 0.4);\n}\n\n.gr-color-black-opacity-5 {\n  color: rgba(0, 0, 0, 0.5);\n}\n\n.gr-color-black-opacity-6 {\n  color: rgba(0, 0, 0, 0.6);\n}\n\n.gr-color-black-opacity-7 {\n  color: rgba(0, 0, 0, 0.7);\n}\n\n.gr-color-black-opacity-8 {\n  color: rgba(0, 0, 0, 0.8);\n}\n\n.gr-color-black-opacity-9 {\n  color: rgba(0, 0, 0, 0.9);\n}\n\n.gr-bg-mirage-opacity-visible {\n  background-color: #131829;\n}\n\n.gr-bg-mirage-opacity-1 {\n  background-color: rgba(19, 24, 41, 0.1);\n}\n\n.gr-bg-mirage-opacity-2 {\n  background-color: rgba(19, 24, 41, 0.2);\n}\n\n.gr-bg-mirage-opacity-3 {\n  background-color: rgba(19, 24, 41, 0.3);\n}\n\n.gr-bg-mirage-opacity-4 {\n  background-color: rgba(19, 24, 41, 0.4);\n}\n\n.gr-bg-mirage-opacity-5 {\n  background-color: rgba(19, 24, 41, 0.5);\n}\n\n.gr-bg-mirage-opacity-6 {\n  background-color: rgba(19, 24, 41, 0.6);\n}\n\n.gr-bg-mirage-opacity-7 {\n  background-color: rgba(19, 24, 41, 0.7);\n}\n\n.gr-bg-mirage-opacity-8 {\n  background-color: rgba(19, 24, 41, 0.8);\n}\n\n.gr-bg-mirage-opacity-9 {\n  background-color: rgba(19, 24, 41, 0.9);\n}\n\n.gr-color-mirage-opacity-visible {\n  color: #131829;\n}\n\n.gr-color-mirage-opacity-1 {\n  color: rgba(19, 24, 41, 0.1);\n}\n\n.gr-color-mirage-opacity-2 {\n  color: rgba(19, 24, 41, 0.2);\n}\n\n.gr-color-mirage-opacity-3 {\n  color: rgba(19, 24, 41, 0.3);\n}\n\n.gr-color-mirage-opacity-4 {\n  color: rgba(19, 24, 41, 0.4);\n}\n\n.gr-color-mirage-opacity-5 {\n  color: rgba(19, 24, 41, 0.5);\n}\n\n.gr-color-mirage-opacity-6 {\n  color: rgba(19, 24, 41, 0.6);\n}\n\n.gr-color-mirage-opacity-7 {\n  color: rgba(19, 24, 41, 0.7);\n}\n\n.gr-color-mirage-opacity-8 {\n  color: rgba(19, 24, 41, 0.8);\n}\n\n.gr-color-mirage-opacity-9 {\n  color: rgba(19, 24, 41, 0.9);\n}\n\n.gr-bg-mirage-2-opacity-visible {\n  background-color: #161c2d;\n}\n\n.gr-bg-mirage-2-opacity-1 {\n  background-color: rgba(22, 28, 45, 0.1);\n}\n\n.gr-bg-mirage-2-opacity-2 {\n  background-color: rgba(22, 28, 45, 0.2);\n}\n\n.gr-bg-mirage-2-opacity-3 {\n  background-color: rgba(22, 28, 45, 0.3);\n}\n\n.gr-bg-mirage-2-opacity-4 {\n  background-color: rgba(22, 28, 45, 0.4);\n}\n\n.gr-bg-mirage-2-opacity-5 {\n  background-color: rgba(22, 28, 45, 0.5);\n}\n\n.gr-bg-mirage-2-opacity-6 {\n  background-color: rgba(22, 28, 45, 0.6);\n}\n\n.gr-bg-mirage-2-opacity-7 {\n  background-color: rgba(22, 28, 45, 0.7);\n}\n\n.gr-bg-mirage-2-opacity-8 {\n  background-color: rgba(22, 28, 45, 0.8);\n}\n\n.gr-bg-mirage-2-opacity-9 {\n  background-color: rgba(22, 28, 45, 0.9);\n}\n\n.gr-color-mirage-2-opacity-visible {\n  color: #161c2d;\n}\n\n.gr-color-mirage-2-opacity-1 {\n  color: rgba(22, 28, 45, 0.1);\n}\n\n.gr-color-mirage-2-opacity-2 {\n  color: rgba(22, 28, 45, 0.2);\n}\n\n.gr-color-mirage-2-opacity-3 {\n  color: rgba(22, 28, 45, 0.3);\n}\n\n.gr-color-mirage-2-opacity-4 {\n  color: rgba(22, 28, 45, 0.4);\n}\n\n.gr-color-mirage-2-opacity-5 {\n  color: rgba(22, 28, 45, 0.5);\n}\n\n.gr-color-mirage-2-opacity-6 {\n  color: rgba(22, 28, 45, 0.6);\n}\n\n.gr-color-mirage-2-opacity-7 {\n  color: rgba(22, 28, 45, 0.7);\n}\n\n.gr-color-mirage-2-opacity-8 {\n  color: rgba(22, 28, 45, 0.8);\n}\n\n.gr-color-mirage-2-opacity-9 {\n  color: rgba(22, 28, 45, 0.9);\n}\n\n.gr-bg-white-opacity-visible {\n  background-color: white;\n}\n\n.gr-bg-white-opacity-1 {\n  background-color: rgba(255, 255, 255, 0.1);\n}\n\n.gr-bg-white-opacity-2 {\n  background-color: rgba(255, 255, 255, 0.2);\n}\n\n.gr-bg-white-opacity-3 {\n  background-color: rgba(255, 255, 255, 0.3);\n}\n\n.gr-bg-white-opacity-4 {\n  background-color: rgba(255, 255, 255, 0.4);\n}\n\n.gr-bg-white-opacity-5 {\n  background-color: rgba(255, 255, 255, 0.5);\n}\n\n.gr-bg-white-opacity-6 {\n  background-color: rgba(255, 255, 255, 0.6);\n}\n\n.gr-bg-white-opacity-7 {\n  background-color: rgba(255, 255, 255, 0.7);\n}\n\n.gr-bg-white-opacity-8 {\n  background-color: rgba(255, 255, 255, 0.8);\n}\n\n.gr-bg-white-opacity-9 {\n  background-color: rgba(255, 255, 255, 0.9);\n}\n\n.gr-color-white-opacity-visible {\n  color: white;\n}\n\n.gr-color-white-opacity-1 {\n  color: rgba(255, 255, 255, 0.1);\n}\n\n.gr-color-white-opacity-2 {\n  color: rgba(255, 255, 255, 0.2);\n}\n\n.gr-color-white-opacity-3 {\n  color: rgba(255, 255, 255, 0.3);\n}\n\n.gr-color-white-opacity-4 {\n  color: rgba(255, 255, 255, 0.4);\n}\n\n.gr-color-white-opacity-5 {\n  color: rgba(255, 255, 255, 0.5);\n}\n\n.gr-color-white-opacity-6 {\n  color: rgba(255, 255, 255, 0.6);\n}\n\n.gr-color-white-opacity-7 {\n  color: rgba(255, 255, 255, 0.7);\n}\n\n.gr-color-white-opacity-8 {\n  color: rgba(255, 255, 255, 0.8);\n}\n\n.gr-color-white-opacity-9 {\n  color: rgba(255, 255, 255, 0.9);\n}\n\n.gr-bg-smoke-opacity-visible {\n  background-color: #f8f8f8;\n}\n\n.gr-bg-smoke-opacity-1 {\n  background-color: rgba(248, 248, 248, 0.1);\n}\n\n.gr-bg-smoke-opacity-2 {\n  background-color: rgba(248, 248, 248, 0.2);\n}\n\n.gr-bg-smoke-opacity-3 {\n  background-color: rgba(248, 248, 248, 0.3);\n}\n\n.gr-bg-smoke-opacity-4 {\n  background-color: rgba(248, 248, 248, 0.4);\n}\n\n.gr-bg-smoke-opacity-5 {\n  background-color: rgba(248, 248, 248, 0.5);\n}\n\n.gr-bg-smoke-opacity-6 {\n  background-color: rgba(248, 248, 248, 0.6);\n}\n\n.gr-bg-smoke-opacity-7 {\n  background-color: rgba(248, 248, 248, 0.7);\n}\n\n.gr-bg-smoke-opacity-8 {\n  background-color: rgba(248, 248, 248, 0.8);\n}\n\n.gr-bg-smoke-opacity-9 {\n  background-color: rgba(248, 248, 248, 0.9);\n}\n\n.gr-color-smoke-opacity-visible {\n  color: #f8f8f8;\n}\n\n.gr-color-smoke-opacity-1 {\n  color: rgba(248, 248, 248, 0.1);\n}\n\n.gr-color-smoke-opacity-2 {\n  color: rgba(248, 248, 248, 0.2);\n}\n\n.gr-color-smoke-opacity-3 {\n  color: rgba(248, 248, 248, 0.3);\n}\n\n.gr-color-smoke-opacity-4 {\n  color: rgba(248, 248, 248, 0.4);\n}\n\n.gr-color-smoke-opacity-5 {\n  color: rgba(248, 248, 248, 0.5);\n}\n\n.gr-color-smoke-opacity-6 {\n  color: rgba(248, 248, 248, 0.6);\n}\n\n.gr-color-smoke-opacity-7 {\n  color: rgba(248, 248, 248, 0.7);\n}\n\n.gr-color-smoke-opacity-8 {\n  color: rgba(248, 248, 248, 0.8);\n}\n\n.gr-color-smoke-opacity-9 {\n  color: rgba(248, 248, 248, 0.9);\n}\n\n.gr-bg-storm-opacity-visible {\n  background-color: #7d818d;\n}\n\n.gr-bg-storm-opacity-1 {\n  background-color: rgba(125, 129, 141, 0.1);\n}\n\n.gr-bg-storm-opacity-2 {\n  background-color: rgba(125, 129, 141, 0.2);\n}\n\n.gr-bg-storm-opacity-3 {\n  background-color: rgba(125, 129, 141, 0.3);\n}\n\n.gr-bg-storm-opacity-4 {\n  background-color: rgba(125, 129, 141, 0.4);\n}\n\n.gr-bg-storm-opacity-5 {\n  background-color: rgba(125, 129, 141, 0.5);\n}\n\n.gr-bg-storm-opacity-6 {\n  background-color: rgba(125, 129, 141, 0.6);\n}\n\n.gr-bg-storm-opacity-7 {\n  background-color: rgba(125, 129, 141, 0.7);\n}\n\n.gr-bg-storm-opacity-8 {\n  background-color: rgba(125, 129, 141, 0.8);\n}\n\n.gr-bg-storm-opacity-9 {\n  background-color: rgba(125, 129, 141, 0.9);\n}\n\n.gr-color-storm-opacity-visible {\n  color: #7d818d;\n}\n\n.gr-color-storm-opacity-1 {\n  color: rgba(125, 129, 141, 0.1);\n}\n\n.gr-color-storm-opacity-2 {\n  color: rgba(125, 129, 141, 0.2);\n}\n\n.gr-color-storm-opacity-3 {\n  color: rgba(125, 129, 141, 0.3);\n}\n\n.gr-color-storm-opacity-4 {\n  color: rgba(125, 129, 141, 0.4);\n}\n\n.gr-color-storm-opacity-5 {\n  color: rgba(125, 129, 141, 0.5);\n}\n\n.gr-color-storm-opacity-6 {\n  color: rgba(125, 129, 141, 0.6);\n}\n\n.gr-color-storm-opacity-7 {\n  color: rgba(125, 129, 141, 0.7);\n}\n\n.gr-color-storm-opacity-8 {\n  color: rgba(125, 129, 141, 0.8);\n}\n\n.gr-color-storm-opacity-9 {\n  color: rgba(125, 129, 141, 0.9);\n}\n\n.gr-bg-ghost-opacity-visible {\n  background-color: #fdfdff;\n}\n\n.gr-bg-ghost-opacity-1 {\n  background-color: rgba(253, 253, 255, 0.1);\n}\n\n.gr-bg-ghost-opacity-2 {\n  background-color: rgba(253, 253, 255, 0.2);\n}\n\n.gr-bg-ghost-opacity-3 {\n  background-color: rgba(253, 253, 255, 0.3);\n}\n\n.gr-bg-ghost-opacity-4 {\n  background-color: rgba(253, 253, 255, 0.4);\n}\n\n.gr-bg-ghost-opacity-5 {\n  background-color: rgba(253, 253, 255, 0.5);\n}\n\n.gr-bg-ghost-opacity-6 {\n  background-color: rgba(253, 253, 255, 0.6);\n}\n\n.gr-bg-ghost-opacity-7 {\n  background-color: rgba(253, 253, 255, 0.7);\n}\n\n.gr-bg-ghost-opacity-8 {\n  background-color: rgba(253, 253, 255, 0.8);\n}\n\n.gr-bg-ghost-opacity-9 {\n  background-color: rgba(253, 253, 255, 0.9);\n}\n\n.gr-color-ghost-opacity-visible {\n  color: #fdfdff;\n}\n\n.gr-color-ghost-opacity-1 {\n  color: rgba(253, 253, 255, 0.1);\n}\n\n.gr-color-ghost-opacity-2 {\n  color: rgba(253, 253, 255, 0.2);\n}\n\n.gr-color-ghost-opacity-3 {\n  color: rgba(253, 253, 255, 0.3);\n}\n\n.gr-color-ghost-opacity-4 {\n  color: rgba(253, 253, 255, 0.4);\n}\n\n.gr-color-ghost-opacity-5 {\n  color: rgba(253, 253, 255, 0.5);\n}\n\n.gr-color-ghost-opacity-6 {\n  color: rgba(253, 253, 255, 0.6);\n}\n\n.gr-color-ghost-opacity-7 {\n  color: rgba(253, 253, 255, 0.7);\n}\n\n.gr-color-ghost-opacity-8 {\n  color: rgba(253, 253, 255, 0.8);\n}\n\n.gr-color-ghost-opacity-9 {\n  color: rgba(253, 253, 255, 0.9);\n}\n\n.gr-bg-gray-1-opacity-visible {\n  background-color: #fcfdfe;\n}\n\n.gr-bg-gray-1-opacity-1 {\n  background-color: rgba(252, 253, 254, 0.1);\n}\n\n.gr-bg-gray-1-opacity-2 {\n  background-color: rgba(252, 253, 254, 0.2);\n}\n\n.gr-bg-gray-1-opacity-3 {\n  background-color: rgba(252, 253, 254, 0.3);\n}\n\n.gr-bg-gray-1-opacity-4 {\n  background-color: rgba(252, 253, 254, 0.4);\n}\n\n.gr-bg-gray-1-opacity-5 {\n  background-color: rgba(252, 253, 254, 0.5);\n}\n\n.gr-bg-gray-1-opacity-6 {\n  background-color: rgba(252, 253, 254, 0.6);\n}\n\n.gr-bg-gray-1-opacity-7 {\n  background-color: rgba(252, 253, 254, 0.7);\n}\n\n.gr-bg-gray-1-opacity-8 {\n  background-color: rgba(252, 253, 254, 0.8);\n}\n\n.gr-bg-gray-1-opacity-9 {\n  background-color: rgba(252, 253, 254, 0.9);\n}\n\n.gr-color-gray-1-opacity-visible {\n  color: #fcfdfe;\n}\n\n.gr-color-gray-1-opacity-1 {\n  color: rgba(252, 253, 254, 0.1);\n}\n\n.gr-color-gray-1-opacity-2 {\n  color: rgba(252, 253, 254, 0.2);\n}\n\n.gr-color-gray-1-opacity-3 {\n  color: rgba(252, 253, 254, 0.3);\n}\n\n.gr-color-gray-1-opacity-4 {\n  color: rgba(252, 253, 254, 0.4);\n}\n\n.gr-color-gray-1-opacity-5 {\n  color: rgba(252, 253, 254, 0.5);\n}\n\n.gr-color-gray-1-opacity-6 {\n  color: rgba(252, 253, 254, 0.6);\n}\n\n.gr-color-gray-1-opacity-7 {\n  color: rgba(252, 253, 254, 0.7);\n}\n\n.gr-color-gray-1-opacity-8 {\n  color: rgba(252, 253, 254, 0.8);\n}\n\n.gr-color-gray-1-opacity-9 {\n  color: rgba(252, 253, 254, 0.9);\n}\n\n.gr-bg-gray-2-opacity-visible {\n  background-color: #f4f7fa;\n}\n\n.gr-bg-gray-2-opacity-1 {\n  background-color: rgba(244, 247, 250, 0.1);\n}\n\n.gr-bg-gray-2-opacity-2 {\n  background-color: rgba(244, 247, 250, 0.2);\n}\n\n.gr-bg-gray-2-opacity-3 {\n  background-color: rgba(244, 247, 250, 0.3);\n}\n\n.gr-bg-gray-2-opacity-4 {\n  background-color: rgba(244, 247, 250, 0.4);\n}\n\n.gr-bg-gray-2-opacity-5 {\n  background-color: rgba(244, 247, 250, 0.5);\n}\n\n.gr-bg-gray-2-opacity-6 {\n  background-color: rgba(244, 247, 250, 0.6);\n}\n\n.gr-bg-gray-2-opacity-7 {\n  background-color: rgba(244, 247, 250, 0.7);\n}\n\n.gr-bg-gray-2-opacity-8 {\n  background-color: rgba(244, 247, 250, 0.8);\n}\n\n.gr-bg-gray-2-opacity-9 {\n  background-color: rgba(244, 247, 250, 0.9);\n}\n\n.gr-color-gray-2-opacity-visible {\n  color: #f4f7fa;\n}\n\n.gr-color-gray-2-opacity-1 {\n  color: rgba(244, 247, 250, 0.1);\n}\n\n.gr-color-gray-2-opacity-2 {\n  color: rgba(244, 247, 250, 0.2);\n}\n\n.gr-color-gray-2-opacity-3 {\n  color: rgba(244, 247, 250, 0.3);\n}\n\n.gr-color-gray-2-opacity-4 {\n  color: rgba(244, 247, 250, 0.4);\n}\n\n.gr-color-gray-2-opacity-5 {\n  color: rgba(244, 247, 250, 0.5);\n}\n\n.gr-color-gray-2-opacity-6 {\n  color: rgba(244, 247, 250, 0.6);\n}\n\n.gr-color-gray-2-opacity-7 {\n  color: rgba(244, 247, 250, 0.7);\n}\n\n.gr-color-gray-2-opacity-8 {\n  color: rgba(244, 247, 250, 0.8);\n}\n\n.gr-color-gray-2-opacity-9 {\n  color: rgba(244, 247, 250, 0.9);\n}\n\n.gr-bg-gray-3-opacity-visible {\n  background-color: #e7e9ed;\n}\n\n.gr-bg-gray-3-opacity-1 {\n  background-color: rgba(231, 233, 237, 0.1);\n}\n\n.gr-bg-gray-3-opacity-2 {\n  background-color: rgba(231, 233, 237, 0.2);\n}\n\n.gr-bg-gray-3-opacity-3 {\n  background-color: rgba(231, 233, 237, 0.3);\n}\n\n.gr-bg-gray-3-opacity-4 {\n  background-color: rgba(231, 233, 237, 0.4);\n}\n\n.gr-bg-gray-3-opacity-5 {\n  background-color: rgba(231, 233, 237, 0.5);\n}\n\n.gr-bg-gray-3-opacity-6 {\n  background-color: rgba(231, 233, 237, 0.6);\n}\n\n.gr-bg-gray-3-opacity-7 {\n  background-color: rgba(231, 233, 237, 0.7);\n}\n\n.gr-bg-gray-3-opacity-8 {\n  background-color: rgba(231, 233, 237, 0.8);\n}\n\n.gr-bg-gray-3-opacity-9 {\n  background-color: rgba(231, 233, 237, 0.9);\n}\n\n.gr-color-gray-3-opacity-visible {\n  color: #e7e9ed;\n}\n\n.gr-color-gray-3-opacity-1 {\n  color: rgba(231, 233, 237, 0.1);\n}\n\n.gr-color-gray-3-opacity-2 {\n  color: rgba(231, 233, 237, 0.2);\n}\n\n.gr-color-gray-3-opacity-3 {\n  color: rgba(231, 233, 237, 0.3);\n}\n\n.gr-color-gray-3-opacity-4 {\n  color: rgba(231, 233, 237, 0.4);\n}\n\n.gr-color-gray-3-opacity-5 {\n  color: rgba(231, 233, 237, 0.5);\n}\n\n.gr-color-gray-3-opacity-6 {\n  color: rgba(231, 233, 237, 0.6);\n}\n\n.gr-color-gray-3-opacity-7 {\n  color: rgba(231, 233, 237, 0.7);\n}\n\n.gr-color-gray-3-opacity-8 {\n  color: rgba(231, 233, 237, 0.8);\n}\n\n.gr-color-gray-3-opacity-9 {\n  color: rgba(231, 233, 237, 0.9);\n}\n\n.gr-bg-gray-310-opacity-visible {\n  background-color: #d5d7dd;\n}\n\n.gr-bg-gray-310-opacity-1 {\n  background-color: rgba(213, 215, 221, 0.1);\n}\n\n.gr-bg-gray-310-opacity-2 {\n  background-color: rgba(213, 215, 221, 0.2);\n}\n\n.gr-bg-gray-310-opacity-3 {\n  background-color: rgba(213, 215, 221, 0.3);\n}\n\n.gr-bg-gray-310-opacity-4 {\n  background-color: rgba(213, 215, 221, 0.4);\n}\n\n.gr-bg-gray-310-opacity-5 {\n  background-color: rgba(213, 215, 221, 0.5);\n}\n\n.gr-bg-gray-310-opacity-6 {\n  background-color: rgba(213, 215, 221, 0.6);\n}\n\n.gr-bg-gray-310-opacity-7 {\n  background-color: rgba(213, 215, 221, 0.7);\n}\n\n.gr-bg-gray-310-opacity-8 {\n  background-color: rgba(213, 215, 221, 0.8);\n}\n\n.gr-bg-gray-310-opacity-9 {\n  background-color: rgba(213, 215, 221, 0.9);\n}\n\n.gr-color-gray-310-opacity-visible {\n  color: #d5d7dd;\n}\n\n.gr-color-gray-310-opacity-1 {\n  color: rgba(213, 215, 221, 0.1);\n}\n\n.gr-color-gray-310-opacity-2 {\n  color: rgba(213, 215, 221, 0.2);\n}\n\n.gr-color-gray-310-opacity-3 {\n  color: rgba(213, 215, 221, 0.3);\n}\n\n.gr-color-gray-310-opacity-4 {\n  color: rgba(213, 215, 221, 0.4);\n}\n\n.gr-color-gray-310-opacity-5 {\n  color: rgba(213, 215, 221, 0.5);\n}\n\n.gr-color-gray-310-opacity-6 {\n  color: rgba(213, 215, 221, 0.6);\n}\n\n.gr-color-gray-310-opacity-7 {\n  color: rgba(213, 215, 221, 0.7);\n}\n\n.gr-color-gray-310-opacity-8 {\n  color: rgba(213, 215, 221, 0.8);\n}\n\n.gr-color-gray-310-opacity-9 {\n  color: rgba(213, 215, 221, 0.9);\n}\n\n.gr-bg-gray-opacity-opacity-visible {\n  background-color: #e7e9ed;\n}\n\n.gr-bg-gray-opacity-opacity-1 {\n  background-color: rgba(231, 233, 237, 0.1);\n}\n\n.gr-bg-gray-opacity-opacity-2 {\n  background-color: rgba(231, 233, 237, 0.2);\n}\n\n.gr-bg-gray-opacity-opacity-3 {\n  background-color: rgba(231, 233, 237, 0.3);\n}\n\n.gr-bg-gray-opacity-opacity-4 {\n  background-color: rgba(231, 233, 237, 0.4);\n}\n\n.gr-bg-gray-opacity-opacity-5 {\n  background-color: rgba(231, 233, 237, 0.5);\n}\n\n.gr-bg-gray-opacity-opacity-6 {\n  background-color: rgba(231, 233, 237, 0.6);\n}\n\n.gr-bg-gray-opacity-opacity-7 {\n  background-color: rgba(231, 233, 237, 0.7);\n}\n\n.gr-bg-gray-opacity-opacity-8 {\n  background-color: rgba(231, 233, 237, 0.8);\n}\n\n.gr-bg-gray-opacity-opacity-9 {\n  background-color: rgba(231, 233, 237, 0.9);\n}\n\n.gr-color-gray-opacity-opacity-visible {\n  color: #e7e9ed;\n}\n\n.gr-color-gray-opacity-opacity-1 {\n  color: rgba(231, 233, 237, 0.1);\n}\n\n.gr-color-gray-opacity-opacity-2 {\n  color: rgba(231, 233, 237, 0.2);\n}\n\n.gr-color-gray-opacity-opacity-3 {\n  color: rgba(231, 233, 237, 0.3);\n}\n\n.gr-color-gray-opacity-opacity-4 {\n  color: rgba(231, 233, 237, 0.4);\n}\n\n.gr-color-gray-opacity-opacity-5 {\n  color: rgba(231, 233, 237, 0.5);\n}\n\n.gr-color-gray-opacity-opacity-6 {\n  color: rgba(231, 233, 237, 0.6);\n}\n\n.gr-color-gray-opacity-opacity-7 {\n  color: rgba(231, 233, 237, 0.7);\n}\n\n.gr-color-gray-opacity-opacity-8 {\n  color: rgba(231, 233, 237, 0.8);\n}\n\n.gr-color-gray-opacity-opacity-9 {\n  color: rgba(231, 233, 237, 0.9);\n}\n\n.gr-bg-blackish-blue-opacity-opacity-visible {\n  background-color: #161c2d;\n}\n\n.gr-bg-blackish-blue-opacity-opacity-1 {\n  background-color: rgba(22, 28, 45, 0.1);\n}\n\n.gr-bg-blackish-blue-opacity-opacity-2 {\n  background-color: rgba(22, 28, 45, 0.2);\n}\n\n.gr-bg-blackish-blue-opacity-opacity-3 {\n  background-color: rgba(22, 28, 45, 0.3);\n}\n\n.gr-bg-blackish-blue-opacity-opacity-4 {\n  background-color: rgba(22, 28, 45, 0.4);\n}\n\n.gr-bg-blackish-blue-opacity-opacity-5 {\n  background-color: rgba(22, 28, 45, 0.5);\n}\n\n.gr-bg-blackish-blue-opacity-opacity-6 {\n  background-color: rgba(22, 28, 45, 0.6);\n}\n\n.gr-bg-blackish-blue-opacity-opacity-7 {\n  background-color: rgba(22, 28, 45, 0.7);\n}\n\n.gr-bg-blackish-blue-opacity-opacity-8 {\n  background-color: rgba(22, 28, 45, 0.8);\n}\n\n.gr-bg-blackish-blue-opacity-opacity-9 {\n  background-color: rgba(22, 28, 45, 0.9);\n}\n\n.gr-color-blackish-blue-opacity-opacity-visible {\n  color: #161c2d;\n}\n\n.gr-color-blackish-blue-opacity-opacity-1 {\n  color: rgba(22, 28, 45, 0.1);\n}\n\n.gr-color-blackish-blue-opacity-opacity-2 {\n  color: rgba(22, 28, 45, 0.2);\n}\n\n.gr-color-blackish-blue-opacity-opacity-3 {\n  color: rgba(22, 28, 45, 0.3);\n}\n\n.gr-color-blackish-blue-opacity-opacity-4 {\n  color: rgba(22, 28, 45, 0.4);\n}\n\n.gr-color-blackish-blue-opacity-opacity-5 {\n  color: rgba(22, 28, 45, 0.5);\n}\n\n.gr-color-blackish-blue-opacity-opacity-6 {\n  color: rgba(22, 28, 45, 0.6);\n}\n\n.gr-color-blackish-blue-opacity-opacity-7 {\n  color: rgba(22, 28, 45, 0.7);\n}\n\n.gr-color-blackish-blue-opacity-opacity-8 {\n  color: rgba(22, 28, 45, 0.8);\n}\n\n.gr-color-blackish-blue-opacity-opacity-9 {\n  color: rgba(22, 28, 45, 0.9);\n}\n\n.gr-bg-narvik-opacity-visible {\n  background-color: #edf9f2;\n}\n\n.gr-bg-narvik-opacity-1 {\n  background-color: rgba(237, 249, 242, 0.1);\n}\n\n.gr-bg-narvik-opacity-2 {\n  background-color: rgba(237, 249, 242, 0.2);\n}\n\n.gr-bg-narvik-opacity-3 {\n  background-color: rgba(237, 249, 242, 0.3);\n}\n\n.gr-bg-narvik-opacity-4 {\n  background-color: rgba(237, 249, 242, 0.4);\n}\n\n.gr-bg-narvik-opacity-5 {\n  background-color: rgba(237, 249, 242, 0.5);\n}\n\n.gr-bg-narvik-opacity-6 {\n  background-color: rgba(237, 249, 242, 0.6);\n}\n\n.gr-bg-narvik-opacity-7 {\n  background-color: rgba(237, 249, 242, 0.7);\n}\n\n.gr-bg-narvik-opacity-8 {\n  background-color: rgba(237, 249, 242, 0.8);\n}\n\n.gr-bg-narvik-opacity-9 {\n  background-color: rgba(237, 249, 242, 0.9);\n}\n\n.gr-color-narvik-opacity-visible {\n  color: #edf9f2;\n}\n\n.gr-color-narvik-opacity-1 {\n  color: rgba(237, 249, 242, 0.1);\n}\n\n.gr-color-narvik-opacity-2 {\n  color: rgba(237, 249, 242, 0.2);\n}\n\n.gr-color-narvik-opacity-3 {\n  color: rgba(237, 249, 242, 0.3);\n}\n\n.gr-color-narvik-opacity-4 {\n  color: rgba(237, 249, 242, 0.4);\n}\n\n.gr-color-narvik-opacity-5 {\n  color: rgba(237, 249, 242, 0.5);\n}\n\n.gr-color-narvik-opacity-6 {\n  color: rgba(237, 249, 242, 0.6);\n}\n\n.gr-color-narvik-opacity-7 {\n  color: rgba(237, 249, 242, 0.7);\n}\n\n.gr-color-narvik-opacity-8 {\n  color: rgba(237, 249, 242, 0.8);\n}\n\n.gr-color-narvik-opacity-9 {\n  color: rgba(237, 249, 242, 0.9);\n}\n\n.gr-opacity-visible {\n  opacity: 1;\n}\n\n.gr-opacity-1 {\n  opacity: 0.1;\n}\n\n.gr-opacity-2 {\n  opacity: 0.2;\n}\n\n.gr-opacity-3 {\n  opacity: 0.3;\n}\n\n.gr-opacity-4 {\n  opacity: 0.4;\n}\n\n.gr-opacity-5 {\n  opacity: 0.5;\n}\n\n.gr-opacity-6 {\n  opacity: 0.6;\n}\n\n.gr-opacity-7 {\n  opacity: 0.7;\n}\n\n.gr-opacity-8 {\n  opacity: 0.8;\n}\n\n.gr-opacity-9 {\n  opacity: 0.9;\n}\n\n.gr-text-color {\n  color: var(--color-texts) !important;\n}\n\n.gr-text-color-opacity {\n  color: var(--color-texts-opacity);\n}\n\n.gr-fill-color {\n  fill: var(--color-texts) !important;\n}\n\n.bg-default {\n  background: var(--bg);\n}\n\n.bg-default-1 {\n  background: var(--bg);\n}\n\n.bg-default-2 {\n  background: var(--bg-2);\n}\n\n.bg-default-3 {\n  background: var(--bg-3);\n}\n\n.bg-default-4 {\n  background: var(--bg-4);\n}\n\n.bg-default-5 {\n  background: var(--bg-5);\n}\n\n.bg-default-6 {\n  background: var(--bg-6);\n}\n\n.bg-default-7 {\n  background: var(--bg-7);\n}\n\n.bg-default-8 {\n  background: var(--bg-8);\n}\n\n.gr-fill-default-4 {\n  fill: var(--bg-4);\n}\n\n.bg-opposite {\n  background: var(--bg-opposite);\n}\n\n.gr-flex-all-center {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.gr-flex-y-center {\n  display: flex;\n  align-items: center;\n}\n\n@media (min-width: 576px) {\n  .row-sm {\n    display: flex;\n    flex-wrap: wrap;\n    margin-right: -15px;\n    margin-left: -15px;\n  }\n}\n\n@media (min-width: 992px) {\n  .row-lg {\n    display: flex;\n    flex-wrap: wrap;\n    margin-right: -15px;\n    margin-left: -15px;\n  }\n}\n\n@media (min-width: 768px) {\n  .row-md {\n    display: flex;\n    flex-wrap: wrap;\n    margin-right: -15px;\n    margin-left: -15px;\n  }\n}\n\n@media (min-width: 1200px) {\n  .row-xl {\n    display: flex;\n    flex-wrap: wrap;\n    margin-right: -15px;\n    margin-left: -15px;\n  }\n}\n\n/*~~~~~ Normal Shadows ~~~~~*/\n.shadow-1 {\n  box-shadow: 0 34px 33px -23px rgba(22, 28, 45, 0.13);\n}\n\n.shadow-2 {\n  box-shadow: 0 31px 34px -20px rgba(0, 0, 0, 0.09);\n}\n\n.shadow-3 {\n  box-shadow: 0 42px 44px -10px rgba(1, 23, 48, 0.12);\n}\n\n.shadow-4 {\n  box-shadow: 0 32px 64px rgba(22, 28, 45, 0.08);\n}\n\n.shadow-5 {\n  box-shadow: 0 62px 64px -10px rgba(1, 23, 48, 0.12);\n}\n\n.shadow-6 {\n  box-shadow: 0 32px 54px rgba(22, 28, 45, 0.16);\n}\n\n.shadow-7 {\n  box-shadow: 0 54px 53px -23px rgba(22, 28, 45, 0.14);\n}\n\n.shadow-8 {\n  box-shadow: 0 52px 74px rgba(0, 0, 0, 0.11);\n}\n\n.shadow-9 {\n  box-shadow: 0 22px 45px rgba(91, 9, 0, 0.2);\n}\n\n.shadow-10 {\n  box-shadow: 0 22px 45px rgba(0, 0, 0, 0.09);\n}\n\n.shadow-blue {\n  box-shadow: 0 14px 64px rgba(71, 59, 240, 0.4);\n}\n\n.shadow-red {\n  box-shadow: 0 14px 64px rgba(246, 75, 75, 0.4);\n}\n\n.shadow-green {\n  box-shadow: 0 14px 64px rgba(104, 213, 133, 0.4);\n}\n\n/*~~~~~ Circle Sizes ~~~~~*/\n.circle-xxxs {\n  max-width: 16px;\n  min-width: 16px;\n  max-height: 16px;\n  min-height: 16px;\n  border-radius: 500px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.circle-xxs {\n  max-width: 20px;\n  min-width: 20px;\n  max-height: 20px;\n  min-height: 20px;\n  border-radius: 500px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.circle-xs {\n  max-width: 31px;\n  min-width: 31px;\n  max-height: 31px;\n  min-height: 31px;\n  border-radius: 500px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 16px;\n}\n\n.circle-sm {\n  max-width: 43px;\n  min-width: 43px;\n  max-height: 43px;\n  min-height: 43px;\n  border-radius: 500px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.circle-sm-2 {\n  max-width: 54px;\n  min-width: 54px;\n  max-height: 54px;\n  min-height: 54px;\n  border-radius: 500px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.circle-md {\n  max-width: 66px;\n  min-width: 66px;\n  max-height: 66px;\n  min-height: 66px;\n  border-radius: 500px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 18px;\n}\n\n.circle-lg {\n  max-width: 72px;\n  min-width: 72px;\n  max-height: 72px;\n  min-height: 72px;\n  border-radius: 500px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 28px;\n}\n\n.circle-xl {\n  max-width: 92px;\n  min-width: 92px;\n  max-height: 92px;\n  min-height: 92px;\n  border-radius: 500px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.circle-xxl {\n  max-width: 111px;\n  min-width: 111px;\n  max-height: 111px;\n  min-height: 111px;\n  border-radius: 500px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.circle-xxxl {\n  max-width: 164px;\n  min-width: 164px;\n  max-height: 164px;\n  min-height: 164px;\n  border-radius: 500px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.circle-18 {\n  max-width: 18px;\n  min-width: 18px;\n  max-height: 18px;\n  min-height: 18px;\n  border-radius: 500px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.circle-30 {\n  max-width: 30px;\n  min-width: 30px;\n  max-height: 30px;\n  min-height: 30px;\n  border-radius: 500px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.circle-32 {\n  max-width: 32px;\n  min-width: 32px;\n  max-height: 32px;\n  min-height: 32px;\n  border-radius: 500px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.circle-40 {\n  max-width: 40px;\n  min-width: 40px;\n  max-height: 40px;\n  min-height: 40px;\n  border-radius: 500px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.circle-42 {\n  max-width: 42px;\n  min-width: 42px;\n  max-height: 42px;\n  min-height: 42px;\n  border-radius: 500px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.circle-43 {\n  max-width: 43px;\n  min-width: 43px;\n  max-height: 43px;\n  min-height: 43px;\n  border-radius: 500px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.circle-78 {\n  max-width: 78px;\n  min-width: 78px;\n  max-height: 78px;\n  min-height: 78px;\n  border-radius: 500px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.circle-96 {\n  max-width: 96px;\n  min-width: 96px;\n  max-height: 96px;\n  min-height: 96px;\n  border-radius: 500px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.circle-120 {\n  max-width: 120px;\n  min-width: 120px;\n  max-height: 120px;\n  min-height: 120px;\n  border-radius: 500px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n/*~~~~~ Square Sizes ~~~~~*/\n.square-80 {\n  max-width: 80px;\n  min-width: 80px;\n  max-height: 80px;\n  min-height: 80px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.square-95 {\n  max-width: 95px;\n  min-width: 95px;\n  max-height: 95px;\n  min-height: 95px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n/*~~~~~ responsive width ~~~~~*/\n@media (min-width: 480px) {\n  .w-xs-25 {\n    width: 25% !important;\n  }\n  .w-xs-50 {\n    width: 50% !important;\n  }\n  .w-xs-75 {\n    width: 75% !important;\n  }\n  .w-xs-100 {\n    width: 100% !important;\n  }\n  .w-xs-auto {\n    width: auto !important;\n  }\n  .w-xs-20 {\n    width: 20% !important;\n  }\n  .w-xs-30 {\n    width: 30% !important;\n  }\n  .w-xs-35 {\n    width: 35% !important;\n  }\n  .w-xs-37 {\n    width: 37% !important;\n  }\n  .w-xs-40 {\n    width: 35% !important;\n  }\n  .w-xs-55 {\n    width: 55% !important;\n  }\n  .w-xs-60 {\n    width: 60% !important;\n  }\n  .w-xs-68 {\n    width: 68% !important;\n  }\n  .w-xs-70 {\n    width: 70% !important;\n  }\n  .w-xs-76 {\n    width: 76% !important;\n  }\n  .w-xs-80 {\n    width: 80% !important;\n  }\n  .w-xs-85 {\n    width: 85% !important;\n  }\n}\n\n@media (min-width: 576px) {\n  .w-sm-25 {\n    width: 25% !important;\n  }\n  .w-sm-50 {\n    width: 50% !important;\n  }\n  .w-sm-75 {\n    width: 75% !important;\n  }\n  .w-sm-100 {\n    width: 100% !important;\n  }\n  .w-sm-auto {\n    width: auto !important;\n  }\n  .w-sm-20 {\n    width: 20% !important;\n  }\n  .w-sm-30 {\n    width: 30% !important;\n  }\n  .w-sm-35 {\n    width: 35% !important;\n  }\n  .w-sm-37 {\n    width: 37% !important;\n  }\n  .w-sm-40 {\n    width: 35% !important;\n  }\n  .w-sm-55 {\n    width: 55% !important;\n  }\n  .w-sm-60 {\n    width: 60% !important;\n  }\n  .w-sm-68 {\n    width: 68% !important;\n  }\n  .w-sm-70 {\n    width: 70% !important;\n  }\n  .w-sm-76 {\n    width: 76% !important;\n  }\n  .w-sm-80 {\n    width: 80% !important;\n  }\n  .w-sm-85 {\n    width: 85% !important;\n  }\n}\n\n@media (min-width: 768px) {\n  .w-md-25 {\n    width: 25% !important;\n  }\n  .w-md-50 {\n    width: 50% !important;\n  }\n  .w-md-75 {\n    width: 75% !important;\n  }\n  .w-md-100 {\n    width: 100% !important;\n  }\n  .w-md-auto {\n    width: auto !important;\n  }\n  .w-md-20 {\n    width: 20% !important;\n  }\n  .w-md-30 {\n    width: 30% !important;\n  }\n  .w-md-35 {\n    width: 35% !important;\n  }\n  .w-md-37 {\n    width: 37% !important;\n  }\n  .w-md-40 {\n    width: 35% !important;\n  }\n  .w-md-55 {\n    width: 55% !important;\n  }\n  .w-md-60 {\n    width: 60% !important;\n  }\n  .w-md-68 {\n    width: 68% !important;\n  }\n  .w-md-70 {\n    width: 70% !important;\n  }\n  .w-md-76 {\n    width: 76% !important;\n  }\n  .w-md-80 {\n    width: 80% !important;\n  }\n  .w-md-85 {\n    width: 85% !important;\n  }\n}\n\n@media (min-width: 992px) {\n  .w-lg-25 {\n    width: 25% !important;\n  }\n  .w-lg-50 {\n    width: 50% !important;\n  }\n  .w-lg-75 {\n    width: 75% !important;\n  }\n  .w-lg-100 {\n    width: 100% !important;\n  }\n  .w-lg-auto {\n    width: auto !important;\n  }\n  .w-lg-20 {\n    width: 20% !important;\n  }\n  .w-lg-30 {\n    width: 30% !important;\n  }\n  .w-lg-35 {\n    width: 35% !important;\n  }\n  .w-lg-37 {\n    width: 37% !important;\n  }\n  .w-lg-40 {\n    width: 35% !important;\n  }\n  .w-lg-55 {\n    width: 55% !important;\n  }\n  .w-lg-60 {\n    width: 60% !important;\n  }\n  .w-lg-68 {\n    width: 68% !important;\n  }\n  .w-lg-70 {\n    width: 70% !important;\n  }\n  .w-lg-76 {\n    width: 76% !important;\n  }\n  .w-lg-80 {\n    width: 80% !important;\n  }\n  .w-lg-85 {\n    width: 85% !important;\n  }\n}\n\n@media (min-width: 1200px) {\n  .w-xl-25 {\n    width: 25% !important;\n  }\n  .w-xl-50 {\n    width: 50% !important;\n  }\n  .w-xl-75 {\n    width: 75% !important;\n  }\n  .w-xl-100 {\n    width: 100% !important;\n  }\n  .w-xl-auto {\n    width: auto !important;\n  }\n  .w-xl-20 {\n    width: 20% !important;\n  }\n  .w-xl-30 {\n    width: 30% !important;\n  }\n  .w-xl-35 {\n    width: 35% !important;\n  }\n  .w-xl-37 {\n    width: 37% !important;\n  }\n  .w-xl-40 {\n    width: 35% !important;\n  }\n  .w-xl-55 {\n    width: 55% !important;\n  }\n  .w-xl-60 {\n    width: 60% !important;\n  }\n  .w-xl-68 {\n    width: 68% !important;\n  }\n  .w-xl-70 {\n    width: 70% !important;\n  }\n  .w-xl-76 {\n    width: 76% !important;\n  }\n  .w-xl-80 {\n    width: 80% !important;\n  }\n  .w-xl-85 {\n    width: 85% !important;\n  }\n}\n\n.rounded-xs {\n  border-radius: 3px;\n}\n\n.rounded-top-xs {\n  border-top-left-radius: 3px;\n  border-top-right-radius: 3px;\n}\n\n.rounded-bottom-xs {\n  border-bottom-left-radius: 3px;\n  border-bottom-right-radius: 3px;\n}\n\n.rounded-left-xs {\n  border-top-left-radius: 3px;\n  border-bottom-left-radius: 3px;\n}\n\n.rounded-right-xs {\n  border-top-right-radius: 3px;\n  border-bottom-right-radius: 3px;\n}\n\n.border-top-xs {\n  border-top-width: 3px;\n  border-top-style: solid;\n}\n\n.rounded-md {\n  border-radius: 0.5rem;\n}\n\n.rounded-top-md {\n  border-top-left-radius: 0.5rem;\n  border-top-right-radius: 0.5rem;\n}\n\n.rounded-bottom-md {\n  border-bottom-left-radius: 0.5rem;\n  border-bottom-right-radius: 0.5rem;\n}\n\n.rounded-left-md {\n  border-top-left-radius: 0.5rem;\n  border-bottom-left-radius: 0.5rem;\n}\n\n.rounded-right-md {\n  border-top-right-radius: 0.5rem;\n  border-bottom-right-radius: 0.5rem;\n}\n\n.border-top-md {\n  border-top-width: 0.5rem;\n  border-top-style: solid;\n}\n\n.rounded-xl {\n  border-radius: 1.25rem;\n}\n\n.rounded-top-xl {\n  border-top-left-radius: 1.25rem;\n  border-top-right-radius: 1.25rem;\n}\n\n.rounded-bottom-xl {\n  border-bottom-left-radius: 1.25rem;\n  border-bottom-right-radius: 1.25rem;\n}\n\n.rounded-left-xl {\n  border-top-left-radius: 1.25rem;\n  border-bottom-left-radius: 1.25rem;\n}\n\n.rounded-right-xl {\n  border-top-right-radius: 1.25rem;\n  border-bottom-right-radius: 1.25rem;\n}\n\n.border-top-xl {\n  border-top-width: 1.25rem;\n  border-top-style: solid;\n}\n\n.rounded-3 {\n  border-radius: 3px;\n}\n\n.rounded-top-3 {\n  border-top-left-radius: 3px;\n  border-top-right-radius: 3px;\n}\n\n.rounded-bottom-3 {\n  border-bottom-left-radius: 3px;\n  border-bottom-right-radius: 3px;\n}\n\n.rounded-left-3 {\n  border-top-left-radius: 3px;\n  border-bottom-left-radius: 3px;\n}\n\n.rounded-right-3 {\n  border-top-right-radius: 3px;\n  border-bottom-right-radius: 3px;\n}\n\n.border-top-3 {\n  border-top-width: 3px;\n  border-top-style: solid;\n}\n\n.rounded-5 {\n  border-radius: 5px;\n}\n\n.rounded-top-5 {\n  border-top-left-radius: 5px;\n  border-top-right-radius: 5px;\n}\n\n.rounded-bottom-5 {\n  border-bottom-left-radius: 5px;\n  border-bottom-right-radius: 5px;\n}\n\n.rounded-left-5 {\n  border-top-left-radius: 5px;\n  border-bottom-left-radius: 5px;\n}\n\n.rounded-right-5 {\n  border-top-right-radius: 5px;\n  border-bottom-right-radius: 5px;\n}\n\n.border-top-5 {\n  border-top-width: 5px;\n  border-top-style: solid;\n}\n\n.rounded-8 {\n  border-radius: 8px;\n}\n\n.rounded-top-8 {\n  border-top-left-radius: 8px;\n  border-top-right-radius: 8px;\n}\n\n.rounded-bottom-8 {\n  border-bottom-left-radius: 8px;\n  border-bottom-right-radius: 8px;\n}\n\n.rounded-left-8 {\n  border-top-left-radius: 8px;\n  border-bottom-left-radius: 8px;\n}\n\n.rounded-right-8 {\n  border-top-right-radius: 8px;\n  border-bottom-right-radius: 8px;\n}\n\n.border-top-8 {\n  border-top-width: 8px;\n  border-top-style: solid;\n}\n\n.rounded-10 {\n  border-radius: 10px;\n}\n\n.rounded-top-10 {\n  border-top-left-radius: 10px;\n  border-top-right-radius: 10px;\n}\n\n.rounded-bottom-10 {\n  border-bottom-left-radius: 10px;\n  border-bottom-right-radius: 10px;\n}\n\n.rounded-left-10 {\n  border-top-left-radius: 10px;\n  border-bottom-left-radius: 10px;\n}\n\n.rounded-right-10 {\n  border-top-right-radius: 10px;\n  border-bottom-right-radius: 10px;\n}\n\n.border-top-10 {\n  border-top-width: 10px;\n  border-top-style: solid;\n}\n\n.rounded-12 {\n  border-radius: 12px;\n}\n\n.rounded-top-12 {\n  border-top-left-radius: 12px;\n  border-top-right-radius: 12px;\n}\n\n.rounded-bottom-12 {\n  border-bottom-left-radius: 12px;\n  border-bottom-right-radius: 12px;\n}\n\n.rounded-left-12 {\n  border-top-left-radius: 12px;\n  border-bottom-left-radius: 12px;\n}\n\n.rounded-right-12 {\n  border-top-right-radius: 12px;\n  border-bottom-right-radius: 12px;\n}\n\n.border-top-12 {\n  border-top-width: 12px;\n  border-top-style: solid;\n}\n\n.rounded-15 {\n  border-radius: 15px;\n}\n\n.rounded-top-15 {\n  border-top-left-radius: 15px;\n  border-top-right-radius: 15px;\n}\n\n.rounded-bottom-15 {\n  border-bottom-left-radius: 15px;\n  border-bottom-right-radius: 15px;\n}\n\n.rounded-left-15 {\n  border-top-left-radius: 15px;\n  border-bottom-left-radius: 15px;\n}\n\n.rounded-right-15 {\n  border-top-right-radius: 15px;\n  border-bottom-right-radius: 15px;\n}\n\n.border-top-15 {\n  border-top-width: 15px;\n  border-top-style: solid;\n}\n\n.rounded-20 {\n  border-radius: 20px;\n}\n\n.rounded-top-20 {\n  border-top-left-radius: 20px;\n  border-top-right-radius: 20px;\n}\n\n.rounded-bottom-20 {\n  border-bottom-left-radius: 20px;\n  border-bottom-right-radius: 20px;\n}\n\n.rounded-left-20 {\n  border-top-left-radius: 20px;\n  border-bottom-left-radius: 20px;\n}\n\n.rounded-right-20 {\n  border-top-right-radius: 20px;\n  border-bottom-right-radius: 20px;\n}\n\n.border-top-20 {\n  border-top-width: 20px;\n  border-top-style: solid;\n}\n\n.rounded-25 {\n  border-radius: 25px;\n}\n\n.rounded-top-25 {\n  border-top-left-radius: 25px;\n  border-top-right-radius: 25px;\n}\n\n.rounded-bottom-25 {\n  border-bottom-left-radius: 25px;\n  border-bottom-right-radius: 25px;\n}\n\n.rounded-left-25 {\n  border-top-left-radius: 25px;\n  border-bottom-left-radius: 25px;\n}\n\n.rounded-right-25 {\n  border-top-right-radius: 25px;\n  border-bottom-right-radius: 25px;\n}\n\n.border-top-25 {\n  border-top-width: 25px;\n  border-top-style: solid;\n}\n\n@media (min-width: 768px) {\n  .gr-md-rounded {\n    border-radius: 0.25rem;\n  }\n}\n\n@media (min-width: 992px) {\n  .border-lg-left {\n    border-left: 1px solid var(--border-color);\n  }\n}\n\n.border-sm-divider {\n  width: 161px;\n  height: 1px;\n  border: 3px solid #13151C;\n  opacity: 0.1;\n  margin: 0 auto;\n}\n\n.border-sm-divider.dark {\n  border: 3px solid #ffffff;\n}\n\n.form-control {\n  border-color: #e7e9ed;\n}\n\n.border-3 {\n  border-width: 3px;\n  border-style: solid;\n}\n\n.border-color-2 {\n  border: 1px solid var(--border-color-2) !important;\n}\n\n.bg-gradient {\n  position: relative;\n  z-index: 1;\n}\n\n.bg-gradient-black:before {\n  background-image: linear-gradient(270deg, rgba(0, 0, 0, 0) 0%, #13151C 100%);\n  position: absolute;\n  content: \"\";\n  width: 100%;\n  height: 100%;\n  top: 0;\n  left: 0;\n  z-index: -1;\n}\n\n.bg-gradient-white:before {\n  background-image: linear-gradient(270deg, rgba(0, 0, 0, 0) 0%, #fbfbfb 100%);\n  position: absolute;\n  content: \"\";\n  width: 100%;\n  height: 100%;\n  top: 0;\n  left: 0;\n  z-index: -1;\n}\n\n.bg-gradient-1 {\n  position: relative;\n  background: linear-gradient(225deg, #313c59 0%, #13151C 100%);\n}\n\n.bg-gradient-2 {\n  background-image: linear-gradient(to right, #F8F8F8 0%, #EBEBEB 100%);\n}\n\n.bg-gradient-3 {\n  background-image: linear-gradient(to bottom, var(--bg-2) 62%, var(--bg-4) 62%, var(--bg-4) 100%);\n}\n\n.bg-overlay {\n  position: relative;\n  z-index: 1;\n}\n\n.bg-overlay:before {\n  position: absolute;\n  content: \"\";\n  width: 100%;\n  height: 100%;\n  top: 0;\n  left: 0;\n  z-index: -1;\n}\n\n.bg-overlay.overlay-1:before {\n  background: #13151C;\n  opacity: 0.51;\n}\n\n.bg-overlay.overlay-2:before {\n  background: #13151C;\n  opacity: 0.8;\n}\n\na {\n  transition: .4s;\n}\n\na:hover {\n  color: #473bf0;\n}\n\n.gr-hover-shadow-1 {\n  transition: .3s;\n}\n\n.gr-hover-shadow-1:hover {\n  box-shadow: 0 34px 33px -23px rgba(22, 28, 45, 0.13);\n}\n\n.gr-hover-shadow-2 {\n  transition: .3s;\n}\n\n.gr-hover-shadow-2:hover {\n  box-shadow: 0 31px 34px -20px rgba(0, 0, 0, 0.09);\n}\n\n.gr-hover-shadow-3 {\n  transition: .3s;\n}\n\n.gr-hover-shadow-3:hover {\n  box-shadow: 0 42px 44px -10px rgba(1, 23, 48, 0.12);\n}\n\n.gr-hover-shadow-4 {\n  transition: .3s;\n}\n\n.gr-hover-shadow-4:hover {\n  box-shadow: 0 32px 54px rgba(22, 28, 45, 0.16);\n}\n\n.gr-hover-shadow-5 {\n  transition: .3s;\n}\n\n.gr-hover-shadow-5:hover {\n  box-shadow: 0 30px 44px rgba(13, 21, 46, 0.09);\n}\n\n.gr-hover-shadow-6 {\n  transition: .3s;\n  position: relative;\n  z-index: 1;\n}\n\n.gr-hover-shadow-6:hover {\n  z-index: 3;\n  box-shadow: 0 62px 64px -10px rgba(1, 23, 48, 0.12);\n}\n\n.gr-hover-rotate-img img {\n  will-change: transform;\n  transition: 0.8s cubic-bezier(0.39, 0.575, 0.565, 1);\n  transform: rotate(0deg) scale(1);\n  overflow: hidden;\n}\n\n@media (min-width: 768px) {\n  .gr-hover-rotate-img:hover img {\n    transform: rotate(-3deg) scale(1.2);\n    opacity: .6;\n  }\n}\n\n.gr-hover-opacity-full {\n  transition: .4s;\n}\n\n@media (min-width: 768px) {\n  .gr-hover-opacity-full:hover {\n    opacity: 1;\n  }\n}\n\n.gr-hover-y {\n  transition: .4s;\n}\n\n@media (min-width: 768px) {\n  .gr-hover-y:hover {\n    transform: translateY(-8px);\n  }\n}\n\n.gr-abs-hover-y {\n  transition: .4s;\n}\n\n@media (min-width: 768px) {\n  .gr-abs-hover-y:hover {\n    transform: translate(-50%, -65%);\n  }\n}\n\n.gr-hover-text-green {\n  transition: .4s;\n}\n\n.gr-hover-text-green:hover {\n  color: #68d585 !important;\n}\n\n.gr-hover-text-green:hover i {\n  color: #68d585 !important;\n}\n\n.gr-hover-text-red {\n  transition: .4s;\n}\n\n.gr-hover-text-red:hover {\n  color: #f64b4b !important;\n}\n\n.gr-hover-text-red:hover i {\n  color: #f64b4b !important;\n}\n\n.hover-underline {\n  transition: .4s;\n}\n\n.hover-underline:hover {\n  text-decoration: underline;\n}\n\n.bg-white:hover, a.bg-white:hover {\n  background-color: #fff !important;\n}\n\n.text-white:hover, a.text-white:hover {\n  color: #fff !important;\n}\n\n.gr-hover-text-green, a.gr-hover-text-green {\n  transition: .4s;\n}\n\n.gr-hover-text-green:hover, a.gr-hover-text-green:hover {\n  color: #68d585 !important;\n}\n\n.gr-hover-text-green:hover i, a.gr-hover-text-green:hover i {\n  color: #68d585 !important;\n}\n\n.gr-hover-text-blue, a.gr-hover-text-blue {\n  transition: .4s;\n}\n\n.gr-hover-text-blue:hover, a.gr-hover-text-blue:hover {\n  color: #473bf0 !important;\n}\n\n.gr-hover-text-blue:hover i, a.gr-hover-text-blue:hover i {\n  color: #473bf0 !important;\n}\n\n.gr-product-hover-1 .hover-animation-item {\n  position: absolute;\n  bottom: 20px;\n  width: 100%;\n  left: 0;\n  text-align: center;\n  z-index: 999;\n  opacity: 0;\n  transition: .6s;\n}\n\n.gr-product-hover-1:hover .hover-animation-item {\n  bottom: 35px;\n  opacity: 1;\n}\n\n.gr-hover-scale-img img {\n  transition: .3s;\n  transform: scale(1);\n  box-shadow: 0 32px 54px rgba(22, 28, 45, 0);\n  will-change: transform;\n}\n\n.gr-hover-scale-img:hover img {\n  transform: scale(0.9);\n  box-shadow: 0 32px 54px rgba(22, 28, 45, 0.16);\n}\n\n@keyframes animate-rotate {\n  0% {\n    transform: rotate(-2deg);\n  }\n  20% {\n    transform: rotate(2deg);\n  }\n  40% {\n    transform: rotate(2deg);\n  }\n  80% {\n    transform: rotate(-2deg);\n  }\n  100% {\n    transform: rotate(-2deg);\n  }\n}\n\n.img-animate-1 {\n  animation-name: animate-rotate;\n  animation-duration: 5s;\n  animation-delay: 2s;\n  animation-timing-function: linear;\n  animation-iteration-count: infinite;\n}\n\n.bg-pattern {\n  position: relative;\n  z-index: 1;\n}\n\n.bg-pattern:before {\n  position: absolute;\n  content: \"\";\n  width: 100%;\n  height: 100%;\n  top: 0;\n  left: 0;\n  z-index: -1;\n}\n\n.bg-pattern.pattern-1:before {\n  background: url(../image/patterns/pattern-1.png) no-repeat center;\n  background-size: cover;\n  opacity: 0.14;\n}\n\n.bg-pattern.pattern-2:before {\n  background: url(../image/patterns/pattern-2.png) no-repeat center;\n  background-size: cover;\n}\n\n.bg-pattern.pattern-3:before {\n  background: url(../image/patterns/pattern-3.png) no-repeat 50% 100%;\n  background-size: cover;\n}\n\n.bg-pattern.pattern-4:before {\n  background: url(../image/patterns/pattern-4.png) no-repeat 50% 100%;\n  background-size: cover;\n}\n\n.bg-pattern.pattern-5:before {\n  background: url(../image/patterns/pattern-5.png) no-repeat left bottom;\n  background-size: contain;\n}\n\n.bg-pattern.pattern-6:before {\n  background: url(../image/patterns/pattern-6.png) no-repeat top right;\n  background-size: auto;\n}\n\n.bg-pattern.pattern-7:before {\n  background: url(../image/patterns/pattern-7.png) no-repeat top right;\n  background-size: auto;\n}\n\n@media (min-width: 768px) {\n  .gr-abs-md {\n    position: absolute;\n  }\n}\n\n@media (min-width: 992px) {\n  .gr-abs-lg {\n    position: absolute !important;\n  }\n}\n\n@media (min-width: 992px) {\n  .gr-abs-lg-ly-center {\n    position: absolute;\n    top: 50%;\n    left: 0;\n    transform: translateY(-50%);\n  }\n}\n\n@media (min-width: 992px) {\n  .gr-abs-lg-ry-center {\n    position: absolute;\n    top: 50%;\n    right: 0;\n    transform: translateY(-50%);\n  }\n}\n\n@media (min-width: 992px) {\n  .gr-abs-bx-center {\n    position: absolute;\n    bottom: 55px;\n    right: 50%;\n    transform: translateX(50%);\n  }\n}\n\n.gr-abs-tl {\n  position: absolute;\n  top: 0;\n  left: 0;\n}\n\n.gr-abs-tr {\n  position: absolute;\n  top: 0;\n  right: 0;\n}\n\n.gr-abs-bl {\n  position: absolute;\n  bottom: 0;\n  left: 0;\n}\n\n.gr-abs-br {\n  position: absolute;\n  bottom: 0;\n  right: 0;\n}\n\n.gr-abs-cr {\n  position: absolute;\n  top: 50%;\n  right: 0;\n  transform: translateY(-50%);\n}\n\n@media (min-width: 768px) {\n  .gr-abs-md-cr {\n    position: absolute;\n    top: 50%;\n    right: 0;\n    transform: translateY(-50%);\n  }\n}\n\n.gr-abs-center {\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n}\n\n.gr-abs-br-custom {\n  position: absolute;\n  bottom: -50px;\n  right: -36px;\n}\n\n.gr-abs-br-custom-2 {\n  position: absolute;\n  bottom: 12%;\n  right: 21%;\n}\n\n.gr-abs-bl-custom {\n  position: absolute;\n  bottom: -50px;\n  left: -36px;\n  z-index: -1;\n}\n\n.gr-abs-bl-custom-2 {\n  position: absolute;\n  bottom: -5px;\n  left: -70px;\n  z-index: -1;\n}\n\n.gr-abs-bl-custom-3 {\n  position: absolute;\n  bottom: 0;\n  left: -16%;\n}\n\n.gr-abs-bl-custom-4 {\n  position: absolute;\n  bottom: -40px;\n  left: -56px;\n}\n\n@media (min-width: 768px) {\n  .gr-abs-bl-custom-4 {\n    bottom: -40px;\n    left: 16px;\n  }\n}\n\n@media (min-width: 992px) {\n  .gr-abs-bl-custom-4 {\n    bottom: -40px;\n    left: -5px;\n  }\n}\n\n.gr-abs-bl-custom-5 {\n  position: absolute;\n  bottom: 38px;\n  left: -12px;\n}\n\n@media (min-width: 768px) {\n  .gr-abs-bl-custom-5 {\n    bottom: 38px;\n    left: -12px;\n  }\n}\n\n@media (min-width: 992px) {\n  .gr-abs-bl-custom-5 {\n    bottom: 38px;\n    left: -12px;\n  }\n}\n\n.gr-abs-tl-custom {\n  position: absolute;\n  top: -6px;\n  left: 45px;\n}\n\n.gr-abs-tl-custom-2 {\n  position: absolute;\n  top: 40px;\n  left: 52px;\n  z-index: -1;\n}\n\n@media (min-width: 992px) {\n  .gr-abs-tl-custom-2 {\n    top: 24px;\n    left: 35px;\n  }\n}\n\n@media (min-width: 1200px) {\n  .gr-abs-tl-custom-2 {\n    top: 40px;\n    left: 52px;\n  }\n}\n\n.gr-abs-tl-custom-3 {\n  position: absolute;\n  top: 80px;\n  left: -30px;\n}\n\n@media (min-width: 480px) {\n  .gr-abs-tl-custom-3 {\n    top: 80px;\n    left: -50px;\n  }\n}\n\n@media (min-width: 1200px) {\n  .gr-abs-tl-custom-3 {\n    top: 80px;\n    left: -65px;\n  }\n}\n\n.gr-abs-tl-custom-4 {\n  position: absolute;\n  top: 50px;\n  left: 43.5%;\n}\n\n@media (min-width: 992px) {\n  .gr-abs-tl-custom-4 {\n    top: 50px;\n    left: 43.5%;\n  }\n}\n\n@media (min-width: 1200px) {\n  .gr-abs-tl-custom-4 {\n    top: 50px;\n    left: 43.5%;\n  }\n}\n\n.hero-sm-card-1 {\n  position: absolute;\n  top: 32px;\n  left: -70px;\n  transform: scale(0.8);\n}\n\n@media (min-width: 480px) {\n  .hero-sm-card-1 {\n    transform: scale(0.9);\n  }\n}\n\n@media (min-width: 768px) {\n  .hero-sm-card-1 {\n    left: -96px;\n    transform: scale(1);\n  }\n}\n\n@media (min-width: 992px) {\n  .hero-sm-card-1 {\n    top: -12px;\n    left: -94px;\n  }\n}\n\n@media (min-width: 1200px) {\n  .hero-sm-card-1 {\n    top: 50px;\n    left: -96px;\n  }\n}\n\n.hero-sm-card-2 {\n  position: absolute;\n  bottom: 16px;\n  right: -60px;\n  transform: scale(0.8);\n}\n\n@media (min-width: 480px) {\n  .hero-sm-card-2 {\n    bottom: 60px;\n  }\n}\n\n@media (min-width: 768px) {\n  .hero-sm-card-2 {\n    bottom: 95px;\n    transform: scale(1);\n  }\n}\n\n@media (min-width: 992px) {\n  .hero-sm-card-2 {\n    bottom: 60px;\n    right: -16px;\n  }\n}\n\n@media (min-width: 1200px) {\n  .hero-sm-card-2 {\n    bottom: 60px;\n    right: -25px;\n  }\n}\n\n@media (min-width: 1366px) {\n  .hero-sm-card-2 {\n    bottom: 60px;\n    right: -60px;\n  }\n}\n\n.gr-abs-tr-custom {\n  position: absolute;\n  top: -32px;\n  right: -38px;\n}\n\n.gr-abs-tr-custom-2 {\n  position: absolute;\n  top: 26%;\n  right: 15%;\n  z-index: -1;\n}\n\n.gr-abs-shape-custom-1 {\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translateX(-20%) translateY(-50%);\n}\n\n.gr-abs-shape-custom-2 {\n  position: absolute;\n  top: -11%;\n  right: 30px;\n  height: 100%;\n}\n\n.gr-abs-shape-custom-2 img {\n  height: 100%;\n}\n\n@media (min-width: 480px) {\n  .gr-abs-shape-custom-2 {\n    top: 0%;\n    right: 18%;\n    height: auto;\n  }\n  .gr-abs-shape-custom-2 img {\n    transform: scale(1.2);\n  }\n}\n\n@media (min-width: 576px) {\n  .gr-abs-shape-custom-2 {\n    top: -7%;\n    right: -6%;\n  }\n  .gr-abs-shape-custom-2 img {\n    transform: scale(1);\n  }\n}\n\n@media (min-width: 768px) {\n  .gr-abs-shape-custom-2 {\n    top: -4%;\n    right: -4%;\n  }\n  .gr-abs-shape-custom-2 img {\n    transform: scale(1.1);\n  }\n}\n\n@media (min-width: 992px) {\n  .gr-abs-shape-custom-2 {\n    top: -8%;\n    right: -6%;\n  }\n  .gr-abs-shape-custom-2 img {\n    transform: scale(1);\n  }\n}\n\n@media (min-width: 1200px) {\n  .gr-abs-shape-custom-2 {\n    top: -8%;\n    right: -7%;\n  }\n}\n\n.gr-z-index-n1 {\n  z-index: -1;\n}\n\n.gr-z-index-1 {\n  z-index: 1;\n}\n\n.z-index-99 {\n  z-index: 99;\n}\n\n.z-index-super {\n  z-index: 9999;\n}\n\n.gr-abs-img-custom-2 {\n  position: absolute;\n  top: 30%;\n  right: 0;\n}\n\n.gr-custom-tl-1 {\n  position: relative;\n  top: -45px;\n  left: 0;\n}\n\n.gr-custom-br-1 {\n  position: relative;\n  bottom: -86px;\n  right: 0;\n}\n\n.gr-box-shadow-1 {\n  box-shadow: 0 34px 33px -23px rgba(22, 28, 45, 0.13);\n}\n\n.gr-box-shadow-2 {\n  box-shadow: 0 31px 34px -20px rgba(0, 0, 0, 0.09);\n}\n\n.gr-box-shadow-3 {\n  box-shadow: 0 42px 44px -10px rgba(1, 23, 48, 0.12);\n}\n\n.gr-box-shadow-4 {\n  box-shadow: 0 32px 64px rgba(22, 28, 45, 0.08);\n}\n\n.gr-box-shadow-5 {\n  box-shadow: 0 62px 64px -10px rgba(1, 23, 48, 0.12);\n}\n\n.gr-image-box-shadow {\n  box-shadow: 0 42px 44px -10px rgba(1, 23, 48, 0.12);\n}\n\n.gr-card-box-shadow {\n  box-shadow: 0 42px 44px -10px rgba(1, 23, 48, 0.12);\n}\n\n.w-fit-content {\n  width: fit-content;\n}\n\n@media (min-width: 992px) {\n  .lg\\:min-h-vh-100 {\n    min-height: 100vh;\n  }\n}\n\n.min-h-vh-100 {\n  min-height: 100vh;\n}\n\n@media (min-width: 480px) {\n  .h-xs-100 {\n    height: 100%;\n  }\n}\n\n@media (min-width: 576px) {\n  .h-sm-100 {\n    height: 100%;\n  }\n}\n\n@media (min-width: 576px) {\n  .h-md-100 {\n    height: 100% !important;\n  }\n}\n\n@media (min-width: 992px) {\n  .h-lg-100 {\n    height: 100%;\n  }\n}\n\n.h-px-50 {\n  min-height: 50px;\n}\n\n.gr-min-width-219 {\n  min-width: 219px;\n}\n\n.gr-textarea-height {\n  min-height: 175px;\n}\n\n.fluid-map-height {\n  min-height: 300px;\n  width: 100%;\n}\n\n@media (min-width: 576px) {\n  .fluid-map-height {\n    min-height: 400px;\n  }\n}\n\n@media (min-width: 768px) {\n  .fluid-map-height {\n    min-height: 541px;\n  }\n}\n\n.line-height-reset {\n  line-height: 1;\n}\n\np {\n  font-size: 1.1875rem;\n  color: var(--color-texts-opacity);\n}\n\n.gr-text-1 {\n  font-size: 5rem;\n  letter-spacing: -2.5px;\n  line-height: 1.05;\n}\n\n@media (max-width: 1200px) {\n  .gr-text-1 {\n    font-size: calc(1.625rem + 4.5vw) ;\n  }\n}\n\n.gr-text-1.gr-lh-reset::before {\n  content: '';\n  display: block;\n  height: 0;\n  width: 0;\n  margin-top: calc((1 - 1.15) * 0.5em);\n}\n\n.gr-text-1.gr-lh-reset::after {\n  content: '';\n  display: block;\n  height: 0;\n  width: 0;\n  margin-bottom: calc((1 - 1.15) * 0.5em);\n}\n\n.gr-text-2 {\n  font-size: 3.75rem;\n  letter-spacing: -2px;\n  line-height: 1.083;\n}\n\n@media (max-width: 1200px) {\n  .gr-text-2 {\n    font-size: calc(1.5rem + 3vw) ;\n  }\n}\n\n.gr-text-2.gr-lh-reset::before {\n  content: '';\n  display: block;\n  height: 0;\n  width: 0;\n  margin-top: calc((1 - 1.183) * 0.5em);\n}\n\n.gr-text-2.gr-lh-reset::after {\n  content: '';\n  display: block;\n  height: 0;\n  width: 0;\n  margin-bottom: calc((1 - 1.183) * 0.5em);\n}\n\n.gr-text-3 {\n  font-size: 3rem;\n  letter-spacing: -1.8px;\n  line-height: 1.21;\n}\n\n@media (max-width: 1200px) {\n  .gr-text-3 {\n    font-size: calc(1.425rem + 2.1vw) ;\n  }\n}\n\n.gr-text-3.gr-lh-reset::before {\n  content: '';\n  display: block;\n  height: 0;\n  width: 0;\n  margin-top: calc((1 - 1.31) * 0.5em);\n}\n\n.gr-text-3.gr-lh-reset::after {\n  content: '';\n  display: block;\n  height: 0;\n  width: 0;\n  margin-bottom: calc((1 - 1.31) * 0.5em);\n}\n\n.gr-text-4 {\n  font-size: 2.25rem;\n  letter-spacing: -1.2px;\n  line-height: 1.3;\n}\n\n@media (max-width: 1200px) {\n  .gr-text-4 {\n    font-size: calc(1.35rem + 1.2vw) ;\n  }\n}\n\n.gr-text-4.gr-lh-reset::before {\n  content: '';\n  display: block;\n  height: 0;\n  width: 0;\n  margin-top: calc((1 - 1.4) * 0.5em);\n}\n\n.gr-text-4.gr-lh-reset::after {\n  content: '';\n  display: block;\n  height: 0;\n  width: 0;\n  margin-bottom: calc((1 - 1.4) * 0.5em);\n}\n\n.gr-text-5 {\n  font-size: 2rem;\n  letter-spacing: -1.2px;\n  line-height: 1.375;\n}\n\n@media (max-width: 1200px) {\n  .gr-text-5 {\n    font-size: calc(1.325rem + 0.9vw) ;\n  }\n}\n\n.gr-text-5.gr-lh-reset::before {\n  content: '';\n  display: block;\n  height: 0;\n  width: 0;\n  margin-top: calc((1 - 1.475) * 0.5em);\n}\n\n.gr-text-5.gr-lh-reset::after {\n  content: '';\n  display: block;\n  height: 0;\n  width: 0;\n  margin-bottom: calc((1 - 1.475) * 0.5em);\n}\n\n.gr-text-6 {\n  font-size: 1.5rem;\n  letter-spacing: -0.5px;\n  line-height: 1.42;\n}\n\n@media (max-width: 1200px) {\n  .gr-text-6 {\n    font-size: calc(1.275rem + 0.3vw) ;\n  }\n}\n\n.gr-text-6.gr-lh-reset::before {\n  content: '';\n  display: block;\n  height: 0;\n  width: 0;\n  margin-top: calc((1 - 1.52) * 0.5em);\n}\n\n.gr-text-6.gr-lh-reset::after {\n  content: '';\n  display: block;\n  height: 0;\n  width: 0;\n  margin-bottom: calc((1 - 1.52) * 0.5em);\n}\n\n.gr-text-7 {\n  font-size: 1.3125rem;\n  letter-spacing: -0.5px;\n  line-height: 1.5;\n}\n\n@media (max-width: 1200px) {\n  .gr-text-7 {\n    font-size: calc(1.25625rem + 0.075vw) ;\n  }\n}\n\n.gr-text-7.gr-lh-reset::before {\n  content: '';\n  display: block;\n  height: 0;\n  width: 0;\n  margin-top: calc((1 - 1.6) * 0.5em);\n}\n\n.gr-text-7.gr-lh-reset::after {\n  content: '';\n  display: block;\n  height: 0;\n  width: 0;\n  margin-bottom: calc((1 - 1.6) * 0.5em);\n}\n\n.gr-text-8 {\n  font-size: 1.1875rem;\n  letter-spacing: -0.2px;\n  line-height: 1.69;\n}\n\n.gr-text-9 {\n  font-size: 1.0625rem;\n  letter-spacing: -0.2px;\n  line-height: 1.71;\n}\n\n.gr-text-10 {\n  font-size: 1rem;\n}\n\n.gr-text-11 {\n  font-size: 0.9375rem;\n  letter-spacing: -0.1px;\n  line-height: 1.73;\n}\n\n.gr-text-12 {\n  font-size: 0.8125rem;\n  line-height: 1.63;\n}\n\n.gr-text-12.gr-lh-reset::before {\n  content: '';\n  display: block;\n  height: 0;\n  width: 0;\n  margin-top: calc((1 - 1.73) * 0.5em);\n}\n\n.gr-text-12.gr-lh-reset::after {\n  content: '';\n  display: block;\n  height: 0;\n  width: 0;\n  margin-bottom: calc((1 - 1.73) * 0.5em);\n}\n\n.gr-text-13 {\n  font-size: 0.625rem;\n  line-height: 1.63;\n}\n\n.gr-text-14 {\n  font-size: 0.5rem;\n  line-height: 1.63;\n}\n\n.gr-text-underline {\n  text-decoration: underline;\n}\n\n.text-linethrough {\n  text-decoration: line-through;\n}\n\n.hero-card-1-animation {\n  animation: float 3s ease-in-out infinite;\n}\n\n.hero-card-2-animation {\n  animation: float 3s ease-in-out 1s infinite;\n}\n\n@keyframes float {\n  0% {\n    box-shadow: 0 22px 45px rgba(0, 0, 0, 0.09);\n    transform: translatey(0px);\n  }\n  50% {\n    box-shadow: 0 22px 45px rgba(0, 0, 0, 0.2);\n    transform: translatey(-15px);\n  }\n  100% {\n    box-shadow: 0 22px 45px rgba(0, 0, 0, 0.09);\n    transform: translatey(0px);\n  }\n}\n\n@keyframes floatX {\n  0% {\n    transform: translatex(-15px);\n  }\n  50% {\n    transform: translatex(0px);\n  }\n  100% {\n    transform: translatex(-15px);\n  }\n}\n\n@keyframes rotate {\n  0% {\n    box-shadow: 0 22px 45px rgba(0, 0, 0, 0.09);\n    transform: rotate3d(16, 2, 1, -1deg);\n  }\n  50% {\n    box-shadow: 0 22px 45px rgba(0, 0, 0, 0.2);\n    transform: rotate3d(16, 2, 1, -25deg);\n  }\n  100% {\n    box-shadow: 0 22px 45px rgba(0, 0, 0, 0.09);\n    transform: rotate3d(16, 2, 1, -1deg);\n  }\n}\n\n.hover-tilt:hover .animation-tilt {\n  animation-play-state: running;\n}\n\n.animation-tilt {\n  animation: tilt 3s linear infinite;\n  will-change: transform;\n  animation-play-state: paused;\n}\n\n@keyframes tilt {\n  0% {\n    transform: perspective(300px) rotateX(-8.23deg) rotateY(-4.91deg) scale3d(1, 1, 1);\n  }\n  16% {\n    transform: perspective(300px) rotateX(-8.31degdeg) rotateY(-4.98deg) scale3d(1, 1, 1);\n  }\n  33% {\n    transform: perspective(300px) rotateX(-7.39deg) rotateY(7.39deg) scale3d(1, 1, 1);\n  }\n  49% {\n    transform: perspective(300px) rotateX(3.9deg) rotateY(5.27deg) scale3d(1, 1, 1);\n  }\n  82% {\n    transform: perspective(300px) rotateX(2.64deg) rotateY(-5.44deg) scale3d(1, 1, 1);\n  }\n  100% {\n    transform: perspective(300px) rotateX(-8.23deg) rotateY(-4.91deg) scale3d(1, 1, 1);\n  }\n}\n\nbutton:focus {\n  outline: none;\n  box-shadow: none;\n}\n\n.btn-reset {\n  background: transparent;\n  border: none;\n}\n\n.bg-image {\n  background-repeat: no-repeat;\n  background-position: center;\n  background-size: cover;\n}\n\n.bg-image-right {\n  background-repeat: no-repeat;\n  background-position: right;\n  background-size: contain;\n}\n\n.bg-parallax-image {\n  background-attachment: fixed;\n  background-size: cover;\n  background-repeat: no-repeat;\n  background-position: center;\n}\n\n.hero-img-custom {\n  position: relative;\n  bottom: -2px;\n  left: 0;\n}\n\n@media (min-width: 480px) {\n  .hero-img-custom {\n    bottom: -33px;\n    left: 0;\n  }\n}\n\n@media (min-width: 576px) {\n  .hero-img-custom {\n    bottom: -9px;\n    left: 0;\n  }\n}\n\n@media (min-width: 768px) {\n  .hero-img-custom {\n    position: absolute;\n    bottom: -47px;\n    left: 0;\n  }\n}\n\n@media (min-width: 992px) {\n  .hero-img-custom {\n    bottom: -104px;\n    left: -145px;\n  }\n}\n\n@media (min-width: 1200px) {\n  .hero-img-custom {\n    bottom: -120px;\n    left: -145px;\n  }\n}\n\n.social-icons {\n  display: inline-flex;\n  align-items: center;\n  font-size: 18px;\n}\n\n.social-icons li a {\n  margin-right: 20px;\n}\n\n@media (min-width: 768px) {\n  .social-icons li i {\n    transition: .4s;\n  }\n  .social-icons li:hover i {\n    transform: translateY(-8px);\n  }\n}\n\n.social-icons li:last-child a {\n  margin-right: 0;\n}\n\n.list-style-check {\n  list-style: none;\n}\n\n.list-style-check li i {\n  font-size: 16px;\n  color: #68d585;\n  margin-right: 14px;\n  margin-top: 7px;\n}\n\n.footer-list {\n  transition: .4s;\n}\n\n.nice-select {\n  height: 55px;\n  border-radius: 5px;\n  width: 100%;\n  display: flex;\n  align-items: center;\n  padding: 0 20px;\n}\n\n.nice-select .list {\n  width: 100%;\n}\n\n.gr-text-exerpt {\n  text-overflow: ellipsis;\n  overflow: hidden;\n  white-space: nowrap;\n}\n\n.focus-reset:focus {\n  outline: none !important;\n  box-shadow: none !important;\n}\n\n.separator-line {\n  width: 1px;\n  height: 169px;\n  border: 1px solid var(--border-color);\n  margin: 0 auto;\n  margin-bottom: 30px;\n}\n\n.strike-bottom {\n  display: inline-block;\n  position: relative;\n  z-index: 1;\n}\n\n.strike-bottom::after {\n  position: absolute;\n  content: \"\";\n  width: 100%;\n  height: 4px;\n  left: 0;\n  bottom: 5px;\n  background: white;\n  z-index: -1;\n}\n\n.strike-bottom.green::after {\n  background: #68d585;\n}\n\n.strike-bottom.red::after {\n  background: #f64b4b;\n}\n\n.strike-bottom.blue::after {\n  background: #473bf0;\n}\n\n.strike-bottom.white::after {\n  background: white;\n}\n\n.nice-select:active, .nice-select.open, .nice-select:focus {\n  border-color: rgba(71, 59, 240, 0.22) !important;\n}\n\n.nice-select.rounded-8 {\n  border-radius: 8px !important;\n}\n\n.nice-select.arrow-2:after {\n  border-width: 5px;\n  border-color: #000 transparent transparent transparent;\n  border-style: solid;\n  display: block;\n  height: 5px;\n  width: 5px;\n  margin-top: -2.5px;\n  transform-origin: 40% 23%;\n  transform: rotate(0deg);\n}\n\n.nice-select.arrow-2.open:after {\n  transform: rotate(180deg);\n}\n\n.nice-select.arrow-3:after {\n  right: 16px;\n  height: 8px;\n  width: 8px;\n  border-color: #13151C;\n}\n\n.nice-select.arrow-3.open:after {\n  transform: rotate(180deg);\n}\n\n.arrow-box-dropdown:after {\n  border-width: 5px;\n  border-color: #000 transparent transparent transparent;\n  border-style: solid;\n  display: block;\n  height: 5px;\n  width: 5px;\n  margin-top: -2.5px;\n  transform-origin: 40% 23%;\n  transform: rotate(0deg);\n  pointer-events: none;\n  position: absolute;\n  right: 12px;\n  top: 50%;\n  content: '';\n}\n\n.arrow-box-dropdown.open:after {\n  transform: rotate(180deg);\n}\n\n.arrow-toggle:after {\n  border-width: 5px;\n  border-color: #000 transparent transparent transparent;\n  border-style: solid;\n  display: block;\n  content: \"\";\n  height: 5px;\n  width: 5px;\n  margin-top: -2.5px;\n  transform-origin: 40% 23%;\n  transform: rotate(0deg);\n  pointer-events: none;\n  position: absolute;\n  right: 12px;\n  top: 50%;\n  transition: all .15s ease-in-out;\n}\n\n.min-height-px-64 {\n  min-height: 64px;\n}\n\n.min-height-px-50 {\n  height: 50px;\n}\n\n.min-height-px-297 {\n  min-height: 297px;\n}\n\n.pointer-none {\n  pointer-events: none;\n}\n\n.responsive-scaling {\n  transform: scale(0.7);\n}\n\n@media (min-width: 768px) {\n  .responsive-scaling {\n    transform: scale(0.9);\n  }\n}\n\n@media (min-width: 992px) {\n  .responsive-scaling {\n    transform: scale(1);\n  }\n}\n\n.border-black-dynamic {\n  border-color: #161c2d !important;\n}\n\n[data-theme='dark'] .border-black-dynamic, .dark-mode-texts .border-black-dynamic {\n  border-color: #fff !important;\n}\n\n.image-group-p12 .image-bg-positioning {\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  z-index: -1;\n  transform: translate(-53%, -45%) scale(0.75);\n}\n\n@media (min-width: 992px) {\n  .image-group-p12 .image-bg-positioning {\n    transform: translate(-53%, -45%) scale(0.75);\n  }\n}\n\n@media (min-width: 1200px) {\n  .image-group-p12 .image-bg-positioning {\n    transform: translate(-52%, -47%) scale(0.9);\n  }\n}\n\n.animation-item {\n  perspective: 1000px;\n}\n\n.abs-img {\n  position: absolute;\n  right: -15px;\n  top: -40px;\n  z-index: -1;\n}\n\n.wave-shape {\n  position: absolute;\n  bottom: -1px;\n  left: 0;\n  width: 120%;\n}\n\n@media (min-width: 1200px) {\n  .wave-shape {\n    width: 106%;\n  }\n  .wave-shape img {\n    width: 106%;\n  }\n}\n\n.tel-content-image-group-1 {\n  min-height: 335px;\n  width: 100%;\n  position: relative;\n}\n\n@media (min-width: 576px) {\n  .tel-content-image-group-1 {\n    margin-left: -9%;\n  }\n}\n\n@media (min-width: 992px) {\n  .tel-content-image-group-1 {\n    margin-left: 0%;\n  }\n}\n\n.tel-content-image-group-1 .abs-image-1 {\n  position: absolute;\n  top: 0;\n  left: 0;\n  cursor: pointer;\n}\n\n@media (min-width: 480px) {\n  .tel-content-image-group-1 .abs-image-1 {\n    top: 13%;\n  }\n}\n\n@media (min-width: 576px) {\n  .tel-content-image-group-1 .abs-image-1 {\n    top: 5%;\n    left: -2%;\n  }\n}\n\n@media (min-width: 768px) {\n  .tel-content-image-group-1 .abs-image-1 {\n    top: 10%;\n    left: 4%;\n  }\n}\n\n@media (min-width: 992px) {\n  .tel-content-image-group-1 .abs-image-1 {\n    top: 10%;\n    left: -25px;\n  }\n}\n\n@media (min-width: 1200px) {\n  .tel-content-image-group-1 .abs-image-1 {\n    left: 0;\n  }\n}\n\n.tel-content-image-group-1 .abs-image-2 {\n  position: absolute;\n  bottom: -15%;\n  right: 0px;\n  cursor: pointer;\n}\n\n@media (min-width: 480px) {\n  .tel-content-image-group-1 .abs-image-2 {\n    bottom: -8%;\n    right: -6%;\n  }\n}\n\n@media (min-width: 576px) {\n  .tel-content-image-group-1 .abs-image-2 {\n    bottom: -13%;\n    right: -17%;\n  }\n}\n\n@media (min-width: 768px) {\n  .tel-content-image-group-1 .abs-image-2 {\n    bottom: -18%;\n    right: -5%;\n  }\n}\n\n@media (min-width: 992px) {\n  .tel-content-image-group-1 .abs-image-2 {\n    bottom: -5%;\n    right: 0px;\n    margin-bottom: -30px;\n  }\n}\n\n@media (min-width: 1200px) {\n  .tel-content-image-group-1 .abs-image-2 {\n    right: 20px;\n    margin-bottom: -50px;\n  }\n}\n\n.tel-content-image-group-1 .responsive-scaling-2 {\n  transform: scale(0.7);\n}\n\n@media (min-width: 480px) {\n  .tel-content-image-group-1 .responsive-scaling-2 {\n    transform: scale(0.8);\n  }\n}\n\n@media (min-width: 576px) {\n  .tel-content-image-group-1 .responsive-scaling-2 {\n    transform: scale(0.9);\n  }\n}\n\n@media (min-width: 768px) {\n  .tel-content-image-group-1 .responsive-scaling-2 {\n    transform: scale(1);\n  }\n}\n\n@media (min-width: 992px) {\n  .tel-content-image-group-1 .responsive-scaling-2 {\n    transform: scale(0.9);\n  }\n}\n\n@media (min-width: 1200px) {\n  .tel-content-image-group-1 .responsive-scaling-2 {\n    transform: scale(1);\n  }\n}\n\n.tel-content-image-group-2 {\n  padding-top: 25px;\n  display: inline-block;\n  padding-left: 50px;\n  padding-right: 25px;\n}\n\n@media (min-width: 400px) {\n  .tel-content-image-group-2 {\n    padding-left: 100px;\n  }\n}\n\n@media (min-width: 576px) {\n  .tel-content-image-group-2 {\n    padding-left: 125px;\n  }\n}\n\n.tel-content-image-group-2 > img {\n  width: 100%;\n}\n\n@media (min-width: 480px) {\n  .tel-content-image-group-2 > img {\n    width: auto;\n  }\n}\n\n.tel-content-image-group-2 .abs-image-1 {\n  position: absolute;\n  bottom: 40px;\n  left: 0;\n  z-index: 1;\n  zoom: 70%;\n}\n\n@media (min-width: 370px) {\n  .tel-content-image-group-2 .abs-image-1 {\n    zoom: 80%;\n  }\n}\n\n@media (min-width: 400px) {\n  .tel-content-image-group-2 .abs-image-1 {\n    zoom: 100%;\n  }\n}\n\n.tel-content-image-group-2 .abs-image-2 {\n  position: absolute;\n  height: 100%;\n  z-index: -1;\n  top: 0px;\n  right: 0px;\n}\n\n.tel-content-image-group-2 .abs-image-2 > img {\n  height: 85%;\n}\n\n@media (min-width: 420px) {\n  .tel-content-image-group-2 .abs-image-2 > img {\n    height: auto;\n  }\n}\n\n.hover-shadow-up:hover .anim-shadow-up {\n  box-shadow: 0 32px 44px -15px rgba(1, 16, 30, 0.18);\n}\n\n.anim-shadow-up {\n  box-shadow: 0 32px 44px -15px rgba(1, 16, 30, 0.18);\n  transition: .4s;\n}\n\n@media (min-width: 992px) {\n  .anim-shadow-up {\n    box-shadow: 0 32px 44px -15px rgba(1, 16, 30, 0);\n  }\n}\n\n.parallax-section-750 {\n  height: 350px;\n}\n\n@media (min-width: 768px) {\n  .parallax-section-750 {\n    height: 500px;\n  }\n}\n\n@media (min-width: 992px) {\n  .parallax-section-750 {\n    height: 792px;\n  }\n}\n", "/*=== Media Query ===*/\r\n// Screen Width\r\n$screen-xxs: 320px;\r\n$screen-xs: 480px;\r\n$screen-sm: 576px;\r\n$screen-md: 768px;\r\n$screen-lg: 992px;\r\n$screen-xl: 1200px;\r\n$screen-xxl: 1366px;\r\n$screen-xxxl: 1600px;\r\n\r\n@mixin brk-point($mw) {\r\n    @media(min-width: $mw) {\r\n        @content;\r\n    }\r\n}\r\n@mixin mobile-xs {\r\n    @media(min-width: $screen-xxs) {\r\n        @content;\r\n    }\r\n}\r\n\r\n@mixin mobile {\r\n    @media(min-width: $screen-xs) {\r\n        @content;\r\n    }\r\n}\r\n\r\n@mixin mobile-lg {\r\n    @media(min-width: $screen-sm) {\r\n        @content;\r\n    }\r\n}\r\n@mixin mobile-lg-only {\r\n    @media(min-width: $screen-sm) and (max-width:$screen-md) {\r\n        @content;\r\n    }\r\n}\r\n\r\n@mixin tablet {\r\n    @media(min-width: $screen-md) {\r\n        @content;\r\n    }\r\n}\r\n\r\n@mixin desktops {\r\n    @media(min-width: $screen-lg) {\r\n        @content;\r\n    }\r\n}\r\n\r\n@mixin desktops-only {\r\n    @media(min-width: $screen-lg) and (max-width:$screen-xl) {\r\n        @content;\r\n    }\r\n}\r\n@mixin till-desktop {\r\n    @media(min-width: $screen-xxs) and (max-width:$screen-lg) {\r\n        @content;\r\n    }\r\n}\r\n\r\n@mixin large-desktops {\r\n    @media(min-width: $screen-xl) {\r\n        @content;\r\n    }\r\n}\r\n\r\n@mixin large-desktops-mid {\r\n    @media(min-width: $screen-xxl) {\r\n        @content;\r\n    }\r\n}\r\n\r\n@mixin extra-large-desktops {\r\n    @media(min-width: $screen-xxxl) {\r\n        @content;\r\n    }\r\n}\r\n\r\n@mixin section-padding{\r\n    padding: 50px 0;\r\n    @include tablet {\r\n        padding: 120px 0;\r\n\r\n    }\r\n}\r\n", ".accordion-trigger{\r\n  &.arrow-icon{\r\n    position: relative;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    &:after{\r\n      content: \"\\ea05\";\r\n      font-family: \"Grayic\";\r\n      font-size: 32px;\r\n      display: block;\r\n      line-height: 1;\r\n      transform: rotate(0deg);\r\n      transition: .4s;\r\n    }\r\n    &[aria-expanded=\"true\"]{\r\n      &:after{\r\n        transform: rotate(-180deg);\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// .accordion {\r\n//   &.border-top-5 {\r\n//     border-top: 5px solid;\r\n//   }\r\n// }", ".gr-badge{\r\n  min-width: 95px;\r\n  min-height: 29px;\r\n  display: inline-flex;\r\n  justify-content:center;\r\n  align-items: center;\r\n}", ".btn {\r\n  // color: $gray-940;\r\n  min-width: 175px;\r\n  display: inline-flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  // transition: .4s cubic-bezier(0.39, 0.575, 0.565, 1);\r\n  // &:hover {\r\n  //   transform: translateY(-8px);\r\n  // }\r\n  &-hover-translate-none{\r\n    &:hover {\r\n      transform: translateY(0);\r\n    }\r\n  }\r\n  &.with-icon {\r\n    i {\r\n      margin-left: 25px;\r\n    }\r\n    &-left{\r\n      i {\r\n        margin-right: 15px;\r\n      }\r\n    }\r\n  }\r\n  &-primary{\r\n    color: #fff;\r\n    &.with-icon {\r\n      i {\r\n        margin-left: 25px;\r\n      }\r\n    }\r\n    &:hover{\r\n      background: $primary;\r\n    }\r\n  }\r\n  &-white{\r\n    border: 1px solid #d5d7dd;\r\n    &:hover {\r\n      color: $primary;\r\n      background: #fff;\r\n    }\r\n  }\r\n  &-link {\r\n    &.with-icon {\r\n      display: inline-flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      i {\r\n        margin-left: 16px;\r\n        transition: .4s;\r\n      }\r\n      &:hover {\r\n        i {\r\n          margin-left: 25px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n.card-btn-link {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  i {\r\n    margin-left: auto;\r\n    transition: .4s;\r\n  }\r\n  &:hover {\r\n    i {\r\n      margin-right: -5px;\r\n    }\r\n  }\r\n}\r\n\r\n.btn-toggle{\r\n  width: 72px;\r\n  height: 33px;\r\n  border-radius: 17px;\r\n  background-color: rgba($blackish-blue, 0.15);\r\n  position: relative;\r\n  margin: 0 15px;\r\n  span{\r\n    width: 21px;\r\n    height: 21px;\r\n    background-color: #FFFFFF;\r\n    position: absolute;\r\n    left: 0;\r\n    margin-left: 6px;\r\n    top: 0;\r\n    margin-top: 6px;\r\n    transition: .4s;;\r\n    border-radius: 500px;\r\n    pointer-events:none;\r\n  }\r\n  &.active{\r\n    background: $primary;\r\n    span{\r\n      left: calc(100% - 33px);\r\n    }\r\n  }\r\n}", "\r\n$white:    #fff;\r\n$ghost:    #fdfdff;\r\n$alabaster: #fbfbfb;\r\n$smoke:    #f8f8f8;\r\n$gray-110: #fcfdfe;\r\n$gray-120: #f4f7fa;\r\n$gray-210: #e7e9ed;\r\n$gray-opacity:   rgba(231, 233, 237, 0.13);\r\n$gray-310: #d5d7dd;\r\n$storm:    #7d818d;\r\n$gray-610: #666666;\r\n$gray-710: #413e65;\r\n$gray-910: #1F1F1F;\r\n$gray-920: #1e1e20;\r\n$gray-930: #19191b;\r\n$gray-940: #161c2d;\r\n$black:    #000 ;\r\n\r\n\r\n\r\n$red:                   #f64b4b;\r\n$blue:                  #473bf0;\r\n$sky-blue:              #1082e9;\r\n$green:                 #68d585;\r\n$green-shamrock:        #2bd67b;\r\n$blackish-blue:         #13151C;\r\n$blackish-blue-opacity: rgba(#161c2d,.7);\r\n$mirage:                #131829;\r\n$mirage-2:              #161c2d;\r\n$yellow: #f7e36d;\r\n$yellow-orange: #FCAD38;\r\n$narvik: #EDF9F2;\r\n\r\n$primary:       $blue;\r\n$secondary:     $green;\r\n\r\n", ".card-list{\r\n  max-width: 315px;\r\n  margin: 30px 0;\r\n  li{\r\n    color: $blackish-blue;\r\n    // font-size: 17px;\r\n    // font-weight: 400;\r\n    letter-spacing: -0.2px;\r\n    line-height: 29px;\r\n    margin-bottom: 13px;\r\n    display: flex;\r\n    &.disabled {\r\n      color: rgba($blackish-blue, 0.7);\r\n      // opacity: .7;\r\n      // color: #7D818D;\r\n      // &:before{\r\n      //   content: \"\\f00d\";\r\n      //   color: #D5D7DD;\r\n      // }\r\n      i {\r\n        color: #d5d7dd;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.card-columns {\r\n  column-count: 1;\r\n  @include mobile-lg {\r\n    column-count: 2;\r\n  }\r\n  &.column-3 {\r\n    @include tablet {\r\n      column-count: 3;\r\n    }\r\n  }\r\n}\r\n\r\n\r\n.job-card-hover{\r\n  \r\n  i{\r\n    transition: .4s;\r\n  }\r\n  &:hover{\r\n    i{\r\n      color: $primary!important;\r\n    }\r\n  }\r\n  \r\n}\r\n\r\n\r\n.category-card {\r\n  transition: .4s;\r\n  background-color: #fff;\r\n  .title {\r\n    color: $blackish-blue;\r\n  }\r\n  .sub-title {\r\n    color: rgba($blackish-blue, 0.7);\r\n  }\r\n\r\n  .card-icon {\r\n    background-color: $blue;\r\n    @include tablet {\r\n      transition: .3s;\r\n      transform: scale(0);\r\n      // transform-origin: center;\r\n      background-color: rgba(#fff, 0.2);\r\n\r\n    }\r\n  }\r\n  &:hover {\r\n    @include tablet{\r\n      background-color: $blue;\r\n      .card-title {\r\n        color: #fff;\r\n      }\r\n      .sub-title {\r\n        color: rgba(#fff, 0.7);\r\n      }\r\n      .card-icon {\r\n        transform: scale(1);\r\n      }\r\n    }\r\n  }\r\n}", ".cart-details-main-block{\r\n  .cart-product{\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n    .product-block{\r\n      flex-basis:100%;\r\n      @include mobile{\r\n        flex-basis:85%;\r\n      }\r\n      @include mobile-lg{\r\n        flex-basis:75%;\r\n      }\r\n      @include tablet{\r\n        flex-basis:60%;\r\n      }\r\n      @include desktops{\r\n        flex-basis: 50%;\r\n      }\r\n      @include large-desktops{\r\n        flex-basis: 40%;\r\n      }\r\n    }\r\n    .quantity-block{\r\n      @include mobile-lg{\r\n        flex-basis: 15%\r\n      }\r\n      @include tablet{\r\n        flex-basis: 15%\r\n      }\r\n      @include desktops{\r\n        flex-basis: 15%\r\n      }\r\n      @include large-desktops{\r\n        flex-basis: 15%\r\n      }\r\n    }\r\n    .price-block{\r\n      @include tablet{\r\n        flex-basis: 15%\r\n      }\r\n      @include desktops{\r\n        flex-basis: 15%\r\n      }\r\n      @include large-desktops{\r\n        flex-basis: 15%\r\n      }\r\n    }\r\n    .quantity-block{\r\n      flex-basis: 40%;\r\n      @include mobile{\r\n        flex-basis: 40%\r\n      }\r\n      @include mobile-lg{\r\n        flex-basis: 40%\r\n      }\r\n      @include tablet{\r\n        flex-basis: 25%;\r\n      }\r\n      @include desktops{\r\n        flex-basis: 25%;\r\n      }\r\n      @include large-desktops{\r\n        flex-basis: 25%;\r\n      }\r\n    }\r\n    .total-block{\r\n      // flex-basis: 50%;\r\n      @include mobile-lg{\r\n        flex-basis: 15%;\r\n      }\r\n      @include tablet{\r\n        flex-basis: 15%;\r\n      }\r\n      @include desktops{\r\n        flex-basis: 10%;\r\n      }\r\n      @include large-desktops{\r\n        flex-basis: 20%;\r\n      }\r\n    }\r\n    .cross-btn-positioning{\r\n      position: absolute;\r\n      top: 40px;\r\n      right: 16px;\r\n      @include tablet{\r\n        position: static;\r\n      }\r\n    }\r\n    // &.item{\r\n      \r\n    // }\r\n  }\r\n}\r\n// .cart-product{\r\n\r\n//   &.item{\r\n    \r\n//   }\r\n// }", ".gr-check-input{\r\n  padding-top: 5px;\r\n  margin-bottom: 20px;\r\n  input:checked{\r\n    &~.checkbox{\r\n      background: $primary;\r\n      &::after{\r\n        // content: \"\\f14a\";\r\n        // font-family: \"Font Awesome 5 free\";\r\n        // font-weight: 900;\r\n        opacity: 1;\r\n        color: #fff;\r\n      }\r\n    }\r\n  }\r\n  .checkbox{\r\n    position: relative;\r\n    line-height: 1;\r\n    width: 19px;\r\n    height: 19px;\r\n    border-radius: 3px;\r\n    background-color: #E7E9ED;\r\n    &:after{\r\n      content: \"\\eaba\";\r\n      font-family: 'Grayic';\r\n      font-size: 15px;\r\n      font-weight: normal;\r\n      color: $black;\r\n      position: absolute;\r\n      top: 0;\r\n      left: 0;\r\n      right: 0;\r\n      bottom: 0;\r\n      opacity: 0;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n    }\r\n  }\r\n}\r\n\r\n.gr-radio-input{\r\n  input{\r\n    &:checked{\r\n      ~ label{\r\n        .round-indicator{\r\n          border: 1px solid $primary;\r\n          background: #fff;\r\n          &:after{\r\n            opacity: 1;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n.round-indicator{\r\n  position: relative;\r\n  display: inline-block;\r\n  border: 1px solid #E5E5E5;\r\n  background-color: #E5E5E5;\r\n  min-width: 20px;\r\n  max-width: 20px;\r\n  min-height: 20px;\r\n  max-height: 20px;\r\n  border-radius: 500px;\r\n  &:after{\r\n    content: \"\";\r\n    background-color: $primary;\r\n    min-width: 12px;\r\n    max-width: 12px;\r\n    min-height: 12px;\r\n    max-height: 12px;\r\n    border-radius: 500px;\r\n    position: absolute;\r\n    top: 50%;\r\n    left: 50%;\r\n    opacity: 0;\r\n    transform: translate(-50%,-50%);\r\n  }\r\n}\r\n\r\n\r\n.location-filter-form{\r\n  border-radius: 10px;\r\n  .single-input{\r\n    border-bottom:1px solid $gray-210;\r\n    @include mobile-lg{\r\n      border-right:1px solid $gray-210;\r\n    }\r\n    @include desktops{\r\n      border-bottom:0;\r\n    }\r\n    &:last-child{\r\n      border-right: 0;\r\n    }\r\n  }\r\n  .location-select{\r\n    position: relative;\r\n    padding-left: 50px;\r\n    border: 0;\r\n    font-size: 15px;\r\n    &:before{\r\n      content: '\\f3c5';\r\n      font-family: \"Font Awesome 5 Free\";\r\n      font-weight: 700;\r\n      left: 0;\r\n      top: 0;\r\n      height: 100%;\r\n      position: absolute;\r\n      width: 45px;\r\n      font-size: 18px;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n    }\r\n    .list{\r\n      margin-top: 0;\r\n    }\r\n  }\r\n.form-control{\r\n  &:focus{\r\n    box-shadow: none;\r\n  }\r\n}\r\n\r\n.date-picker{\r\n  display: flex;\r\n  align-items: center;\r\n  >div{\r\n    height: 100%;\r\n    width: 100%;\r\n  }\r\n}\r\n    .date-picker-input{\r\n      padding-left: 45px;\r\n      margin-top: 3px;\r\n      color: $blackish-blue;\r\n      font-size: 15px;\r\n      &::placeholder{\r\n        color: $blackish-blue;\r\n      }\r\n    }\r\n    .date-picker-icon{\r\n      font-size: 18px;\r\n      width: 45px;\r\n      height: 100%;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      color: $blackish-blue;\r\n      position: absolute;\r\n      top: 0;\r\n      left: 0;\r\n\r\n    }\r\n    .search-btn{\r\n      min-width: 199px;\r\n      \r\n    }\r\n}", "\r\n  .header-btn {\r\n    @include desktops {\r\n      margin-left: 10px;\r\n    }\r\n    a{\r\n      display: none;\r\n      @include brk-point(360px){\r\n        display: inline-flex;\r\n        min-height: 35px;\r\n        min-width: 120px;\r\n        font-size: 14px;\r\n        font-weight: 500;\r\n        border-radius: 0.625rem;\r\n      }\r\n      @include brk-point(400px){\r\n        min-height: 45px;\r\n        min-width: 141px;\r\n        font-size: 15px;\r\n      }\r\n      @include mobile-lg{\r\n        min-height: 50px;\r\n      }\r\n    }\r\n  }\r\n  .header-btns{\r\n    .btn{\r\n      min-width: 124px;\r\n      height: 45px;\r\n      font-size: 15px;\r\n    }\r\n  }\r\n  .btn-close {\r\n    position: absolute;\r\n    top: 0;\r\n    right: 0;\r\n    font-size: 22px;\r\n    width: 60px;\r\n    height: 50px;\r\n    display: flex;\r\n    justify-content: center;\r\n    border: none;\r\n    background: transparent;\r\n    font-weight: 700;\r\n    i{\r\n      color: #353638;\r\n    }\r\n  }\r\n  \r\n \r\n\r\n\r\n  .site-header{\r\n    .brand-logo{\r\n      min-width: 150px;\r\n    }\r\n    &--absolute{\r\n      position: absolute;\r\n      top: 0;\r\n      left: 0;\r\n      width: 100%;\r\n      z-index: 999;\r\n    }\r\n    &--sticky{\r\n      &:not(.mobile-sticky-enable){\r\n        position: absolute!important;\r\n        top: 0;\r\n        right: 0;\r\n        width: 100%;\r\n        z-index: 999;\r\n        @include desktops{\r\n          position: fixed!important;\r\n          transition: .4s;\r\n          &.scrolling{\r\n            transform: translateY(-100%);\r\n            transition: .4s;\r\n            \r\n          }\r\n          &.reveal-header{\r\n            transform: translateY(0%);\r\n            box-shadow: 0 12px 34px -11px rgba(65, 62, 101, 0.1);\r\n            z-index: 9999;\r\n          }\r\n        }\r\n      }\r\n      &.mobile-sticky-enable{\r\n        top: 0;\r\n        right: 0;\r\n        width: 100%;\r\n        z-index: 999;\r\n        position: fixed!important;\r\n        transition: .4s;\r\n        &.scrolling{\r\n          transform: translateY(-100%);\r\n          transition: .4s;\r\n        }\r\n        &.reveal-header{\r\n          transform: translateY(0%);\r\n          box-shadow: 0 12px 34px -11px rgba(65, 62, 101, 0.1);\r\n          z-index: 9999;\r\n        }\r\n      }\r\n    }\r\n    &--menu{\r\n      &-center{\r\n        .navbar-nav-wrapper{\r\n          @include desktops{\r\n            margin-left: auto;\r\n            margin-right: auto;\r\n          }\r\n        }\r\n      }\r\n      &-left{\r\n        .container-fluid{\r\n          .navbar-nav-wrapper{\r\n            @include large-desktops{\r\n              @include make-container();\r\n              // @include make-container-max-widths();\r\n            }\r\n          }\r\n          >.container-fluid{\r\n            .gr-megamenu-dropdown{\r\n              left: 0%;\r\n              transform: translateX(0%) translateY(10px);\r\n            }\r\n            .nav-item.dropdown:hover > .gr-megamenu-dropdown.center{\r\n              transform: translateX(0%) translateY(-10px);\r\n              left: 0%;\r\n            }\r\n          }\r\n        }\r\n        .navbar-nav-wrapper{\r\n          .navbar-nav{\r\n            justify-content: flex-start;\r\n            padding-left: 15px;\r\n          }\r\n          @include large-desktops{\r\n            width: 100%;\r\n          }\r\n        }\r\n      }\r\n      &-right{\r\n        .navbar-nav-wrapper{\r\n          margin-left: auto;\r\n        }\r\n        >.container-fluid{\r\n          .gr-megamenu-dropdown{\r\n            left: 100%;\r\n            transform: translateX(-100%) translateY(10px);\r\n          }\r\n          .nav-item.dropdown:hover > .gr-megamenu-dropdown.center{\r\n            transform: translateX(-100%) translateY(-10px);\r\n            left: 100%;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n\r\n  .single-div{\r\n    &:after {\r\n      position: absolute;\r\n      right: 0;\r\n      top: 62px;\r\n      bottom: 62px;\r\n      width: 1px;\r\n      content: \"\";\r\n      background: red;\r\n  }\r\n  &:last-child{\r\n    &:after {\r\n      background: transparent;\r\n    }\r\n  }\r\n  }\r\n  .single-div{\r\n    +.single-div{\r\n        &:after {\r\n          position: absolute;\r\n          left: 0;\r\n          top: 62px;\r\n          bottom: 62px;\r\n          width: 1px;\r\n          content: \"\";\r\n          background: red;\r\n      }\r\n    }\r\n  }\r\n\r\n\r\n  .header-cart{\r\n    position: relative;\r\n    font-size: 20px;\r\n    color: var(--color-texts);\r\n    margin-left: auto;\r\n    margin-right: 15px;\r\n    margin-right: 15px;\r\n    @include desktops {\r\n      margin-left: 10px;\r\n    }\r\n    span{\r\n      height: 20px;\r\n      width: 20px;\r\n      font-size: 12px;\r\n      border-radius: 500px;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      position: absolute;\r\n      top: 0;\r\n      right: 0;\r\n      transform: translate(50%,-50%);\r\n      background: $green;\r\n      color: #fff;\r\n\r\n    }\r\n  }\r\n\r\n\r\n\r\n  .sticky-header {\r\n    &:not(.mobile-sticky-enable) {\r\n      position: absolute !important;\r\n      top: 0;\r\n      right: 0;\r\n      width: 100%;\r\n      z-index: 999;\r\n\r\n      @include desktops {\r\n        position: fixed !important;\r\n        transition: .4s;\r\n\r\n        &.scrolling {\r\n          transform: translateY(-100%);\r\n          transition: .4s;\r\n\r\n        }\r\n\r\n        &.reveal-header {\r\n          transform: translateY(0%);\r\n          box-shadow: 0 12px 34px -11px rgba(65, 62, 101, 0.1);\r\n          z-index: 1000;\r\n          background: #fff;\r\n        }\r\n      }\r\n    }\r\n\r\n    &.mobile-sticky-enable {\r\n      top: 0;\r\n      right: 0;\r\n      width: 100%;\r\n      z-index: 999;\r\n      position: fixed !important;\r\n      transition: .4s;\r\n\r\n      &.scrolling {\r\n        transform: translateY(-100%);\r\n        transition: .4s;\r\n      }\r\n\r\n      &.reveal-header {\r\n        transform: translateY(0%);\r\n        box-shadow: 0 12px 34px -11px rgba(65, 62, 101, 0.1);\r\n        z-index: 9999;\r\n        background: #fff;\r\n      }\r\n    }\r\n  }", "/// Grid system\n//\n// Generate semantic grid columns with these mixins.\n\n@mixin make-container($gutter: $grid-gutter-width) {\n  width: 100%;\n  padding-right: $gutter / 2;\n  padding-left: $gutter / 2;\n  margin-right: auto;\n  margin-left: auto;\n}\n\n@mixin make-row($gutter: $grid-gutter-width) {\n  display: flex;\n  flex-wrap: wrap;\n  margin-right: -$gutter / 2;\n  margin-left: -$gutter / 2;\n}\n\n// For each breakpoint, define the maximum width of the container in a media query\n@mixin make-container-max-widths($max-widths: $container-max-widths, $breakpoints: $grid-breakpoints) {\n  @each $breakpoint, $container-max-width in $max-widths {\n    @include media-breakpoint-up($breakpoint, $breakpoints) {\n      max-width: $container-max-width;\n    }\n  }\n  @include deprecate(\"The `make-container-max-widths` mixin\", \"v4.5.2\", \"v5\");\n}\n\n@mixin make-col-ready($gutter: $grid-gutter-width) {\n  position: relative;\n  // Prevent columns from becoming too narrow when at smaller grid tiers by\n  // always setting `width: 100%;`. This works because we use `flex` values\n  // later on to override this initial width.\n  width: 100%;\n  padding-right: $gutter / 2;\n  padding-left: $gutter / 2;\n}\n\n@mixin make-col($size, $columns: $grid-columns) {\n  flex: 0 0 percentage($size / $columns);\n  // Add a `max-width` to ensure content within each column does not blow out\n  // the width of the column. Applies to IE10+ and Firefox. Chrome and Safari\n  // do not appear to require this.\n  max-width: percentage($size / $columns);\n}\n\n@mixin make-col-auto() {\n  flex: 0 0 auto;\n  width: auto;\n  max-width: 100%; // Reset earlier grid tiers\n}\n\n@mixin make-col-offset($size, $columns: $grid-columns) {\n  $num: $size / $columns;\n  margin-left: if($num == 0, 0, percentage($num));\n}\n\n// Row columns\n//\n// Specify on a parent element(e.g., .row) to force immediate children into NN\n// numberof columns. Supports wrapping to new lines, but does not do a Masonry\n// style grid.\n@mixin row-cols($count) {\n  > * {\n    flex: 0 0 100% / $count;\n    max-width: 100% / $count;\n  }\n}\n", "\r\n// main menu\r\n.main-menu{\r\n  @include desktops{\r\n    display: flex;\r\n    justify-content: flex-end;\r\n  }\r\n  li{\r\n    a{\r\n      @include desktops{\r\n      }\r\n    }\r\n  }\r\n  \r\n/* ----------------------\r\n  Custom toggle arrow \r\n------------------------*/ \r\n\r\n.gr-toggle-arrow{\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  &:after{\r\n    display: none;\r\n  }\r\n  i {\r\n    font-size: 24px;\r\n    width: auto;\r\n    line-height: 1;\r\n    height: auto;\r\n    font-weight: 900;\r\n    margin-left: 5px;\r\n    transition: .6s;\r\n  }\r\n  &:hover{\r\n    i{\r\n      transform: rotate(-180deg);\r\n    }\r\n  }\r\n}\r\n  &>li{\r\n    &>.nav-link{\r\n      color: var(--color-texts)!important;\r\n      font-size: 15px;\r\n      font-weight: 700;\r\n      @include desktops{\r\n        padding-top: 18px!important;\r\n        padding-bottom: 18px!important;\r\n        padding-left: 18px!important;\r\n        padding-right: 18px!important;\r\n      }\r\n      &:hover{\r\n        color: $primary!important;\r\n      }\r\n     \r\n    }\r\n    &:hover{\r\n      &>.gr-toggle-arrow{\r\n        \r\n        i{\r\n          transform: rotate(-180deg);\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n\r\n/*----- Dropdown styles\r\n-------------------------*/\r\n.gr-menu-dropdown{\r\n  @include desktops{\r\n    position: absolute;\r\n    min-width: 227px;\r\n    max-width: 227px;\r\n    box-shadow: 0 52px 54px rgba(65, 62, 101, 0.3);\r\n    border-radius: 8px;\r\n    border: 1px solid #E5E5E5;\r\n    background-color: #FFFFFF;\r\n    padding: 15px 0px;\r\n    top: 100%;\r\n    z-index: -99;\r\n    opacity: 0;\r\n    transition: opacity .4s,top .4s;\r\n    pointer-events: none;\r\n    // left: -90%;\r\n    left: 0;\r\n    right: auto;\r\n    border-radius: 0 0 10px 10px;\r\n    border: 1px solid #eae9f2;\r\n    background-color: #ffffff;\r\n    display: block;\r\n    border-top: 3px solid $blue;\r\n  }\r\n  >.drop-menu-item{\r\n    color: #19191b;\r\n    font-size: 16px;\r\n    font-weight: 700;\r\n    letter-spacing: -0.5px;\r\n    padding-left: 30px ;\r\n    padding-right: 30px ;\r\n    padding-top: 10px;\r\n    padding-bottom: 10px;\r\n    >a{\r\n      color: inherit;\r\n    }\r\n    &:hover{\r\n     >a{\r\n      color: $blue;\r\n     }\r\n    }\r\n    \r\n      >.gr-menu-dropdown{\r\n        border-top-color: $green;\r\n        // display: block;\r\n        @include desktops{\r\n          top: 10px;\r\n          left: auto;\r\n          right: 0;\r\n          opacity: 0;\r\n          transform: translateX(110%);\r\n          transition: .4s;\r\n          pointer-events: none;\r\n          will-change: transform;\r\n        }\r\n        >.drop-menu-item{\r\n          @include brk-point(380px){\r\n            padding-left: 25px;\r\n            padding-right: 25px;\r\n          }\r\n          @include desktops{\r\n            padding-left: 30px ;\r\n            padding-right: 30px ;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  &.dropdown-right{\r\n    left: auto;\r\n    right: -90%;\r\n  }\r\n}\r\n\r\n\r\n/*----- Dropdown hover activation related styles\r\n-------------------------------------------------*/\r\n.nav-item.dropdown{\r\n  @include desktops{\r\n   position: relative;\r\n   z-index: 99;\r\n  }\r\n  &:hover{\r\n   >.gr-menu-dropdown{\r\n     @include desktops{\r\n       top: 90%;\r\n       opacity: 1;\r\n       pointer-events: visible;\r\n     }\r\n   }\r\n   >.gr-megamenu-dropdown.center{\r\n    @include desktops{\r\n      transform: translateX(-50%) translateY(-10px);\r\n      z-index: 99;\r\n      opacity: 1;\r\n      pointer-events: visible;\r\n    }\r\n  }\r\n  >.gr-megamenu-dropdown-2,&.triangle-shape:after,&.triangle-shape:before{\r\n    @include desktops{\r\n      transform: translateX(-50%) translateY(-10px);\r\n      z-index: 99;\r\n      opacity: 1;\r\n      pointer-events: visible;\r\n    }\r\n  }\r\n  }\r\n}\r\n\r\n\r\n.drop-menu-item.dropdown{\r\n\r\n  >.gr-toggle-arrow{\r\n    i{\r\n      transform:rotate(-90deg);\r\n    }\r\n  }\r\n  position: relative;\r\n    &:hover{\r\n      >.gr-menu-dropdown{\r\n        @include desktops{\r\n          top: 10px;\r\n          opacity: 1;\r\n          pointer-events: visible;\r\n          transform: translateX(100%);\r\n        \r\n        }\r\n      }\r\n    }\r\n}\r\n\r\n\r\n/*-----Mega Dropdown styles\r\n-------------------------*/\r\n.nav-item.dropdown.dropdown-mega{\r\n  z-index: 1;\r\n @include desktops{\r\n  position: static;\r\n }\r\n}\r\n// Mega Dropdown Style 1\r\n.gr-megamenu-dropdown{\r\n  .dropdown-image-block{\r\n    max-height: 336px;\r\n  }\r\n  @include till-desktop{\r\n    border: 0;\r\n  }\r\n  @include desktops{\r\n    padding: 15px;\r\n    min-width: 925px;\r\n    left: 50%;\r\n    transform: translateX(-50%) translateY(10px);\r\n    // transition: .4s transform;\r\n    will-change: transform;\r\n    // border-top: 3px solid $blue;\r\n    top: 100%;\r\n    z-index: -99;\r\n    opacity: 0;\r\n    pointer-events: none;\r\n    transition: .4s opacity ,.4s transform;\r\n    box-shadow: 0 42px 54px rgba(0, 0, 0, 0.09);\r\n    border: 1px solid #e7e9ed;\r\n    border-radius: 8px;\r\n  }\r\n    @include large-desktops{\r\n      min-width: 1100px;\r\n    }\r\n  .single-dropdown-block{\r\n    .mega-drop-menu-item{\r\n      padding-top: 14px;\r\n      padding-bottom: 14px;\r\n      display: block;\r\n    }\r\n  }\r\n  .mega-drop-menu-item{\r\n    padding-top: 2.5px;\r\n    padding-bottom: 2.5px;\r\n    color: $blackish-blue;\r\n    font-size: 15px;\r\n    font-weight: 700;\r\n\r\n    @include till-desktop{\r\n        border-top: 1px solid var(--border-color);\r\n        padding-left: 20px;\r\n        padding-bottom: 13px;\r\n        padding-top: 13px;\r\n    }\r\n    @include desktops{\r\n      padding-left: 10px ;\r\n      padding-right: 10px ;\r\n      &:hover{\r\n        color: $primary!important;\r\n      }\r\n    }\r\n    \r\n   \r\n  }\r\n  @include till-desktop{\r\n    [class*=\"col-\"]{\r\n      padding-left: 0;\r\n      padding-right: 0;\r\n    }\r\n    [class*=\"row-\"]{\r\n      margin-left: 0;\r\n      margin-right: 0;\r\n    }\r\n  }\r\n}\r\n\r\n// Mega Dropdown Style 2\r\n.gr-megamenu-dropdown-2{\r\n  @include till-desktop{\r\n    border: 0;\r\n  }\r\n  @include desktops{\r\n    box-shadow: 0 42px 54px rgba(0, 0, 0, 0.09);\r\n    padding: 25px;\r\n    min-width: 956px;\r\n    left: 50%;\r\n    // transition: .4s transform;\r\n    will-change: transform;\r\n    // border-top: 3px solid $blue;\r\n    top: 100%;\r\n    transform: translateX(-50%) translateY(10px);\r\n    z-index: -99;\r\n    opacity: 0;\r\n    pointer-events: none;\r\n    transition: .4s opacity ,.4s transform;\r\n    box-shadow: 0 52px 54px rgba(65, 62, 101, 0.3);\r\n    border: 1px solid #e7e9ed;\r\n  }\r\n    @include large-desktops{\r\n      min-width: 1100px;\r\n    }\r\n  .single-dropdown-block{\r\n    .mega-drop-menu-item{\r\n      padding-top: 10px;\r\n      padding-bottom: 10px;\r\n      display: block!important;\r\n    }\r\n  }\r\n  .mega-drop-menu-item{\r\n    padding-top: 2.5px;\r\n    padding-bottom: 2.5px;\r\n    color: $blackish-blue;\r\n    font-size: 15px;\r\n    font-weight: 700;\r\n    @include till-desktop{\r\n        border-top: 1px solid var(--border-color);\r\n        padding-left: 20px;\r\n        padding-bottom: 13px;\r\n        padding-top: 13px;\r\n    }\r\n    @include desktops{\r\n      padding-left: 20px ;\r\n      padding-right: 25px ;\r\n      border-radius: 11px;\r\n      width: fit-content;\r\n      &:hover{\r\n        background: #f4f7fa;\r\n  \r\n      }\r\n    }\r\n    \r\n    @include desktops{\r\n      &:hover{\r\n        color: $primary!important;\r\n      }\r\n    }\r\n    .single-menu-title{\r\n      color: $blackish-blue;\r\n      margin-bottom: 3px;\r\n    }\r\n    p{\r\n      margin-bottom: 0;\r\n      font-weight: normal;\r\n    }\r\n    \r\n  }\r\n  @include till-desktop{\r\n    [class*=\"col-\"]{\r\n      padding-left: 0;\r\n      padding-right: 0;\r\n    }\r\n    [class*=\"row-\"]{\r\n      margin-left: 0;\r\n      margin-right: 0;\r\n    }\r\n  }\r\n}\r\n\r\n.dropdown-mega.triangle-shape{\r\n  position: relative;\r\n  &:before{\r\n    position: absolute;\r\n    bottom: -7px;\r\n    right: -2px;\r\n    border:15px solid;\r\n    border-color: transparent transparent #fff transparent;\r\n    transform: translateX(-50%) translateY(10px);\r\n    z-index: 100!important;\r\n    opacity: 0;\r\n    transition: .4s;\r\n    pointer-events: none!important;\r\n    @include desktops{\r\n      content: \"\";\r\n    }\r\n  }\r\n  &:after{\r\n    \r\n    position: absolute;\r\n    bottom: -4px;\r\n    right: 0;\r\n    border:14px solid;\r\n    border-color: transparent transparent #e7e9ed transparent;\r\n    transform: translateX(-50%) translateY(10px);\r\n    z-index: 99!important;\r\n    opacity: 0;\r\n    transition: .4s;\r\n    pointer-events: none!important;\r\n    @include desktops{\r\n      content: \"\";\r\n    }\r\n  }\r\n}\r\n\r\n// \r\n.offcanvas-active{\r\n  \r\n  &.navbar-expand-lg{\r\n    .btn-close-off-canvas{\r\n      @include desktops{\r\n        display: none;\r\n      }\r\n    }\r\n    @include till-desktop{\r\n      .navbar-collapse{\r\n        display: block;\r\n        position: fixed;\r\n        top: 0;\r\n        background: var(--bg);\r\n        left: -100%;\r\n        padding-left: 20px;\r\n        padding-right: 20px;\r\n        height: 100%;\r\n        transition:left .4s;\r\n        z-index: 999999;\r\n        box-shadow: 0 0 87px 0 rgba(0, 0, 0, 0.09);\r\n        padding-top: 50px;\r\n        width: 250px;\r\n        overflow-y: auto;\r\n        @include brk-point(380px) {\r\n          width: 300px;\r\n        }\r\n        @include mobile-lg {\r\n          width: 350px;\r\n        }\r\n        &.show{\r\n          left: 0%;\r\n          ~.btn-close-off-canvas{\r\n            .icon-burger{\r\n              display: block;\r\n            }\r\n          \r\n          }\r\n        }\r\n        &.collapsing{\r\n          transition:height 0s;\r\n          height: 100%;\r\n        }\r\n\r\n        &::-webkit-scrollbar {\r\n          width: 8px;\r\n        }\r\n         \r\n        &::-webkit-scrollbar-track {\r\n          background:#fff\r\n        }\r\n         \r\n        &::-webkit-scrollbar-thumb {\r\n          background-color: $primary;\r\n          outline: 1px solid slategrey;\r\n        }\r\n      }\r\n      .btn-close-off-canvas{\r\n        .icon-burger{\r\n          display: block;\r\n        }\r\n      }\r\n      .main-menu {\r\n        >li {\r\n          padding-bottom: 0px;\r\n          margin-bottom: 0px;\r\n            border-bottom: 1px solid var(--border-color);\r\n            >.nav-link{\r\n              padding-bottom: 13px;\r\n              padding-top: 13px;\r\n            }\r\n            .gr-menu-dropdown {\r\n              border: 0;\r\n              border-radius: 0;\r\n              min-width: auto;\r\n              padding: 0;\r\n              >li {\r\n                padding-top:0;\r\n                padding-bottom:0;\r\n                border-top: 1px solid var(--border-color);\r\n                padding-left: 20px;\r\n                a{\r\n                  padding-top: 13px;\r\n                  padding-bottom: 13px;\r\n                }\r\n              }\r\n            }\r\n            &:last-child {\r\n              border-bottom-color: transparent;\r\n            }\r\n        }\r\n        li {\r\n          i {\r\n            margin-left: 8px;\r\n            position: relative;\r\n            top: 3px;\r\n          }\r\n          &:hover {\r\n              >a {\r\n                  color:$primary;\r\n              }\r\n          }\r\n        }\r\n        a {\r\n            display: flex;\r\n        }\r\n      }\r\n\r\n    }\r\n  }\r\n  \r\n}\r\n\r\n\r\n\r\n.navbar-toggler{\r\n  color: var(--color-texts-opacity)!important;\r\n  border-color:var(--color-texts-opacity)!important;\r\n}\r\n@mixin icon-bar-size{\r\n  width: 20px;\r\n  height: 3px;\r\n}\r\n$bar-space: -6px;\r\n.hamburger-icon{\r\n  border-radius: 5px;\r\n  border-width: 2px;\r\n  padding: 3px 10px;\r\n  .hamburger{\r\n    font: inherit;\r\n    display: inline-block;\r\n    overflow: visible;\r\n    margin: 0;\r\n    // padding: 7px 0px 5px;\r\n    padding: 2px 0px 0px;\r\n    cursor: pointer;\r\n    transition-timing-function: linear;\r\n    transition-duration: .15s;\r\n    transition-property: opacity,filter;\r\n    text-transform: none;\r\n    color: inherit;\r\n    border: 0;\r\n    background-color: transparent;\r\n    .hamburger-box {\r\n      position: relative;\r\n      display: inline-block;\r\n      width: 20px;\r\n      height: 15px;\r\n\r\n      .hamburger-inner {\r\n        transition-timing-function: cubic-bezier(.55,.055,.675,.19);\r\n        transition-duration: 75ms!important;\r\n        position: absolute;\r\n        transition-timing-function: ease;\r\n        transition-duration: .15s;\r\n        transition-property: transform;\r\n        border-radius: 4px;\r\n        background-color: var(--color-texts-opacity);\r\n        top: 50%;\r\n        display: block;\r\n        margin-top: -2px;\r\n        @include icon-bar-size;\r\n          &:before{\r\n            top: $bar-space;\r\n            transition: top 75ms ease .12s,opacity 75ms ease;\r\n          }\r\n          &:after {\r\n            bottom: $bar-space;\r\n            transition:bottom 75ms ease .12s,transform 75ms cubic-bezier(.55,.055,.675,.19)\r\n          }            \r\n          &:after, &:before {\r\n            display: block;\r\n            content: \"\";\r\n            position: absolute;\r\n            @include icon-bar-size;\r\n            border-radius: 4px;\r\n            background-color: var(--color-texts-opacity);\r\n          }\r\n    }\r\n  }\r\n  }\r\n &[aria-expanded=\"true\"] .hamburger .hamburger-box .hamburger-inner:after {\r\n    bottom: 0;\r\n    transition: bottom 75ms ease,transform 75ms cubic-bezier(.215,.61,.355,1) .12s;\r\n    transform: rotate(-90deg);\r\n  }\r\n  &[aria-expanded=\"true\"] .hamburger .hamburger-box .hamburger-inner:before {\r\n    top: 0;\r\n    transition: top 75ms ease,opacity 75ms ease .12s!important;\r\n    opacity: 0;\r\n  }\r\n  &[aria-expanded=\"true\"] .hamburger .hamburger-box .hamburger-inner {\r\n    transition-delay: .12s;\r\n    transition-timing-function: cubic-bezier(.215,.61,.355,1);\r\n    transform: rotate(45deg);\r\n}\r\n&:focus{\r\n  outline: none;\r\n  box-shadow: none;\r\n}\r\n}\r\n\r\n.offcanvas-btn-close {\r\n  position: absolute;\r\n  top: 0;\r\n  right: 0;\r\n  font-size: 22px;\r\n  width: 60px;\r\n  height: 50px;\r\n  display: flex;\r\n  justify-content: center;\r\n  border: none;\r\n  background: transparent;\r\n  font-weight: 700;\r\n  i{\r\n    color: var(--color-texts)!important;\r\n  }\r\n}\r\n.gr-cross-icon{\r\n  transition-timing-function: cubic-bezier(.55,.055,.675,.19);\r\n  transition-duration: 75ms!important;\r\n  position: absolute;\r\n  transition-timing-function: ease;\r\n  transition-duration: .15s;\r\n  transition-property: transform;\r\n  border-radius: 4px;\r\n  background-color: var(--color-texts-opacity);\r\n  top: 50%;\r\n  display: block;\r\n  margin-top: -2px;\r\n\r\n  // @include icon-bar-size;\r\n  height: 0;\r\n  width: 0;\r\n  left: 50%;\r\n  transform: translateX(-6.5px) rotate(45deg);\r\n  \r\n    &:before{\r\n      top: 0;\r\n      transition: top 75ms ease .12s,opacity 75ms ease;\r\n    }\r\n    &:after {\r\n      bottom: -3px;\r\n      transition:bottom 75ms ease .12s,transform 75ms cubic-bezier(.55,.055,.675,.19);\r\n      transform: rotate(-90deg);\r\n    }            \r\n    &:after, &:before {\r\n      display: block;\r\n      content: \"\";\r\n      position: absolute;\r\n      @include icon-bar-size;\r\n      border-radius: 4px;\r\n      background-color: var(--color-texts-opacity);\r\n    }\r\n}\r\n\r\n", "[data-pricing-dynamic][data-value-active=\"yearly\"]{\r\n  .dynamic-value{\r\n    &:before{\r\n    display: inline-block;\r\n    content: attr(data-yearly);\r\n    }\r\n  }\r\n  [data-pricing-trigger]{\r\n    background: $primary;\r\n    span{\r\n      \r\n      left: calc(100% - 33px);\r\n    }\r\n  }\r\n}\r\n[data-pricing-dynamic][data-value-active=\"monthly\"]{\r\n  .dynamic-value{\r\n    &:before{\r\n      display: inline-block;\r\n      content: attr(data-monthly);\r\n    }\r\n  }\r\n  \r\n}\r\n\r\n.dynamic-value{\r\n  &:before{\r\n    display: inline-block;\r\n    content: attr(data-active);\r\n  }\r\n}\r\n.static-value{\r\n  &:before{\r\n    display: inline-block;\r\n    content: attr(data-active);\r\n  }\r\n}", ".product-details-v-slider{\r\n  .slick-list{\r\n    margin:0 -5px;\r\n    @include tablet{\r\n      margin:-5px 0 ;\r\n    }\r\n    @include desktops{\r\n      margin:-5px 0 ;\r\n    }\r\n  }\r\n  .single-slide{\r\n      border: 1px solid var(--border-color);\r\n      background: var(--bg);\r\n      // padding: 30px;\r\n      margin:0 5px;\r\n      border-radius: 10px;\r\n      @include tablet{\r\n        margin:5px 0 ;\r\n      }\r\n      @include desktops{\r\n        margin:5px 0 ;\r\n      }\r\n      img{\r\n        width: 100%;\r\n      }\r\n    }\r\n}\r\n\r\n.product-details-slider{\r\n  .slick-list{\r\n    margin: 0 -15px;\r\n  }\r\n  .single-slide{\r\n    border: 1px solid var(--border-color);\r\n    background: var(--bg);\r\n    padding: 30px;\r\n    margin:0 15px;\r\n    border-radius: 10px;\r\n    img{\r\n      width: 100%;\r\n\r\n    }\r\n  }\r\n}\r\n.slick-slide:focus {\r\n  outline: none;\r\n}\r\n\r\n\r\n// Job Site Page\r\n\r\n.job-feature-slider{\r\n  .slick-list{\r\n    margin: 0 -15px;\r\n    padding-bottom: 45px;\r\n  }\r\n  .single-slide{\r\n    margin: 0 15px;\r\n  }\r\n  &-arrows{\r\n    display: flex;\r\n    border-radius: 10px;\r\n    background-color: #f4f7fa;\r\n    max-width: fit-content;\r\n    .slick-arrow{\r\n      font-size: 0;\r\n      border: 0;;\r\n      background: transparent;\r\n      position: relative;\r\n      &::before{\r\n        font-family: \"Font Awesome 5 Free\";\r\n        font-size: 22px;\r\n        height: 44px;\r\n        width: 34px;\r\n        display: flex;\r\n        font-weight: 900;\r\n        justify-content: center;\r\n        align-items: center;\r\n        color: $black;\r\n      }\r\n    }\r\n    .slick-prev{\r\n     &:before{\r\n      content: \"\\f060\";\r\n      opacity: 0.3;\r\n      transition: .4s;\r\n     }\r\n     &:hover{\r\n      &:before{\r\n        opacity: 1;\r\n       }\r\n     }\r\n    }\r\n    .slick-next{\r\n      &:before{\r\n        content: \"\\f061\";\r\n       }\r\n    }\r\n  }\r\n}", ".gr-timeline-wrapper {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  flex-wrap: wrap;\r\n  @include brk-point(400px){\r\n    margin: 0 -15px;\r\n  }\r\n  @include desktops{\r\n    margin: 0 -35px;\r\n  }\r\n  .single-timeline-feature {\r\n    position: relative;\r\n    z-index: 1;\r\n    margin-bottom: 40px;\r\n    text-align: center;\r\n    @include brk-point(400px){\r\n      padding-left: 15px;\r\n      padding-right: 15px;\r\n      min-width: calc(80% - 15px);\r\n      max-width: calc(80% - 15px);\r\n    }\r\n    @include brk-point(500px){\r\n      padding-left: 15px;\r\n      padding-right: 15px;\r\n      min-width: calc(50% - 15px);\r\n      max-width: calc(50% - 15px);\r\n    }\r\n    @include tablet{\r\n      margin-bottom: 0;\r\n      min-width: calc(33.333% - 15px);\r\n      max-width: calc(33.333% - 15px);\r\n    }\r\n    @include desktops{\r\n      padding-left: 35px;\r\n      padding-right: 35px;\r\n      min-width: calc(33.333% - 35px);\r\n      max-width: calc(33.333% - 35px);\r\n    }\r\n    // &:first-child{\r\n     \r\n    // }\r\n    &:nth-child(2){\r\n      &:before{\r\n        display: none;\r\n        @include tablet{\r\n          display: block;\r\n        }\r\n      }\r\n    }\r\n    &:last-child{\r\n      margin-bottom: 0;\r\n      &:before{\r\n        content: \"\";\r\n      }\r\n    }\r\n    &:before{\r\n      position: absolute;\r\n      content: url(../image/svg/l6-how-timeline.svg);\r\n      top: 20px;\r\n      left: 50%;\r\n      z-index: -1;\r\n      display: none;\r\n      @include brk-point(500px){\r\n        width: 95%;\r\n        overflow: hidden;\r\n        display: block;\r\n      }\r\n      @include tablet{\r\n        width: 95%;\r\n        overflow: hidden;\r\n      }\r\n      @include large-desktops{\r\n        width: 100%;\r\n        overflow: unset;\r\n      }\r\n    }\r\n  \r\n  }\r\n}", ".gr-nav-tabs{\r\n  @include mobile-lg{\r\n    border-bottom: 1px solid var(--border-color);\r\n  }\r\n    .nav-link{\r\n      padding-left:0px;\r\n      padding-right: 0px;\r\n      margin-right: 50px;\r\n      padding-bottom: 20px;\r\n      border-bottom: 1px solid var(--border-color);\r\n      padding-top: 20px;\r\n      margin-bottom:-1px ;\r\n      color: var(--texts-color);\r\n      @include mobile-lg{\r\n        margin-right: 100px;\r\n      }\r\n      &:last-child{\r\n        margin-right: 0;\r\n      }\r\n      &.active{\r\n        border-color: $primary;\r\n        color: $primary;\r\n      }\r\n    }\r\n}", "@each $color, $value in $theme-colors{\r\n\r\n  @each $op-key,$op-value in $gr-opacity{\r\n    .gr-bg-#{$color}-opacity-#{$op-key}{\r\n      background-color: rgba($value,$op-value);\r\n    }\r\n  }\r\n  @each $op-key,$op-value in $gr-opacity{\r\n    .gr-color-#{$color}-opacity-#{$op-key}{\r\n      color: rgba($value,$op-value);\r\n    }\r\n  }\r\n  \r\n}\r\n@each $op-key,$op-value in $gr-opacity{\r\n  .gr-opacity-#{$op-key}{\r\n    opacity:$op-value;\r\n  }\r\n}\r\n.gr-text-color{\r\n  color: var(--color-texts)!important;\r\n}\r\n.gr-text-color-opacity{\r\n  color: var(--color-texts-opacity);\r\n}\r\n.gr-fill-color{\r\n  fill: var(--color-texts)!important;;\r\n}\r\n\r\n.bg-default{\r\n  background: var(--bg);\r\n  &-1{\r\n    background: var(--bg);\r\n  }\r\n  &-2{\r\n    background: var(--bg-2);\r\n  }\r\n  &-3{\r\n    background: var(--bg-3);\r\n  }\r\n  &-4{\r\n    background: var(--bg-4);\r\n  }\r\n  &-5{\r\n    background: var(--bg-5);\r\n  }\r\n  &-6{\r\n    background: var(--bg-6);\r\n  }\r\n  &-7{\r\n    background: var(--bg-7);\r\n  }\r\n  &-8{\r\n    background: var(--bg-8);\r\n  }\r\n}\r\n\r\n.gr-fill-default-4{\r\n  fill:var(--bg-4);\r\n}\r\n\r\n.bg-opposite{\r\n  background: var(--bg-opposite);\r\n}\r\n", "// Variables\n//\n// Variables should follow the `$component-state-property-size` formula for\n// consistent naming. Ex: $nav-link-disabled-color and $modal-content-box-shadow-xs.\n\n// Color system\n\n$white:    #fff !default;\n$gray-100: #f8f9fa !default;\n$gray-200: #e9ecef !default;\n$gray-300: #dee2e6 !default;\n$gray-400: #ced4da !default;\n$gray-500: #adb5bd !default;\n$gray-600: #6c757d !default;\n$gray-700: #495057 !default;\n$gray-800: #343a40 !default;\n$gray-900: #212529 !default;\n$black:    #000 !default;\n\n$grays: () !default;\n$grays: map-merge(\n  (\n    \"100\": $gray-100,\n    \"200\": $gray-200,\n    \"300\": $gray-300,\n    \"400\": $gray-400,\n    \"500\": $gray-500,\n    \"600\": $gray-600,\n    \"700\": $gray-700,\n    \"800\": $gray-800,\n    \"900\": $gray-900\n  ),\n  $grays\n);\n\n$blue:    #007bff !default;\n$indigo:  #6610f2 !default;\n$purple:  #6f42c1 !default;\n$pink:    #e83e8c !default;\n$red:     #dc3545 !default;\n$orange:  #fd7e14 !default;\n$yellow:  #ffc107 !default;\n$green:   #28a745 !default;\n$teal:    #20c997 !default;\n$cyan:    #17a2b8 !default;\n\n$colors: () !default;\n$colors: map-merge(\n  (\n    \"blue\":       $blue,\n    \"indigo\":     $indigo,\n    \"purple\":     $purple,\n    \"pink\":       $pink,\n    \"red\":        $red,\n    \"orange\":     $orange,\n    \"yellow\":     $yellow,\n    \"green\":      $green,\n    \"teal\":       $teal,\n    \"cyan\":       $cyan,\n    \"white\":      $white,\n    \"gray\":       $gray-600,\n    \"gray-dark\":  $gray-800\n  ),\n  $colors\n);\n\n$primary:       $blue !default;\n$secondary:     $gray-600 !default;\n$success:       $green !default;\n$info:          $cyan !default;\n$warning:       $yellow !default;\n$danger:        $red !default;\n$light:         $gray-100 !default;\n$dark:          $gray-800 !default;\n\n$theme-colors: () !default;\n$theme-colors: map-merge(\n  (\n    \"primary\":    $primary,\n    \"secondary\":  $secondary,\n    \"success\":    $success,\n    \"info\":       $info,\n    \"warning\":    $warning,\n    \"danger\":     $danger,\n    \"light\":      $light,\n    \"dark\":       $dark\n  ),\n  $theme-colors\n);\n\n// Set a specific jump point for requesting color jumps\n$theme-color-interval:      8% !default;\n\n// The yiq lightness value that determines when the lightness of color changes from \"dark\" to \"light\". Acceptable values are between 0 and 255.\n$yiq-contrasted-threshold:  150 !default;\n\n// Customize the light and dark text colors for use in our YIQ color contrast function.\n$yiq-text-dark:             $gray-900 !default;\n$yiq-text-light:            $white !default;\n\n// Characters which are escaped by the escape-svg function\n$escaped-characters: (\n  (\"<\", \"%3c\"),\n  (\">\", \"%3e\"),\n  (\"#\", \"%23\"),\n  (\"(\", \"%28\"),\n  (\")\", \"%29\"),\n) !default;\n\n\n// Options\n//\n// Quickly modify global styling by enabling or disabling optional features.\n\n$enable-caret:                                true !default;\n$enable-rounded:                              true !default;\n$enable-shadows:                              false !default;\n$enable-gradients:                            false !default;\n$enable-transitions:                          true !default;\n$enable-prefers-reduced-motion-media-query:   true !default;\n$enable-hover-media-query:                    false !default; // Deprecated, no longer affects any compiled CSS\n$enable-grid-classes:                         true !default;\n$enable-pointer-cursor-for-buttons:           true !default;\n$enable-print-styles:                         true !default;\n$enable-responsive-font-sizes:                false !default;\n$enable-validation-icons:                     true !default;\n$enable-deprecation-messages:                 true !default;\n\n\n// Spacing\n//\n// Control the default styling of most Bootstrap elements by modifying these\n// variables. Mostly focused on spacing.\n// You can add more entries to the $spacers map, should you need more variation.\n\n$spacer: 1rem !default;\n$spacers: () !default;\n$spacers: map-merge(\n  (\n    0: 0,\n    1: ($spacer * .25),\n    2: ($spacer * .5),\n    3: $spacer,\n    4: ($spacer * 1.5),\n    5: ($spacer * 3)\n  ),\n  $spacers\n);\n\n// This variable affects the `.h-*` and `.w-*` classes.\n$sizes: () !default;\n$sizes: map-merge(\n  (\n    25: 25%,\n    50: 50%,\n    75: 75%,\n    100: 100%,\n    auto: auto\n  ),\n  $sizes\n);\n\n\n// Body\n//\n// Settings for the `<body>` element.\n\n$body-bg:                   $white !default;\n$body-color:                $gray-900 !default;\n\n\n// Links\n//\n// Style anchor elements.\n\n$link-color:                              theme-color(\"primary\") !default;\n$link-decoration:                         none !default;\n$link-hover-color:                        darken($link-color, 15%) !default;\n$link-hover-decoration:                   underline !default;\n// Darken percentage for links with `.text-*` class (e.g. `.text-success`)\n$emphasized-link-hover-darken-percentage: 15% !default;\n\n// Paragraphs\n//\n// Style p element.\n\n$paragraph-margin-bottom:   1rem !default;\n\n\n// Grid breakpoints\n//\n// Define the minimum dimensions at which your layout will change,\n// adapting to different screen sizes, for use in media queries.\n\n$grid-breakpoints: (\n  xs: 0,\n  sm: 576px,\n  md: 768px,\n  lg: 992px,\n  xl: 1200px\n) !default;\n\n@include _assert-ascending($grid-breakpoints, \"$grid-breakpoints\");\n@include _assert-starts-at-zero($grid-breakpoints, \"$grid-breakpoints\");\n\n\n// Grid containers\n//\n// Define the maximum width of `.container` for different screen sizes.\n\n$container-max-widths: (\n  sm: 540px,\n  md: 720px,\n  lg: 960px,\n  xl: 1140px\n) !default;\n\n@include _assert-ascending($container-max-widths, \"$container-max-widths\");\n\n\n// Grid columns\n//\n// Set the number of columns and specify the width of the gutters.\n\n$grid-columns:                12 !default;\n$grid-gutter-width:           30px !default;\n$grid-row-columns:            6 !default;\n\n\n// Components\n//\n// Define common padding and border radius sizes and more.\n\n$line-height-lg:              1.5 !default;\n$line-height-sm:              1.5 !default;\n\n$border-width:                1px !default;\n$border-color:                $gray-300 !default;\n\n$border-radius:               .25rem !default;\n$border-radius-lg:            .3rem !default;\n$border-radius-sm:            .2rem !default;\n\n$rounded-pill:                50rem !default;\n\n$box-shadow-sm:               0 .125rem .25rem rgba($black, .075) !default;\n$box-shadow:                  0 .5rem 1rem rgba($black, .15) !default;\n$box-shadow-lg:               0 1rem 3rem rgba($black, .175) !default;\n\n$component-active-color:      $white !default;\n$component-active-bg:         theme-color(\"primary\") !default;\n\n$caret-width:                 .3em !default;\n$caret-vertical-align:        $caret-width * .85 !default;\n$caret-spacing:               $caret-width * .85 !default;\n\n$transition-base:             all .2s ease-in-out !default;\n$transition-fade:             opacity .15s linear !default;\n$transition-collapse:         height .35s ease !default;\n\n$embed-responsive-aspect-ratios: () !default;\n$embed-responsive-aspect-ratios: join(\n  (\n    (21 9),\n    (16 9),\n    (4 3),\n    (1 1),\n  ),\n  $embed-responsive-aspect-ratios\n);\n\n// Typography\n//\n// Font, line-height, and color for body text, headings, and more.\n\n// stylelint-disable value-keyword-case\n$font-family-sans-serif:      -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, \"Helvetica Neue\", Arial, \"Noto Sans\", sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\" !default;\n$font-family-monospace:       SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace !default;\n$font-family-base:            $font-family-sans-serif !default;\n// stylelint-enable value-keyword-case\n\n$font-size-base:              1rem !default; // Assumes the browser default, typically `16px`\n$font-size-lg:                $font-size-base * 1.25 !default;\n$font-size-sm:                $font-size-base * .875 !default;\n\n$font-weight-lighter:         lighter !default;\n$font-weight-light:           300 !default;\n$font-weight-normal:          400 !default;\n$font-weight-bold:            700 !default;\n$font-weight-bolder:          bolder !default;\n\n$font-weight-base:            $font-weight-normal !default;\n$line-height-base:            1.5 !default;\n\n$h1-font-size:                $font-size-base * 2.5 !default;\n$h2-font-size:                $font-size-base * 2 !default;\n$h3-font-size:                $font-size-base * 1.75 !default;\n$h4-font-size:                $font-size-base * 1.5 !default;\n$h5-font-size:                $font-size-base * 1.25 !default;\n$h6-font-size:                $font-size-base !default;\n\n$headings-margin-bottom:      $spacer / 2 !default;\n$headings-font-family:        null !default;\n$headings-font-weight:        500 !default;\n$headings-line-height:        1.2 !default;\n$headings-color:              null !default;\n\n$display1-size:               6rem !default;\n$display2-size:               5.5rem !default;\n$display3-size:               4.5rem !default;\n$display4-size:               3.5rem !default;\n\n$display1-weight:             300 !default;\n$display2-weight:             300 !default;\n$display3-weight:             300 !default;\n$display4-weight:             300 !default;\n$display-line-height:         $headings-line-height !default;\n\n$lead-font-size:              $font-size-base * 1.25 !default;\n$lead-font-weight:            300 !default;\n\n$small-font-size:             80% !default;\n\n$text-muted:                  $gray-600 !default;\n\n$blockquote-small-color:      $gray-600 !default;\n$blockquote-small-font-size:  $small-font-size !default;\n$blockquote-font-size:        $font-size-base * 1.25 !default;\n\n$hr-border-color:             rgba($black, .1) !default;\n$hr-border-width:             $border-width !default;\n\n$mark-padding:                .2em !default;\n\n$dt-font-weight:              $font-weight-bold !default;\n\n$kbd-box-shadow:              inset 0 -.1rem 0 rgba($black, .25) !default;\n$nested-kbd-font-weight:      $font-weight-bold !default;\n\n$list-inline-padding:         .5rem !default;\n\n$mark-bg:                     #fcf8e3 !default;\n\n$hr-margin-y:                 $spacer !default;\n\n\n// Tables\n//\n// Customizes the `.table` component with basic values, each used across all table variations.\n\n$table-cell-padding:          .75rem !default;\n$table-cell-padding-sm:       .3rem !default;\n\n$table-color:                 $body-color !default;\n$table-bg:                    null !default;\n$table-accent-bg:             rgba($black, .05) !default;\n$table-hover-color:           $table-color !default;\n$table-hover-bg:              rgba($black, .075) !default;\n$table-active-bg:             $table-hover-bg !default;\n\n$table-border-width:          $border-width !default;\n$table-border-color:          $border-color !default;\n\n$table-head-bg:               $gray-200 !default;\n$table-head-color:            $gray-700 !default;\n$table-th-font-weight:        null !default;\n\n$table-dark-color:            $white !default;\n$table-dark-bg:               $gray-800 !default;\n$table-dark-accent-bg:        rgba($white, .05) !default;\n$table-dark-hover-color:      $table-dark-color !default;\n$table-dark-hover-bg:         rgba($white, .075) !default;\n$table-dark-border-color:     lighten($table-dark-bg, 7.5%) !default;\n\n$table-striped-order:         odd !default;\n\n$table-caption-color:         $text-muted !default;\n\n$table-bg-level:              -9 !default;\n$table-border-level:          -6 !default;\n\n\n// Buttons + Forms\n//\n// Shared variables that are reassigned to `$input-` and `$btn-` specific variables.\n\n$input-btn-padding-y:         .375rem !default;\n$input-btn-padding-x:         .75rem !default;\n$input-btn-font-family:       null !default;\n$input-btn-font-size:         $font-size-base !default;\n$input-btn-line-height:       $line-height-base !default;\n\n$input-btn-focus-width:       .2rem !default;\n$input-btn-focus-color:       rgba($component-active-bg, .25) !default;\n$input-btn-focus-box-shadow:  0 0 0 $input-btn-focus-width $input-btn-focus-color !default;\n\n$input-btn-padding-y-sm:      .25rem !default;\n$input-btn-padding-x-sm:      .5rem !default;\n$input-btn-font-size-sm:      $font-size-sm !default;\n$input-btn-line-height-sm:    $line-height-sm !default;\n\n$input-btn-padding-y-lg:      .5rem !default;\n$input-btn-padding-x-lg:      1rem !default;\n$input-btn-font-size-lg:      $font-size-lg !default;\n$input-btn-line-height-lg:    $line-height-lg !default;\n\n$input-btn-border-width:      $border-width !default;\n\n\n// Buttons\n//\n// For each of Bootstrap's buttons, define text, background, and border color.\n\n$btn-padding-y:               $input-btn-padding-y !default;\n$btn-padding-x:               $input-btn-padding-x !default;\n$btn-font-family:             $input-btn-font-family !default;\n$btn-font-size:               $input-btn-font-size !default;\n$btn-line-height:             $input-btn-line-height !default;\n$btn-white-space:             null !default; // Set to `nowrap` to prevent text wrapping\n\n$btn-padding-y-sm:            $input-btn-padding-y-sm !default;\n$btn-padding-x-sm:            $input-btn-padding-x-sm !default;\n$btn-font-size-sm:            $input-btn-font-size-sm !default;\n$btn-line-height-sm:          $input-btn-line-height-sm !default;\n\n$btn-padding-y-lg:            $input-btn-padding-y-lg !default;\n$btn-padding-x-lg:            $input-btn-padding-x-lg !default;\n$btn-font-size-lg:            $input-btn-font-size-lg !default;\n$btn-line-height-lg:          $input-btn-line-height-lg !default;\n\n$btn-border-width:            $input-btn-border-width !default;\n\n$btn-font-weight:             $font-weight-normal !default;\n$btn-box-shadow:              inset 0 1px 0 rgba($white, .15), 0 1px 1px rgba($black, .075) !default;\n$btn-focus-width:             $input-btn-focus-width !default;\n$btn-focus-box-shadow:        $input-btn-focus-box-shadow !default;\n$btn-disabled-opacity:        .65 !default;\n$btn-active-box-shadow:       inset 0 3px 5px rgba($black, .125) !default;\n\n$btn-link-disabled-color:     $gray-600 !default;\n\n$btn-block-spacing-y:         .5rem !default;\n\n// Allows for customizing button radius independently from global border radius\n$btn-border-radius:           $border-radius !default;\n$btn-border-radius-lg:        $border-radius-lg !default;\n$btn-border-radius-sm:        $border-radius-sm !default;\n\n$btn-transition:              color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out !default;\n\n\n// Forms\n\n$label-margin-bottom:                   .5rem !default;\n\n$input-padding-y:                       $input-btn-padding-y !default;\n$input-padding-x:                       $input-btn-padding-x !default;\n$input-font-family:                     $input-btn-font-family !default;\n$input-font-size:                       $input-btn-font-size !default;\n$input-font-weight:                     $font-weight-base !default;\n$input-line-height:                     $input-btn-line-height !default;\n\n$input-padding-y-sm:                    $input-btn-padding-y-sm !default;\n$input-padding-x-sm:                    $input-btn-padding-x-sm !default;\n$input-font-size-sm:                    $input-btn-font-size-sm !default;\n$input-line-height-sm:                  $input-btn-line-height-sm !default;\n\n$input-padding-y-lg:                    $input-btn-padding-y-lg !default;\n$input-padding-x-lg:                    $input-btn-padding-x-lg !default;\n$input-font-size-lg:                    $input-btn-font-size-lg !default;\n$input-line-height-lg:                  $input-btn-line-height-lg !default;\n\n$input-bg:                              $white !default;\n$input-disabled-bg:                     $gray-200 !default;\n\n$input-color:                           $gray-700 !default;\n$input-border-color:                    $gray-400 !default;\n$input-border-width:                    $input-btn-border-width !default;\n$input-box-shadow:                      inset 0 1px 1px rgba($black, .075) !default;\n\n$input-border-radius:                   $border-radius !default;\n$input-border-radius-lg:                $border-radius-lg !default;\n$input-border-radius-sm:                $border-radius-sm !default;\n\n$input-focus-bg:                        $input-bg !default;\n$input-focus-border-color:              lighten($component-active-bg, 25%) !default;\n$input-focus-color:                     $input-color !default;\n$input-focus-width:                     $input-btn-focus-width !default;\n$input-focus-box-shadow:                $input-btn-focus-box-shadow !default;\n\n$input-placeholder-color:               $gray-600 !default;\n$input-plaintext-color:                 $body-color !default;\n\n$input-height-border:                   $input-border-width * 2 !default;\n\n$input-height-inner:                    add($input-line-height * 1em, $input-padding-y * 2) !default;\n$input-height-inner-half:               add($input-line-height * .5em, $input-padding-y) !default;\n$input-height-inner-quarter:            add($input-line-height * .25em, $input-padding-y / 2) !default;\n\n$input-height:                          add($input-line-height * 1em, add($input-padding-y * 2, $input-height-border, false)) !default;\n$input-height-sm:                       add($input-line-height-sm * 1em, add($input-padding-y-sm * 2, $input-height-border, false)) !default;\n$input-height-lg:                       add($input-line-height-lg * 1em, add($input-padding-y-lg * 2, $input-height-border, false)) !default;\n\n$input-transition:                      border-color .15s ease-in-out, box-shadow .15s ease-in-out !default;\n\n$form-text-margin-top:                  .25rem !default;\n\n$form-check-input-gutter:               1.25rem !default;\n$form-check-input-margin-y:             .3rem !default;\n$form-check-input-margin-x:             .25rem !default;\n\n$form-check-inline-margin-x:            .75rem !default;\n$form-check-inline-input-margin-x:      .3125rem !default;\n\n$form-grid-gutter-width:                10px !default;\n$form-group-margin-bottom:              1rem !default;\n\n$input-group-addon-color:               $input-color !default;\n$input-group-addon-bg:                  $gray-200 !default;\n$input-group-addon-border-color:        $input-border-color !default;\n\n$custom-forms-transition:               background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out !default;\n\n$custom-control-gutter:                 .5rem !default;\n$custom-control-spacer-x:               1rem !default;\n$custom-control-cursor:                 null !default;\n\n$custom-control-indicator-size:         1rem !default;\n$custom-control-indicator-bg:           $input-bg !default;\n\n$custom-control-indicator-bg-size:      50% 50% !default;\n$custom-control-indicator-box-shadow:   $input-box-shadow !default;\n$custom-control-indicator-border-color: $gray-500 !default;\n$custom-control-indicator-border-width: $input-border-width !default;\n\n$custom-control-label-color:            null !default;\n\n$custom-control-indicator-disabled-bg:          $input-disabled-bg !default;\n$custom-control-label-disabled-color:           $gray-600 !default;\n\n$custom-control-indicator-checked-color:        $component-active-color !default;\n$custom-control-indicator-checked-bg:           $component-active-bg !default;\n$custom-control-indicator-checked-disabled-bg:  rgba(theme-color(\"primary\"), .5) !default;\n$custom-control-indicator-checked-box-shadow:   null !default;\n$custom-control-indicator-checked-border-color: $custom-control-indicator-checked-bg !default;\n\n$custom-control-indicator-focus-box-shadow:     $input-focus-box-shadow !default;\n$custom-control-indicator-focus-border-color:   $input-focus-border-color !default;\n\n$custom-control-indicator-active-color:         $component-active-color !default;\n$custom-control-indicator-active-bg:            lighten($component-active-bg, 35%) !default;\n$custom-control-indicator-active-box-shadow:    null !default;\n$custom-control-indicator-active-border-color:  $custom-control-indicator-active-bg !default;\n\n$custom-checkbox-indicator-border-radius:       $border-radius !default;\n$custom-checkbox-indicator-icon-checked:        url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' width='8' height='8' viewBox='0 0 8 8'><path fill='#{$custom-control-indicator-checked-color}' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26l2.974 2.99L8 2.193z'/></svg>\") !default;\n\n$custom-checkbox-indicator-indeterminate-bg:           $component-active-bg !default;\n$custom-checkbox-indicator-indeterminate-color:        $custom-control-indicator-checked-color !default;\n$custom-checkbox-indicator-icon-indeterminate:         url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' width='4' height='4' viewBox='0 0 4 4'><path stroke='#{$custom-checkbox-indicator-indeterminate-color}' d='M0 2h4'/></svg>\") !default;\n$custom-checkbox-indicator-indeterminate-box-shadow:   null !default;\n$custom-checkbox-indicator-indeterminate-border-color: $custom-checkbox-indicator-indeterminate-bg !default;\n\n$custom-radio-indicator-border-radius:          50% !default;\n$custom-radio-indicator-icon-checked:           url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' width='12' height='12' viewBox='-4 -4 8 8'><circle r='3' fill='#{$custom-control-indicator-checked-color}'/></svg>\") !default;\n\n$custom-switch-width:                           $custom-control-indicator-size * 1.75 !default;\n$custom-switch-indicator-border-radius:         $custom-control-indicator-size / 2 !default;\n$custom-switch-indicator-size:                  subtract($custom-control-indicator-size, $custom-control-indicator-border-width * 4) !default;\n\n$custom-select-padding-y:           $input-padding-y !default;\n$custom-select-padding-x:           $input-padding-x !default;\n$custom-select-font-family:         $input-font-family !default;\n$custom-select-font-size:           $input-font-size !default;\n$custom-select-height:              $input-height !default;\n$custom-select-indicator-padding:   1rem !default; // Extra padding to account for the presence of the background-image based indicator\n$custom-select-font-weight:         $input-font-weight !default;\n$custom-select-line-height:         $input-line-height !default;\n$custom-select-color:               $input-color !default;\n$custom-select-disabled-color:      $gray-600 !default;\n$custom-select-bg:                  $input-bg !default;\n$custom-select-disabled-bg:         $gray-200 !default;\n$custom-select-bg-size:             8px 10px !default; // In pixels because image dimensions\n$custom-select-indicator-color:     $gray-800 !default;\n$custom-select-indicator:           url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' width='4' height='5' viewBox='0 0 4 5'><path fill='#{$custom-select-indicator-color}' d='M2 0L0 2h4zm0 5L0 3h4z'/></svg>\") !default;\n$custom-select-background:          escape-svg($custom-select-indicator) no-repeat right $custom-select-padding-x center / $custom-select-bg-size !default; // Used so we can have multiple background elements (e.g., arrow and feedback icon)\n\n$custom-select-feedback-icon-padding-right: add(1em * .75, (2 * $custom-select-padding-y * .75) + $custom-select-padding-x + $custom-select-indicator-padding) !default;\n$custom-select-feedback-icon-position:      center right ($custom-select-padding-x + $custom-select-indicator-padding) !default;\n$custom-select-feedback-icon-size:          $input-height-inner-half $input-height-inner-half !default;\n\n$custom-select-border-width:        $input-border-width !default;\n$custom-select-border-color:        $input-border-color !default;\n$custom-select-border-radius:       $border-radius !default;\n$custom-select-box-shadow:          inset 0 1px 2px rgba($black, .075) !default;\n\n$custom-select-focus-border-color:  $input-focus-border-color !default;\n$custom-select-focus-width:         $input-focus-width !default;\n$custom-select-focus-box-shadow:    0 0 0 $custom-select-focus-width $input-btn-focus-color !default;\n\n$custom-select-padding-y-sm:        $input-padding-y-sm !default;\n$custom-select-padding-x-sm:        $input-padding-x-sm !default;\n$custom-select-font-size-sm:        $input-font-size-sm !default;\n$custom-select-height-sm:           $input-height-sm !default;\n\n$custom-select-padding-y-lg:        $input-padding-y-lg !default;\n$custom-select-padding-x-lg:        $input-padding-x-lg !default;\n$custom-select-font-size-lg:        $input-font-size-lg !default;\n$custom-select-height-lg:           $input-height-lg !default;\n\n$custom-range-track-width:          100% !default;\n$custom-range-track-height:         .5rem !default;\n$custom-range-track-cursor:         pointer !default;\n$custom-range-track-bg:             $gray-300 !default;\n$custom-range-track-border-radius:  1rem !default;\n$custom-range-track-box-shadow:     inset 0 .25rem .25rem rgba($black, .1) !default;\n\n$custom-range-thumb-width:                   1rem !default;\n$custom-range-thumb-height:                  $custom-range-thumb-width !default;\n$custom-range-thumb-bg:                      $component-active-bg !default;\n$custom-range-thumb-border:                  0 !default;\n$custom-range-thumb-border-radius:           1rem !default;\n$custom-range-thumb-box-shadow:              0 .1rem .25rem rgba($black, .1) !default;\n$custom-range-thumb-focus-box-shadow:        0 0 0 1px $body-bg, $input-focus-box-shadow !default;\n$custom-range-thumb-focus-box-shadow-width:  $input-focus-width !default; // For focus box shadow issue in IE/Edge\n$custom-range-thumb-active-bg:               lighten($component-active-bg, 35%) !default;\n$custom-range-thumb-disabled-bg:             $gray-500 !default;\n\n$custom-file-height:                $input-height !default;\n$custom-file-height-inner:          $input-height-inner !default;\n$custom-file-focus-border-color:    $input-focus-border-color !default;\n$custom-file-focus-box-shadow:      $input-focus-box-shadow !default;\n$custom-file-disabled-bg:           $input-disabled-bg !default;\n\n$custom-file-padding-y:             $input-padding-y !default;\n$custom-file-padding-x:             $input-padding-x !default;\n$custom-file-line-height:           $input-line-height !default;\n$custom-file-font-family:           $input-font-family !default;\n$custom-file-font-weight:           $input-font-weight !default;\n$custom-file-color:                 $input-color !default;\n$custom-file-bg:                    $input-bg !default;\n$custom-file-border-width:          $input-border-width !default;\n$custom-file-border-color:          $input-border-color !default;\n$custom-file-border-radius:         $input-border-radius !default;\n$custom-file-box-shadow:            $input-box-shadow !default;\n$custom-file-button-color:          $custom-file-color !default;\n$custom-file-button-bg:             $input-group-addon-bg !default;\n$custom-file-text: (\n  en: \"Browse\"\n) !default;\n\n\n// Form validation\n\n$form-feedback-margin-top:          $form-text-margin-top !default;\n$form-feedback-font-size:           $small-font-size !default;\n$form-feedback-valid-color:         theme-color(\"success\") !default;\n$form-feedback-invalid-color:       theme-color(\"danger\") !default;\n\n$form-feedback-icon-valid-color:    $form-feedback-valid-color !default;\n$form-feedback-icon-valid:          url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' width='8' height='8' viewBox='0 0 8 8'><path fill='#{$form-feedback-icon-valid-color}' d='M2.3 6.73L.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/></svg>\") !default;\n$form-feedback-icon-invalid-color:  $form-feedback-invalid-color !default;\n$form-feedback-icon-invalid:        url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' width='12' height='12' fill='none' stroke='#{$form-feedback-icon-invalid-color}' viewBox='0 0 12 12'><circle cx='6' cy='6' r='4.5'/><path stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/><circle cx='6' cy='8.2' r='.6' fill='#{$form-feedback-icon-invalid-color}' stroke='none'/></svg>\") !default;\n\n$form-validation-states: () !default;\n$form-validation-states: map-merge(\n  (\n    \"valid\": (\n      \"color\": $form-feedback-valid-color,\n      \"icon\": $form-feedback-icon-valid\n    ),\n    \"invalid\": (\n      \"color\": $form-feedback-invalid-color,\n      \"icon\": $form-feedback-icon-invalid\n    ),\n  ),\n  $form-validation-states\n);\n\n// Z-index master list\n//\n// Warning: Avoid customizing these values. They're used for a bird's eye view\n// of components dependent on the z-axis and are designed to all work together.\n\n$zindex-dropdown:                   1000 !default;\n$zindex-sticky:                     1020 !default;\n$zindex-fixed:                      1030 !default;\n$zindex-modal-backdrop:             1040 !default;\n$zindex-modal:                      1050 !default;\n$zindex-popover:                    1060 !default;\n$zindex-tooltip:                    1070 !default;\n\n\n// Navs\n\n$nav-link-padding-y:                .5rem !default;\n$nav-link-padding-x:                1rem !default;\n$nav-link-disabled-color:           $gray-600 !default;\n\n$nav-tabs-border-color:             $gray-300 !default;\n$nav-tabs-border-width:             $border-width !default;\n$nav-tabs-border-radius:            $border-radius !default;\n$nav-tabs-link-hover-border-color:  $gray-200 $gray-200 $nav-tabs-border-color !default;\n$nav-tabs-link-active-color:        $gray-700 !default;\n$nav-tabs-link-active-bg:           $body-bg !default;\n$nav-tabs-link-active-border-color: $gray-300 $gray-300 $nav-tabs-link-active-bg !default;\n\n$nav-pills-border-radius:           $border-radius !default;\n$nav-pills-link-active-color:       $component-active-color !default;\n$nav-pills-link-active-bg:          $component-active-bg !default;\n\n$nav-divider-color:                 $gray-200 !default;\n$nav-divider-margin-y:              $spacer / 2 !default;\n\n\n// Navbar\n\n$navbar-padding-y:                  $spacer / 2 !default;\n$navbar-padding-x:                  $spacer !default;\n\n$navbar-nav-link-padding-x:         .5rem !default;\n\n$navbar-brand-font-size:            $font-size-lg !default;\n// Compute the navbar-brand padding-y so the navbar-brand will have the same height as navbar-text and nav-link\n$nav-link-height:                   $font-size-base * $line-height-base + $nav-link-padding-y * 2 !default;\n$navbar-brand-height:               $navbar-brand-font-size * $line-height-base !default;\n$navbar-brand-padding-y:            ($nav-link-height - $navbar-brand-height) / 2 !default;\n\n$navbar-toggler-padding-y:          .25rem !default;\n$navbar-toggler-padding-x:          .75rem !default;\n$navbar-toggler-font-size:          $font-size-lg !default;\n$navbar-toggler-border-radius:      $btn-border-radius !default;\n\n$navbar-dark-color:                 rgba($white, .5) !default;\n$navbar-dark-hover-color:           rgba($white, .75) !default;\n$navbar-dark-active-color:          $white !default;\n$navbar-dark-disabled-color:        rgba($white, .25) !default;\n$navbar-dark-toggler-icon-bg:       url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' width='30' height='30' viewBox='0 0 30 30'><path stroke='#{$navbar-dark-color}' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/></svg>\") !default;\n$navbar-dark-toggler-border-color:  rgba($white, .1) !default;\n\n$navbar-light-color:                rgba($black, .5) !default;\n$navbar-light-hover-color:          rgba($black, .7) !default;\n$navbar-light-active-color:         rgba($black, .9) !default;\n$navbar-light-disabled-color:       rgba($black, .3) !default;\n$navbar-light-toggler-icon-bg:      url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' width='30' height='30' viewBox='0 0 30 30'><path stroke='#{$navbar-light-color}' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/></svg>\") !default;\n$navbar-light-toggler-border-color: rgba($black, .1) !default;\n\n$navbar-light-brand-color:                $navbar-light-active-color !default;\n$navbar-light-brand-hover-color:          $navbar-light-active-color !default;\n$navbar-dark-brand-color:                 $navbar-dark-active-color !default;\n$navbar-dark-brand-hover-color:           $navbar-dark-active-color !default;\n\n\n// Dropdowns\n//\n// Dropdown menu container and contents.\n\n$dropdown-min-width:                10rem !default;\n$dropdown-padding-x:                0 !default;\n$dropdown-padding-y:                .5rem !default;\n$dropdown-spacer:                   .125rem !default;\n$dropdown-font-size:                $font-size-base !default;\n$dropdown-color:                    $body-color !default;\n$dropdown-bg:                       $white !default;\n$dropdown-border-color:             rgba($black, .15) !default;\n$dropdown-border-radius:            $border-radius !default;\n$dropdown-border-width:             $border-width !default;\n$dropdown-inner-border-radius:      subtract($dropdown-border-radius, $dropdown-border-width) !default;\n$dropdown-divider-bg:               $gray-200 !default;\n$dropdown-divider-margin-y:         $nav-divider-margin-y !default;\n$dropdown-box-shadow:               0 .5rem 1rem rgba($black, .175) !default;\n\n$dropdown-link-color:               $gray-900 !default;\n$dropdown-link-hover-color:         darken($gray-900, 5%) !default;\n$dropdown-link-hover-bg:            $gray-100 !default;\n\n$dropdown-link-active-color:        $component-active-color !default;\n$dropdown-link-active-bg:           $component-active-bg !default;\n\n$dropdown-link-disabled-color:      $gray-600 !default;\n\n$dropdown-item-padding-y:           .25rem !default;\n$dropdown-item-padding-x:           1.5rem !default;\n\n$dropdown-header-color:             $gray-600 !default;\n$dropdown-header-padding:           $dropdown-padding-y $dropdown-item-padding-x !default;\n\n\n// Pagination\n\n$pagination-padding-y:              .5rem !default;\n$pagination-padding-x:              .75rem !default;\n$pagination-padding-y-sm:           .25rem !default;\n$pagination-padding-x-sm:           .5rem !default;\n$pagination-padding-y-lg:           .75rem !default;\n$pagination-padding-x-lg:           1.5rem !default;\n$pagination-line-height:            1.25 !default;\n\n$pagination-color:                  $link-color !default;\n$pagination-bg:                     $white !default;\n$pagination-border-width:           $border-width !default;\n$pagination-border-color:           $gray-300 !default;\n\n$pagination-focus-box-shadow:       $input-btn-focus-box-shadow !default;\n$pagination-focus-outline:          0 !default;\n\n$pagination-hover-color:            $link-hover-color !default;\n$pagination-hover-bg:               $gray-200 !default;\n$pagination-hover-border-color:     $gray-300 !default;\n\n$pagination-active-color:           $component-active-color !default;\n$pagination-active-bg:              $component-active-bg !default;\n$pagination-active-border-color:    $pagination-active-bg !default;\n\n$pagination-disabled-color:         $gray-600 !default;\n$pagination-disabled-bg:            $white !default;\n$pagination-disabled-border-color:  $gray-300 !default;\n\n\n// Jumbotron\n\n$jumbotron-padding:                 2rem !default;\n$jumbotron-color:                   null !default;\n$jumbotron-bg:                      $gray-200 !default;\n\n\n// Cards\n\n$card-spacer-y:                     .75rem !default;\n$card-spacer-x:                     1.25rem !default;\n$card-border-width:                 $border-width !default;\n$card-border-radius:                $border-radius !default;\n$card-border-color:                 rgba($black, .125) !default;\n$card-inner-border-radius:          subtract($card-border-radius, $card-border-width) !default;\n$card-cap-bg:                       rgba($black, .03) !default;\n$card-cap-color:                    null !default;\n$card-height:                       null !default;\n$card-color:                        null !default;\n$card-bg:                           $white !default;\n\n$card-img-overlay-padding:          1.25rem !default;\n\n$card-group-margin:                 $grid-gutter-width / 2 !default;\n$card-deck-margin:                  $card-group-margin !default;\n\n$card-columns-count:                3 !default;\n$card-columns-gap:                  1.25rem !default;\n$card-columns-margin:               $card-spacer-y !default;\n\n\n// Tooltips\n\n$tooltip-font-size:                 $font-size-sm !default;\n$tooltip-max-width:                 200px !default;\n$tooltip-color:                     $white !default;\n$tooltip-bg:                        $black !default;\n$tooltip-border-radius:             $border-radius !default;\n$tooltip-opacity:                   .9 !default;\n$tooltip-padding-y:                 .25rem !default;\n$tooltip-padding-x:                 .5rem !default;\n$tooltip-margin:                    0 !default;\n\n$tooltip-arrow-width:               .8rem !default;\n$tooltip-arrow-height:              .4rem !default;\n$tooltip-arrow-color:               $tooltip-bg !default;\n\n// Form tooltips must come after regular tooltips\n$form-feedback-tooltip-padding-y:     $tooltip-padding-y !default;\n$form-feedback-tooltip-padding-x:     $tooltip-padding-x !default;\n$form-feedback-tooltip-font-size:     $tooltip-font-size !default;\n$form-feedback-tooltip-line-height:   $line-height-base !default;\n$form-feedback-tooltip-opacity:       $tooltip-opacity !default;\n$form-feedback-tooltip-border-radius: $tooltip-border-radius !default;\n\n\n// Popovers\n\n$popover-font-size:                 $font-size-sm !default;\n$popover-bg:                        $white !default;\n$popover-max-width:                 276px !default;\n$popover-border-width:              $border-width !default;\n$popover-border-color:              rgba($black, .2) !default;\n$popover-border-radius:             $border-radius-lg !default;\n$popover-inner-border-radius:       subtract($popover-border-radius, $popover-border-width) !default;\n$popover-box-shadow:                0 .25rem .5rem rgba($black, .2) !default;\n\n$popover-header-bg:                 darken($popover-bg, 3%) !default;\n$popover-header-color:              $headings-color !default;\n$popover-header-padding-y:          .5rem !default;\n$popover-header-padding-x:          .75rem !default;\n\n$popover-body-color:                $body-color !default;\n$popover-body-padding-y:            $popover-header-padding-y !default;\n$popover-body-padding-x:            $popover-header-padding-x !default;\n\n$popover-arrow-width:               1rem !default;\n$popover-arrow-height:              .5rem !default;\n$popover-arrow-color:               $popover-bg !default;\n\n$popover-arrow-outer-color:         fade-in($popover-border-color, .05) !default;\n\n\n// Toasts\n\n$toast-max-width:                   350px !default;\n$toast-padding-x:                   .75rem !default;\n$toast-padding-y:                   .25rem !default;\n$toast-font-size:                   .875rem !default;\n$toast-color:                       null !default;\n$toast-background-color:            rgba($white, .85) !default;\n$toast-border-width:                1px !default;\n$toast-border-color:                rgba(0, 0, 0, .1) !default;\n$toast-border-radius:               .25rem !default;\n$toast-box-shadow:                  0 .25rem .75rem rgba($black, .1) !default;\n\n$toast-header-color:                $gray-600 !default;\n$toast-header-background-color:     rgba($white, .85) !default;\n$toast-header-border-color:         rgba(0, 0, 0, .05) !default;\n\n\n// Badges\n\n$badge-font-size:                   75% !default;\n$badge-font-weight:                 $font-weight-bold !default;\n$badge-padding-y:                   .25em !default;\n$badge-padding-x:                   .4em !default;\n$badge-border-radius:               $border-radius !default;\n\n$badge-transition:                  $btn-transition !default;\n$badge-focus-width:                 $input-btn-focus-width !default;\n\n$badge-pill-padding-x:              .6em !default;\n// Use a higher than normal value to ensure completely rounded edges when\n// customizing padding or font-size on labels.\n$badge-pill-border-radius:          10rem !default;\n\n\n// Modals\n\n// Padding applied to the modal body\n$modal-inner-padding:               1rem !default;\n\n// Margin between elements in footer, must be lower than or equal to 2 * $modal-inner-padding\n$modal-footer-margin-between:       .5rem !default;\n\n$modal-dialog-margin:               .5rem !default;\n$modal-dialog-margin-y-sm-up:       1.75rem !default;\n\n$modal-title-line-height:           $line-height-base !default;\n\n$modal-content-color:               null !default;\n$modal-content-bg:                  $white !default;\n$modal-content-border-color:        rgba($black, .2) !default;\n$modal-content-border-width:        $border-width !default;\n$modal-content-border-radius:       $border-radius-lg !default;\n$modal-content-inner-border-radius: subtract($modal-content-border-radius, $modal-content-border-width) !default;\n$modal-content-box-shadow-xs:       0 .25rem .5rem rgba($black, .5) !default;\n$modal-content-box-shadow-sm-up:    0 .5rem 1rem rgba($black, .5) !default;\n\n$modal-backdrop-bg:                 $black !default;\n$modal-backdrop-opacity:            .5 !default;\n$modal-header-border-color:         $border-color !default;\n$modal-footer-border-color:         $modal-header-border-color !default;\n$modal-header-border-width:         $modal-content-border-width !default;\n$modal-footer-border-width:         $modal-header-border-width !default;\n$modal-header-padding-y:            1rem !default;\n$modal-header-padding-x:            1rem !default;\n$modal-header-padding:              $modal-header-padding-y $modal-header-padding-x !default; // Keep this for backwards compatibility\n\n$modal-xl:                          1140px !default;\n$modal-lg:                          800px !default;\n$modal-md:                          500px !default;\n$modal-sm:                          300px !default;\n\n$modal-fade-transform:              translate(0, -50px) !default;\n$modal-show-transform:              none !default;\n$modal-transition:                  transform .3s ease-out !default;\n$modal-scale-transform:             scale(1.02) !default;\n\n\n// Alerts\n//\n// Define alert colors, border radius, and padding.\n\n$alert-padding-y:                   .75rem !default;\n$alert-padding-x:                   1.25rem !default;\n$alert-margin-bottom:               1rem !default;\n$alert-border-radius:               $border-radius !default;\n$alert-link-font-weight:            $font-weight-bold !default;\n$alert-border-width:                $border-width !default;\n\n$alert-bg-level:                    -10 !default;\n$alert-border-level:                -9 !default;\n$alert-color-level:                 6 !default;\n\n\n// Progress bars\n\n$progress-height:                   1rem !default;\n$progress-font-size:                $font-size-base * .75 !default;\n$progress-bg:                       $gray-200 !default;\n$progress-border-radius:            $border-radius !default;\n$progress-box-shadow:               inset 0 .1rem .1rem rgba($black, .1) !default;\n$progress-bar-color:                $white !default;\n$progress-bar-bg:                   theme-color(\"primary\") !default;\n$progress-bar-animation-timing:     1s linear infinite !default;\n$progress-bar-transition:           width .6s ease !default;\n\n\n// List group\n\n$list-group-color:                  null !default;\n$list-group-bg:                     $white !default;\n$list-group-border-color:           rgba($black, .125) !default;\n$list-group-border-width:           $border-width !default;\n$list-group-border-radius:          $border-radius !default;\n\n$list-group-item-padding-y:         .75rem !default;\n$list-group-item-padding-x:         1.25rem !default;\n\n$list-group-hover-bg:               $gray-100 !default;\n$list-group-active-color:           $component-active-color !default;\n$list-group-active-bg:              $component-active-bg !default;\n$list-group-active-border-color:    $list-group-active-bg !default;\n\n$list-group-disabled-color:         $gray-600 !default;\n$list-group-disabled-bg:            $list-group-bg !default;\n\n$list-group-action-color:           $gray-700 !default;\n$list-group-action-hover-color:     $list-group-action-color !default;\n\n$list-group-action-active-color:    $body-color !default;\n$list-group-action-active-bg:       $gray-200 !default;\n\n\n// Image thumbnails\n\n$thumbnail-padding:                 .25rem !default;\n$thumbnail-bg:                      $body-bg !default;\n$thumbnail-border-width:            $border-width !default;\n$thumbnail-border-color:            $gray-300 !default;\n$thumbnail-border-radius:           $border-radius !default;\n$thumbnail-box-shadow:              0 1px 2px rgba($black, .075) !default;\n\n\n// Figures\n\n$figure-caption-font-size:          90% !default;\n$figure-caption-color:              $gray-600 !default;\n\n\n// Breadcrumbs\n\n$breadcrumb-font-size:              null !default;\n\n$breadcrumb-padding-y:              .75rem !default;\n$breadcrumb-padding-x:              1rem !default;\n$breadcrumb-item-padding:           .5rem !default;\n\n$breadcrumb-margin-bottom:          1rem !default;\n\n$breadcrumb-bg:                     $gray-200 !default;\n$breadcrumb-divider-color:          $gray-600 !default;\n$breadcrumb-active-color:           $gray-600 !default;\n$breadcrumb-divider:                quote(\"/\") !default;\n\n$breadcrumb-border-radius:          $border-radius !default;\n\n\n// Carousel\n\n$carousel-control-color:             $white !default;\n$carousel-control-width:             15% !default;\n$carousel-control-opacity:           .5 !default;\n$carousel-control-hover-opacity:     .9 !default;\n$carousel-control-transition:        opacity .15s ease !default;\n\n$carousel-indicator-width:           30px !default;\n$carousel-indicator-height:          3px !default;\n$carousel-indicator-hit-area-height: 10px !default;\n$carousel-indicator-spacer:          3px !default;\n$carousel-indicator-active-bg:       $white !default;\n$carousel-indicator-transition:      opacity .6s ease !default;\n\n$carousel-caption-width:             70% !default;\n$carousel-caption-color:             $white !default;\n\n$carousel-control-icon-width:        20px !default;\n\n$carousel-control-prev-icon-bg:      url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' fill='#{$carousel-control-color}' width='8' height='8' viewBox='0 0 8 8'><path d='M5.25 0l-4 4 4 4 1.5-1.5L4.25 4l2.5-2.5L5.25 0z'/></svg>\") !default;\n$carousel-control-next-icon-bg:      url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' fill='#{$carousel-control-color}' width='8' height='8' viewBox='0 0 8 8'><path d='M2.75 0l-1.5 1.5L3.75 4l-2.5 2.5L2.75 8l4-4-4-4z'/></svg>\") !default;\n\n$carousel-transition-duration:       .6s !default;\n$carousel-transition:                transform $carousel-transition-duration ease-in-out !default; // Define transform transition first if using multiple transitions (e.g., `transform 2s ease, opacity .5s ease-out`)\n\n\n// Spinners\n\n$spinner-width:         2rem !default;\n$spinner-height:        $spinner-width !default;\n$spinner-border-width:  .25em !default;\n\n$spinner-width-sm:        1rem !default;\n$spinner-height-sm:       $spinner-width-sm !default;\n$spinner-border-width-sm: .2em !default;\n\n\n// Close\n\n$close-font-size:                   $font-size-base * 1.5 !default;\n$close-font-weight:                 $font-weight-bold !default;\n$close-color:                       $black !default;\n$close-text-shadow:                 0 1px 0 $white !default;\n\n\n// Code\n\n$code-font-size:                    87.5% !default;\n$code-color:                        $pink !default;\n\n$kbd-padding-y:                     .2rem !default;\n$kbd-padding-x:                     .4rem !default;\n$kbd-font-size:                     $code-font-size !default;\n$kbd-color:                         $white !default;\n$kbd-bg:                            $gray-900 !default;\n\n$pre-color:                         $gray-900 !default;\n$pre-scrollable-max-height:         340px !default;\n\n\n// Utilities\n\n$displays: none, inline, inline-block, block, table, table-row, table-cell, flex, inline-flex !default;\n$overflows: auto, hidden !default;\n$positions: static, relative, absolute, fixed, sticky !default;\n$user-selects: all, auto, none !default;\n\n\n// Printing\n\n$print-page-size:                   a3 !default;\n$print-body-min-width:              map-get($grid-breakpoints, \"lg\") !default;\n", "$grays: ();\r\n$grays: map-merge(\r\n  (\r\n    \"110\": $gray-110, \r\n    \"120\": $gray-120, \r\n    \"210\": $gray-210, \r\n    \"310\": $gray-310, \r\n    \"610\": $gray-610, \r\n    \"710\": $gray-710,\r\n    \"910\": $gray-910,\r\n    \"920\": $gray-920,\r\n    \"930\": $gray-930,\r\n    \"940\": $gray-940,\r\n  ),\r\n  $grays\r\n);\r\n\r\n\r\n$theme-colors: () ;\r\n$theme-colors: map-merge(\r\n  (\r\n    \"primary\":    $primary,\r\n    \"secondary\":  $secondary,\r\n    // \"light\":      $light,\r\n    // \"dark\":       $dark\r\n    \"red\":              $red,\r\n    \"green\":            $green,\r\n    \"green-shamrock\":   $green-shamrock,\r\n    \"blue\":             $blue,\r\n    \"sky-blue\":         $sky-blue,\r\n    \"yellow\":           $yellow,\r\n    \"yellow-orange\":    $yellow-orange,\r\n    \"blackish-blue\":    $blackish-blue,\r\n    \"black\":            $black,\r\n    \"mirage\":           $mirage,\r\n    \"mirage-2\":         $mirage-2,\r\n    \"white\":            $white,\r\n    \"smoke\":            $smoke,\r\n    \"storm\":            $storm,\r\n    \"ghost\":            $ghost,\r\n    \"gray-1\":           $gray-110,\r\n    \"gray-2\":           $gray-120,\r\n    \"gray-3\":           $gray-210,\r\n    \"gray-310\":         $gray-310,\r\n    \"gray-opacity\":     $gray-opacity,\r\n    \"blackish-blue-opacity\" : $blackish-blue-opacity,\r\n    \"narvik\"    :#EDF9F2\r\n  ),\r\n  $theme-colors\r\n);\r\n\r\n\r\n//-------------------------\r\n//-- Opacity Values\r\n//-------------------------\r\n$gr-opacity: ();\r\n$gr-opacity: map-merge(\r\n  (\r\n    visible : 1,\r\n    1 : .1,\r\n    2 : .2,\r\n    3 : .3,\r\n    4 : .4,\r\n    5 : .5,\r\n    6 : .6,\r\n    7 : .7,\r\n    8 : .8,\r\n    9 : .9\r\n  ),\r\n  $gr-opacity\r\n);\r\n\r\n\r\n\r\n", "\r\n.gr-flex-all-center {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n \r\n.gr-flex-y-center{\r\n  display: flex;\r\n  align-items: center;\r\n}", ".row-sm{\r\n  @include mobile-lg{\r\n    @include make-row();\r\n  }\r\n}\r\n.row-lg{\r\n  @include desktops{\r\n    @include make-row();\r\n  }\r\n}\r\n.row-md{\r\n  @include tablet{\r\n    @include make-row();\r\n  }\r\n}\r\n.row-xl{\r\n  @include large-desktops{\r\n    @include make-row();\r\n  }\r\n}\r\n", "\r\n/*~~~~~ Normal Shadows ~~~~~*/\r\n@each $key, $value in $gr-shadows {\r\n  .shadow-#{$key}{\r\n      box-shadow: $value;\r\n  }\r\n}\r\n\r\n\r\n", "$shadow-1 : 0 34px 33px -23px rgba(22, 28, 45, 0.13);\r\n$shadow-2 : 0 31px 34px -20px rgba(0, 0, 0, 0.09);\r\n$shadow-3 : 0 42px 44px -10px rgba(1, 23, 48, 0.12);\r\n$shadow-4 : 0 32px 64px rgba(22, 28, 45, 0.08);\r\n$shadow-5 : 0 62px 64px -10px rgba(1, 23, 48, 0.12);\r\n$shadow-6 : 0 32px 54px rgba(22, 28, 45, 0.16);\r\n$shadow-7 : 0 54px 53px -23px rgba(22, 28, 45, 0.14);\r\n$shadow-8 : 0 52px 74px rgba(0, 0, 0, 0.11);\r\n$shadow-9 : 0 22px 45px rgba(91, 9, 0, 0.2);\r\n$shadow-10 : 0 22px 45px rgba(0, 0, 0, 0.09);\r\n$shadow-blue : 0 14px 64px rgba(71, 59, 240, 0.4);\r\n$shadow-red :  0 14px 64px rgba(246, 75, 75, 0.4);\r\n$shadow-green : 0 14px 64px rgba(104, 213, 133, 0.4);\r\n\r\n\r\n$gr-shadows: (\r\n  1 : $shadow-1,\r\n  2 : $shadow-2,\r\n  3 : $shadow-3,\r\n  4 : $shadow-4,\r\n  5 : $shadow-5,\r\n  6 : $shadow-6,\r\n  7 : $shadow-7,\r\n  8 : $shadow-8,\r\n  9 : $shadow-9,\r\n  10 : $shadow-10,\r\n  \"blue\" : $shadow-blue,\r\n  \"red\" : $shadow-red,\r\n  \"green\" : $shadow-green\r\n);", "/*~~~~~ Circle Sizes ~~~~~*/\r\n@each $size, $length in $circle-size {\r\n  .circle-#{$size}{\r\n    max-width: #{$length};\r\n    min-width: #{$length};\r\n    max-height: #{$length};\r\n    min-height: #{$length};\r\n    border-radius: 500px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    @if $size == \"xs\" {\r\n      font-size: 16px;\r\n    } @else if $size == \"md\"{\r\n      font-size: 18px;\r\n    } @else if $size == \"lg\"{\r\n      font-size: 28px;\r\n    }\r\n  }\r\n}\r\n\r\n/*~~~~~ Square Sizes ~~~~~*/\r\n@each $size in $gr-square-size {\r\n  .square-#{$size}{\r\n    max-width: #{$size}px;\r\n    min-width: #{$size}px;\r\n    max-height: #{$size}px;\r\n    min-height: #{$size}px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n  }\r\n}\r\n\r\n\r\n@if($responsive-size){\r\n  /*~~~~~ responsive width ~~~~~*/\r\n  @each $breakpoint in map-keys($grid-breakpoints) {\r\n    @include media-breakpoint-up($breakpoint) {\r\n      $infix: breakpoint-infix($breakpoint, $grid-breakpoints);\r\n      @if($infix != ''){\r\n        @each $prop, $abbrev in (width: w) {\r\n          @each $size, $length in $sizes {\r\n            .#{$abbrev}#{$infix}-#{$size} { #{$prop}: $length !important; }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}", "// Breakpoint viewport sizes and media queries.\n//\n// Breakpoints are defined as a map of (name: minimum width), order from small to large:\n//\n//    (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px)\n//\n// The map defined in the `$grid-breakpoints` global variable is used as the `$breakpoints` argument by default.\n\n// Name of the next breakpoint, or null for the last breakpoint.\n//\n//    >> breakpoint-next(sm)\n//    md\n//    >> breakpoint-next(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    md\n//    >> breakpoint-next(sm, $breakpoint-names: (xs sm md lg xl))\n//    md\n@function breakpoint-next($name, $breakpoints: $grid-breakpoints, $breakpoint-names: map-keys($breakpoints)) {\n  $n: index($breakpoint-names, $name);\n  @return if($n != null and $n < length($breakpoint-names), nth($breakpoint-names, $n + 1), null);\n}\n\n// Minimum breakpoint width. Null for the smallest (first) breakpoint.\n//\n//    >> breakpoint-min(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    576px\n@function breakpoint-min($name, $breakpoints: $grid-breakpoints) {\n  $min: map-get($breakpoints, $name);\n  @return if($min != 0, $min, null);\n}\n\n// Maximum breakpoint width. Null for the largest (last) breakpoint.\n// The maximum value is calculated as the minimum of the next one less 0.02px\n// to work around the limitations of `min-` and `max-` prefixes and viewports with fractional widths.\n// See https://www.w3.org/TR/mediaqueries-4/#mq-min-max\n// Uses 0.02px rather than 0.01px to work around a current rounding bug in Safari.\n// See https://bugs.webkit.org/show_bug.cgi?id=178261\n//\n//    >> breakpoint-max(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    767.98px\n@function breakpoint-max($name, $breakpoints: $grid-breakpoints) {\n  $next: breakpoint-next($name, $breakpoints);\n  @return if($next, breakpoint-min($next, $breakpoints) - .02, null);\n}\n\n// Returns a blank string if smallest breakpoint, otherwise returns the name with a dash in front.\n// Useful for making responsive utilities.\n//\n//    >> breakpoint-infix(xs, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    \"\"  (Returns a blank string)\n//    >> breakpoint-infix(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    \"-sm\"\n@function breakpoint-infix($name, $breakpoints: $grid-breakpoints) {\n  @return if(breakpoint-min($name, $breakpoints) == null, \"\", \"-#{$name}\");\n}\n\n// Media of at least the minimum breakpoint width. No query for the smallest breakpoint.\n// Makes the @content apply to the given breakpoint and wider.\n@mixin media-breakpoint-up($name, $breakpoints: $grid-breakpoints) {\n  $min: breakpoint-min($name, $breakpoints);\n  @if $min {\n    @media (min-width: $min) {\n      @content;\n    }\n  } @else {\n    @content;\n  }\n}\n\n// Media of at most the maximum breakpoint width. No query for the largest breakpoint.\n// Makes the @content apply to the given breakpoint and narrower.\n@mixin media-breakpoint-down($name, $breakpoints: $grid-breakpoints) {\n  $max: breakpoint-max($name, $breakpoints);\n  @if $max {\n    @media (max-width: $max) {\n      @content;\n    }\n  } @else {\n    @content;\n  }\n}\n\n// Media that spans multiple breakpoint widths.\n// Makes the @content apply between the min and max breakpoints\n@mixin media-breakpoint-between($lower, $upper, $breakpoints: $grid-breakpoints) {\n  $min: breakpoint-min($lower, $breakpoints);\n  $max: breakpoint-max($upper, $breakpoints);\n\n  @if $min != null and $max != null {\n    @media (min-width: $min) and (max-width: $max) {\n      @content;\n    }\n  } @else if $max == null {\n    @include media-breakpoint-up($lower, $breakpoints) {\n      @content;\n    }\n  } @else if $min == null {\n    @include media-breakpoint-down($upper, $breakpoints) {\n      @content;\n    }\n  }\n}\n\n// Media between the breakpoint's minimum and maximum widths.\n// No minimum for the smallest breakpoint, and no maximum for the largest one.\n// Makes the @content apply only to the given breakpoint, not viewports any wider or narrower.\n@mixin media-breakpoint-only($name, $breakpoints: $grid-breakpoints) {\n  $min: breakpoint-min($name, $breakpoints);\n  $max: breakpoint-max($name, $breakpoints);\n\n  @if $min != null and $max != null {\n    @media (min-width: $min) and (max-width: $max) {\n      @content;\n    }\n  } @else if $max == null {\n    @include media-breakpoint-up($name, $breakpoints) {\n      @content;\n    }\n  } @else if $min == null {\n    @include media-breakpoint-down($name, $breakpoints) {\n      @content;\n    }\n  }\n}\n", "@each $name,$value in $gr-border-radius{\r\n  .rounded-#{$name}{\r\n    border-radius: $value;\r\n  }\r\n  .rounded-top-#{$name}{\r\n    border-top-left-radius: $value;\r\n    border-top-right-radius: $value;\r\n  }\r\n  .rounded-bottom-#{$name}{\r\n    border-bottom-left-radius: $value;\r\n    border-bottom-right-radius: $value;\r\n  }\r\n  .rounded-left-#{$name}{\r\n    border-top-left-radius: $value;\r\n    border-bottom-left-radius: $value;\r\n  }\r\n  .rounded-right-#{$name}{\r\n    border-top-right-radius: $value;\r\n    border-bottom-right-radius: $value;\r\n  }\r\n  .border-top-#{$name}{\r\n    border-top-width: $value;\r\n    border-top-style: solid;\r\n  }\r\n\r\n} \r\n", "// Allows for customizing button radius independently from global border radius\r\n// $border-radius-sm:            0.3125rem;    //5px (bootstrap provided variable)\r\n// $border-radius:               0.625rem;   //10px (bootstrap provided variable)\r\n// $border-radius-lg:            0.625rem;  //10px (bootstrap provided variable)\r\n$border-radius-xs:            3px;           //custom\r\n$border-radius-md:            0.5rem;      //8px custom\r\n$border-radius-xl:            1.25rem;  //20px  custom\r\n\r\n\r\n\r\n\r\n//--------------------------------------------------------------------------------------------------------------\r\n//--> use this format in the array to generate custom border radius class. EG: gr-rounded-{your-key}\r\n//-------------------------------------------------------------------------------------------------------------\r\n// 1 : 5px,\r\n// 2 : 8px,\r\n// 3 : 10px,\r\n// 4 : 15px,\r\n$gr-border-radius:(\r\n  xs : $border-radius-xs,\r\n  md : $border-radius-md,\r\n  xl: $border-radius-xl,\r\n  3: 3px,\r\n  5: 5px,\r\n  8: 8px,\r\n  10: 10px,\r\n  12: 12px,\r\n  15: 15px,\r\n  20: 20px,\r\n  25: 25px\r\n);\r\n\r\n\r\n", ".gr-md-rounded{\r\n  @include tablet{\r\n    border-radius: $border-radius;\r\n  }\r\n}\r\n\r\n\r\n.border-lg-left{\r\n  @include desktops{\r\n    border-left: 1px solid var(--border-color);\r\n  }\r\n}\r\n\r\n.border-sm-divider {\r\n  width: 161px;\r\n  height: 1px;\r\n  border: 3px solid $blackish-blue;\r\n  opacity: 0.1;\r\n  margin: 0 auto;\r\n  &.dark {\r\n    border: 3px solid #ffffff;\r\n  }\r\n}\r\n\r\n.form-control {\r\n  border-color: $gray-210;\r\n}\r\n\r\n.border-3{\r\n  border-width: 3px;\r\n  border-style: solid;\r\n}\r\n.border-color-2 {\r\n  border: 1px solid var(--border-color-2) !important;\r\n}", ".bg-gradient {\r\n  position: relative;\r\n  z-index: 1;\r\n  &-black {\r\n    &:before {\r\n      background-image: linear-gradient(270deg, rgba(0, 0, 0, 0) 0%, $blackish-blue 100%);\r\n      position: absolute;\r\n      content: \"\";\r\n      width: 100%;\r\n      height: 100%;\r\n      top: 0;\r\n      left: 0;\r\n      z-index: -1;\r\n    }\r\n  }\r\n  &-white {\r\n    &:before {\r\n      background-image: linear-gradient(270deg, rgba(0, 0, 0, 0) 0%, #fbfbfb 100%);\r\n      position: absolute;\r\n      content: \"\";\r\n      width: 100%;\r\n      height: 100%;\r\n      top: 0;\r\n      left: 0;\r\n      z-index: -1;\r\n    }\r\n  }\r\n  &-1 {\r\n    position: relative;\r\n    background: linear-gradient(225deg, #313c59 0%, $blackish-blue 100%);\r\n  }\r\n  &-2{\r\n    background-image: linear-gradient(to right, #F8F8F8 0%, #EBEBEB 100%);\r\n  }\r\n  &-3{\r\n    background-image: linear-gradient(to bottom, var(--bg-2) 62%, var(--bg-4) 62%, var(--bg-4) 100%);\r\n  }\r\n}\r\n\r\n\r\n.bg-overlay {\r\n  position: relative;\r\n  z-index: 1;\r\n  &:before {\r\n    position: absolute;\r\n    content: \"\";\r\n    width: 100%;\r\n    height: 100%;\r\n    top: 0;\r\n    left: 0;\r\n    z-index: -1;\r\n  }\r\n  &.overlay-1 {\r\n    &:before {\r\n      background: $blackish-blue;\r\n      opacity: 0.51;\r\n    }\r\n  }\r\n  &.overlay-2 {\r\n    &:before {\r\n      background: $blackish-blue;\r\n      opacity: 0.8;\r\n    }\r\n  }\r\n}", "a{\r\n  transition: .4s;\r\n  &:hover {\r\n    color: $primary;\r\n  }\r\n}\r\n\r\n.gr-hover-shadow-1 {\r\n  transition: .3s;\r\n  &:hover {\r\n    box-shadow: 0 34px 33px -23px rgba(22, 28, 45, 0.13);\r\n\r\n  }\r\n}\r\n.gr-hover-shadow-2 {\r\n  transition: .3s;\r\n  &:hover {\r\n    box-shadow: 0 31px 34px -20px rgba(0, 0, 0, 0.09);\r\n\r\n  }\r\n}\r\n.gr-hover-shadow-3 {\r\n  transition: .3s;\r\n  &:hover {\r\n    box-shadow: 0 42px 44px -10px rgba(1, 23, 48, 0.12);\r\n\r\n  }\r\n}\r\n.gr-hover-shadow-4 {\r\n  transition: .3s;\r\n  &:hover {\r\n    box-shadow: 0 32px 54px rgba(22, 28, 45, 0.16);\r\n  }\r\n}\r\n.gr-hover-shadow-5 {\r\n  transition: .3s;\r\n  &:hover {\r\n    box-shadow: 0 30px 44px rgba(13, 21, 46, 0.09)\r\n  }\r\n}\r\n.gr-hover-shadow-6 {\r\n  transition: .3s;\r\n  position: relative;\r\n  z-index: 1;\r\n  &:hover {\r\n    z-index: 3;\r\n    box-shadow: 0 62px 64px -10px rgba(1, 23, 48, 0.12);\r\n  }\r\n}\r\n\r\n.gr-hover-rotate-img {\r\n  img {\r\n    will-change: transform;\r\n    transition: 0.8s cubic-bezier(0.39, 0.575, 0.565, 1);\r\n    transform: rotate(0deg) scale(1);\r\n    overflow: hidden;\r\n  }\r\n  &:hover {\r\n    @include tablet{\r\n      img {\r\n        transform: rotate(-3deg) scale(1.2);\r\n        opacity: .6;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.gr-hover-opacity-full {\r\n  transition: .4s;\r\n  &:hover {\r\n    @include tablet{\r\n      opacity: 1;\r\n    }\r\n  }\r\n}\r\n\r\n.gr-hover-y {\r\n  transition: .4s;\r\n  &:hover {\r\n    @include tablet{\r\n      transform: translateY(-8px);\r\n    }\r\n  }\r\n}\r\n.gr-abs-hover-y {\r\n  transition: .4s;\r\n  &:hover {\r\n    @include tablet{\r\n      transform: translate(-50%, -65%);\r\n    }\r\n  }\r\n}\r\n\r\n.gr-hover-text-green {\r\n  transition: .4s;\r\n  &:hover {\r\n    color: $green!important;\r\n    i {\r\n      color: $green!important;\r\n\r\n    }\r\n  }\r\n}\r\n\r\n.gr-hover-text-red {\r\n  transition: .4s;\r\n  &:hover {\r\n    color: $red!important;\r\n    i {\r\n      color: $red!important;\r\n    }\r\n  }\r\n}\r\n\r\n.hover-underline {\r\n  transition: .4s;\r\n  &:hover {\r\n    text-decoration: underline;\r\n  }\r\n}\r\n\r\n\r\n.bg-white, a.bg-white {\r\n  &:hover {\r\n    background-color: #fff!important;\r\n  }\r\n}\r\n.text-white, a.text-white {\r\n  &:hover {\r\n    color: #fff!important;\r\n  }\r\n}\r\n\r\n.gr-hover-text-green, a.gr-hover-text-green {\r\n  transition: .4s;\r\n  &:hover {\r\n    color: $green!important;\r\n    i {\r\n      color: $green!important;\r\n    }\r\n  }\r\n}\r\n\r\n.gr-hover-text-blue, a.gr-hover-text-blue {\r\n  transition: .4s;\r\n  &:hover {\r\n    color: $blue!important;\r\n    i {\r\n      color: $blue!important;\r\n    }\r\n  }\r\n}\r\n\r\n\r\n\r\n.gr-product-hover-1{\r\n  .hover-animation-item{\r\n    position: absolute;\r\n    bottom: 20px;\r\n    width: 100%;\r\n    left: 0;\r\n    text-align: center;\r\n    z-index: 999;\r\n    opacity: 0;\r\n    transition: .6s;\r\n  }\r\n  &:hover{\r\n    .hover-animation-item{\r\n      bottom: 35px;\r\n      opacity: 1;\r\n    }\r\n  }\r\n}\r\n\r\n.gr-hover-scale-img {\r\n  img {\r\n    transition: .3s;\r\n    transform: scale(1);\r\n    box-shadow: 0 32px 54px rgba(22, 28, 45, 0);\r\n    will-change: transform;\r\n  }\r\n  &:hover {\r\n    img {\r\n      transform: scale(0.9);\r\n      box-shadow: 0 32px 54px rgba(22, 28, 45, 0.16);\r\n    }\r\n  }\r\n}\r\n\r\n// .content-img-animate-2 {\r\n//   animation-name: animate-round;\r\n//   animation-duration: 18s;\r\n//   animation-delay: 2s;\r\n//   animation-timing-function: ease-in;\r\n//   animation-iteration-count: infinite;\r\n// }\r\n\r\n@keyframes animate-rotate {\r\n  0% {transform: rotate(-2deg);}\r\n  20% {transform: rotate(2deg);}\r\n  40% {transform: rotate(2deg);}\r\n  80% {transform: rotate(-2deg);}\r\n  100% {transform: rotate(-2deg);}\r\n}\r\n.img-animate-1 {\r\n  animation-name: animate-rotate;\r\n  animation-duration: 5s;\r\n  animation-delay: 2s;\r\n  animation-timing-function: linear;\r\n  animation-iteration-count: infinite;\r\n}", ".bg-pattern {\r\n  position: relative;\r\n  z-index: 1;\r\n  &:before {\r\n    position: absolute;\r\n    content: \"\";\r\n    width: 100%;\r\n    height: 100%;\r\n    top: 0;\r\n    left: 0;\r\n    z-index: -1;\r\n  }\r\n  &.pattern-1 {\r\n    &:before {\r\n      background: url(../image/patterns/pattern-1.png) no-repeat center;\r\n      background-size: cover;\r\n      opacity: 0.14;\r\n\r\n    }\r\n  }\r\n  &.pattern-2 {\r\n    &:before {\r\n      // content: url(../image/patterns/pattern-2.png);\r\n      background: url(../image/patterns/pattern-2.png) no-repeat center;\r\n      background-size: cover;\r\n    }\r\n  }\r\n  &.pattern-3 {\r\n    &:before {\r\n      background: url(../image/patterns/pattern-3.png) no-repeat 50% 100%;\r\n      background-size: cover;\r\n    }\r\n  }\r\n  &.pattern-4 {\r\n    &:before {\r\n      background: url(../image/patterns/pattern-4.png) no-repeat 50% 100%;\r\n      background-size: cover;\r\n    }\r\n  }\r\n  &.pattern-5 {\r\n    &:before {\r\n      background: url(../image/patterns/pattern-5.png) no-repeat left bottom;\r\n      background-size: contain;\r\n    }\r\n  }\r\n  &.pattern-6 {\r\n    &:before {\r\n      background: url(../image/patterns/pattern-6.png) no-repeat top right;\r\n      background-size: auto;\r\n    }\r\n  }\r\n  &.pattern-7 {\r\n    &:before {\r\n      background: url(../image/patterns/pattern-7.png) no-repeat top right;\r\n      background-size: auto;\r\n    }\r\n  }\r\n\r\n}", ".gr-abs-md{\r\n  @include tablet{\r\n    position: absolute;\r\n  }\r\n}\r\n\r\n.gr-abs-lg {\r\n  @include desktops {\r\n    position: absolute!important;\r\n  }\r\n}\r\n\r\n.gr-abs-lg-ly-center{\r\n  @include desktops {\r\n    position: absolute;\r\n    top: 50%;\r\n    left: 0;\r\n    transform: translateY(-50%);\r\n  }\r\n}\r\n\r\n.gr-abs-lg-ry-center{\r\n  @include desktops {\r\n    position: absolute;\r\n    top: 50%;\r\n    right: 0;\r\n    transform: translateY(-50%);\r\n  }\r\n}\r\n.gr-abs-bx-center{\r\n  @include desktops {\r\n    position: absolute;\r\n    bottom: 55px;\r\n    right: 50%;\r\n    transform: translateX(50%);\r\n  }\r\n}\r\n\r\n.gr-abs-tl {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n}\r\n.gr-abs-tr {\r\n  position: absolute;\r\n  top: 0;\r\n  right: 0;\r\n}\r\n\r\n.gr-abs-bl {\r\n  position: absolute;\r\n  bottom: 0;\r\n  left: 0;\r\n}\r\n.gr-abs-br {\r\n  position: absolute;\r\n  bottom: 0;\r\n  right: 0;\r\n}\r\n.gr-abs-cr {\r\n  position: absolute;\r\n  top: 50%;\r\n  right: 0;\r\n  transform: translateY(-50%);\r\n}\r\n.gr-abs-md-cr {\r\n  @include tablet {\r\n    position: absolute;\r\n    top: 50%;\r\n    right: 0;\r\n    transform: translateY(-50%);\r\n  }\r\n}\r\n.gr-abs-center{\r\n  position: absolute;\r\n  top: 50%;\r\n  left: 50%;\r\n  transform: translate(-50%,-50%);\r\n}\r\n.gr-abs-br-custom {\r\n  position: absolute;\r\n  bottom: -50px;\r\n  right: -36px;\r\n}\r\n.gr-abs-br-custom-2 {\r\n  position: absolute;\r\n  bottom: 12%;\r\n  right: 21%;\r\n}\r\n\r\n\r\n.gr-abs-bl-custom {\r\n  position: absolute;\r\n  bottom: -50px;\r\n  left: -36px;\r\n  z-index: -1;\r\n}\r\n\r\n.gr-abs-bl-custom-2 {\r\n  position: absolute;\r\n  bottom: -5px;\r\n  left: -70px;\r\n  z-index: -1;\r\n}\r\n.gr-abs-bl-custom-3 {\r\n  position: absolute;\r\n  bottom: 0;\r\n  left: -16%;\r\n}\r\n\r\n.gr-abs-bl-custom-4 {\r\n  position: absolute;\r\n  bottom: -40px;\r\n  left: -56px;\r\n  @include tablet {\r\n    bottom: -40px;\r\n    left: 16px;\r\n  }\r\n  @include desktops {\r\n    bottom: -40px;\r\n    left: -5px;\r\n  }\r\n}\r\n\r\n.gr-abs-bl-custom-5 {\r\n  position: absolute;\r\n  bottom: 38px;\r\n  left: -12px;\r\n  @include tablet {\r\n    bottom: 38px;\r\n    left: -12px;\r\n  }\r\n  @include desktops {\r\n    bottom: 38px;\r\n    left: -12px;\r\n  }\r\n}\r\n\r\n.gr-abs-tl-custom {\r\n  position: absolute;\r\n  top: -6px;\r\n  left: 45px;\r\n}\r\n\r\n.gr-abs-tl-custom-2 {\r\n  position: absolute;\r\n  top: 40px;\r\n  left: 52px;\r\n  z-index: -1;\r\n  @include desktops {\r\n    top: 24px;\r\n    left: 35px;\r\n  }\r\n  @include large-desktops {\r\n    top: 40px;\r\n    left: 52px;\r\n  }\r\n}\r\n\r\n.gr-abs-tl-custom-3 {\r\n  position: absolute;\r\n  top: 80px;\r\n  left: -30px;\r\n  @include mobile {\r\n    top: 80px;\r\n    left: -50px;\r\n  }\r\n  @include large-desktops {\r\n    top: 80px;\r\n    left: -65px;\r\n  }\r\n}\r\n.gr-abs-tl-custom-4 {\r\n  position: absolute;\r\n  top: 50px;\r\n  left: 43.5%;\r\n  @include desktops {\r\n    top: 50px;\r\n    left: 43.5%;\r\n  }\r\n  @include large-desktops {\r\n    top: 50px;\r\n    left: 43.5%;\r\n  }\r\n}\r\n\r\n.hero-sm-card-1 {\r\n  position: absolute;\r\n  // top: 92px;\r\n  // left: -34px;\r\n  top: 32px;\r\n  left: -70px;\r\n  transform: scale(.8);\r\n  @include mobile {\r\n    transform: scale(.9);\r\n  }\r\n  @include tablet {\r\n    left: -96px;\r\n    transform: scale(1);\r\n\r\n  }\r\n  @include desktops {\r\n    top: -12px;\r\n    left: -94px;\r\n  }\r\n  @include large-desktops {\r\n    top: 50px;\r\n    left: -96px;\r\n  }\r\n}\r\n\r\n.hero-sm-card-2 {\r\n  position: absolute;\r\n  bottom: 16px;\r\n  right: -60px;\r\n  transform: scale(.8);\r\n\r\n  @include mobile {\r\n    bottom: 60px;\r\n  }\r\n  @include tablet {\r\n    bottom: 95px;\r\n    transform: scale(1);\r\n  }\r\n  @include desktops {\r\n    bottom: 60px;\r\n    right: -16px;\r\n  }\r\n  @include large-desktops {\r\n    bottom: 60px;\r\n    right: -25px;\r\n  }\r\n  @include large-desktops-mid {\r\n    bottom: 60px;\r\n    right: -60px;\r\n  }\r\n}\r\n\r\n\r\n.gr-abs-tr-custom {\r\n  position: absolute;\r\n  top: -32px;\r\n  right: -38px;\r\n}\r\n\r\n.gr-abs-tr-custom-2 {\r\n  position: absolute;\r\n  top: 26%;\r\n  right: 15%;\r\n  z-index: -1;\r\n}\r\n\r\n\r\n\r\n.gr-abs-shape-custom-1 {\r\n  position: absolute;\r\n  top: 50%;\r\n  left: 50%;\r\n  transform: translateX(-20%) translateY(-50%);\r\n}\r\n.gr-abs-shape-custom-2 {\r\n  position: absolute;\r\n  top: -11%;\r\n  right: 30px;\r\n  height: 100%;\r\n  img {\r\n    height: 100%;\r\n  }\r\n  @include mobile {\r\n    top: 0%;\r\n    right: 18%;\r\n    height: auto;\r\n    img {\r\n      transform: scale(1.2);\r\n    }\r\n  }\r\n  @include mobile-lg {\r\n    top: -7%;\r\n    right: -6%;\r\n    img {\r\n      transform: scale(1);\r\n    }\r\n  }\r\n  @include tablet {\r\n    top: -4%;\r\n    right: -4%;\r\n    img {\r\n      transform: scale(1.1);\r\n    }\r\n  }\r\n  @include desktops {\r\n    top: -8%;\r\n    right: -6%;\r\n    img {\r\n      transform: scale(1);\r\n    }\r\n  }\r\n  @include large-desktops {\r\n    top: -8%;\r\n    right: -7%;\r\n  }\r\n}\r\n\r\n.gr-z-index-n1 {\r\n  z-index: -1;\r\n}\r\n.gr-z-index-1 {\r\n  z-index: 1;\r\n}\r\n.z-index-99{\r\n  z-index: 99;\r\n}\r\n.z-index-super{\r\n  z-index: 9999;\r\n}\r\n\r\n.gr-abs-img-custom-2 {\r\n  position: absolute;\r\n  top: 30%;\r\n  right: 0;\r\n}\r\n\r\n\r\n.gr-custom-tl-1 {\r\n  position: relative;\r\n  top: -45px;\r\n  left: 0;\r\n}\r\n.gr-custom-br-1 {\r\n  position: relative;\r\n  bottom: -86px;\r\n  right: 0;\r\n}", "\r\n.gr-box-shadow-1{\r\n  box-shadow: 0 34px 33px -23px rgba(22, 28, 45, 0.13);\r\n}\r\n.gr-box-shadow-2{\r\n  box-shadow: 0 31px 34px -20px rgba(0, 0, 0, 0.09);\r\n\r\n}\r\n.gr-box-shadow-3{\r\n  box-shadow: 0 42px 44px -10px rgba(1, 23, 48, 0.12);\r\n}\r\n.gr-box-shadow-4{\r\n  box-shadow: 0 32px 64px rgba(22, 28, 45, 0.08);\r\n}\r\n.gr-box-shadow-5{\r\n  box-shadow: 0 62px 64px -10px rgba(1, 23, 48, 0.12);\r\n}\r\n.gr-image-box-shadow{\r\n  box-shadow: 0 42px 44px -10px rgba(1, 23, 48, 0.12);\r\n}\r\n\r\n.gr-card-box-shadow{\r\n  box-shadow: 0 42px 44px -10px rgba(1, 23, 48, 0.12);\r\n}\r\n\r\n\r\n\r\n", ".w-fit-content{\r\n  width: fit-content;\r\n}\r\n.lg{\r\n  &\\:min-h-vh-100{\r\n    @include desktops{\r\n      min-height: 100vh;\r\n    }\r\n  }\r\n}\r\n.min-h-vh-100{\r\n  min-height: 100vh;\r\n \r\n}\r\n.h-xs-100{\r\n  @include mobile{\r\n    height: 100%;\r\n  }\r\n}\r\n.h-sm-100{\r\n  @include mobile-lg{\r\n    height: 100%;\r\n  }\r\n}\r\n.h-md-100{\r\n  @include mobile-lg{\r\n    height: 100%!important; \r\n  }\r\n}\r\n.h-lg-100{\r\n  @include desktops{\r\n    height: 100%;\r\n  }\r\n}\r\n.h-px-50{\r\n  min-height: 50px;\r\n}\r\n.gr-min-width-219{\r\n  min-width: 219px;\r\n}\r\n\r\n.gr-textarea-height{\r\n  min-height: 175px;\r\n}\r\n.fluid-map-height{\r\n  min-height: 300px;\r\n  width: 100%;\r\n  @include mobile-lg{\r\n    min-height: 400px;\r\n  }\r\n  @include tablet{\r\n    min-height: 541px;\r\n  }\r\n}", "// .h--default {\r\n//   margin-bottom: $headings-margin-bottom;\r\n//   font-family: $headings-font-family;\r\n//   font-weight: $headings-font-weight;\r\n//   // line-height: $headings-line-height;\r\n//   color: $headings-color;\r\n// }\r\n\r\n\r\n// .h--default { @include font-size($h7-font-size); }\r\n.line-height-reset{\r\n  line-height: 1;\r\n}\r\np{\r\n  @include font-size($text-base);\r\n  color: $body-color;\r\n}\r\n.gr-text{\r\n  // @include font-size($font-size-base);\r\n  &-1 {\r\n    @include font-size($display3-size);//Approx 80 px\r\n    letter-spacing: -2.5px;\r\n    line-height: 1.05;  //84\r\n    &.gr-lh-reset {\r\n      @include line-height-reset(1.15);\r\n    }\r\n  }\r\n  &-2 {\r\n    @include font-size($display4-size);//Approx 60 px\r\n    letter-spacing: -2px;\r\n    line-height: 1.083;  //65\r\n    &.gr-lh-reset {\r\n      @include line-height-reset(1.183);\r\n    }\r\n  }\r\n  &-3 {\r\n    @include font-size($h1-font-size);//Approx 48 px\r\n    letter-spacing: -1.8px;\r\n    line-height: 1.21;  //58\r\n    &.gr-lh-reset {\r\n      @include line-height-reset(1.31);\r\n    }\r\n  }\r\n  &-4 {\r\n    @include font-size($h2-font-size);//Approx 36 px\r\n    letter-spacing: -1.2px;\r\n    line-height: 1.3;  //48\r\n    &.gr-lh-reset {\r\n      @include line-height-reset(1.4);\r\n    }\r\n  }\r\n  &-5 {\r\n    @include font-size($h3-font-size);//Approx 32 px\r\n    letter-spacing: -1.2px;\r\n    line-height: 1.375;  //44\r\n    &.gr-lh-reset {\r\n      @include line-height-reset(1.475);\r\n    }\r\n  }\r\n  &-6 {\r\n    @include font-size($h4-font-size);//Approx 24 px\r\n    letter-spacing: -0.5px;\r\n    line-height: 1.42;  //34\r\n    &.gr-lh-reset {\r\n      @include line-height-reset(1.52);\r\n    }\r\n  }\r\n  &-7 {\r\n    @include font-size($h5-font-size);//Approx 21 px\r\n    letter-spacing: -0.5px;\r\n    line-height: 1.5;  //32\r\n    &.gr-lh-reset {\r\n      @include line-height-reset(1.6);\r\n    }\r\n  }\r\n  &-8{\r\n    @include font-size($p2-font-size);//Approx 19 px\r\n    letter-spacing: -0.2px;\r\n    line-height: 1.69;\r\n  }\r\n  &-9{\r\n    @include font-size($p3-font-size);//Approx 17 px\r\n    letter-spacing: -0.2px;\r\n    line-height: 1.71;\r\n  }\r\n  &-10{\r\n    @include font-size($p4-font-size); //for browser default assuming 16px\r\n  }\r\n  &-11{\r\n    @include font-size($p5-font-size);//Approx 15 px\r\n    letter-spacing: -0.1px;\r\n    line-height: 1.73;\r\n  }\r\n  &-12 {\r\n    @include font-size($h8-font-size);//Approx 13 px\r\n    line-height: 1.63;  \r\n    &.gr-lh-reset {\r\n      @include line-height-reset(1.73);\r\n    }\r\n  }\r\n  &-13 {\r\n    font-size: $font-size-base * 0.625; //Approx 10 px\r\n    line-height: 1.63;  \r\n  }\r\n  &-14 {\r\n    font-size: $font-size-base * 0.5; //Approx 8 px\r\n    line-height: 1.63;\r\n  }\r\n}\r\n\r\n.gr-text-underline {\r\n  text-decoration: underline;\r\n}\r\n\r\n\r\n.text-linethrough{\r\n  text-decoration: line-through;\r\n}", "// stylelint-disable property-blacklist, scss/dollar-variable-default\n\n// SCSS RFS mixin\n//\n// Automated font-resizing\n//\n// See https://github.com/twbs/rfs\n\n// Configuration\n\n// Base font size\n$rfs-base-font-size: 1.25rem !default;\n$rfs-font-size-unit: rem !default;\n\n// Breakpoint at where font-size starts decreasing if screen width is smaller\n$rfs-breakpoint: 1200px !default;\n$rfs-breakpoint-unit: px !default;\n\n// Resize font-size based on screen height and width\n$rfs-two-dimensional: false !default;\n\n// Factor of decrease\n$rfs-factor: 10 !default;\n\n@if type-of($rfs-factor) != \"number\" or $rfs-factor <= 1 {\n  @error \"`#{$rfs-factor}` is not a valid  $rfs-factor, it must be greater than 1.\";\n}\n\n// Generate enable or disable classes. Possibilities: false, \"enable\" or \"disable\"\n$rfs-class: false !default;\n\n// 1 rem = $rfs-rem-value px\n$rfs-rem-value: 16 !default;\n\n// Safari iframe resize bug: https://github.com/twbs/rfs/issues/14\n$rfs-safari-iframe-resize-bug-fix: false !default;\n\n// Disable RFS by setting $enable-responsive-font-sizes to false\n$enable-responsive-font-sizes: true !default;\n\n// Cache $rfs-base-font-size unit\n$rfs-base-font-size-unit: unit($rfs-base-font-size);\n\n// Remove px-unit from $rfs-base-font-size for calculations\n@if $rfs-base-font-size-unit == \"px\" {\n  $rfs-base-font-size: $rfs-base-font-size / ($rfs-base-font-size * 0 + 1);\n}\n@else if $rfs-base-font-size-unit == \"rem\" {\n  $rfs-base-font-size: $rfs-base-font-size / ($rfs-base-font-size * 0 + 1 / $rfs-rem-value);\n}\n\n// Cache $rfs-breakpoint unit to prevent multiple calls\n$rfs-breakpoint-unit-cache: unit($rfs-breakpoint);\n\n// Remove unit from $rfs-breakpoint for calculations\n@if $rfs-breakpoint-unit-cache == \"px\" {\n  $rfs-breakpoint: $rfs-breakpoint / ($rfs-breakpoint * 0 + 1);\n}\n@else if $rfs-breakpoint-unit-cache == \"rem\" or $rfs-breakpoint-unit-cache == \"em\" {\n  $rfs-breakpoint: $rfs-breakpoint / ($rfs-breakpoint * 0 + 1 / $rfs-rem-value);\n}\n\n// Responsive font-size mixin\n@mixin rfs($fs, $important: false) {\n  // Cache $fs unit\n  $fs-unit: if(type-of($fs) == \"number\", unit($fs), false);\n\n  // Add !important suffix if needed\n  $rfs-suffix: if($important, \" !important\", \"\");\n\n  // If $fs isn't a number (like inherit) or $fs has a unit (not px or rem, like 1.5em) or $ is 0, just print the value\n  @if not $fs-unit or $fs-unit != \"\" and $fs-unit != \"px\" and $fs-unit != \"rem\" or $fs == 0 {\n    font-size: #{$fs}#{$rfs-suffix};\n  }\n  @else {\n    // Variables for storing static and fluid rescaling\n    $rfs-static: null;\n    $rfs-fluid: null;\n\n    // Remove px-unit from $fs for calculations\n    @if $fs-unit == \"px\" {\n      $fs: $fs / ($fs * 0 + 1);\n    }\n    @else if $fs-unit == \"rem\" {\n      $fs: $fs / ($fs * 0 + 1 / $rfs-rem-value);\n    }\n\n    // Set default font-size\n    @if $rfs-font-size-unit == rem {\n      $rfs-static: #{$fs / $rfs-rem-value}rem#{$rfs-suffix};\n    }\n    @else if $rfs-font-size-unit == px {\n      $rfs-static: #{$fs}px#{$rfs-suffix};\n    }\n    @else {\n      @error \"`#{$rfs-font-size-unit}` is not a valid unit for $rfs-font-size-unit. Use `px` or `rem`.\";\n    }\n\n    // Only add media query if font-size is bigger as the minimum font-size\n    // If $rfs-factor == 1, no rescaling will take place\n    @if $fs > $rfs-base-font-size and $enable-responsive-font-sizes {\n      $min-width: null;\n      $variable-unit: null;\n\n      // Calculate minimum font-size for given font-size\n      $fs-min: $rfs-base-font-size + ($fs - $rfs-base-font-size) / $rfs-factor;\n\n      // Calculate difference between given font-size and minimum font-size for given font-size\n      $fs-diff: $fs - $fs-min;\n\n      // Base font-size formatting\n      // No need to check if the unit is valid, because we did that before\n      $min-width: if($rfs-font-size-unit == rem, #{$fs-min / $rfs-rem-value}rem, #{$fs-min}px);\n\n      // If two-dimensional, use smallest of screen width and height\n      $variable-unit: if($rfs-two-dimensional, vmin, vw);\n\n      // Calculate the variable width between 0 and $rfs-breakpoint\n      $variable-width: #{$fs-diff * 100 / $rfs-breakpoint}#{$variable-unit};\n\n      // Set the calculated font-size.\n      $rfs-fluid: calc(#{$min-width} + #{$variable-width}) #{$rfs-suffix};\n    }\n\n    // Rendering\n    @if $rfs-fluid == null {\n      // Only render static font-size if no fluid font-size is available\n      font-size: $rfs-static;\n    }\n    @else {\n      $mq-value: null;\n\n      // RFS breakpoint formatting\n      @if $rfs-breakpoint-unit == em or $rfs-breakpoint-unit == rem {\n        $mq-value: #{$rfs-breakpoint / $rfs-rem-value}#{$rfs-breakpoint-unit};\n      }\n      @else if $rfs-breakpoint-unit == px {\n        $mq-value: #{$rfs-breakpoint}px;\n      }\n      @else {\n        @error \"`#{$rfs-breakpoint-unit}` is not a valid unit for $rfs-breakpoint-unit. Use `px`, `em` or `rem`.\";\n      }\n\n      @if $rfs-class == \"disable\" {\n        // Adding an extra class increases specificity,\n        // which prevents the media query to override the font size\n        &,\n        .disable-responsive-font-size &,\n        &.disable-responsive-font-size {\n          font-size: $rfs-static;\n        }\n      }\n      @else {\n        font-size: $rfs-static;\n      }\n\n      @if $rfs-two-dimensional {\n        @media (max-width: #{$mq-value}), (max-height: #{$mq-value}) {\n          @if $rfs-class == \"enable\" {\n            .enable-responsive-font-size &,\n            &.enable-responsive-font-size {\n              font-size: $rfs-fluid;\n            }\n          }\n          @else {\n            font-size: $rfs-fluid;\n          }\n\n          @if $rfs-safari-iframe-resize-bug-fix {\n            // stylelint-disable-next-line length-zero-no-unit\n            min-width: 0vw;\n          }\n        }\n      }\n      @else {\n        @media (max-width: #{$mq-value}) {\n          @if $rfs-class == \"enable\" {\n            .enable-responsive-font-size &,\n            &.enable-responsive-font-size {\n              font-size: $rfs-fluid;\n            }\n          }\n          @else {\n            font-size: $rfs-fluid;\n          }\n\n          @if $rfs-safari-iframe-resize-bug-fix {\n            // stylelint-disable-next-line length-zero-no-unit\n            min-width: 0vw;\n          }\n        }\n      }\n    }\n  }\n}\n\n// The font-size & responsive-font-size mixin uses RFS to rescale font sizes\n@mixin font-size($fs, $important: false) {\n  @include rfs($fs, $important);\n}\n\n@mixin responsive-font-size($fs, $important: false) {\n  @include rfs($fs, $important);\n}\n", "@import \"theme-color-control\";\r\n@import \"theme-color-maping\";\r\n@import \"theme-border-control\";\r\n@import \"theme-font-control\";\r\n@import \"theme-form-control\";\r\n@import \"theme-btn-control\";\r\n@import \"theme-grid-control\";\r\n@import \"theme-spacing-control\";\r\n@import \"theme-shadow-control\";\r\n@import \"theme-size-control\";\r\n\r\n\r\n\r\n// Body\r\n// Settings for the `<body>` element.\r\n\r\n$body-bg:                   var(--bg);\r\n$body-color:                var(--color-texts-opacity);\r\n$border-color:              var(--border-color);\r\n\r\n\r\n// Links\r\n// Style anchor elements.\r\n\r\n$link-color:                              $primary;\r\n$link-hover-color:                        darken($link-color, 15%);\r\n$link-decoration:                         none;\r\n$link-hover-decoration:                   none;\r\n\r\n\r\n\r\n", "@mixin line-height-reset($line-height) {\r\n  &::before {\r\n    content: '';\r\n    display: block;\r\n    height: 0;\r\n    width: 0;\r\n    margin-top: calc((1 - #{$line-height}) * 0.5em);\r\n  }\r\n  &::after {\r\n    content: '';\r\n    display: block;\r\n    height: 0;\r\n    width: 0;\r\n    margin-bottom: calc((1 - #{$line-height}) * 0.5em);\r\n  }\r\n}", ".hero-card-1-animation{\r\n\tanimation: float 3s ease-in-out infinite;\r\n}\r\n.hero-card-2-animation{\r\n\tanimation: float 3s ease-in-out 1s infinite;\r\n}\r\n\r\n@keyframes float {\r\n\t0% {\r\n\t\tbox-shadow: 0 22px 45px rgba(0, 0, 0, 0.09);\r\n\t\ttransform: translatey(0px);\r\n\t}\r\n\t50% {\r\n\t\tbox-shadow: 0 22px 45px rgba(0, 0, 0, 0.2);\r\n\t\ttransform: translatey(-15px);\r\n\t}\r\n\t100% {\r\n\t\tbox-shadow: 0 22px 45px rgba(0, 0, 0, 0.09);\r\n\t\ttransform: translatey(0px);\r\n\t}\r\n}\r\n@keyframes floatX {\r\n\t0% {\r\n\t\ttransform: translatex(-15px);\r\n\t}\r\n\t50% {\r\n\t\ttransform: translatex(0px);\r\n\t}\r\n\t100% {\r\n\t\ttransform: translatex(-15px);\r\n\t}\r\n}\r\n\r\n@keyframes rotate {\r\n\t0% {\r\n\t\tbox-shadow: 0 22px 45px rgba(0, 0, 0, 0.09);\r\n\t\ttransform: rotate3d(16,2,1,-1deg)\r\n\t}\r\n\t50% {\r\n\t\tbox-shadow: 0 22px 45px rgba(0, 0, 0, 0.2);\r\n\t\ttransform:rotate3d(16,2,1,-25deg)\r\n\t}\r\n\t100% {\r\n\t\tbox-shadow: 0 22px 45px rgba(0, 0, 0, 0.09);\r\n\t\ttransform: rotate3d(16,2,1,-1deg)\r\n\t}\r\n}\r\n.hover-tilt{\r\n  &:hover{\r\n    .animation-tilt{\r\n     animation-play-state: running;\r\n    }\r\n  }\r\n}\r\n.animation-tilt{\r\n  animation:  tilt 3s linear infinite;\r\n  will-change: transform; \r\n  animation-play-state: paused;\r\n}\r\n@keyframes tilt{\r\n  0% {\r\n    transform: perspective(300px) rotateX(-8.23deg) rotateY(-4.91deg) scale3d(1,1,1);\r\n\t}\r\n\t16% {\r\n    transform: perspective(300px) rotateX(-8.31degdeg) rotateY(-4.98deg) scale3d(1,1,1);\r\n  }\r\n  33%{\r\n    transform: perspective(300px) rotateX(-7.39deg) rotateY(7.39deg) scale3d(1,1,1);\r\n  }\r\n  49%{\r\n    transform: perspective(300px) rotateX(3.9deg) rotateY(5.27deg) scale3d(1,1,1);\r\n  }\r\n  82%{\r\n    transform: perspective(300px) rotateX(2.64deg) rotateY(-5.44deg) scale3d(1,1,1);\r\n  }\r\n\t100% {\r\n    transform: perspective(300px) rotateX(-8.23deg) rotateY(-4.91deg) scale3d(1,1,1);\r\n\t}\r\n}", "button{\r\n  &:focus{\r\n    outline: none;\r\n    box-shadow: none;\r\n  }\r\n}\r\n.btn-reset{\r\n  background: transparent;\r\n  border: none;\r\n}\r\n.bg-image {\r\n  background-repeat: no-repeat;\r\n  background-position: center;\r\n  background-size: cover;\r\n}\r\n\r\n.bg-image-right {\r\n  background-repeat: no-repeat;\r\n  background-position: right;\r\n  background-size: contain;\r\n}\r\n\r\n.bg-parallax-image{\r\n  background-attachment: fixed;\r\n  background-size: cover;\r\n  background-repeat: no-repeat;\r\n  background-position: center;\r\n}\r\n\r\n.hero-img-custom {\r\n  position: relative;\r\n  bottom: -2px;\r\n  left: 0;\r\n  @include mobile {\r\n    bottom: -33px;\r\n    left: 0;\r\n  }\r\n  @include mobile-lg {\r\n    bottom: -9px;\r\n    left: 0;\r\n  }\r\n  @include tablet {\r\n    position: absolute;\r\n    bottom: -47px;\r\n    left: 0;\r\n  }\r\n  @include desktops {\r\n    bottom: -104px;\r\n    left: -145px;\r\n  }\r\n  @include large-desktops {\r\n    bottom: -120px;\r\n    left: -145px;\r\n  }\r\n}\r\n\r\n.social-icons{\r\n  display: inline-flex;\r\n  align-items: center;\r\n  font-size: 18px;\r\n  li{\r\n    a{\r\n      // transition: .4s;\r\n      // margin: 0 5px;\r\n      margin-right: 20px;\r\n    }\r\n    @include tablet {\r\n      i {\r\n        transition: .4s;\r\n      }\r\n      &:hover{\r\n        i {\r\n          transform: translateY(-8px);\r\n          // color: $primary;\r\n        }\r\n      }\r\n    }\r\n    &:last-child {\r\n      a {\r\n        margin-right: 0;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.list-style-check{\r\n  list-style: none;\r\n  li{\r\n    i{\r\n      font-size: 16px;\r\n      color: $green;\r\n      margin-right: 14px;\r\n      margin-top: 7px;\r\n    }\r\n  }\r\n}\r\n\r\n.footer-list {\r\n  transition: .4s;\r\n}\r\n\r\n.nice-select {\r\n  height: 55px;\r\n  border-radius: 5px;\r\n  width: 100%;\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 0 20px;\r\n  .list {\r\n    width: 100%;\r\n  }\r\n}\r\n\r\n.gr-text-exerpt{\r\n  text-overflow: ellipsis; \r\n  overflow: hidden;\r\n  white-space: nowrap; \r\n}\r\n\r\n.focus-reset{\r\n  &:focus{\r\n    outline: none!important;\r\n    box-shadow: none!important;\r\n  }\r\n}\r\n\r\n.separator-line {\r\n  width: 1px;\r\n  height: 169px;\r\n  border: 1px solid var(--border-color);\r\n  margin: 0 auto;\r\n  margin-bottom: 30px;\r\n}\r\n\r\n.strike-bottom {\r\n  display: inline-block;\r\n  position: relative;\r\n  z-index: 1;\r\n  &::after {\r\n    position: absolute;\r\n    content: \"\";\r\n    width: 100%;\r\n    height: 4px;\r\n    left: 0;\r\n    bottom: 5px;\r\n    background: white;\r\n    z-index: -1;\r\n  }\r\n  &.green {\r\n    &::after {\r\n      background: $green;\r\n    }\r\n  }\r\n  &.red {\r\n    &::after {\r\n      background: $red;\r\n    }\r\n  }\r\n  &.blue {\r\n    &::after {\r\n      background: $blue;\r\n    }\r\n  }\r\n  &.white {\r\n    &::after {\r\n      background: white;\r\n    }\r\n  }\r\n}\r\n\r\n.nice-select{\r\n  &:active, &.open, &:focus{\r\n    border-color:rgba( $primary, .22)!important;\r\n  }\r\n  &.rounded-8 {\r\n    border-radius: 8px!important;\r\n  }\r\n  &.arrow-2{\r\n    &:after {\r\n      border-width: 5px;\r\n      border-color: #000 transparent transparent transparent;\r\n      border-style: solid;\r\n      display: block;\r\n      height: 5px;\r\n      width: 5px;\r\n      margin-top: -2.5px;\r\n      transform-origin: 40% 23%;\r\n      transform: rotate(0deg);\r\n    }\r\n    &.open:after{\r\n      transform:  rotate(180deg);\r\n    }\r\n \r\n  }\r\n\r\n  &.arrow-3{\r\n    &:after {\r\n      right: 16px;\r\n      height: 8px;\r\n      width: 8px;\r\n      border-color: $blackish-blue;\r\n    }\r\n    &.open:after{\r\n      transform:  rotate(180deg);\r\n    }\r\n  }\r\n\r\n  &.loaction-select{\r\n    \r\n    \r\n  }\r\n}\r\n.arrow-box-dropdown{\r\n  &:after {\r\n    border-width: 5px;\r\n    border-color: #000 transparent transparent transparent;\r\n    border-style: solid;\r\n    display: block;\r\n    height: 5px;\r\n    width: 5px;\r\n    margin-top: -2.5px;\r\n    transform-origin: 40% 23%;\r\n    transform: rotate(0deg);\r\n    pointer-events: none;\r\n    position: absolute;\r\n    right: 12px;\r\n    top: 50%;\r\n    content: '';\r\n  }\r\n  &.open:after{\r\n    transform:  rotate(180deg);\r\n  }\r\n\r\n}\r\n.arrow-toggle{\r\n  &:after{\r\n    border-width: 5px;\r\n    border-color: #000 transparent transparent transparent;\r\n    border-style: solid;\r\n    display: block;\r\n    content: \"\";\r\n    height: 5px;\r\n    width: 5px;\r\n    margin-top: -2.5px;\r\n    transform-origin: 40% 23%;\r\n    transform: rotate(0deg);\r\n    pointer-events: none;\r\n    position: absolute;\r\n    right: 12px;\r\n    top: 50%;\r\n    transition: all .15s ease-in-out;\r\n  }\r\n}\r\n\r\n\r\n.min-height-px-64 {\r\n  min-height: 64px;\r\n}\r\n\r\n.min-height-px-50{\r\n  height: 50px;\r\n}\r\n\r\n.min-height-px-297{\r\n  min-height: 297px;\r\n}\r\n\r\n.pointer-none{\r\n  pointer-events: none;\r\n}\r\n\r\n.responsive-scaling {\r\n  transform: scale(0.7);\r\n  @include tablet {\r\n    transform: scale(0.9);\r\n  }\r\n  @include desktops {\r\n    transform: scale(1);\r\n  }\r\n}\r\n\r\n\r\n.border-black-dynamic{\r\n  border-color: $mirage-2!important;\r\n  @include light-mode{\r\n    border-color: $white!important;\r\n  }\r\n}\r\n\r\n\r\n\r\n.image-group-p12{\r\n  .image-bg-positioning{\r\n    position: absolute;\r\n    top: 50%;\r\n    left: 50%;\r\n    z-index: -1;\r\n    transform: translate(-53%, -45%) scale(0.75);\r\n    @include desktops{\r\n      transform: translate(-53%, -45%) scale(0.75);\r\n    }\r\n    @include large-desktops{\r\n      transform: translate(-52%, -47%) scale(0.9);\r\n    }\r\n  }\r\n}\r\n\r\n\r\n.animation-item{\r\n  perspective: 1000px;\r\n}\r\n\r\n", ".abs-img {\r\n  position: absolute;\r\n  right: -15px;\r\n  top: -40px;\r\n  z-index: -1;\r\n}\r\n.wave-shape {\r\n  position: absolute;\r\n  bottom: -1px;\r\n  left: 0;\r\n  width: 120%;\r\n  @include large-desktops {\r\n    width: 106%;\r\n    // bottom: -35px;\r\n    img {\r\n      width: 106%;\r\n    }\r\n  }\r\n}", ".tel-content-image-group-1{\r\n  min-height: 335px;\r\n  width: 100%;\r\n  position: relative;\r\n  @include mobile-lg{\r\n    margin-left: -9%;\r\n  }\r\n  @include desktops{\r\n    margin-left: 0%;\r\n  }\r\n  .abs-image-1{\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    cursor: pointer;\r\n    @include mobile{\r\n      top: 13%;\r\n    }\r\n    @include mobile-lg{\r\n      top: 5%;\r\n      left: -2%;\r\n    }\r\n    @include tablet{\r\n      top: 10%;\r\n      left: 4%;\r\n    }\r\n    @include desktops{\r\n      top: 10%;\r\n      left: -25px;\r\n    }\r\n    @include large-desktops{\r\n      left: 0;\r\n    }\r\n  }\r\n  .abs-image-2{\r\n    position: absolute;\r\n    bottom: -15%;\r\n    right: 0px;\r\n    cursor: pointer;\r\n    @include mobile{\r\n      bottom: -8%;\r\n      right: -6%;\r\n    }\r\n    @include mobile-lg{\r\n      bottom: -13%;\r\n      right: -17%;\r\n    }\r\n    @include tablet{\r\n      bottom:-18%;\r\n      right: -5%;\r\n    }\r\n    @include desktops{\r\n      bottom: -5%;\r\n      right: 0px;\r\n      margin-bottom: -30px;\r\n    }\r\n    @include large-desktops{\r\n      right: 20px;\r\n      margin-bottom: -50px;\r\n    }\r\n  }\r\n  .responsive-scaling-2{\r\n    transform: scale(.7);\r\n    @include mobile{\r\n      transform: scale(.8);\r\n    }\r\n    @include mobile-lg{\r\n      transform: scale(.9);\r\n    }\r\n    @include tablet{\r\n      transform: scale(1);\r\n    }\r\n    @include desktops{\r\n      transform: scale(.9);\r\n    }\r\n    @include large-desktops{\r\n      transform: scale(1);\r\n    }\r\n  }\r\n}\r\n\r\n\r\n.tel-content-image-group-2{\r\n  padding-top: 25px;\r\n  display: inline-block;\r\n  padding-left: 50px;\r\n  padding-right: 25px;\r\n  @include brk-point(400px){\r\n    padding-left: 100px;\r\n  }\r\n  @include mobile-lg{\r\n    padding-left: 125px;\r\n  }\r\n  >img{\r\n    width: 100%;\r\n    @include mobile{\r\n      width: auto;\r\n    }\r\n  }\r\n  .abs-image{\r\n    &-1{\r\n     position: absolute;\r\n     bottom:40px;\r\n     left: 0;\r\n     z-index: 1;\r\n     zoom: 70%;\r\n     @include brk-point(370px){\r\n      zoom: 80%;\r\n     }\r\n     @include brk-point(400px){\r\n      zoom: 100%;\r\n     }\r\n    }\r\n    &-2{\r\n      position: absolute;\r\n      height: 100%;\r\n      z-index: -1;\r\n      top: 0px;\r\n      right:0px;\r\n      >img{\r\n        height: 85%;\r\n        @include brk-point(420px){\r\n          height: auto;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n\r\n.hover-shadow-up{\r\n  &:hover{\r\n    .anim-shadow-up{\r\n      box-shadow: 0 32px 44px -15px rgba(1, 16, 30, 0.18);\r\n    }\r\n  }\r\n}\r\n.anim-shadow-up{\r\n  box-shadow: 0 32px 44px -15px rgba(1, 16, 30, 0.18);\r\n  @include desktops{\r\n    box-shadow: 0 32px 44px -15px rgba(1, 16, 30, 0);\r\n  }\r\n  transition: .4s;\r\n}", ".parallax-section-750{\r\n  height:  350px;\r\n    @include tablet{\r\n      height:  500px;\r\n    }\r\n  @include desktops{\r\n    height:  792px;\r\n  }\r\n}"]}