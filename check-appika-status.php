<?php
/**
 * Quick Appika Status Check
 * Run this on your server to see what's happening with Appika API
 */

echo "<h1>Appika API Status Check</h1>";
echo "<p>Checking current status of Appika integration...</p>";

// Check 1: API Configuration
echo "<h2>1. API Configuration</h2>";
if (file_exists('config/api-config.php')) {
    require_once 'config/api-config.php';
    $apiConfig = getCustomerApiConfig();
    echo "✅ API config loaded<br>";
    echo "Endpoint: " . $apiConfig['endpoint'] . "<br>";
    echo "Path: " . $apiConfig['path'] . "<br>";
    echo "Key: " . substr($apiConfig['key'], 0, 10) . "...<br>";
} else {
    echo "❌ API config file not found<br>";
    exit;
}

// Check 2: Required Files
echo "<h2>2. Required Files</h2>";
$required_files = [
    'functions/create-customer-minimal.php',
    'functions/customer-data-service.php',
    'vendor/autoload.php'
];

foreach ($required_files as $file) {
    if (file_exists($file)) {
        echo "✅ $file exists<br>";
    } else {
        echo "❌ $file missing<br>";
    }
}

// Check 3: Recent Error Logs
echo "<h2>3. Recent Error Logs</h2>";
$log_files = [
    'logs/appika_debug.log',
    'logs/appika_api.log',
    'logs/appika_api_update.log'
];

foreach ($log_files as $log_file) {
    if (file_exists($log_file)) {
        $lines = file($log_file);
        $recent_lines = array_slice($lines, -5); // Last 5 lines
        echo "<h3>$log_file (last 5 lines):</h3>";
        echo "<pre>";
        foreach ($recent_lines as $line) {
            echo htmlspecialchars($line);
        }
        echo "</pre>";
    } else {
        echo "⚠️ $log_file not found<br>";
    }
}

// Check 4: Test API Connection
echo "<h2>4. Test API Connection</h2>";
if (class_exists('\GuzzleHttp\Client')) {
    try {
        $client = new \GuzzleHttp\Client([
            'base_uri' => $apiConfig['endpoint'],
            'timeout' => 10,
            'http_errors' => false,
        ]);
        
        $response = $client->request('GET', $apiConfig['path'], [
            'headers' => [
                'X-api-key' => $apiConfig['key'],
                'Accept' => 'application/json',
            ],
            'query' => ['limit' => 1]
        ]);
        
        $statusCode = $response->getStatusCode();
        echo "API Status Code: $statusCode<br>";
        
        if ($statusCode >= 200 && $statusCode < 300) {
            echo "✅ API connection successful<br>";
        } else {
            echo "❌ API connection failed<br>";
            $body = $response->getBody()->getContents();
            echo "Response: <pre>" . htmlspecialchars($body) . "</pre>";
        }
        
    } catch (Exception $e) {
        echo "❌ API connection exception: " . $e->getMessage() . "<br>";
    }
} else {
    echo "❌ Guzzle HTTP Client not available<br>";
}

// Check 5: Recent User Creation Attempts
echo "<h2>5. Recent User Creation Attempts</h2>";
if (file_exists('functions/server.php')) {
    require_once 'functions/server.php';
    
    // Check recent users in database
    $result = mysqli_query($conn, "
        SELECT id, username, email, appika_customer_id, registration_time 
        FROM user 
        WHERE registration_time >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
        ORDER BY registration_time DESC 
        LIMIT 5
    ");
    
    if ($result && mysqli_num_rows($result) > 0) {
        echo "<table border='1'>";
        echo "<tr><th>ID</th><th>Username</th><th>Email</th><th>Appika ID</th><th>Registration Time</th></tr>";
        while ($row = mysqli_fetch_assoc($result)) {
            echo "<tr>";
            echo "<td>" . $row['id'] . "</td>";
            echo "<td>" . $row['username'] . "</td>";
            echo "<td>" . $row['email'] . "</td>";
            echo "<td>" . ($row['appika_customer_id'] ?: 'NULL') . "</td>";
            echo "<td>" . $row['registration_time'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "No recent user registrations found in the last 24 hours<br>";
    }
} else {
    echo "❌ Cannot check database - server.php not found<br>";
}

echo "<h2>Status Check Complete</h2>";
echo "<p>This will help identify what changed and why Appika integration stopped working.</p>";
?> 