<?php
/**
 * Fix Guzzle Loading Issue
 * This script will diagnose and fix the Guzzle autoloading problem
 */

echo "<h1>Fixing Guzzle Loading Issue</h1>";

// Step 1: Check if vendor/autoload.php exists and is readable
echo "<h2>Step 1: Check vendor/autoload.php</h2>";
if (file_exists('vendor/autoload.php')) {
    echo "✅ vendor/autoload.php exists<br>";
    
    if (is_readable('vendor/autoload.php')) {
        echo "✅ vendor/autoload.php is readable<br>";
        
        // Try to include it
        try {
            require_once 'vendor/autoload.php';
            echo "✅ vendor/autoload.php included successfully<br>";
        } catch (Exception $e) {
            echo "❌ Error including vendor/autoload.php: " . $e->getMessage() . "<br>";
        }
    } else {
        echo "❌ vendor/autoload.php is not readable<br>";
        echo "Permissions: " . substr(sprintf('%o', fileperms('vendor/autoload.php')), -4) . "<br>";
    }
} else {
    echo "❌ vendor/autoload.php does not exist<br>";
}

// Step 2: Check if Guzzle files exist
echo "<h2>Step 2: Check Guzzle Files</h2>";
$guzzle_files = [
    'vendor/guzzlehttp/guzzle/src/Client.php',
    'vendor/guzzlehttp/guzzle/src/ClientInterface.php',
    'vendor/guzzlehttp/psr7/src/Request.php',
    'vendor/guzzlehttp/psr7/src/Response.php'
];

foreach ($guzzle_files as $file) {
    if (file_exists($file)) {
        echo "✅ $file exists<br>";
    } else {
        echo "❌ $file missing<br>";
    }
}

// Step 3: Check Composer autoload files
echo "<h2>Step 3: Check Composer Autoload Files</h2>";
$composer_files = [
    'vendor/composer/autoload_real.php',
    'vendor/composer/autoload_static.php',
    'vendor/composer/autoload_psr4.php',
    'vendor/composer/autoload_classmap.php'
];

foreach ($composer_files as $file) {
    if (file_exists($file)) {
        echo "✅ $file exists<br>";
    } else {
        echo "❌ $file missing<br>";
    }
}

// Step 4: Try manual include of Guzzle
echo "<h2>Step 4: Try Manual Include</h2>";
try {
    // Try to include Guzzle directly
    if (file_exists('vendor/guzzlehttp/guzzle/src/Client.php')) {
        require_once 'vendor/guzzlehttp/guzzle/src/Client.php';
        echo "✅ Manually included Guzzle Client.php<br>";
        
        if (class_exists('GuzzleHttp\Client')) {
            echo "✅ GuzzleHttp\Client class now exists!<br>";
        } else {
            echo "❌ GuzzleHttp\Client class still doesn't exist after manual include<br>";
        }
    } else {
        echo "❌ Cannot manually include - Guzzle Client.php not found<br>";
    }
} catch (Exception $e) {
    echo "❌ Error manually including Guzzle: " . $e->getMessage() . "<br>";
}

// Step 5: Check if there are any PHP errors
echo "<h2>Step 5: Check for PHP Errors</h2>";
$error_log = error_get_last();
if ($error_log) {
    echo "⚠️ Last PHP Error: " . $error_log['message'] . "<br>";
} else {
    echo "✅ No PHP errors detected<br>";
}

// Step 6: Try alternative approach - check if we can use cURL directly
echo "<h2>Step 6: Test cURL Alternative</h2>";
if (function_exists('curl_init')) {
    echo "✅ cURL is available<br>";
    
    // Test a simple cURL request to Appika
    $apiConfig = getCustomerApiConfig();
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $apiConfig['endpoint'] . $apiConfig['path']);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Authorization: Bearer ' . $apiConfig['key'],
        'Content-Type: application/json'
    ]);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($error) {
        echo "❌ cURL Error: $error<br>";
    } else {
        echo "✅ cURL request completed<br>";
        echo "HTTP Code: $http_code<br>";
        echo "Response: " . substr($response, 0, 200) . "...<br>";
    }
} else {
    echo "❌ cURL is not available<br>";
}

// Step 7: Provide solutions
echo "<h2>Step 7: Solutions</h2>";
echo "<h3>If Guzzle files are missing:</h3>";
echo "1. Download the complete vendor folder from your local machine<br>";
echo "2. Upload the entire vendor/ directory to your server<br>";
echo "3. Make sure all files have proper permissions (644 for files, 755 for directories)<br>";

echo "<h3>If Guzzle files exist but aren't loading:</h3>";
echo "1. Check file permissions on vendor/ directory<br>";
echo "2. Try running: chmod -R 755 vendor/<br>";
echo "3. Check if your hosting provider allows Composer autoloading<br>";

echo "<h3>Alternative Solution:</h3>";
echo "If Guzzle continues to fail, we can modify the code to use cURL directly instead of Guzzle.<br>";

echo "<h2>Next Steps</h2>";
echo "Based on the results above, we'll know exactly what to fix. The most common solution is to re-upload the vendor folder with proper permissions.";
?> 