<?php
/**
 * Test User Creation Script
 * Simulates the user creation process to identify where Appika sync fails
 */

require_once 'functions/server.php';
require_once 'functions/create-customer-minimal.php';
require_once 'config/api-config.php';

echo "<h1>User Creation Test</h1>";

// Test data
$testUser = [
    'username' => 'test_' . time(),
    'email' => 'test_' . time() . '@example.com',
    'password' => 'test123',
    'first_name' => 'Test User',
    'tell' => '*********0',
    'company_name' => 'Test Company',
    'tax_id' => '*********',
    'address' => '123 Test Street',
    'address2' => 'Test Address 2',
    'district' => 'Test District',
    'city' => 'Test City',
    'state' => 'Test State',
    'postal_code' => '12345',
    'country' => 'TH',
    'timezone' => 'Asia/Bangkok'
];

echo "<h2>Test User Data</h2>";
echo "<pre>" . print_r($testUser, true) . "</pre>";

// Step 1: Create minimal customer record
echo "<h2>Step 1: Creating Minimal Customer Record</h2>";
try {
    $result = createMinimalCustomerRecord($testUser);
    echo "Result: <pre>" . print_r($result, true) . "</pre>";
    
    if ($result['success']) {
        $userId = $result['user_id'];
        echo "✅ User created with ID: $userId<br>";
        
        // Step 2: Create customer in Appika
        echo "<h2>Step 2: Creating Customer in Appika</h2>";
        $appikaResult = createCustomerInAppika($userId, $testUser);
        echo "Appika Result: <pre>" . print_r($appikaResult, true) . "</pre>";
        
        if ($appikaResult['success']) {
            echo "✅ Customer created in Appika successfully<br>";
            echo "Appika ID: " . $appikaResult['appika_id'] . "<br>";
            echo "Appika Customer ID: " . $appikaResult['appika_customer_id'] . "<br>";
        } else {
            echo "❌ Failed to create customer in Appika<br>";
            echo "Error: " . $appikaResult['message'] . "<br>";
        }
        
        // Step 3: Update local record with Appika IDs
        if ($appikaResult['success']) {
            echo "<h2>Step 3: Updating Local Record</h2>";
            $updateResult = updateCustomerAppikaIds(
                $userId, 
                $appikaResult['appika_id'], 
                $appikaResult['appika_customer_id']
            );
            
            if ($updateResult) {
                echo "✅ Local record updated successfully<br>";
            } else {
                echo "❌ Failed to update local record<br>";
            }
        }
        
        // Step 4: Verify final result
        echo "<h2>Step 4: Final Verification</h2>";
        $finalResult = createCustomerMinimal($testUser);
        echo "Final Result: <pre>" . print_r($finalResult, true) . "</pre>";
        
        if ($finalResult['success']) {
            echo "✅ Complete user creation process successful!<br>";
        } else {
            echo "❌ Complete user creation process failed<br>";
        }
        
    } else {
        echo "❌ Failed to create minimal customer record<br>";
        echo "Error: " . $result['message'] . "<br>";
    }
    
} catch (Exception $e) {
    echo "❌ Exception during user creation: " . $e->getMessage() . "<br>";
    echo "Stack trace: <pre>" . $e->getTraceAsString() . "</pre>";
}

echo "<h2>Test Complete</h2>";
echo "<p>This test simulates the exact user creation process used during registration.</p>";
?> 