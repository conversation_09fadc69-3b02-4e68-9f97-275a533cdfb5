<?php
session_start();
include('../functions/server.php');
include('../functions/timezone-helper.php');

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'error' => 'Not logged in']);
    exit();
}

$user_id = $_SESSION['user_id'];

// Get the last message ID from the request (for fetching only new messages)
$last_message_id = isset($_GET['last_id']) ? intval($_GET['last_id']) : 0;

try {
    // Get messages for this user (live chat mode)
    $messages_query = "SELECT cm.*,
                      CASE
                          WHEN cm.sender_type = 'admin' THEN a.username
                          WHEN cm.sender_type = 'user' THEN u.username
                      END as sender_name
                      FROM chat_messages cm
                      LEFT JOIN admin_users a ON cm.sender_id = a.id AND cm.sender_type = 'admin'
                      LEFT JOIN user u ON cm.sender_id = u.id AND cm.sender_type = 'user'
                      WHERE cm.user_id = ? AND cm.id > ?
                      ORDER BY cm.created_at ASC";

    $stmt = mysqli_prepare($conn, $messages_query);
    mysqli_stmt_bind_param($stmt, 'ii', $user_id, $last_message_id);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);

    $messages = [];
    while ($message = mysqli_fetch_assoc($result)) {
        // Format time for display
        $message['formatted_time'] = showCustomerTime($message['created_at'], 'g:i A');
        $messages[] = $message;
    }
    mysqli_stmt_close($stmt);

    // Mark admin messages as read
    if (!empty($messages)) {
        $update_query = "UPDATE chat_messages
                        SET is_read = 1
                        WHERE user_id = ? AND sender_type = 'admin' AND is_read = 0";
        $stmt = mysqli_prepare($conn, $update_query);
        mysqli_stmt_bind_param($stmt, 'i', $user_id);
        mysqli_stmt_execute($stmt);
        mysqli_stmt_close($stmt);
    }

    echo json_encode([
        'success' => true,
        'messages' => $messages
    ]);

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => 'Database error: ' . $e->getMessage()
    ]);
}
?>
