<?php
session_start();
include('../functions/server.php');
include('../functions/graphql_functions.php');

// Set JSON header
header('Content-Type: application/json');

// Check if user is logged in
if (!isset($_SESSION['username'])) {
    echo json_encode(['success' => false, 'error' => 'Not logged in']);
    exit();
}

$username = $_SESSION['username'];
$user_id = $_SESSION['user_id'];

// Get action
$action = $_GET['action'] ?? $_POST['action'] ?? '';
$ticket_id = intval($_GET['ticket_id'] ?? $_POST['ticket_id'] ?? 0);

if ($ticket_id <= 0) {
    echo json_encode(['success' => false, 'error' => 'Invalid ticket ID']);
    exit();
}

// Verify user has access to this ticket
$user_query = "SELECT email FROM user WHERE username = ?";
$stmt = mysqli_prepare($conn, $user_query);
mysqli_stmt_bind_param($stmt, 's', $username);
mysqli_stmt_execute($stmt);
$user_result = mysqli_stmt_get_result($stmt);
$user_data = mysqli_fetch_assoc($user_result);
mysqli_stmt_close($stmt);

if (!$user_data) {
    echo json_encode(['success' => false, 'error' => 'User not found']);
    exit();
}

$user_email = $user_data['email'];

switch ($action) {
    case 'get_messages':
        // Get messages from Appika API (same as working ticket-chat-api.php)
        $query = '
        query GetTicketWithMessages($id: Int!) {
            getTicket(id: $id) {
                id
                req_email
                status
                ticketMsg {
                    message
                    creator_by_contact
                    creator_by_agent
                }
            }
        }';

        $variables = ['id' => $ticket_id];
        $result = makeGraphQLRequest($query, $variables);

        if (!$result['success']) {
            echo json_encode(['success' => false, 'error' => $result['error'] ?? 'API error']);
            exit();
        }

        $ticketData = $result['data']['data']['getTicket'] ?? null;

        if (!$ticketData) {
            echo json_encode(['success' => false, 'error' => 'Ticket not found']);
            exit();
        }

        // Verify user access
        if ($ticketData['req_email'] !== $user_email) {
            echo json_encode(['success' => false, 'error' => 'Access denied']);
            exit();
        }

        // Process messages (same logic as working ticket-chat-api.php)
        $messages = [];
        if (isset($ticketData['ticketMsg']) && is_array($ticketData['ticketMsg'])) {
            foreach ($ticketData['ticketMsg'] as $index => $msg) {
                $messageText = $msg['message'] ?? '';

                // Skip empty messages
                if (empty(trim($messageText))) {
                    continue;
                }

                // Determine sender based on Appika's creator fields
                $creatorByContact = $msg['creator_by_contact'] ?? null;
                $creatorByAgent = $msg['creator_by_agent'] ?? null;

                if (!empty($creatorByContact)) {
                    // Message sent by contact/user
                    $senderType = 'user';
                    $senderName = 'You';
                } elseif (!empty($creatorByAgent)) {
                    // Message sent by agent/support
                    $senderType = 'admin';
                    $senderName = 'Support';
                } else {
                    // First message is usually description, others default to user
                    $senderType = $index === 0 ? 'system' : 'user';
                    $senderName = $index === 0 ? 'System' : 'You';
                }

                $messages[] = [
                    'id' => $index,
                    'message' => $messageText,
                    'sender_type' => $senderType,
                    'sender_name' => $senderName,
                    'is_description' => $index === 0,
                    'creator_by_contact' => $creatorByContact,
                    'creator_by_agent' => $creatorByAgent
                ];
            }
        }

        echo json_encode(['success' => true, 'messages' => $messages]);
        break;
        
    case 'send_message':
        $message = isset($_POST['message']) ? trim($_POST['message']) : '';

        if (empty($message)) {
            echo json_encode(['success' => false, 'error' => 'Message cannot be empty']);
            exit();
        }

        // Use the correct mutation for user (contact) messages
        $mutation = '
        mutation updateTicketMessageByContact($id: Int!, $reply_msg: String!) {
            updateTicketMessageByContact(id: $id, reply_msg: $reply_msg) {
                id
                message
                msg_type
                creator_by_contact
                creator_by_agent
            }
        }';
        $variables = [
            'id' => $ticket_id,
            'reply_msg' => $message
        ];

        $result = makeGraphQLRequest($mutation, $variables);
        if ($result['success'] && isset($result['data']['data']['updateTicketMessageByContact'])) {
            $msg = $result['data']['data']['updateTicketMessageByContact'];
            echo json_encode([
                'success' => true,
                'message' => [
                    'id' => $msg['id'] ?? time(),
                    'message' => $msg['message'] ?? $message,
                    'sender_type' => !empty($msg['creator_by_contact']) ? 'user' : (!empty($msg['creator_by_agent']) ? 'admin' : 'user'),
                    'sender_name' => !empty($msg['creator_by_contact']) ? 'You' : (!empty($msg['creator_by_agent']) ? 'Support' : 'You'),
                    'msg_type' => $msg['msg_type'] ?? null,
                    'creator_by_contact' => $msg['creator_by_contact'] ?? null,
                    'creator_by_agent' => $msg['creator_by_agent'] ?? null,
                    'is_description' => false
                ]
            ]);
        } else {
            echo json_encode([
                'success' => false,
                'error' => $result['error'] ?? 'Unknown error'
            ]);
        }
        break;

    default:
        echo json_encode(['success' => false, 'error' => 'Invalid action']);
        break;
}
?>
