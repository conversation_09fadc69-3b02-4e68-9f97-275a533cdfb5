<?php
session_start();
include('../functions/server.php');
include('../functions/graphql_functions.php');

// Set JSON header
header('Content-Type: application/json');

// Debug info
$debug = [
    'session_username' => $_SESSION['username'] ?? 'NOT SET',
    'session_user_id' => $_SESSION['user_id'] ?? 'NOT SET',
    'get_params' => $_GET,
    'post_params' => $_POST
];

// Check if user is logged in
if (!isset($_SESSION['username'])) {
    echo json_encode(['success' => false, 'error' => 'Not logged in', 'debug' => $debug]);
    exit();
}

$username = $_SESSION['username'];
$user_id = $_SESSION['user_id'];

// Get ticket ID
$ticket_id = intval($_GET['ticket_id'] ?? $_POST['ticket_id'] ?? 0);

if ($ticket_id <= 0) {
    echo json_encode(['success' => false, 'error' => 'Invalid ticket ID', 'debug' => $debug]);
    exit();
}

// Get user email
$user_query = "SELECT email FROM user WHERE username = ?";
$stmt = mysqli_prepare($conn, $user_query);
mysqli_stmt_bind_param($stmt, 's', $username);
mysqli_stmt_execute($stmt);
$user_result = mysqli_stmt_get_result($stmt);
$user_data = mysqli_fetch_assoc($user_result);
mysqli_stmt_close($stmt);

if (!$user_data) {
    echo json_encode(['success' => false, 'error' => 'User not found in database', 'debug' => $debug]);
    exit();
}

$user_email = $user_data['email'];
$debug['user_email'] = $user_email;

// Test GraphQL query
$query = '
query GetTicket($id: Int!) {
    getTicket(id: $id) {
        id
        req_email
        subject
        type
        type_name
        priority
        status
        contact_id
        agent_id
    }
}';

$variables = ['id' => $ticket_id];
$debug['query_variables'] = $variables;

$result = makeGraphQLRequest($query, $variables);

$debug['graphql_result'] = $result;

if (!$result['success']) {
    echo json_encode([
        'success' => false, 
        'error' => 'GraphQL request failed: ' . ($result['error'] ?? 'Unknown error'),
        'debug' => $debug
    ]);
    exit();
}

$ticketData = $result['data']['data']['getTicket'] ?? null;
$debug['ticket_data'] = $ticketData;

if (!$ticketData) {
    echo json_encode([
        'success' => false, 
        'error' => 'Ticket not found in API response',
        'debug' => $debug
    ]);
    exit();
}

// Check email access
$debug['email_match'] = ($ticketData['req_email'] === $user_email);

echo json_encode([
    'success' => true,
    'message' => 'Debug successful',
    'debug' => $debug
]);
?>
