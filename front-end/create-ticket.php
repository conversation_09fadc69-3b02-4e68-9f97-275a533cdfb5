<?php
include('../functions/server.php');
include('../functions/maintenance-check.php');
require_once '../functions/graphql_functions.php'; // Include GraphQL functions for updateAppikaTicket
require_once '../functions/customer-data-service.php'; // Include customer data service
require_once '../vendor/autoload.php'; // Include Composer autoloader for Guzzle
session_start();

if (!isset($_SESSION['username'])) {
    header("Location: ../index.php");
    exit();
}

// Check if user panel is accessible (strict mode)
checkMaintenanceStatus('strict');

$username = $_SESSION['username'];

// Fetch complete user info including Appika data
$user = getCompleteCustomerData($username);
if (!$user) {
    // Fallback to basic user data if customer service fails
    $userQuery = "SELECT * FROM user WHERE username = '$username'";
    $userResult = mysqli_query($conn, $userQuery);
    $user = mysqli_fetch_assoc($userResult);
}
$userId = $user['id'] ?? 0;
$appikaCustomerId = $user['appika_customer_id'] ?? null;

// Get the latest purchase to show "- New" indicator
$latestPurchaseQuery = "SELECT DISTINCT ticket_type, MAX(purchase_time) as latest_purchase_time
                       FROM purchasetickets
                       WHERE username = '$username'
                       AND purchase_time = (
                           SELECT MAX(purchase_time) FROM purchasetickets
                           WHERE username = '$username'
                       )
                       GROUP BY ticket_type";
$latestPurchaseResult = mysqli_query($conn, $latestPurchaseQuery);
$latestPurchaseTypes = [];
$latestPurchaseTime = null;

if ($latestPurchaseResult) {
    while ($row = mysqli_fetch_assoc($latestPurchaseResult)) {
        $latestPurchaseTypes[] = strtolower($row['ticket_type']);
        if (!$latestPurchaseTime) {
            $latestPurchaseTime = $row['latest_purchase_time'];
        }
    }
}

// Check which ticket types have been used since the latest purchase
$usedTicketTypes = [];
if ($latestPurchaseTime && !empty($latestPurchaseTypes)) {
    $usedTicketsQuery = "SELECT DISTINCT ticket_type FROM support_tickets
                        WHERE user_id = $userId
                        AND created_at > '$latestPurchaseTime'";
    $usedTicketsResult = mysqli_query($conn, $usedTicketsQuery);

    if ($usedTicketsResult) {
        while ($row = mysqli_fetch_assoc($usedTicketsResult)) {
            $usedTicketTypes[] = strtolower($row['ticket_type']);
        }
    }
}

// Filter out ticket types that have been used since latest purchase
$newTicketTypes = array_diff($latestPurchaseTypes, $usedTicketTypes);

// Check if user has any tickets available
$hasTickets = ($user['starter_tickets'] > 0) || ($user['premium_tickets'] > 0) || ($user['ultimate_tickets'] > 0);

// Initialize message
$message = "";

// Flag to show success animation
$showSuccessAnimation = false;
$createdTicketId = null; // Store the created ticket ID for redirect

// Load centralized API configuration
require_once '../config/api-config.php';

// Get GraphQL API configuration
$graphqlConfig = getGraphqlApiConfig();
$graphqlEndpoint = $graphqlConfig['endpoint'];
$apiKey = $graphqlConfig['key'];

// makeGraphQLRequest function is now loaded from graphql_functions.php

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Initialize variables
    $subject = mysqli_real_escape_string($conn, $_POST['subject'] ?? '');
    $priority = 'Normal'; // Set default priority to Normal
    $ticketType = mysqli_real_escape_string($conn, $_POST['ticket_type'] ?? '');
    $severity = 'Normal'; // Set default severity to Normal
    $problemType = NULL; // Set problem type to NULL
    $description = mysqli_real_escape_string($conn, $_POST['description'] ?? '');

    // Handle photo uploads
    $attachments = [];
    $upload_errors = [];

    if (isset($_FILES['photos']) && !empty($_FILES['photos']['name'][0])) {
        $upload_dir = '../uploads/tickets/';

        // Create upload directory if it doesn't exist
        if (!is_dir($upload_dir)) {
            mkdir($upload_dir, 0755, true);
        }

        $allowed_types = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
        $max_file_size = 5 * 1024 * 1024; // 5MB
        $max_files = 5;

        $file_count = min(count($_FILES['photos']['name']), $max_files);

        for ($i = 0; $i < $file_count; $i++) {
            if ($_FILES['photos']['error'][$i] === UPLOAD_ERR_OK) {
                $file_tmp = $_FILES['photos']['tmp_name'][$i];
                $file_name = $_FILES['photos']['name'][$i];
                $file_size = $_FILES['photos']['size'][$i];
                $file_type = $_FILES['photos']['type'][$i];

                // Validate file type
                if (!in_array($file_type, $allowed_types)) {
                    $upload_errors[] = "File $file_name: Invalid file type. Only JPG, PNG, GIF, and WebP are allowed.";
                    continue;
                }

                // Validate file size
                if ($file_size > $max_file_size) {
                    $upload_errors[] = "File $file_name: File too large. Maximum size is 5MB.";
                    continue;
                }

                // Generate unique filename
                $file_extension = pathinfo($file_name, PATHINFO_EXTENSION);
                $unique_filename = 'ticket_' . time() . '_' . $i . '_' . uniqid() . '.' . $file_extension;
                $file_path = $upload_dir . $unique_filename;

                // Move uploaded file
                if (move_uploaded_file($file_tmp, $file_path)) {
                    $attachments[] = 'uploads/tickets/' . $unique_filename;
                } else {
                    $upload_errors[] = "File $file_name: Failed to upload.";
                }
            } else {
                $upload_errors[] = "File upload error for " . $_FILES['photos']['name'][$i];
            }
        }
    }

    // Show upload errors if any
    if (!empty($upload_errors)) {
        $message = "Upload errors: " . implode("; ", $upload_errors);
    }

    // Problem type validation removed - proceed with ticket creation
    {
        // First, sync user tickets to handle any expired tickets
        require_once('../functions/ticket-expiration-functions.php');
        syncUserTableTickets($user['username']);

        // Re-fetch user data after sync to get updated ticket counts
        $user = getCompleteCustomerData($username);
        if (!$user) {
            // Fallback to basic user data if customer service fails
            $userQuery = "SELECT * FROM user WHERE username = '$username'";
            $userResult = mysqli_query($conn, $userQuery);
            $user = mysqli_fetch_assoc($userResult);
        }

        // Check if user has remaining tickets of selected type
        $remaining = $user[$ticketType . '_tickets'] ?? 0;

        if ($remaining > 0) {
            // Step 1: Create ticket in Appika API ONLY (no local database save)
            // Map ticket type to API format
            $typeMapping = [
                'starter' => 1,
                'premium' => 2,
                'ultimate' => 3
            ];
            $type = $typeMapping[$ticketType] ?? 1;

            // Map priority to ALL CAPS
            $priorityMapping = [
                'Information' => 'LOW',
                'Minor' => 'LOW',
                'Normal' => 'MEDIUM',        // Fix: Normal should map to MEDIUM
                'Important' => 'MEDIUM',
                'Critical' => 'HIGH'
            ];
            $apiPriority = $priorityMapping[$priority] ?? 'MEDIUM';

            // Always use createTicketByContact (includes description automatically)
            $mutation = '
            mutation CreateTicketByContact(
              $name: String!,
              $email: String!,
              $subject: String!,
              $reply_msg: String!,
              $type: Int!
            ) {
              createTicketByContact(
                name: $name,
                email: $email,
                subject: $subject,
                reply_msg: $reply_msg,
                type: $type
              ) {
                id
                subject
                ticketMsg {
                  message
                }
              }
            }';

            // Clean description by normalizing line endings before sending to Appika
            $cleanDescription = str_replace(["\r\n", "\r"], "\n", $description);

            $variables = [
                'name' => getCustomerDisplayName($user),
                'email' => $user['email'],
                'subject' => $subject,
                'reply_msg' => $cleanDescription,
                'type' => $typeMapping[$ticketType] ?? 1
            ];
            $mutationType = 'createTicketByContact';

            // Step 2: Send to GraphQL API first
            $apiResult = makeGraphQLRequest($mutation, $variables);
            // Debug log for ticket creation
            file_put_contents('../logs/appika_debug.log', date('c') . "\nCREATE Sent: " . json_encode($variables) . "\nCREATE Response: " . json_encode($apiResult) . "\n\n", FILE_APPEND);

            // Create logs directory if it doesn't exist
            if (!file_exists("../logs")) {
                mkdir("../logs", 0755, true);
            }

            // Check success for createTicketByContact
            $apiSuccess = false;
            $appikaApiId = null;

            if ($apiResult['success'] && isset($apiResult['data']['data']['createTicketByContact']['id'])) {
                $appikaApiId = $apiResult['data']['data']['createTicketByContact']['id'];
                $apiSuccess = true;
                file_put_contents('../logs/appika_debug.log', date('c') . "\nSUCCESS: createTicketByContact created ticket with description included in ticketMsg\n", FILE_APPEND);
            }

            if ($apiSuccess) {
                // Step 3: Get Appika ID from API response and generate appika_id
                $appikaId = str_pad($appikaApiId, 3, '0', STR_PAD_LEFT); // e.g., HT076

                // Step 3.5: Update ticket priority and type in Appika (createTicketByContact doesn't accept these parameters)
                // Note: Don't include contact_id in update to preserve the auto-assigned contact_id from createTicketByContact

                // Map ticket type to display name for Appika
                $typeDisplayMapping = [
                    'starter' => 'Starter',
                    'premium' => 'Business',
                    'ultimate' => 'Ultimate'
                ];
                $typeDisplayName = $typeDisplayMapping[$ticketType] ?? ucfirst($ticketType);

                $updateData = [
                    'agent_id' => null,
                    'subject' => $subject,
                    'type' => $type,
                    'type_name' => $typeDisplayName,
                    'priority' => $apiPriority,
                    'status' => 'OPEN',
                    'req_email' => $user['email'],
                    'time_track' => '00:00:00',
                    'reply_msg' => "Priority set to $priority, Type set to $typeDisplayName",
                    'tags' => ''
                ];

                // Update the ticket with proper priority and type
                $updateResult = updateAppikaTicket($appikaApiId, $updateData);
                file_put_contents('../logs/appika_debug.log', date('c') . "\nUPDATE Priority/Type - Sent: " . json_encode($updateData) . "\nUPDATE Response: " . json_encode($updateResult) . "\n\n", FILE_APPEND);

                // Step 4: Only decrease ticket count - NO DATABASE SAVE
                // Use FIFO ticket system to consume ticket
                require_once('../functions/ticket-expiration-functions.php');
                $ticket_used = useTicketsFIFO($user['username'], $ticketType, 1);

                // Sync user table ticket counts after usage
                syncUserTableTickets($user['username']);

                if (!$ticket_used) {
                    // Fallback to old system if FIFO fails
                    $update = "UPDATE user SET {$ticketType}_tickets = {$ticketType}_tickets - 1 WHERE id = $userId";
                    mysqli_query($conn, $update);
                }

                // Success - no logging needed (only log failures for admin review)

                // Set success message
                $message = "Ticket created successfully!!";
                $showSuccessAnimation = true;
                $createdTicketId = $appikaApiId; // Use Appika ID for redirect
            } else {
                // API failed - DO NOT create ticket locally, show error message
                // Log API failure
                $log_file = fopen("../logs/ticket_api.log", "a");
                fwrite($log_file, date('Y-m-d H:i:s') . " - $mutationType API Failed: " . json_encode($apiResult) . "\n");
                fclose($log_file);

                // Create log entry for failed attempt (without consuming ticket)
                $action = "fail";
                $logDescription = "Failed to create {$ticketType} ticket - API sync failed: " . ($apiResult['error'] ?? 'Unknown error');
                $amount = "0"; // No ticket consumed
                $created_at_utc_log_fallback = getCurrentUTC();
                $logInsert = "INSERT INTO ticket_logs (ticket_id, action, description, user_id, ticket_type, amount, created_at)
                              VALUES (NULL, '$action', '$logDescription', $userId, '$ticketType', '$amount', '$created_at_utc_log_fallback')";
                mysqli_query($conn, $logInsert);

                // Show error message - ticket creation failed
                $message = "Failed to create ticket. Please try again or contact our support.";
            }
        } else {
            $message = "You don't have remaining $ticketType tickets.";
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <title>Create New Support Ticket</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link rel="shortcut icon" href="../image/png/favicon.png" type="image/x-icon">
    <!-- Bootstrap and plugins -->
    <link rel="stylesheet" href="../css/bootstrap.css">
    <link rel="stylesheet" href="../css/main.css">
    <link rel="stylesheet" href="../fonts/icon-font/css/style.css">
    <link rel="stylesheet" href="../fonts/typography-font/typo.css">
    <link rel="stylesheet" href="../fonts/fontawesome-5/css/all.css">
    <link rel="stylesheet" href="../plugins/aos/aos.min.css">
    <link rel="stylesheet" href="../plugins/fancybox/jquery.fancybox.min.css">
    <link rel="stylesheet" href="../plugins/nice-select/nice-select.min.css">
    <link rel="stylesheet" href="../plugins/slick/slick.min.css">
    <link rel="stylesheet" href="../plugins/date-picker/css/gijgo.min.css" type="text/css" />
    <link rel="stylesheet" href="../plugins/theme-mode-switcher/switcher-panel.css">
    <link rel="stylesheet" href="../css/theme-mode-custom.css">
    <style>
    body {
        padding-top: 200px;
        background-color: #F4F7FA;
    }

    body {
        margin-top: -85px !important;

    }

    @media (max-width: 767px) {
        body {
            margin-top: -15px !important;
        }
    }

    .container {
        width: 1800px;
        max-width: 95%;
    }

    /* Make site-wrapper wider */
    .site-wrapper {
        max-width: 100% !important;
        width: 100% !important;
        overflow-x: visible !important;
    }

    /* Responsive styles */
    @media (max-width: 991px) {
        body {
            padding-top: 150px;
        }
    }

    @media (max-width: 767px) {
        body {
            padding-top: 120px;
        }
    }

    @media (max-width: 575px) {
        .btn {
            padding: 0.5rem;
            font-size: 0.9rem;
        }

        /* Ensure consistent font size on mobile */
        .dropdown-select,
        .form-control,
        select,
        option,
        .dropdown-item,
        .dropdown-toggle-btn {
            font-size: 14px !important;
        }
    }

    /* Menu open state for mobile */
    body.menu-open {
        overflow: hidden;
    }

    /* Dropdown select styling */
    .dropdown-select,
    .form-control,
    select,
    option,
    .dropdown-item,
    .dropdown-toggle-btn {
        font-size: 14px !important;
    }

    /* Custom Dropdown Styling */
    .custom-dropdown-container {
        position: relative;
    }

    .custom-dropdown {
        position: relative;
    }

    .dropdown-toggle-btn {
        cursor: pointer;
        text-align: left;
        position: relative;
        padding-right: 30px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        font-size: 14px !important;
    }

    .dropdown-toggle-btn::after {
        content: '\f107';
        font-family: 'Font Awesome 5 Free';
        font-weight: 900;
        position: absolute;
        right: 12px;
        top: 50%;
        transform: translateY(-50%);
        transition: transform 0.2s ease;
    }

    .custom-dropdown.open .dropdown-toggle-btn::after {
        transform: translateY(-50%) rotate(180deg);
    }

    .dropdown-menu {
        display: none;
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        z-index: 1000;
        background-color: #fff;
        border: 1px solid rgba(0, 0, 0, 0.15);
        border-radius: 4px;
        box-shadow: 0 6px 12px rgba(0, 0, 0, 0.175);
        max-height: 300px;
        overflow-y: auto;
        margin-top: 5px;
    }

    .custom-dropdown.open .dropdown-menu {
        display: block;
    }

    .dropdown-search {
        background-color: #fff;
        /* Removed sticky positioning */
        padding: 8px;
        border-bottom: 1px solid #ddd;
    }

    #problem_type_search:focus {
        border-color: #473BF0;
        box-shadow: 0 0 0 0.2rem rgba(71, 59, 240, 0.25);
        outline: none;
    }

    .dropdown-options {
        max-height: 250px;
        overflow-y: auto;
        overflow-x: hidden;
        position: relative;
        /* Ensure proper positioning of children */
    }

    .dropdown-item {
        padding: 8px 16px;
        cursor: pointer;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .dropdown-item:hover {
        background-color: #f8f9fa;
    }

    .dropdown-item.active {
        background-color: #473BF0;
        color: white;
    }

    .dropdown-item.hidden {
        display: none;
    }

    /* Category header styling */
    .dropdown-category-header {
        padding: 10px 16px;
        font-weight: bold;
        background-color: #473BF0;
        color: white;
        border-bottom: 1px solid #3730c0;
        font-size: 14px;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        /* Removed sticky positioning */
    }

    .dropdown-category-header.hidden {
        display: none;
    }

    /* Improve dropdown item styling */
    .dropdown-item {
        padding: 10px 16px;
        cursor: pointer;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        border-bottom: 1px solid #f0f0f0;
        font-size: 14px !important;
    }

    /* Remove hover effect but keep active state */
    .dropdown-item:hover {
        background-color: transparent;
    }

    /* Style for selected item */
    .dropdown-item.active {
        background-color: #473BF0;
        color: white;
    }

    /* Custom Alert Popup Styling */
    .custom-alert {
        display: none;
        position: fixed;
        top: 20%;
        left: 50%;
        transform: translateX(-50%);
        min-width: 300px;
        max-width: 500px;
        background-color: #fff;
        border-radius: 8px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        z-index: 9999;
        overflow: hidden;
        animation: alertFadeIn 0.3s ease-out;
    }

    .custom-alert.show {
        display: block;
    }

    .custom-alert-header {
        padding: 15px 20px;
        background-color: #f8d7da;
        color: #721c24;
        font-weight: bold;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .custom-alert-header i {
        margin-right: 10px;
        font-size: 20px;
    }

    .custom-alert-body {
        padding: 20px;
        color: #333;
    }

    .custom-alert-footer {
        padding: 10px 20px 15px;
        text-align: right;
    }

    .custom-alert-btn {
        padding: 8px 16px;
        background-color: #473BF0;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-weight: 500;
        transition: all 0.2s ease;
    }

    .custom-alert-btn:hover {
        background-color: #3b31c8;
        transform: translateY(-2px);
        box-shadow: 0 2px 5px rgba(71, 59, 240, 0.3);
    }

    .custom-alert-overlay {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 9998;
    }

    .custom-alert-overlay.show {
        display: block;
    }

    @keyframes alertFadeIn {
        from {
            opacity: 0;
            transform: translate(-50%, -20px);
        }

        to {
            opacity: 1;
            transform: translate(-50%, 0);
        }
    }
    </style>
</head>

<body>

    <?php include('../header-footer/header.php'); ?>

    <div class="container">
        <div class="row">
            <!-- Sidebar - Hidden on mobile, visible on larger screens -->
            <div class="col-lg-3 col-md-4 d-none d-md-block mb-4">
                <?php include('user-menu.php'); ?>
            </div>

            <!-- Mobile menu - Visible only on mobile -->
            <div class="col-12 d-md-none mb-4">
                <?php include('user-menu.php'); ?>
            </div>

            <!-- Main content -->
            <div class="col-lg-9 col-md-8">
                <div class="cart-details-main-block" id="dynamic-cart">
                    <!-- White background card with rounded corners -->
                    <div class="bg-white p-8 rounded-10 mb-5 overflow-hidden position-relative">
                        <h2 class="text-center mb-4">Create New Support Ticket</h2>

                        <!-- Want more tickets popup text - only show when user has no tickets -->
                        <?php
                        // Check if user has any tickets available (same logic as dropdown)
                        $hasAnyTickets = ($user['starter_tickets'] > 0) || ($user['premium_tickets'] > 0) || ($user['ultimate_tickets'] > 0);

                        if (!$hasAnyTickets): ?>
                        <div class="text-center mb-4">
                            <div class="alert alert-warning d-inline-block"
                                style="background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; padding: 10px 20px; margin: 0; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                                <i class="fas fa-exclamation-triangle text-warning"></i>
                                <span class="text-dark" style="font-weight: 500;">Want more tickets? </span>
                                <a href="../support-ticket/buy-now" class="text-primary"
                                    style="font-weight: 600; text-decoration: underline;">
                                    Click here to go to pricing page
                                </a>
                            </div>
                        </div>
                        <?php endif; ?>

                        <!-- API Integration Status -->
                        <!-- <div class="alert alert-info text-center mb-3">
                            <i class="fas fa-sync-alt"></i> <strong>Enhanced with GraphQL API Integration</strong>
                            <small class="d-block mt-1">Tickets are submitted directly to Appika API only (no local database save)</small>
                        </div> -->

                        <?php if (!empty($message)) : ?>
                        <div
                            class="alert alert-<?php echo (strpos($message, 'successfully') !== false) ? 'success' : ((strpos($message, 'API sync failed') !== false) ? 'warning' : 'danger'); ?> text-center">
                            <?php echo $message; ?>
                        </div>
                        <?php endif; ?>

                        <form method="POST" id="ticketForm" enctype="multipart/form-data" onsubmit="return false;">
                            <!-- Subject and Ticket Type in same row -->
                            <div class="row">
                                <!-- Subject field (left column) -->
                                <div class="col-md-8">
                                    <div class="form-group">
                                        <label for="subject">Subject</label>
                                        <input type="text" name="subject" class="form-control" required>
                                    </div>
                                </div>

                                <!-- Ticket Type field (right column) -->
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label for="ticket_type">
                                            Ticket Type
                                            <span class="ticket-help-icon" data-toggle="tooltip" data-html="true"
                                                data-placement="top" title="<div class='tooltip-content'>
                                                         <div class='tooltip-plan'><strong>Starter:</strong><br>
                                                         • 24 hours response time<br>
                                                         • Online knowledge base<br>
                                                         • Basic PC troubleshooting<br>
                                                         • Software & OS support</div>
                                                         <div class='tooltip-plan'><strong>Business:</strong><br>
                                                         • 24 hours response time<br>
                                                         • Online knowledge base<br>
                                                         • PC & Network troubleshooting<br>
                                                         • PC & Network troubleshooting (software, OS, hardware issues)<br>
                                                         • + Custom Configurations</div>
                                                         <div class='tooltip-plan'><strong>Ultimate:</strong><br>
                                                         • 4 hours response time<br>
                                                         • Online knowledge base<br>
                                                         • PC & Network troubleshooting<br>
                                                         • PC & Network troubleshooting (software, OS, hardware issues)<br>
                                                         • + Custom Configurations<br>
                                                         • Dedicated AE<br>
                                                         • Priority Help Desk</div>
                                                         </div>">
                                                ?
                                            </span>
                                        </label>
                                        <select name="ticket_type" id="ticket_type" class="form-control dropdown-select"
                                            required>
                                            <option value="">Please select ticket type</option>
                                            <option value="starter"
                                                data-remaining="<?php echo $user['starter_tickets']; ?>">Starter
                                                (<?php echo $user['starter_tickets']; ?>
                                                remaining)<?php echo (in_array('starter', $newTicketTypes) && $user['starter_tickets'] > 0) ? ' - New' : ''; ?>
                                            </option>
                                            <option value="premium"
                                                data-remaining="<?php echo $user['premium_tickets']; ?>">Business
                                                (<?php echo $user['premium_tickets']; ?>
                                                remaining)<?php echo (in_array('premium', $newTicketTypes) && $user['premium_tickets'] > 0) ? ' - New' : ''; ?>
                                            </option>
                                            <option value="ultimate"
                                                data-remaining="<?php echo $user['ultimate_tickets']; ?>">Ultimate
                                                (<?php echo $user['ultimate_tickets']; ?>
                                                remaining)<?php echo (in_array('ultimate', $newTicketTypes) && $user['ultimate_tickets'] > 0) ? ' - New' : ''; ?>
                                            </option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <!-- Row 2: Problem Description - Full Width -->
                            <div class="row">
                                <div class="col-12">
                                    <div class="form-group">
                                        <label for="description">Problem Description</label>
                                        <textarea name="description" class="form-control" rows="5" required
                                            placeholder="Please describe your issue in detail."></textarea>
                                    </div>
                                </div>
                            </div>

                            <!-- Row 3: Photo Upload Section - Full Width -->
                            <div class="row">
                                <div class="col-12">
                                    <div class="form-group">
                                        <label for="photos">Attach Photos (Optional)</label>
                                        <div class="photo-upload-container">
                                            <div class="upload-area" id="uploadArea">
                                                <div class="upload-content">
                                                    <i class="fas fa-camera fa-3x text-muted mb-3"></i>
                                                    <h5 class="text-muted">Drag & drop photos here</h5>
                                                    <p class="text-muted mb-3">or click to browse</p>
                                                    <p class="text-muted small">Max 5 photos, 5MB each<br>JPG, PNG, GIF,
                                                        WebP
                                                        supported</p>
                                                </div>
                                            </div>
                                            <input type="file" name="photos[]" id="photos" multiple accept="image/*"
                                                style="display: none;">

                                            <!-- Photo Preview Area -->
                                            <div class="photo-preview-container mt-3" id="photoPreview"
                                                style="display: none;">
                                                <h6 class="mb-2">Selected Photos:</h6>
                                                <div class="photo-preview-grid" id="photoPreviewGrid"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Row 4: Buttons - Centered -->
                            <div class="row">
                                <div class="col-12">
                                    <div class="form-group text-center">
                                        <a href="javascript:void(0)" onclick="confirmCancel()"
                                            class="btn btn-danger btn-lg w-auto mr-2"
                                            style="border-radius: 6px; padding: 10px 15px; font-weight: 500; transition: all 0.3s ease;">
                                            Cancel
                                        </a>
                                        <button type="button" class="btn btn-lg w-auto"
                                            onclick="<?php echo $hasTickets ? 'confirmSubmit()' : 'showNoTicketsAlert()'; ?>"
                                            <?php echo !$hasTickets ? 'disabled' : ''; ?>
                                            style="background-color: <?php echo $hasTickets ? '#473BF0' : '#6c757d'; ?>; color: white; border-radius: 6px; padding: 10px 15px; font-weight: 600; transition: all 0.3s ease; box-shadow: 0 4px 6px rgba(71, 59, 240, 0.2); <?php echo !$hasTickets ? 'cursor: not-allowed; opacity: 0.6;' : ''; ?>">
                                            <?php echo $hasTickets ? 'Create Ticket' : 'No Tickets Available'; ?>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Confirmation Modal -->
    <div class="modal fade" id="confirmModal" tabindex="-1" role="dialog" aria-labelledby="confirmModalLabel"
        aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="confirmModalLabel">Confirm Ticket Creation</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    Are you sure you want to create this support ticket? This will use one of your available tickets.
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-danger text" data-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" onclick="submitForm()">Create Ticket</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Cancel Confirmation Modal -->
    <div class="modal fade" id="cancelConfirmModal" tabindex="-1" role="dialog"
        aria-labelledby="cancelConfirmModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="cancelConfirmModalLabel">Confirm Cancellation</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    Are you sure you want to cancel creating this ticket? All entered information will be lost.
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary"
                        style="background-color: #473BF0; color: white; border-color: #473BF0;"
                        data-dismiss="modal">Continue Editing</button>
                    <button type="button" class="btn btn-danger" onclick="cancelTicketCreation()">Yes, Cancel
                        Ticket</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Success Animation Modal -->
    <?php if ($showSuccessAnimation) : ?>
    <div class="modal fade" id="successModal" tabindex="-1" role="dialog" aria-hidden="true" data-backdrop="static">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-body text-center py-5">
                    <div class="success-animation">
                        <svg class="checkmark" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 52 52">
                            <circle class="checkmark__circle" cx="26" cy="26" r="25" fill="none" />
                            <path class="checkmark__check" fill="none" d="M14.1 27.2l7.1 7.2 16.7-16.8" />
                        </svg>
                    </div>
                    <h3 class="mt-4">Ticket Created Successfully!</h3>
                    <p class="mb-4">Your support ticket has been submitted to our support system and our team will respond shortly.</p>
                    <p class="text-muted">Redirecting to My Tickets in <span id="countdown">3</span> seconds...</p>
                    <a href="#" id="chatNowBtn" class="btn btn-primary">View My Tickets</a>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <style>
    /* Ticket Type Help Icon Styles */
    .ticket-help-icon {
        display: inline-block;
        width: 18px;
        height: 18px;
        background-color: #473BF0;
        color: white;
        border-radius: 50%;
        text-align: center;
        line-height: 18px;
        font-size: 12px;
        font-weight: bold;
        margin-left: 5px;
        cursor: help;
        transition: all 0.3s ease;
    }

    .ticket-help-icon:hover {
        background-color: #5a4fcf;
        transform: scale(1.1);
    }

    /* Custom Tooltip Styles */
    .tooltip-inner {
        max-width: 350px;
        padding: 15px;
        background-color: #2d3748;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        text-align: left;
    }

    .tooltip-content {
        font-size: 13px;
        line-height: 1.4;
    }

    .tooltip-plan {
        margin-bottom: 12px;
        padding-bottom: 8px;
        border-bottom: 1px solid #4a5568;
    }

    .tooltip-plan:last-child {
        margin-bottom: 0;
        border-bottom: none;
        padding-bottom: 0;
    }

    .tooltip-plan strong {
        color: #e2e8f0;
        font-size: 14px;
    }

    .tooltip.show {
        opacity: 1;
    }

    .tooltip .arrow::before {
        border-top-color: #2d3748;
    }

    /* Photo Upload Styles */
    .photo-upload-container {
        border: 2px dashed #ddd;
        border-radius: 8px;
        padding: 20px;
        background-color: #fafafa;
        transition: all 0.3s ease;
    }

    .upload-area {
        text-align: center;
        padding: 40px 20px;
        cursor: pointer;
        transition: all 0.3s ease;
        position: relative;
        border-radius: 8px;
    }

    .upload-area:hover {
        background-color: #f0f0f0;
        border-color: #473BF0;
    }

    .upload-area.dragover {
        background-color: #e8f4fd;
        border-color: #473BF0;
        transform: scale(1.02);
    }

    .upload-content {
        pointer-events: none;
        user-select: none;
    }

    .photo-preview-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
        gap: 15px;
        margin-top: 10px;
    }

    .photo-preview-item {
        position: relative;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        transition: transform 0.2s ease;
    }

    .photo-preview-item:hover {
        transform: scale(1.05);
    }

    .photo-preview-img {
        width: 100%;
        height: 120px;
        object-fit: cover;
        display: block;
    }

    .photo-remove-btn {
        position: absolute;
        top: 5px;
        right: 5px;
        background: rgba(220, 53, 69, 0.9);
        color: white;
        border: none;
        border-radius: 50%;
        width: 25px;
        height: 25px;
        font-size: 12px;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: background-color 0.2s ease;
    }

    .photo-remove-btn:hover {
        background: rgba(220, 53, 69, 1);
    }

    .photo-info {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        background: rgba(0, 0, 0, 0.7);
        color: white;
        padding: 5px;
        font-size: 11px;
        text-align: center;
    }

    /* Success Animation Styles */
    .success-animation {
        margin: 0 auto;
        width: 100px;
        height: 100px;
    }

    .checkmark {
        width: 100px;
        height: 100px;
        border-radius: 50%;
        display: block;
        stroke-width: 2;
        stroke: #4bb71b;
        stroke-miterlimit: 10;
        box-shadow: inset 0px 0px 0px #4bb71b;
        animation: fill .4s ease-in-out .4s forwards, scale .3s ease-in-out .9s both;
    }

    .checkmark__circle {
        stroke-dasharray: 166;
        stroke-dashoffset: 166;
        stroke-width: 1;
        /* ค่าตั้งต้น stroke-width */
        stroke-miterlimit: 2;
        stroke: #4bb71b;
        fill: none;
        animation: stroke 0.6s cubic-bezier(0.65, 0, 0.45, 1) forwards, resetStrokeWidth 0.1s 1s forwards;
        /* เอ่มการ reset ค่า stroke-width อนิเมร์นเสร็จ */
    }

    .checkmark__check {
        transform-origin: 50% 50%;
        stroke-dasharray: 48;
        stroke-dashoffset: 48;
        animation: stroke 0.3s cubic-bezier(0.65, 0, 0.45, 1) 0.8s forwards;
    }

    @keyframes stroke {
        100% {
            stroke-dashoffset: 0;
        }
    }

    @keyframes scale {

        0%,
        100% {
            transform: none;
        }

        50% {
            transform: scale3d(1.1, 1.1, 1);
        }
    }

    /* แก้ตรง้ถ้าขอบหนา */
    @keyframes fill {
        100% {
            box-shadow: inset 0px 0px 0px 1px #4bb71b;
        }
    }

    /* Animation to reset stroke-width back to its initial value */
    @keyframes resetStrokeWidth {
        0% {
            stroke-width: 1;
        }

        100% {
            stroke-width: 1;
            /* คเซ็ต stroke-width ให้เป็น 1 */
        }
    }
    </style>

    <!-- Vendor Scripts -->
    <script src="../js/vendor.min.js"></script>
    <!-- Plugin's Scripts -->
    <script src="../plugins/fancybox/jquery.fancybox.min.js"></script>
    <script src="../plugins/nice-select/jquery.nice-select.min.js"></script>
    <script src="../plugins/aos/aos.min.js"></script>
    <script src="../plugins/slick/slick.min.js"></script>
    <script src="../plugins/date-picker/js/gijgo.min.js"></script>
    <script src="../plugins/counter-up/jquery.waypoints.min.js"></script>
    <script src="../plugins/counter-up/jquery.counterup.min.js"></script>
    <script src="../plugins/theme-mode-switcher/gr-theme-mode-switcher.js"></script>
    <!-- Activation Script -->
    <!-- <script src="../js/custom.js"></script> -->

    <script>
    // Show success modal on page load if needed
    <?php if ($showSuccessAnimation) : ?>
    $(document).ready(function() {
        $('#successModal').modal('show');

        // Prevent form resubmission on refresh
        if (window.history.replaceState) {
            window.history.replaceState(null, null, window.location.href);
        }

        // Get the created ticket ID from PHP (now Appika ID)
        var createdTicketId = <?php echo $createdTicketId ? $createdTicketId : 'null'; ?>;

        // Build redirect URL with proper path detection
        var isLocalhost = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';
        var basePath = isLocalhost ? '/helloit' : '';
        // Redirect to my-ticket.php instead of chat-support.php
        var redirectUrl = basePath + '/front-end/my-ticket.php';

        // Update the "Start Chat Now" button href
        document.getElementById('chatNowBtn').href = redirectUrl;

        // Start countdown for automatic redirect
        var countdownElement = document.getElementById('countdown');
        var secondsLeft = 3;

        // Update countdown every second
        var countdownInterval = setInterval(function() {
            secondsLeft--;
            countdownElement.textContent = secondsLeft;

            if (secondsLeft <= 0) {
                clearInterval(countdownInterval);
                window.location.href = redirectUrl;
            }
        }, 1000);

        // Add event listener to the success modal
        $('#successModal').on('hidden.bs.modal', function() {
            // Clear the interval if modal is closed manually
            clearInterval(countdownInterval);
            // Redirect to chat when modal is closed
            window.location.href = redirectUrl;
        });
    });
    <?php endif; ?>

    // Function to show custom alert
    function showAlert(message, title = 'Warning') {
        document.getElementById('alertTitle').textContent = title;
        document.getElementById('alertMessage').textContent = message;
        document.getElementById('alertOverlay').classList.add('show');
        document.getElementById('customAlert').classList.add('show');
    }

    // Function to close custom alert
    function closeAlert() {
        document.getElementById('alertOverlay').classList.remove('show');
        document.getElementById('customAlert').classList.remove('show');
    }

    // Function to validate form fields
    function validateForm() {
        var form = document.getElementById('ticketForm');
        var subject = form.elements['subject'].value.trim();
        var description = form.elements['description'].value.trim();
        var ticketType = form.elements['ticket_type'].value;

        // Check subject
        if (!subject) {
            showAlert('Please enter a subject for your ticket.');
            form.elements['subject'].focus();
            return false;
        }

        // Check ticket type
        if (!ticketType) {
            showAlert('Please select a ticket type.');
            form.elements['ticket_type'].focus();
            return false;
        }

        // Check if selected ticket type has remaining tickets
        var selectedOption = form.elements['ticket_type'].options[form.elements['ticket_type'].selectedIndex];
        var remainingTickets = parseInt(selectedOption.getAttribute('data-remaining') || '0');

        if (remainingTickets <= 0) {
            var ticketTypeName = ticketType.charAt(0).toUpperCase() + ticketType.slice(1);
            if (ticketType === 'premium') ticketTypeName = 'Business';

            showAlert(
                `Not enough ${ticketTypeName} tickets available. Please select another ticket type or purchase more tickets from our pricing page.`
            );
            form.elements['ticket_type'].focus();
            return false;
        }

        // Check description
        if (!description) {
            showAlert('Please provide a detailed description of your problem.');
            form.elements['description'].focus();
            return false;
        }

        return true;
    }

    // Function to show confirmation modal
    function confirmSubmit() {
        // Validate form first
        if (!validateForm()) {
            return;
        }

        // Show confirmation modal
        $('#confirmModal').modal('show');
    }

    // Function to submit the form after confirmation
    function submitForm() {
        // Hide the confirmation modal
        $('#confirmModal').modal('hide');

        // Submit the form
        document.getElementById('ticketForm').method = 'POST';
        document.getElementById('ticketForm').onsubmit = null;
        document.getElementById('ticketForm').submit();
    }

    // Function to show alert when user has no tickets
    function showNoTicketsAlert() {
        showAlert(
            'You don\'t have any support tickets remaining. Please go to our pricing page to purchase more tickets.',
            'No Tickets Available');
    }

    // Function to show cancel confirmation modal
    function confirmCancel() {
        $('#cancelConfirmModal').modal('show');
    }

    // Function to cancel ticket creation and redirect to my-ticket.php
    function cancelTicketCreation() {
        window.location.href = 'my-ticket.php';
    }

    // Initialize modal functionality when document is ready
    $(document).ready(function() {
        // Initialize tooltips
        $('[data-toggle="tooltip"]').tooltip({
            html: true,
            trigger: 'hover',
            delay: {
                show: 300,
                hide: 100
            }
        });

        // Initialize the confirmation modal
        $('#confirmModal').modal({
            show: false,
            backdrop: true,
            keyboard: true
        });

        // Initialize the cancel confirmation modal
        $('#cancelConfirmModal').modal({
            show: false,
            backdrop: true,
            keyboard: true
        });

        // Handle close button click for confirmation modal
        $('#confirmModal .close').on('click', function() {
            $('#confirmModal').modal('hide');
        });

        // Handle Cancel button click for confirmation modal
        $('#confirmModal .btn-danger').on('click', function() {
            $('#confirmModal').modal('hide');
        });

        // Handle close button click for cancel confirmation modal
        $('#cancelConfirmModal .close').on('click', function() {
            $('#cancelConfirmModal').modal('hide');
        });

        // Handle Continue Editing button click for cancel confirmation modal
        $('#cancelConfirmModal .btn-secondary').on('click', function() {
            $('#cancelConfirmModal').modal('hide');
        });
    });
    </script>

    <!-- Custom Dropdown with Search Script -->
    <script>
    $(document).ready(function() {
        // Form validation - use our custom validation
        $('#ticketForm').on('submit', function(e) {
            if (!validateForm()) {
                e.preventDefault();
                return false;
            }
        });

        // Photo Upload Functionality
        let selectedFiles = [];
        const maxFiles = 5;
        const maxFileSize = 5 * 1024 * 1024; // 5MB
        const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];

        // Click to upload
        $('#uploadArea').on('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            // console.log('Upload area clicked'); // Debug log
            $('#photos')[0].click();
        });

        // File input change
        $('#photos').on('change', function(e) {
            // console.log('File input changed:', e.target.files.length, 'files'); // Debug log
            if (e.target.files.length > 0) {
                handleFiles(e.target.files);
            }
        });

        // Drag and drop functionality
        $('#uploadArea').on('dragover', function(e) {
            e.preventDefault();
            $(this).addClass('dragover');
        });

        $('#uploadArea').on('dragleave', function(e) {
            e.preventDefault();
            $(this).removeClass('dragover');
        });

        $('#uploadArea').on('drop', function(e) {
            e.preventDefault();
            $(this).removeClass('dragover');
            handleFiles(e.originalEvent.dataTransfer.files);
        });

        function handleFiles(files) {
            for (let i = 0; i < files.length && selectedFiles.length < maxFiles; i++) {
                const file = files[i];

                // Validate file type
                if (!allowedTypes.includes(file.type)) {
                    showAlert(`File "${file.name}" is not a supported image format.`);
                    continue;
                }

                // Validate file size
                if (file.size > maxFileSize) {
                    showAlert(`File "${file.name}" is too large. Maximum size is 5MB.`);
                    continue;
                }

                // Check if file already selected
                if (selectedFiles.some(f => f.name === file.name && f.size === file.size)) {
                    showAlert(`File "${file.name}" is already selected.`);
                    continue;
                }

                selectedFiles.push(file);
            }

            if (selectedFiles.length >= maxFiles) {
                showAlert(`Maximum ${maxFiles} photos allowed.`);
            }

            updatePhotoPreview();
            updateFileInput();
        }

        function updatePhotoPreview() {
            const previewContainer = $('#photoPreview');
            const previewGrid = $('#photoPreviewGrid');

            if (selectedFiles.length === 0) {
                previewContainer.hide();
                return;
            }

            previewContainer.show();
            previewGrid.empty();

            selectedFiles.forEach((file, index) => {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const previewItem = $(`
                        <div class="photo-preview-item" data-index="${index}">
                            <img src="${e.target.result}" alt="${file.name}" class="photo-preview-img">
                            <button type="button" class="photo-remove-btn" onclick="removePhoto(${index})">
                                <i class="fas fa-times"></i>
                            </button>
                            <div class="photo-info">
                                ${file.name}<br>
                                ${(file.size / 1024 / 1024).toFixed(1)}MB
                            </div>
                        </div>
                    `);
                    previewGrid.append(previewItem);
                };
                reader.readAsDataURL(file);
            });
        }

        function updateFileInput() {
            const dt = new DataTransfer();
            selectedFiles.forEach(file => dt.items.add(file));
            document.getElementById('photos').files = dt.files;
        }

        // Global function to remove photo
        window.removePhoto = function(index) {
            selectedFiles.splice(index, 1);
            updatePhotoPreview();
            updateFileInput();
        };
    });
    </script>

    <script>
    // Auto-update ticket counts every 5 seconds
    function updateTicketCounts() {
        fetch('get-ticket-counts.php', {
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Update ticket count displays in the cards
                const starterCard = document.querySelector('.col-md-4:nth-child(1) h3');
                const businessCard = document.querySelector('.col-md-4:nth-child(2) h3');
                const ultimateCard = document.querySelector('.col-md-4:nth-child(3) h3');

                if (starterCard) starterCard.textContent = data.starter_tickets;
                if (businessCard) businessCard.textContent = data.premium_tickets;
                if (ultimateCard) ultimateCard.textContent = data.ultimate_tickets;

                // Update dropdown options availability
                updateDropdownAvailability(data);

                // console.log('Create ticket: Ticket counts updated:', data);
            }
        })
        .catch(error => console.error('Error updating ticket counts:', error));
    }

    function updateDropdownAvailability(data) {
        // Update dropdown options based on available tickets
        const dropdownItems = document.querySelectorAll('.dropdown-item');
        dropdownItems.forEach(item => {
            const ticketType = item.getAttribute('data-value');
            let available = 0;

            switch(ticketType) {
                case 'starter':
                    available = data.starter_tickets;
                    break;
                case 'premium':
                    available = data.premium_tickets;
                    break;
                case 'ultimate':
                    available = data.ultimate_tickets;
                    break;
            }

            // Update the remaining count in the dropdown text
            const remainingText = item.querySelector('.remaining-count');
            if (remainingText) {
                remainingText.textContent = `(${available} remaining)`;
            }

            // Disable/enable the option based on availability
            if (available <= 0) {
                item.classList.add('disabled');
                item.style.opacity = '0.5';
                item.style.pointerEvents = 'none';
            } else {
                item.classList.remove('disabled');
                item.style.opacity = '1';
                item.style.pointerEvents = 'auto';
            }
        });
    }

    // Start auto-update when page loads
    document.addEventListener('DOMContentLoaded', function() {
        // Update immediately
        updateTicketCounts();
        // Then update every 5 seconds
        setInterval(updateTicketCounts, 5000);
    });
    </script>

    <!-- Custom Alert Popup -->
    <div class="custom-alert-overlay" id="alertOverlay"></div>
    <div class="custom-alert" id="customAlert">
        <div class="custom-alert-header">
            <div>
                <i class="fas fa-exclamation-triangle"></i>
                <span id="alertTitle">Warning</span>
            </div>
            <button type="button" class="close" onclick="closeAlert()">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
        <div class="custom-alert-body" id="alertMessage">
            Please fill in all required fields.
        </div>
        <div class="custom-alert-footer">
            <button type="button" class="custom-alert-btn" onclick="closeAlert()">OK</button>
        </div>
    </div>

</body>

</html>