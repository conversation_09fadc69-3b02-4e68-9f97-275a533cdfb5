<?php
session_start();
include('../functions/server.php');
if (isset($_GET['logout'])) {
    session_destroy();
    unset($_SESSION['username']);
    header('location: ../index.php');
}
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>contact</title>
    <link rel="shortcut icon" href="../image/png/favicon.png" type="image/x-icon">
    <!-- Bootstrap , fonts & icons  -->
    <link rel="stylesheet" href="../css/bootstrap.css">
    <link rel="stylesheet" href="../fonts/icon-font/css/style.css">
    <link rel="stylesheet" href="../fonts/typography-font/typo.css">
    <link rel="stylesheet" href="../fonts/fontawesome-5/css/all.css">
    <!-- Plugin'stylesheets  -->
    <link rel="stylesheet" href="../plugins/aos/aos.min.css">
    <link rel="stylesheet" href="../plugins/fancybox/jquery.fancybox.min.css">
    <link rel="stylesheet" href="../plugins/nice-select/nice-select.min.css">
    <link rel="stylesheet" href="../plugins/slick/slick.min.css">
    <link rel="stylesheet" href="../plugins/date-picker/css/gijgo.min.css" type="text/css" />
    <!-- Vendor stylesheets  -->
    <link rel="stylesheet" href="../plugins/theme-mode-switcher/switcher-panel.css">
    <link rel="stylesheet" href="../css/main.css">
    <link rel="stylesheet" href="../css/theme-mode-custom.css">
    <!-- Custom stylesheet -->
    <style>
    /* Custom responsive styles for contact page */

    /* General styles */
    body {
        overflow-x: hidden;
    }

    .inner-banner {
        padding-top: 120px;
        padding-bottom: 60px;
    }

    .inner-banner-simple {
        margin-top: -100px;
    }

    .contact-section {
        padding-bottom: 80px;
    }

    .login-form {
        position: relative;
        z-index: 1;
    }

    /* Desktop styles (default) */
    @media (min-width: 1200px) {
        .inner-banner {
            padding-top: 150px;
        }

        .title.gr-text-3 {
            font-size: 2.5rem;
            margin-bottom: 1.5rem;
        }

        .gr-text-8 {
            font-size: 1.1rem;
            line-height: 1.7;
        }

        .single-contact-widget {
            margin-bottom: 30px;
        }

        .widget-icon {
            font-size: 1.5rem;
        }

        .widget-text h3 {
            font-size: 1.25rem;
        }

        .widget-text p {
            font-size: 1rem;
            line-height: 1.6;
        }

        .login-form {
            padding: 35px 40px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
        }
    }

    /* Tablet styles */
    @media (min-width: 768px) and (max-width: 1199px) {
        .inner-banner {
            padding-top: 120px;
            padding-bottom: 50px;
        }

        .pb-md-11,
        .py-md-11 {
            margin-top: -120px !important;
        }

        .title.gr-text-3 {
            font-size: 2.2rem;
            margin-bottom: 1.2rem;
        }

        .gr-text-8 {
            font-size: 1rem;
            line-height: 1.6;
        }

        .contact-section {
            padding-bottom: 60px;
        }

        .single-contact-widget {
            margin-bottom: 25px;
        }

        .widget-text h3 {
            font-size: 1.1rem;
            margin-bottom: 0.3rem;
        }

        .widget-text p {
            font-size: 0.95rem;
            line-height: 1.5;
        }

        .login-form {
            padding: 30px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-control {
            padding: 10px 15px;
        }

        /* Fix order on tablet */
        .order-sm-2 {
            order: 2;
        }

        .order-md-1 {
            order: 1;
        }
    }

    /* Mobile styles */
    @media (max-width: 767px) {
        .inner-banner {
            padding-top: 100px;
            padding-bottom: 40px;
        }

        .title.gr-text-3 {
            font-size: 1.8rem;
            margin-bottom: 1rem;
        }

        .gr-text-8 {
            font-size: 0.95rem;
            line-height: 1.5;
            margin-bottom: 1.5rem;
        }

        .px-md-15 {
            padding-left: 15px !important;
            padding-right: 15px !important;
        }

        .contact-section {
            padding-bottom: 50px;
        }

        .single-contact-widget {
            margin-bottom: 20px;
        }

        .widget-icon {
            font-size: 1.2rem;
        }

        .widget-text h3 {
            font-size: 1rem;
            margin-bottom: 0.2rem;
        }

        .widget-text p {
            font-size: 0.9rem;
            line-height: 1.4;
        }

        .login-form {
            padding: 25px;
            margin-bottom: 30px;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-control {
            padding: 8px 12px;
            font-size: 0.9rem;
        }

        .gr-text-11 {
            font-size: 0.9rem;
        }

        .btn {
            padding: 10px 15px;
            font-size: 0.95rem;
        }

        /* Fix order on mobile - form first, then addresses */
        .order-sm-2 {
            order: 2;
        }

        .order-md-1 {
            order: 1;
            margin-bottom: 20px !important;
        }

        /* Container adjustments */
        .container {
            padding-left: 20px;
            padding-right: 20px;
        }
    }

    /* Small mobile styles */
    @media (max-width: 480px) {
        .inner-banner {
            padding-top: 90px;
            padding-bottom: 30px;
        }

        .title.gr-text-3 {
            font-size: 1.5rem;
        }

        .gr-text-8 {
            font-size: 0.9rem;
            line-height: 1.4;
        }

        .login-form {
            padding: 20px 15px;
        }

        .form-group {
            margin-bottom: 12px;
        }

        .form-control {
            padding: 8px 10px;
            font-size: 0.85rem;
        }

        .btn {
            padding: 8px 12px;
            font-size: 0.9rem;
        }

        .widget-text h3 {
            font-size: 0.95rem;
        }

        .widget-text p {
            font-size: 0.85rem;
            line-height: 1.3;
        }
    }

    /* Professional Contact Page Styles */
    .inner-banner {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        position: relative;
        overflow: hidden;
        min-height: 600px;
        display: flex;
        align-items: center;
    }

    .inner-banner::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="1" fill="white" opacity="0.1"/><circle cx="10" cy="60" r="1" fill="white" opacity="0.1"/><circle cx="90" cy="40" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
        opacity: 0.3;
    }

    .contact-hero-content {
        position: relative;
        z-index: 2;
        text-align: center;
    }

    .contact-main-title {
        font-size: 4rem;
        font-weight: 700;
        color: white;
        margin-bottom: 1.5rem;
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        animation: fadeInUp 1s ease-out;
    }

    .contact-subtitle {
        font-size: 1.3rem;
        color: rgba(255, 255, 255, 0.9);
        margin-bottom: 3rem;
        line-height: 1.6;
        animation: fadeInUp 1s ease-out 0.2s both;
    }

    .hero-features {
        display: flex;
        justify-content: center;
        gap: 3rem;
        margin-top: 3rem;
        animation: fadeInUp 1s ease-out 0.4s both;
    }

    .hero-feature {
        text-align: center;
        color: white;
    }

    .hero-feature-icon {
        font-size: 3rem;
        margin-bottom: 1rem;
        display: block;
    }

    .hero-feature-text {
        font-size: 1.1rem;
        font-weight: 500;
    }

    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }

        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .contact-section {
        background: #f8f9fa;
        padding: 0 100px;
    }

    .contact-card {
        background: white;
        border-radius: 20px;
        padding: 3rem;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
        margin-bottom: 2rem;
        transition: all 0.3s ease;
        border: 1px solid rgba(0, 0, 0, 0.05);
    }

    .contact-card:hover {
        transform: translateY(-10px);
        box-shadow: 0 30px 80px rgba(0, 0, 0, 0.15);
    }

    .contact-card-icon {
        width: 80px;
        height: 80px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 2rem;
        font-size: 2rem;
        color: white;
        box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
    }

    .contact-card-title {
        font-size: 1.5rem;
        font-weight: 600;
        color: #333;
        margin-bottom: 1rem;
        text-align: center;
    }

    .contact-card-content {
        color: #666;
        line-height: 1.6;
        text-align: center;
    }

    .office-location {
        background: white;
        border-radius: 15px;
        padding: 2rem;
        margin-bottom: 1.5rem;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
        transition: all 0.3s ease;
        border-left: 4px solid #667eea;
    }

    .office-location:hover {
        transform: translateX(5px);
        box-shadow: 0 15px 40px rgba(0, 0, 0, 0.12);
    }

    .office-title {
        font-size: 1.3rem;
        font-weight: 600;
        color: #333;
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .office-title::before {
        content: '📍';
        font-size: 1.5rem;
    }

    .office-address {
        color: #666;
        line-height: 1.6;
        font-size: 1rem;
    }

    .professional-form {
        background: white;
        border-radius: 20px;
        padding: 3rem;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(0, 0, 0, 0.05);
    }

    .form-title {
        font-size: 2rem;
        font-weight: 600;
        color: #333;
        margin-bottom: 2rem;
        text-align: center;
        position: relative;
    }

    .form-title::after {
        content: '';
        position: absolute;
        bottom: -10px;
        left: 50%;
        transform: translateX(-50%);
        width: 60px;
        height: 3px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 2px;
    }

    .form-group {
        margin-bottom: 1.5rem;
    }

    .form-label {
        font-size: 1rem;
        font-weight: 600;
        color: #333;
        margin-bottom: 0.5rem;
        display: block;
    }

    .form-control {
        width: 100%;
        padding: 1rem 1.5rem;
        border: 2px solid #e9ecef;
        border-radius: 10px;
        font-size: 1rem;
        transition: all 0.3s ease;
        background: #f8f9fa;
    }

    .form-control:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        background: white;
        outline: none;
    }

    .btn-submit {
        background: #5045F1;
        border: none;
        padding: 1rem 3rem;
        border-radius: 50px;
        color: white;
        font-size: 1.1rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        width: 100%;
        text-transform: uppercase;
        letter-spacing: 1px;
    }

    .btn-submit:hover {
        background: #372fc7;
    }

    .contact-info-section {
        background: white;
        border-radius: 20px;
        padding: 3rem;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(0, 0, 0, 0.05);
    }

    .section-title {
        font-size: 2rem;
        font-weight: 600;
        color: #333;
        margin-bottom: 2rem;
        text-align: center;
        position: relative;
    }

    .section-title::after {
        content: '';
        position: absolute;
        bottom: -10px;
        left: 50%;
        transform: translateX(-50%);
        width: 60px;
        height: 3px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 2px;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .contact-main-title {
            font-size: 2.5rem;
        }

        .contact-subtitle {
            font-size: 1.1rem;
        }

        .hero-features {
            flex-direction: column;
            gap: 2rem;
        }

        .contact-card,
        .professional-form,
        .contact-info-section {
            padding: 2rem;
        }

        .form-title,
        .section-title {
            font-size: 1.5rem;
        }

        .inner-banner {
            min-height: 500px;
        }
    }

    @media (max-width: 480px) {
        .contact-main-title {
            font-size: 2rem;
        }

        .contact-card,
        .professional-form,
        .contact-info-section {
            padding: 1.5rem;
        }

        .hero-feature-icon {
            font-size: 2rem;
        }

        .contact-card-icon {
            width: 60px;
            height: 60px;
            font-size: 1.5rem;
        }
    }

    @media (max-width: 991px) {
        .contact-section {
            padding: 0 20px !important;
        }
        .contact-card {
            padding: 2rem 1.2rem !important;
            border-radius: 16px !important;
        }
    }

    @media (max-width: 767px) {
        .contact-section {
            padding: 0 5px !important;
        }
        .contact-card {
            padding: 1.2rem 0.5rem !important;
            border-radius: 12px !important;
            margin-bottom: 1rem !important;
            box-shadow: 0 6px 24px rgba(0,0,0,0.08) !important;
        }
        .contact-card-icon {
            width: 60px !important;
            height: 60px !important;
            margin-bottom: 1rem !important;
        }
        .form-group {
            margin-bottom: 10px !important;
        }
        .form-control {
            font-size: 1rem !important;
            padding: 10px 12px !important;
        }
        .btn {
            width: 100% !important;
            font-size: 1.1rem !important;
            padding: 12px 0 !important;
        }
    }

    .btn, .btn-primary, button[type="submit"] {
        background: #5045F1 !important;
        color: #fff !important;
        border: none !important;
    }
    .btn:hover, .btn-primary:hover, button[type="submit"]:hover {
        background: #372fc7 !important;
        color: #fff !important;
    }
    </style>
</head>
<br>

<body data-theme="light">
    <div class="site-wrapper overflow-hidden">
        <!-- Header Area -->
        <?php
        include('../header-footer/newnavtest.php');
        ?>
        <!-- navbar-dark bg-default-2 -->
        <!-- navbar- -->
        <div class="inner-banner-simple pt-29 pb-50 pb-md-11">
            <div class="container">
                <div class="row">
                    <div class="col-lg-12">
                        <div class="contact-hero-content">
                            <h1 class="contact-main-title" style="text-align: left; color: #000;">Get in Touch</h1>
                            <p class="contact-subtitle" style="text-align: left; color: #000;">We're here to help and
                                We're ready to help you with your questions and concerns.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- Contact section -->
        <div class="contact-section" style="padding-bottom: 150px !important;">
            <div class="container">
                <!-- Main Content Row -->
                <div class="row">
                    <div class="col-lg-6 mb-4">
                        <div class="professional-form">
                            <h2 class="form-title">Send us a Message</h2>

                            <?php if (isset($_SESSION['contact_success'])): ?>
                            <div class="alert alert-success"
                                style="background: #d4edda; color: #155724; padding: 15px; border-radius: 8px; margin-bottom: 20px; border: 1px solid #c3e6cb;">
                                <?php echo $_SESSION['contact_success']; unset($_SESSION['contact_success']); ?>
                            </div>
                            <?php endif; ?>

                            <?php if (isset($_SESSION['contact_errors'])): ?>
                            <div class="alert alert-danger"
                                style="background: #f8d7da; color: #721c24; padding: 15px; border-radius: 8px; margin-bottom: 20px; border: 1px solid #f5c6cb;">
                                <?php foreach ($_SESSION['contact_errors'] as $error): ?>
                                <div><?php echo $error; ?></div>
                                <?php endforeach; ?>
                                <?php unset($_SESSION['contact_errors']); ?>
                            </div>
                            <?php endif; ?>

                            <form action="/support-ticket/send-contact" method="POST">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="first_name" class="form-label">First Name *</label>
                                            <input class="form-control" type="text" id="first_name" name="first_name"
                                                placeholder="" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="last_name" class="form-label">Last Name *</label>
                                            <input class="form-control" type="text" id="last_name" name="last_name"
                                                placeholder="" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="email" class="form-label">Email Address *</label>
                                            <input class="form-control" type="email" id="email" name="email"
                                                placeholder="" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="phone" class="form-label">Phone Number</label>
                                            <input class="form-control" type="tel" id="phone" name="phone"
                                                placeholder="">
                                        </div>
                                    </div>
                                    <div class="col-12">
                                        <div class="form-group">
                                            <label for="company" class="form-label">Company</label>
                                            <input class="form-control" type="text" id="company" name="company"
                                                placeholder="">
                                        </div>
                                    </div>
                                    <div class="col-12">
                                        <div class="form-group">
                                            <label for="subject" class="form-label">Subject *</label>
                                            <input class="form-control" type="text" id="subject" name="subject"
                                                placeholder="" required>
                                        </div>
                                    </div>
                                    <div class="col-12">
                                        <div class="form-group">
                                            <label for="message" class="form-label">Message *</label>
                                            <textarea class="form-control" id="message" name="message" rows="5"
                                                placeholder="" required></textarea>
                                        </div>
                                    </div>
                                    <!-- Honeypot field for spam protection -->
                                    <div style="display: none;">
                                        <input type="text" name="website" placeholder="Leave this field empty">
                                    </div>
                                    <div class="col-12">
                                        <button type="submit" class="btn-submit">Send Message</button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                    <div class="col-lg-6">
                        <div class="contact-info-section">
                            <h2 class="section-title">Our Offices</h2>

                            <div class="office-location">
                                <h4 class="office-title">United States</h4>
                                <div class="office-address">
                                    1875 Mission ST STE 103 #371<br>
                                    San Francisco, CA 94103<br>
                                    Phone Number: +****************
                                </div>
                            </div>

                            <div class="office-location">
                                <h4 class="office-title">Singapore</h4>
                                <div class="office-address">
                                    170 Upper Bukit Timah Rd, #02-10 <br>
                                    Singapore 588179<br>
                                    Phone Number: +(65) 8224 2660
                                </div>
                            </div>

                            <div class="office-location">
                                <h4 class="office-title">Thailand</h4>
                                <div class="office-address">
                                    1112/111 Sukhumvit Road. <br>
                                    Phra Khanong, Khlong Toei, Bangkok 10110, Thailand<br>
                                    Phone Number: +(66) 2 381 9075
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- Footer section -->
        <?php
        include('../header-footer/footer.php');
        ?>
    </div>
    <!-- Vendor Scripts -->
    <script src="../js/vendor.min.js"></script>
    <!-- Plugin's Scripts -->
    <script src="../plugins/fancybox/jquery.fancybox.min.js"></script>
    <script src="../plugins/nice-select/jquery.nice-select.min.js"></script>
    <script src="../plugins/aos/aos.min.js"></script>
    <script src="../plugins/slick/slick.min.js"></script>
    <script src="../plugins/date-picker/js/gijgo.min.js"></script>
    <script src="../plugins/counter-up/jquery.waypoints.min.js"></script>
    <script src="../plugins/counter-up/jquery.counterup.min.js"></script>
    <script src="../plugins/theme-mode-switcher/gr-theme-mode-switcher.js"></script>
    <!-- Activation Script -->
    <script src="../js/custom.js"></script>
</body>

</html>