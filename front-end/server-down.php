<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HelloIT - Service Temporarily Unavailable</title>
    <link rel="shortcut icon" href="../image/png/favicon.png" type="image/x-icon">
    <link rel="stylesheet" href="../fonts/fontawesome-5/css/all.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f8f9fa;
            color: #333;
            line-height: 1.6;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .server-down-container {
            max-width: 600px;
            width: 90%;
            background: #fff;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            padding: 40px;
            text-align: center;
            margin: 20px;
        }

        .server-icon {
            font-size: 80px;
            color: #dc3545;
            margin-bottom: 30px;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .server-title {
            font-size: 32px;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 20px;
        }

        .server-subtitle {
            font-size: 18px;
            color: #7f8c8d;
            margin-bottom: 30px;
            font-weight: 400;
        }

        .server-message {
            font-size: 16px;
            color: #555;
            margin-bottom: 30px;
            line-height: 1.8;
        }

        .status-indicator {
            display: inline-flex;
            align-items: center;
            background: #f8d7da;
            color: #721c24;
            padding: 12px 20px;
            border-radius: 25px;
            border: 1px solid #f5c6cb;
            margin-bottom: 30px;
            font-weight: 500;
        }

        .status-indicator i {
            margin-right: 8px;
            font-size: 16px;
        }

        .features-working {
            background: #d4edda;
            border-radius: 10px;
            padding: 25px;
            margin: 30px 0;
            border-left: 4px solid #28a745;
        }

        .features-working h3 {
            color: #155724;
            margin-bottom: 15px;
            font-size: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .features-working h3 i {
            margin-right: 10px;
        }

        .features-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }

        .feature-item {
            display: flex;
            align-items: center;
            color: #155724;
            font-size: 14px;
            justify-content: center;
        }

        .feature-item i {
            color: #28a745;
            margin-right: 8px;
            font-size: 16px;
        }

        .action-buttons {
            margin-top: 30px;
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .btn {
            padding: 12px 25px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            font-size: 14px;
            display: inline-flex;
            align-items: center;
        }

        .btn i {
            margin-right: 8px;
        }

        .btn-primary {
            background: #473BF0;
            color: #fff;
        }

        .btn-primary:hover {
            background: #3d32d9;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(71, 59, 240, 0.3);
        }

        .btn-outline {
            background: transparent;
            color: #473BF0;
            border: 2px solid #473BF0;
        }

        .btn-outline:hover {
            background: #473BF0;
            color: #fff;
            transform: translateY(-2px);
        }

        .btn-success {
            background: #28a745;
            color: #fff;
        }

        .btn-success:hover {
            background: #218838;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(40, 167, 69, 0.3);
        }

        .info-section {
            margin-top: 30px;
            padding: 20px;
            background: #e8f4fd;
            border-radius: 10px;
            border-left: 4px solid #473BF0;
        }

        .info-section h4 {
            color: #473BF0;
            margin-bottom: 10px;
            font-size: 16px;
        }

        .info-section p {
            color: #555;
            font-size: 14px;
            margin: 0;
        }

        .last-updated {
            margin-top: 30px;
            font-size: 12px;
            color: #999;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .server-down-container {
                padding: 30px 20px;
                margin: 10px;
            }

            .server-icon {
                font-size: 60px;
            }

            .server-title {
                font-size: 26px;
            }

            .server-subtitle {
                font-size: 16px;
            }

            .features-list {
                grid-template-columns: 1fr;
                gap: 10px;
            }

            .action-buttons {
                flex-direction: column;
                align-items: center;
            }

            .btn {
                width: 100%;
                max-width: 250px;
                justify-content: center;
            }
        }

        @media (max-width: 480px) {
            .server-down-container {
                padding: 25px 15px;
            }

            .server-icon {
                font-size: 50px;
            }

            .server-title {
                font-size: 22px;
            }

            .server-subtitle {
                font-size: 14px;
            }

            .server-message {
                font-size: 14px;
            }
        }

        /* Loading animation for refresh */
        .refreshing {
            opacity: 0.7;
            pointer-events: none;
        }

        .refresh-spinner {
            display: none;
            width: 20px;
            height: 20px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #473BF0;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 8px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .refreshing .refresh-spinner {
            display: inline-block;
        }
    </style>
</head>
<body>
    <div class="server-down-container">
        <div class="server-icon">
            <i class="fas fa-server"></i>
        </div>
        
        <h1 class="server-title">Server Temporarily Unavailable</h1>
        <p class="server-subtitle">We're experiencing technical difficulties</p>
        
        <div class="status-indicator">
            <i class="fas fa-exclamation-triangle"></i>
            <span>Some services are currently offline</span>
        </div>
        
        <div class="server-message">
            <p>Our technical team is working to resolve this issue as quickly as possible. We apologize for any inconvenience this may cause. Please try again in a few minutes.</p>
        </div>

        <div class="features-working">
            <h3><i class="fas fa-check-circle"></i> Services Still Available</h3>
            <div class="features-list">
                <div class="feature-item">
                    <i class="fas fa-check"></i>
                    <span>Browse Tickets</span>
                </div>
                <div class="feature-item">
                    <i class="fas fa-check"></i>
                    <span>Purchase Tickets</span>
                </div>
                <div class="feature-item">
                    <i class="fas fa-check"></i>
                    <span>Payment Processing</span>
                </div>
                <div class="feature-item">
                    <i class="fas fa-check"></i>
                    <span>Email Support</span>
                </div>
            </div>
        </div>

        <div class="action-buttons">
            <a href="javascript:void(0)" class="btn btn-primary" onclick="checkStatus()">
                <div class="refresh-spinner"></div>
                <i class="fas fa-sync-alt"></i>
                <span id="refreshText">Try Again</span>
            </a>
            <a href="../index.php" class="btn btn-success">
                <i class="fas fa-home"></i>
                Return to Homepage
            </a>
            <a href="mailto:<EMAIL>" class="btn btn-outline">
                <i class="fas fa-envelope"></i>
                Contact Support
            </a>
        </div>

        <div class="info-section">
            <h4><i class="fas fa-info-circle"></i> What's Happening?</h4>
            <p>We're performing essential maintenance to improve your experience. Most services will be restored shortly. Thank you for your patience and understanding.</p>
        </div>

        <div class="last-updated">
            <i class="fas fa-clock"></i>
            Last checked: <span id="lastUpdated"><?php echo date('F j, Y \a\t g:i A'); ?></span>
        </div>
    </div>

    <script>
        let isChecking = false;

        function checkStatus() {
            if (isChecking) return;
            
            isChecking = true;
            const container = document.querySelector('.server-down-container');
            const refreshText = document.getElementById('refreshText');
            const lastUpdated = document.getElementById('lastUpdated');
            
            // Add loading state
            container.classList.add('refreshing');
            refreshText.textContent = 'Checking...';
            
            // Check server status
            fetch('../functions/appika-status-checker.php')
                .then(response => response.json())
                .then(data => {
                    if (data.online) {
                        // Redirect back to the page they were trying to access
                        const referrer = document.referrer;
                        if (referrer && referrer.includes(window.location.hostname)) {
                            window.location.href = referrer;
                        } else {
                            window.location.href = '../index.php';
                        }
                    } else {
                        lastUpdated.textContent = new Date().toLocaleString();
                    }
                })
                .catch(error => {
                    console.error('Status check failed:', error);
                })
                .finally(() => {
                    // Remove loading state
                    container.classList.remove('refreshing');
                    refreshText.textContent = 'Try Again';
                    isChecking = false;
                });
        }

        // Auto-refresh every 30 seconds
        setInterval(function() {
            if (!isChecking) {
                checkStatus();
            }
        }, 30000);

        // Initial status check after 2 seconds
        setTimeout(checkStatus, 2000);
    </script>
</body>
</html>
