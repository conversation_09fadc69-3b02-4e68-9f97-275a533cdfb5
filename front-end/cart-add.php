<?php
session_start();
include('../functions/server.php');

$ticket_id = isset($_POST['ticket_id']) ? $_POST['ticket_id'] : null;
$quantity = isset($_POST['quantity']) ? $_POST['quantity'] : 1; // กำหนดค่าเริ่มต้น

if ($ticket_id && $quantity > 0) {
    if (isset($_SESSION['user_id'])) {
        // ถ้าผู้ใช้ล็อกอินแล้ว
        $user_id = $_SESSION['user_id'];

        // เช็คตะกร้าที่มีสถานะ active
        $cart_result = mysqli_query($conn, "SELECT * FROM cart WHERE user_id = '$user_id' AND status = 'active'");
        $cart = mysqli_fetch_assoc($cart_result);

        if (!$cart) {
            // ถ้าไม่มีตะกร้า active สร้างตะกร้าใหม่
            mysqli_query($conn, "INSERT INTO cart (user_id, status) VALUES ('$user_id', 'active')");
            $cart_id = mysqli_insert_id($conn);
        } else {
            // ใช้ตะกร้าที่มีอยู่แล้ว
            $cart_id = $cart['cart_id'];
        }

        // ตรวจสอบว่า ticket มีอยู่ในตาราง tickets
        $ticket_result = mysqli_query($conn, "SELECT * FROM tickets WHERE ticketid = '$ticket_id'");
        $ticket = mysqli_fetch_assoc($ticket_result);

        if ($ticket) {
            // เช็คว่ามีสินค้าในตะกร้าแล้วหรือยัง
            $existing_item_result = mysqli_query($conn, "SELECT * FROM cart_items WHERE cart_id = '$cart_id' AND ticket_id = '$ticket_id'");
            $existing_item = mysqli_fetch_assoc($existing_item_result);
            $current_quantity = $existing_item ? (int)$existing_item['quantity'] : 0;
            $new_quantity = $current_quantity + $quantity;
            if ($new_quantity > 10) {
                echo json_encode(['status' => 'error', 'message' => 'This ticket plan has already reached the limit of 10.']);
                exit;
            }
            if ($existing_item) {
                // ถ้ามีสินค้าแล้ว อัพเดทจำนวนสินค้า
                mysqli_query($conn, "UPDATE cart_items SET quantity = quantity + '$quantity' WHERE cart_id = '$cart_id' AND ticket_id = '$ticket_id'");
                echo json_encode(['status' => 'success', 'message' => 'Item updated in cart.']);
            } else {
                // ถ้ายังไม่มีสินค้าในตะกร้า เพิ่มสินค้าใหม่
                mysqli_query($conn, "INSERT INTO cart_items (cart_id, ticket_id, quantity) VALUES ('$cart_id', '$ticket_id', '$quantity')");
                echo json_encode(['status' => 'success', 'message' => 'Item added to cart.']);
            }
        } else {
            echo json_encode(['status' => 'error', 'message' => 'Ticket not found.']);
        }
    } else {
        // ถ้ายังไม่ได้ล็อกอิน (บันทึกในตะกร้าชั่วคราว)
        if (!isset($_SESSION['guest_cart'])) {
            $_SESSION['guest_cart'] = [];
        }

        $ticket_result = mysqli_query($conn, "SELECT * FROM tickets WHERE ticketid = '$ticket_id'");
        $ticket = mysqli_fetch_assoc($ticket_result);

        if ($ticket) {
            // เพิ่มสินค้าลงในตะกร้าชั่วคราว
            $found = false;
            $current_quantity = 0;
            foreach ($_SESSION['guest_cart'] as &$item) {
                if ($item['ticket_id'] == $ticket_id) {
                    $current_quantity = $item['quantity'];
                    $found = true;
                    break;
                }
            }
            $new_quantity = $current_quantity + $quantity;
            if ($new_quantity > 10) {
                echo json_encode(['status' => 'error', 'message' => 'This ticket plan has already reached the limit of 10.']);
                exit;
            }
            $found = false;
            foreach ($_SESSION['guest_cart'] as &$item) {
                if ($item['ticket_id'] == $ticket_id) {
                    $item['quantity'] += $quantity;
                    $found = true;
                    break;
                }
            }
            if (!$found) {
                // Generate a unique cart_item_id for guest cart items
                $cart_item_id = 'guest_' . time() . '_' . mt_rand(1000, 9999);
                $_SESSION['guest_cart'][] = [
                    'ticket_id' => $ticket_id,
                    'quantity' => $quantity,
                    'ticket_type' => $ticket['ticket_type'],
                    'package_size' => $ticket['package_size'],
                    'numbers_per_package' => $ticket['numbers_per_package'],
                    'dollar_price_per_package' => $ticket['dollar_price_per_package'],
                    'cart_item_id' => $cart_item_id
                ];
            }
            echo json_encode(['status' => 'success', 'message' => 'Item added to cart.']);
        } else {
            echo json_encode(['status' => 'error', 'message' => 'Ticket not found.']);
        }
    }
} else {
    echo json_encode(['status' => 'error', 'message' => 'Invalid data.']);
}