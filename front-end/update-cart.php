<?php
session_start();
include('../functions/server.php'); // ตรวจสอบว่าไฟล์นี้มีการเชื่อมต่อกับฐานข้อมูล

if (!isset($_SESSION['user_id'])) {
    echo json_encode(['status' => 'error', 'message' => 'User not logged in.']);
    exit;
}

$user_id = $_SESSION['user_id'];
$cart_item_id = isset($_POST['cart_item_id']) ? $_POST['cart_item_id'] : null;
$quantity = isset($_POST['quantity']) ? $_POST['quantity'] : 1;

if ($cart_item_id && $quantity > 0) {
    try {
        // อัพเดตจำนวนสินค้าในตะกร้า
        $query = "UPDATE cart_items SET quantity = ? WHERE id = ? AND cart_id IN (SELECT cart_id FROM cart WHERE user_id = ?)";
        $stmt = $conn->prepare($query);
        $stmt->execute([$quantity, $cart_item_id, $user_id]);

        // คำนวณราคาใหม่
        $query = "SELECT ci.quantity, t.dollar_price_per_package
                  FROM cart_items ci
                  JOIN tickets t ON ci.ticket_id = t.ticketid
                  WHERE ci.id = ?";
        $stmt = $conn->prepare($query);
        $stmt->execute([$cart_item_id]);
        $item = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($item) {
            // คำนวณราคาของสินค้าที่อัปเดต
            $total_item_price = $item['quantity'] * $item['dollar_price_per_package'];

            // ส่งข้อมูลกลับไปยัง JavaScript
            echo json_encode(['status' => 'success', 'total_item_price' => $total_item_price]);
        } else {
            echo json_encode(['status' => 'error', 'message' => 'Item not found.']);
        }
    } catch (PDOException $e) {
        echo json_encode(['status' => 'error', 'message' => 'Error: ' . $e->getMessage()]);
    }
} else {
    echo json_encode(['status' => 'error', 'message' => 'Invalid data.']);
}
