<?php
session_start();
include('../functions/server.php'); // เชื่อมต่อฐานข้อมูล

// ตรวจสอบว่าได้รับข้อมูลจาก POST หรือไม่
if (isset($_POST['cart_items'])) {
    // แปลงข้อมูลที่ได้รับจาก JSON
    $cart_items = json_decode($_POST['cart_items'], true);

    if (empty($cart_items)) {
        echo "No items received to update.";
        exit;
    }

    // ตรวจสอบว่าเป็นผู้ใช้ที่ล็อกอินหรือไม่
    if (isset($_SESSION['user_id'])) {
        // ผู้ใช้ล็อกอินแล้ว
        $user_id = $_SESSION['user_id'];

        // อัปเดตข้อมูลในฐานข้อมูล
        foreach ($cart_items as $item) {
            $cart_item_id = $item['cart_item_id'];
            $quantity = $item['quantity'];

            // ตรวจสอบว่า cart_item_id และ user_id ตรงกัน
            $query = "UPDATE cart_items SET quantity = ? WHERE id = ? AND user_id = ?";
            $stmt = $conn->prepare($query);

            if ($stmt) {
                $stmt->bind_param("iii", $quantity, $cart_item_id, $user_id);
                $stmt->execute();
                if ($stmt->affected_rows > 0) {
                    echo "Cart updated successfully in database!";
                } else {
                    echo "No changes made to the database for cart item ID: $cart_item_id.";
                }
                $stmt->close();
            } else {
                echo "Error in preparing the SQL statement!";
            }
        }
    } else {
        // ถ้าผู้ใช้ไม่ได้ล็อกอิน
        if (isset($_SESSION['guest_cart'])) {
            // อัปเดตข้อมูลใน session
            foreach ($cart_items as $item) {
                $cart_item_id = $item['cart_item_id'];
                $quantity = $item['quantity'];

                // อัปเดตใน session
                $item_found = false;  // Flag to check if item is found in guest cart
                foreach ($_SESSION['guest_cart'] as &$guest_item) {
                    if ($guest_item['cart_item_id'] == $cart_item_id) {
                        $guest_item['quantity'] = $quantity;
                        $item_found = true;
                        break;
                    }
                }

                // Check if item was found and updated in guest cart
                if (!$item_found) {
                    echo "Cart item ID: $cart_item_id not found in guest cart.";
                }
            }

            // เซฟข้อมูลที่อัปเดตลงใน session
            $_SESSION['guest_cart'] = $_SESSION['guest_cart'];
            echo "Cart updated successfully in session!";
        } else {
            echo "No cart data found in session!";
        }
    }
} else {
    echo "No cart items received!";
}
