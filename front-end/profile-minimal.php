<?php
/**
 * Customer Profile Page - Minimal Database + Appika Integration
 * Displays essential data from local database and extended data from Appika API
 */

session_start();
include('../functions/server.php');
include('../functions/customer-data-service.php');

// Check if user is logged in
if (!isset($_SESSION['username'])) {
    header('location: login.php');
    exit();
}

$username = $_SESSION['username'];

// Get complete customer data (local + Appika)
$customerData = getCompleteCustomerData($username);

if (!$customerData) {
    echo "Customer data not found.";
    exit();
}

// Calculate total tickets
$totalTickets = ($customerData['premium_tickets'] ?? 0) + ($customerData['ultimate_tickets'] ?? 0);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Customer Profile - HelloIT</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .profile-header {
            background: linear-gradient(135deg, #473BF0 0%, #6366f1 100%);
            color: white;
            padding: 2rem 0;
        }
        .profile-card {
            border: none;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            border-radius: 10px;
        }
        .info-label {
            font-weight: 600;
            color: #6b7280;
            margin-bottom: 0.25rem;
        }
        .info-value {
            color: #1f2937;
            margin-bottom: 1rem;
        }
        .data-source-badge {
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
        }
        .appika-data {
            border-left: 3px solid #473BF0;
            padding-left: 1rem;
        }
        .local-data {
            border-left: 3px solid #10b981;
            padding-left: 1rem;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <?php include('../header-footer/header.php'); ?>

    <!-- Profile Header -->
    <div class="profile-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="mb-2">
                        <i class="fas fa-user-circle me-3"></i>
                        <?php echo htmlspecialchars(getCustomerDisplayName($customerData)); ?>
                    </h1>
                    <p class="mb-0 opacity-75">
                        Customer ID: <?php echo htmlspecialchars($customerData['username']); ?>
                        <?php if (!empty($customerData['appika_id'])): ?>
                            | Appika ID: <?php echo htmlspecialchars($customerData['appika_id']); ?>
                        <?php endif; ?>
                    </p>
                </div>
                <div class="col-md-4 text-md-end">
                    <div class="badge bg-light text-dark fs-6 p-3">
                        <i class="fas fa-ticket-alt me-2"></i>
                        Total Tickets: <?php echo number_format($totalTickets); ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Profile Content -->
    <div class="container my-5">
        <div class="row">
            <!-- Essential Information (Local Database) -->
            <div class="col-lg-6 mb-4">
                <div class="card profile-card h-100">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-database me-2"></i>
                            Essential Information
                            <span class="badge bg-light text-success ms-2 data-source-badge">Local Database</span>
                        </h5>
                    </div>
                    <div class="card-body local-data">
                        <div class="row">
                            <div class="col-sm-4 info-label">Username:</div>
                            <div class="col-sm-8 info-value"><?php echo htmlspecialchars($customerData['username']); ?></div>
                        </div>
                        <div class="row">
                            <div class="col-sm-4 info-label">Email:</div>
                            <div class="col-sm-8 info-value"><?php echo htmlspecialchars($customerData['email']); ?></div>
                        </div>
                        <div class="row">
                            <div class="col-sm-4 info-label">Premium Tickets:</div>
                            <div class="col-sm-8 info-value"><?php echo number_format($customerData['premium_tickets'] ?? 0); ?></div>
                        </div>
                        <div class="row">
                            <div class="col-sm-4 info-label">Ultimate Tickets:</div>
                            <div class="col-sm-8 info-value"><?php echo number_format($customerData['ultimate_tickets'] ?? 0); ?></div>
                        </div>
                        <div class="row">
                            <div class="col-sm-4 info-label">Registration:</div>
                            <div class="col-sm-8 info-value"><?php echo date('M j, Y', strtotime($customerData['registration_time'])); ?></div>
                        </div>
                        <div class="row">
                            <div class="col-sm-4 info-label">Timezone:</div>
                            <div class="col-sm-8 info-value"><?php echo htmlspecialchars($customerData['timezone'] ?? 'UTC'); ?></div>
                        </div>
                        <?php if (!empty($customerData['stripe_customer_id'])): ?>
                        <div class="row">
                            <div class="col-sm-4 info-label">Stripe ID:</div>
                            <div class="col-sm-8 info-value"><?php echo htmlspecialchars($customerData['stripe_customer_id']); ?></div>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Extended Information (Appika API) -->
            <div class="col-lg-6 mb-4">
                <div class="card profile-card h-100">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-cloud me-2"></i>
                            Extended Information
                            <span class="badge bg-light text-primary ms-2 data-source-badge">Appika API</span>
                        </h5>
                    </div>
                    <div class="card-body appika-data">
                        <?php if ($customerData['has_appika_data']): ?>
                            <div class="row">
                                <div class="col-sm-4 info-label">Full Name:</div>
                                <div class="col-sm-8 info-value"><?php echo htmlspecialchars($customerData['name'] ?: 'Not set'); ?></div>
                            </div>
                            <div class="row">
                                <div class="col-sm-4 info-label">Status:</div>
                                <div class="col-sm-8 info-value">
                                    <?php 
                                    $status = $customerData['status'] ?? '';
                                    if ($status === 'a') {
                                        echo '<span class="badge bg-success">Active</span>';
                                    } elseif ($status === 'i') {
                                        echo '<span class="badge bg-danger">Inactive</span>';
                                    } else {
                                        echo '<span class="badge bg-secondary">Unknown</span>';
                                    }
                                    ?>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-sm-4 info-label">Phone:</div>
                                <div class="col-sm-8 info-value"><?php echo htmlspecialchars($customerData['phone'] ?: 'Not set'); ?></div>
                            </div>
                            <div class="row">
                                <div class="col-sm-4 info-label">Contact Person:</div>
                                <div class="col-sm-8 info-value"><?php echo htmlspecialchars($customerData['contact_person'] ?: 'Not set'); ?></div>
                            </div>
                            <div class="row">
                                <div class="col-sm-4 info-label">Address:</div>
                                <div class="col-sm-8 info-value">
                                    <?php 
                                    $address = formatCustomerAddress($customerData);
                                    echo $address ? htmlspecialchars($address) : 'Not set';
                                    ?>
                                </div>
                            </div>
                            <?php if (!empty($customerData['start_date'])): ?>
                            <div class="row">
                                <div class="col-sm-4 info-label">Start Date:</div>
                                <div class="col-sm-8 info-value"><?php echo date('M j, Y', strtotime($customerData['start_date'])); ?></div>
                            </div>
                            <?php endif; ?>
                        <?php else: ?>
                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <strong>No Appika Data Available</strong><br>
                                Extended customer information is not available. This may happen if:
                                <ul class="mb-0 mt-2">
                                    <li>The customer was not synced to Appika API</li>
                                    <li>There was an error during customer creation</li>
                                    <li>The Appika API is temporarily unavailable</li>
                                </ul>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Additional Locations (if available) -->
        <?php if ($customerData['has_appika_data'] && !empty($customerData['all_locations']) && count($customerData['all_locations']) > 1): ?>
        <div class="row">
            <div class="col-12">
                <div class="card profile-card">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-map-marker-alt me-2"></i>
                            All Locations
                            <span class="badge bg-light text-info ms-2 data-source-badge">Appika API</span>
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <?php foreach ($customerData['all_locations'] as $index => $location): ?>
                            <div class="col-md-6 mb-3">
                                <div class="border rounded p-3 <?php echo ($location['is_primary_loc'] === 'y') ? 'border-primary' : 'border-light'; ?>">
                                    <h6 class="mb-2">
                                        Location <?php echo $index + 1; ?>
                                        <?php if ($location['is_primary_loc'] === 'y'): ?>
                                            <span class="badge bg-primary">Primary</span>
                                        <?php endif; ?>
                                    </h6>
                                    <div class="small">
                                        <div><strong>Address:</strong> <?php echo htmlspecialchars($location['add1'] ?: 'Not set'); ?></div>
                                        <?php if (!empty($location['add2'])): ?>
                                        <div><strong>Address 2:</strong> <?php echo htmlspecialchars($location['add2']); ?></div>
                                        <?php endif; ?>
                                        <div><strong>City:</strong> <?php echo htmlspecialchars($location['city'] ?: 'Not set'); ?></div>
                                        <div><strong>State:</strong> <?php echo htmlspecialchars($location['state_code'] ?: 'Not set'); ?></div>
                                        <div><strong>Postal Code:</strong> <?php echo htmlspecialchars($location['zip'] ?: 'Not set'); ?></div>
                                        <div><strong>Country:</strong> <?php echo htmlspecialchars($location['ccode'] ?: 'Not set'); ?></div>
                                        <?php if (!empty($location['tel_work'])): ?>
                                        <div><strong>Phone:</strong> <?php echo htmlspecialchars($location['tel_work']); ?></div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <!-- Action Buttons -->
        <div class="row mt-4">
            <div class="col-12 text-center">
                <a href="my-ticket.php" class="btn btn-primary me-3">
                    <i class="fas fa-ticket-alt me-2"></i>My Tickets
                </a>
                <a href="cart.php" class="btn btn-success me-3">
                    <i class="fas fa-shopping-cart me-2"></i>Purchase Tickets
                </a>
                <a href="edit-profile.php" class="btn btn-outline-primary">
                    <i class="fas fa-edit me-2"></i>Edit Profile
                </a>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <?php include('../header-footer/footer.php'); ?>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
