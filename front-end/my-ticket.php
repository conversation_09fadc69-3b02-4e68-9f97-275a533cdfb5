<?php
include('../functions/server.php');
include('../functions/maintenance-check.php');
require_once '../functions/graphql_functions.php'; // Include GraphQL functions for Appika API
session_start();

if (!isset($_SESSION['username'])) {
    header("Location: ../index.php");
    exit();
}

// Check if user panel is accessible (strict mode)
checkMaintenanceStatus('strict');

$username = $_SESSION['username'];

// Get filter values from form
$search = isset($_GET['search']) ? trim($_GET['search']) : "";
$typeFilter = isset($_GET['type']) ? trim($_GET['type']) : "";
$statusFilter = isset($_GET['status']) ? trim($_GET['status']) : "";
$ratingFilter = isset($_GET['rating']) ? trim($_GET['rating']) : "";

// Get user data for ticket counts and email
require_once('../functions/ticket-expiration-functions.php');
syncUserTableTickets($username);

// Re-fetch user data after sync to get updated ticket counts
$userQuery = "SELECT * FROM user WHERE username = '$username'";
$userResult = mysqli_query($conn, $userQuery);
$user = mysqli_fetch_assoc($userResult);
$userId = $user['id'] ?? 0;
$userEmail = $user['email'] ?? '';

$starterRemaining = $user['starter_tickets'] ?? 0;
$premiumRemaining = $user['premium_tickets'] ?? 0;
$ultimateRemaining = $user['ultimate_tickets'] ?? 0;

// Pagination settings
$itemsPerPage = 5;
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
if ($page < 1) $page = 1;

// Function to fetch user tickets from Appika API
function fetchUserAppikaTickets($userEmail, $search = "", $typeFilter = "", $statusFilter = "") {
    // Fetch all tickets from Appika API
    $query = '
    query GetTickets {
        getTickets {
            data {
                id
                ticket_no
                contact_id
                agent_id
                req_email
                subject
                type
                type_name
                priority
                status
                created
                updated
                creator_by_contact
                creator_by_agent
            }
        }
    }';

    $result = makeGraphQLRequest($query, []);

    if (!$result['success']) {
        // Check if this is a connection error that should trigger maintenance mode
        $error = $result['error'];
        if (strpos($error, 'Connection Error') !== false ||
            strpos($error, 'timeout') !== false ||
            strpos($error, 'timed out') !== false ||
            strpos($error, 'Operation timed out') !== false) {

            // This is a server connectivity issue - redirect to maintenance
            error_log("MAINTENANCE_TRIGGER: API connection error detected: $error");

            // Determine maintenance page URL
            $maintenance_url = 'server-down.php';
            if (strpos($_SERVER['REQUEST_URI'], '/front-end/') === false) {
                $maintenance_url = '../front-end/server-down.php';
            }

            header("Location: $maintenance_url");
            exit();
        }

        return ['tickets' => [], 'error' => $result['error']];
    }

    // Extract tickets from response
    $allTickets = [];
    if (isset($result['data']['data']['getTickets']['data'])) {
        $allTickets = $result['data']['data']['getTickets']['data'];
    } elseif (isset($result['data']['data']['getTickets']) && is_array($result['data']['data']['getTickets'])) {
        $allTickets = $result['data']['data']['getTickets'];
    }

    // Filter tickets by user email
    $userTickets = array_filter($allTickets, function($ticket) use ($userEmail) {
        return strtolower($ticket['req_email']) === strtolower($userEmail);
    });

    // Apply search filter
    if (!empty($search)) {
        $userTickets = array_filter($userTickets, function($ticket) use ($search) {
            $search_lower = strtolower($search);
            return (
                strpos(strtolower($ticket['id']), $search_lower) !== false ||
                strpos(strtolower($ticket['ticket_no']), $search_lower) !== false ||
                strpos(strtolower($ticket['subject']), $search_lower) !== false ||
                strpos(strtolower($ticket['created']), $search_lower) !== false
            );
        });
    }

    // Apply type filter
    if (!empty($typeFilter)) {
        $userTickets = array_filter($userTickets, function($ticket) use ($typeFilter) {
            // Map Appika types to local types
            $typeMapping = [
                1 => 'starter',
                2 => 'business',  // Changed from 'premium' to 'business'
                3 => 'ultimate'
            ];
            $ticketType = $typeMapping[$ticket['type']] ?? '';
            return strtolower($ticketType) === strtolower($typeFilter);
        });
    }

    // Apply status filter
    if (!empty($statusFilter)) {
        $userTickets = array_filter($userTickets, function($ticket) use ($statusFilter) {
            return strtolower($ticket['status']) === strtolower($statusFilter);
        });
    }

    // Sort by created date (newest first)
    usort($userTickets, function($a, $b) {
        return strtotime($b['created']) - strtotime($a['created']);
    });

    return ['tickets' => array_values($userTickets), 'error' => null];
}

// Fetch tickets from Appika API
$apiResult = fetchUserAppikaTickets($userEmail, $search, $typeFilter, $statusFilter);
$allUserTickets = $apiResult['tickets'];
$apiError = $apiResult['error'];

// Calculate pagination
$totalItems = count($allUserTickets);
$totalPages = ceil($totalItems / $itemsPerPage);
$offset = ($page - 1) * $itemsPerPage;

// Get tickets for current page
$tickets = array_slice($allUserTickets, $offset, $itemsPerPage);

/**
 * Generate smart pagination array
 * Shows: 1 2 3 ... 8 9 10 ... 18 19 20
 */
function generateSmartPagination($currentPage, $totalPages, $delta = 2) {
    $pages = [];

    // Always show first page
    $pages[] = 1;

    // Calculate range around current page
    $start = max(2, $currentPage - $delta);
    $end = min($totalPages - 1, $currentPage + $delta);

    // Add ellipsis after first page if needed
    if ($start > 2) {
        $pages[] = '...';
    }

    // Add pages around current page
    for ($i = $start; $i <= $end; $i++) {
        if ($i != 1 && $i != $totalPages) { // Don't duplicate first/last
            $pages[] = $i;
        }
    }

    // Add ellipsis before last page if needed
    if ($end < $totalPages - 1) {
        $pages[] = '...';
    }

    // Always show last page (if more than 1 page)
    if ($totalPages > 1) {
        $pages[] = $totalPages;
    }

    return array_unique($pages);
}

$paginationPages = generateSmartPagination($page, $totalPages);
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <title>My Support Tickets</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link rel="shortcut icon" href="../image/png/favicon.png" type="image/x-icon">
    <!-- Bootstrap and plugins -->
    <link rel="stylesheet" href="../css/bootstrap.css">
    <link rel="stylesheet" href="../css/main.css">
    <link rel="stylesheet" href="../fonts/icon-font/css/style.css">
    <link rel="stylesheet" href="../fonts/typography-font/typo.css">
    <link rel="stylesheet" href="../fonts/fontawesome-5/css/all.css">
    <link rel="stylesheet" href="../plugins/aos/aos.min.css">
    <link rel="stylesheet" href="../plugins/fancybox/jquery.fancybox.min.css">
    <link rel="stylesheet" href="../plugins/nice-select/nice-select.min.css">
    <link rel="stylesheet" href="../plugins/slick/slick.min.css">
    <link rel="stylesheet" href="../plugins/date-picker/css/gijgo.min.css" type="text/css" />
    <link rel="stylesheet" href="../plugins/theme-mode-switcher/switcher-panel.css">
    <link rel="stylesheet" href="../css/theme-mode-custom.css">
    <style>
    body {
        padding-top: 140px;
        background-color: #F4F7FA;
    }

    @media (max-width: 767px) {
        body {
            margin-top: -20px;
        }

        .badge {
            padding: 6.8px;
            font-size: 14px !important;
        }
    }

    .badge {
        padding: 6.8px;
        font-size: 16px;
    }

    .badge-open {
        background-color: #4CAF50;
        color: #fff;
    }

    .badge-wip {
        background-color: #FF9800;
        color: #fff;
    }

    .badge-solved {
        background-color: #17a2b8;
        color: #fff;
    }

    .badge-in_progress {
        background-color: #2196F3;
        color: #fff;
    }

    .badge-resolved {
        background-color: #9C27B0;
        color: #fff;
    }

    .badge-closed {
        background-color: #757575;
        color: #fff;
    }

    /* Ticket type badges */
    .badge-ultimate {
        background-color: #007BFF;
        color: #fff !important;
        font-size: 16px;
        padding: 6.8px;
    }

    .badge-starter {
        background-color: #fbbf24;
        color: #fff !important;
        font-size: 16px;
        padding: 6.8px;
    }

    .badge-premium,
    .badge-business {
        background-color: #01A7E1;
        color: #fff !important;
        font-size: 16px;
        padding: 6.8px;
    }

    .badge-ultimate {
        background-color: #793BF0;
        color: #fff !important;
        font-size: 16px;
        padding: 6.8px;
    }

    .card h3 {
        color: #007bff;
        margin: 0;
    }

    .card h5 {
        font-weight: 600;
        margin-bottom: 10px;
    }

    /* Responsive styles */
    @media (max-width: 991px) {
        body {
            padding-top: 150px;
        }

        .ticket-cards .card {
            margin-bottom: 15px;
        }
    }

    @media (max-width: 767px) {
        body {
            padding-top: 120px;
        }

        .filter-container {
            margin-top: 15px;
            text-align: left !important;
            padding-left: 40px !important;
            padding-right: 40px !important;
        }

        .filter-container select {
            margin-bottom: 10px;
            margin-left: 0 !important;
            margin-right: 5px;
            width: 48% !important;
            display: inline-block !important;

        }

        /* Removed search-container styles as they're now handled by search-filter-row */

        .ticket-cards .col-md-4 {
            margin-bottom: 15px;
        }

        .table-responsive {
            border: 0;
            width: 100%;
            margin-bottom: 15px;
            overflow-y: hidden;
            -ms-overflow-style: -ms-autohiding-scrollbar;
        }

        .pagination-container .pagination {
            flex-wrap: wrap;
            justify-content: center;
        }

        .pagination-container .page-item {
            margin-bottom: 5px;
        }

        /* Smart pagination ellipsis styling */
        .pagination .page-item.disabled .page-link {
            color: #6c757d;
            background-color: transparent;
            border-color: transparent;
            cursor: default;
        }

        .pagination .page-item.disabled .page-link:hover {
            color: #6c757d;
            background-color: transparent;
            border-color: transparent;
        }
    }

    @media (max-width: 575px) {
        .create-ticket-btn {
            display: block;
            width: 100%;
            margin-bottom: 20px;
        }

        .table th,
        .table td {
            padding: 0.5rem;
            font-size: 0.85rem;
        }

        .btn-sm {
            padding: 0.25rem 0.4rem;
            font-size: 0.75rem;
        }
    }

    /* Menu open state for mobile */
    body.menu-open {
        overflow: hidden;
    }

    /* edit field here */
    .container {
        width: 1800px;
        max-width: 95%;
    }

    /* Make site-wrapper wider */
    .site-wrapper {
        max-width: 100% !important;
        width: 100% !important;
        overflow-x: visible !important;
    }

    /* Rating button styles - make rating button use brand color */
    .rating-view-btn {
        background-color: #473BF0 !important;
        border-color: #473BF0 !important;
        color: white !important;
    }

    .rating-view-btn:hover {
        background-color: #3d32d9 !important;
        border-color: #3d32d9 !important;
        color: white !important;
    }

    /* Rate Now button styles - green for new ratings */
    .btn-success.details-btn {
        background-color: #28a745 !important;
        border-color: #28a745 !important;
        color: white !important;
    }

    .btn-success.details-btn:hover {
        background-color: #218838 !important;
        border-color: #1e7e34 !important;
        color: white !important;
    }

    /* Button width and chat button color fixes */
    .btn-group .btn {
        min-width: 60px !important;
        max-width: 80px !important;
        padding: 0.25rem 0.5rem !important;
        font-size: 0.875rem !important;
    }

    /* Chat button - use brand color */
    .btn-success {
        background-color: #473BF0 !important;
        border-color: #473BF0 !important;
        color: white !important;
    }

    .btn-success:hover {
        background-color: #3d32d9 !important;
        border-color: #3d32d9 !important;
        color: white !important;
    }

    /* View button */
    .btn-outline-primary.details-btn {
        min-width: 60px !important;
        max-width: 80px !important;
    }
    </style>
</head>

<body>
    <?php include('../header-footer/header.php'); ?>

    <div class="container">
        <div class="row">
            <!-- Sidebar - Hidden on mobile, visible on larger screens -->
            <div class="col-lg-3 col-md-4 d-none d-md-block mb-4">
                <?php include('user-menu.php'); ?>
            </div>

            <!-- Mobile menu - Visible only on mobile -->
            <div class="col-12 d-md-none mb-4">
                <?php include('user-menu.php'); ?>
            </div>

            <!-- Main content area -->
            <div class="col-lg-9 col-md-8">
                <div class="cart-details-main-block" id="dynamic-cart">
                    <!-- White background card with rounded corners -->
                    <div class="bg-white p-8 rounded-10 mb-5 overflow-hidden position-relative">
                        <h2 class="text-center mb-4">My Support Tickets</h2>

                        <!-- Login Success Message -->
                        <?php if (isset($_GET['login_success']) && $_GET['login_success'] == '1'): ?>
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="fas fa-check-circle me-2"></i>
                            <strong>Welcome back!</strong> You have successfully signed in. Your support tickets are now available below.
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                        <?php endif; ?>

                        <!-- Timezone indicator -->
                        <div class="text-center mb-3">
                            <small class="text-muted">
                                <i class="fas fa-clock"></i>
                                Times shown in your timezone: <span id="customer-timezone-display"><?php echo getCustomerTimezone(); ?></span>
                            </small>
                        </div>

                        <!-- add new ticket button and pricing link -->
                        <div class="d-flex justify-content-between align-items-center mb-3 flex-wrap" id="ticket-header-section">
                            <div class="pricing-text-container" style="flex: 1; min-width: 200px;">
                                <?php
                                // Determine which ticket types user doesn't have or has 0 remaining
                                $missingTickets = [];
                                if ($starterRemaining == 0) $missingTickets[] = 'Starter';
                                if ($premiumRemaining == 0) $missingTickets[] = 'Business';
                                if ($ultimateRemaining == 0) $missingTickets[] = 'Ultimate';

                                // Auto-detect environment for URL paths
                                $is_localhost_url = (strpos($_SERVER['HTTP_HOST'], 'localhost') !== false || strpos($_SERVER['HTTP_HOST'], '127.0.0.1') !== false);
                                $url_base = $is_localhost_url ? '/helloit' : '';

                                if (!empty($missingTickets)) {
                                    // User is missing some ticket types
                                    $ticketText = count($missingTickets) == 1 ? $missingTickets[0] . ' tickets' : implode(' and ', $missingTickets) . ' tickets';
                                    echo '<span class="pricing-text" style="color: #6c757d; font-size: 14px; line-height: 1.4;">Need more ' . $ticketText . '? <a href="' . $url_base . '/support-ticket/buy-now" style="color: #473BF0; text-decoration: none;">Click here to browse more tickets</a></span>';
                                } else {
                                    // User has all ticket types (at least 1 of each)
                                    echo '<span class="pricing-text" style="color: #6c757d; font-size: 14px; line-height: 1.4;">Need more tickets? <a href="' . $url_base . '/support-ticket/buy-now" style="color: #473BF0; text-decoration: none;">Click here to browse more tickets</a></span>';
                                }
                                ?>
                            </div>
                            <div class="create-ticket-container" style="flex-shrink: 0;">
                                <a href="create-ticket.php" class="btn create-ticket-btn"
                                    style="background-color: #473BF0; color: white; padding: 0.5em 1em; font-size: 0.9em; border-radius: 5px; text-decoration: none; white-space: nowrap;">Create
                                    New Ticket</a>
                            </div>
                        </div>

                        <style>
                        /* Responsive styles for ticket header section */
                        @media (max-width: 991px) {
                            #ticket-header-section {
                                flex-direction: column !important;
                                align-items: flex-start !important;
                                gap: 15px;
                            }

                            .pricing-text-container {
                                width: 100% !important;
                                flex: none !important;
                                min-width: auto !important;
                            }

                            .create-ticket-container {
                                width: 100% !important;
                                text-align: center;
                            }

                            .create-ticket-btn {
                                width: 100% !important;
                                max-width: 200px;
                            }
                        }

                        @media (max-width: 767px) {
                            .pricing-text {
                                font-size: 13px !important;
                            }

                            #ticket-header-section {
                                gap: 12px;
                            }
                        }

                        @media (max-width: 575px) {
                            .pricing-text {
                                font-size: 12px !important;
                            }

                            .create-ticket-btn {
                                font-size: 0.85em !important;
                                padding: 0.4em 0.8em !important;
                            }
                        }
                        </style>


                        <!-- Remaining Tickets Dashboard -->
                        <div class="row text-center mb-4 ticket-cards">
                            <div class="col-md-4 col-sm-6">
                                <div class="card shadow-sm">
                                    <div class="card-body">
                                        <h5>Remaining Starter Tickets</h5>
                                        <h3><?php echo $starterRemaining; ?></h3>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 col-sm-6">
                                <div class="card shadow-sm">
                                    <div class="card-body">
                                        <h5>Remaining Business Tickets</h5>
                                        <h3><?php echo $premiumRemaining; ?></h3>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 col-sm-12">
                                <div class="card shadow-sm">
                                    <div class="card-body">
                                        <h5>Remaining Ultimate Tickets</h5>
                                        <h3><?php echo $ultimateRemaining; ?></h3>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Ticket Expiration Warning -->
                        <?php
                        include_once('../functions/ticket-expiration-functions.php');

                        // Run automatic cleanup if enabled
                        // TEMPORARILY DISABLED FOR DEBUGGING - Testing if cleanup is interfering
                        // runAutomaticCleanup();

                        $expiring_tickets = getTicketsExpiringSoon($username);
                        if (!empty($expiring_tickets)):
                        ?>
                        <div class="alert alert-warning alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>Ticket Expiration Warning!</strong>
                            You have <?php echo count($expiring_tickets); ?> ticket purchase(s) expiring soon.
                            <a href="my-ticket-log.php" class="alert-link">View details in Ticket Logs</a>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                        <?php endif; ?>

                        <br>

                        <style>
                        .form-control,
                        .btn,
                        .filter-select {
                            font-size: 14px;
                            /* Reduced from 16px */
                            height: 40px !important;
                        }

                        .filter-select {
                            font-size: 14px;
                            height: 40px !important;
                            line-height: 1.5 !important;
                            padding: 8px 12px !important;
                            /* width: 120px !important; */
                        }

                        /* Additional responsive styles for the search form and filters */
                        @media (min-width: 992px) {

                            /* Desktop-specific styles */
                            .search-filter-row {
                                justify-content: flex-start !important;
                                /* Left align on desktop */
                                /* padding-left: 20px; */
                                margin-left: -10px;
                                /* Add some padding on the left */
                            }

                            .search-box .input-group {
                                margin-left: 0;
                                /* Ensure left alignment on desktop */
                            }

                            /* Filter row styles for desktop */
                            .filter-row-container {
                                margin-top: 15px;
                                margin-bottom: 20px;
                            }

                            .filter-item {
                                padding: 0 5px;
                            }

                            .filter-select {
                                width: 100% !important;
                                border-radius: 4px;
                            }
                        }

                        @media (max-width: 576px) {
                            .search-filter-row {
                                margin: 0 -10px;
                            }

                            h2 {
                                font-size: 1.5rem;
                            }
                        }

                        .filter-container select {
                            width: auto !important;
                            padding: 4px 8px;
                            /* Smaller padding */
                            height: 40px !important;
                            /* Allow height to adjust to content */
                        }

                        /* Removed search-container styles as they're now handled by search-box */

                        .d-flex.flex-wrap.gap-2 {
                            gap: 10px;
                        }

                        @media (max-width: 767px) {
                            .filter-container select {
                                width: 46% !important;
                                /* Slightly smaller on mobile */
                                font-size: 13px;
                                /* Even smaller font on mobile */
                            }
                        }

                        /* Search bar styles from my-ticket-log.php - modified for left alignment */
                        .search-filter-row {
                            padding: 0 40px;
                        }

                        .search-box {
                            width: auto;
                        }

                        .search-box .input-group {
                            width: 550px !important;
                            max-width: 100% !important;
                            margin-left: 0;
                            /* Align to the left */
                        }

                        .search-input {
                            border-top-right-radius: 0 !important;
                            border-bottom-right-radius: 0 !important;
                            height: 38px;
                            font-size: 14px;
                        }

                        .search-button {
                            border-top-right-radius: 4px !important;
                            border-bottom-right-radius: 4px !important;
                            border-top-left-radius: 0 !important;
                            border-bottom-left-radius: 0 !important;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            padding: 0.375rem 0.75rem;
                            width: 40px !important;
                            min-width: 40px !important;
                        }

                        /* Hide the word "Search" on smaller screens */
                        @media (max-width: 767px) {
                            .search-button span {
                                display: none;
                            }

                            .search-button i {
                                margin-right: 0;
                            }

                            .search-filter-row {
                                padding: 0 15px;
                            }

                            .search-box .input-group {
                                width: 100% !important;
                                max-width: 100% !important;
                                margin: 0 auto;
                                /* Center on mobile */
                            }

                            .search-filter-row {
                                justify-content: center !important;
                                /* Center on mobile */
                            }

                            .search-input {
                                font-size: 16px;
                                /* Larger font size for better mobile readability */
                            }
                        }

                        /* Extra small devices */
                        @media (max-width: 480px) {
                            .search-filter-row {
                                padding: 0 10px;
                            }

                            .search-input::placeholder {
                                font-size: 14px;
                            }

                            ul.pagination.justify-content-center {
                                font-size: 14px !important;
                            }
                        }

                        .filter-container select {
                            height: 38px;
                            font-size: 14px;
                            min-width: 120px;
                            margin-right: 8px;
                            margin-bottom: 8px;
                        }

                        /* Corner styling for filter selects */
                        .filter-select.top-left {
                            border-top-left-radius: 4px;
                            border-top-right-radius: 0;
                            border-bottom-left-radius: 0;
                            border-bottom-right-radius: 0;
                        }

                        .filter-select.top-right {
                            border-top-left-radius: 0;
                            border-top-right-radius: 4px;
                            border-bottom-left-radius: 0;
                            border-bottom-right-radius: 0;
                        }

                        .filter-select.bottom-left {
                            border-top-left-radius: 0;
                            border-top-right-radius: 0;
                            border-bottom-left-radius: 4px;
                            border-bottom-right-radius: 0;
                        }

                        .filter-select.bottom-right {
                            border-top-left-radius: 0;
                            border-top-right-radius: 0;
                            border-bottom-left-radius: 0;
                            border-bottom-right-radius: 4px;
                        }


                        /* Responsive adjustments */
                        @media (min-width: 768px) and (max-width: 991px) {

                            /* Tablet-specific styles */
                            .search-filter-row {
                                justify-content: flex-start !important;
                                /* Left align on tablets */
                            }

                            .search-box .input-group {
                                margin-left: 0;
                                /* Ensure left alignment on tablets */
                                width: 450px !important;
                                /* Slightly smaller on tablets */
                            }

                            /* Filter row styles for tablets */
                            .filter-row-container {
                                margin-top: 15px;
                                margin-bottom: 20px;
                            }

                            .filter-item {
                                padding: 0 5px;
                            }

                            .filter-select {
                                width: 100% !important;
                                border-radius: 4px;
                                font-size: 13px;
                            }
                        }

                        @media (max-width: 767px) {
                            .filter-container {
                                padding-left: 15px !important;
                                padding-right: 15px !important;
                            }

                            /* Mobile styles for filter row */
                            .filter-row-container .row {
                                margin: 0;
                            }

                            .filter-item {
                                margin-bottom: 10px;
                            }

                            .filter-select {
                                width: 100% !important;
                                font-size: 13px;
                                border-radius: 4px !important;
                            }
                        }

                        @media (max-width: 575px) {
                            .filter-row-container {
                                padding: 0 10px;
                            }

                            .filter-select {
                                font-size: 12px;
                            }
                        }

                        /* Table styles */
                        .table td {
                            vertical-align: middle;
                        }

                        /* Subject cell styles */
                        td:nth-child(3) {
                            text-align: left !important;
                            padding-left: 15px !important;
                            max-width: 40%;
                            white-space: normal;
                        }

                        .subject-content {
                            overflow: hidden;
                            text-overflow: ellipsis;
                            max-height: 2.4em;
                            line-height: 1.2;
                            display: -webkit-box;
                            -webkit-line-clamp: 2;
                            -webkit-box-orient: vertical;
                        }

                        /* Details button styles */
                        td:nth-child(8) .btn {
                            padding: 0.15rem 0.4rem;
                            font-size: 11px;
                            min-width: 40px;
                            max-width: 40px;
                            height: 24px;
                            line-height: 1;
                        }

                        /* Mobile styles for table */
                        @media (max-width: 767px) {
                            td:nth-child(3) {
                                max-width: 45%;
                            }

                            td:nth-child(8) .btn {
                                padding: 0.1rem 0.3rem;
                                font-size: 10px;
                                min-width: 35px;
                                max-width: 35px;
                                height: 22px;
                            }
                        }
                        </style>

                        <!-- Search form and filters -->
                        <form method="GET">
                            <div class="mb-3" style="padding: 0 0px align-items-center;">
                                <!-- Search bar -->
                                <div class="mb-3">
                                    <div class="search-filter-row d-flex justify-content-start flex-wrap">
                                        <div class="search-box w-100">
                                            <div class="input-group">
                                                <input type="text" name="search" class="form-control search-input"
                                                    placeholder="Search by ID, subject or date..."
                                                    value="<?php echo htmlspecialchars($search); ?>">
                                                <div class="input-group-append">
                                                    <button type="submit" class="btn btn-primary search-button">
                                                        <i class="fas fa-search"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Filters row -->
                                <div class="filter-row-container">
                                    <div class="row mx-auto" style="max-width: 95%;">
                                        <!-- Filter 1 -->
                                        <div class="col-md-3 filter-item">
                                            <select name="type" class="form-control filter-select"
                                                onchange="this.form.submit()">
                                                <option value="">All Types</option>
                                                <option value="starter"
                                                    <?php echo $typeFilter === 'starter' ? 'selected' : ''; ?>>Starter
                                                </option>
                                                <option value="premium"
                                                    <?php echo $typeFilter === 'premium' ? 'selected' : ''; ?>>Business
                                                </option>
                                                <option value="ultimate"
                                                    <?php echo $typeFilter === 'ultimate' ? 'selected' : ''; ?>>Ultimate
                                                </option>
                                            </select>
                                        </div>

                                        <!-- Filter 2 -->
                                        <div class="col-md-3 filter-item">
                                            <select name="status" class="form-control filter-select"
                                                onchange="this.form.submit()">
                                                <option value="">All Statuses</option>
                                                <option value="open"
                                                    <?php echo $statusFilter === 'open' ? 'selected' : ''; ?>>Open
                                                </option>
                                                <option value="wip"
                                                    <?php echo $statusFilter === 'wip' ? 'selected' : ''; ?>>WIP
                                                </option>
                                                <option value="solved"
                                                    <?php echo $statusFilter === 'solved' ? 'selected' : ''; ?>>Solved
                                                </option>
                                                <option value="closed"
                                                    <?php echo $statusFilter === 'closed' ? 'selected' : ''; ?>>Closed
                                                </option>
                                            </select>
                                        </div>

                                        <!-- Filter 3 - Rating Status -->
                                        <div class="col-md-3 filter-item">
                                            <select name="rating" class="form-control filter-select"
                                                onchange="this.form.submit()">
                                                <option value="">All Rating Status</option>
                                                <option value="rated"
                                                    <?php echo $ratingFilter === 'rated' ? 'selected' : ''; ?>>
                                                    Already Rated
                                                </option>
                                                <option value="not_rated"
                                                    <?php echo $ratingFilter === 'not_rated' ? 'selected' : ''; ?>>
                                                    Not Rated Yet
                                                </option>
                                            </select>
                                        </div>

                                        <!-- Empty column to maintain layout -->
                                        <div class="col-md-3 filter-item">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>


                        <!-- Show API Error if any -->
                        <?php if ($apiError): ?>
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle"></i>
                            <strong>API Error:</strong> <?php echo htmlspecialchars($apiError); ?>
                            <br><small>Showing tickets from Appika API. If you continue to see this error, please contact support.</small>
                        </div>
                        <?php endif; ?>

                        <!-- Check if we have tickets to display -->
                        <?php if ($totalItems > 0): ?>
                        <div class="table-responsive">
                            <table class="table table-bordered table-hover table-striped text-center mx-auto"
                                style="max-width: 95%;">
                                <thead class="thead-light">
                                    <tr>
                                        <th style="width: 12%;">Created</th>
                                        <th style="width: 8%;">ID</th>
                                        <th style="width: 50%;">Subject</th>
                                        <th style="width: 10%;">Type</th>
                                        <th style="width: 10%;">Status</th>
                                        <th style="width: 10%;">Rating</th>
                                        <th style="width: 10%;">Details</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($tickets as $ticket): ?>
                                    <?php
                                        // Map Appika types to local types
                                        $typeMapping = [
                                            1 => 'starter',
                                            2 => 'business',  // Changed from 'premium' to 'business'
                                            3 => 'ultimate'
                                        ];
                                        $ticketType = $typeMapping[$ticket['type']] ?? 'starter';
                                        $badgeClass = $ticketType;
                                        $displayText = ucfirst($ticketType);

                                        // No need for special case anymore since we map directly to 'business'
                                    ?>
                                    <tr>
                                        <td><?php echo showCustomerTimeSimple($ticket['created']); ?></td>
                                        <td><?php echo $ticket['id']; ?></td>
                                        <td>
                                            <div class="subject-content">
                                                <?php echo htmlspecialchars($ticket['subject']); ?>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge badge-<?php echo $badgeClass; ?>">
                                                <?php echo $displayText; ?>
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge badge-<?php echo strtolower($ticket['status']); ?>">
                                                <?php echo ucfirst(strtolower($ticket['status'])); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <?php
                                            $isAppikaTicket = isset($ticket['type']) && !isset($ticket['ticket_type']); // crude check, adjust if needed
                                            $userRating = null;
                                            if ($isAppikaTicket) {
                                                $appika_ticket_id = $ticket['id'];
                                                $ratingQuery = "SELECT * FROM ticket_ratings WHERE appika_ticket_id = $appika_ticket_id AND user_id = $userId AND is_appika_ticket = 1";
                                                $ratingResult = mysqli_query($conn, $ratingQuery);
                                                $userRating = mysqli_fetch_assoc($ratingResult);
                                            } else {
                                                $local_ticket_id = $ticket['id'];
                                                $ratingQuery = "SELECT * FROM ticket_ratings WHERE ticket_id = $local_ticket_id AND user_id = $userId AND (is_appika_ticket = 0 OR is_appika_ticket IS NULL)";
                                                $ratingResult = mysqli_query($conn, $ratingQuery);
                                                $userRating = mysqli_fetch_assoc($ratingResult);
                                            }
                                            if (in_array(strtolower($ticket['status']), ['resolved', 'closed'])):
                                                if ($userRating): ?>
                                                    <span class="text-success" style="font-size: 14px;">
                                                        <?php for ($i = 1; $i <= 5; $i++): ?>
                                                            <i class="fas fa-star<?php echo $i <= $userRating['rating'] ? '' : ' text-muted'; ?>"></i>
                                                        <?php endfor; ?>
                                                        (<?php echo $userRating['rating']; ?>/5)
                                                    </span>
                                                <?php else: ?>
                                                    <a href="rate-ticket.php?<?php echo $isAppikaTicket ? 'appika_id=' . $ticket['id'] : 'id=' . $ticket['id']; ?>&from=my-ticket" class="btn btn-warning btn-sm">
                                                        <i class="fas fa-star"></i> Rate
                                                    </a>
                                                <?php endif; ?>
                                            <?php else: ?>
                                                <span class="text-muted" style="font-size: 14px;">Can't rate yet</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="ticket-detail.php?ticket_id=<?php echo $ticket['id']; ?>"
                                                    class="btn btn-sm btn-outline-primary details-btn">View</a>
                                                <a href="ticket-chat-view.php?ticket_id=<?php echo $ticket['id']; ?>"
                                                    class="btn btn-sm btn-success">
                                                    <i class="fas fa-comments"></i>&nbsp;Message
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>

                            <!-- Pagination Controls -->
                            <?php if ($totalPages > 1): ?>
                            <div class="pagination-container mt-4">
                                <nav aria-label="Page navigation">
                                    <ul class="pagination justify-content-center">
                                        <!-- Previous Button -->
                                        <li class="page-item <?php echo ($page <= 1) ? 'disabled' : ''; ?>">
                                            <a class="page-link"
                                                href="?page=<?php echo $page - 1; ?><?php echo !empty($search) ? '&search=' . urlencode($search) : ''; ?><?php echo !empty($typeFilter) ? '&type=' . urlencode($typeFilter) : ''; ?><?php echo !empty($statusFilter) ? '&status=' . urlencode($statusFilter) : ''; ?><?php echo !empty($ratingFilter) ? '&rating=' . urlencode($ratingFilter) : ''; ?>"
                                                aria-label="Previous">
                                                <span aria-hidden="true">&laquo; Previous</span>
                                            </a>
                                        </li>

                                        <!-- Smart Page Numbers -->
                                        <?php foreach ($paginationPages as $pageNum): ?>
                                            <?php if ($pageNum === '...'): ?>
                                                <li class="page-item disabled">
                                                    <span class="page-link">...</span>
                                                </li>
                                            <?php else: ?>
                                                <li class="page-item <?php echo ($page == $pageNum) ? 'active' : ''; ?>">
                                                    <a class="page-link"
                                                        href="?page=<?php echo $pageNum; ?><?php echo !empty($search) ? '&search=' . urlencode($search) : ''; ?><?php echo !empty($typeFilter) ? '&type=' . urlencode($typeFilter) : ''; ?><?php echo !empty($statusFilter) ? '&status=' . urlencode($statusFilter) : ''; ?><?php echo !empty($ratingFilter) ? '&rating=' . urlencode($ratingFilter) : ''; ?>">
                                                        <?php echo $pageNum; ?>
                                                    </a>
                                                </li>
                                            <?php endif; ?>
                                        <?php endforeach; ?>

                                        <!-- Next Button -->
                                        <li class="page-item <?php echo ($page >= $totalPages) ? 'disabled' : ''; ?>">
                                            <a class="page-link"
                                                href="?page=<?php echo $page + 1; ?><?php echo !empty($search) ? '&search=' . urlencode($search) : ''; ?><?php echo !empty($typeFilter) ? '&type=' . urlencode($typeFilter) : ''; ?><?php echo !empty($statusFilter) ? '&status=' . urlencode($statusFilter) : ''; ?><?php echo !empty($ratingFilter) ? '&rating=' . urlencode($ratingFilter) : ''; ?>"
                                                aria-label="Next">
                                                <span aria-hidden="true">Next &raquo;</span>
                                            </a>
                                        </li>
                                    </ul>
                                </nav>
                            </div>
                            <?php endif; ?>
                        </div>
                        <?php else : ?>
                        <div class="text-center mt-5">
                            <?php if (!empty($search) || !empty($typeFilter) || !empty($statusFilter) || !empty($ratingFilter)) : ?>
                            <p class="mb-3">No support tickets found with the current filters.</p>
                            <p><a href="my-ticket.php" class="btn btn-outline-primary">Clear all filters</a></p>
                            <?php else : ?>
                            <p>No support tickets found.</p>
                            <?php endif; ?>
                        </div>
                        <?php endif; ?>


                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="../plugins/jquery/jquery.min.js"></script>
    <script src="../js/vendor.min.js"></script>
    <script src="../plugins/theme-mode-switcher/gr-theme-mode-switcher.js"></script>

    <!-- At the very end of the file, just before the closing </body> tag -->
    <script>
    // Direct initialization for the user menu on my-ticket.php
    (function() {
        // Function to initialize the menu
        function initUserMenu() {
            // Get menu elements - use more specific selectors
            var menuTitles = document.querySelectorAll('.dashboard-menu .menu-title');

            if (menuTitles.length === 0) {
                return;
            }

            // Setup each menu (there might be multiple on mobile/desktop)
            menuTitles.forEach(function(menuTitle) {
                var menuContainer = menuTitle.closest('.dashboard-menu');
                if (!menuContainer) return;

                var menuToggle = menuContainer.querySelector('.mobile-menu-toggle');
                var menuItems = menuContainer.querySelector('.menu-items');

                if (!menuToggle || !menuItems) {
                    return;
                }

                // Toggle function
                function toggleMenu(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    menuItems.classList.toggle('expanded');
                    menuToggle.classList.toggle('active');
                }

                // Remove existing handler and add new one
                menuTitle.onclick = null;
                menuTitle.onclick = toggleMenu;

                // Check for active items
                var hasActiveItem = menuItems.querySelector('.active');
                if (hasActiveItem && window.innerWidth <= 767) {
                    menuItems.classList.add('expanded');
                    menuToggle.classList.add('active');
                }
            });
        }

        // Run immediately
        initUserMenu();

        // Also run when DOM is fully loaded
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', initUserMenu);
        }

        // Run again after delays to catch any late rendering
        setTimeout(initUserMenu, 100);
        setTimeout(initUserMenu, 500);
        setTimeout(initUserMenu, 1000);

        // Try to use the global function if available
        if (window.initUserMenu) {
            setTimeout(window.initUserMenu, 200);
            setTimeout(window.initUserMenu, 600);
        }
    })();
    </script>

    <!-- Customer Timezone Detection -->
    <script src="../js/customer-timezone.js"></script>

    <!-- Update timezone display when detected -->
    <script>
    document.addEventListener('customerTimezoneDetected', function(event) {
        const timezoneDisplay = document.getElementById('customer-timezone-display');
        if (timezoneDisplay) {
            timezoneDisplay.textContent = event.detail.timezone;
        }
    });

    // Also update on page load if timezone is already available
    document.addEventListener('DOMContentLoaded', function() {
        setTimeout(function() {
            if (window.CustomerTimezone) {
                const timezone = window.CustomerTimezone.getCustomerTimezone();
                const timezoneDisplay = document.getElementById('customer-timezone-display');
                if (timezoneDisplay && timezone) {
                    timezoneDisplay.textContent = timezone;
                }
            }
        }, 1000);
    });
    </script>

    <!-- Auto-hide login success message -->
    <script>
    // Auto-update ticket counts and warnings every 5 seconds
    function updateTicketCounts() {
        fetch('get-ticket-counts.php', {
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Update ticket count displays
                document.querySelector('.col-md-4:nth-child(1) h3').textContent = data.starter_tickets;
                document.querySelector('.col-md-4:nth-child(2) h3').textContent = data.premium_tickets;
                document.querySelector('.col-md-4:nth-child(3) h3').textContent = data.ultimate_tickets;

                // Update expiration warning
                const existingWarning = document.querySelector('.alert-warning');
                if (data.expiring_tickets_count > 0) {
                    if (!existingWarning) {
                        // Create warning if it doesn't exist
                        const warningHtml = `
                            <div class="alert alert-warning alert-dismissible fade show" role="alert">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <strong>Ticket Expiration Warning!</strong>
                                You have ${data.expiring_tickets_count} ticket purchase(s) expiring soon.
                                <a href="my-ticket-log.php" class="alert-link">View details in Ticket Logs</a>
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                        `;
                        const ticketCards = document.querySelector('.ticket-cards');
                        ticketCards.insertAdjacentHTML('afterend', warningHtml);
                    } else {
                        // Update existing warning count
                        existingWarning.innerHTML = `
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>Ticket Expiration Warning!</strong>
                            You have ${data.expiring_tickets_count} ticket purchase(s) expiring soon.
                            <a href="my-ticket-log.php" class="alert-link">View details in Ticket Logs</a>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        `;
                    }
                } else if (existingWarning) {
                    // Remove warning if no expiring tickets
                    existingWarning.remove();
                }
            }
        })
        .catch(error => console.error('Error updating ticket counts:', error));
    }

    document.addEventListener('DOMContentLoaded', function() {
        // Auto-hide login success alert after 5 seconds
        const loginSuccessAlert = document.querySelector('.alert-success[role="alert"]');
        if (loginSuccessAlert) {
            setTimeout(function() {
                // Use Bootstrap's fade out animation
                loginSuccessAlert.classList.remove('show');
                // Remove from DOM after animation completes
                setTimeout(function() {
                    if (loginSuccessAlert.parentNode) {
                        loginSuccessAlert.parentNode.removeChild(loginSuccessAlert);
                    }
                }, 150); // Bootstrap fade animation duration
            }, 5000); // 5 seconds
        }

        // Start auto-update for ticket counts
        updateTicketCounts(); // Update immediately
        setInterval(updateTicketCounts, 5000); // Then update every 5 seconds
    });
    </script>

</body>

</html>