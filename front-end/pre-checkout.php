<?php
// Auto-detect environment for file paths
$is_localhost = (strpos($_SERVER['HTTP_HOST'], 'localhost') !== false || strpos($_SERVER['HTTP_HOST'], '127.0.0.1') !== false);
$file_base_path = $is_localhost ? '/helloit' : '';

require_once $_SERVER['DOCUMENT_ROOT'] . $file_base_path . '/vendor/autoload.php';
include($_SERVER['DOCUMENT_ROOT'] . $file_base_path . '/functions/server.php');
include($_SERVER['DOCUMENT_ROOT'] . $file_base_path . '/functions/maintenance-check.php');
session_start();

// Check server status for account creation functionality
$server_status = getMaintenanceStatus();

// Handle logout request
if (isset($_GET['logout'])) {
    // Preserve guest cart and other important session data before logout
    $guest_cart = isset($_SESSION['guest_cart']) ? $_SESSION['guest_cart'] : null;
    $admin_username = isset($_SESSION['admin_username']) ? $_SESSION['admin_username'] : null;
    $admin_id = isset($_SESSION['admin_id']) ? $_SESSION['admin_id'] : null;
    $admin_role = isset($_SESSION['admin_role']) ? $_SESSION['admin_role'] : null;

    // Only unset front-end user session variables, not admin or cart variables
    unset($_SESSION['username']);
    unset($_SESSION['user_id']);
    unset($_SESSION['success']);
    unset($_SESSION['transferred_cart_hash']); // Clear transfer tracking
    unset($_SESSION['pre_checkout_cart_transferred']);

    // If no admin session exists, destroy and recreate session to preserve cart
    if (!$admin_username) {
        session_destroy();
        session_start();

        // Restore guest cart after session recreation
        if ($guest_cart) {
            $_SESSION['guest_cart'] = $guest_cart;
        }
    }

    // Redirect to the same page without logout parameter to refresh the page
    $redirect_url = strtok($_SERVER["REQUEST_URI"], '?'); // Remove query parameters
    header("Location: " . $redirect_url);
    exit();
}

// Auto-detect environment for URL paths
$is_localhost_url = (strpos($_SERVER['HTTP_HOST'], 'localhost') !== false || strpos($_SERVER['HTTP_HOST'], '127.0.0.1') !== false);
$base_path = $is_localhost_url ? '/helloit' : '';
$url_base = $is_localhost_url ? '/helloit' : '';

// Note: Removed automatic redirect for logged-in users
// Logged-in users should be able to access pre-checkout page directly
// and see their payment methods without being redirected to cart.php

// Get cart information for display (matching cart.php logic)
$user_id = isset($_SESSION['user_id']) ? $_SESSION['user_id'] : null;

// Get user email if logged in
$user_email = '';
if ($user_id) {
    $email_query = "SELECT email FROM user WHERE id = ?";
    $email_stmt = $conn->prepare($email_query);
    $email_stmt->bind_param("i", $user_id);
    $email_stmt->execute();
    $email_result = $email_stmt->get_result();
    if ($email_row = $email_result->fetch_assoc()) {
        $user_email = $email_row['email'];
    }
    $email_stmt->close();
}

// Pre-checkout cart logic: Always prioritize guest cart if it exists
// This preserves the original checkout session even after login
if (isset($_SESSION['guest_cart']) && !empty($_SESSION['guest_cart'])) {
    // Use guest cart (original checkout session) - even if user is logged in
    $cart_items = $_SESSION['guest_cart'];
    $using_guest_cart = true;
} elseif ($user_id) {
    // Only use database cart if no guest cart exists
    $query = "SELECT c.cart_id, ci.id AS cart_item_id, ci.ticket_id, ci.quantity, t.ticket_type, t.dollar_price_per_package
              FROM cart c
              JOIN cart_items ci ON c.cart_id = ci.cart_id
              JOIN tickets t ON ci.ticket_id = t.ticketid
              WHERE c.user_id = ? AND c.status = 'active'";

    $stmt = $conn->prepare($query);
    $stmt->bind_param("i", $user_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $cart_items = $result->fetch_all(MYSQLI_ASSOC);
    $using_guest_cart = false;
} else {
    // No user and no guest cart
    $cart_items = [];
    $using_guest_cart = false;
}

// Calculate cart total and item count
$cart_total = 0;
$cart_items_count = 0;

if (!empty($cart_items)) {
    foreach ($cart_items as $item) {
        $quantity = isset($item['quantity']) ? (int)$item['quantity'] : 0;
        $price = isset($item['dollar_price_per_package']) ? (float)$item['dollar_price_per_package'] : 0.00;
        $cart_total += $quantity * $price;
        $cart_items_count += $quantity;
    }
}

// If no items in cart, redirect to cart page
if ($cart_items_count == 0) {
    header("Location: " . $url_base . "/front-end/cart.php");
    exit();
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Checkout - HelloIT</title>
    <link rel="icon" type="image/webp" href="<?php echo $base_path; ?>/image/wp/HelloIT-new.webp">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .checkout-container {
            max-width: 1200px;
            margin: 50px auto;
            padding: 20px;
        }

        .checkout-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .checkout-header {
            background: linear-gradient(135deg, #6754e2 0%, #5344c9 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .checkout-header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: 600;
        }

        .checkout-header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
            font-size: 16px;
        }

        .checkout-body {
            padding: 40px;
        }

        /* Horizontal Layout for Desktop */
        .checkout-content {
            display: flex;
            gap: 40px;
            align-items: flex-start;
        }

        .left-column {
            flex: 1;
            min-width: 0;
        }

        .right-column {
            flex: 1;
            min-width: 0;
        }

        .order-summary {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 30px;
            border: 1px solid #e9ecef;
        }

        /* Force mobile styles for screens smaller than 768px */
        @media screen and (max-width: 767px) {
            .order-summary {
                width: 350px !important;
                max-width: 100% !important;
                padding: 15px !important;
                margin-bottom: 20px !important;
                border-radius: 8px !important;
                margin-left: auto !important;
                margin-right: auto !important;
            }

            .order-summary h3 {
                font-size: 16px !important;
                margin-bottom: 12px !important;
            }

            .order-item {
                padding: 8px 0 !important;
                font-size: 14px !important;
            }

            .order-item:last-child {
                font-size: 16px !important;
                font-weight: 600 !important;
            }
        }

        /* Force mobile styles for screens smaller than 576px */
        @media screen and (max-width: 575px) {
            .order-summary {
                width: 350px !important;
                max-width: calc(100vw - 20px) !important;
                padding: 12px !important;
                margin-bottom: 15px !important;
                border-radius: 6px !important;
                margin-left: auto !important;
                margin-right: auto !important;
            }

            .order-summary h3 {
                font-size: 15px !important;
                margin-bottom: 10px !important;
            }

            .order-summary h3 i {
                font-size: 14px !important;
            }

            .order-item {
                padding: 6px 0 !important;
                font-size: 13px !important;
                line-height: 1.4 !important;
            }

            .order-item:last-child {
                padding: 10px 0 !important;
                font-size: 15px !important;
                font-weight: 600 !important;
                border-top: 2px solid #6754e2 !important;
            }

            /* Enhanced button styling for small mobile */
            .already-account-form > div:last-child {
                justify-content: center !important;
                gap: 12px !important;
                flex-wrap: nowrap !important;
            }

            .already-account-login-btn,
            .already-account-clear-btn {
                flex: 1 !important;
                max-width: 140px !important;
                min-width: 120px !important;
                padding: 12px 15px !important;
                font-size: 14px !important;
                text-align: center !important;
            }
        }

        /* Force mobile styles for very small screens */
        @media screen and (max-width: 375px) {
            .order-summary {
                width: 320px !important;
                max-width: calc(100vw - 20px) !important;
                padding: 10px !important;
                margin-bottom: 12px !important;
                border-radius: 6px !important;
                margin-left: auto !important;
                margin-right: auto !important;
            }

            .order-summary h3 {
                font-size: 14px !important;
                margin-bottom: 8px !important;
            }

            .order-item {
                padding: 5px 0 !important;
                font-size: 12px !important;
            }

            .order-item:last-child {
                padding: 8px 0 !important;
                font-size: 14px !important;
                font-weight: 600 !important;
                border-top: 2px solid #6754e2 !important;
            }

            /* Button styling for very small screens */
            .already-account-form > div:last-child {
                justify-content: center !important;
                gap: 10px !important;
                flex-wrap: nowrap !important;
            }

            .already-account-login-btn,
            .already-account-clear-btn {
                flex: 1 !important;
                max-width: 130px !important;
                min-width: 110px !important;
                padding: 10px 12px !important;
                font-size: 13px !important;
                text-align: center !important;
            }
        }

        .order-summary h3 {
            color: #495057;
            font-size: 18px;
            margin-bottom: 15px;
            font-weight: 600;
        }

        .order-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #dee2e6;
        }

        .order-item:last-child {
            border-bottom: none;
            font-weight: 600;
            font-size: 18px;
            color: #6754e2;
            margin-top: 10px;
            padding-top: 15px;
            border-top: 2px solid #6754e2;
        }

        .email-section {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 25px;
            border: 1px solid #e9ecef;
            margin-bottom: 25px;
        }

        .email-section h4 {
            color: #495057;
            font-size: 20px;
            margin-bottom: 15px;
            font-weight: 600;
        }

        .email-section p {
            color: #6c757d;
            margin-bottom: 20px;
            line-height: 1.6;
        }

        .email-input-group {
            display: flex;
            gap: 0;
            margin-bottom: 15px;
        }

        .email-input {
            flex: 1;
            padding: 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px 0 0 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        .email-input:focus {
            outline: none;
            border-color: #6754e2;
        }

        .check-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 15px 25px;
            border-radius: 0 8px 8px 0;
            font-weight: 600;
            cursor: pointer;
            transition: background-color 0.3s;
        }

        .check-btn:hover {
            background: #0056b3;
        }

        .check-btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }

        .email-help {
            font-size: 14px;
            color: #6c757d;
            margin-bottom: 0;
        }

        .checkout-options {
            display: none;
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #dee2e6;
        }

        .save-payment-option {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }

        .save-payment-option input[type="checkbox"] {
            margin-right: 12px;
            transform: scale(1.2);
        }

        .save-payment-option label {
            margin: 0;
            font-weight: 500;
            color: #495057;
            cursor: pointer;
        }

        .save-payment-note {
            font-size: 12px;
            color: #6c757d;
            margin-top: 5px;
            margin-left: 28px;
        }

        .proceed-btn {
            width: 100%;
            background: linear-gradient(135deg, #6754e2 0%, #5344c9 100%);
            color: white;
            border: none;
            padding: 15px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s, box-shadow 0.2s;
        }

        .proceed-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(103, 84, 226, 0.3);
        }

        .cancel-btn {
            width: 100%;
            background: transparent;
            color: #6c757d;
            border: 2px solid #dee2e6;
            padding: 12px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            margin-top: 10px;
        }

        .cancel-btn:hover {
            background: #f8f9fa;
            border-color: #adb5bd;
            color: #495057;
        }

        .back-link {
            display: inline-flex;
            align-items: center;
            color: #6754e2;
            text-decoration: none;
            font-weight: 500;
            margin-bottom: 20px;
            transition: color 0.3s;
        }

        .back-link:hover {
            color: #5344c9;
        }

        .back-link i {
            margin-right: 8px;
        }

        .alert {
            border-radius: 8px;
            margin-top: 15px;
        }

        /* Already Have Account Section */
        .already-have-account-section {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 25px;
            border: 1px solid #e9ecef;
            margin-bottom: 25px;
        }

        .already-have-account-section h4 {
            color: #495057;
            font-size: 20px;
            margin-bottom: 15px;
            font-weight: 600;
        }

        .already-have-account-section p {
            color: #6c757d;
            margin-bottom: 20px;
            line-height: 1.6;
        }

        .already-account-form {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .already-account-input-group {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .already-account-input {
            padding: 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
            width: 100%;
            min-height: 50px;
        }

        .already-account-input:focus {
            outline: none;
            border-color: #6754e2;
            box-shadow: 0 0 0 3px rgba(103, 84, 226, 0.1);
        }

        .already-account-input:disabled {
            background-color: #f8f9fa;
            color: #6c757d;
            cursor: not-allowed;
        }

        .already-account-password-group {
            position: relative;
            width: 100%;
        }

        .already-account-password-toggle {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: #6c757d;
            cursor: pointer;
            padding: 8px;
            font-size: 16px;
        }

        .already-account-password-toggle:hover {
            color: #495057;
        }

        .already-account-login-btn {
            background: linear-gradient(135deg, #6754e2 0%, #5344c9 100%);
            color: white;
            border: none;
            padding: 15px 25px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s, box-shadow 0.2s;
            min-height: 50px;
        }

        .already-account-login-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(103, 84, 226, 0.3);
        }

        .already-account-login-btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
        }

        .already-account-clear-btn {
            background: #6c757d;
            color: white;
            border: none;
            padding: 15px 25px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: background-color 0.2s;
            align-self: flex-start;
            min-height: 50px;
        }

        .already-account-clear-btn:hover {
            background: #5a6268;
        }

        .already-account-error {
            background: #f8d7da;
            color: #721c24;
            padding: 10px;
            border-radius: 6px;
            margin-top: 10px;
            border: 1px solid #f5c6cb;
            font-size: 14px;
        }

        /* Inline Login Section */
        .inline-login-section {
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #dee2e6;
        }

        .login-form-container h5 {
            color: #495057;
            font-size: 18px;
            margin-bottom: 10px;
            font-weight: 600;
        }

        .login-help {
            color: #6c757d;
            font-size: 14px;
            margin-bottom: 20px;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-label {
            font-weight: 500;
            color: #495057;
            margin-bottom: 5px;
            display: block;
        }

        .form-control {
            width: 100%;
            padding: 12px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s;
        }

        .form-control:focus {
            outline: none;
            border-color: #6754e2;
            box-shadow: 0 0 0 3px rgba(103, 84, 226, 0.1);
        }

        .password-input-group {
            position: relative;
        }

        .password-toggle {
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: #6c757d;
            cursor: pointer;
            padding: 5px;
        }

        .password-toggle:hover {
            color: #495057;
        }

        .login-btn {
            width: 100%;
            background: linear-gradient(135deg, #6754e2 0%, #5344c9 100%);
            color: white;
            border: none;
            padding: 15px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s, box-shadow 0.2s;
        }

        .login-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(103, 84, 226, 0.3);
        }

        .login-btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
        }

        .cancel-btn {
            width: 100%;
            background: #6c757d;
            color: white;
            border: none;
            padding: 15px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s, box-shadow 0.2s;
            margin-top: 10px;
        }

        .cancel-btn:hover {
            background: #5a6268;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(108, 117, 125, 0.3);
        }

        .login-error {
            background: #f8d7da;
            color: #721c24;
            padding: 12px;
            border-radius: 8px;
            margin-top: 15px;
            border: 1px solid #f5c6cb;
        }

        /* Payment Methods Section */
        .payment-methods-section {
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #dee2e6;
        }

        .payment-methods-section h5 {
            color: #495057;
            font-size: 18px;
            margin-bottom: 15px;
            font-weight: 600;
        }

        .payment-method-item {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            cursor: pointer;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .payment-method-item:hover {
            border-color: #6754e2;
            background: #f8f7ff;
        }

        .payment-method-item.selected {
            border-color: #6754e2;
            background: #f8f7ff;
            box-shadow: 0 0 0 3px rgba(103, 84, 226, 0.1);
        }

        .payment-method-info {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .card-icon {
            width: 40px;
            height: 25px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .card-icon img {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
            background: white;
            border-radius: 4px;
            border: 1px solid #e9ecef;
            padding: 2px;
        }

        .card-icon i {
            /* Style for icon-only card icons (like Add New Card) */
            background: none;
            border: none;
            padding: 0;
        }

        .add-card-icon {
            position: relative;
            background: #f8f9fa !important;

        }

        .add-card-icon:hover {
            background: #e7f3ff !important;
            border-color: #5344c9 !important;
        }

        .security-section {
            margin-top: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 12px;
            text-align: center;
            border: 1px solid #e9ecef;
        }

        .payment-logos {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 15px;
            flex-wrap: wrap;
        }

        .payment-logos img {
            height: 30px;
            width: auto;
            max-width: 60px;
            object-fit: contain;
            filter: grayscale(20%);
            transition: filter 0.3s ease;
        }

        .payment-logos img:hover {
            filter: grayscale(0%);
        }

        /* Tablet and Mobile responsive - vertical layout */
        @media (max-width: 768px) {
            .checkout-content {
                flex-direction: column;
                gap: 20px;
            }

            .checkout-container {
                max-width: 600px;
            }

            /* Order summary mobile optimizations */
            .order-summary {
                padding: 15px !important;
                margin-bottom: 20px !important;
                border-radius: 8px !important;
            }

            .order-summary h3 {
                font-size: 16px !important;
                margin-bottom: 12px !important;
            }

            .order-item {
                padding: 8px 0 !important;
                font-size: 14px !important;
            }

            .order-item:last-child {
                font-size: 16px !important;
                font-weight: 600 !important;
            }
        }

        /* Mobile responsive for security section and order summary */
        @media (max-width: 576px) {
            .security-section {
                padding: 15px;
                margin-top: 20px;
            }

            .payment-logos {
                gap: 10px;
            }

            .payment-logos img {
                height: 25px;
                max-width: 50px;
            }

            /* Enhanced order summary for small mobile screens */
            .order-summary {
                padding: 12px !important;
                margin-bottom: 15px !important;
                border-radius: 6px !important;
            }

            .order-summary h3 {
                font-size: 15px !important;
                margin-bottom: 10px !important;
            }

            .order-summary h3 i {
                font-size: 14px !important;
            }

            .order-item {
                padding: 6px 0 !important;
                font-size: 13px !important;
                line-height: 1.4 !important;
            }

            .order-item:last-child {
                padding: 10px 0 !important;
                font-size: 15px !important;
                font-weight: 600 !important;
                border-top: 2px solid #6754e2 !important;
            }

            /* Compact layout for very small screens */
            .checkout-container {
                margin: 10px;
                padding: 10px;
            }

            .checkout-body {
                padding: 15px;
            }
        }

        .card-details {
            display: flex;
            flex-direction: column;
        }

        .card-brand {
            font-weight: 600;
            color: #495057;
            font-size: 14px;
        }

        .card-number {
            color: #6c757d;
            font-size: 13px;
        }

        .card-expiry {
            color: #6c757d;
            font-size: 12px;
        }

        .default-badge {
            background: #28a745;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 11px;
            font-weight: 500;
        }

        .proceed-payment-btn {
            width: 100%;
            background: linear-gradient(135deg, #6754e2 0%, #5344c9 100%);
            color: white;
            border: none;
            padding: 15px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s, box-shadow 0.2s;
            margin-top: 20px;
        }

        .proceed-payment-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(103, 84, 226, 0.3);
        }

        .proceed-payment-btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
        }

        @media (max-width: 768px) {
            .checkout-container {
                margin: 20px auto;
                padding: 15px;
            }

            .checkout-body {
                padding: 25px;
            }

            .email-input-group {
                flex-direction: column;
                gap: 10px;
            }

            .email-input, .check-btn {
                border-radius: 8px;
            }

            .payment-method-item {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }

            .payment-method-info {
                width: 100%;
            }

            /* Already Have Account Section Mobile */
            .already-have-account-section {
                padding: 20px;
                margin-bottom: 20px;
            }

            .already-account-form {
                gap: 15px;
            }

            .already-account-input-group {
                gap: 12px;
            }

            .already-account-input {
                padding: 15px;
                font-size: 16px; /* Prevent zoom on iOS */
                min-height: 50px;
            }

            .already-account-login-btn {
                padding: 15px 20px;
                font-size: 16px;
                min-height: 50px;
            }

            .already-account-clear-btn {
                padding: 15px 20px;
                font-size: 16px;
                min-height: 50px;
            }

            /* Center and equal width buttons on mobile */
            .already-account-form > div:last-child {
                justify-content: center !important;
                gap: 15px !important;
            }

            .already-account-login-btn,
            .already-account-clear-btn {
                flex: 1 !important;
                max-width: 150px !important;
                text-align: center !important;
            }

            .already-account-password-toggle {
                padding: 10px;
                font-size: 16px;
            }

            /* Order summary mobile enhancements */
            .order-summary {
                padding: 16px !important;
                margin-bottom: 18px !important;
            }

            .order-summary h3 {
                font-size: 16px !important;
                margin-bottom: 12px !important;
            }

            .order-item {
                padding: 8px 0 !important;
                font-size: 14px !important;
            }

            .order-item:last-child {
                padding: 12px 0 !important;
                font-size: 16px !important;
                font-weight: 600 !important;
            }
        }

        /* Extra small mobile screens */
        @media (max-width: 320px) {
            .checkout-container {
                margin: 5px;
                padding: 8px;
            }

            .checkout-body {
                padding: 12px;
            }

            .order-summary {
                padding: 10px !important;
                margin-bottom: 12px !important;
            }

            .order-summary h3 {
                font-size: 14px !important;
                margin-bottom: 8px !important;
            }

            .order-item {
                padding: 5px 0 !important;
                font-size: 12px !important;
            }

            .order-item:last-child {
                padding: 8px 0 !important;
                font-size: 14px !important;
                font-weight: 600 !important;
            }
        }
    </style>
</head>
<body>
    <div class="checkout-container">
        <div class="checkout-card">
            <!-- Header -->
            <div class="checkout-header">
                <h1><img src="<?php echo $base_path; ?>/image/wp/HelloIT-new.webp" alt="HelloIT" style="height: 32px; width: auto; margin-right: 12px; vertical-align: middle;">Secure Checkout</h1>
                <p>Complete your purchase securely with HelloIT</p>
            </div>

            <!-- Body -->
            <div class="checkout-body">
                <!-- Back to Cart Link -->
                <?php if ($user_id && isset($using_guest_cart) && $using_guest_cart): ?>
                    <!-- For logged-in users with guest cart: trigger cart merge -->
                    <a href="<?php echo $url_base; ?>/front-end/cart.php?merge_guest_cart=1" class="back-link">
                        <i class="fas fa-arrow-left"></i>Back to Cart
                    </a>
                <?php else: ?>
                    <!-- Regular back to cart link -->
                    <a href="<?php echo $url_base; ?>/front-end/cart.php" class="back-link">
                        <i class="fas fa-arrow-left"></i>Back to Cart
                    </a>
                <?php endif; ?>

                <!-- Horizontal Layout Container -->
                <div class="checkout-content">
                    <!-- Left Column: Order Summary -->
                    <div class="left-column">
                        <div class="order-summary">
                            <h3><i class="fas fa-shopping-cart me-2"></i>Order Summary</h3>
                            <div class="order-item">
                                <span><?php echo $cart_items_count; ?> item(s) in your cart</span>
                                <span>$<?php echo number_format($cart_total, 2); ?></span>
                            </div>
                            <div class="order-item">
                                <span><strong>Total</strong></span>
                                <span><strong>$<?php echo number_format($cart_total, 2); ?></strong></span>
                            </div>
                        </div>
                    </div>

                    <!-- Right Column: Email Verification & Payment -->
                    <div class="right-column">

                        <!-- Already Have Account Section -->
                        <div class="already-have-account-section" id="already-have-account-section">
                            <h4><i class="fas fa-user-check me-2"></i>Already have account?</h4>
                            <p>If you already have an account with us, sign in here for faster checkout.</p>

                            <form class="already-account-form" id="already-account-form">
                                <div class="already-account-input-group">
                                    <input type="email" class="already-account-input" id="already-account-email" placeholder="Enter your email address" required>
                                    <div class="already-account-password-group">
                                        <input type="password" class="already-account-input" id="already-account-password" placeholder="Enter your password" required>
                                        <button type="button" class="already-account-password-toggle" id="already-account-password-toggle">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </div>
                                </div>

                                <div style="display: flex; gap: 10px; align-items: center; flex-wrap: wrap;">
                                    <button type="submit" class="already-account-login-btn" id="already-account-login-btn">
                                        <i class="fas fa-sign-in-alt me-1"></i>Sign In
                                    </button>
                                    <button type="button" class="already-account-clear-btn" id="already-account-clear-btn">
                                        <i class="fas fa-times me-1"></i>Clear
                                    </button>
                                </div>
                            </form>

                            <div id="already-account-error" class="already-account-error" style="display: none;"></div>
                        </div>

                <!-- Email Validation Section -->
                <div class="email-section" <?php echo !$server_status['online'] ? 'style="opacity: 0.5; pointer-events: none;"' : ''; ?>>
                    <?php if (!$server_status['online']): ?>
                        <div class="server-notice" style="background: #f8d7da; color: #721c24; padding: 15px; border-radius: 8px; margin-bottom: 15px; border: 1px solid #f5c6cb;">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>Account Creation Temporarily Unavailable</strong><br>
                            <small>New account registration is currently disabled due to server maintenance. Existing users can still sign in above.</small>
                        </div>
                    <?php endif; ?>

                    <h4><i class="fas fa-envelope me-2"></i>No Account Yet?</h4>
                    <p>Enter your email address for better checkout experience.</p>

                    <div class="email-input-group">
                        <input type="email" class="email-input" id="guest-email" placeholder="Enter your email address" autocomplete="off" required <?php echo !$server_status['online'] ? 'disabled' : ''; ?>>
                        <button type="button" class="check-btn" id="check-email-btn" <?php echo !$server_status['online'] ? 'disabled' : ''; ?>>
                            <i class="fas fa-search me-1"></i>Check
                        </button>
                    </div>

                    <p class="email-help">
                        <i class="fas fa-info-circle me-1"></i>
                        <?php if ($server_status['online']): ?>
                            We'll check if you have an existing account to provide the best checkout experience.
                        <?php else: ?>
                            Account services are temporarily unavailable. Please try again later or contact support.
                        <?php endif; ?>
                    </p>
                    
                    <!-- Email Check Result -->
                    <div id="email-check-result"></div>

                    <!-- Inline Login Section (shown when existing email is found) -->
                    <div class="inline-login-section" id="inline-login-section" style="display: none;">
                        <div class="login-form-container">
                            <h5><i class="fas fa-sign-in-alt me-2"></i>Sign In to Continue</h5>
                            <p class="login-help">Please enter your password to access your account and saved payment methods.</p>

                            <form id="inline-login-form">
                                <div class="form-group mb-3">
                                    <label for="login-email" class="form-label">Email Address</label>
                                    <div class="email-input-group">
                                        <input type="email" class="email-input" id="login-email" placeholder="Enter your email address" required disabled>
                                        <button type="button" class="check-btn" style="display: none;"><i class="fas fa-search me-1"></i>Check</button>
                                    </div>
                                </div>

                                <div class="form-group mb-3">
                                    <label for="login-password" class="form-label">Password</label>
                                    <div class="password-input-group">
                                        <input type="password" class="form-control" id="login-password" placeholder="Enter your password" required>
                                        <button type="button" class="password-toggle" id="password-toggle">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </div>
                                </div>

                                <button type="submit" class="login-btn" id="login-btn">
                                    <i class="fas fa-sign-in-alt me-2"></i>Login & Continue Purchase
                                </button>

                                <button type="button" class="cancel-btn" id="cancel-login-btn">
                                    <i class="fas fa-times me-2"></i>Cancel
                                </button>
                            </form>

                            <div id="login-error" class="login-error" style="display: none;"></div>
                        </div>
                    </div>

                    <!-- Payment Methods Section (shown after successful login) -->
                    <div class="payment-methods-section" id="payment-methods-section" style="display: none;">
                        <h5><i class="fas fa-credit-card me-2"></i>Select Payment Method</h5>
                        <div id="payment-methods-container"></div>

                        <!-- Save Payment Method Checkbox (only shown for new card) -->
                        <div class="save-card-option" id="save-card-option" style="display: none; margin-top: 15px;">
                            <label class="save-card-checkbox" style="display: flex; align-items: center; gap: 10px; cursor: pointer; padding: 12px; background: #f8f9fa; border-radius: 8px; border: 1px solid #e9ecef;">
                                <input type="checkbox" name="save_payment_method" id="save_payment_method" value="1" checked style="width: 18px; height: 18px; accent-color: #6754e2;">
                                <span class="checkbox-text" style="font-size: 14px; color: #495057;">
                                    <i class="fas fa-credit-card me-1" style="color: #6754e2;"></i>
                                    Save my payment method for faster checkout
                                </span>
                            </label>

                        </div>

                        <div class="payment-actions">
                            <button type="button" class="proceed-payment-btn" id="proceed-payment-btn">
                                <i class="fas fa-lock me-2"></i>Pay Now
                            </button>
                        </div>
                        <p class="save-card-note" style="margin: 8px 0 0 28px; font-size: 12px; color: #6c757d;">
                            Your payment information will be securely stored by Stripe for future purchases.
                        </p>
                    </div>

                    <!-- Guest Checkout Options (shown for new emails) -->
                    <div class="checkout-options" id="checkout-options" style="display: none;">
                        <div class="save-payment-option">
                            <input type="checkbox" id="save-payment-checkbox" checked>
                            <label for="save-payment-checkbox">
                                <i class="fas fa-credit-card me-1"></i>
                                Save my payment method for faster checkout
                            </label>
                        </div>


                        <!-- Hidden form for submission -->
                        <form id="checkout-form" action="<?php echo $url_base; ?>/front-end/create-checkout-session.php" method="POST">
                            <input type="hidden" name="save_payment_method" id="save_payment_method_hidden" value="1">
                            <input type="hidden" name="guest_email" id="guest_email_hidden" value="">
                            <input type="hidden" name="source" value="pre_checkout">

                            <button type="submit" class="proceed-btn" id="proceed-btn">
                                <i class="fas fa-lock me-2"></i>Continue Checkout
                            </button>

                            <!-- Cancel/Clear button -->
                            <button type="button" class="cancel-btn" id="cancel-checkout-btn" style="display: none;">
                                <i class="fas fa-times me-2"></i>Cancel & Start Over
                            </button>

                            <div class="save-payment-note">
                                Your payment information will be securely stored by Stripe for future purchases.
                            </div>
                        </form>


                    </div>
                    </div> <!-- End right-column -->
                </div> <!-- End checkout-content -->
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        const basePath = '<?php echo $url_base; ?>';
        let currentUserEmail = '<?php echo htmlspecialchars($user_email); ?>'; // Store current user email

        document.addEventListener('DOMContentLoaded', function() {
            initializeEmailCheck();
            initializeSavePaymentCheckbox();
            initializeInlineLogin();
            initializePasswordToggle();
            initializePaymentMethods();
            initializeCancelButton();
            initializeAlreadyHaveAccount();

            // Check if user is already logged in and load payment methods directly
            checkIfUserLoggedIn();
        });

        function initializeEmailCheck() {
            const emailInput = document.getElementById('guest-email');
            const checkEmailBtn = document.getElementById('check-email-btn');
            const emailResult = document.getElementById('email-check-result');
            const guestEmailHidden = document.getElementById('guest_email_hidden');

            // Handle email check button click
            checkEmailBtn.addEventListener('click', function() {
                const email = emailInput.value.trim();

                if (!email) {
                    showEmailResult('error', 'Please enter your email address.');
                    return;
                }

                if (!isValidEmail(email)) {
                    showEmailResult('error', 'Please enter a valid email address.');
                    return;
                }

                checkEmailBtn.disabled = true;
                checkEmailBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Checking...';

                // Send AJAX request to check email
                const xhr = new XMLHttpRequest();
                xhr.open('POST', basePath + '/front-end/check-email-exists.php', true);
                xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
                xhr.responseType = 'json';

                xhr.onload = function() {
                    checkEmailBtn.disabled = false;
                    checkEmailBtn.innerHTML = '<i class="fas fa-search me-1"></i>Check';

                    if (xhr.status === 200 && xhr.response) {
                        if (xhr.response.exists) {
                            // Email exists - show inline login
                            showEmailResult('success', 'Account found! Please sign in to continue.');
                            showInlineLogin(email);
                        } else {
                            // Email doesn't exist - proceed as guest
                            showEmailResult('success', 'Great! You can proceed as a new customer.');
                            guestEmailHidden.value = email;

                            // Show checkout options
                            const checkoutOptions = document.getElementById('checkout-options');
                            if (checkoutOptions) {
                                checkoutOptions.style.display = 'block';
                            }

                            // Show cancel button
                            const cancelBtn = document.getElementById('cancel-checkout-btn');
                            if (cancelBtn) {
                                cancelBtn.style.display = 'block';
                            }
                        }
                    } else {
                        showEmailResult('error', 'Error checking email. Please try again.');
                    }
                };

                xhr.onerror = function() {
                    checkEmailBtn.disabled = false;
                    checkEmailBtn.innerHTML = '<i class="fas fa-search me-1"></i>Check';
                    showEmailResult('error', 'Connection error. Please try again.');
                };

                xhr.send('email=' + encodeURIComponent(email));
            });

            // Allow Enter key to trigger email check
            emailInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    checkEmailBtn.click();
                }
            });
        }

        function showEmailResult(type, message) {
            const emailResult = document.getElementById('email-check-result');
            let alertClass = 'alert-info';
            let icon = 'fas fa-info-circle';

            switch (type) {
                case 'success':
                    alertClass = 'alert-success';
                    icon = 'fas fa-check-circle';
                    break;
                case 'warning':
                    alertClass = 'alert-warning';
                    icon = 'fas fa-exclamation-triangle';
                    break;
                case 'error':
                    alertClass = 'alert-danger';
                    icon = 'fas fa-exclamation-circle';
                    break;
            }

            const resultHtml = `
                <div class="alert ${alertClass}" id="email-result-alert">
                    <i class="${icon} me-2"></i>${message}
                </div>
            `;

            emailResult.innerHTML = resultHtml;

            // Auto-hide "Account found" message after 3 seconds
            if (type === 'success' && message.includes('Account found')) {
                setTimeout(() => {
                    const alertElement = document.getElementById('email-result-alert');
                    if (alertElement) {
                        alertElement.style.transition = 'opacity 0.5s ease-out';
                        alertElement.style.opacity = '0';
                        setTimeout(() => {
                            emailResult.innerHTML = '';
                        }, 500); // Wait for fade out animation to complete
                    }
                }, 3000); // 3 seconds delay
            }
        }

        function showInlineLogin(email) {
            // Hide email input section
            const emailInput = document.getElementById('guest-email');
            const checkBtn = document.getElementById('check-email-btn');
            emailInput.disabled = true;
            checkBtn.style.display = 'none';

            // Hide the "No Account Yet?" header and description
            const emailSection = document.querySelector('.email-section h4');
            const emailDescription = emailSection ? emailSection.nextElementSibling : null;
            const emailHelp = document.querySelector('.email-help');

            if (emailSection) emailSection.style.display = 'none';
            if (emailDescription) emailDescription.style.display = 'none';
            if (emailHelp) emailHelp.style.display = 'none';

            // Hide already have account section
            const alreadyHaveAccountSection = document.getElementById('already-have-account-section');
            if (alreadyHaveAccountSection) {
                alreadyHaveAccountSection.style.display = 'none';
            }

            // Show inline login section
            const inlineLoginSection = document.getElementById('inline-login-section');
            const loginEmailField = document.getElementById('login-email');

            loginEmailField.value = email;
            inlineLoginSection.style.display = 'block';

            // Focus on password field
            setTimeout(() => {
                document.getElementById('login-password').focus();
            }, 100);
        }

        function isValidEmail(email) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailRegex.test(email);
        }

        function initializeSavePaymentCheckbox() {
            const savePaymentCheckbox = document.getElementById('save-payment-checkbox');
            const savePaymentHidden = document.getElementById('save_payment_method_hidden');

            if (savePaymentCheckbox && savePaymentHidden) {
                // Set initial value based on checkbox state
                savePaymentHidden.value = savePaymentCheckbox.checked ? '1' : '0';

                // Update hidden field when checkbox changes
                savePaymentCheckbox.addEventListener('change', function() {
                    savePaymentHidden.value = this.checked ? '1' : '0';
                    // console.log('Save payment method:', this.checked ? 'Yes' : 'No');
                });
            }
        }

        function initializeInlineLogin() {
            const loginForm = document.getElementById('inline-login-form');
            if (!loginForm) return;

            loginForm.addEventListener('submit', function(e) {
                e.preventDefault();

                const email = document.getElementById('login-email').value;
                const password = document.getElementById('login-password').value;
                const loginBtn = document.getElementById('login-btn');
                const loginError = document.getElementById('login-error');

                if (!password) {
                    showLoginError('Please enter your password.');
                    return;
                }

                // Disable login button and show loading
                loginBtn.disabled = true;
                loginBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Signing In...';
                loginError.style.display = 'none';

                // Send login request using the same parameters as sign-in-db.php expects
                const xhr = new XMLHttpRequest();
                xhr.open('POST', basePath + '/functions/sign-in-db.php', true);
                xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
                // Don't set responseType to 'json' so we can access responseText for debugging

                xhr.onload = function() {
                    loginBtn.disabled = false;
                    loginBtn.innerHTML = '<i class="fas fa-sign-in-alt me-2"></i>Login & Continue Purchase';

                    // console.log('Login response status:', xhr.status);
                    // console.log('Login response text:', xhr.responseText);

                    if (xhr.status === 200) {
                        // Try to parse JSON response
                        let response;
                        try {
                            response = JSON.parse(xhr.responseText);

                            if (response.success) {
                                // Store email in global variable before loading payment methods
                                currentUserEmail = email;
                                // Login successful - load payment methods
                                loadUserPaymentMethods();
                            } else {
                                showLoginError(response.message || 'Invalid email or password.');
                            }
                        } catch (e) {
                            // Check if this is a redirect response (means login was successful but not AJAX)
                            if (xhr.responseText.includes('location:') || xhr.responseText.includes('my-ticket.php')) {
                                loadUserPaymentMethods();
                            } else if (xhr.responseText.trim() === '') {
                                // Empty response might mean successful login but no AJAX response
                                loadUserPaymentMethods();
                            } else {
                                showLoginError('Login system error. Please check your credentials and try again.');
                            }
                        }
                    } else {
                        showLoginError('Server error. Please try again.');
                    }
                };

                xhr.onerror = function() {
                    loginBtn.disabled = false;
                    loginBtn.innerHTML = '<i class="fas fa-sign-in-alt me-2"></i>Login & Continue Purchase';
                    showLoginError('Connection error. Please try again.');
                };

                // Use the same parameter names as the existing sign-in system
                // Note: signin_user triggers the login logic, ajax_login=1 makes it return JSON
                // cart_transfer=1 tells the system to transfer guest cart to user cart
                const formData = 'signin_user=1&username=' + encodeURIComponent(email) + '&password=' + encodeURIComponent(password) + '&ajax_login=1&cart_transfer=1&return_to=pre_checkout';
                // console.log('Sending login request with data:', formData);
                xhr.send(formData);
            });
        }

        function initializePasswordToggle() {
            const passwordToggle = document.getElementById('password-toggle');
            const passwordInput = document.getElementById('login-password');

            if (passwordToggle && passwordInput) {
                passwordToggle.addEventListener('click', function() {
                    const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
                    passwordInput.setAttribute('type', type);

                    const icon = this.querySelector('i');
                    icon.className = type === 'password' ? 'fas fa-eye' : 'fas fa-eye-slash';
                });
            }
        }

        function showLoginError(message) {
            const loginError = document.getElementById('login-error');
            loginError.innerHTML = '<i class="fas fa-exclamation-circle me-2"></i>' + message;
            loginError.style.display = 'block';
        }

        function initializeCancelButton() {
            const cancelBtn = document.getElementById('cancel-login-btn');
            if (!cancelBtn) return;

            cancelBtn.addEventListener('click', function() {
                // Hide the inline login section
                const inlineLoginSection = document.getElementById('inline-login-section');
                inlineLoginSection.style.display = 'none';

                // Show the "No Account Yet?" header and description again
                const emailSection = document.querySelector('.email-section h4');
                const emailDescription = emailSection ? emailSection.nextElementSibling : null;
                const emailHelp = document.querySelector('.email-help');

                if (emailSection) emailSection.style.display = 'block';
                if (emailDescription) emailDescription.style.display = 'block';
                if (emailHelp) emailHelp.style.display = 'block';

                // Reset and re-enable the email input section
                const emailInput = document.getElementById('guest-email');
                const checkBtn = document.getElementById('check-email-btn');
                const emailResult = document.getElementById('email-check-result');

                emailInput.disabled = false;
                emailInput.value = '';
                checkBtn.style.display = 'inline-block';
                emailResult.innerHTML = '';

                // Clear login form
                document.getElementById('login-password').value = '';
                const loginError = document.getElementById('login-error');
                loginError.style.display = 'none';

                // Show already have account section again
                const alreadyHaveAccountSection = document.getElementById('already-have-account-section');
                if (alreadyHaveAccountSection) {
                    alreadyHaveAccountSection.style.display = 'block';
                }

                // Focus back on email input
                setTimeout(() => {
                    emailInput.focus();
                }, 100);
            });
        }

        function initializeAlreadyHaveAccount() {
            const alreadyAccountForm = document.getElementById('already-account-form');
            const alreadyAccountEmail = document.getElementById('already-account-email');
            const alreadyAccountPassword = document.getElementById('already-account-password');
            const alreadyAccountLoginBtn = document.getElementById('already-account-login-btn');
            const alreadyAccountClearBtn = document.getElementById('already-account-clear-btn');
            const alreadyAccountPasswordToggle = document.getElementById('already-account-password-toggle');
            const alreadyAccountError = document.getElementById('already-account-error');

            const guestEmail = document.getElementById('guest-email');
            const checkEmailBtn = document.getElementById('check-email-btn');

            // Handle input in already account form - hide "No Account Yet?" section
            function handleAlreadyAccountInput() {
                const hasInput = alreadyAccountEmail.value.trim() || alreadyAccountPassword.value.trim();

                const emailSection = document.querySelector('.email-section');

                if (hasInput) {
                    // Hide the entire "No Account Yet?" section
                    if (emailSection) {
                        emailSection.style.display = 'none';
                    }
                } else {
                    // Show the "No Account Yet?" section again
                    if (emailSection) {
                        emailSection.style.display = 'block';
                    }
                }
            }

            // Handle input in guest email section - hide "Already have account?" section
            function handleGuestEmailInput() {
                const hasInput = guestEmail.value.trim();

                const alreadyHaveAccountSection = document.getElementById('already-have-account-section');

                if (hasInput) {
                    // Hide the entire "Already have account?" section
                    if (alreadyHaveAccountSection) {
                        alreadyHaveAccountSection.style.display = 'none';
                    }
                } else {
                    // Show the "Already have account?" section again
                    if (alreadyHaveAccountSection) {
                        alreadyHaveAccountSection.style.display = 'block';
                    }
                }
            }

            // Add event listeners for input monitoring
            if (alreadyAccountEmail) {
                alreadyAccountEmail.addEventListener('input', handleAlreadyAccountInput);
            }
            if (alreadyAccountPassword) {
                alreadyAccountPassword.addEventListener('input', handleAlreadyAccountInput);
            }
            if (guestEmail) {
                guestEmail.addEventListener('input', handleGuestEmailInput);
            }

            // Password toggle functionality
            if (alreadyAccountPasswordToggle && alreadyAccountPassword) {
                alreadyAccountPasswordToggle.addEventListener('click', function() {
                    const type = alreadyAccountPassword.getAttribute('type') === 'password' ? 'text' : 'password';
                    alreadyAccountPassword.setAttribute('type', type);

                    const icon = this.querySelector('i');
                    icon.className = type === 'password' ? 'fas fa-eye' : 'fas fa-eye-slash';
                });
            }

            // Clear button functionality
            if (alreadyAccountClearBtn) {
                alreadyAccountClearBtn.addEventListener('click', function() {
                    alreadyAccountEmail.value = '';
                    alreadyAccountPassword.value = '';
                    alreadyAccountError.style.display = 'none';

                    // Show "No Account Yet?" section again
                    handleAlreadyAccountInput();

                    // Focus on guest email
                    setTimeout(() => {
                        guestEmail.focus();
                    }, 100);
                });
            }

            // Form submission
            if (alreadyAccountForm) {
                alreadyAccountForm.addEventListener('submit', function(e) {
                    e.preventDefault();

                    const email = alreadyAccountEmail.value.trim();
                    const password = alreadyAccountPassword.value.trim();

                    if (!email || !password) {
                        showAlreadyAccountError('Please enter both email and password.');
                        return;
                    }

                    if (!isValidEmail(email)) {
                        showAlreadyAccountError('Please enter a valid email address.');
                        return;
                    }

                    // Disable login button and show loading
                    alreadyAccountLoginBtn.disabled = true;
                    alreadyAccountLoginBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Signing In...';
                    alreadyAccountError.style.display = 'none';

                    // Send login request
                    const xhr = new XMLHttpRequest();
                    xhr.open('POST', basePath + '/functions/sign-in-db.php', true);
                    xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');

                    xhr.onload = function() {
                        alreadyAccountLoginBtn.disabled = false;
                        alreadyAccountLoginBtn.innerHTML = '<i class="fas fa-sign-in-alt me-1"></i>Sign In';

                        // console.log('Already Account Login response status:', xhr.status);
                        // console.log('Already Account Login response text:', xhr.responseText);

                        if (xhr.status === 200) {
                            let response;
                            try {
                                response = JSON.parse(xhr.responseText);

                                if (response.success) {
                                    // Store email in global variable before loading payment methods
                                    currentUserEmail = email;
                                    // Login successful - load payment methods
                                    loadUserPaymentMethods();
                                } else {
                                    showAlreadyAccountError(response.message || 'Invalid email or password.');
                                }
                            } catch (e) {
                                // Check if this is a redirect response (means login was successful but not AJAX)
                                if (xhr.responseText.includes('location:') || xhr.responseText.includes('my-ticket.php')) {
                                    currentUserEmail = email;
                                    loadUserPaymentMethods();
                                } else if (xhr.responseText.trim() === '') {
                                    // Empty response might mean successful login but no AJAX response
                                    currentUserEmail = email;
                                    loadUserPaymentMethods();
                                } else {
                                    showAlreadyAccountError('Login system error. Please check your credentials and try again.');
                                }
                            }
                        } else {
                            showAlreadyAccountError('Server error. Please try again.');
                        }
                    };

                    xhr.onerror = function() {
                        alreadyAccountLoginBtn.disabled = false;
                        alreadyAccountLoginBtn.innerHTML = '<i class="fas fa-sign-in-alt me-1"></i>Sign In';
                        showAlreadyAccountError('Connection error. Please try again.');
                    };

                    // Use the same parameter names as the working "No Account Yet?" login
                    // Note: signin_user triggers the login logic, ajax_login=1 makes it return JSON
                    // cart_transfer=1 tells the system to transfer guest cart to user cart
                    const formData = 'signin_user=1&username=' + encodeURIComponent(email) + '&password=' + encodeURIComponent(password) + '&ajax_login=1&cart_transfer=1&return_to=pre_checkout';
                    // console.log('Already Account - Sending login request with data:', formData);
                    xhr.send(formData);
                });
            }
        }

        function showAlreadyAccountError(message) {
            const alreadyAccountError = document.getElementById('already-account-error');
            alreadyAccountError.innerHTML = '<i class="fas fa-exclamation-circle me-2"></i>' + message;
            alreadyAccountError.style.display = 'block';
        }

        function handleLogout() {
            // Show loading state
            const emailResult = document.getElementById('email-check-result');
            if (emailResult) {
                emailResult.innerHTML = `
                    <div class="alert alert-info" style="background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; padding: 12px 16px; border-radius: 8px; margin-bottom: 20px; display: flex; align-items: center; gap: 10px;">
                        <i class="fas fa-spinner fa-spin" style="font-size: 16px;"></i>
                        <span>Logging out...</span>
                    </div>
                `;
            }

            // Send logout request
            const xhr = new XMLHttpRequest();
            xhr.open('POST', basePath + '/functions/logout.php', true);
            xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');

            xhr.onload = function() {
                if (xhr.status === 200) {
                    try {
                        const response = JSON.parse(xhr.responseText);
                        if (response.success) {
                            // Logout successful - reset the page to initial state
                            resetToInitialState();
                        } else {
                            // Logout failed - try fallback method
                            console.error('Logout failed:', response.message);
                            fallbackLogout();
                        }
                    } catch (e) {
                        // JSON parse error - try fallback method
                        console.error('Logout response parse error:', e);
                        fallbackLogout();
                    }
                } else {
                    // HTTP error - try fallback method
                    console.error('Logout HTTP error:', xhr.status);
                    fallbackLogout();
                }
            };

            xhr.onerror = function() {
                // Network error - try fallback method
                console.error('Logout network error');
                fallbackLogout();
            };

            xhr.send('logout=1');
        }

        function fallbackLogout() {
            // Fallback: Use the existing GET parameter method
            // This will redirect to the same page with ?logout=1 parameter
            const currentUrl = window.location.href.split('?')[0]; // Remove existing query params
            window.location.href = currentUrl + '?logout=1';
        }

        function resetToInitialState() {
            // Clear stored user email
            currentUserEmail = '';

            // Show already have account section
            const alreadyHaveAccountSection = document.getElementById('already-have-account-section');
            if (alreadyHaveAccountSection) {
                alreadyHaveAccountSection.style.display = 'block';
            }

            // Show email verification section
            const emailSection = document.querySelector('.email-section h4');
            const emailDescription = emailSection ? emailSection.nextElementSibling : null;
            const emailInputGroup = document.querySelector('.email-input-group');
            const emailHelp = document.querySelector('.email-help');

            if (emailSection) emailSection.style.display = 'block';
            if (emailDescription) emailDescription.style.display = 'block';
            if (emailInputGroup) emailInputGroup.style.display = 'flex';
            if (emailHelp) emailHelp.style.display = 'block';

            // Hide payment methods section
            const paymentMethodsSection = document.getElementById('payment-methods-section');
            if (paymentMethodsSection) {
                paymentMethodsSection.style.display = 'none';
            }

            // Hide inline login section
            const inlineLoginSection = document.getElementById('inline-login-section');
            if (inlineLoginSection) {
                inlineLoginSection.style.display = 'none';
            }

            // Hide checkout options
            const checkoutOptions = document.getElementById('checkout-options');
            if (checkoutOptions) {
                checkoutOptions.style.display = 'none';
            }

            // Clear all form fields
            const alreadyAccountEmail = document.getElementById('already-account-email');
            const alreadyAccountPassword = document.getElementById('already-account-password');
            const guestEmail = document.getElementById('guest-email');
            const loginPassword = document.getElementById('login-password');

            if (alreadyAccountEmail) alreadyAccountEmail.value = '';
            if (alreadyAccountPassword) alreadyAccountPassword.value = '';
            if (guestEmail) {
                guestEmail.value = '';
                guestEmail.disabled = false;
                guestEmail.placeholder = 'Enter your email address';
            }
            if (loginPassword) loginPassword.value = '';

            // Re-enable all buttons and inputs
            const checkEmailBtn = document.getElementById('check-email-btn');
            const alreadyAccountLoginBtn = document.getElementById('already-account-login-btn');

            if (checkEmailBtn) {
                checkEmailBtn.disabled = false;
                checkEmailBtn.style.display = 'inline-block';
            }
            if (alreadyAccountLoginBtn) alreadyAccountLoginBtn.disabled = false;

            // Clear email result
            const emailResult = document.getElementById('email-check-result');
            if (emailResult) {
                emailResult.innerHTML = '';
            }

            // Clear any error messages
            const alreadyAccountError = document.getElementById('already-account-error');
            const loginError = document.getElementById('login-error');
            if (alreadyAccountError) alreadyAccountError.style.display = 'none';
            if (loginError) loginError.style.display = 'none';

            // Focus on the first input field
            setTimeout(() => {
                if (alreadyAccountEmail) {
                    alreadyAccountEmail.focus();
                }
            }, 100);
        }

        function checkIfUserLoggedIn() {
            // Check if user is already logged in by making a simple AJAX request
            const xhr = new XMLHttpRequest();
            xhr.open('GET', basePath + '/functions/check-login-status.php', true);
            xhr.responseType = 'json';

            xhr.onload = function() {
                if (xhr.status === 200 && xhr.response) {
                    if (xhr.response.logged_in) {
                        // User is logged in, hide email verification and show payment methods directly
                        hideEmailVerificationSection();
                        loadUserPaymentMethods();
                    }
                    // If not logged in, do nothing - let user go through normal email verification
                }
            };

            xhr.onerror = function() {
                // If error checking login status, do nothing - let user go through normal flow
            };

            xhr.send();
        }

        function hideEmailVerificationSection() {
            // Hide the email verification section since user is already logged in
            const emailSection = document.querySelector('.email-section h4');
            const emailDescription = emailSection ? emailSection.nextElementSibling : null;
            const emailInputGroup = document.querySelector('.email-input-group');
            const emailHelp = document.querySelector('.email-help');

            if (emailSection) emailSection.style.display = 'none';
            if (emailDescription) emailDescription.style.display = 'none';
            if (emailInputGroup) emailInputGroup.style.display = 'none';
            if (emailHelp) emailHelp.style.display = 'none';

            // Hide already have account section since user is already logged in
            const alreadyHaveAccountSection = document.getElementById('already-have-account-section');
            if (alreadyHaveAccountSection) {
                alreadyHaveAccountSection.style.display = 'none';
            }

            // Clear any existing email result content since we'll show user info in payment methods
            const emailResult = document.getElementById('email-check-result');
            if (emailResult) {
                emailResult.innerHTML = '';
            }

            // Show a message that user is logged in with their email (for users already logged in on page load)
            const userEmail = '<?php echo htmlspecialchars($user_email); ?>';
            if (userEmail && emailResult) {
                emailResult.innerHTML = `
                    <div class="alert alert-success" style="background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 12px 16px; border-radius: 8px; margin-bottom: 20px;">
                        <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 8px;">
                            <i class="fas fa-user-circle" style="font-size: 18px;"></i>
                            <span>You are logged-in as <strong>${userEmail}</strong></span>
                        </div>
                        <div style="font-size: 13px; color: #0f5132;">
                            <span>Not this account? </span>
                            <a href="#" id="logout-link-preloaded" style="color: #0f5132; text-decoration: underline; font-weight: 600; cursor: pointer;">
                                Click here to logout
                            </a>
                        </div>
                    </div>
                `;

                // Add logout functionality for preloaded users
                const logoutLink = document.getElementById('logout-link-preloaded');
                if (logoutLink) {
                    logoutLink.addEventListener('click', function(e) {
                        e.preventDefault();
                        handleLogout();
                    });
                }
            }
        }

        function loadUserPaymentMethods() {
            // Hide login section (with null check)
            const inlineLoginSection = document.getElementById('inline-login-section');
            if (inlineLoginSection) {
                inlineLoginSection.style.display = 'none';
            }

            // Hide already have account section
            const alreadyHaveAccountSection = document.getElementById('already-have-account-section');
            if (alreadyHaveAccountSection) {
                alreadyHaveAccountSection.style.display = 'none';
            }

            // Handle email section visibility when user logs in from "Already have account?"
            const alreadyAccountEmail = document.getElementById('already-account-email');
            if (alreadyAccountEmail && alreadyAccountEmail.value) {
                // User logged in from "Already have account?" form
                // First, show the email section again (it was hidden by handleAlreadyAccountInput)
                const emailSection = document.querySelector('.email-section');
                if (emailSection) {
                    emailSection.style.display = 'block';
                }

                // Then hide only the email verification parts, not the entire section
                hideEmailVerificationSection();
            }

            // Show permanent user info for all logged-in users
            const loginEmailField = document.getElementById('login-email');
            const guestEmailField = document.getElementById('guest-email');

            let userEmail = '';
            // Check multiple sources for the user email
            if (currentUserEmail) {
                // Use stored email from login (most reliable)
                userEmail = currentUserEmail;
            } else if (loginEmailField && loginEmailField.value) {
                // User logged in from "No Account Yet?" (inline login)
                userEmail = loginEmailField.value;
            } else if (alreadyAccountEmail && alreadyAccountEmail.value) {
                // User logged in from "Already have account?" form
                userEmail = alreadyAccountEmail.value;
            } else if (guestEmailField && guestEmailField.value) {
                // User entered email in guest field and was found to have account
                userEmail = guestEmailField.value;
            } else {
                // Fallback: Get email from PHP session (for users already logged in on page load)
                const serverUserEmail = '<?php echo htmlspecialchars($user_email); ?>';
                if (serverUserEmail) {
                    userEmail = serverUserEmail;
                }
            }

            // Always show user info with logout link for logged-in users
            const emailResult = document.getElementById('email-check-result');
            if (emailResult) {
                emailResult.innerHTML = `
                    <div class="alert alert-success" style="background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 12px 16px; border-radius: 8px; margin-bottom: 20px;">
                        <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 8px;">
                            <i class="fas fa-user-circle" style="font-size: 18px;"></i>
                            <span>You are logged-in as <strong>${userEmail || 'User'}</strong></span>
                        </div>
                        <div style="font-size: 13px; color: #0f5132;">
                            <span>Not this account? </span>
                            <a href="#" id="logout-link" style="color: #0f5132; text-decoration: underline; font-weight: 600; cursor: pointer;">
                                Click here to logout
                            </a>
                        </div>
                    </div>
                `;

                // Add logout functionality
                const logoutLink = document.getElementById('logout-link');
                if (logoutLink) {
                    logoutLink.addEventListener('click', function(e) {
                        e.preventDefault();
                        handleLogout();
                    });
                }
            }

            // Show loading state
            const paymentMethodsSection = document.getElementById('payment-methods-section');
            const paymentMethodsContainer = document.getElementById('payment-methods-container');

            if (!paymentMethodsSection || !paymentMethodsContainer) {
                console.error('Payment methods section or container not found');
                return;
            }

            paymentMethodsSection.style.display = 'block';
            paymentMethodsContainer.innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin"></i> Loading payment methods...</div>';

            // Load payment methods using the same approach as cart.php
            const xhr = new XMLHttpRequest();
            xhr.open('GET', basePath + '/functions/get-payment-methods.php', true);
            // Don't set responseType to allow debugging

            xhr.onload = function() {
                if (xhr.status === 200) {
                    try {
                        const response = JSON.parse(xhr.responseText);

                        if (response.success) {
                            if (response.payment_methods && response.payment_methods.length > 0) {
                                displayPaymentMethods(response.payment_methods);
                            } else {
                                paymentMethodsContainer.innerHTML = '<div class="alert alert-info"><i class="fas fa-info-circle me-2"></i>No saved payment methods found. You can add a new card below.</div>';
                                displayNewCardOption();
                            }
                        } else {
                            paymentMethodsContainer.innerHTML = '<div class="alert alert-warning"><i class="fas fa-exclamation-triangle me-2"></i>' + (response.message || 'No payment methods found') + '<br><small>This might be because you haven\'t made any purchases yet.</small></div>';
                            displayNewCardOption();
                        }
                    } catch (e) {
                        console.error('Error parsing payment methods response:', e);
                        console.error('Response text:', xhr.responseText);
                        paymentMethodsContainer.innerHTML = '<div class="alert alert-danger">Error loading payment methods. Please try again.</div>';
                    }
                } else {
                    console.error('Payment methods HTTP error:', xhr.status, xhr.statusText);
                    paymentMethodsContainer.innerHTML = '<div class="alert alert-danger">Server error loading payment methods.</div>';
                }
            };

            xhr.onerror = function() {
                paymentMethodsContainer.innerHTML = '<div class="alert alert-danger">Connection error. Please try again.</div>';
            };

            xhr.send();
        }

        function displayPaymentMethods(paymentMethods) {
            const container = document.getElementById('payment-methods-container');
            let html = '';

            // Add saved payment methods (now using Stripe format)
            paymentMethods.forEach((method, index) => {
                const isDefault = method.is_default || index === 0; // Use is_default from API or first method
                const cardBrand = method.card_brand.charAt(0).toUpperCase() + method.card_brand.slice(1);

                html += `
                    <div class="payment-method-item" data-payment-method="${method.id}">
                        <div class="payment-method-info">
                            <div class="card-icon">
                                ${getCardIcon(method.card_brand)}
                            </div>
                            <div class="card-details">
                                <div class="card-brand">${cardBrand}</div>
                                <div class="card-number">•••• •••• •••• ${method.card_last4}</div>
                                <div class="card-expiry">Expires ${method.card_exp_month}/${method.card_exp_year}</div>
                            </div>
                        </div>
                        ${isDefault ? '<span class="default-badge">Default</span>' : ''}
                    </div>
                `;
            });

            // Add new card option if user has less than 2 cards (same as cart.php)
            if (paymentMethods.length < 2) {
                html += `
                    <div class="payment-method-item" data-payment-method="new_card">
                        <div class="payment-method-info">
                            <div class="card-icon add-card-icon">
                                <i class="fas fa-credit-card" style="font-size: 20px; color: #6754e2;"></i>
                                <i class="fas fa-plus" style="font-size: 12px; color: #6754e2; position: absolute; top: -2px; right: -2px; background: white; border-radius: 50%; padding: 2px;"></i>
                            </div>
                            <div class="card-details">
                                <div class="card-brand">Credit/Debit Card</div>
                                <div class="card-number">Add New Card</div>
                            </div>
                        </div>
                    </div>
                `;
            } else {
                // Show warning if user has reached the limit (same as cart.php)
                html += `
                    <div class="alert alert-warning" style="margin-top: 15px;">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>Maximum Cards Reached</strong><br>
                        You already have 2 payment methods saved. To add a new card, please remove an existing one first.
                        <br><a href="${basePath}/front-end/payment-methods.php?return_to=pre_checkout"
                             style="color: #007bff; text-decoration: none; display: inline-flex; align-items: center; gap: 8px; margin-top: 15px; padding: 10px 16px; background: #e7f3ff; border: 1px solid #007bff; border-radius: 6px; font-weight: 600; transition: all 0.3s ease; cursor: pointer;"
                             onmouseover="this.style.background='#007bff'; this.style.color='white';"
                             onmouseout="this.style.background='#e7f3ff'; this.style.color='#007bff';">
                            <i class="fas fa-credit-card"></i> Manage Payment Methods
                        </a>
                    </div>
                `;
            }

            container.innerHTML = html;

            // Select first payment method by default
            const firstMethod = container.querySelector('.payment-method-item');
            if (firstMethod) {
                firstMethod.classList.add('selected');
                // Initialize save payment method checkbox visibility
                toggleSavePaymentMethodCheckbox(firstMethod);
            }
        }

        function displayNewCardOption() {
            const container = document.getElementById('payment-methods-container');
            container.innerHTML = `
                <div class="payment-method-item selected" data-payment-method="new_card">
                    <div class="payment-method-info">
                        <div class="card-icon add-card-icon">
                            <i class="fas fa-credit-card" style="font-size: 20px; color: #6754e2;"></i>
                            <i class="fas fa-plus" style="font-size: 12px; color: #6754e2; position: absolute; top: -2px; right: -2px; background: white; border-radius: 50%; padding: 2px;"></i>
                        </div>
                        <div class="card-details">
                            <div class="card-brand">Add New Card</div>
                            <div class="card-number">Enter your payment information</div>
                        </div>
                    </div>
                </div>
            `;

            // Show save payment method checkbox for new card
            const saveCardOption = document.getElementById('save-card-option');
            if (saveCardOption) {
                saveCardOption.style.display = 'block';
            }
        }

        function getCardIcon(brand) {
            const brandLower = brand.toLowerCase();
            const basePath = '<?php echo $base_path; ?>';

            // Map card brands to local logo files
            const cardLogos = {
                'visa': basePath + '/image/card-logo/visa.webp',
                'mastercard': basePath + '/image/card-logo/mastercard.svg',
                'amex': basePath + '/image/card-logo/AE.svg',
                'american_express': basePath + '/image/card-logo/AE.svg',
                'discover': basePath + '/image/card-logo/discover.png',
                'maestro': basePath + '/image/card-logo/Maestro.svg',
                'diners': basePath + '/image/card-logo/dinacard.svg',
                'diners_club': basePath + '/image/card-logo/dinacard.svg',
                'jcb': basePath + '/image/card-logo/bccard.png',
                'unionpay': basePath + '/image/card-logo/bccard.png'
            };

            // Get the appropriate logo or fallback to Stripe logo
            const logoSrc = cardLogos[brandLower] || basePath + '/image/card-logo/stripe-payment-logo.png';
            const fallbackSrc = basePath + '/image/card-logo/stripe-payment-logo.png';

            return `<img src="${logoSrc}"
                         alt="${brand}"
                         style="height: 25px; width: auto; max-width: 40px; object-fit: contain;"
                         onerror="this.src='${fallbackSrc}'">`;
        }

        function toggleSavePaymentMethodCheckbox(selectedMethodElement) {
            const saveCardOption = document.getElementById('save-card-option');
            const paymentMethodId = selectedMethodElement.getAttribute('data-payment-method');

            if (paymentMethodId === 'new_card') {
                // Show checkbox for new card
                saveCardOption.style.display = 'block';
            } else {
                // Hide checkbox for saved cards
                saveCardOption.style.display = 'none';
            }
        }

        function initializePaymentMethods() {
            // Handle payment method selection
            document.addEventListener('click', function(e) {
                const paymentMethodItem = e.target.closest('.payment-method-item');
                if (paymentMethodItem) {
                    // Remove selected class from all items
                    document.querySelectorAll('.payment-method-item').forEach(item => {
                        item.classList.remove('selected');
                    });

                    // Add selected class to clicked item
                    paymentMethodItem.classList.add('selected');

                    // Show/hide save payment method checkbox based on selection
                    toggleSavePaymentMethodCheckbox(paymentMethodItem);
                }
            });

            // Handle proceed payment button
            const proceedPaymentBtn = document.getElementById('proceed-payment-btn');
            if (proceedPaymentBtn) {
                proceedPaymentBtn.addEventListener('click', function() {
                    const selectedMethod = document.querySelector('.payment-method-item.selected');
                    if (!selectedMethod) {
                        alert('Please select a payment method.');
                        return;
                    }

                    const paymentMethodId = selectedMethod.getAttribute('data-payment-method');

                    if (paymentMethodId === 'new_card') {
                        // Get save payment method checkbox value
                        const savePaymentMethodCheckbox = document.getElementById('save_payment_method');
                        const savePaymentMethod = savePaymentMethodCheckbox ? savePaymentMethodCheckbox.checked : false;

                        // Add save payment method value to the form
                        const checkoutForm = document.getElementById('checkout-form');
                        let saveMethodInput = checkoutForm.querySelector('input[name="save_payment_method"]');
                        if (!saveMethodInput) {
                            saveMethodInput = document.createElement('input');
                            saveMethodInput.type = 'hidden';
                            saveMethodInput.name = 'save_payment_method';
                            checkoutForm.appendChild(saveMethodInput);
                        }
                        saveMethodInput.value = savePaymentMethod ? '1' : '0';

                        // Submit the existing checkout form which is already configured for cart checkout
                        checkoutForm.submit();
                    } else {
                        // Show confirmation modal for saved payment method
                        showPaymentConfirmation(selectedMethod, paymentMethodId);
                    }
                });
            }
        }

        function showPaymentConfirmation(selectedMethodElement, paymentMethodId) {
            // Get payment method details
            const cardInfo = selectedMethodElement.querySelector('.card-details');
            const cardBrand = cardInfo.querySelector('.card-brand').textContent;
            const cardNumber = cardInfo.querySelector('.card-number').textContent;
            const cardExpiry = cardInfo.querySelector('.card-expiry').textContent;

            // Create and show confirmation modal
            const modalHtml = `
                <div class="modal fade" id="paymentConfirmModal" tabindex="-1" style="z-index: 9999;">
                    <div class="modal-dialog modal-dialog-centered">
                        <div class="modal-content" style="border-radius: 15px; border: none; box-shadow: 0 10px 30px rgba(0,0,0,0.2);">
                            <div class="modal-header" style="background: linear-gradient(135deg, #6754e2 0%, #8b7cf6 100%); color: white; border-radius: 15px 15px 0 0;">
                                <h5 class="modal-title"><i class="fas fa-credit-card me-2"></i>Confirm Payment</h5>
                                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body" style="padding: 25px;">
                                <div class="payment-summary">
                                    <h6><i class="fas fa-credit-card me-2"></i>Payment Method</h6>
                                    <div class="selected-card" style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin-bottom: 20px;">
                                        <div style="display: flex; align-items: center; gap: 12px;">
                                            <div class="card-icon">${getCardIcon(cardBrand.toLowerCase())}</div>
                                            <div>
                                                <div style="font-weight: 600;">${cardBrand}</div>
                                                <div style="color: #6c757d; font-size: 14px;">${cardNumber}</div>
                                                <div style="color: #6c757d; font-size: 12px;">${cardExpiry}</div>
                                            </div>
                                        </div>
                                    </div>

                                    <h6><i class="fas fa-shopping-cart me-2"></i>Order Summary</h6>
                                    <div class="order-total" style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin-bottom: 20px;">
                                        <div style="display: flex; justify-content: space-between; align-items: center;">
                                            <span style="font-weight: 600;">Total Amount:</span>
                                            <span style="font-weight: 600; color: #6754e2; font-size: 18px;">$<?php echo number_format($cart_total, 2); ?></span>
                                        </div>
                                    </div>

                                    <div class="security-notice" style="background: #e8f5e8; padding: 12px; border-radius: 8px; border-left: 4px solid #28a745;">
                                        <small><i class="fas fa-shield-alt me-1"></i>Your payment is secured by Stripe's industry-leading encryption.</small>
                                    </div>
                                </div>
                            </div>
                            <div class="modal-footer" style="border-top: 1px solid #dee2e6; padding: 20px;">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                <button type="button" class="btn btn-primary" id="confirmPaymentBtn" style="background: #6754e2; border-color: #6754e2;">
                                    <i class="fas fa-lock me-2"></i>Confirm Payment
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // Remove existing modal if any
            const existingModal = document.getElementById('paymentConfirmModal');
            if (existingModal) {
                existingModal.remove();
            }

            // Add modal to body
            document.body.insertAdjacentHTML('beforeend', modalHtml);

            // Show modal
            const modal = new bootstrap.Modal(document.getElementById('paymentConfirmModal'));
            modal.show();

            // Remove any existing event listeners to prevent duplicates
            const confirmBtn = document.getElementById('confirmPaymentBtn');
            const newConfirmBtn = confirmBtn.cloneNode(true);
            confirmBtn.parentNode.replaceChild(newConfirmBtn, confirmBtn);

            // Handle confirm payment
            document.getElementById('confirmPaymentBtn').addEventListener('click', function() {
                // Add debugging
                // console.log('Confirm payment clicked');
                // console.log('Payment method ID:', paymentMethodId);
                // console.log('Base path:', basePath);

                const redirectUrl = basePath + '/front-end/create-pre-checkout-payment.php?payment_method=' + paymentMethodId;
                // console.log('Redirecting to:', redirectUrl);

                // Redirect to payment processing with saved payment method (pre-checkout specific)
                window.location.href = redirectUrl;
            });
        }

        // Handle cancel/clear button functionality
        document.addEventListener('DOMContentLoaded', function() {
            const cancelBtn = document.getElementById('cancel-checkout-btn');
            if (cancelBtn) {
                cancelBtn.addEventListener('click', function() {
                    // Clear the guest email input and prevent autofill
                    const emailInput = document.getElementById('guest-email');
                    if (emailInput) {
                        emailInput.value = '';
                        emailInput.setAttribute('autocomplete', 'off');
                        emailInput.disabled = false;
                    }

                    // Show the check button again
                    const checkBtn = document.getElementById('check-email-btn');
                    if (checkBtn) {
                        checkBtn.style.display = 'inline-block';
                        checkBtn.disabled = false;
                        checkBtn.innerHTML = '<i class="fas fa-search me-1"></i>Check';
                    }

                    // Hide the email result
                    const emailResult = document.getElementById('email-check-result');
                    if (emailResult) {
                        emailResult.style.display = 'none';
                    }

                    // Hide checkout options
                    const checkoutOptions = document.getElementById('checkout-options');
                    if (checkoutOptions) {
                        checkoutOptions.style.display = 'none';
                    }

                    // Hide the cancel button itself
                    cancelBtn.style.display = 'none';

                    // Clear hidden guest email
                    const guestEmailHidden = document.getElementById('guest_email_hidden');
                    if (guestEmailHidden) {
                        guestEmailHidden.value = '';
                    }

                    // Show the "Already have account?" section again
                    const alreadyHaveAccountSection = document.getElementById('already-have-account-section');
                    if (alreadyHaveAccountSection) {
                        alreadyHaveAccountSection.style.display = 'block';
                    }

                    // Show the "No Account Yet?" header and description again
                    const emailSection = document.querySelector('.email-section h4');
                    const emailDescription = emailSection ? emailSection.nextElementSibling : null;
                    const emailHelp = document.querySelector('.email-help');

                    if (emailSection) emailSection.style.display = 'block';
                    if (emailDescription) emailDescription.style.display = 'block';
                    if (emailHelp) emailHelp.style.display = 'block';

                    // Reinitialize mutual exclusivity listeners
                    initializeAlreadyHaveAccount();

                    // Focus back on email input
                    if (emailInput) {
                        setTimeout(() => emailInput.focus(), 100);
                    }
                });
            }
        });


    </script>
</body>
</html>
