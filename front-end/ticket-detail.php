<?php
include('../functions/server.php');
include('../functions/maintenance-check.php');
include('../functions/timezone-helper.php');
require_once '../functions/graphql_functions.php'; // Include GraphQL functions for Appika API
session_start();

if (!isset($_SESSION['username'])) {
    header("Location: ../index.php");
    exit();
}

// Check if user panel is accessible (strict mode)
checkMaintenanceStatus('strict');

$username = $_SESSION['username'];

// Get ticket ID from URL - support multiple parameter formats
$ticketId = isset($_GET['id']) ? (int) $_GET['id'] : 0;
$appikaId = isset($_GET['appika_id']) ? (int) $_GET['appika_id'] : 0;
$ticketIdParam = isset($_GET['ticket_id']) ? (int) $_GET['ticket_id'] : 0;

// Determine which type of ID we're working with
// Priority: ticket_id (new format) > appika_id > id (legacy)
if ($ticketIdParam > 0) {
    $isAppikaTicket = true;
    $currentTicketId = $ticketIdParam;
} elseif ($appikaId > 0) {
    $isAppikaTicket = true;
    $currentTicketId = $appikaId;
} else {
    $isAppikaTicket = false;
    $currentTicketId = $ticketId;
}

if ($currentTicketId <= 0) {
    echo "Invalid ticket ID.";
    exit();
}

// Get user data
$userQuery = "SELECT * FROM user WHERE username = '$username'";
$userResult = mysqli_query($conn, $userQuery);
$user = mysqli_fetch_assoc($userResult);
$userId = $user['id'] ?? 0;
$userEmail = $user['email'] ?? '';

$ticket = null;
$userRating = null;

if ($isAppikaTicket) {
    // Fetch ticket from Appika API (using working query from appika-ticket-details.php)
    $query = '
    query GetTicket($id: Int!) {
        getTicket(id: $id) {
            id
            ticket_no
            contact_id
            agent_id
            req_email
            subject
            type
            type_name
            priority
            status
            created
            updated
            contacts {
                id
                email
                fname
                status
            }
            ticketMsg {
                id
                message
            }
        }
    }';
    
    $variables = ['id' => $currentTicketId];
    $result = makeGraphQLRequest($query, $variables);
    
    if ($result['success'] && isset($result['data']['data']['getTicket'])) {
        $appikaTicket = $result['data']['data']['getTicket'];
        
        // Check if this ticket belongs to the current user
        if (strtolower($appikaTicket['req_email']) !== strtolower($userEmail)) {
            echo "Ticket not found or you don't have permission to view it.";
            exit();
        }
        
        // Get the description from the first ticket message (same logic as appika-ticket-details.php)
        $description = '';
        if (isset($appikaTicket['ticketMsg']) && is_array($appikaTicket['ticketMsg']) && !empty($appikaTicket['ticketMsg'])) {
            // Get the first message as the description
            $firstMessage = $appikaTicket['ticketMsg'][0];
            if (isset($firstMessage['message']) && !empty($firstMessage['message'])) {
                $description = $firstMessage['message'];
            } else {
                $description = 'No description found for this ticket.';
            }
        } else {
            $description = 'No description found for this ticket.';
        }

        // Convert Appika ticket to local format for compatibility
        // Use display names that match what users see
        $typeMapping = [1 => 'Starter', 2 => 'Business', 3 => 'Ultimate'];
        $ticket = [
            'id' => $appikaTicket['id'],
            'appika_id' => $appikaTicket['id'],
            'ticket_no' => $appikaTicket['ticket_no'],
            'subject' => $appikaTicket['subject'],
            'description' => $description,
            'priority' => ucfirst(strtolower($appikaTicket['priority'])),
            'ticket_type' => $typeMapping[$appikaTicket['type']] ?? 'Starter',
            'status' => strtolower($appikaTicket['status']),
            'created_at' => $appikaTicket['created'],
            'updated_at' => $appikaTicket['updated'],
            'admin_name' => null, // Not available from Appika API
            'is_appika_ticket' => true
        ];
    } else {
        echo "Ticket not found or API error: " . ($result['error'] ?? 'Unknown error');
        exit();
    }
    
    // For Appika tickets, we don't have local rating data
    $userRating = null;
    
} else {
    // Fetch ticket from local database (legacy support)
    $sql = "SELECT st.*, au.username AS admin_name
            FROM support_tickets st
            LEFT JOIN admin_users au ON st.assigned_admin_id = au.id
            WHERE st.id = $ticketId AND st.user_id = $userId
            LIMIT 1";
    
    $result = mysqli_query($conn, $sql);
    $ticket = mysqli_fetch_assoc($result);
    
    if (!$ticket) {
        echo "Ticket not found or you don't have permission to view it.";
        exit();
    }
    
    $ticket['is_appika_ticket'] = false;
    
    // Check if user has rated this ticket (only for local tickets)
    $ratingQuery = "SELECT * FROM ticket_ratings WHERE ticket_id = $ticketId AND user_id = $userId";
    $ratingResult = mysqli_query($conn, $ratingQuery);
    $userRating = mysqli_fetch_assoc($ratingResult);
}
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <title>My Support Tickets</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link rel="shortcut icon" href="../image/png/favicon.png" type="image/x-icon">
    <!-- Bootstrap and plugins -->
    <link rel="stylesheet" href="../css/bootstrap.css">
    <link rel="stylesheet" href="../css/main.css">
    <link rel="shortcut icon" href="../image/png/favicon.png" type="image/x-icon">
    <!-- Bootstrap, fonts & icons -->
    <link rel="stylesheet" href="../css/bootstrap.css">
    <link rel="stylesheet" href="../fonts/icon-font/css/style.css">
    <link rel="stylesheet" href="../fonts/typography-font/typo.css">
    <link rel="stylesheet" href="../fonts/fontawesome-5/css/all.css">
    <!-- Plugin'stylesheets -->
    <link rel="stylesheet" href="../plugins/aos/aos.min.css">
    <link rel="stylesheet" href="../plugins/fancybox/jquery.fancybox.min.css">
    <link rel="stylesheet" href="../plugins/nice-select/nice-select.min.css">
    <link rel="stylesheet" href="../plugins/slick/slick.min.css">
    <link rel="stylesheet" href="../plugins/date-picker/css/gijgo.min.css" type="text/css" />
    <!-- Vendor stylesheets -->
    <link rel="stylesheet" href="../plugins/theme-mode-switcher/switcher-panel.css">
    <link rel="stylesheet" href="../css/main.css">
    <link rel="stylesheet" href="../css/theme-mode-custom.css">
    <style>
    body {
        padding-top: 200px;
        padding-bottom: 100px;
    }

    .badge {
        padding: 6.8px;
        font-size: 16px;
    }

    .badge-open {
        background-color: #fd7e14;
        color: #fff;
    }

    .badge-in_progress {
        background-color: #0d6efd;
        color: #fff;
    }

    .badge-resolved {
        background-color: #28a745;
        color: #fff;
    }

    .badge-closed {
        background-color: #6c757d;
        color: #fff;
    }

    /* Table styles */
    .table-responsive {
        overflow-x: auto;
    }

    .table td.label {
        font-weight: bold;
        width: 30%;
        background-color: #f8f9fa;
    }

    /* Button styles */
    .ticket-buttons {
        display: flex;
        justify-content: center;
        gap: 15px;
        flex-wrap: wrap;
    }

    .ticket-btn {
        min-width: 180px;
        padding: 10px 20px;
        font-size: 16px;
        text-align: center;
        border-radius: 4px;
        transition: all 0.3s ease;
    }

    /* Responsive styles */
    @media (max-width: 991px) {
        body {
            padding-top: 150px;
            padding-bottom: 80px;
        }

        .table td.label {
            width: 35%;
        }

        .ticket-btn {
            min-width: 160px;
            font-size: 15px;
        }
    }

    @media (max-width: 767px) {
        body {
            padding-top: 120px;
            padding-bottom: 60px;
        }

        .table td.label {
            width: 40%;
        }

        h2 {
            font-size: 1.5rem;
        }

        .ticket-btn {
            min-width: 150px;
            font-size: 14px;
            padding: 8px 16px;
        }
    }

    @media (max-width: 575px) {

        .table td,
        .table th {
            padding: 0.5rem;
            font-size: 0.9rem;
        }

        .ticket-buttons {
            flex-direction: column;
            align-items: center;
            width: 100%;
            gap: 0px;
        }

        .ticket-btn {
            width: 80%;
            max-width: 250px;
            margin-bottom: 10px;
        }
    }

    /* Menu open state for mobile */
    body.menu-open {
        overflow: hidden;
    }

    .container {
        width: 1500px;
        max-width: 95%;
    }

    /* Photo Attachment Styles */
    .ticket-attachments {
        margin-top: 10px;
    }

    .attachment-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        gap: 15px;
        margin-bottom: 10px;
    }

    .attachment-item {
        position: relative;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        transition: transform 0.2s ease;
        background: #f8f9fa;
    }

    .attachment-item:hover {
        transform: scale(1.05);
    }

    .attachment-thumbnail {
        width: 100%;
        height: 120px;
        object-fit: cover;
        display: block;
        transition: opacity 0.2s ease;
    }

    .attachment-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0,0,0,0.7);
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        opacity: 0;
        transition: opacity 0.2s ease;
        font-size: 1.5rem;
    }

    .attachment-item:hover .attachment-overlay {
        opacity: 1;
    }

    .attachment-filename {
        padding: 8px;
        font-size: 0.85rem;
        text-align: center;
        background: white;
        border-top: 1px solid #dee2e6;
        word-break: break-word;
    }

    /* Rating Display Styles */
    .rating-display {
        background-color: #f8f9fa;
        padding: 15px;
        border-radius: 8px;
        border: 1px solid #dee2e6;
    }

    .rating-display .star-rating {
        margin-bottom: 10px;
    }

    .rating-display .star-rating i.fas {
        color: #ffc107;
    }

    .rating-display .star-rating i.far {
        color: #dee2e6;
    }

    .rating-display .star-rating i {
        font-size: 1.2rem;
        margin-right: 2px;
    }

    .rating-comment {
        background-color: #f8f9fa;
        padding: 10px;
        border-radius: 5px;
        border-left: 4px solid #ffc107;
        margin-bottom: 8px;
        font-style: italic;
    }

    @media (max-width: 767px) {
        .rating-display .star-rating i {
            font-size: 1rem;
        }
    }
    </style>


</head>

<body>

    <?php include('../header-footer/header.php'); ?>

    <div class="container">
        <div class="row">
            <!-- Sidebar - Hidden on mobile, visible on larger screens -->
            <div class="col-lg-3 col-md-4 d-none d-md-block mb-4">
                <?php include('user-menu.php'); ?>
            </div>

            <!-- Mobile menu - Visible only on mobile -->
            <div class="col-12 d-md-none mb-4">
                <?php include('user-menu.php'); ?>
            </div>

            <!-- Main Content -->
            <div class="col-lg-9 col-md-8">
                <h2 class="mb-4 text-center">Ticket #<?php echo $ticket['id']; ?> -
                    <?php echo htmlspecialchars($ticket['subject']); ?></h2>

                <!-- Timezone indicator -->
                <div class="text-center mb-3">
                    <small class="text-muted">
                        <i class="fas fa-clock"></i>
                        Times shown in your timezone: <span id="customer-timezone-display"><?php echo getCustomerTimezone(); ?></span>
                    </small>
                </div>

                <div class="table-responsive">
                    <table class="table table-bordered">
                        <tr>
                            <td class="label">Subject</td>
                            <td><?php echo htmlspecialchars($ticket['subject']); ?></td>
                        </tr>
                        <tr>
                            <td class="label">Description</td>
                            <td><?php
                                $cleanDescription = str_replace(["\r\n", "\r"], "\n", $ticket['description']);
                                echo nl2br(htmlspecialchars($cleanDescription));
                            ?></td>
                        </tr>
                        <tr>
                            <td class="label">Type</td>
                            <td><?php echo ucfirst($ticket['ticket_type']); ?></td>
                        </tr>
                        <tr>
                            <td class="label">Status</td>
                            <td>
                                <span class="badge badge-<?php echo $ticket['status']; ?>">
                                    <?php echo ucfirst($ticket['status']); ?>
                                </span>
                            </td>
                        </tr>
                        <tr>
                            <td class="label">Priority</td>
                            <td><?php echo ucfirst($ticket['priority']); ?></td>
                        </tr>
                        <tr>
                            <td class="label">Created</td>
                            <td><?php echo showCustomerTime($ticket['created_at']); ?></td>
                        </tr>
                        <?php if (!empty($ticket['updated_at'])): ?>
                        <tr>
                            <td class="label">Last Updated</td>
                            <td><?php echo showCustomerTime($ticket['updated_at']); ?></td>
                        </tr>
                        <?php endif; ?>
                        <?php if (!empty($ticket['admin_name'])): ?>
                        <tr>
                            <td class="label">Assigned Admin</td>
                            <td><?php echo htmlspecialchars($ticket['admin_name']); ?></td>
                        </tr>
                        <?php endif; ?>

                        <!-- Show attachments if available (only for local tickets) -->
                        <?php if (!$ticket['is_appika_ticket'] && !empty($ticket['attachments'])): ?>
                        <?php
                            $attachments = json_decode($ticket['attachments'], true);
                            if (is_array($attachments) && count($attachments) > 0):
                        ?>
                        <tr>
                            <td class="label">Attachments</td>
                            <td>
                                <div class="ticket-attachments">
                                    <div class="attachment-grid">
                                        <?php foreach ($attachments as $index => $attachment): ?>
                                        <?php if (isset($attachment['filename']) && isset($attachment['path'])): ?>
                                        <div class="attachment-item">
                                            <a href="<?php echo htmlspecialchars($attachment['path']); ?>"
                                                data-fancybox="ticket-gallery"
                                                data-caption="<?php echo htmlspecialchars($attachment['filename']); ?>">
                                                <img src="<?php echo htmlspecialchars($attachment['path']); ?>"
                                                    alt="<?php echo htmlspecialchars($attachment['filename']); ?>"
                                                    class="attachment-thumbnail"
                                                    onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                                                <div class="attachment-overlay" style="display: none;">
                                                    <i class="fas fa-search-plus"></i>
                                                </div>
                                            </a>
                                            <div class="attachment-filename">
                                                <?php echo htmlspecialchars($attachment['filename']); ?>
                                            </div>
                                        </div>
                                        <?php endif; ?>
                                        <?php endforeach; ?>
                                    </div>
                                </div>
                            </td>
                        </tr>
                        <?php endif; ?>
                        <?php endif; ?>

                        <!-- Show rating if user has rated this ticket (only for local tickets) -->
                        <?php if (!$ticket['is_appika_ticket'] && $userRating): ?>
                        <tr>
                            <td class="label">Your Rating</td>
                            <td>
                                <div class="rating-display">
                                    <div class="star-rating">
                                        <?php for ($i = 1; $i <= 5; $i++): ?>
                                            <i class="<?php echo $i <= $userRating['rating'] ? 'fas' : 'far'; ?> fa-star"></i>
                                        <?php endfor; ?>
                                        <span class="ml-2">(<?php echo $userRating['rating']; ?>/5)</span>
                                    </div>
                                    <?php if (!empty($userRating['comment'])): ?>
                                        <div class="rating-comment">
                                            <?php echo nl2br(htmlspecialchars($userRating['comment'])); ?>
                                        </div>
                                    <?php endif; ?>
                                    <small class="text-muted">
                                        Rated on: <?php echo showCustomerTime($userRating['created_at']); ?>
                                        <?php if ($userRating['created_at'] !== $userRating['updated_at']): ?>
                                            (Updated: <?php echo showCustomerTime($userRating['updated_at']); ?>)
                                        <?php endif; ?>
                                    </small>
                                </div>
                            </td>
                        </tr>
                        <?php endif; ?>
                    </table>
                </div>

                <div class="text-center mt-4 mb-5 pb-4">
                    <div class="ticket-buttons">

                        <!-- Rating Button - Only show for resolved/closed local tickets -->
                        <?php if (!$ticket['is_appika_ticket'] && in_array($ticket['status'], ['resolved', 'closed'])): ?>
                        <a href="rate-ticket.php?id=<?php echo $ticket['id']; ?>&from=ticket-detail"
                            class="btn btn-warning ticket-btn" style="background-color: #ffc107; color: white;">
                            <i class="fas fa-star"></i> &nbsp;
                            <?php echo $userRating ? 'View Rating' : 'Rate Ticket'; ?>
                        </a>
                        <?php endif; ?>

                        <a href="my-ticket.php" class="btn btn-secondary ticket-btn"
                            style="background-color: #473bf0; color: white;">Back to My Tickets</a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="../js/vendor.min.js"></script>
    <script src="../plugins/fancybox/jquery.fancybox.min.js"></script>
    <script src="../js/customer-timezone.js"></script>

    <script>
    $(document).ready(function() {
        // Initialize Fancybox for photo gallery
        $('[data-fancybox="ticket-gallery"]').fancybox({
            buttons: [
                "zoom",
                "slideShow",
                "fullScreen",
                "download",
                "thumbs",
                "close"
            ],
            loop: true,
            protect: true,
            animationEffect: "fade",
            transitionEffect: "slide",
            thumbs: {
                autoStart: true,
                hideOnClose: true
            },
            caption: function(instance, item) {
                return $(this).data('caption') || '';
            }
        });
    });

    // Update timezone display when detected
    document.addEventListener('customerTimezoneDetected', function(event) {
        const timezoneDisplay = document.getElementById('customer-timezone-display');
        if (timezoneDisplay) {
            timezoneDisplay.textContent = event.detail.timezone;
        }
        console.log('Customer timezone updated in ticket-detail:', event.detail.timezone);
    });

    // Also update on page load if timezone is already available
    document.addEventListener('DOMContentLoaded', function() {
        setTimeout(function() {
            if (window.CustomerTimezone) {
                const timezone = window.CustomerTimezone.getCustomerTimezone();
                const timezoneDisplay = document.getElementById('customer-timezone-display');
                if (timezoneDisplay && timezone) {
                    timezoneDisplay.textContent = timezone;
                }
            }
        }, 1000);
    });
    </script>

</body>

</html>
