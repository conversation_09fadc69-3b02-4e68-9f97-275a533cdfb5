<?php
session_start();
require_once("../functions/server.php");

// Set content type to JSON
header('Content-Type: application/json');

// Check if request method is POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['error' => 'Invalid request method']);
    exit();
}

// Get email from POST data
$email = isset($_POST['email']) ? trim($_POST['email']) : '';

// Validate email
if (empty($email)) {
    echo json_encode(['error' => 'Email is required']);
    exit();
}

if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
    echo json_encode(['error' => 'Invalid email format']);
    exit();
}

try {
    // Check if email exists in user table
    $stmt = $conn->prepare("SELECT id, email, username FROM user WHERE email = ? LIMIT 1");
    $stmt->bind_param("s", $email);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        // Email exists
        $user = $result->fetch_assoc();
        echo json_encode([
            'exists' => true,
            'message' => 'Email found in our system',
            'user_id' => $user['id']
        ]);
    } else {
        // Email doesn't exist
        echo json_encode([
            'exists' => false,
            'message' => 'Email not found - new customer'
        ]);
    }
    
    $stmt->close();
    
} catch (Exception $e) {
    error_log("Email check error: " . $e->getMessage());
    echo json_encode(['error' => 'Database error occurred']);
}

$conn->close();
?>
