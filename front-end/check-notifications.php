<?php
// Include database connection
include('../functions/server.php');

// Check if user is logged in
session_start();
if (!isset($_SESSION['user_id'])) {
    echo json_encode(['error' => 'Not authenticated']);
    exit;
}

$user_id = intval($_SESSION['user_id']);

// Count unread messages from admin
$unread_messages_query = "SELECT COUNT(*) as count FROM chat_messages WHERE sender_type = 'admin' AND is_read = 0 AND ticket_id IN (SELECT id FROM support_tickets WHERE user_id = $user_id)";
$unread_messages_result = mysqli_query($conn, $unread_messages_query);
$unread_messages_count = mysqli_fetch_assoc($unread_messages_result)['count'];

// Return JSON response
header('Content-Type: application/json');
echo json_encode([
    'unread_messages' => (int)$unread_messages_count
]);
?>
