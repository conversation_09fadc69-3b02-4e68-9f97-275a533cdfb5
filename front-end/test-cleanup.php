<?php
session_start();
include_once('../functions/ticket-expiration-functions.php');

echo "<h2>Testing Automatic Cleanup</h2>";

// Check if automatic cleanup is enabled
$settings = getExpirationSettings();
echo "<p>Automatic Cleanup Enabled: " . ($settings['auto_cleanup_enabled'] ? 'YES' : 'NO') . "</p>";

// Run cleanup manually
echo "<h3>Running Cleanup...</h3>";
$result = runAutomaticCleanup();
echo "<p>Cleanup Result: " . ($result ? 'SUCCESS' : 'FAILED') . "</p>";

// Check user tickets after cleanup
if (isset($_SESSION['username'])) {
    $username = $_SESSION['username'];
    include_once('../functions/db_connection.php');
    
    $stmt = $pdo->prepare("SELECT starter_tickets, business_tickets, ultimate_tickets FROM users WHERE username = ?");
    $stmt->execute([$username]);
    $user = $stmt->fetch();
    
    echo "<h3>User Tickets After Cleanup:</h3>";
    echo "<p>Starter: " . $user['starter_tickets'] . "</p>";
    echo "<p>Business: " . $user['business_tickets'] . "</p>";
    echo "<p>Ultimate: " . $user['ultimate_tickets'] . "</p>";
}

echo "<p><a href='my-ticket.php'>Back to My Tickets</a></p>";
?>
