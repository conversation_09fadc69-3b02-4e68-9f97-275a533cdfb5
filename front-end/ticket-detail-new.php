<?php
include('../functions/server.php');
include('../functions/timezone-helper.php');
require_once '../functions/graphql_functions.php'; // Include GraphQL functions for Appika API
session_start();

if (!isset($_SESSION['username'])) {
    header("Location: ../index.php");
    exit();
}

$username = $_SESSION['username'];

// Get ticket ID from URL - support both local ID and Appika ID
$ticketId = isset($_GET['id']) ? (int) $_GET['id'] : 0;
$appikaId = isset($_GET['appika_id']) ? (int) $_GET['appika_id'] : 0;

// Determine which type of ID we're working with
$isAppikaTicket = ($appikaId > 0);
$currentTicketId = $isAppikaTicket ? $appikaId : $ticketId;

if ($currentTicketId <= 0) {
    echo "Invalid ticket ID.";
    exit();
}

// Get user data
$userQuery = "SELECT * FROM user WHERE username = '$username'";
$userResult = mysqli_query($conn, $userQuery);
$user = mysqli_fetch_assoc($userResult);
$userId = $user['id'] ?? 0;
$userEmail = $user['email'] ?? '';

$ticket = null;
$userRating = null;

if ($isAppikaTicket) {
    // Fetch ticket from Appika API
    $query = '
    query GetTicket($id: Int!) {
        getTicket(id: $id) {
            id
            ticket_no
            contact_id
            agent_id
            req_email
            subject
            type
            type_name
            priority
            status
            created
            updated
            ticketMsg {
                id
                message
                created_at
            }
            contacts {
                id
                email
                fname
                status
            }
        }
    }';
    
    $variables = ['id' => $currentTicketId];
    $result = makeGraphQLRequest($query, $variables);
    
    if ($result['success'] && isset($result['data']['data']['getTicket'])) {
        $appikaTicket = $result['data']['data']['getTicket'];
        
        // Check if this ticket belongs to the current user
        if (strtolower($appikaTicket['req_email']) !== strtolower($userEmail)) {
            echo "Ticket not found or you don't have permission to view it.";
            exit();
        }
        
        // Convert Appika ticket to local format for compatibility
        $typeMapping = [1 => 'starter', 2 => 'premium', 3 => 'ultimate'];
        $ticket = [
            'id' => $appikaTicket['id'],
            'appika_id' => $appikaTicket['id'],
            'ticket_no' => $appikaTicket['ticket_no'],
            'subject' => $appikaTicket['subject'],
            'description' => isset($appikaTicket['ticketMsg'][0]['message']) ? $appikaTicket['ticketMsg'][0]['message'] : '',
            'priority' => ucfirst(strtolower($appikaTicket['priority'])),
            'ticket_type' => $typeMapping[$appikaTicket['type']] ?? 'starter',
            'status' => strtolower($appikaTicket['status']),
            'created_at' => $appikaTicket['created'],
            'updated_at' => $appikaTicket['updated'],
            'admin_name' => null, // Not available from Appika API
            'is_appika_ticket' => true
        ];
    } else {
        echo "Ticket not found or API error: " . ($result['error'] ?? 'Unknown error');
        exit();
    }
    
    // For Appika tickets, we don't have local rating data
    $userRating = null;
    
} else {
    // Fetch ticket from local database (legacy support)
    $sql = "SELECT st.*, au.username AS admin_name
            FROM support_tickets st
            LEFT JOIN admin_users au ON st.assigned_admin_id = au.id
            WHERE st.id = $ticketId AND st.user_id = $userId
            LIMIT 1";
    
    $result = mysqli_query($conn, $sql);
    $ticket = mysqli_fetch_assoc($result);
    
    if (!$ticket) {
        echo "Ticket not found or you don't have permission to view it.";
        exit();
    }
    
    $ticket['is_appika_ticket'] = false;
    
    // Check if user has rated this ticket (only for local tickets)
    $ratingQuery = "SELECT * FROM ticket_ratings WHERE ticket_id = $ticketId AND user_id = $userId";
    $ratingResult = mysqli_query($conn, $ratingQuery);
    $userRating = mysqli_fetch_assoc($ratingResult);
}
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <title>My Support Tickets</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link rel="shortcut icon" href="../image/png/favicon.png" type="image/x-icon">
    <!-- Bootstrap and plugins -->
    <link rel="stylesheet" href="../css/bootstrap.css">
    <link rel="stylesheet" href="../css/main.css">
    <link rel="shortcut icon" href="../image/png/favicon.png" type="image/x-icon">
    <!-- Bootstrap, fonts & icons -->
    <link rel="stylesheet" href="../css/bootstrap.css">
    <link rel="stylesheet" href="../fonts/icon-font/css/style.css">
    <link rel="stylesheet" href="../fonts/typography-font/typo.css">
    <link rel="stylesheet" href="../fonts/fontawesome-5/css/all.css">
    <!-- Plugin'stylesheets -->
    <link rel="stylesheet" href="../plugins/aos/aos.min.css">
    <link rel="stylesheet" href="../plugins/fancybox/jquery.fancybox.min.css">
    <link rel="stylesheet" href="../plugins/nice-select/nice-select.min.css">
    <link rel="stylesheet" href="../plugins/slick/slick.min.css">
    <link rel="stylesheet" href="../plugins/date-picker/css/gijgo.min.css" type="text/css" />
    <!-- Vendor stylesheets -->
    <link rel="stylesheet" href="../plugins/theme-mode-switcher/switcher-panel.css">
    <link rel="stylesheet" href="../css/main.css">
    <link rel="stylesheet" href="../css/theme-mode-custom.css">
    <style>
    body {
        padding-top: 200px;
        padding-bottom: 100px;
    }

    .badge {
        padding: 6.8px;
        font-size: 16px;
    }

    .badge-open {
        background-color: #fd7e14;
        color: #fff;
    }

    .badge-in_progress {
        background-color: #0d6efd;
        color: #fff;
    }

    .badge-resolved {
        background-color: #28a745;
        color: #fff;
    }

    .badge-closed {
        background-color: #6c757d;
        color: #fff;
    }

    /* Table styles */
    .table-responsive {
        overflow-x: auto;
    }

    .table td.label {
        font-weight: bold;
        width: 30%;
        background-color: #f8f9fa;
    }

    /* Button styles */
    .ticket-buttons {
        display: flex;
        justify-content: center;
        gap: 15px;
        flex-wrap: wrap;
    }

    .ticket-btn {
        min-width: 180px;
        padding: 10px 20px;
        font-size: 16px;
        text-align: center;
        border-radius: 4px;
        transition: all 0.3s ease;
    }

    /* Responsive styles */
    @media (max-width: 991px) {
        body {
            padding-top: 150px;
            padding-bottom: 80px;
        }

        .table td.label {
            width: 35%;
        }
