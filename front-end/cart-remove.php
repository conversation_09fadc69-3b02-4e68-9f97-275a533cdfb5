<?php
include('../functions/server.php');
session_start();

// Set header to return JSON response
header('Content-Type: application/json');

// ตรวจสอบว่าผู้ใช้ล็อกอินหรือไม่
$user_id = isset($_SESSION['user_id']) ? $_SESSION['user_id'] : null;

// ตรวจสอบว่ามีการส่งค่า cart_item_id มาหรือไม่
if (isset($_POST['cart_item_id'])) {
    $cart_item_id = $_POST['cart_item_id'];
    $remove_quantity = isset($_POST['remove_quantity']) ? intval($_POST['remove_quantity']) : 0;
    $response = array();

    // ตรวจสอบว่าจำนวนที่ต้องการลบถูกต้องหรือไม่
    if ($remove_quantity <= 0) {
        echo json_encode([
            'status' => 'error',
            'message' => 'Invalid quantity to remove.'
        ]);
        exit;
    }

    if ($user_id) {
        // สำหรับผู้ใช้ที่ล็อกอิน
        // ดึงข้อมูลสินค้าในตะกร้าปัจจุบัน
        $query = "SELECT id, quantity FROM cart_items WHERE id = ? AND cart_id IN (SELECT cart_id FROM cart WHERE user_id = ? AND status = 'active')";
        $stmt = $conn->prepare($query);
        $stmt->bind_param("ii", $cart_item_id, $user_id);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($result->num_rows > 0) {
            $item = $result->fetch_assoc();
            $current_quantity = $item['quantity'];

            // ถ้าจำนวนที่ต้องการลบมากกว่าหรือเท่ากับจำนวนปัจจุบัน ให้ลบรายการทั้งหมด
            if ($remove_quantity >= $current_quantity) {
                $query = "DELETE FROM cart_items WHERE id = ? AND cart_id IN (SELECT cart_id FROM cart WHERE user_id = ? AND status = 'active')";
                $stmt = $conn->prepare($query);
                $stmt->bind_param("ii", $cart_item_id, $user_id);
                $stmt->execute();

                if ($stmt->affected_rows > 0) {
                    $response['status'] = 'success';
                    $response['message'] = 'Item removed successfully.';
                } else {
                    $response['status'] = 'error';
                    $response['message'] = 'Error removing item.';
                }
            } else {
                // ถ้าจำนวนที่ต้องการลบน้อยกว่าจำนวนปัจจุบัน ให้ลดจำนวนลง
                $new_quantity = $current_quantity - $remove_quantity;
                $query = "UPDATE cart_items SET quantity = ? WHERE id = ? AND cart_id IN (SELECT cart_id FROM cart WHERE user_id = ? AND status = 'active')";
                $stmt = $conn->prepare($query);
                $stmt->bind_param("iii", $new_quantity, $cart_item_id, $user_id);
                $stmt->execute();

                if ($stmt->affected_rows > 0) {
                    $response['status'] = 'success';
                    $response['message'] = 'Quantity updated successfully.';
                    $response['new_quantity'] = $new_quantity;
                } else {
                    $response['status'] = 'error';
                    $response['message'] = 'Error updating quantity.';
                }
            }
        } else {
            $response['status'] = 'error';
            $response['message'] = 'Item not found in cart.';
        }
    } else {
        // สำหรับผู้ใช้ที่ไม่ได้ล็อกอิน (guest)
        $itemUpdated = false;

        if (isset($_SESSION['guest_cart']) && !empty($_SESSION['guest_cart'])) {
            // Debug information
            error_log('Guest cart before removal: ' . print_r($_SESSION['guest_cart'], true));
            error_log('Attempting to remove cart_item_id: ' . $cart_item_id);

            foreach ($_SESSION['guest_cart'] as $key => $item) {
                error_log('Checking item with cart_item_id: ' . (isset($item['cart_item_id']) ? $item['cart_item_id'] : 'undefined'));

                if (isset($item['cart_item_id']) && $item['cart_item_id'] == $cart_item_id) {
                    $current_quantity = $item['quantity'];
                    error_log('Found matching item with quantity: ' . $current_quantity);

                    // ถ้าจำนวนที่ต้องการลบมากกว่าหรือเท่ากับจำนวนปัจจุบัน ให้ลบรายการทั้งหมด
                    if ($remove_quantity >= $current_quantity) {
                        unset($_SESSION['guest_cart'][$key]);
                        // Reindex the array to avoid gaps
                        $_SESSION['guest_cart'] = array_values($_SESSION['guest_cart']);
                        $response['status'] = 'success';
                        $response['message'] = 'Item removed successfully.';
                        error_log('Item completely removed from guest cart');
                    } else {
                        // ถ้าจำนวนที่ต้องการลบน้อยกว่าจำนวนปัจจุบัน ให้ลดจำนวนลง
                        $_SESSION['guest_cart'][$key]['quantity'] = $current_quantity - $remove_quantity;
                        $response['status'] = 'success';
                        $response['message'] = 'Quantity updated successfully.';
                        $response['new_quantity'] = $_SESSION['guest_cart'][$key]['quantity'];
                        error_log('Item quantity updated to: ' . $_SESSION['guest_cart'][$key]['quantity']);
                    }

                    $itemUpdated = true;
                    break;
                }
            }

            // Debug information after removal
            error_log('Guest cart after removal: ' . print_r($_SESSION['guest_cart'], true));
        } else {
            error_log('Guest cart is not set or empty');
        }

        if (!$itemUpdated) {
            $response['status'] = 'error';
            $response['message'] = 'Item not found in guest cart.';
        }
    }

    // Return JSON response
    echo json_encode($response);
} else {
    // No cart_item_id provided
    echo json_encode([
        'status' => 'error',
        'message' => 'No item ID provided.'
    ]);
}
