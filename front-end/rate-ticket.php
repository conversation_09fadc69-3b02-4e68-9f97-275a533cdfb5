<?php
include('../functions/server.php');
include('../functions/timezone-helper.php');
session_start();

if (!isset($_SESSION['username'])) {
    header("Location: ../index.php");
    exit();
}

$username = $_SESSION['username'];

// Get ticket ID from URL
$ticketId = isset($_GET['id']) ? (int) $_GET['id'] : 0;
$appikaId = isset($_GET['appika_id']) ? (int) $_GET['appika_id'] : 0;
$isAppikaTicket = ($appikaId > 0);
$currentTicketId = $isAppikaTicket ? $appikaId : $ticketId;

if ($currentTicketId <= 0) {
    header("Location: my-ticket.php");
    exit();
}

// Get referrer page to determine back button destination
$from = isset($_GET['from']) ? $_GET['from'] : 'my-ratings';

// Determine back URL and button text based on referrer
switch($from) {
    case 'my-ticket':
        $backUrl = 'my-ticket.php';
        $backText = 'Back to My Tickets';
        break;
    case 'ticket-detail':
        $backUrl = 'ticket-detail.php?id=' . $ticketId;
        $backText = 'Back to Ticket Details';
        break;
    case 'my-ratings':
    default:
        $backUrl = 'my-ratings.php';
        $backText = 'Back to Ratings';
        break;
}

// Get user ID from username
$userQuery = "SELECT id FROM user WHERE username = '$username'";
$userResult = mysqli_query($conn, $userQuery);
$user = mysqli_fetch_assoc($userResult);
$userId = $user['id'] ?? 0;

// Get ticket information
if ($isAppikaTicket) {
    // Fetch ticket from Appika API
    require_once '../functions/graphql_functions.php';
    $query = '
    query GetTicket($id: Int!) {
        getTicket(id: $id) {
            id
            ticket_no
            req_email
            subject
            type
            priority
            status
            created
            updated
        }
    }';
    $variables = ['id' => $currentTicketId];
    $result = makeGraphQLRequest($query, $variables);
    if ($result['success'] && isset($result['data']['data']['getTicket'])) {
        $appikaTicket = $result['data']['data']['getTicket'];
        $ticket = [
            'id' => $appikaTicket['id'],
            'subject' => $appikaTicket['subject'],
            'ticket_type' => $appikaTicket['type'],
            'status' => strtolower($appikaTicket['status']),
            'created_at' => $appikaTicket['created'],
            'updated_at' => $appikaTicket['updated'] ?? $appikaTicket['created'],
            'is_appika_ticket' => true
        ];
    } else {
        header("Location: my-ticket.php");
        exit();
    }
    // Check if user has already rated this Appika ticket
    $existingRatingQuery = "SELECT * FROM ticket_ratings WHERE appika_ticket_id = $currentTicketId AND user_id = $userId AND is_appika_ticket = 1";
    $existingRatingResult = mysqli_query($conn, $existingRatingQuery);
    $existingRating = mysqli_fetch_assoc($existingRatingResult);
} else {
    // Get ticket information
    $sql = "SELECT st.*, au.username AS admin_name
            FROM support_tickets st
            LEFT JOIN admin_users au ON st.assigned_admin_id = au.id
            WHERE st.id = $ticketId AND st.user_id = $userId
            LIMIT 1";

    $result = mysqli_query($conn, $sql);

    if (!$result || mysqli_num_rows($result) == 0) {
        header("Location: my-ticket.php");
        exit();
    }

    $ticket = mysqli_fetch_assoc($result);

    // Check if ticket can be rated (only resolved or closed)
    if (!in_array($ticket['status'], ['resolved', 'closed'])) {
        header("Location: ticket-detail.php?id=" . $ticketId);
        exit();
    }

    // Check if user has already rated this ticket
    $existingRatingQuery = "SELECT * FROM ticket_ratings WHERE ticket_id = $ticketId AND user_id = $userId";
    $existingRatingResult = mysqli_query($conn, $existingRatingQuery);
    $existingRating = mysqli_fetch_assoc($existingRatingResult);
}

// If user has already rated, redirect to view-only page or show read-only display
if ($existingRating) {
    // User has already rated, show read-only view
    $isReadOnly = true;
} else {
    $isReadOnly = false;
}

// Format dates in user timezone
$createdDate = showCustomerTime($ticket['created_at'] ?? null);
$updatedDate = showCustomerTime($ticket['updated_at'] ?? $ticket['created_at'] ?? null);
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <title><?php echo $isReadOnly ? 'View Rating for' : 'Rate'; ?> Ticket #<?php echo $ticket['id']; ?> - HelloIT Support</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link rel="shortcut icon" href="../image/png/favicon.png" type="image/x-icon">
    <!-- Bootstrap and plugins -->
    <link rel="stylesheet" href="../css/bootstrap.css">
    <link rel="stylesheet" href="../css/main.css">
    <link rel="stylesheet" href="../fonts/icon-font/css/style.css">
    <link rel="stylesheet" href="../fonts/typography-font/typo.css">
    <link rel="stylesheet" href="../fonts/fontawesome-5/css/all.css">
    <link rel="stylesheet" href="../plugins/aos/aos.min.css">
    <link rel="stylesheet" href="../plugins/fancybox/jquery.fancybox.min.css">
    <link rel="stylesheet" href="../plugins/nice-select/nice-select.min.css">
    <link rel="stylesheet" href="../plugins/slick/slick.min.css">
    <link rel="stylesheet" href="../plugins/date-picker/css/gijgo.min.css" type="text/css" />
    <link rel="stylesheet" href="../plugins/theme-mode-switcher/switcher-panel.css">
    <link rel="stylesheet" href="../css/theme-mode-custom.css">
    
    <style>
        body {
            padding-top: 140px;
            background-color: #F4F7FA;
        }

        @media (max-width: 767px) {
            body {
                margin-top: -20px;
            }
        }

        .rating-container {
            background-color: #fff;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
        }

        .star-rating {
            display: flex;
            justify-content: center;
            margin: 20px 0;
            gap: 10px;
        }

        .star {
            cursor: pointer;
            transition: color 0.2s ease;
            user-select: none;
        }

        .star i {
            font-size: 3rem;
            color: #ddd;
        }

        .star:hover i,
        .star.active i {
            color: #ffc107;
        }

        .star.hover i {
            color: #ffeb3b;
        }

        .ticket-info {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .status-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
        }

        .status-resolved {
            background-color: #28a745;
            color: white;
        }

        .status-closed {
            background-color: #6c757d;
            color: white;
        }

        .btn-submit {
            background-color: #473BF0;
            border-color: #473BF0;
            color: white;
            padding: 12px 30px;
            font-size: 16px;
            border-radius: 25px;
            transition: all 0.3s ease;
        }

        .btn-submit:hover {
            background-color: #3d32d9;
            border-color: #3d32d9;
            color: white;
            transform: translateY(-2px);
        }

        .btn-secondary {
            background-color: #6c757d;
            border-color: #6c757d;
            color: white;
            padding: 12px 30px;
            font-size: 16px;
            border-radius: 25px;
        }

        .rating-form {
            max-width: 600px;
            margin: 0 auto;
        }

        .form-group label {
            font-weight: 600;
            color: #333;
        }

        .form-control {
            border-radius: 8px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
        }

        .form-control:focus {
            border-color: #473BF0;
            box-shadow: 0 0 0 0.2rem rgba(71, 59, 240, 0.25);
        }

        .alert {
            border-radius: 8px;
            border: none;
        }

        .existing-rating {
            background-color: #e7f3ff;
            border: 2px solid #007bff;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
        }

        /* Menu open state for mobile */
        body.menu-open {
            overflow: hidden;
        }

        /* Container width to match other pages */
        .container {
            width: 1500px;
            max-width: 95%;
        }

        /* Make site-wrapper wider */
        .site-wrapper {
            max-width: 100% !important;
            width: 100% !important;
            overflow-x: visible !important;
        }

        /* Header with back button styling */
        .rating-header {
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 1.5rem;
        }

        .rating-header .btn {
            position: absolute;
            left: 0;
            flex-shrink: 0;
        }

        .rating-header h2 {
            margin: 0;
            text-align: center;
        }

        /* Back button specific sizing */
        .back-btn-40 {
            width: 40px !important;
            height: 40px !important;
            padding: 0 !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            min-width: 40px !important;
            min-height: 40px !important;
            max-width: 40px !important;
            max-height: 40px !important;
        }

        /* Font size adjustments */
        h5 {
            font-size: 16px !important;
        }

        p, span, small, label, .form-control, .btn, .text-muted, .alert, div:not(.rating-container):not(.ticket-info):not(.existing-rating):not(.rating-header) {
            font-size: 14px !important;
        }

        .form-text {
            font-size: 14px !important;
        }

        .status-badge {
            font-size: 14px !important;
        }

        @media (max-width: 767px) {
            .rating-container {
                padding: 20px 15px;
            }

            .star i {
                font-size: 3rem !important;
            }

            .star-rating {
                gap: 5px;
            }

            .rating-header {
                flex-direction: column;
                align-items: center;
                gap: 15px;
                justify-content: flex-start;
            }

            .rating-header .btn {
                position: static;
                align-self: flex-start;
            }

            .rating-header h2 {
                font-size: 1.5rem;
                text-align: center !important;
            }
        }
    </style>
</head>

<body data-theme="light">
    <?php include('../header-footer/header.php'); ?>

    <div class="container">
        <div class="row">
            <!-- Sidebar - Hidden on mobile, visible on larger screens -->
            <div class="col-lg-3 col-md-4 d-none d-md-block mb-4">
                <?php include('user-menu.php'); ?>
            </div>

            <!-- Mobile menu - Visible only on mobile -->
            <div class="col-12 d-md-none mb-4">
                <?php include('user-menu.php'); ?>
            </div>

            <!-- Main Content -->
            <div class="col-lg-9 col-md-8">
                <div class="rating-container">
                    <!-- Header with back button for all views -->
                    <div class="rating-header">
                        <a href="<?php echo $backUrl; ?>" class="btn btn-outline-primary back-btn-40 mr-3">
                            <i class="fas fa-arrow-left"></i>
                        </a>
                        <h2 class="mb-0">
                            <i class="fas fa-star text-warning"></i>
                            <?php echo $isReadOnly ? 'Your Rating for This Ticket' : 'Rate Your Support Experience'; ?>
                        </h2>
                    </div>

                    <!-- Ticket Information -->
                    <div class="ticket-info">
                        <div class="row">
                            <div class="col-md-8">
                                <h5><strong>Ticket #<?php echo $ticket['id']; ?>:</strong> <?php echo htmlspecialchars($ticket['subject']); ?></h5>
                                <p class="mb-1"><strong>Type:</strong> <?php echo ucfirst($ticket['ticket_type']); ?></p>
                                <p class="mb-0"><strong>Created:</strong> <?php echo $createdDate; ?></p>
                            </div>
                            <div class="col-md-4 text-md-right">
                                <span class="status-badge status-<?php echo $ticket['status']; ?>">
                                    <?php echo ucfirst($ticket['status']); ?>
                                </span>
                            </div>
                        </div>
                    </div>

                    <?php if ($existingRating): ?>
                    <!-- Show existing rating -->
                    <div class="existing-rating">
                        <h5><i class="fas fa-check-circle text-success"></i> Your Rating</h5>
                        <div class="d-flex align-items-center mb-2">
                            <span class="mr-2">Rating:</span>
                            <?php for ($i = 1; $i <= 5; $i++): ?>
                                <i class="fas fa-star <?php echo $i <= $existingRating['rating'] ? 'text-warning' : 'text-muted'; ?>"></i>
                            <?php endfor; ?>
                            <span class="ml-2">(<?php echo $existingRating['rating']; ?>/5 stars)</span>
                        </div>
                        <?php if (!empty($existingRating['comment'])): ?>
                        <p class="mb-2"><strong>Your Comment:</strong> <?php echo htmlspecialchars($existingRating['comment']); ?></p>
                        <?php endif; ?>
                        <small class="text-muted">Rated on: <?php echo showCustomerTime($existingRating['created_at']); ?></small>
                        <div class="mt-3 p-3" style="background-color: #d4edda; border: 1px solid #c3e6cb; border-radius: 5px;">
                            <i class="fas fa-info-circle text-success"></i>
                            <strong>Thank you for your feedback!</strong> Your rating has been recorded and cannot be changed.
                        </div>

                        <!-- View All Reviews Button - Only show when coming from ticket-detail -->
                        <?php if ($from === 'ticket-detail'): ?>
                        <div class="text-center mt-3">
                            <a href="my-ratings.php" class="btn btn-outline-primary">
                                <i class="fas fa-list"></i> &nbsp;
                                View All Reviews
                            </a>
                        </div>
                        <?php endif; ?>
                    </div>
                    <?php endif; ?>

                    <!-- Rating Form - Only show if no existing rating -->
                    <?php if (!$existingRating): ?>
                    <form id="ratingForm" class="rating-form">
                        <input type="hidden" id="ticketId" value="<?php echo $ticketId; ?>">
                        
                        <!-- Star Rating -->
                        <div class="form-group text-center">
                            <label for="rating">How would you rate our support?</label>
                            <div class="star-rating" id="starRating">
                                <span class="star" data-rating="1"><i class="fas fa-star"></i></span>
                                <span class="star" data-rating="2"><i class="fas fa-star"></i></span>
                                <span class="star" data-rating="3"><i class="fas fa-star"></i></span>
                                <span class="star" data-rating="4"><i class="fas fa-star"></i></span>
                                <span class="star" data-rating="5"><i class="fas fa-star"></i></span>
                            </div>
                            <input type="hidden" id="rating" name="rating" value="<?php echo $existingRating['rating'] ?? ''; ?>">
                            <div id="ratingText" class="text-muted mt-2">
                                <?php if ($existingRating): ?>
                                    <?php echo $existingRating['rating']; ?> out of 5 stars
                                <?php else: ?>
                                    Click on stars to rate
                                <?php endif; ?>
                            </div>
                        </div>

                        <!-- Comment -->
                        <div class="form-group">
                            <label for="comment">Additional Comments (Optional)</label>
                            <textarea class="form-control" id="comment" name="comment" rows="4" 
                                placeholder="Tell us about your experience with our support team..."
                                maxlength="1000"><?php echo htmlspecialchars($existingRating['comment'] ?? ''); ?></textarea>
                            <small class="form-text text-muted">Maximum 1000 characters</small>
                        </div>

                        <!-- Submit Buttons -->
                        <div class="text-center">
                            <button type="submit" class="btn btn-submit">
                                <i class="fas fa-paper-plane"></i> &nbsp;
                                Submit Rating
                            </button>
                        </div>
                    </form>

                    <!-- View All Reviews Button - Only show when coming from ticket-detail -->
                    <?php if ($from === 'ticket-detail'): ?>
                    <div class="text-center mt-3">
                        <a href="my-ratings.php" class="btn btn-outline-secondary">
                            <i class="fas fa-list"></i> &nbsp;
                            View All Reviews
                        </a>
                    </div>
                    <?php endif; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Success/Error Alert -->
    <div id="alertContainer" style="position: fixed; top: 100px; right: 20px; z-index: 9999;"></div>

    <script src="../js/vendor.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Only initialize rating form if it exists (not in read-only mode)
            const form = document.getElementById('ratingForm');
            if (!form) {
                return; // Exit if no form (read-only mode)
            }

            const stars = document.querySelectorAll('.star');
            const ratingInput = document.getElementById('rating');
            const ratingText = document.getElementById('ratingText');
            
            // Star hover and click events
            stars.forEach(star => {
                star.addEventListener('mouseover', function() {
                    const rating = parseInt(this.dataset.rating);
                    highlightStars(rating, 'hover');
                });
                
                star.addEventListener('mouseout', function() {
                    const currentRating = parseInt(ratingInput.value) || 0;
                    highlightStars(currentRating, 'active');
                });
                
                star.addEventListener('click', function() {
                    const rating = parseInt(this.dataset.rating);
                    setRating(rating);
                });
            });
            
            function highlightStars(rating, className) {
                stars.forEach((star, index) => {
                    star.classList.remove('active', 'hover');
                    if (index < rating) {
                        star.classList.add(className);
                    }
                });
            }
            
            function setRating(rating) {
                ratingInput.value = rating;
                highlightStars(rating, 'active');
                
                const ratingTexts = ['', 'Poor', 'Fair', 'Good', 'Very Good', 'Excellent'];
                ratingText.textContent = `${rating} out of 5 stars - ${ratingTexts[rating]}`;
            }
            
            // Form submission
            form.addEventListener('submit', function(e) {
                e.preventDefault();
                
                const rating = parseInt(ratingInput.value);
                if (!rating || rating < 1 || rating > 5) {
                    showAlert('Please select a rating between 1 and 5 stars.', 'danger');
                    return;
                }
                
                const formData = new FormData();
                formData.append('ticket_id', <?php echo $isAppikaTicket ? 0 : $ticketId; ?>);
                formData.append('appika_ticket_id', <?php echo $isAppikaTicket ? $currentTicketId : 0; ?>);
                formData.append('is_appika_ticket', <?php echo $isAppikaTicket ? 1 : 0; ?>);
                formData.append('rating', rating);
                formData.append('comment', document.getElementById('comment').value);
                
                // Disable submit button
                const submitBtn = form.querySelector('button[type="submit"]');
                const originalText = submitBtn.innerHTML;
                submitBtn.disabled = true;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Submitting...';
                
                fetch('../functions/submit-rating.php', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showAlert(data.message, 'success');
                        setTimeout(() => {
                            window.location.href = '<?php echo $backUrl; ?>';
                        }, 2000);
                    } else {
                        showAlert(data.error || 'An error occurred while submitting your rating.', 'danger');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showAlert('An error occurred while submitting your rating.', 'danger');
                })
                .finally(() => {
                    // Re-enable submit button
                    submitBtn.disabled = false;
                    submitBtn.innerHTML = originalText;
                });
            });
            
            function showAlert(message, type) {
                const alertContainer = document.getElementById('alertContainer');
                const alertDiv = document.createElement('div');
                alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
                alertDiv.innerHTML = `
                    ${message}
                    <button type="button" class="close" data-dismiss="alert">
                        <span>&times;</span>
                    </button>
                `;
                
                alertContainer.appendChild(alertDiv);
                
                // Auto-dismiss after 5 seconds
                setTimeout(() => {
                    if (alertDiv.parentNode) {
                        alertDiv.remove();
                    }
                }, 5000);
            }
        });
    </script>
</body>

</html>
