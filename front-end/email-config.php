<?php
// Email Configuration
// You can modify these settings based on your email provider

return [
    // SMTP Settings (for PHPMailer)
    'smtp_host' => 'smtp.gmail.com',
    'smtp_port' => 587,
    'smtp_username' => '<EMAIL>', // Change this to your email
    'smtp_password' => 'your-app-password',    // Change this to your app password
    'smtp_encryption' => 'tls', // or 'ssl'
    
    // Email Recipients
    'to_email' => '<EMAIL>',
    'to_name' => 'HelloIT Support',
    
    // Email Settings
    'from_name' => 'HelloIT Contact Form',
    'subject_prefix' => 'Contact Form Submission: ',
    
    // Other providers examples:
    /*
    // For Outlook/Hotmail
    'smtp_host' => 'smtp-mail.outlook.com',
    'smtp_port' => 587,
    
    // For Yahoo
    'smtp_host' => 'smtp.mail.yahoo.com',
    'smtp_port' => 587,
    
    // For custom hosting
    'smtp_host' => 'mail.yourdomain.com',
    'smtp_port' => 587,
    */
];
?>