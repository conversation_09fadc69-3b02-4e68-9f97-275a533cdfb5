# 📧 Email Setup Guide for Contact Form

## 🔧 Current Status

Your contact form now has a **fallback system**:

1. **First**: Tries to send email via PHP mail()
2. **Fallback**: Saves submissions to `contact-submissions.log` file

## 📁 Check Contact Submissions

If email fails, check the file: `front-end/contact-submissions.log`

## ⚙️ Option 1: Configure XAMPP for Gmail SMTP (Recommended)

### Step 1: Edit php.ini

1. Open `C:\xampp\php\php.ini`
2. Find and update these lines:

```ini
[mail function]
SMTP = smtp.gmail.com
smtp_port = 587
sendmail_from = <EMAIL>
sendmail_path = "\"C:\xampp\sendmail\sendmail.exe\" -t"
```

### Step 2: Edit sendmail.ini

1. Open `C:\xampp\sendmail\sendmail.ini`
2. Update these lines:

```ini
[sendmail]
smtp_server=smtp.gmail.com
smtp_port=587
error_logfile=error.log
debug_logfile=debug.log
auth_username=<EMAIL>
auth_password=your-app-password
force_sender=<EMAIL>
```

### Step 3: Get Gmail App Password

1. Go to Google Account settings
2. Enable 2-Factor Authentication
3. Generate an "App Password" for "Mail"
4. Use this password (not your regular Gmail password)

### Step 4: Restart XAMPP

Restart Apache after making changes.

## 🔧 Option 2: Use a Mail Testing Service

### Mailtrap (Free for testing)

1. Sign up at mailtrap.io
2. Get SMTP credentials
3. Update `email-config.php`:

```php
'smtp_host' => 'smtp.mailtrap.io',
'smtp_port' => 2525,
'smtp_username' => 'your-mailtrap-username',
'smtp_password' => 'your-mailtrap-password',
```

## 📝 Option 3: Development Mode (Current Setup)

For now, the contact form will:

- ✅ Show success message to users
- ✅ Save all submissions to `contact-submissions.log`
- ✅ Include all form data and user info
- ✅ Work perfectly for development/testing

## 🚀 For Production

When you deploy to a live server:

1. Most hosting providers have mail() function working
2. Or configure SMTP with your hosting provider
3. The contact form will automatically work

## 📧 Current Email Configuration

Check `front-end/email-config.php` to update:

- Recipient email (currently: <EMAIL>)
- SMTP settings
- Email subject prefix

## 🔍 Troubleshooting

1. **Check**: `contact-submissions.log` for saved messages
2. **Verify**: Email settings in `email-config.php`
3. **Test**: Try sending a test message
4. **Logs**: Check XAMPP error logs if needed

Your contact form is now working with a reliable fallback system!
