<?php
session_start();
include_once('../functions/server.php');
include_once('../functions/ticket-expiration-functions.php');

if (!isset($_SESSION['username'])) {
    die('Please login first');
}

$username = $_SESSION['username'];
echo "<h2>Debug Ticket Expiration Notifications</h2>";

echo "<h3>Current Configuration:</h3>";
$config = getTicketExpirationConfig();
echo "<pre>";
print_r($config);
echo "</pre>";

echo "<h3>User's Tickets:</h3>";
$query = "SELECT * FROM purchasetickets WHERE username = ? ORDER BY purchase_time DESC";
$stmt = mysqli_prepare($conn, $query);
mysqli_stmt_bind_param($stmt, 's', $username);
mysqli_stmt_execute($stmt);
$result = mysqli_stmt_get_result($stmt);

echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th>Purchase Time</th><th>Ticket Type</th><th>Remaining</th><th>Expiration Date</th><th>Status</th></tr>";

while ($row = mysqli_fetch_assoc($result)) {
    $status = 'Active';
    if ($row['expiration_date'] < date('Y-m-d H:i:s')) {
        $status = 'EXPIRED';
    } elseif ($row['remaining_tickets'] == 0) {
        $status = 'Used Up';
    }
    
    echo "<tr>";
    echo "<td>" . $row['purchase_time'] . "</td>";
    echo "<td>" . $row['ticket_type'] . "</td>";
    echo "<td>" . $row['remaining_tickets'] . "</td>";
    echo "<td>" . $row['expiration_date'] . "</td>";
    echo "<td style='color: " . ($status == 'EXPIRED' ? 'red' : 'green') . ";'>" . $status . "</td>";
    echo "</tr>";
}
echo "</table>";

echo "<h3>Tickets Expiring Soon:</h3>";
$expiring_tickets = getTicketsExpiringSoon($username);
echo "<p>Count: " . count($expiring_tickets) . "</p>";

if (!empty($expiring_tickets)) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>Purchase Time</th><th>Ticket Type</th><th>Remaining</th><th>Expiration Date</th></tr>";
    
    foreach ($expiring_tickets as $ticket) {
        echo "<tr>";
        echo "<td>" . $ticket['purchase_time'] . "</td>";
        echo "<td>" . $ticket['ticket_type'] . "</td>";
        echo "<td>" . $ticket['remaining_tickets'] . "</td>";
        echo "<td>" . $ticket['expiration_date'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>No tickets expiring soon.</p>";
}

echo "<h3>Expired Tickets:</h3>";
$expired_tickets = getExpiredTickets($username);
echo "<p>Count: " . count($expired_tickets) . "</p>";

if (!empty($expired_tickets)) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>Purchase Time</th><th>Ticket Type</th><th>Remaining</th><th>Expiration Date</th></tr>";
    
    foreach ($expired_tickets as $ticket) {
        echo "<tr>";
        echo "<td>" . $ticket['purchase_time'] . "</td>";
        echo "<td>" . $ticket['ticket_type'] . "</td>";
        echo "<td>" . $ticket['remaining_tickets'] . "</td>";
        echo "<td>" . $ticket['expiration_date'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>No expired tickets found.</p>";
}

echo "<h3>Current Time:</h3>";
echo "<p>" . date('Y-m-d H:i:s') . "</p>";

mysqli_stmt_close($stmt);
?>
