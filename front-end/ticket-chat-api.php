<?php
header('Content-Type: application/json');
include('../functions/server.php');
include('../functions/timezone-helper.php');
require_once '../functions/graphql_functions.php';
session_start();

// Check if user is logged in
if (!isset($_SESSION['username'])) {
    echo json_encode(['success' => false, 'error' => 'Not logged in']);
    exit();
}

$username = $_SESSION['username'];

// Get user data
$userQuery = "SELECT * FROM user WHERE username = '$username'";
$userResult = mysqli_query($conn, $userQuery);
$user = mysqli_fetch_assoc($userResult);
$userId = $user['id'] ?? 0;
$userEmail = $user['email'] ?? '';

// Check if request is POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'error' => 'Invalid request method']);
    exit();
}

$action = $_POST['action'] ?? '';
$ticketId = isset($_POST['ticket_id']) ? (int) $_POST['ticket_id'] : 0;
$isAppika = isset($_POST['is_appika']) ? (bool) $_POST['is_appika'] : false;

if ($ticketId <= 0) {
    echo json_encode(['success' => false, 'error' => 'Invalid ticket ID']);
    exit();
}

// Function to format date for display
function formatMessageDate($dateString) {
    if (empty($dateString)) {
        return 'Unknown time';
    }
    
    try {
        $date = new DateTime($dateString);
        return $date->format('M d, g:i A');
    } catch (Exception $e) {
        return htmlspecialchars($dateString);
    }
}

switch ($action) {
    case 'get_messages':
        // Get messages from Appika API ONLY - no local database support
        $query = '
        query GetTicketWithMessages($id: Int!) {
            getTicket(id: $id) {
                id
                req_email
                status
                ticketMsg {
                    message
                    creator_by_contact
                    creator_by_agent
                }
            }
        }';

        $variables = ['id' => $ticketId];
        $result = makeGraphQLRequest($query, $variables);

        if (!$result['success']) {
            echo json_encode(['success' => false, 'error' => $result['error'] ?? 'API error']);
            exit();
        }

        $ticketData = $result['data']['data']['getTicket'] ?? null;

        if (!$ticketData) {
            echo json_encode(['success' => false, 'error' => 'Ticket not found']);
            exit();
        }

        // Verify ticket belongs to user
        if (strtolower($ticketData['req_email']) !== strtolower($userEmail)) {
            echo json_encode(['success' => false, 'error' => 'Access denied']);
            exit();
        }

        // Format messages for display (from Appika API only - NO local database)
        $messages = [];
        if (isset($ticketData['ticketMsg']) && is_array($ticketData['ticketMsg'])) {
            foreach ($ticketData['ticketMsg'] as $index => $msg) {
                $messageText = $msg['message'] ?? '';

                // Skip empty messages
                if (empty(trim($messageText))) {
                    continue;
                }

                // Determine sender based on Appika's creator fields
                $creatorByContact = $msg['creator_by_contact'] ?? null;
                $creatorByAgent = $msg['creator_by_agent'] ?? null;

                if (!empty($creatorByContact)) {
                    // Message sent by contact/user
                    $senderType = 'user';
                    $senderName = 'You';
                } elseif (!empty($creatorByAgent)) {
                    // Message sent by agent/support
                    $senderType = 'admin';
                    $senderName = 'Support Agent';
                } else {
                    // Default to user if no creator info
                    $senderType = 'user';
                    $senderName = 'You';
                }

                $messages[] = [
                    'id' => $index,
                    'message' => $messageText,
                    'sender_type' => $senderType,
                    'sender_name' => $senderName,
                    'formatted_time' => 'Recently',
                    'created' => date('M d, g:i A'),
                    'creator_by_contact' => $creatorByContact,
                    'creator_by_agent' => $creatorByAgent
                ];
            }
        }

        echo json_encode(['success' => true, 'messages' => $messages]);
        break;
        
    case 'send_message':
        $message = isset($_POST['message']) ? trim($_POST['message']) : '';

        if (empty($message)) {
            echo json_encode(['success' => false, 'error' => 'Message cannot be empty']);
            exit();
        }

        // Send message via Appika API using updateTicket with reply_msg (the correct way)
        // First, get the current ticket data to preserve existing values
        $verifyQuery = '
        query GetTicket($id: Int!) {
            getTicket(id: $id) {
                id
                req_email
                subject
                type
                type_name
                priority
                status
                contact_id
            }
        }';

        $verifyResult = makeGraphQLRequest($verifyQuery, ['id' => $ticketId]);

        if (!$verifyResult['success']) {
            echo json_encode(['success' => false, 'error' => 'Failed to verify ticket']);
            exit();
        }

        $ticketData = $verifyResult['data']['data']['getTicket'] ?? null;

        if (!$ticketData) {
            echo json_encode(['success' => false, 'error' => 'Ticket not found']);
            exit();
        }

        // Verify ticket belongs to user
        if (strtolower($ticketData['req_email']) !== strtolower($userEmail)) {
            echo json_encode(['success' => false, 'error' => 'Access denied']);
            exit();
        }

        // Check if ticket is closed
        if (in_array(strtolower($ticketData['status']), ['closed', 'resolved'])) {
            echo json_encode(['success' => false, 'error' => 'This ticket is ' . $ticketData['status'] . '. You cannot send new messages.']);
            exit();
        }

        // Use the existing updateAppikaTicket function to send the message
        $updateData = [
            'contact_id' => $ticketData['contact_id'],
            'agent_id' => null,
            'subject' => $ticketData['subject'],
            'type' => $ticketData['type'],
            'type_name' => $ticketData['type_name'],
            'priority' => strtoupper($ticketData['priority']),
            'status' => strtoupper($ticketData['status']),
            'req_email' => $ticketData['req_email'],
            'time_track' => '00:00:00',
            'reply_msg' => $message, // This is how messages are sent in Appika
            'tags' => ''
        ];

        $result = updateAppikaTicket($ticketId, $updateData);

        if ($result['success']) {
            echo json_encode(['success' => true, 'message' => 'Message sent successfully']);
        } else {
            echo json_encode(['success' => false, 'error' => $result['error'] ?? 'Failed to send message']);
        }
        break;
        
    default:
        echo json_encode(['success' => false, 'error' => 'Invalid action']);
        break;
}
?>
