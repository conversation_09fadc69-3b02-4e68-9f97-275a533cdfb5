<?php
session_start();
include_once('../functions/server.php');
include_once('../functions/ticket-expiration-functions.php');
header('Content-Type: application/json');

$user_id = isset($_SESSION['user_id']) ? intval($_SESSION['user_id']) : 0;
$username = isset($_SESSION['username']) ? $_SESSION['username'] : '';
$unread_admin_messages = 0;
$expiring_tickets_count = 0;

if ($user_id > 0) {
    // First, sync user tickets to handle any expired tickets
    if ($username) {
        syncUserTableTickets($username);
    }

    // Count unread chat messages
    $sql = "SELECT COUNT(*) as count FROM chat_messages WHERE sender_type = 'admin' AND is_read = 0 AND ticket_id IN (SELECT id FROM support_tickets WHERE user_id = $user_id)";
    $result = mysqli_query($conn, $sql);
    $row = mysqli_fetch_assoc($result);
    $unread_admin_messages = (int)$row['count'];

    // Count tickets expiring soon
    if ($username) {
        $expiring_tickets = getTicketsExpiringSoon($username);
        $expiring_tickets_count = count($expiring_tickets);
    }
}

echo json_encode([
    'success' => true,
    'unread_admin_messages' => $unread_admin_messages,
    'expiring_tickets_count' => $expiring_tickets_count
]);
