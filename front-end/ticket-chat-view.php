<?php
session_start();
include('../functions/server.php');
include('../functions/timezone-helper.php');
include('../functions/graphql_functions.php');

// Check if user is logged in
if (!isset($_SESSION['username'])) {
    header('Location: ../login.php');
    exit();
}

$username = $_SESSION['username'];
$user_id = $_SESSION['user_id'];

// Get ticket ID from URL
$ticket_id = isset($_GET['ticket_id']) ? intval($_GET['ticket_id']) : 0;

if ($ticket_id <= 0) {
    echo '<div class="alert alert-danger">Invalid ticket ID provided.</div>';
    exit();
}

// Get user email for Appika verification
$user_query = "SELECT email FROM user WHERE username = ?";
$stmt = mysqli_prepare($conn, $user_query);
mysqli_stmt_bind_param($stmt, 's', $username);
mysqli_stmt_execute($stmt);
$user_result = mysqli_stmt_get_result($stmt);
$user_data = mysqli_fetch_assoc($user_result);
mysqli_stmt_close($stmt);

if (!$user_data) {
    echo '<div class="alert alert-danger">User not found.</div>';
    exit();
}

$user_email = $user_data['email'];

// Fetch ticket details from Appika API
$query = '
query GetTicket($id: Int!) {
    getTicket(id: $id) {
        id
        ticket_no
        contact_id
        agent_id
        req_email
        subject
        type
        type_name
        priority
        status
        created
        updated
        contacts {
            id
            email
            fname
            status
        }
        ticketMsg {
            message
            creator_by_contact
            creator_by_agent
        }
    }
}';

$variables = ['id' => $ticket_id];
$result = makeGraphQLRequest($query, $variables);

if (!$result['success']) {
    echo '<div class="alert alert-danger">Error fetching ticket: ' . htmlspecialchars($result['error'] ?? 'Unknown error') . '</div>';
    exit();
}

$ticket = $result['data']['data']['getTicket'] ?? null;

if (!$ticket) {
    echo '<div class="alert alert-warning">Ticket not found.</div>';
    exit();
}

// Check if user has access to this ticket (by email)
if ($ticket['req_email'] !== $user_email) {
    echo '<div class="alert alert-danger">Access denied. This ticket does not belong to you.</div>';
    exit();
}

// Process messages from Appika API (same logic as HelloITOld)
$messages = [];
if (isset($ticket['ticketMsg']) && is_array($ticket['ticketMsg'])) {
    foreach ($ticket['ticketMsg'] as $index => $msg) {
        $messageText = $msg['message'] ?? '';

        // Skip empty messages
        if (empty(trim($messageText))) {
            continue;
        }

        // Determine sender based on Appika's creator fields
        $creatorByContact = $msg['creator_by_contact'] ?? null;
        $creatorByAgent = $msg['creator_by_agent'] ?? null;

        if (!empty($creatorByContact)) {
            // Message sent by contact/user
            $senderType = 'user';
            $senderName = 'You';
        } elseif (!empty($creatorByAgent)) {
            // Message sent by agent/support
            $senderType = 'admin';
            $senderName = 'Support';
        } else {
            // First message is usually description, others default to user
            $senderType = $index === 0 ? 'description' : 'user';
            $senderName = $index === 0 ? 'System' : 'You';
        }

        $messages[] = [
            'id' => $index,
            'message' => $messageText,
            'sender_type' => $senderType,
            'sender_name' => $senderName,
            'is_description' => $index === 0,
            'creator_by_contact' => $creatorByContact,
            'creator_by_agent' => $creatorByAgent
        ];
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ticket Chat - #<?php echo htmlspecialchars($ticket['ticket_no'] ?? $ticket_id); ?></title>
    <link rel="shortcut icon" href="../image/png/favicon.png" type="image/x-icon">
    <!-- Bootstrap and plugins -->
    <link rel="stylesheet" href="../css/bootstrap.css">
    <link rel="stylesheet" href="../css/main.css">
    <link rel="stylesheet" href="../fonts/fontawesome-5/css/all.css">
    <style>
    body {
        background-color: #f8f9fa;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }

    .chat-container {
        height: 100vh;
        display: flex;
        flex-direction: column;
    }

    .chat-header {
        background-color: #473BF0;
        color: white;
        padding: 15px 20px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .chat-header h4 {
        margin: 0;
        font-size: 18px;
    }

    .back-btn {
        background-color: rgba(255,255,255,0.2);
        color: white;
        border: none;
        padding: 8px 16px;
        border-radius: 5px;
        text-decoration: none;
        transition: background-color 0.3s;
    }

    .back-btn:hover {
        background-color: rgba(255,255,255,0.3);
        color: white;
        text-decoration: none;
    }

    .chat-body {
        flex: 1;
        display: flex;
        overflow: hidden;
    }

    /* Left Side - Ticket Details */
    .ticket-details {
        width: 35%;
        background-color: white;
        border-right: 1px solid #dee2e6;
        overflow-y: auto;
        padding: 20px;
    }

    .ticket-info-card {
        background-color: #f8f9fa;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
    }

    .ticket-info-card h5 {
        color: #473BF0;
        margin-bottom: 15px;
        font-weight: 600;
    }

    .info-row {
        display: flex;
        justify-content: space-between;
        margin-bottom: 10px;
        padding: 8px 0;
        border-bottom: 1px solid #e9ecef;
    }

    .info-row:last-child {
        border-bottom: none;
        margin-bottom: 0;
    }

    .info-label {
        font-weight: 600;
        color: #495057;
        flex: 0 0 40%;
    }

    .info-value {
        color: #212529;
        flex: 1;
        text-align: right;
    }

    .badge {
        padding: 0.25em 0.6em;
        font-size: 75%;
        font-weight: 700;
        border-radius: 0.25rem;
    }

    .badge-starter { background-color: #fbbf24; color: #fff; }
    .badge-business { background-color: #01A7E1; color: #fff; }
    .badge-ultimate { background-color: #793BF0; color: #fff; }
    .badge-high { background-color: #dc3545; color: #fff; }
    .badge-medium { background-color: #ffc107; color: #212529; }
    .badge-low { background-color: #28a745; color: #fff; }
    .badge-urgent { background-color: #dc3545; color: #fff; }
    .badge-open { background-color: #4CAF50; color: #fff; }
    .badge-wip { background-color: #FF9800; color: #fff; }
    .badge-closed { background-color: #757575; color: #fff; }

    /* Right Side - Chat */
    .chat-area {
        width: 65%;
        display: flex;
        flex-direction: column;
        background-color: white;
    }

    .messages-container {
        flex: 1;
        overflow-y: auto;
        padding: 20px;
        background-color: #f8f9fa;
    }

    .message {
        margin-bottom: 15px;
        display: flex;
        flex-direction: column;
    }

    .message.user {
        align-items: flex-end;
    }

    .message.admin {
        align-items: flex-start;
    }

    .message.description {
        justify-content: center;
        margin-bottom: 25px;
    }

    .message-bubble {
        max-width: 70%;
        padding: 12px 16px;
        border-radius: 18px;
        word-wrap: break-word;
        position: relative;
    }

    .message.user .message-bubble {
        background-color: #e9ecef;
        color: #333;
        border-bottom-right-radius: 5px;
    }

    .message.admin .message-bubble {
        background-color: #473BF0;
        color: white;
        border-bottom-left-radius: 5px;
    }

    .message.description .message-bubble {
        background-color: #e3f2fd;
        color: #1976d2;
        border-left: 4px solid #2196f3;
        border-radius: 5px;
        max-width: 90%;
    }

    .message-info {
        font-size: 12px;
        color: #6c757d;
        margin-top: 5px;
        text-align: left;
    }

    .message.user .message-info {
        text-align: right;
    }

    .message-form {
        padding: 20px;
        background-color: white;
        border-top: 1px solid #dee2e6;
    }

    .message-input-group {
        display: flex;
        gap: 10px;
        align-items: flex-end;
    }

    .message-input {
        flex: 1;
        border: 1px solid #ced4da;
        border-radius: 25px;
        padding: 12px 20px;
        resize: none;
        min-height: 50px;
        max-height: 120px;
        font-family: inherit;
    }

    .send-btn {
        background-color: #473BF0;
        color: white;
        border: none;
        border-radius: 50%;
        width: 50px;
        height: 50px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: background-color 0.3s;
    }

    .send-btn:hover {
        background-color: #3d32d9;
    }

    .send-btn:disabled {
        background-color: #6c757d;
        cursor: not-allowed;
    }

    .empty-messages {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        text-align: center;
        color: #6c757d;
        padding: 40px;
        height: 100%;
        min-height: 200px;
    }

    .empty-messages i {
        font-size: 48px;
        margin-bottom: 15px;
        color: #dee2e6;
    }

    /* Mobile Responsive */
    @media (max-width: 768px) {
        .chat-body {
            flex-direction: column;
        }
        
        .ticket-details {
            width: 100%;
            max-height: 40vh;
        }
        
        .chat-area {
            width: 100%;
            flex: 1;
        }
        
        .message-bubble {
            max-width: 85%;
        }
        
        .chat-header h4 {
            font-size: 16px;
        }
    }

    /* Notification styles */
    .notification-dot {
        width: 8px;
        height: 8px;
        background-color: #dc3545;
        border-radius: 50%;
        display: inline-block;
        margin-left: 5px;
        animation: pulse 2s infinite;
    }

    @keyframes pulse {
        0% { opacity: 1; }
        50% { opacity: 0.5; }
        100% { opacity: 1; }
    }
    </style>
</head>
<body>
    <div class="chat-container">
        <!-- Header -->
        <div class="chat-header">
            <a href="my-ticket.php" class="back-btn">
                <i class="fas fa-arrow-left"></i> Back to Tickets
            </a>
        </div>

        <!-- Body -->
        <div class="chat-body">
            <!-- Left Side - Ticket Details -->
            <div class="ticket-details">
                <div class="ticket-info-card">
                    <h5><i class="fas fa-info-circle"></i> Ticket Information</h5>
                    
                    <div class="info-row">
                        <span class="info-label">Ticket ID:</span>
                        <span class="info-value">#<?php echo htmlspecialchars($ticket['ticket_no'] ?? $ticket_id); ?></span>
                    </div>
                    
                    <div class="info-row">
                        <span class="info-label">Subject:</span>
                        <span class="info-value"><?php echo htmlspecialchars($ticket['subject'] ?? 'N/A'); ?></span>
                    </div>
                    
                    <div class="info-row">
                        <span class="info-label">Status:</span>
                        <span class="info-value">
                            <span class="badge badge-<?php echo strtolower($ticket['status'] ?? 'open'); ?>">
                                <?php echo strtoupper($ticket['status'] ?? 'OPEN'); ?>
                            </span>
                        </span>
                    </div>
                    
                    <div class="info-row">
                        <span class="info-label">Priority:</span>
                        <span class="info-value">
                            <span class="badge badge-<?php echo strtolower($ticket['priority'] ?? 'medium'); ?>">
                                <?php echo strtoupper($ticket['priority'] ?? 'MEDIUM'); ?>
                            </span>
                        </span>
                    </div>
                    
                    <div class="info-row">
                        <span class="info-label">Type:</span>
                        <span class="info-value">
                            <span class="badge badge-<?php echo strtolower($ticket['type_name'] ?? 'starter'); ?>">
                                <?php echo ucfirst($ticket['type_name'] ?? 'Starter'); ?>
                            </span>
                        </span>
                    </div>
                    
                    <div class="info-row">
                        <span class="info-label">Created:</span>
                        <span class="info-value"><?php echo date('M j, Y H:i', strtotime($ticket['created'] ?? 'now')); ?></span>
                    </div>
                    
                    <div class="info-row">
                        <span class="info-label">Updated:</span>
                        <span class="info-value"><?php echo date('M j, Y H:i', strtotime($ticket['updated'] ?? 'now')); ?></span>
                    </div>

                    <?php if (!empty($ticket['description'])): ?>
                    <div class="info-row">
                        <span class="info-label">Description:</span>
                        <span class="info-value"><?php echo nl2br(htmlspecialchars($ticket['description'])); ?></span>
                    </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Right Side - Chat Area -->
            <div class="chat-area">
                <!-- Messages Container -->
                <div class="messages-container" id="messagesContainer">
                    <?php if (empty($messages)): ?>
                    <div class="empty-messages">
                        <i class="fas fa-comments"></i>
                        <p>No messages yet. Start the conversation by sending a message below.</p>
                    </div>
                    <?php else: ?>
                        <?php foreach ($messages as $message): ?>
                            <?php if ($message['is_description']): ?>
                            <div class="message description">
                                <div class="message-bubble">
                                    <strong>Ticket Description:</strong><br>
                                    <?php echo nl2br(htmlspecialchars($message['message'])); ?>
                                </div>
                            </div>
                            <?php else: ?>
                            <div class="message <?php echo $message['sender_type']; ?>">
                                <div class="message-bubble">
                                    <?php echo nl2br(htmlspecialchars($message['message'])); ?>
                                </div>
                                <div class="message-info">
                                    <?php echo htmlspecialchars($message['sender_name'] ?? 'Unknown'); ?>
                                </div>
                            </div>
                            <?php endif; ?>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>

                <!-- Message Form -->
                <?php if (strtoupper($ticket['status'] ?? '') !== 'CLOSED'): ?>
                <div class="message-form">
                    <form id="messageForm" onsubmit="sendMessage(event)">
                        <div class="message-input-group">
                            <textarea 
                                id="messageInput" 
                                class="message-input" 
                                placeholder="Type your message here..." 
                                required
                                onkeydown="handleKeyDown(event)"
                                oninput="autoResize(this)"
                            ></textarea>
                            <button type="submit" class="send-btn" id="sendBtn">
                                <i class="fas fa-paper-plane"></i>
                            </button>
                        </div>
                    </form>
                </div>
                <?php else: ?>
                <div class="message-form">
                    <div class="alert alert-info mb-0">
                        <i class="fas fa-lock"></i> This ticket is closed. You cannot send new messages.
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Include chat notification system -->
    <script src="../js/jquery-3.4.1.min.js"></script>
    <script src="../js/chat-notifications.js"></script>
    <script>
    const ticketId = <?php echo $ticket_id; ?>;
    const userId = <?php echo $user_id; ?>;
    
    // Initialize chat notifications
    const chatNotifications = new ChatNotificationSystem({
        soundEnabled: true,
        browserNotificationsEnabled: true,
        soundFile: "../sounds/mixkit-software-interface-start-2574.wav"
    });
    
    chatNotifications.initialize();
    
    // Auto-resize textarea
    function autoResize(textarea) {
        textarea.style.height = 'auto';
        textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px';
    }
    
    // Handle Enter key
    function handleKeyDown(event) {
        if (event.key === 'Enter' && !event.shiftKey) {
            event.preventDefault();
            document.getElementById('messageForm').dispatchEvent(new Event('submit'));
        }
    }
    
    // Send message function - using Appika API
    function sendMessage(event) {
        event.preventDefault();
        
        const messageInput = document.getElementById('messageInput');
        const sendBtn = document.getElementById('sendBtn');
        const message = messageInput.value.trim();
        
        if (!message) return;
        
        // Disable form
        sendBtn.disabled = true;
        sendBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
        
        // Send message via fetch to appika-message-api.php
        fetch('appika-message-api.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `action=send_message&ticket_id=${ticketId}&message=${encodeURIComponent(message)}`
        })
        .then(response => response.json())
        .then(data => {
            if (data.success && data.message) {
                messageInput.value = '';
                messageInput.style.height = 'auto';
                // Optimistically add the new message to the chat
                let messages = window.currentMessages || [];
                messages.push({
                    ...data.message,
                    is_description: false // never treat as description
                });
                window.currentMessages = messages;
                displayMessages(messages);
                // Reload from server after a short delay to ensure sync
                setTimeout(loadMessages, 1000);
            } else if (data.error) {
                alert('Error sending message: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error sending message. Please try again.');
        })
        .finally(() => {
            sendBtn.disabled = false;
            sendBtn.innerHTML = '<i class="fas fa-paper-plane"></i>';
        });
    }
    
    // Load messages function - using Appika API
    function loadMessages() {
        fetch(`appika-message-api.php?action=get_messages&ticket_id=${ticketId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                window.currentMessages = data.messages;
                displayMessages(data.messages);
            }
        })
        .catch(error => console.error('Error loading messages:', error));
    }
    
    // Display messages
    function displayMessages(messages) {
        const messagesContainer = document.getElementById('messagesContainer');
        let html = '';
        
        if (messages.length === 0) {
            html = `
                <div class="empty-messages">
                    <i class="fas fa-comments"></i>
                    <p>No messages yet. Start the conversation by sending a message below.</p>
                </div>
            `;
        } else {
            messages.forEach((message, index) => {
                if (message.is_description) {
                    // First message is description
                    html += `
                        <div class="message description">
                            <div class="message-bubble">
                                <strong>Ticket Description:</strong><br>
                                ${escapeHtml(message.message).replace(/\n/g, '<br>')}
                            </div>
                        </div>
                    `;
                } else {
                    // Chat messages
                    const messageClass = message.sender_type;
                    const senderName = message.sender_name || 'Unknown';
                    
                    html += `
                        <div class="message ${messageClass}">
                            <div class="message-bubble">
                                ${escapeHtml(message.message).replace(/\n/g, '<br>')}
                            </div>
                            <div class="message-info">${senderName}</div>
                        </div>
                    `;
                }
            });
        }
        
        messagesContainer.innerHTML = html;
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }
    
    // Escape HTML
    function escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
    
    // Auto-refresh messages every 10 seconds
    setInterval(loadMessages, 10000);
    
    // Request notification permission on page load
    chatNotifications.requestPermission();
    </script>
</body>
</html>
