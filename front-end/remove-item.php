<?php
session_start();
include('../functions/server.php');

if (!isset($_SESSION['user_id'])) {
    echo "User not logged in.";
    exit;
}

$user_id = $_SESSION['user_id'];
$cart_item_id = isset($_POST['cart_item_id']) ? $_POST['cart_item_id'] : null;

if ($cart_item_id) {
    try {
        // ลบสินค้าออกจากตะกร้า
        $query = "DELETE FROM cart_items WHERE cart_item_id = :cart_item_id AND cart_id IN (SELECT cart_id FROM cart WHERE user_id = :user_id)";
        $stmt = $pdo->prepare($query);
        $stmt->execute(['cart_item_id' => $cart_item_id, 'user_id' => $user_id]);

        echo "Item removed";
    } catch (PDOException $e) {
        echo "Error: " . $e->getMessage();
    }
}
?>