<?php
session_start();
include('../functions/server.php');

if (!isset($_SESSION['username'])) {
    echo "Please log in first";
    exit();
}

$username = $_SESSION['username'];

echo "<h2>Debug: Expiration Logs for $username</h2>";

// Check if ticket_expiration_log table exists
$check_table = "SHOW TABLES LIKE 'ticket_expiration_log'";
$result = mysqli_query($conn, $check_table);
if (mysqli_num_rows($result) == 0) {
    echo "<p style='color: red;'>❌ ticket_expiration_log table does not exist!</p>";
    exit();
} else {
    echo "<p style='color: green;'>✅ ticket_expiration_log table exists</p>";
}

// Check table structure
echo "<h3>Table Structure:</h3>";
$describe = "DESCRIBE ticket_expiration_log";
$result = mysqli_query($conn, $describe);
echo "<table border='1'><tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
while ($row = mysqli_fetch_assoc($result)) {
    echo "<tr>";
    foreach ($row as $value) {
        echo "<td>" . htmlspecialchars($value) . "</td>";
    }
    echo "</tr>";
}
echo "</table>";

// Check all expiration logs for this user
echo "<h3>All Expiration Logs for $username:</h3>";
$query = "SELECT * FROM ticket_expiration_log WHERE username = ? ORDER BY cleanup_date DESC";
$stmt = mysqli_prepare($conn, $query);
mysqli_stmt_bind_param($stmt, 's', $username);
mysqli_stmt_execute($stmt);
$result = mysqli_stmt_get_result($stmt);

if (mysqli_num_rows($result) == 0) {
    echo "<p style='color: orange;'>⚠️ No expiration logs found for $username</p>";
} else {
    echo "<table border='1'>";
    echo "<tr><th>ID</th><th>Username</th><th>Ticket Type</th><th>Expired Quantity</th><th>Purchase Date</th><th>Expiration Date</th><th>Cleanup Date</th><th>Transaction ID</th></tr>";
    while ($row = mysqli_fetch_assoc($result)) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($row['id']) . "</td>";
        echo "<td>" . htmlspecialchars($row['username']) . "</td>";
        echo "<td>" . htmlspecialchars($row['ticket_type']) . "</td>";
        echo "<td>" . htmlspecialchars($row['expired_quantity']) . "</td>";
        echo "<td>" . htmlspecialchars($row['purchase_date']) . "</td>";
        echo "<td>" . htmlspecialchars($row['expiration_date']) . "</td>";
        echo "<td>" . htmlspecialchars($row['cleanup_date']) . "</td>";
        echo "<td>" . htmlspecialchars($row['transaction_id']) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
}
mysqli_stmt_close($stmt);

// Check all expiration logs (all users)
echo "<h3>All Expiration Logs (All Users):</h3>";
$query_all = "SELECT * FROM ticket_expiration_log ORDER BY cleanup_date DESC LIMIT 20";
$result_all = mysqli_query($conn, $query_all);

if (mysqli_num_rows($result_all) == 0) {
    echo "<p style='color: orange;'>⚠️ No expiration logs found in the entire table</p>";
} else {
    echo "<table border='1'>";
    echo "<tr><th>ID</th><th>Username</th><th>Ticket Type</th><th>Expired Quantity</th><th>Purchase Date</th><th>Expiration Date</th><th>Cleanup Date</th><th>Transaction ID</th></tr>";
    while ($row = mysqli_fetch_assoc($result_all)) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($row['id']) . "</td>";
        echo "<td>" . htmlspecialchars($row['username']) . "</td>";
        echo "<td>" . htmlspecialchars($row['ticket_type']) . "</td>";
        echo "<td>" . htmlspecialchars($row['expired_quantity']) . "</td>";
        echo "<td>" . htmlspecialchars($row['purchase_date']) . "</td>";
        echo "<td>" . htmlspecialchars($row['expiration_date']) . "</td>";
        echo "<td>" . htmlspecialchars($row['cleanup_date']) . "</td>";
        echo "<td>" . htmlspecialchars($row['transaction_id']) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
}

// Test the query used in my-ticket-log.php
echo "<h3>Testing my-ticket-log.php Query:</h3>";
$userId = $_SESSION['user_id'];
$searchSqlExpiration = "";

$sqlLogs = "SELECT * FROM (
    SELECT
        tl.created_at,
        tl.action,
        tl.ticket_type,
        tl.amount,
        tl.description,
        tl.performed_by_admin_id,
        t.ticket_type as ticket_type_name,
        'ticket_log' as log_type
    FROM ticket_logs tl
    LEFT JOIN tickets t ON tl.ticket_id = t.ticketid
    WHERE tl.user_id = $userId

    UNION ALL

    SELECT
        tel.cleanup_date as created_at,
        'expired' as action,
        tel.ticket_type,
        tel.expired_quantity as amount,
        CONCAT('Expired ', tel.expired_quantity, ' ', tel.ticket_type, ' tickets (purchased on ', DATE_FORMAT(tel.purchase_date, '%M %d, %Y'), ')') as description,
        NULL as performed_by_admin_id,
        tel.ticket_type as ticket_type_name,
        'expiration_log' as log_type
    FROM ticket_expiration_log tel
    WHERE tel.username = '$username' $searchSqlExpiration
) as combined_logs
ORDER BY created_at DESC
LIMIT 10";

$resultLogs = mysqli_query($conn, $sqlLogs);

if (mysqli_num_rows($resultLogs) == 0) {
    echo "<p style='color: orange;'>⚠️ No logs found with the combined query</p>";
} else {
    echo "<table border='1'>";
    echo "<tr><th>Created At</th><th>Action</th><th>Ticket Type</th><th>Amount</th><th>Description</th><th>Log Type</th></tr>";
    while ($row = mysqli_fetch_assoc($resultLogs)) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($row['created_at']) . "</td>";
        echo "<td>" . htmlspecialchars($row['action']) . "</td>";
        echo "<td>" . htmlspecialchars($row['ticket_type']) . "</td>";
        echo "<td>" . htmlspecialchars($row['amount']) . "</td>";
        echo "<td>" . htmlspecialchars($row['description']) . "</td>";
        echo "<td>" . htmlspecialchars($row['log_type']) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
}
?>
