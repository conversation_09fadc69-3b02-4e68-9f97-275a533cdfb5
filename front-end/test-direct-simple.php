<?php
session_start();
include('../functions/server.php');
include('../functions/graphql_functions.php');

echo "<h2>Simple Direct GraphQL Test - Today's Date: " . date('Y-m-d H:i:s') . "</h2>";

// Test parameters
$ticket_id = 286;
$test_message = "Test message from direct GraphQL - " . date('H:i:s');

echo "<p><strong>Ticket ID:</strong> $ticket_id</p>";
echo "<p><strong>Test Message:</strong> $test_message</p>";

// Step 1: Get current ticket data
echo "<h3>Step 1: Get Current Ticket Data</h3>";
$getCurrentQuery = '
query GetTicket($id: Int!) {
    getTicket(id: $id) {
        id
        subject
        type
        priority
        status
        ticketMsg {
            message
        }
    }
}';

$currentResult = makeGraphQLRequest($getCurrentQuery, ['id' => $ticket_id]);
echo "<p><strong>Current ticketMsg count:</strong> " . count($currentResult['data']['data']['getTicket']['ticketMsg'] ?? []) . "</p>";

// Step 2: Send message using direct GraphQL
echo "<h3>Step 2: Send Message with Direct GraphQL</h3>";

$currentTicket = $currentResult['data']['data']['getTicket'];

$mutation = '
mutation updateTicket(
  $id: Int!,
  $subject: String!,
  $type: Int!,
  $priority: String!,
  $status: String!,
  $time_track: String!,
  $reply_msg: String!,
  $reply_type: String!,
  $tags: String
) {
  updateTicket(
    id: $id,
    subject: $subject,
    type: $type,
    priority: $priority,
    status: $status,
    time_track: $time_track,
    reply_msg: $reply_msg,
    reply_type: $reply_type,
    tags: $tags
  ) {
    id
    subject
    status
    updated
  }
}';

$variables = [
    'id' => $ticket_id,
    'subject' => $currentTicket['subject'],
    'type' => (int)$currentTicket['type'],
    'priority' => strtoupper($currentTicket['priority']),
    'status' => strtoupper($currentTicket['status']),
    'time_track' => '00:00:00',
    'reply_msg' => $test_message,
    'reply_type' => 'user',
    'tags' => ''
];

echo "<p><strong>Sending mutation...</strong></p>";
$updateResult = makeGraphQLRequest($mutation, $variables);

if ($updateResult['success']) {
    echo "<div style='color: green;'>✅ Message sent successfully!</div>";
    echo "<p><strong>Updated timestamp:</strong> " . ($updateResult['data']['data']['updateTicket']['updated'] ?? 'N/A') . "</p>";
} else {
    echo "<div style='color: red;'>❌ Failed: " . ($updateResult['error'] ?? 'Unknown error') . "</div>";
}

echo "<pre>";
print_r($updateResult);
echo "</pre>";

// Step 3: Check ticket again after 3 seconds
echo "<h3>Step 3: Check Ticket Again After 3 Seconds</h3>";
sleep(3);

$finalResult = makeGraphQLRequest($getCurrentQuery, ['id' => $ticket_id]);
$finalCount = count($finalResult['data']['data']['getTicket']['ticketMsg'] ?? []);

echo "<p><strong>Final ticketMsg count:</strong> $finalCount</p>";

if ($finalCount > count($currentTicket['ticketMsg'])) {
    echo "<div style='color: green;'>🎉 SUCCESS: Message was added!</div>";
} else {
    echo "<div style='color: orange;'>⚠️ Message sent but not visible yet (email processing delay)</div>";
}

echo "<pre>";
print_r($finalResult['data']);
echo "</pre>";
?>
