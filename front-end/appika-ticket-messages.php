<?php
session_start();
include('../functions/server.php');
include('../functions/graphql_functions.php');

// Check if user is logged in
if (!isset($_SESSION['username'])) {
    header('Location: ../login.php');
    exit();
}

$username = $_SESSION['username'];
$user_id = $_SESSION['user_id'];

// Get ticket ID from URL
$ticket_id = isset($_GET['ticket_id']) ? intval($_GET['ticket_id']) : 0;

if ($ticket_id <= 0) {
    echo '<div class="alert alert-danger">Invalid ticket ID provided.</div>';
    exit();
}

// Fetch ticket details from Appika API
$query = '
query GetTicket($id: Int!) {
    getTicket(id: $id) {
        id
        ticket_no
        contact_id
        agent_id
        req_email
        subject
        type
        type_name
        priority
        status
        created
        updated
        contacts {
            id
            email
            fname
            status
        }
        ticketMsg {
            message
            creator_by_contact
            creator_by_agent
        }
    }
}';

$variables = ['id' => $ticket_id];
$result = makeGraphQLRequest($query, $variables);

if (!$result['success']) {
    echo '<div class="alert alert-danger">Error fetching ticket: ' . htmlspecialchars($result['error'] ?? 'Unknown error') . '</div>';
    exit();
}

$ticket = $result['data']['data']['getTicket'] ?? null;

if (!$ticket) {
    echo '<div class="alert alert-warning">Ticket not found.</div>';
    exit();
}

// Check if user has access to this ticket (by email)
$user_query = "SELECT email FROM user WHERE username = ?";
$stmt = mysqli_prepare($conn, $user_query);
mysqli_stmt_bind_param($stmt, 's', $username);
mysqli_stmt_execute($stmt);
$user_result = mysqli_stmt_get_result($stmt);
$user_data = mysqli_fetch_assoc($user_result);
mysqli_stmt_close($stmt);

if (!$user_data || $user_data['email'] !== $ticket['req_email']) {
    echo '<div class="alert alert-danger">Access denied. This ticket does not belong to you.</div>';
    exit();
}

// Process messages (same logic as working ticket-chat-api.php)
$messages = [];
if (isset($ticket['ticketMsg']) && is_array($ticket['ticketMsg'])) {
    foreach ($ticket['ticketMsg'] as $index => $msg) {
        $messageText = $msg['message'] ?? '';

        // Skip empty messages
        if (empty(trim($messageText))) {
            continue;
        }

        // Determine sender based on Appika's creator fields
        $creatorByContact = $msg['creator_by_contact'] ?? null;
        $creatorByAgent = $msg['creator_by_agent'] ?? null;

        if (!empty($creatorByContact)) {
            // Message sent by contact/user
            $senderType = 'user';
            $senderName = 'You';
        } elseif (!empty($creatorByAgent)) {
            // Message sent by agent/support
            $senderType = 'admin';
            $senderName = 'Support';
        } else {
            // First message is usually description, others default to user
            $senderType = $index === 0 ? 'system' : 'user';
            $senderName = $index === 0 ? 'System' : 'You';
        }

        $messages[] = [
            'id' => $index,
            'message' => $messageText,
            'sender_type' => $senderType,
            'sender_name' => $senderName,
            'is_description' => $index === 0,
            'creator_by_contact' => $creatorByContact,
            'creator_by_agent' => $creatorByAgent
        ];
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ticket Messages - #<?php echo htmlspecialchars($ticket['ticket_no'] ?? $ticket_id); ?></title>
    <link rel="shortcut icon" href="../image/png/favicon.png" type="image/x-icon">
    <!-- Bootstrap and plugins -->
    <link rel="stylesheet" href="../css/bootstrap.css">
    <link rel="stylesheet" href="../css/main.css">
    <link rel="stylesheet" href="../fonts/fontawesome-5/css/all.css">
    <style>
    body {
        background-color: #f8f9fa;
        padding-top: 20px;
        padding-bottom: 20px;
    }

    .message-container {
        max-width: 900px;
        margin: 0 auto;
        background: white;
        border-radius: 10px;
        box-shadow: 0 0 20px rgba(0,0,0,0.1);
        overflow: hidden;
    }

    .message-header {
        background-color: #473BF0;
        color: white;
        padding: 20px;
        text-align: center;
    }

    .message-header h2 {
        margin: 0;
        font-size: 24px;
    }

    .ticket-info {
        background-color: #f8f9fa;
        padding: 15px 20px;
        border-bottom: 1px solid #dee2e6;
    }

    .ticket-info .row {
        margin: 0;
    }

    .ticket-info .col-md-6 {
        padding: 5px 0;
    }

    .info-label {
        font-weight: 600;
        color: #495057;
    }

    .info-value {
        color: #212529;
    }

    .badge {
        padding: 0.25em 0.6em;
        font-size: 75%;
        font-weight: 700;
        border-radius: 0.25rem;
    }

    .badge-starter { background-color: #fbbf24; color: #fff; }
    .badge-business { background-color: #01A7E1; color: #fff; }
    .badge-ultimate { background-color: #793BF0; color: #fff; }
    .badge-high { background-color: #dc3545; color: #fff; }
    .badge-medium { background-color: #ffc107; color: #212529; }
    .badge-low { background-color: #28a745; color: #fff; }
    .badge-urgent { background-color: #dc3545; color: #fff; }
    .badge-open { background-color: #4CAF50; color: #fff; }
    .badge-wip { background-color: #FF9800; color: #fff; }
    .badge-closed { background-color: #757575; color: #fff; }

    .messages-area {
        height: 400px;
        overflow-y: auto;
        padding: 20px;
        background-color: #f8f9fa;
    }

    .message {
        margin-bottom: 15px;
    }

    .message.description {
        background-color: #e3f2fd;
        border-left: 4px solid #2196f3;
        padding: 15px;
        border-radius: 5px;
    }

    .message.user {
        text-align: right;
    }

    .message.admin {
        text-align: left;
    }

    .message-bubble {
        display: inline-block;
        max-width: 70%;
        padding: 12px 16px;
        border-radius: 18px;
        word-wrap: break-word;
    }

    .message.user .message-bubble {
        background-color: #473BF0;
        color: white;
    }

    .message.admin .message-bubble {
        background-color: #e9ecef;
        color: #333;
    }

    .message-info {
        font-size: 12px;
        color: #6c757d;
        margin-top: 5px;
    }

    .message-form {
        padding: 20px;
        background-color: white;
        border-top: 1px solid #dee2e6;
    }

    .message-input-group {
        display: flex;
        gap: 10px;
    }

    .message-input {
        flex: 1;
        border: 1px solid #ced4da;
        border-radius: 25px;
        padding: 12px 20px;
        resize: none;
        min-height: 50px;
        max-height: 120px;
    }

    .send-btn {
        background-color: #473BF0;
        color: white;
        border: none;
        border-radius: 50%;
        width: 50px;
        height: 50px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: background-color 0.3s;
    }

    .send-btn:hover {
        background-color: #3d32d9;
    }

    .send-btn:disabled {
        background-color: #6c757d;
        cursor: not-allowed;
    }

    .empty-messages {
        text-align: center;
        color: #6c757d;
        padding: 40px;
    }

    .empty-messages i {
        font-size: 48px;
        margin-bottom: 15px;
        color: #dee2e6;
    }

    .back-btn {
        background-color: #6c757d;
        color: white;
        border: none;
        padding: 8px 16px;
        border-radius: 5px;
        text-decoration: none;
        display: inline-block;
        margin-bottom: 20px;
    }

    .back-btn:hover {
        background-color: #5a6268;
        color: white;
        text-decoration: none;
    }

    @media (max-width: 768px) {
        .message-container {
            margin: 10px;
            border-radius: 5px;
        }
        
        .message-bubble {
            max-width: 85%;
        }
        
        .ticket-info .col-md-6 {
            margin-bottom: 10px;
        }
    }
    </style>
</head>
<body>
    <div class="container-fluid">
        <a href="my-ticket.php" class="back-btn">
            <i class="fas fa-arrow-left"></i> Back to My Tickets
        </a>
        
        <div class="message-container">
            <div class="message-header">
                <h2>Ticket Messages</h2>
                <p class="mb-0">Ticket #<?php echo htmlspecialchars($ticket['ticket_no'] ?? $ticket_id); ?></p>
            </div>

            <div class="ticket-info">
                <div class="row">
                    <div class="col-md-6">
                        <span class="info-label">Subject:</span>
                        <span class="info-value"><?php echo htmlspecialchars($ticket['subject'] ?? 'N/A'); ?></span>
                    </div>
                    <div class="col-md-6">
                        <span class="info-label">Status:</span>
                        <span class="info-value">
                            <span class="badge badge-<?php echo strtolower($ticket['status'] ?? 'open'); ?>">
                                <?php echo strtoupper($ticket['status'] ?? 'OPEN'); ?>
                            </span>
                        </span>
                    </div>
                    <div class="col-md-6">
                        <span class="info-label">Priority:</span>
                        <span class="info-value">
                            <span class="badge badge-<?php echo strtolower($ticket['priority'] ?? 'medium'); ?>">
                                <?php echo strtoupper($ticket['priority'] ?? 'MEDIUM'); ?>
                            </span>
                        </span>
                    </div>
                    <div class="col-md-6">
                        <span class="info-label">Type:</span>
                        <span class="info-value">
                            <span class="badge badge-<?php echo strtolower($ticket['type_name'] ?? 'starter'); ?>">
                                <?php echo ucfirst($ticket['type_name'] ?? 'Starter'); ?>
                            </span>
                        </span>
                    </div>
                </div>
            </div>

            <div class="messages-area" id="messagesArea">
                <?php if (empty($messages)): ?>
                <div class="empty-messages">
                    <i class="fas fa-comments"></i>
                    <p>No messages yet. Start the conversation by sending a message below.</p>
                </div>
                <?php else: ?>
                    <?php foreach ($messages as $message): ?>
                        <?php if ($message['is_description']): ?>
                        <div class="message description">
                            <strong>Ticket Description:</strong><br>
                            <?php echo nl2br(htmlspecialchars($message['message'])); ?>
                        </div>
                        <?php else: ?>
                        <div class="message admin">
                            <div class="message-bubble">
                                <?php echo nl2br(htmlspecialchars($message['message'])); ?>
                            </div>
                            <div class="message-info">
                                <?php echo htmlspecialchars($message['sender_name']); ?>
                            </div>
                        </div>
                        <?php endif; ?>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>

            <?php if (strtoupper($ticket['status'] ?? '') !== 'CLOSED'): ?>
            <div class="message-form">
                <form id="messageForm" onsubmit="sendMessage(event)">
                    <div class="message-input-group">
                        <textarea 
                            id="messageInput" 
                            class="message-input" 
                            placeholder="Type your message here..." 
                            required
                            onkeydown="handleKeyDown(event)"
                            oninput="autoResize(this)"
                        ></textarea>
                        <button type="submit" class="send-btn" id="sendBtn">
                            <i class="fas fa-paper-plane"></i>
                        </button>
                    </div>
                </form>
            </div>
            <?php else: ?>
            <div class="message-form">
                <div class="alert alert-info mb-0">
                    <i class="fas fa-lock"></i> This ticket is closed. You cannot send new messages.
                </div>
            </div>
            <?php endif; ?>
        </div>
    </div>

    <script src="../js/jquery-3.4.1.min.js"></script>
    <script src="../js/bootstrap.js"></script>
    <script>
    const ticketId = <?php echo $ticket_id; ?>;
    
    // Auto-resize textarea
    function autoResize(textarea) {
        textarea.style.height = 'auto';
        textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px';
    }
    
    // Handle Enter key
    function handleKeyDown(event) {
        if (event.key === 'Enter' && !event.shiftKey) {
            event.preventDefault();
            document.getElementById('messageForm').dispatchEvent(new Event('submit'));
        }
    }
    
    // Send message function
    function sendMessage(event) {
        event.preventDefault();
        
        const messageInput = document.getElementById('messageInput');
        const sendBtn = document.getElementById('sendBtn');
        const message = messageInput.value.trim();
        
        if (!message) return;
        
        // Disable form
        sendBtn.disabled = true;
        sendBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
        
        // Send message via fetch
        fetch('appika-message-api.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `action=send_message&ticket_id=${ticketId}&message=${encodeURIComponent(message)}`
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                messageInput.value = '';
                messageInput.style.height = 'auto';
                loadMessages(); // Reload messages
            } else {
                alert('Error sending message: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error sending message. Please try again.');
        })
        .finally(() => {
            sendBtn.disabled = false;
            sendBtn.innerHTML = '<i class="fas fa-paper-plane"></i>';
        });
    }
    
    // Load messages function
    function loadMessages() {
        fetch(`appika-message-api.php?action=get_messages&ticket_id=${ticketId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayMessages(data.messages);
            }
        })
        .catch(error => console.error('Error loading messages:', error));
    }
    
    // Display messages
    function displayMessages(messages) {
        const messagesArea = document.getElementById('messagesArea');
        let html = '';
        
        if (messages.length === 0) {
            html = `
                <div class="empty-messages">
                    <i class="fas fa-comments"></i>
                    <p>No messages yet. Start the conversation by sending a message below.</p>
                </div>
            `;
        } else {
            messages.forEach((message, index) => {
                if (index === 0) {
                    // First message is description
                    html += `
                        <div class="message description">
                            <strong>Ticket Description:</strong><br>
                            ${escapeHtml(message.message).replace(/\n/g, '<br>')}
                        </div>
                    `;
                } else {
                    // Chat messages
                    const isUser = message.sender_type === 'user';
                    const messageClass = isUser ? 'user' : 'admin';
                    const senderName = isUser ? 'You' : 'Support';
                    
                    html += `
                        <div class="message ${messageClass}">
                            <div class="message-bubble">
                                ${escapeHtml(message.message).replace(/\n/g, '<br>')}
                            </div>
                            <div class="message-info">${senderName}</div>
                        </div>
                    `;
                }
            });
        }
        
        messagesArea.innerHTML = html;
        messagesArea.scrollTop = messagesArea.scrollHeight;
    }
    
    // Escape HTML
    function escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
    
    // Auto-refresh messages every 10 seconds
    setInterval(loadMessages, 10000);
    </script>
</body>
</html>
