<?php
session_start();
include('../functions/server.php');
if (isset($_GET['logout'])) {
    session_destroy();
    unset($_SESSION['username']);
    header('location: ../index.php');
}
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>faq</title>
    <link rel="shortcut icon" href="../image/png/favicon.png" type="image/x-icon">
    <!-- Bootstrap , fonts & icons  -->
    <link rel="stylesheet" href="../css/bootstrap.css">
    <link rel="stylesheet" href="../fonts/icon-font/css/style.css">
    <link rel="stylesheet" href="../fonts/typography-font/typo.css">
    <link rel="stylesheet" href="../fonts/fontawesome-5/css/all.css">
    <!-- Plugin'stylesheets  -->
    <link rel="stylesheet" href="../plugins/aos/aos.min.css">
    <link rel="stylesheet" href="../plugins/fancybox/jquery.fancybox.min.css">
    <link rel="stylesheet" href="../plugins/nice-select/nice-select.min.css">
    <link rel="stylesheet" href="../plugins/slick/slick.min.css">
    <link rel="stylesheet" href="../plugins/date-picker/css/gijgo.min.css" type="text/css" />
    <!-- Vendor stylesheets  -->
    <link rel="stylesheet" href="../plugins/theme-mode-switcher/switcher-panel.css">
    <link rel="stylesheet" href="../css/main.css">
    <link rel="stylesheet" href="../css/theme-mode-custom.css">
    <!-- Custom stylesheet -->
</head>
<style>
.pt-lg-24,
.py-lg-24 {
    /* padding-top: 6.875rem !important; */
    margin-top: -50px !important;
}

/* Support Severity Banner Styles */
.support-severity-section {
    margin: 0px 0;
    margin-left: -15px;
    margin-right: -15px;
    width: 100vw;
    position: relative;
    left: 50%;
    right: 50%;
    margin-left: -50vw;
    margin-right: -50vw;
}

.support-severity-banner {
    padding: 60px 0;
    width: 100%;
}

.support-severity-banner .table {
    margin-bottom: 0 !important;
}

.support-severity-banner .table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(0, 0, 0, 0.05) !important;
}

.support-severity-banner .bg-primary {
    background-color: #473BF0 !important;
}

.support-severity-banner .rounded-10 {
    border-radius: 10px !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    /* Add more padding-top for mobile screens */
    body {
        padding-top: 100px !important;
    }

    .support-severity-banner {
        padding: 40px 0;
    }

    .support-severity-banner .table-responsive {
        max-width: 100% !important;
    }

    .support-severity-banner h2 {
        font-size: 24px !important;
    }

    .support-severity-banner p {
        font-size: 14px !important;
    }
}

.support-title {
    font-size: 1.2rem;
    font-weight: bold;
}

.support-list li {
    font-size: 1rem;
}

@media (max-width: 992px) {
    .support-severity-banner {
        padding: 30px 0;
    }

    .support-title {
        font-size: 1rem;
    }

    .support-list li {
        font-size: 0.9rem;
    }
}

@media (max-width: 768px) {
    .support-severity-banner {
        padding: 20px 0;
    }

    .support-title {
        font-size: 0.9rem;
    }

    .support-list li {
        font-size: 0.8rem;
    }
}

@media (max-width: 576px) {
    .support-severity-banner {
        padding: 15px 0;
    }

    .support-title {
        font-size: 0.8rem;
    }

    .support-list li {
        font-size: 0.7rem;
    }
}
</style>

<body data-theme="light" class="theme-light">
    <div class="site-wrapper overflow-hidden">
        <!-- Header Area -->
        <?php include('../header-footer/newnavtest.php'); ?>
        <!-- Hero Area -->
        <div class="newsletter-section bg-default-4 py-13 py-lg-24 border-bottom" style="margin-top: 80px;">
            <div class="container">
                <div class="row justify-content-center">
                    <div class="col-md-10 col-lg-8">
                        <div class="section-title text-center mb-12">
                            <h2 class="title gr-text-4 mb-6">Frequently Asked Questions</h2>
                            <p class="gr-text-8 mb-0">Find answers to common questions about our support desk and remote
                                IT services.</p>
                        </div>
                    </div>
                </div>
                <!-- FAQ Accordion -->
                <div class="row justify-content-center">
                    <div class="col-12 col-md-10 col-lg-8" data-aos="fade-left" data-aos-duration="1200"
                        data-aos-once="true">
                        <div class="accordion rounded-10 bg-white shadow-sm " id="accordionExample">
                            <?php
                            $faqs = [
                                [
                                    "question" => "What is HelloIT and how does it Work?",
                                    "answer" => "HelloIT is an online IT support desk created to provide fast help desk and remote support for fixing computer, applications, and network issues. It allows users to submit tickets and get instant responses at an affordable cost."
                                ],
                                [
                                    "question" => "How do I get started on HelloIT?",
                                    "answer" => "You must first subscribe to a <a href='https://helloit.io/support-ticket/buy-now'>plan</a>. Upon successful payment, you can log in to your account and submit a ticket through the customer portal."
                                ],
                                [
                                    "question" => "What devices and platforms are being supported?",
                                    "answer" => "We support desktops and laptops on both Windows and Mac operating systems. Servers and mobile devices are not supported in the plan."
                                ],
                                [
                                    "question" => "What type of issues does HelloIT cover?",
                                    "answer" => "<ul>
                                <li>Troubleshooting & Repairs</li>
                                <li>Upgrades</li>
                                <li>Software Installation</li>
                                <li>New PC Set-up</li>
                                <li>Networking</li>
                                <li>Cybersecurity</li>
                                <li>Malware and Virus Protection</li>
                                <li>Data Backup & Recovery</li>
                                <li>IT Consultancy and Advice</li>
                            </ul>"
                                ],
                                [
                                    "question" => "How do I submit a ticket on HelloIT?",
                                    "answer" => "Login to the customer portal with your username and password. On the dashboard left navigation bar, click on “Open a Support Ticket” to submit a ticket."
                                ],
                                [
                                    "question" => "How long will it take before HelloIT responds to my ticket?",
                                    "answer" => "We will respond to your ticket as soon as possible. Each submitted ticket will be tagged with a priority and processed accordingly. Please refer to “Support Severity Definitions” on our <a href='https://helloit.io/support-ticket/buy-now'>pricing page</a> for information about our response time."
                                ],
                                [
                                    "question" => "I forgot my password associated with my account.",
                                    "answer" => "Go to the account <a href='https://helloit.io/support-ticket/sign-in'>login page</a>, and click on the “Forgot Password?” link. Enter your username and click “Send Reset Link”. An email with a password reset link will be sent to your account to reset your password."
                                ],
                                [
                                    "question" => "How can I cancel my order?",
                                    "answer" => "You may cancel your order within 30 days of your initial purchase. Simply submit your request through our online form, and we will refund the outstanding balance to your account. Cancellation will not be accepted after 30 days of purchase."
                                ]
                            ];

                            foreach ($faqs as $index => $faq) {
                                $isFirst = $index === 0 ? "show" : "";
                                $expanded = $index === 0 ? "true" : "false";
                                echo "
                    <div class='border-bottom overflow-hidden'>
                        <div class='mb-0' id='heading$index'>
                        <button class='btn-reset gr-text-7 font-weight-bold text-left text-blackish-blue px-0 py-8 accordion-trigger arrow-icon w-100' type='button' data-toggle='collapse' data-target='#collapse$index' aria-expanded='$expanded' aria-controls='collapse$index'>
                            {$faq['question']}
                        </button>
                        </div>
                        <div id='collapse$index' class='collapse $isFirst' aria-labelledby='heading$index' data-parent='#accordionExample'>
                        <div class='gr-color-blackish-blue-opacity-7 mt-n3 gr-text-9 pb-8 pr-8'>
                            {$faq['answer']}
                        </div>
                        </div>
                    </div>";
                            }
                            ?>
                        </div>
                    </div>
                </div>
  <div class="form-bottom text-center pt-10">
                    <p class="gr-text-11 gr-text-color-opacity mb-0">Haven’t got your answer?
                        <a
                            href="<?php echo (basename($_SERVER['PHP_SELF']) === 'index.php') ? 'support-ticket/contact' : '../support-ticket/contact'; ?>">Contact
                            our support now</a>
                    </p>
                </div>
            </div>
        </div>
       

        <!-- Support Severity Definitions and Priority Help Desk Section -->
        <div class="support-severity-section">
            <div class="inner-banner support-severity-banner"
                style="background: linear-gradient(to top,  #473BF0, #6754E2); color: white; padding: 40px 0;">
                <div class="container">
                    <div class="row justify-content-center" style="padding: 50px 0;">
                                <!-- First Section: Support Severity Definitions -->
                                <div class="col-12 mb-5">
                                    <div class="text-center mb-5">
                                        <h2 style="color: white !important; font-size: 32px; margin-bottom: 20px;">Support Severity
                                            Definitions</h2>
                                        <p style="color: white !important; font-size: 18px;">To ensure efficient service delivery,
                                            your support request will be categorized into one of four tiers and handled accordingly.
                                        </p>
                                    </div>
                                </div>

                                <div class="col-lg-10 col-md-11 mb-5 d-flex justify-content-center">
                                    <div class="table-responsive bg-white p-3 rounded-10" style="max-width: 800px;">
                                        <table class="table table-striped table-bordered">
                                            <thead class="bg-primary text-white">
                                                <tr>
                                                    <th scope="col" width="15%">Priority</th>
                                                    <th scope="col" width="40%">Definition</th>
                                                    <th scope="col" width="45%">Response Time</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr>
                                                    <td class="font-weight-bold">1</td>
                                                    <td>Critical</td>
                                                    <td>4 Business hours</td>
                                                </tr>
                                                <tr>
                                                    <td class="font-weight-bold">2</td>
                                                    <td>Important</td>
                                                    <td>8 Business hours</td>
                                                </tr>
                                                <tr>
                                                    <td class="font-weight-bold">3</td>
                                                    <td>Normal</td>
                                                    <td>1 Business days</td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                                <br>
                                <br>
                                <!-- Second Section: Priority Help Desk -->
                                <div class="col-xl-10 col-lg-11 mt-5 mb-5" style="margin-top: 100px !important;">
                                    <div class="px-md-12 text-center mb-5">
                                        <h2 class="title gr-text-4 mb-4" style="color: white !important;">Priority Help Desk</h2>
                                        <p class="gr-text-8 mb-0" style="color: white !important;">
                                            <b>Priority Help Desk</b> offers significantly faster response times and broader support
                                            coverage, including both PC and network-related issues. It's specifically designed for
                                            clients operating mission-critical systems that demand high availability and rapid
                                            resolution.
                                        </p>
                                    </div>
                                </div>

                                <div class="col-lg-10 col-md-11 mb-5 d-flex justify-content-center">
                                    <div class="table-responsive bg-white p-3 rounded-10" style="max-width: 800px;">
                                        <table class="table table-striped table-bordered">
                                            <thead class="bg-primary text-white">
                                                <tr>
                                                    <th scope="col" width="15%">Priority</th>
                                                    <th scope="col" width="40%">Definition</th>
                                                    <th scope="col" width="45%">Response Time</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr>
                                                    <td class="font-weight-bold">1</td>
                                                    <td>Critical</td>
                                                    <td>1 Business hours</td>
                                                </tr>
                                                <tr>
                                                    <td class="font-weight-bold">2</td>
                                                    <td>Important</td>
                                                    <td>2 Business hours</td>
                                                </tr>
                                                <tr>
                                                    <td class="font-weight-bold">3</td>
                                                    <td>Normal</td>
                                                    <td>4 Business hours</td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

               
            </div>
        </div>
        <!-- Footer Section -->
        <?php include('../header-footer/footer.php'); ?>
    </div>
    <!-- Vendor Scripts -->
    <script src="../js/vendor.min.js"></script>
    <!-- Plugin's Scripts -->
    <script src="../plugins/fancybox/jquery.fancybox.min.js"></script>
    <script src="../plugins/nice-select/jquery.nice-select.min.js"></script>
    <script src="../plugins/aos/aos.min.js"></script>
    <script src="../plugins/slick/slick.min.js"></script>
    <script src="../plugins/date-picker/js/gijgo.min.js"></script>
    <script src="../plugins/counter-up/jquery.waypoints.min.js"></script>
    <script src="../plugins/counter-up/jquery.counterup.min.js"></script>
    <script src="../plugins/theme-mode-switcher/gr-theme-mode-switcher.js"></script>
    <!-- Activation Script -->
    <script src="../js/custom.js"></script>
</body>

</html>