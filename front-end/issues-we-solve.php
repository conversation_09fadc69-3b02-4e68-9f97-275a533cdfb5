<?php
session_start();
include('../functions/server.php');
if (isset($_GET['logout'])) {
  session_destroy();
  unset($_SESSION['username']);
  header('location: ../index.php');
}
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>issues-we-solve</title>
    <link rel="shortcut icon" href="../image/png/favicon.png" type="image/x-icon">
    <!-- Bootstrap , fonts & icons  -->
    <link rel="stylesheet" href="../css/bootstrap.css">
    <link rel="stylesheet" href="../fonts/icon-font/css/style.css">
    <link rel="stylesheet" href="../fonts/typography-font/typo.css">
    <link rel="stylesheet" href="../fonts/fontawesome-5/css/all.css">
    <!-- Plugin'stylesheets  -->
    <link rel="stylesheet" href="../plugins/aos/aos.min.css">
    <link rel="stylesheet" href="../plugins/fancybox/jquery.fancybox.min.css">
    <link rel="stylesheet" href="../plugins/nice-select/nice-select.min.css">
    <link rel="stylesheet" href="../plugins/slick/slick.min.css">
    <link rel="stylesheet" href="../plugins/date-picker/css/gijgo.min.css" type="text/css" />
    <!-- Vendor stylesheets  -->
    <link rel="stylesheet" href="../plugins/theme-mode-switcher/switcher-panel.css">
    <link rel="stylesheet" href="../css/main.css">
    <link rel="stylesheet" href="../css/theme-mode-custom.css">
    <!-- Custom stylesheet -->
</head>

<body data-theme="light">
    <div class="site-wrapper overflow-hidden">
        <!-- Header Area -->
        <?php
    include('../header-footer/newnavtest.php');
    ?>
        <!-- navbar-dark -->
        <!-- Section 1 -->
        <style>
        .bg-default-2 {
            background: linear-gradient(to right, #473BF0, #762EE5);
            height: auto;
            min-height: 300px;
            padding: 30px 0;
            max-height: 535px;
        }

        @media (max-width: 991px) {
            .bg-default-2 {
                min-height: 250px;
                padding: 25px 0;
            }
        }

        @media (max-width: 767px) {
            .bg-default-2 {
                min-height: auto;
                padding: 20px 0;
            }
        }

        .row.justify-content-center.align-items-center {
            margin-top: -100px !important;
        }

        .custom-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            /* Vertically align columns */
            max-width: 1200px;
            /* Adjust as needed */
            margin-left: auto;
            margin-right: auto;
            padding-left: 20px;
            padding-right: 20px;
        }

        .custom-column {
            width: 50%;
            /* Two equal columns */
        }

        .custom-image-widget {
            text-align: center;
            margin-top: 10px;
        }

        .custom-image-widget img {
            max-width: 80%;
            height: auto;
            border-radius: 5px;
        }

        .custom-heading-widget h1 {
            color: #ffffff;
            font-size: 40px;
            font-weight: 700;
            line-height: 1.2;
            margin-bottom: 15px;
            font-family: "Circular Std", sans-serif;
            word-wrap: break-word;
            overflow-wrap: break-word;
            hyphens: none;
        }

        .custom-text-widget p {
            color: #ffffff;
            font-size: 20px;
            font-weight: 400;
            line-height: 30px;
            font-family: "Circular Std", sans-serif;
            margin-left: 5px;
        }

        /* Responsive styles */
        @media (max-width: 991px) {
            .custom-heading-widget h1 {
                font-size: 32px;
                line-height: 1.3;
                margin-bottom: 12px;
                text-align: center;
            }

            .custom-text-widget p {
                font-size: 18px;
                line-height: 26px;
            }
        }

        @media (max-width: 767px) {
            .custom-container {
                flex-direction: column;
            }

            .custom-column {
                width: 100%;
            }

            .custom-column.order-1 {
                margin-bottom: 20px;
            }

            .custom-column.order-2 {
                margin-bottom: 10px;
            }

            .row.justify-content-center.align-items-center {
                flex-direction: column;
            }

            .custom-heading-widget h1 {
                font-size: 26px;
                line-height: 1.4;
                text-align: center;
                margin-bottom: 12px;
                padding: 0 10px;
                overflow-wrap: break-word;
                hyphens: none;
            }

            .custom-text-widget p {
                font-size: 16px;
                line-height: 22px;
                text-align: center;
                margin-left: 0;
            }

            .custom-image-widget img {
                max-width: 70%;
                margin: 0 auto;
                display: block;
                border-radius: 20px;
            }

            @media (max-width: 480px) {
                .custom-image-widget img {
                    max-width: 65%;
                    height: auto;
                    margin: 0 auto;
                }
            }
        }

        /* Additional mobile breakpoints for h1 */
        @media (max-width: 576px) {
            .custom-heading-widget h1 {
                font-size: 22px;
                line-height: 1.4;
                padding: 0 15px;
                margin-bottom: 15px;
                hyphens: none;
                overflow-wrap: break-word;
            }
        }

        @media (max-width: 480px) {
            .custom-heading-widget h1 {
                font-size: 20px;
                line-height: 1.5;
                padding: 0 10px;
                margin-bottom: 12px;
                hyphens: none;
                overflow-wrap: break-word;
            }
        }

        @media (max-width: 375px) {
            .custom-heading-widget h1 {
                font-size: 18px;
                line-height: 1.5;
                padding: 0 8px;
                margin-bottom: 10px;
                hyphens: none;
                overflow-wrap: break-word;
            }
        }
        </style>
        <div class="position-relative bg-default-2 bg-pattern pattern-2"
            style="padding-top: 120px; padding-bottom: 0px;">
            <div class="container">
                <div class="row justify-content-center align-items-center">
                    <div class="custom-column fade-in-right order-1" data-aos="fade-left" data-aos-duration="1100"
                        data-aos-once="true">
                        <div class="custom-image-widget">
                            <picture>
                                <source srcset="../image/wp/job-man-loon.webp 520w,
                                    ../image/wp/job-man-loon-235x300.webp 235w,
                                    ../image/wp/job-man-loon-492x628.webp 492w" type="image/webp" sizes="(max-width: 500px) 100vw, 500px">
                                <img fetchpriority="high" decoding="async" width="396" height="505"
                                    src="../image/wp/job-man-loon.png" alt="Professional IT Support Expert - Job Man" class="img-responsive"
                                    srcset="../image/wp/job-man-loon.png 520w,
                                        ../image/wp/job-man-loon.png 235w,
                                        ../image/wp/job-man-loon.png 492w" sizes="(max-width: 500px) 100vw, 500px">
                            </picture>
                        </div>
                    </div>

                    <div class="custom-column fade-in-left order-2" style="animation-delay: 500ms;" data-aos="fade-left"
                        data-aos-duration="1100" data-aos-once="true">
                        <div class="custom-heading-widget">
                            <h1>
                                Businesses often face delays and disruptions when attempting to handle technical issues
                                without expert support. </h1>
                        </div>
                        <div class="custom-text-widget">
                            <!-- <p>Here is how we fit in the picture.</p> -->
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Section 2 -->
        <!-- <div class="content-section2 pt-13 pt-lg-17 bg-default-6 overflow-hidden"> -->
        <!-- Feature Section -->
        <!-- style="height: 237px; width: 370px;" -->
        <style>
        /* Responsive styles for feature cards */
        @media (max-width: 767px) {
            .feature-card {
                text-align: center;
                flex-direction: column !important;
            }

            .card-icon {
                margin-right: 0 !important;
                margin-bottom: 15px !important;
                display: flex;
                justify-content: center;
            }

            .card-texts {
                padding-right: 0 !important;
            }

            .gr-text-6 {
                font-size: 18px;
            }

            .gr-text-9 {
                font-size: 14px;
                line-height: 1.5;
            }
        }
        </style>
        <div class="pb-2 pb-lg-9 pt-13 pt-lg-18 bg-default-6 ">
            <div class="container">
                <!-- section-title Starts -->
                <div class="row justify-content-center">
                    <div class="col-xl-8 col-lg-9">
                        <div class="px-md-15 text-center mb-13 mb-lg-22">
                            <h2 class="title gr-text-2 mb-9" data-aos="zoom-in-down" data-aos-duration="1100"
                                data-aos-once="true">Here is how we can help.</h2>
                            <span class="ultimate-plan-notice">* Only available in Ultimate plan</span>
                            <style>
                            .title.gr-text-2 {
                                white-space: nowrap;
                            }

                            /* Ultimate Plan Notice Animation */
                            .ultimate-plan-notice {
                                display: inline-block;
                                background: #667eea;
                                color: white;
                                padding: 8px 16px;
                                border-radius: 20px;
                                font-size: 14px;
                                font-weight: 600;
                                margin-top: 10px;
                                position: relative;
                                overflow: hidden;
                                box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
                                animation: pulseGlow 2s ease-in-out infinite;
                                transition: all 0.3s ease;
                            }

                            .ultimate-plan-notice::before {
                                content: '';
                                position: absolute;
                                top: 0;
                                left: -100%;
                                width: 100%;
                                height: 100%;
                                background: rgba(255, 255, 255, 0.1);
                                animation: shimmer 3s ease-in-out infinite;
                            }

                            .ultimate-plan-notice:hover {
                                transform: scale(1.05);
                                box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
                            }

                            @keyframes pulseGlow {

                                0%,
                                100% {
                                    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
                                }

                                50% {
                                    box-shadow: 0 4px 25px rgba(102, 126, 234, 0.6);
                                }
                            }

                            @keyframes shimmer {
                                0% {
                                    left: -100%;
                                }

                                100% {
                                    left: 100%;
                                }
                            }

                            @media (max-width: 767px) {
                                .title.gr-text-2 {
                                    font-size: 28px;
                                    line-height: 1.3;
                                    white-space: nowrap;
                                }

                                .px-md-15 {
                                    padding-left: 15px !important;
                                    padding-right: 15px !important;
                                }

                                .ultimate-plan-notice {
                                    font-size: 12px;
                                    padding: 6px 12px;
                                    margin-top: 8px;
                                }
                            }

                            @media (max-width: 480px) {
                                .title.gr-text-2 {
                                    font-size: 24px;
                                    white-space: nowrap;
                                }

                                .ultimate-plan-notice {
                                    font-size: 11px;
                                    padding: 5px 10px;
                                    margin-top: 6px;
                                }
                            }



                            /* Modern Feature Cards Styles */
                            .modern-feature-card {
                                background: #ffffff;
                                border-radius: 20px;
                                padding: 30px 25px;
                                box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
                                transition: all 0.3s ease;
                                border: 1px solid #f0f0f0;
                                height: 100%;
                                display: flex;
                                flex-direction: column;
                                position: relative;
                                overflow: hidden;
                            }

                            .modern-feature-card::before {
                                content: '';
                                position: absolute;
                                top: 0;
                                left: 0;
                                right: 0;
                                height: 4px;
                                background: #6754e2;
                                transform: scaleX(0);
                                transition: transform 0.3s ease;
                            }

                            .modern-feature-card:hover {
                                transform: translateY(-8px);
                                box-shadow: 0 20px 40px rgba(0, 0, 0, 0.12);
                            }

                            .modern-feature-card:hover::before {
                                transform: scaleX(1);
                            }

                            .card-icon-wrapper {
                                display: flex;
                                justify-content: center;
                                margin-bottom: 20px;
                            }

                            .card-icon {
                                width: 70px;
                                height: 70px;
                                border-radius: 50%;
                                display: flex;
                                align-items: center;
                                justify-content: center;
                                font-size: 28px;
                                color: white;
                                transition: all 0.3s ease;
                            }

                            .card-content {
                                text-align: center;
                                flex-grow: 1;
                            }

                            .card-title {
                                font-size: 20px;
                                font-weight: 700;
                                color: #2d3748;
                                margin-bottom: 15px;
                                line-height: 1.3;
                            }

                            .card-description {
                                font-size: 15px;
                                color: #718096;
                                line-height: 1.6;
                                margin: 0;
                            }

                            /* Icon Colors */
                            /* 1st Row - #01A7E1 */
                            .computer-icon {
                                background: #01A7E1;
                            }

                            .software-icon {
                                background: #01A7E1;
                            }

                            .email-icon {
                                background: #01A7E1;
                            }

                            /* 2nd Row - #5045F1 */
                            .network-icon {
                                background: #5045F1;
                            }

                            .printer-icon {
                                background: #5045F1;
                            }

                            .account-icon {
                                background: #5045F1;
                            }

                            /* 3rd Row - #5A2D85 */
                            .virus-icon {
                                background: #5A2D85;
                            }

                            .file-icon {
                                background: #5A2D85;
                            }

                            .system-icon {
                                background: #5A2D85;
                            }

                            /* Responsive Design */
                            @media (max-width: 991px) {
                                .modern-feature-card {
                                    padding: 25px 20px;
                                }

                                .card-icon {
                                    width: 60px;
                                    height: 60px;
                                    font-size: 24px;
                                }

                                .card-title {
                                    font-size: 18px;
                                }
                            }

                            @media (max-width: 767px) {
                                .modern-feature-card {
                                    padding: 20px 15px;
                                    margin-bottom: 20px;
                                }

                                .card-icon {
                                    width: 55px;
                                    height: 55px;
                                    font-size: 22px;
                                }

                                .card-title {
                                    font-size: 17px;
                                    margin-bottom: 12px;
                                }

                                .card-description {
                                    font-size: 14px;
                                }
                            }
                            </style>
                        </div>
                    </div>
                </div>
                <!-- ./section-title Ends-->

                <div class="row justify-content-center" data-aos="zoom-in-down" data-aos-duration="1100"
                    data-aos-once="true">
                    <!-- Slow Computer Performance Card -->
                    <div class="col-lg-4 col-md-6 col-sm-12 col-12 mb-4">
                        <div class="modern-feature-card">
                            <div class="card-icon-wrapper">
                                <div class="card-icon computer-icon">
                                    <i class="fas fa-tachometer-alt"></i>
                                </div>
                            </div>
                            <div class="card-content">
                                <h3 class="card-title">Slow Computer Performance</h3>
                                <p class="card-description">Diagnosing and resolving slowness due to software issues,
                                    background processes, or low resources.</p>
                            </div>
                        </div>
                    </div>

                    <!-- Software Installation & Updates Card -->
                    <div class="col-lg-4 col-md-6 col-sm-12 col-12 mb-4">
                        <div class="modern-feature-card">
                            <div class="card-icon-wrapper">
                                <div class="card-icon software-icon">
                                    <i class="fas fa-download"></i>
                                </div>
                            </div>
                            <div class="card-content">
                                <h3 class="card-title">Software Installation & Updates</h3>
                                <p class="card-description">Installing, configuring, and updating business applications
                                    and security patches.</p>
                            </div>
                        </div>
                    </div>

                    <!-- Email Issues Card -->
                    <div class="col-lg-4 col-md-6 col-sm-12 col-12 mb-4">
                        <div class="modern-feature-card">
                            <div class="card-icon-wrapper">
                                <div class="card-icon email-icon">
                                    <i class="fas fa-envelope"></i>
                                </div>
                            </div>
                            <div class="card-content">
                                <h3 class="card-title">Email Issues</h3>
                                <p class="card-description">Troubleshooting problems with sending, receiving, syncing,
                                    or configuring email accounts (e.g., Outlook, Gmail).</p>
                            </div>
                        </div>
                    </div>
                    <!-- Network Connectivity Problems Card -->
                    <div class="col-lg-4 col-md-6 col-sm-12 col-12 mb-4">
                        <div class="modern-feature-card">
                            <div class="card-icon-wrapper">
                                <div class="card-icon network-icon">
                                    <i class="fas fa-wifi"></i>
                                </div>
                            </div>
                            <div class="card-content">
                                <h3 class="card-title">Network Connectivity Problems</h3>
                                <p class="card-description">Assisting with Wi-Fi drops, VPN setup, or internet access
                                    issues*.</p>
                            </div>
                        </div>
                    </div>

                    <!-- Printer & Peripheral Troubles Card -->
                    <div class="col-lg-4 col-md-6 col-sm-12 col-12 mb-4">
                        <div class="modern-feature-card">
                            <div class="card-icon-wrapper">
                                <div class="card-icon printer-icon">
                                    <i class="fas fa-print"></i>
                                </div>
                            </div>
                            <div class="card-content">
                                <h3 class="card-title">Printer & Peripheral Troubles</h3>
                                <p class="card-description">Resolving problems with printers, scanners, webcams, and
                                    other external devices.</p>
                            </div>
                        </div>
                    </div>

                    <!-- Account Lockouts & Password Resets Card -->
                    <div class="col-lg-4 col-md-6 col-sm-12 col-12 mb-4">
                        <div class="modern-feature-card">
                            <div class="card-icon-wrapper">
                                <div class="card-icon account-icon">
                                    <i class="fas fa-key"></i>
                                </div>
                            </div>
                            <div class="card-content">
                                <h3 class="card-title">Account Lockouts & Password Resets</h3>
                                <p class="card-description">Helping users regain access to systems and reset or manage
                                    their credentials.</p>
                            </div>
                        </div>
                    </div>
                    <!-- Virus, Malware, or Phishing Incidents Card -->
                    <div class="col-lg-4 col-md-6 col-sm-12 col-12 mb-4">
                        <div class="modern-feature-card">
                            <div class="card-icon-wrapper">
                                <div class="card-icon virus-icon">
                                    <i class="fas fa-shield-alt"></i>
                                </div>
                            </div>
                            <div class="card-content">
                                <h3 class="card-title">Virus, Malware, or Phishing Incidents</h3>
                                <p class="card-description">Identifying and responding to potential infections or
                                    security threats.</p>
                            </div>
                        </div>
                    </div>

                    <!-- File Access & Sharing Issues Card -->
                    <div class="col-lg-4 col-md-6 col-sm-12 col-12 mb-4">
                        <div class="modern-feature-card">
                            <div class="card-icon-wrapper">
                                <div class="card-icon file-icon">
                                    <i class="fas fa-folder-open"></i>
                                </div>
                            </div>
                            <div class="card-content">
                                <h3 class="card-title">File Access & Sharing Issues</h3>
                                <p class="card-description">Troubleshooting shared drive access, cloud storage problems
                                    (OneDrive, Google Drive), or file permission errors.</p>
                            </div>
                        </div>
                    </div>

                    <!-- System Crashes & Error Messages Card -->
                    <div class="col-lg-4 col-md-6 col-sm-12 col-12 mb-4">
                        <div class="modern-feature-card">
                            <div class="card-icon-wrapper">
                                <div class="card-icon system-icon">
                                    <i class="fas fa-exclamation-triangle"></i>
                                </div>
                            </div>
                            <div class="card-content">
                                <h3 class="card-title">System Crashes & Error Messages</h3>
                                <p class="card-description">Addressing blue screens, random shutdowns, and
                                    software-related system errors.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="container">
                <style>
                .photo-gallery {
                    display: flex;
                    justify-content: center;
                    flex-wrap: wrap;
                    gap: 60px;
                    margin-top: 15px;
                }

                .gallery-photo {
                    width: 100%;
                    max-height: 30px;
                    height: auto;
                    max-width: 200px;
                    width: auto;
                    border-radius: 8px;
                    box-shadow: 0 3px 5px rgba(0, 0, 0, 0.1);
                    filter: grayscale(100%);
                    opacity: 0.6;
                }

                /* Responsive styles for photo gallery */
                @media (max-width: 991px) {
                    .photo-gallery {
                        gap: 40px;
                    }

                    .gallery-photo {
                        max-width: 160px;
                        max-height: 25px;
                    }
                }

                @media (max-width: 767px) {
                    .photo-gallery {
                        gap: 30px;
                        margin-top: 10px;
                    }

                    .gallery-photo {
                        max-width: 120px;
                        max-height: 20px;
                    }
                }

                @media (max-width: 480px) {
                    .photo-gallery {
                        gap: 20px;
                        flex-direction: column;
                        align-items: center;
                    }

                    .gallery-photo {
                        margin-bottom: 15px;
                    }
                }
                </style>
                <!-- <div class="photo-gallery" data-aos="zoom-in-down" data-aos-duration="1100" data-aos-once="true">
                    <img src="../image/wp/service/sketch.png" alt="sketch" class="gallery-photo">
                    <img src="../image/wp/service/DigitalOcean.png" alt="DigitalOcean" class="gallery-photo">
                    <img src="../image/wp/service/slack.png" alt="slack" class="gallery-photo">
                    <img src="../image/wp/service/GrowBots.png" alt="GrowBots" class="gallery-photo">
                </div> -->
            </div>
            <br>
        </div>



        <!-- Content Section 3  -->
        <style>
        .content-img img {
            max-width: 80%;
            height: auto;
            border-radius: 20px;
            border: #ffffff 2px solid;
        }

        .bg-gradient {
            background: linear-gradient(to right, #473BF0, #762EE5);
            height: auto;
            min-height: 400px;
            padding: 50px 0;
        }

        /* Responsive styles for content section 3 */
        @media (max-width: 991px) {
            .content-img img {
                max-width: 90%;
            }
        }

        @media (max-width: 767px) {
            .content-img {
                text-align: center !important;
                margin-left: 0 !important;
                margin-bottom: 30px;
            }

            .content-img img {
                max-width: 80%;
                margin: 0 auto;
            }

            .bg-gradient {
                padding: 40px 0;
            }

            .col-md-6.col-lg-6.mb-n15 {
                margin-bottom: 0 !important;
            }
        }

        @media (max-width: 480px) {
            .content-img img {
                max-width: 90%;
                border-radius: 15px;
            }
        }
        </style>
        <!--  mx-md-6  mb-6-->
        <div class="bg-blue dark-mode-texts pt-13 pt-md-25 pb-13 pb-md-25 bg-gradient">
            <div class="container">
                <div class="row align-items-center">
                    <div class="col-md-6 col-lg-6 mb-n15 order-md-1 order-2" data-aos="fade-up" data-aos-duration="1100"
                        data-aos-once="true">
                        <div class="content-img text-right pr-xl-13 ml-n8">
                            <img src="../image/wp/contact-pc.jpg" alt="" class="circle-photo">
                        </div>
                    </div>
                    <div class="col-xs-11 col-sm-12 col-md-6 col-lg-5 col-xl-5 order-md-2 order-1">
                        <div class="pl-lg-3 pl-xl-12 mt-12 mt-md-0 text-md-left text-center" data-aos="fade-up"
                            data-aos-duration="1100" data-aos-once="true">
                            <h2 class="title gr-text-2 mb-7" style="font-size: 30px;">Tech Issues? We’ve Got You
                                Covered.</h2>
                            <p class="gr-text-8 gr-text-color-opacity">Our remote support team is here to troubleshoot
                                and fix your IT problems—fast, friendly, and reliable. Are you ready?</p>
                            <div class="pt-7 text-md-left text-center">
                                <a href="../front-end/buy-now.php"
                                    class="btn-link with-icon text-white btn-hover-translate-none gr-text-9 pl-0 pr-0 font-weight-bold">Let's
                                    get Started <i class="icon icon-tail-right font-weight-bold"></i></a>
                            </div>
                            <style>
                            .title.gr-text-2 {
                                word-wrap: break-word;
                                line-height: 1.2;
                            }

                            @media (max-width: 991px) {
                                .title.gr-text-2 {
                                    font-size: 2.2rem !important;
                                    line-height: 1.3;
                                }
                            }

                            @media (max-width: 767px) {
                                .title.gr-text-2 {
                                    font-size: 1.8rem !important;
                                    line-height: 1.4;
                                }

                                .btn-link.with-icon {
                                    display: inline-block;
                                    margin: 0 auto;
                                }
                            }

                            @media (max-width: 480px) {
                                .title.gr-text-2 {
                                    font-size: 1.6rem !important;
                                    line-height: 1.4;
                                }
                            }
                            </style>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- Footer section -->
        <?php
    include('../header-footer/footer.php');
    ?>
        <!-- </div> -->
        <!-- Vendor Scripts -->
        <script src="../js/vendor.min.js"></script>
        <!-- Plugin's Scripts -->
        <script src="../plugins/fancybox/jquery.fancybox.min.js"></script>
        <script src="../plugins/nice-select/jquery.nice-select.min.js"></script>
        <script src="../plugins/aos/aos.min.js"></script>
        <script src="../plugins/slick/slick.min.js"></script>
        <script src="../plugins/date-picker/js/gijgo.min.js"></script>
        <script src="../plugins/counter-up/jquery.waypoints.min.js"></script>
        <script src="../plugins/counter-up/jquery.counterup.min.js"></script>
        <script src="../plugins/theme-mode-switcher/gr-theme-mode-switcher.js"></script>
        <!-- Activation Script -->
        <script src="../js/custom.js"></script>


</body>

</html>