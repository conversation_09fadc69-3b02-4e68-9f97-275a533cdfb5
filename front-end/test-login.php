<?php
// Auto-detect environment for file paths
$is_localhost = (strpos($_SERVER['HTTP_HOST'], 'localhost') !== false || strpos($_SERVER['HTTP_HOST'], '127.0.0.1') !== false);
$file_base_path = $is_localhost ? '/helloit' : '';

require_once $_SERVER['DOCUMENT_ROOT'] . $file_base_path . '/vendor/autoload.php';
include($_SERVER['DOCUMENT_ROOT'] . $file_base_path . '/functions/server.php');
session_start();

// Auto-detect environment for URL paths
$is_localhost_url = (strpos($_SERVER['HTTP_HOST'], 'localhost') !== false || strpos($_SERVER['HTTP_HOST'], '127.0.0.1') !== false);
$base_path = $is_localhost_url ? '/helloit' : '';
$url_base = $is_localhost_url ? '/helloit' : '';

if ($_POST) {
    $email = $_POST['email'] ?? '';
    $password = $_POST['password'] ?? '';
    
    echo "<h3>Testing Login for: $email</h3>";
    
    // Check if user exists
    $user_stmt = $conn->prepare("SELECT * FROM user WHERE email = ? LIMIT 1");
    $user_stmt->bind_param("s", $email);
    $user_stmt->execute();
    $user_result = $user_stmt->get_result();
    
    if ($user_result->num_rows == 1) {
        $user_data = $user_result->fetch_assoc();
        $stored_hash = $user_data['password'];
        
        echo "<p><strong>User found:</strong> ID=" . $user_data['id'] . "</p>";
        echo "<p><strong>Stored hash:</strong> " . substr($stored_hash, 0, 50) . "...</p>";
        echo "<p><strong>Hash type:</strong> " . (strpos($stored_hash, '$2y$') === 0 || strpos($stored_hash, '$2a$') === 0 ? 'bcrypt' : 'MD5') . "</p>";
        
        // Test password verification
        if (strpos($stored_hash, '$2y$') === 0 || strpos($stored_hash, '$2a$') === 0) {
            // bcrypt hash
            $verify_result = password_verify($password, $stored_hash);
            echo "<p><strong>bcrypt verification:</strong> " . ($verify_result ? 'PASS' : 'FAIL') . "</p>";
        } else {
            // md5 hash
            $md5_hash = md5($password);
            $verify_result = ($stored_hash === $md5_hash);
            echo "<p><strong>MD5 verification:</strong> " . ($verify_result ? 'PASS' : 'FAIL') . "</p>";
            echo "<p><strong>Generated MD5:</strong> $md5_hash</p>";
        }
        
        // Check temp password
        $temp_stmt = $conn->prepare("SELECT password FROM payment_temp WHERE email = ? ORDER BY id DESC LIMIT 1");
        $temp_stmt->bind_param("s", $email);
        $temp_stmt->execute();
        $temp_result = $temp_stmt->get_result();
        
        if ($temp_result->num_rows > 0) {
            $temp_data = $temp_result->fetch_assoc();
            $temp_password = $temp_data['password'];
            echo "<p><strong>Temp password found:</strong> $temp_password</p>";
            echo "<p><strong>Temp password match:</strong> " . ($password === $temp_password ? 'YES' : 'NO') . "</p>";
        } else {
            echo "<p><strong>No temp password found</strong></p>";
        }
        
    } else {
        echo "<p><strong>User not found in main table</strong></p>";
    }
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>Login Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        form { background: #f5f5f5; padding: 20px; border-radius: 8px; max-width: 400px; }
        input { width: 100%; padding: 10px; margin: 10px 0; border: 1px solid #ddd; border-radius: 4px; }
        button { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; }
    </style>
</head>
<body>
    <h2>Login Test Tool</h2>
    <form method="POST">
        <div>
            <label>Email:</label>
            <input type="email" name="email" required>
        </div>
        <div>
            <label>Password:</label>
            <input type="password" name="password" required>
        </div>
        <button type="submit">Test Login</button>
    </form>
</body>
</html>
