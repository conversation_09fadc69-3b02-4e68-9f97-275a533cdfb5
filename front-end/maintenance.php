<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HelloIT - Service Temporarily Unavailable</title>
    <link rel="shortcut icon" href="../image/png/favicon.png" type="image/x-icon">
    <link rel="stylesheet" href="../fonts/fontawesome-5/css/all.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f8f9fa;
            color: #333;
            line-height: 1.6;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .maintenance-container {
            max-width: 600px;
            width: 90%;
            background: #fff;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            padding: 40px;
            text-align: center;
            margin: 20px;
        }

        .maintenance-icon {
            font-size: 80px;
            color: #473BF0;
            margin-bottom: 30px;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .maintenance-title {
            font-size: 32px;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 20px;
        }

        .maintenance-subtitle {
            font-size: 18px;
            color: #7f8c8d;
            margin-bottom: 30px;
            font-weight: 400;
        }

        .maintenance-message {
            font-size: 16px;
            color: #555;
            margin-bottom: 30px;
            line-height: 1.8;
        }

        .status-indicator {
            display: inline-flex;
            align-items: center;
            background: #fff3cd;
            color: #856404;
            padding: 12px 20px;
            border-radius: 25px;
            border: 1px solid #ffeaa7;
            margin-bottom: 30px;
            font-weight: 500;
        }

        .status-indicator i {
            margin-right: 8px;
            font-size: 16px;
        }

        .features-working {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
            margin: 30px 0;
            border-left: 4px solid #28a745;
        }

        .features-working h3 {
            color: #28a745;
            margin-bottom: 15px;
            font-size: 18px;
            display: flex;
            align-items: center;
        }

        .features-working h3 i {
            margin-right: 10px;
        }

        .features-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }

        .feature-item {
            display: flex;
            align-items: center;
            color: #555;
            font-size: 14px;
        }

        .feature-item i {
            color: #28a745;
            margin-right: 8px;
            font-size: 16px;
        }

        .action-buttons {
            margin-top: 30px;
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .btn {
            padding: 12px 25px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            font-size: 14px;
            display: inline-flex;
            align-items: center;
        }

        .btn i {
            margin-right: 8px;
        }

        .btn-primary {
            background: #473BF0;
            color: #fff;
        }

        .btn-primary:hover {
            background: #3d32d9;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(71, 59, 240, 0.3);
        }

        .btn-outline {
            background: transparent;
            color: #473BF0;
            border: 2px solid #473BF0;
        }

        .btn-outline:hover {
            background: #473BF0;
            color: #fff;
            transform: translateY(-2px);
        }

        .refresh-info {
            margin-top: 30px;
            padding: 20px;
            background: #e8f4fd;
            border-radius: 10px;
            border-left: 4px solid #473BF0;
        }

        .refresh-info h4 {
            color: #473BF0;
            margin-bottom: 10px;
            font-size: 16px;
        }

        .refresh-info p {
            color: #555;
            font-size: 14px;
            margin: 0;
        }

        .last-updated {
            margin-top: 30px;
            font-size: 12px;
            color: #999;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .maintenance-container {
                padding: 30px 20px;
                margin: 10px;
            }

            .maintenance-icon {
                font-size: 60px;
            }

            .maintenance-title {
                font-size: 26px;
            }

            .maintenance-subtitle {
                font-size: 16px;
            }

            .features-list {
                grid-template-columns: 1fr;
                gap: 10px;
            }

            .action-buttons {
                flex-direction: column;
                align-items: center;
            }

            .btn {
                width: 100%;
                max-width: 250px;
                justify-content: center;
            }
        }

        @media (max-width: 480px) {
            .maintenance-container {
                padding: 25px 15px;
            }

            .maintenance-icon {
                font-size: 50px;
            }

            .maintenance-title {
                font-size: 22px;
            }

            .maintenance-subtitle {
                font-size: 14px;
            }

            .maintenance-message {
                font-size: 14px;
            }
        }

        /* Loading animation for refresh */
        .refreshing {
            opacity: 0.7;
            pointer-events: none;
        }

        .refresh-spinner {
            display: none;
            width: 20px;
            height: 20px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #473BF0;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 8px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .refreshing .refresh-spinner {
            display: inline-block;
        }
    </style>
</head>
<body>
    <div class="maintenance-container">
        <div class="maintenance-icon">
            <i class="fas fa-tools"></i>
        </div>
        
        <h1 class="maintenance-title">Service Temporarily Unavailable</h1>
        <p class="maintenance-subtitle">We're experiencing some technical difficulties</p>
        
        <div class="status-indicator">
            <i class="fas fa-exclamation-triangle"></i>
            <span id="statusMessage">Checking system status...</span>
        </div>
        
        <div class="maintenance-message">
            <p>Our team is working hard to restore full functionality. Some of our backend services are currently experiencing connectivity issues, but many features are still available for your use.</p>
        </div>

        <div class="features-working">
            <h3><i class="fas fa-check-circle"></i> What's Still Working</h3>
            <div class="features-list">
                <div class="feature-item">
                    <i class="fas fa-check"></i>
                    <span>User Login & Registration</span>
                </div>
                <div class="feature-item">
                    <i class="fas fa-check"></i>
                    <span>Browse & Purchase Tickets</span>
                </div>
                <div class="feature-item">
                    <i class="fas fa-check"></i>
                    <span>Payment Processing</span>
                </div>
                <div class="feature-item">
                    <i class="fas fa-check"></i>
                    <span>Email Receipts</span>
                </div>
                <div class="feature-item">
                    <i class="fas fa-check"></i>
                    <span>Local Support Tickets</span>
                </div>
                <div class="feature-item">
                    <i class="fas fa-check"></i>
                    <span>Account Management</span>
                </div>
            </div>
        </div>

        <div class="action-buttons">
            <a href="javascript:void(0)" class="btn btn-primary" onclick="checkStatus()">
                <div class="refresh-spinner"></div>
                <i class="fas fa-sync-alt"></i>
                <span id="refreshText">Check Status</span>
            </a>
            <a href="../index.php" class="btn btn-outline">
                <i class="fas fa-home"></i>
                Continue to Homepage
            </a>
        </div>

        <div class="refresh-info">
            <h4><i class="fas fa-info-circle"></i> What We're Doing</h4>
            <p>Our technical team is actively monitoring the situation and working to restore all services. We expect normal operations to resume shortly. Thank you for your patience.</p>
        </div>

        <div class="last-updated">
            <i class="fas fa-clock"></i>
            Last updated: <span id="lastUpdated"><?php echo date('F j, Y \a\t g:i A'); ?></span>
        </div>
    </div>

    <script>
        let isChecking = false;

        function checkStatus() {
            if (isChecking) return;
            
            isChecking = true;
            const container = document.querySelector('.maintenance-container');
            const refreshText = document.getElementById('refreshText');
            const statusMessage = document.getElementById('statusMessage');
            const lastUpdated = document.getElementById('lastUpdated');
            
            // Add loading state
            container.classList.add('refreshing');
            refreshText.textContent = 'Checking...';
            statusMessage.textContent = 'Testing connection...';
            
            // Simulate API check (you can replace this with actual API call)
            fetch('../functions/appika-status-checker.php')
                .then(response => response.json())
                .then(data => {
                    if (data.online) {
                        // Redirect to homepage if services are back online
                        window.location.href = '../index.php';
                    } else {
                        statusMessage.textContent = data.message || 'Services still unavailable';
                        lastUpdated.textContent = new Date().toLocaleString();
                    }
                })
                .catch(error => {
                    statusMessage.textContent = 'Unable to check status at this time';
                    console.error('Status check failed:', error);
                })
                .finally(() => {
                    // Remove loading state
                    container.classList.remove('refreshing');
                    refreshText.textContent = 'Check Again';
                    isChecking = false;
                });
        }

        // Auto-refresh every 30 seconds
        setInterval(function() {
            if (!isChecking) {
                checkStatus();
            }
        }, 30000);

        // Initial status check
        setTimeout(checkStatus, 1000);
    </script>
</body>
</html>
