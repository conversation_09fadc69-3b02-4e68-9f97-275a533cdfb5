<?php
session_start();
include('../functions/server.php');
include('../functions/ticket-expiration-functions.php');

if (!isset($_SESSION['username'])) {
    echo "Please log in first";
    exit();
}

$username = $_SESSION['username'];

echo "<h2>Test Ticket Expiration for $username</h2>";

if (isset($_POST['create_expired_ticket'])) {
    // Create a test expired ticket
    $insert_query = "INSERT INTO purchasetickets 
                    (username, ticket_type, quantity, remaining_tickets, purchase_time, expiration_date, transactionid) 
                    VALUES (?, 'starter', 1, 1, DATE_SUB(NOW(), INTERVAL 2 YEAR), DATE_SUB(NOW(), INTERVAL 1 DAY), ?)";
    
    $transaction_id = 'TEST_' . time();
    $stmt = mysqli_prepare($conn, $insert_query);
    mysqli_stmt_bind_param($stmt, 'ss', $username, $transaction_id);
    
    if (mysqli_stmt_execute($stmt)) {
        echo "<p style='color: green;'>✅ Created test expired ticket</p>";
    } else {
        echo "<p style='color: red;'>❌ Failed to create test ticket: " . mysqli_error($conn) . "</p>";
    }
    mysqli_stmt_close($stmt);
}

if (isset($_POST['run_cleanup'])) {
    echo "<h3>Running Cleanup...</h3>";
    $cleanup_summary = removeExpiredTickets($username);
    
    if (empty($cleanup_summary)) {
        echo "<p style='color: orange;'>⚠️ No expired tickets found to remove</p>";
    } else {
        echo "<p style='color: green;'>✅ Cleanup completed!</p>";
        echo "<pre>" . print_r($cleanup_summary, true) . "</pre>";
    }
}

// Show current expired tickets
echo "<h3>Current Expired Tickets:</h3>";
$expired_tickets = getExpiredTickets($username);
if (empty($expired_tickets)) {
    echo "<p>No expired tickets found</p>";
} else {
    echo "<table border='1'>";
    echo "<tr><th>Username</th><th>Ticket Type</th><th>Remaining</th><th>Purchase Date</th><th>Expiration Date</th><th>Transaction ID</th></tr>";
    foreach ($expired_tickets as $ticket) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($ticket['username']) . "</td>";
        echo "<td>" . htmlspecialchars($ticket['ticket_type']) . "</td>";
        echo "<td>" . htmlspecialchars($ticket['remaining_tickets']) . "</td>";
        echo "<td>" . htmlspecialchars($ticket['purchase_time']) . "</td>";
        echo "<td>" . htmlspecialchars($ticket['expiration_date']) . "</td>";
        echo "<td>" . htmlspecialchars($ticket['transactionid']) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
}

// Show current expiration logs
echo "<h3>Current Expiration Logs:</h3>";
$log_query = "SELECT * FROM ticket_expiration_log WHERE username = ? ORDER BY cleanup_date DESC LIMIT 10";
$stmt = mysqli_prepare($conn, $log_query);
mysqli_stmt_bind_param($stmt, 's', $username);
mysqli_stmt_execute($stmt);
$result = mysqli_stmt_get_result($stmt);

if (mysqli_num_rows($result) == 0) {
    echo "<p>No expiration logs found</p>";
} else {
    echo "<table border='1'>";
    echo "<tr><th>ID</th><th>Ticket Type</th><th>Expired Quantity</th><th>Purchase Date</th><th>Cleanup Date</th><th>Transaction ID</th></tr>";
    while ($row = mysqli_fetch_assoc($result)) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($row['id']) . "</td>";
        echo "<td>" . htmlspecialchars($row['ticket_type']) . "</td>";
        echo "<td>" . htmlspecialchars($row['expired_quantity']) . "</td>";
        echo "<td>" . htmlspecialchars($row['purchase_date']) . "</td>";
        echo "<td>" . htmlspecialchars($row['cleanup_date']) . "</td>";
        echo "<td>" . htmlspecialchars($row['transaction_id']) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
}
mysqli_stmt_close($stmt);
?>

<form method="POST">
    <button type="submit" name="create_expired_ticket" style="background: orange; color: white; padding: 10px; margin: 5px;">Create Test Expired Ticket</button>
    <button type="submit" name="run_cleanup" style="background: red; color: white; padding: 10px; margin: 5px;">Run Cleanup</button>
</form>

<p><a href="debug-expiration-logs.php">View Debug Logs</a></p>
<p><a href="my-ticket-log.php">Back to My Ticket Logs</a></p>
