<?php
// Include database connection
include('../functions/server.php');

// Include timezone helper for proper time handling
include('../functions/timezone-helper.php');

// Check if user is logged in
session_start();
if (!isset($_SESSION['user_id'])) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'error' => 'Not logged in']);
    exit();
}

$user_id = $_SESSION['user_id'];
$username = $_SESSION['username'];

// Check if request is POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'error' => 'Invalid request method']);
    exit();
}

// Get message data
$message = isset($_POST['message']) ? trim($_POST['message']) : '';
$ticket_id = isset($_POST['ticket_id']) ? intval($_POST['ticket_id']) : null;

// Validate data
if (empty($message) || !$ticket_id) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'error' => 'Missing required data']);
    exit();
}

// Verify the ticket belongs to the user and check its status
$ticket_query = "SELECT * FROM support_tickets WHERE id = $ticket_id AND user_id = $user_id";
$ticket_result = mysqli_query($conn, $ticket_query);

if (!$ticket_result || mysqli_num_rows($ticket_result) === 0) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'error' => 'Invalid ticket']);
    exit();
}

// Check if ticket is closed or resolved
$ticket_data = mysqli_fetch_assoc($ticket_result);
if ($ticket_data['status'] === 'closed' || $ticket_data['status'] === 'resolved') {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'error' => 'This ticket is ' . $ticket_data['status'] . '. You cannot send new messages.']);
    exit();
}

// Create chat_messages table if it doesn't exist
$create_table_sql = "CREATE TABLE IF NOT EXISTS chat_messages (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    ticket_id INT(11) NOT NULL,
    sender_id INT(11) NOT NULL,
    sender_type ENUM('user', 'admin') NOT NULL,
    message TEXT NOT NULL,
    is_read TINYINT(1) DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);";

mysqli_query($conn, $create_table_sql);

// Insert message with UTC timestamp
$utc_time = UTCTimeHelper::getCurrentUTC();
$insert_query = "INSERT INTO chat_messages (ticket_id, sender_id, sender_type, message, created_at)
                VALUES (?, ?, 'user', ?, ?)";
$stmt = mysqli_prepare($conn, $insert_query);
mysqli_stmt_bind_param($stmt, 'iiss', $ticket_id, $user_id, $message, $utc_time);
$success = mysqli_stmt_execute($stmt);
$message_id = mysqli_insert_id($conn);
mysqli_stmt_close($stmt);

if ($success) {
    // admin_notifications table removed - notifications handled by chat_messages.is_read system

    // Get the inserted message
    $message_query = "SELECT cm.*,
                     u.username as sender_name
                     FROM chat_messages cm
                     JOIN user u ON cm.sender_id = u.id
                     WHERE cm.id = $message_id";
    $message_result = mysqli_query($conn, $message_query);
    $message_data = mysqli_fetch_assoc($message_result);

    // Format time in user's timezone
    $message_data['formatted_time'] = showCustomerTime($message_data['created_at'], 'M d, g:i A');

    header('Content-Type: application/json');
    echo json_encode([
        'success' => true,
        'message' => $message_data
    ]);
} else {
    header('Content-Type: application/json');
    echo json_encode([
        'success' => false,
        'error' => 'Failed to send message'
    ]);
}
