<?php
session_start();
include('../functions/server.php');
if (isset($_GET['logout'])) {
    session_destroy();
    unset($_SESSION['username']);
    header('location: ../index.php');
}
require '../vendor/autoload.php';
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>pricing</title>
    <link rel="shortcut icon" href="../image/png/favicon.png" type="image/x-icon">
    <!-- Bootstrap , fonts & icons  -->
    <link rel="stylesheet" href="../css/bootstrap.css">
    <link rel="stylesheet" href="../fonts/icon-font/css/style.css">
    <link rel="stylesheet" href="../fonts/typography-font/typo.css">
    <link rel="stylesheet" href="../fonts/fontawesome-5/css/all.css">
    <!-- Plugin'stylesheets  -->
    <link rel="stylesheet" href="../plugins/aos/aos.min.css">
    <link rel="stylesheet" href="../plugins/fancybox/jquery.fancybox.min.css">
    <link rel="stylesheet" href="../plugins/nice-select/nice-select.min.css">
    <link rel="stylesheet" href="../plugins/slick/slick.min.css">
    <link rel="stylesheet" href="../plugins/date-picker/css/gijgo.min.css" type="text/css" />
    <!-- Vendor stylesheets  -->
    <link rel="stylesheet" href="../plugins/theme-mode-switcher/switcher-panel.css">
    <link rel="stylesheet" href="../css/main.css">
    <link rel="stylesheet" href="../css/theme-mode-custom.css">
    <!-- Custom stylesheet -->


    <!-- Existing head content -->
    <script src="https://js.stripe.com/v3/"></script>

    <script> </script>
</head>

<body data-theme="light">
    <div class="site-wrapper overflow-hidden">
        <!-- Header Area -->
        <?php
        include('../header-footer/newnavtest.php');
        ?>
        <!-- navbar-dark -->
        <!-- navbar- -->
        <!-- <div class="inner-banner bg-default-2 pt-25 pt-lg-29">
            <div class="container">
                <div class="row  justify-content-center pt-5">
                    <div class="col-xl-10 col-lg-11">
                        <div class="text-center">
                            <h2 class="title gr-text-2 mb-5 mb-lg-8">Pick The Plan That Works For You</h2>
                            <p class="gr-text-6 mb-0">Whether you're a small, medium or large organization, we have the right support <br> plan that fits your need.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div> -->

        <!-- Section 1 -->
        <!-- bg-default-2 -->
        <div class="inner-banner  pt-30 pt-lg-34">
            <div class="container">
                <div class="row  justify-content-center pt-5">
                    <div class="col-xl-10 col-lg-11">
                        <div class="text-center">
                            <h2 class="title gr-text-2 mb-5 mb-lg-8">Pick The Plan That Works For You</h2>
                            <p class="gr-text-6 mb-0">Whether you're a small, medium or large organization, we have the
                                right support <br> plan that fits your need.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Promotional Banner Section -->
        <div class="promotional-banner-section">
            <div class="container-fluid">
                <div class="promotional-banner">
                    <div class="banner-content">
                        <div class="banner-left">
                            <div class="banner-text">
                                <h3>Save up to 80%<br>on IT costs</h3>
                                <p>with full support from<br>our expert team.</p>
                            </div>
                        </div>
                        <div class="banner-right">
                            <div class="banner-highlight starter" id="banner-highlight">
                                <div class="highlight-content">
                                    <h4 id="highlight-title">Starter</h4>
                                    <p id="highlight-description">– PC / Software & OS<br>– 24 hours respond time</p>
                                    <button class="compare-btn" onclick="scrollToCompare()">COMPARE ALL PLANS</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- section 2 -->
        <!-- toggle -->
        <style>
        .shadepro-pricing-tabs {
            margin-top: 20px;
            margin-bottom: 30px;
            display: flex;
            justify-content: center;
            /* Center the toggle switch */
        }

        .shadepro-pricing-tab {
            background-color: #f0f0f0;
            /* Light grey background for the container */
            border-radius: 20px;
            /* Rounded corners for the container */
            padding: 5px;
            /* Small padding inside the container */
            display: inline-flex;
            /* Align items horizontally */
            overflow: hidden;
            /* Hide any overflow from the active state */
            border: 1px solid #ddd;
            /* Optional subtle border */
        }

        .shadepro-pricing-tab a {
            display: block;
            /* padding: 10px 20px; */
            padding-left: 20px;
            padding-right: 20px;
            text-decoration: none;
            color: #777;
            /* Grey color for inactive tabs */
            font-weight: bold;
            border-radius: 20px;
            transition: background-color 0.3s ease, color 0.3s ease;
            white-space: nowrap;
            /* Prevent text from wrapping */
        }

        .shadepro-pricing-tab a.active {
            background-color: #5045F1;
            /* Blue background for the active tab */
            color: #fff;
            /* White text for the active tab */
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
            /* Subtle shadow for active state */
        }

        .shadepro-pricing-tab a:not(.active):hover {
            color: #333;
        }

        .tabs-title-toggle {
            font-family: 'Circular Std', sans-serif;
            font-size: 1rem;
            font-weight: 600;
            color: #6c757d;
            /* Neutral text color */
            text-decoration: none;
            padding: 5px 10px;
            /* Reduced padding for smaller button size */
            border-radius: 5px;
            transition: all 0.3s ease;
            height: 35px;
        }

        .tabs-title-toggle:hover {
            background-color: #e9ecef;
            /* Light hover effect */
            color: #495057;
        }

        .tabs-title-toggle.active {
            background-color: #5045F1;
            /* Active tab background */
            color: #ffffff;
            /* White text for active tab */
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.15);
            /* Slight shadow for active tab */
        }

        /* Most Popular Ribbon Styles */
        .popular-ribbon {
            position: absolute;
            top: 35px;
            right: -55px;
            background: #5045F1;
            color: white;
            padding: 5px 55px 5px 55px;
            /* Increased left and right padding */
            transform: rotate(45deg);
            font-size: 14px;
            font-weight: 600;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.4);
            z-index: 1;
            text-align: center;
        }

        .popular-ribbon::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: #5045F1;
            transform: rotate(45deg);
            z-index: -1;
            display: none;
        }

        .shadepro-pricing-item {
            position: relative;
            overflow: hidden;
        }

        /* Promotional Banner Styles */
        .promotional-banner-section {
            padding: 50px 0;
            background-color: #f8f9fa;
        }

        .promotional-banner {
            max-width: 1200px;
            margin: 0 auto;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 50%, #6c5ce7 100%);
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .banner-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
            min-height: 200px;
            position: relative;
            padding: 40px 60px;
        }

        @media (min-width: 992px) {
                  .banner-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
            /* min-height: 100px; */
            max-height: 200px;
            position: relative;
            padding: 40px 60px;
        }
        }

        .banner-left {
            flex: 1;
            text-align: left;
            padding-right: 40px;
        }

        .banner-right {
            flex: 0 0 auto;
            padding: 0;
        }



        .banner-text h3 {
            font-size: 2.5rem;
            font-weight: 700;
            color: #2d3748;
            margin-bottom: 10px;
            line-height: 1.2;
        }

        .banner-text p {
            font-size: 1.2rem;
            color: #4a5568;
            margin-bottom: 0;
            line-height: 1.4;
        }

        .banner-right {
            position: relative;
        }

        .banner-highlight {
            background: #ffffff;
            border: 1px solid #e5e7eb;
            border-radius: 16px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            padding: 0;
            text-align: center;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            width: 350px;
            min-height: 200px;
            display: flex;
            flex-direction: column;
        }

        .banner-highlight:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
        }

        /* Top colored border for different plans */
        .banner-highlight::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: #fbbf24; /* Default to Starter */
        }

        .banner-highlight.starter::before {
            background: #fbbf24; /* Starter - Yellow */
        }

        .banner-highlight.business::before {
            background: #01A7E1; /* Business - Blue */
        }

        .banner-highlight.ultimate::before {
            background: #793BF0; /* Ultimate - Purple */
        }

        .highlight-content {
            padding: 30px 24px;
            display: flex;
            flex-direction: column;
            height: 100%;
            justify-content: center;
        }

        .highlight-content h4 {
            font-size: 28px;
            font-weight: 700;
            color: #374151;
            margin-bottom: 8px;
            text-align: center;
        }

        .highlight-content p {
            font-size: 16px;
            color: #6b7280;
            margin-bottom: 24px;
            line-height: 1.4;
            text-align: center;
        }

        .compare-btn {
            background: #5045F1;
            color: white;
            border: none;
            padding: 14px 24px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: auto;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .compare-btn:hover {
            background: #3E36C0;
            transform: translateY(-2px);
        }

        /* Responsive Design */
        @media (max-width: 992px) {
            .banner-content {
                flex-direction: column;
                text-align: center;
                padding: 30px 20px;
            }

            .banner-left,
            .banner-right {
                margin: 15px 0;
                text-align: center !important;
            }

            .banner-left {
                padding-right: 0;
            }

            .banner-center {
                padding: 20px 0;
                order: -1;
            }

            .banner-highlight {
                position: static;
                transform: none;
                margin: 20px auto;
                display: inline-block;
            }

            .banner-text h3 {
                font-size: 2rem;
            }

            .banner-text p {
                font-size: 1.1rem;
            }
        }

        @media (max-width: 768px) {
            .promotional-banner-section {
                padding: 20px 15px;
            }

            .promotional-banner {
                margin: 0 15px;
            }

            .banner-content {
                padding: 20px 15px;
            }

            .banner-image {
                width: 150px;
                height: 150px;
            }

            .banner-text h3 {
                font-size: 1.8rem;
            }

            .banner-text p {
                font-size: 1rem;
            }

            .banner-center {
                padding: 15px 0;
            }

            .highlight-content h4 {
                font-size: 1.3rem;
            }

            .highlight-content p {
                font-size: 0.9rem;
            }

            .compare-btn {
                font-size: 0.8rem;
                padding: 6px 12px;
            }
        }

        @media (max-width: 480px) {
            .banner-text h3 {
                font-size: 1.5rem;
            }

            .banner-text p {
                font-size: 0.9rem;
            }

            .banner-image {
                width: 120px;
                height: 120px;
            }

            .banner-highlight {
                padding: 15px 20px;
            }
        }

        /* Compare Plans Section Styles */
        .compare-plans-section {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 30px 0 60px 0 !important;
            margin-top: 0 !important;
        }

        /* Override Bootstrap utility classes that add excessive padding */
        .container-fluid.pricing-container {
            padding-bottom: 50px !important;
            padding-top: 0 !important;
        }

        /* Override any pt-30, py-30 classes that might be applied */
        .pt-30, .py-30 {
            padding-top: 50px !important;
        }

        .comparison-table-wrapper {
            background: white;
            border-radius: 20px;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            margin: 0 auto;
            max-width: 1000px;
        }

        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 0;
            font-family: 'Circular Std', sans-serif;
        }

        .comparison-table thead tr {
            background: linear-gradient(135deg, #6c5ce7 0%, #5045F1 100%);
        }

        .comparison-table th {
            padding: 20px 15px;
            text-align: center;
            font-weight: 700;
            font-size: 18px;
            color: white;
            border: none;
            position: relative;
        }

        .feature-column {
            background: rgba(255, 255, 255, 0.1) !important;
            text-align: left !important;
            padding-left: 25px !important;
            width: 25%;
        }

        .plan-column {
            width: 25%;
        }

        .starter-column {
            background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%) !important;
        }

        .business-column {
            background: linear-gradient(135deg, #01A7E1 0%, #0284c7 100%) !important;
        }

        .ultimate-column {
            background: linear-gradient(135deg, #793BF0 0%, #6d28d9 100%) !important;
        }

        .comparison-table tbody tr {
            border-bottom: 1px solid #e5e7eb;
            transition: background-color 0.3s ease;
        }

        .comparison-table tbody tr:hover {
            background-color: #f8f9fa;
        }

        .comparison-table tbody tr:last-child {
            border-bottom: none;
        }

        .comparison-table td {
            padding: 20px 15px;
            text-align: center;
            vertical-align: middle;
            font-size: 16px;
            line-height: 1.4;
        }

        .feature-name {
            text-align: left !important;
            padding-left: 25px !important;
            font-weight: 600;
            color: #374151;
            background-color: #f8f9fa;
        }

        .plan-value {
            font-weight: 500;
            color: #4b5563;
        }

        .status-icon {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            font-weight: 700;
            font-size: 14px;
        }

        .status-icon.included {
            background: #10b981;
            color: white;
        }

        .status-icon.excluded {
            background: #ef4444;
            color: white;
        }

        .feature-included {
            color: #374151 !important;
            font-weight: 500 !important;
        }

        .feature-excluded {
            color: #9ca3af !important;
        }

        /* Responsive Design for Comparison Table */
        @media (max-width: 992px) {
            .comparison-table th,
            .comparison-table td {
                padding: 15px 10px;
                font-size: 14px;
            }

            .comparison-table th {
                font-size: 16px;
            }

            .feature-name {
                padding-left: 15px !important;
            }
        }

        @media (max-width: 768px) {
            /* Add more padding-top for mobile screens */
            body {
                padding-top: 0px !important;
            }

            .compare-plans-section {
                padding: 40px 0;
            }

            .comparison-table-wrapper {
                margin: 0 15px;
                border-radius: 15px;
            }

            .comparison-table th,
            .comparison-table td {
                padding: 12px 8px;
                font-size: 13px;
            }

            .comparison-table th {
                font-size: 14px;
            }

            .feature-name {
                padding-left: 12px !important;
            }

            .status-icon {
                width: 20px;
                height: 20px;
                font-size: 12px;
            }
        }

        @media (max-width: 576px) {
            .comparison-table th,
            .comparison-table td {
                padding: 10px 6px;
                font-size: 12px;
            }

            .comparison-table th {
                font-size: 13px;
            }

            .feature-name {
                padding-left: 10px !important;
            }

            .plan-value br {
                display: none;
            }

            .plan-value {
                line-height: 1.2;
            }
        }
        </style>
        <!--                            ///////////////////////////////////////////// -->

        <div class="container-fluid pricing-container" style="padding-left: 100px; padding-right: 100px;">
            <div class="row">
                <div class="col-12 text-center">
                    <div class="shadepro-pricing-tabs style-2">
                        <div class="shadepro-pricing-tab">
                            <a href="javascript:" class="tabs-title-toggle first-tabs-title active"
                                data-pricing-tab-trigger="" data-target="#shadepro-dynamic-deck"
                                data-category="starter">
                                Starter
                            </a>
                            <a href="javascript:" class="tabs-title-toggle second-tabs-title"
                                data-pricing-tab-trigger="" data-target="#shadepro-dynamic-deck"
                                data-category="business">
                                Business
                            </a>
                            <a href="javascript:" class="tabs-title-toggle second-tabs-title"
                                data-pricing-tab-trigger="" data-target="#shadepro-dynamic-deck"
                                data-category="ultimate">
                                Ultimate
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <!-- product pricing -->
            <div class="pricing-box-wrap d-flex flex-wrap justify-content-center" id="shadepro-dynamic-deck"
                data-pricing-dynamic="" data-value-active="monthly">
                <?php
                // Fetch products from the database
                $sql = "SELECT ticketid, ticket_type, package_size, info ,numbers_per_package, dollar_price_per_package FROM tickets";
                $result = $conn->query($sql);
                if ($result->num_rows > 0) {
                    while ($row = $result->fetch_assoc()) {
                        echo '<div class="shadepro-pricing-item-wrap" data-category="' . htmlspecialchars(strtolower($row['ticket_type'])) . '">';
                        echo '<div class="shadepro-pricing-item" data-plan="' . htmlspecialchars(strtolower($row['ticket_type'])) . '">';

                        // Add Most Popular ribbon for 25 Ticket Credits
                        if ($row['numbers_per_package'] == 25) {
                            echo '<div class="popular-ribbon">Most Popular</div>';
                        }

                        // Plan name
                        echo '<div class="plan-name">' . htmlspecialchars($row['ticket_type']) . '</div>';

                        // Price section
                        echo '<div class="price-section">';

                        // Check if this is the $350 package for discount display
                        if ($row['dollar_price_per_package'] == 350) {
                            echo '<div class="original-price">375</div>';
                        }

                        echo '<div class="price-amount">' . number_format($row['dollar_price_per_package']) . '<span class="currency">USD</span></div>';
                        echo '<div class="price-label">' . htmlspecialchars($row['numbers_per_package']) . ' Ticket Credits</div>';
                        echo '</div>';

                        // Description - Service types based on plan
                        echo '<div class="plan-description">';
                        switch (strtoupper($row['ticket_type'])) {
                            case 'STARTER':
                                echo 'Ticketing';
                                break;
                            case 'BUSINESS':
                                echo 'Ticketing / Chat / Email / Remote Access';
                                break;
                            case 'ULTIMATE':
                                echo 'Ticketing / Chat / Email / Remote Access';
                                break;
                            default:
                                echo 'Ticketing';
                                break;
                        }
                        echo '</div>';

                        // Features section
                        echo '<div class="features-section">';
                        echo '<div class="features-wrapper">';
                        echo '<ul class="features-list">';
                        switch (strtoupper($row['ticket_type'])) {
                            case 'STARTER':
                                echo '<li class="feature-included"><i class="fas fa-check"></i> <b style="font-weight: 700;">24 hours respond time</b></li>';
                                echo '<li class="feature-included"><i class="fas fa-check"></i> <b style="font-weight: 700;">Online knowledge base</b></li>';
                                echo '<li class="feature-included"><i class="fas fa-check"></i> Basic PC troubleshooting</li>';
                                echo '<li class="feature-included"><i class="fas fa-check"></i> Software & OS support</li>';
                                break;
                            case 'BUSINESS':
                                echo '<li class="feature-included"><i class="fas fa-check"></i> <b style="font-weight: 700;">24 hours respond time</b></li>';
                                echo '<li class="feature-included"><i class="fas fa-check"></i> <b style="font-weight: 700;">Online knowledge base</b></li>';
                                echo '<li class="feature-included"><i class="fas fa-check"></i> PC & Network troubleshooting (software, OS, hardware issues)</li>';
                                echo '<li class="feature-included"><i class="fas fa-check"></i> + Custom Configurations</li>';
                                break;
                            case 'ULTIMATE':
                                echo '<li class="feature-included"><i class="fas fa-check"></i> <b style="font-weight: 700;">4 hours respond time</b></li>';
                                echo '<li class="feature-included"><i class="fas fa-check"></i> <b style="font-weight: 700;">Online knowledge base</b></li>';
                                echo '<li class="feature-included"><i class="fas fa-check"></i> PC & Network troubleshooting (software, OS, hardware issues)</li>';
                                echo '<li class="feature-included"><i class="fas fa-check"></i> + Custom Configurations</li>';
                                echo '<li class="feature-included"><i class="fas fa-check"></i> Dedicated AE</li>';
                                echo '<li class="feature-included"><i class="fas fa-check"></i> Priority Help Desk</li>';
                                break;
                            default:
                                echo '<p>' . htmlspecialchars($row['info']) . '</p>';
                                break;
                        }
                        echo '</ul>'; // Close features list
                        echo '</div>'; // Close features wrapper
                        echo '</div>'; // Close features section

                        // Action button section
                        echo '<div class="action-section">';
                        echo '<button class="get-plan-btn" onclick="addToCart(' . htmlspecialchars($row['ticketid']) . ')">';
                        echo 'Get this plan';
                        echo '</button>';
                        echo '<span style="font-size: 12px; margin-top: 20px;">09:00 AM - 06:00 PM (GMT+8)</span>';
                        echo '</div>';

                        echo '</div>'; // Close pricing item
                        echo '</div>'; // Close pricing item wrap
                    }
                } else {
                    echo '<p>No products available.</p>';
                }
                // Don't close connection here - move to end of file
                ?>
            </div>
        </div>

        <!-- Compare All Plans Section -->
        <div class="compare-plans-section" id="compare-plans">
            <div class="container">
                <div class="row justify-content-center">
                    <div class="col-12 text-center mb-10">
                        <h2 class="title gr-text-2 mb-5">Compare All Plans</h2>
                        <p class="gr-text-6 mb-0">Choose the perfect plan that fits your business needs</p>
                    </div>
                </div>

                <div class="row justify-content-center">
                    <div class="col-12">
                        <div class="comparison-table-wrapper">
                            <div class="table-responsive">
                                <table class="comparison-table">
                                    <thead>
                                        <tr>
                                            <th class="feature-column">SERVICE</th>
                                            <th class="plan-column starter-column">STARTER</th>
                                            <th class="plan-column business-column">BUSINESS</th>
                                            <th class="plan-column ultimate-column">ULTIMATE</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td class="feature-name">Response Time</td>
                                            <td class="plan-value">Within 24 hours</td>
                                            <td class="plan-value">Within 24 hours</td>
                                            <td class="plan-value">Within 4 hours</td>
                                        </tr>
                                        <tr>
                                            <td class="feature-name">Contact Channels</td>
                                            <td class="plan-value">Ticket</td>
                                            <td class="plan-value">Ticket + Chat + Email<br>+ Remote</td>
                                            <td class="plan-value">Ticket + Chat<br>+ Email + Remote</td>
                                        </tr>
                                        <tr>
                                            <td class="feature-name">Support Scope</td>
                                            <td class="plan-value">PC</td>
                                            <td class="plan-value">PC + Network</td>
                                            <td class="plan-value">PC + Network</td>
                                        </tr>
                                        <tr>
                                            <td class="feature-name">Remote Support</td>
                                            <td class="plan-value feature-excluded">
                                                <span class="status-icon excluded">✕</span>
                                            </td>
                                            <td class="plan-value feature-included">
                                                <span class="status-icon included">✓</span>
                                            </td>
                                            <td class="plan-value feature-included">
                                                <span class="status-icon included">✓</span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="feature-name">Dedicated Account<br>Manager</td>
                                            <td class="plan-value feature-excluded">
                                                <span class="status-icon excluded">✕</span>
                                            </td>
                                            <td class="plan-value feature-excluded">
                                                <span class="status-icon excluded">✕</span>
                                            </td>
                                            <td class="plan-value feature-included">
                                                <span class="status-icon included">✓</span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="feature-name">Priority Help Desk</td>
                                            <td class="plan-value feature-excluded">
                                                <span class="status-icon excluded">✕</span>
                                            </td>
                                            <td class="plan-value feature-excluded">
                                                <span class="status-icon excluded">✕</span>
                                            </td>
                                            <td class="plan-value feature-included">
                                                <span class="status-icon included">✓</span>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Cart Success Modal -->
        <div class="modal fade" id="cartSuccessModal" tabindex="-1" role="dialog"
            aria-labelledby="cartSuccessModalLabel" aria-hidden="true" data-backdrop="static">
            <div class="modal-dialog modal-dialog-centered" role="document">
                <div class="modal-content">
                    <div class="modal-body text-center py-5">
                        <div class="success-animation">
                            <svg class="checkmark" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 52 52">
                                <circle class="checkmark__circle" cx="26" cy="26" r="25" fill="none" />
                                <path class="checkmark__check" fill="none" d="M14.1 27.2l7.1 7.2 16.7-16.8" />
                            </svg>
                        </div>
                        <h3 class="mt-4" id="cartSuccessMessage">Added to cart successfully!</h3>
                        <p class="mb-4">Your item has been added to your shopping cart.</p>
                        <p class="text-muted">This popup will close automatically in <span id="countdown"
                                data-initial-value="3">3</span> seconds...</p>
                        <div class="mt-4">
                            <!-- <button type="button" class="btn btn-outline-primary mr-2" data-dismiss="modal">Continue Shopping</button> -->
                            <?php
                            // Auto-detect environment for cart URL
                            $is_localhost = (strpos($_SERVER['HTTP_HOST'], 'localhost') !== false || strpos($_SERVER['HTTP_HOST'], '127.0.0.1') !== false);
                            $cart_url = $is_localhost ? '/helloit/support-ticket/cart' : '/support-ticket/cart';
                            ?>
                            <a href="<?php echo $cart_url; ?>" class="btn btn-primary">Go to Cart</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Limit Reached Modal -->
        <div class="modal fade" id="limitReachedModal" tabindex="-1" role="dialog"
            aria-labelledby="limitReachedModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered" role="document">
                <div class="modal-content">
                    <div class="modal-body text-center py-5">
                        <div class="success-animation" style="margin: 0 auto; width: 100px; height: 100px;">
                            <svg class="crossmark" width="100" height="100" viewBox="0 0 52 52">
                                <circle class="crossmark__circle" cx="26" cy="26" r="25" fill="none" />
                                <path class="crossmark__path crossmark__path--right" fill="none" d="M16,16 l20,20"
                                    stroke="#ff4d4f" stroke-width="4" stroke-linecap="round" />
                                <path class="crossmark__path crossmark__path--left" fill="none" d="M16,36 l20,-20"
                                    stroke="#ff4d4f" stroke-width="4" stroke-linecap="round" />
                            </svg>
                        </div>
                        <h3 class="mt-4" id="limitReachedTitle" style="font-weight:700;">Limit Reached</h3>
                        <p class="mb-4" style="color:#555;">Please reduce the number of tickets in your cart.</p>
                        <button type="button" class="btn btn-primary mt-2 px-4" data-dismiss="modal"
                            data-bs-dismiss="modal">Close</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Cart Error Modal -->
        <div class="modal fade" id="cartErrorModal" tabindex="-1" role="dialog" aria-labelledby="cartErrorModalLabel"
            aria-hidden="true" data-backdrop="static">
            <div class="modal-dialog modal-dialog-centered" role="document">
                <div class="modal-content">
                    <div class="modal-body text-center py-5">
                        <div class="error-animation">
                            <svg class="crossmark" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 52 52">
                                <circle class="crossmark__circle" cx="26" cy="26" r="25" fill="none" />
                                <path class="crossmark__path crossmark__path--right" fill="none" d="M16,16 l20,20" />
                                <path class="crossmark__path crossmark__path--left" fill="none" d="M16,36 l20,-20" />
                            </svg>
                        </div>
                        <h3 class="mt-4" id="cartErrorMessage">Failed to add to cart</h3>
                        <p class="mb-4">Please try again or contact customer support.</p>
                        <div class="mt-4">
                            <button type="button" class="btn btn-danger" data-dismiss="modal" data-bs-dismiss="modal"
                                style="color: white;">Close</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Add to cart script-->
        <script>
        // Function to add product to cart via AJAX
        function addToCart(ticket_id) {
            var quantity = 1; // Set quantity to 1
            var xhr = new XMLHttpRequest();
            xhr.open("POST", "cart-add", true);
            xhr.setRequestHeader("Content-Type", "application/x-www-form-urlencoded");
            xhr.send("ticket_id=" + ticket_id + "&quantity=" + quantity);
            xhr.onload = function() {
                if (xhr.status == 200) {
                    try {
                        var response = JSON.parse(xhr.responseText);
                        if (response.status == 'success') {
                            document.getElementById('cartSuccessMessage').textContent = response.message ||
                                'Added to cart successfully!';
                            document.getElementById('countdown').textContent = '3';
                            $('#cartSuccessModal').modal('show');
                            var countdownElement = document.getElementById('countdown');
                            countdownElement.textContent = '3';
                            var secondsLeft = 3;
                            if (window.countdownInterval) {
                                clearInterval(window.countdownInterval);
                            }
                            window.countdownInterval = setInterval(function() {
                                secondsLeft--;
                                countdownElement.textContent = secondsLeft;
                                if (secondsLeft <= 0) {
                                    clearInterval(window.countdownInterval);
                                    $('#cartSuccessModal').modal('hide');
                                }
                            }, 1000);
                            $('#cartSuccessModal').on('hidden.bs.modal', function() {
                                if (window.countdownInterval) {
                                    clearInterval(window.countdownInterval);
                                }
                            });
                        } else if (
                            response.message &&
                            (
                                response.message === 'This ticket plan has already reached the limit of 10.' ||
                                response.message.toLowerCase().includes('limit of 10')
                            )
                        ) {
                            // Show the Limit Reached Modal only
                            $('#limitReachedModal').modal('show');
                        } else {
                            document.getElementById('cartErrorMessage').textContent = response.message ||
                                'Failed to add to cart. Please try again.';
                            $('#cartErrorModal').modal('show');
                        }
                    } catch (e) {
                        document.getElementById('cartErrorMessage').textContent =
                            'An unexpected error occurred. Please try again.';
                        $('#cartErrorModal').modal('show');
                    }
                } else {
                    document.getElementById('cartErrorMessage').textContent =
                        'Failed to add to cart. Please try again.';
                    $('#cartErrorModal').modal('show');
                }
            };
            xhr.onerror = function() {
                document.getElementById('cartErrorMessage').textContent =
                    'Network error. Please check your connection and try again.';
                $('#cartErrorModal').modal('show');
            };
        }
        </script>


        <style>
        /* Success Animation Styles */
        .success-animation {
            margin: 0 auto;
            width: 100px;
            height: 100px;
        }

        .checkmark {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            display: block;
            stroke-width: 2;
            stroke: #4bb71b;
            stroke-miterlimit: 10;
            box-shadow: inset 0px 0px 0px #4bb71b;
            animation: fill .4s ease-in-out .4s forwards, scale .3s ease-in-out .9s both;
        }

        .checkmark__circle {
            stroke-dasharray: 166;
            stroke-dashoffset: 166;
            stroke-width: 1;
            stroke-miterlimit: 2;
            stroke: #4bb71b;
            fill: none;
            animation: stroke 0.6s cubic-bezier(0.65, 0, 0.45, 1) forwards, resetStrokeWidth 0.1s 1s forwards;
        }

        .checkmark__check {
            transform-origin: 50% 50%;
            stroke-dasharray: 48;
            stroke-dashoffset: 48;
            animation: stroke 0.3s cubic-bezier(0.65, 0, 0.45, 1) 0.8s forwards;
        }

        @keyframes stroke {
            100% {
                stroke-dashoffset: 0;
            }
        }

        @keyframes scale {

            0%,
            100% {
                transform: none;
            }

            50% {
                transform: scale3d(1.1, 1.1, 1);
            }
        }

        @keyframes fill {
            100% {
                box-shadow: inset 0px 0px 0px 1px #4bb71b;
            }
        }

        @keyframes resetStrokeWidth {
            0% {
                stroke-width: 1;
            }

            100% {
                stroke-width: 1;
            }
        }

        /* Error Animation Styles */
        .error-animation {
            margin: 0 auto;
            width: 100px;
            height: 100px;
        }

        .crossmark {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            display: block;
            stroke-width: 2;
            stroke: #ff6245;
            stroke-miterlimit: 10;
            box-shadow: inset 0px 0px 0px #ff6245;
            animation: fillError .4s ease-in-out .4s forwards, scale .3s ease-in-out .9s both;
        }

        .crossmark__circle {
            stroke-dasharray: 166;
            stroke-dashoffset: 166;
            stroke-width: 1;
            stroke-miterlimit: 2;
            stroke: #ff6245;
            fill: none;
            animation: stroke 0.6s cubic-bezier(0.65, 0, 0.45, 1) forwards;
        }

        .crossmark__path {
            transform-origin: 50% 50%;
            stroke-dasharray: 48;
            stroke-dashoffset: 48;
            animation: stroke 0.3s cubic-bezier(0.65, 0, 0.45, 1) 0.8s forwards;
        }

        @keyframes fillError {
            100% {
                box-shadow: inset 0px 0px 0px 1px #ff6245;
            }
        }

        /* General Styles */
        .pricing-box-wrap {
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
            gap: 20px;
        }

        /* Legacy styles - keeping for compatibility */
        .shadepro-pricing-btn,
        .shadepro-pricing-btn:hover {
            color: white;
        }


        /* Clean Pricing Cards - 3 per row */
        .shadepro-pricing-item-wrap {
            flex: 0 0 calc(30% - 16px);
            max-width: calc(30% - 16px);
            box-sizing: border-box;
            margin: 8px;
        }

        .shadepro-pricing-item {
            background: #ffffff;
            border: 1px solid #e5e7eb;
            border-radius: 16px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            padding: 0;
            text-align: center;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            height: 100%;
            display: flex;
            flex-direction: column;
        }

        .shadepro-pricing-item:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
        }

        /* Top colored border for each plan */
        .shadepro-pricing-item[data-plan="starter"]::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: #fbbf24;
            /* Starter */
        }

        .shadepro-pricing-item[data-plan="business"]::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: #01A7E1;
            /* Business */
        }

        .shadepro-pricing-item[data-plan="ultimate"]::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: #793BF0;
            /* Ultimate */
        }

        /* Plan Name */
        .plan-name {
            padding: 20px 16px 12px;
            font-size: 22px;
            font-weight: 600;
            color: #374151;
            text-align: center;
        }

        /* Price Section */
        .price-section {
            padding: 0 16px 12px;
            text-align: center;
        }

        .price-amount {
            font-size: 42px;
            font-weight: 700;
            color: #111827;
            line-height: 1;
            margin-bottom: 4px;
        }

        .currency {
            font-size: 16px;
            font-weight: 500;
            color: #6b7280;
            margin-left: 4px;
            vertical-align: baseline;
        }

        /* Original price (crossed out) */
        .original-price {
            font-size: 18px;
            color: #9ca3af;
            text-decoration: line-through;
            margin-bottom: 2px;
            font-weight: 500;
        }

        .original-price .currency {
            font-size: 12px;
            font-weight: 400;
            color: #9ca3af;
            margin-left: 2px;
            vertical-align: baseline;
        }

        .price-label {
            font-size: 16px;
            color: #6b7280;
            font-weight: 500;
        }

        /* Description */
        .plan-description {
            padding: 0 16px 16px;
            font-size: 16px;
            color: black;
            line-height: 1.4;
            text-align: center;
            font-weight: 700;
        }

        /* Features Section */
        .features-section {
            padding: 0 16px 20px !important;
            flex-grow: 1;
            display: flex !important;
            justify-content: center !important;
            align-items: flex-start !important;
            width: 100% !important;
        }

        .features-wrapper {
            display: flex !important;
            justify-content: center !important;
            width: 100% !important;
        }

        .features-list {
            list-style: none !important;
            padding: 0 !important;
            margin: 0 auto !important;
            text-align: left !important;
            width: 280px !important;
            display: block !important;
        }

        .features-list li {
            display: flex;
            align-items: center;
            padding: 6px 0;
            font-size: 16px;
            line-height: 1.3;
        }

        .features-list i {
            width: 16px;
            height: 16px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            flex-shrink: 0;
            font-size: 10px;
        }

        .feature-included i {
            background: #10b981;
            color: white;
        }

        .feature-excluded i {
            background: #ef4444;
            color: white;
        }

        .feature-included {
            color: #374151;
            font-weight: 500;
        }

        li.feature-included {
            margin-left: 22px;
        }

        .feature-excluded {
            color: #9ca3af;
            font-weight: 400;
        }

        /* Action Section */
        .action-section {
            padding: 0 16px 24px;
            margin-top: auto;
        }

        .get-plan-btn {
            width: 100%;
            background: #5045F1;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 20px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .get-plan-btn:hover {
            transform: translateY(-2px);
            background: #3E36C0;
        }

        .get-plan-btn:active {
            transform: translateY(0);
        }

        /* Responsive Styles */
        @media (max-width: 1200px) {
            .shadepro-pricing-item-wrap {
                flex: 0 0 calc(31% - 12px);
                max-width: calc(31% - 12px);
                margin: 6px;
            }
        }

        @media (max-width: 900px) {
            .shadepro-pricing-item-wrap {
                flex: 0 0 calc(48% - 12px);
                max-width: calc(48% - 12px);
                margin: 6px;
            }

            .plan-name {
                font-size: 20px;
                padding: 16px 12px 10px;
            }

            .price-amount {
                font-size: 36px;
            }

            .currency {
                font-size: 14px;
            }

            .original-price {
                font-size: 16px;
            }
        }

        @media (max-width: 768px) {

            .shadepro-pricing-item-wrap {
                flex: 0 0 100%;
                max-width: 100%;
                margin: 12px 0;
            }

            .shadepro-pricing-item {
                margin: 0 auto;
                max-width: 350px;
            }

            .plan-name {
                font-size: 20px;
                padding: 20px 20px 12px;
            }

            .price-section {
                padding: 0 20px 12px;
            }

            .price-amount {
                font-size: 36px;
            }

            .currency {
                font-size: 12px;
            }

            .original-price {
                font-size: 14px;
            }

            .plan-description {
                padding: 0 20px 16px;
                font-size: 16px;
            }

            .features-section {
                padding: 0 20px 20px !important;
                display: flex !important;
                justify-content: center !important;
                align-items: flex-start !important;
            }

            .features-wrapper {
                display: flex !important;
                justify-content: center !important;
                width: 100% !important;
            }

            .features-list {
                width: 250px !important;
                margin: 0 auto !important;
                text-align: left !important;
            }

            .features-list li {
                font-size: 16px;
                padding: 6px 0;
            }

            .features-list i {
                width: 14px;
                height: 14px;
                font-size: 9px;
                margin-right: 10px;
            }

            .action-section {
                padding: 0 20px 24px;
            }

            .get-plan-btn {
                padding: 10px 20px;
                font-size: 16px;
                border-radius: 20px;
            }
        }

        /* Responsive container padding */
        @media (max-width: 1200px) {
            .pricing-container {
                padding-left: 50px !important;
                padding-right: 50px !important;
            }
        }

        @media (max-width: 992px) {
            .pricing-container {
                padding-left: 30px !important;
                padding-right: 30px !important;

            }
        }

        @media (max-width: 768px) {
            .pricing-container {
                padding-left: 15px !important;
                padding-right: 15px !important;
            }

            .pt-30,
            .py-30 {
                /* padding-top: 9.6875rem !important; */
                margin-top: 100px !important;
            }
        }
        </style>
        <style>
        .custom-animated-modal {
            border-radius: 16px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.18);
            border: none;
            animation: limitFadeIn 0.35s cubic-bezier(.4, 0, .2, 1);
            max-width: 400px;
            margin: 0 auto;
        }

        @keyframes limitFadeIn {
            0% {
                opacity: 0;
                transform: scale(0.85);
            }

            100% {
                opacity: 1;
                transform: scale(1);
            }
        }

        .limit-animation {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-bottom: 10px;
            animation: popIn 0.5s cubic-bezier(.4, 0, .2, 1);
        }

        @keyframes popIn {
            0% {
                transform: scale(0.7);
                opacity: 0;
            }

            80% {
                transform: scale(1.1);
                opacity: 1;
            }

            100% {
                transform: scale(1);
            }
        }

        @media (max-width: 600px) {
            .custom-animated-modal {
                max-width: 95vw;
            }

            .modal-body {
                padding: 2rem 0.5rem !important;
            }
        }
        </style>
        <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Reset countdown whenever the success modal is shown
            $('#cartSuccessModal').on('show.bs.modal', function() {
                // Reset the countdown text to initial value
                var countdownElement = document.getElementById('countdown');
                var initialValue = countdownElement.getAttribute('data-initial-value') || '3';
                countdownElement.textContent = initialValue;
            });
            const tabs = document.querySelectorAll('.shadepro-pricing-tab a');
            const items = document.querySelectorAll('.shadepro-pricing-item-wrap');

            tabs.forEach(tab => {
                tab.addEventListener('click', function(event) {
                    event.preventDefault();
                    const category = this.dataset.category;

                    // Remove active class from all tabs
                    tabs.forEach(t => t.classList.remove('active'));
                    // Add active class to the clicked tab
                    this.classList.add('active');

                    // Update banner highlight based on selected category
                    updateBannerHighlight(category);

                    // Show/hide items based on the selected category
                    items.forEach(item => {
                        if (category === 'all' || item.dataset.category === category) {
                            item.style.display = 'block';
                        } else {
                            item.style.display = 'none';
                        }
                    });
                });
            });

            // Function to update banner highlight based on selected plan
            function updateBannerHighlight(category) {
                const bannerHighlight = document.getElementById('banner-highlight');
                const highlightTitle = document.getElementById('highlight-title');
                const highlightDescription = document.getElementById('highlight-description');

                // Remove existing plan classes
                bannerHighlight.classList.remove('starter', 'business', 'ultimate');

                // Add the appropriate class and update content
                switch(category) {
                    case 'starter':
                        bannerHighlight.classList.add('starter');
                        highlightTitle.textContent = 'Starter';
                        highlightDescription.innerHTML = 'PC <br> 24 hours respond time';
                        break;
                    case 'business':
                        bannerHighlight.classList.add('business');
                        highlightTitle.textContent = 'Business';
                        highlightDescription.innerHTML = 'PC + Network <br> 24 hours respond time';
                        break;
                    case 'ultimate':
                        bannerHighlight.classList.add('ultimate');
                        highlightTitle.textContent = 'Ultimate';
                        highlightDescription.innerHTML = 'PC + Network <br> 4 hours respond time';
                        break;
                    default:
                        bannerHighlight.classList.add('starter');
                        highlightTitle.textContent = 'Starter';
                        highlightDescription.innerHTML = 'PC <br> 24 hours respond time';
                        break;
                }
            }

            // Initially show only the 'starter' category and set banner highlight
            document.querySelector('.first-tabs-title').click();


        });
        </script>





        <!-- ////////////////////////////////////////////////// -->
        <!-- Pricing section  -->






        <!-- Footer section -->
        <?php
        $is_localhost = (strpos($_SERVER['HTTP_HOST'], 'localhost') !== false || strpos($_SERVER['HTTP_HOST'], '127.0.0.1') !== false);
        $include_path = $is_localhost ? '/helloit' : '';
        include($_SERVER['DOCUMENT_ROOT'] . $include_path . '/header-footer/footer.php');
        ?>
    </div>

    <!-- Vendor Scripts -->
    <?php
    $asset_path = $is_localhost ? '/helloit' : '';
    ?>
    <script src="<?php echo $asset_path; ?>/js/vendor.min.js"></script>
    <!-- Plugin's Scripts -->
    <script src="<?php echo $asset_path; ?>/plugins/fancybox/jquery.fancybox.min.js"></script>
    <script src="<?php echo $asset_path; ?>/plugins/nice-select/jquery.nice-select.min.js"></script>
    <script src="<?php echo $asset_path; ?>/plugins/aos/aos.min.js"></script>
    <script src="<?php echo $asset_path; ?>/plugins/slick/slick.min.js"></script>
    <script src="<?php echo $asset_path; ?>/plugins/date-picker/js/gijgo.min.js"></script>
    <script src="<?php echo $asset_path; ?>/plugins/counter-up/jquery.waypoints.min.js"></script>
    <script src="<?php echo $asset_path; ?>/plugins/counter-up/jquery.counterup.min.js"></script>
    <script src="<?php echo $asset_path; ?>/plugins/theme-mode-switcher/gr-theme-mode-switcher.js"></script>
    <!-- Activation Script -->
    <script src="<?php echo $asset_path; ?>/js/custom.js"></script>
    <!--  -->
    <script>
    function handleBuyNow(event, packageId) {

        event.preventDefault(); // Prevent default action

        var packagesize = packageId; // This will be 'packageS' or 'packageXS'
        var dataValueActiveElement = document.getElementById('pricing-dynamic-deck');
        var tickettype = '';

        if (dataValueActiveElement.getAttribute('data-value-active') == 'monthly') {
            tickettype = 'STARTER';
        } else {
            tickettype = 'BUSINESS';
        }

        // Send request to server to create a Stripe Checkout session
        fetch('<?php echo $asset_path; ?>/functions/create-checkout-session.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    packagesize: packagesize,
                    tickettype: tickettype
                })
            })
            .then(response => response.json())
            .then(data => {
                // Redirect to Stripe Checkout session
                redirectToCheckout(data.sessionId);
            })
            .catch(error => {
                console.error('Error:', error);
            });

    }

    function redirectToCheckout(sessionId) {
        var stripe = Stripe(
            'pk_test_51ROUofEJRyUMDOj5NvEAEvLKglZbOVz4KWqBL10RJRKW5Jd81mZyxhTcGCfE8rCUqdXlpQC9mmBHWSatmdKlRuPc00gG5RvQXn'
        );
        stripe.redirectToCheckout({
            sessionId: sessionId
        }).then(function(result) {
            if (result.error) {
                console.error(result.error.message);
            }
        });
    }
    // Handle any errors returned from Checkout
    const handleResult = function(result) {
        if (result.error) {
            showMessage(result.error.message);
        }

        setLoading(false);
    };

    // Show a spinner on payment processing
    function setLoading(isLoading) {
        if (isLoading) {
            // Disable the button and show a spinner
            payBtn.disabled = true;
            document.querySelector("#spinner").classList.remove("hidden");
            document.querySelector("#buttonText").classList.add("hidden");
        } else {
            // Enable the button and hide spinner
            payBtn.disabled = false;
            document.querySelector("#spinner").classList.add("hidden");
            document.querySelector("#buttonText").classList.remove("hidden");
        }
    }

    // Display message
    function showMessage(messageText) {
        const messageContainer = document.querySelector("#paymentResponse");

        messageContainer.classList.remove("hidden");
        messageContainer.textContent = messageText;

        setTimeout(function() {
            messageContainer.classList.add("hidden");
            messageContainer.textContent = "";
        }, 5000);
    }
    </script>

    <!-- Smooth Scroll Function -->
    <script>
    function scrollToCompare() {
        const compareSection = document.getElementById('compare-plans');

        if (compareSection) {
            // Calculate offset for any fixed headers (adjust if needed)
            const headerOffset = 80; // Adjust this value based on your header height
            const elementPosition = compareSection.getBoundingClientRect().top;
            const offsetPosition = elementPosition + window.pageYOffset - headerOffset;

            window.scrollTo({
                top: offsetPosition,
                behavior: 'smooth'
            });
        } else {
            console.error('Compare section not found!');
        }
    }
    </script>

    <!-- Bootstrap and jQuery for modal functionality -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/popper.js@1.16.1/dist/umd/popper.min.js"></script>
    <script src="https://maxcdn.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
</body>

<?php
// Close database connection at the end
if (isset($conn)) {
    $conn->close();
}
?>

</html>