<?php
include('../functions/server.php');
include('../functions/maintenance-check.php');
include('../functions/timezone-helper.php');
session_start();

if (!isset($_SESSION['username'])) {
    header("Location: ../index.php");
    exit();
}

// Check if user panel is accessible (strict mode)
checkMaintenanceStatus('strict');

$username = $_SESSION['username'];

// Get user ID from username
$userQuery = "SELECT id FROM user WHERE username = '$username'";
$userResult = mysqli_query($conn, $userQuery);
$user = mysqli_fetch_assoc($userResult);
$userId = $user['id'] ?? 0;

// Pagination settings
$itemsPerPage = 5;
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
if ($page < 1) $page = 1;
$offset = ($page - 1) * $itemsPerPage;

// Count total ratings for pagination
$countQuery = "
    SELECT COUNT(*) as total
    FROM ticket_ratings tr
    JOIN support_tickets st ON tr.ticket_id = st.id
    WHERE tr.user_id = $userId
";
$countResult = mysqli_query($conn, $countQuery);
$totalItems = mysqli_fetch_assoc($countResult)['total'];
$totalPages = ceil($totalItems / $itemsPerPage);

// Get ratings by this user with pagination (local tickets)
$localRatingsQuery = "
    SELECT
        tr.*,
        st.id as ticket_id,
        st.subject,
        st.ticket_type,
        st.status,
        st.priority,
        st.created_at as ticket_created,
        st.updated_at as ticket_updated
    FROM ticket_ratings tr
    JOIN support_tickets st ON tr.ticket_id = st.id
    WHERE tr.user_id = $userId AND (tr.is_appika_ticket = 0 OR tr.is_appika_ticket IS NULL)
    ORDER BY tr.created_at DESC
    LIMIT $itemsPerPage OFFSET $offset
";
$localRatingsResult = mysqli_query($conn, $localRatingsQuery);

// Get Appika ratings for this user
$appikaRatingsQuery = "SELECT * FROM ticket_ratings WHERE user_id = $userId AND is_appika_ticket = 1 ORDER BY created_at DESC";
$appikaRatingsResult = mysqli_query($conn, $appikaRatingsQuery);
$appikaRatings = [];
require_once '../functions/graphql_functions.php';
while ($row = mysqli_fetch_assoc($appikaRatingsResult)) {
    // Fetch ticket info from Appika API
    $appikaId = $row['appika_ticket_id'];
    $query = '
    query GetTicket($id: Int!) {
        getTicket(id: $id) {
            id
            subject
            type
            status
            created
            updated
        }
    }';
    $variables = ['id' => (int)$appikaId];
    $result = makeGraphQLRequest($query, $variables);
    if ($result['success'] && isset($result['data']['data']['getTicket'])) {
        $ticket = $result['data']['data']['getTicket'];
        $row['ticket_id'] = $ticket['id'];
        $row['subject'] = $ticket['subject'];
        $row['ticket_type'] = $ticket['type'];
        $row['status'] = $ticket['status'];
        $row['ticket_created'] = $ticket['created'];
        $row['ticket_updated'] = $ticket['updated'] ?? $ticket['created'];
        $appikaRatings[] = $row;
    }
}

// Merge local and Appika ratings for display
$allRatings = [];
while ($row = mysqli_fetch_assoc($localRatingsResult)) {
    $allRatings[] = $row;
}
$allRatings = array_merge($allRatings, $appikaRatings);
// Optionally sort by created date descending
usort($allRatings, function($a, $b) {
    return strtotime($b['created_at']) - strtotime($a['created_at']);
});

// Map Appika type numbers to names for display
foreach ($allRatings as &$rating) {
    if (isset($rating['is_appika_ticket']) && $rating['is_appika_ticket']) {
        $typeMapping = [1 => 'Starter', 2 => 'Business', 3 => 'Ultimate'];
        $rating['ticket_type'] = $typeMapping[$rating['ticket_type']] ?? $rating['ticket_type'];
    }
}
unset($rating);
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <title>My Ticket Ratings - HelloIT Support</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link rel="shortcut icon" href="../image/png/favicon.png" type="image/x-icon">
    <!-- Bootstrap and plugins -->
    <link rel="stylesheet" href="../css/bootstrap.css">
    <link rel="stylesheet" href="../css/main.css">
    <link rel="stylesheet" href="../fonts/icon-font/css/style.css">
    <link rel="stylesheet" href="../fonts/typography-font/typo.css">
    <link rel="stylesheet" href="../fonts/fontawesome-5/css/all.css">
    <link rel="stylesheet" href="../plugins/aos/aos.min.css">
    <link rel="stylesheet" href="../plugins/fancybox/jquery.fancybox.min.css">
    <link rel="stylesheet" href="../plugins/nice-select/nice-select.min.css">
    <link rel="stylesheet" href="../plugins/slick/slick.min.css">
    <link rel="stylesheet" href="../plugins/date-picker/css/gijgo.min.css" type="text/css" />
    <link rel="stylesheet" href="../plugins/theme-mode-switcher/switcher-panel.css">
    <link rel="stylesheet" href="../css/theme-mode-custom.css">
    
    <style>

        body {
            padding-top: 140px;
            background-color: #F4F7FA;
        }

        @media (max-width: 767px) {
            body {
                margin-top: -20px;
            }
        }

        .ratings-container {
            background-color: #fff;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
        }

        .rating-card {
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            transition: box-shadow 0.3s ease;
        }

        .rating-card:hover {
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .star-display {
            color: #ffc107;
            font-size: 1.2rem;
        }

        .star-display .empty {
            color: #ddd;
        }

        .ticket-type-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: bold;
            text-transform: uppercase;
        }

        .type-starter {
            background-color: #fbbf24;
            color: #fff;
        }

        .type-premium {
            background-color: #01A7E1;
            color: #fff;
        }

        .type-ultimate {
            background-color: #793BF0;
            color: #fff;
        }

        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: bold;
            text-transform: uppercase;
        }

        .status-resolved {
            background-color: #28a745;
            color: white;
        }

        .status-closed {
            background-color: #6c757d;
            color: white;
        }

        .no-ratings {
            text-align: center;
            padding: 40px;
            color: #6c757d;
        }

        .no-ratings i {
            font-size: 4rem;
            margin-bottom: 20px;
            color: #ddd;
        }

        .no-ratings .btn {
            padding: 8px 16px;
            font-size: 14px;
            border-radius: 20px;
            display: inline-block;
            width: auto;
            white-space: nowrap;
            text-decoration: none;
            line-height: 1;
            font-weight: 400;
            border: none;
            background-color: #473BF0;
            color: white;
            transition: all 0.3s ease;
        }

        .no-ratings .btn:hover {
            background-color: #3d32d9;
            color: white;
            text-decoration: none;
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(71, 59, 240, 0.3);
        }

        .no-ratings .btn i {
            font-size: 13px;
            margin-right: 6px;
        }



        /* Menu open state for mobile */
        body.menu-open {
            overflow: hidden;
        }

        /* Container width to match other pages */
        .container {
            width: 1800px;
            max-width: 95%;
        }

        /* Make site-wrapper wider */
        .site-wrapper {
            max-width: 100% !important;
            width: 100% !important;
            overflow-x: visible !important;
        }

        /* Pagination styling */
        .pagination-container .pagination {
            margin-bottom: 0;
        }

        .pagination .page-link {
            color: #473BF0;
            border-color: #dee2e6;
        }

        .pagination .page-item.active .page-link {
            background-color: #473BF0;
            border-color: #473BF0;
            color: white;
        }

        .pagination .page-link:hover {
            color: #3d32d9;
            background-color: #e9ecef;
            border-color: #dee2e6;
        }

        .pagination .page-item.disabled .page-link {
            color: #6c757d;
            background-color: #fff;
            border-color: #dee2e6;
        }

        @media (max-width: 767px) {
            .ratings-container {
                padding: 20px 15px;
            }

            .rating-card {
                padding: 15px;
            }

            .pagination-container .pagination {
                flex-wrap: wrap;
                justify-content: center;
            }

            .pagination-container .page-item {
                margin-bottom: 5px;
            }

            .no-ratings .btn {
                padding: 6px 12px;
                font-size: 13px;
            }
        }
    </style>
</head>

<body data-theme="light">
    <?php include('../header-footer/header.php'); ?>

    <div class="container">
        <div class="row">
            <!-- Sidebar - Hidden on mobile, visible on larger screens -->
            <div class="col-lg-3 col-md-4 d-none d-md-block mb-4">
                <?php include('user-menu.php'); ?>
            </div>

            <!-- Mobile menu - Visible only on mobile -->
            <div class="col-12 d-md-none mb-4">
                <?php include('user-menu.php'); ?>
            </div>

            <!-- Main Content -->
            <div class="col-lg-9 col-md-8">
                <div class="ratings-container">
                    <h2 class="text-center mb-4">
                        My Ticket Ratings
                    </h2>

                    <?php if (count($allRatings) > 0): ?>
                        <!-- Rating Cards -->
                        <?php foreach ($allRatings as $rating): ?>
                        <div class="rating-card">
                            <div class="row">
                                <div class="col-md-8">
                                    <h5 class="mb-2">
                                        <a href="ticket-detail.php?<?php echo ($rating['is_appika_ticket'] ?? 0) ? 'ticket_id=' . $rating['ticket_id'] : 'id=' . $rating['ticket_id']; ?>" class="text-decoration-none">
                                            Ticket #<?php echo $rating['ticket_id']; ?>: <?php echo htmlspecialchars($rating['subject']); ?>
                                        </a>
                                    </h5>
                                    <div class="mb-2">
                                        <span class="ticket-type-badge type-<?php echo $rating['ticket_type']; ?>">
                                            <?php echo ucfirst($rating['ticket_type']); ?>
                                        </span>
                                        &nbsp;
                                        <span class="status-badge status-<?php echo $rating['status']; ?>">
                                            <?php echo ucfirst($rating['status']); ?>
                                        </span>
                                    </div>
                                    <div class="star-display mb-2">
                                        <?php for ($i = 1; $i <= 5; $i++): ?>
                                            <i class="fas fa-star <?php echo $i <= $rating['rating'] ? '' : 'empty'; ?>"></i>
                                        <?php endfor; ?>
                                        <span class="ml-2 text-muted">(<?php echo $rating['rating']; ?>/5)</span>
                                    </div>
                                    <small class="text-muted">
                                        <i class="fas fa-clock"></i>
                                        Rated on: <?php echo showCustomerTime($rating['created_at']); ?>
                                        <?php if (($rating['created_at'] ?? '') != ($rating['updated_at'] ?? '')): ?>
                                            (Updated: <?php echo showCustomerTime($rating['updated_at']); ?>)
                                        <?php endif; ?>
                                    </small>
                                </div>
                                <div class="col-md-4 text-md-right">
                                    <div class="mt-2">
                                        <a href="rate-ticket.php?<?php echo ($rating['is_appika_ticket'] ?? 0) ? 'appika_id=' . $rating['ticket_id'] : 'id=' . $rating['ticket_id']; ?>&from=my-ratings" class="btn btn-outline-primary btn-sm">
                                            <i class="fas fa-info-circle"></i> &nbsp; View Rating Info
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; ?>

                        <!-- Pagination Controls -->
                        <?php if ($totalPages > 1): ?>
                        <div class="pagination-container mt-4">
                            <nav aria-label="Page navigation">
                                <ul class="pagination justify-content-center">
                                    <!-- Previous Button -->
                                    <li class="page-item <?php echo ($page <= 1) ? 'disabled' : ''; ?>">
                                        <a class="page-link" href="?page=<?php echo $page - 1; ?>" aria-label="Previous">
                                            <span aria-hidden="true">&laquo; Previous</span>
                                        </a>
                                    </li>

                                    <!-- Page Numbers -->
                                    <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                                    <li class="page-item <?php echo ($page == $i) ? 'active' : ''; ?>">
                                        <a class="page-link" href="?page=<?php echo $i; ?>">
                                            <?php echo $i; ?>
                                        </a>
                                    </li>
                                    <?php endfor; ?>

                                    <!-- Next Button -->
                                    <li class="page-item <?php echo ($page >= $totalPages) ? 'disabled' : ''; ?>">
                                        <a class="page-link" href="?page=<?php echo $page + 1; ?>" aria-label="Next">
                                            <span aria-hidden="true">Next &raquo;</span>
                                        </a>
                                    </li>
                                </ul>
                            </nav>
                        </div>
                        <?php endif; ?>

                    <?php else: ?>
                        <!-- No Ratings -->
                        <div class="no-ratings">
                            <i class="fas fa-star-half-alt"></i>
                            <h4>No Ratings Yet</h4>
                            <p class="mb-4">You haven't rated any tickets yet. Once you have resolved or closed tickets,<br> you can rate your support experience.</p>
                        </div>
                    <?php endif; ?>


                </div>
            </div>
        </div>
    </div>

    <script src="../js/vendor.min.js"></script>
</body>

</html>
