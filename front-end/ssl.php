<?php
session_start();
include('../functions/server.php');
if (isset($_GET['logout'])) {
  session_destroy();
  unset($_SESSION['username']);
  header('location: ../index.php');
}
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>Supported Software List (SSL)</title>
    <link rel="shortcut icon" href="../image/png/favicon.png" type="image/x-icon">
    <!-- Bootstrap , fonts & icons  -->
    <link rel="stylesheet" href="../css/bootstrap.css">
    <link rel="stylesheet" href="../fonts/icon-font/css/style.css">
    <link rel="stylesheet" href="../fonts/typography-font/typo.css">
    <link rel="stylesheet" href="../fonts/fontawesome-5/css/all.css">
    <!-- Plugin'stylesheets  -->
    <link rel="stylesheet" href="../plugins/aos/aos.min.css">
    <link rel="stylesheet" href="../plugins/fancybox/jquery.fancybox.min.css">
    <link rel="stylesheet" href="../plugins/nice-select/nice-select.min.css">
    <link rel="stylesheet" href="../plugins/slick/slick.min.css">
    <link rel="stylesheet" href="../plugins/date-picker/css/gijgo.min.css" type="text/css" />
    <!-- Vendor stylesheets  -->
    <link rel="stylesheet" href="../plugins/theme-mode-switcher/switcher-panel.css">
    <link rel="stylesheet" href="../css/main.css">
    <link rel="stylesheet" href="../css/theme-mode-custom.css">
    <!-- Custom stylesheet -->
</head>

<body data-theme="light">
    <div class="site-wrapper overflow-hidden">
        <!-- Header Area -->
        <?php
    include('../header-footer/newnavtest.php');
    ?>
        <!-- navbar-dark -->
        <!-- Section 1 -->
        <style>
        .bg-default-2 {
            background: linear-gradient(to right, #473BF0, #762EE5);
            height: auto;
            min-height: 300px;
            padding: 30px 0;
        }

        @media (max-width: 991px) {
            .bg-default-2 {
                min-height: 250px;
                padding: 25px 0;
            }
        }

        @media (max-width: 767px) {
            .bg-default-2 {
                min-height: auto;
                padding: 20px 0;
            }
        }

        .row.justify-content-center.align-items-center {
            margin-top: -100px !important;
        }

        .custom-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            /* Vertically align columns */
            max-width: 1200px;
            /* Adjust as needed */
            margin-left: auto;
            margin-right: auto;
            padding-left: 20px;
            padding-right: 20px;
        }

        .custom-column {
            width: 50%;
            /* Two equal columns */
        }

        .custom-image-widget {
            text-align: center;
            margin-top: 10px;
        }

        .custom-image-widget img {
            max-width: 80%;
            height: auto;
            border-radius: 5px;
        }

        .custom-heading-widget h1 {
            color: #ffffff;
            font-size: 40px;
            font-weight: 700;
            line-height: 1.2;
            margin-bottom: 15px;
            font-family: "Circular Std", sans-serif;
            word-wrap: break-word;
            overflow-wrap: break-word;
            hyphens: none;
        }

        .custom-text-widget p {
            color: #ffffff;
            font-size: 20px;
            font-weight: 400;
            line-height: 30px;
            font-family: "Circular Std", sans-serif;
            margin-left: 5px;
        }

        /* Responsive styles */
        @media (max-width: 991px) {
            .custom-heading-widget h1 {
                font-size: 32px;
                line-height: 1.3;
                margin-bottom: 12px;
                text-align: center;
            }

            .custom-text-widget p {
                font-size: 18px;
                line-height: 26px;
            }
        }

        @media (max-width: 767px) {
            .custom-container {
                flex-direction: column;
            }

            .custom-column {
                width: 100%;
            }

            .custom-column.order-1 {
                margin-bottom: 20px;
            }

            .custom-column.order-2 {
                margin-bottom: 10px;
            }

            .row.justify-content-center.align-items-center {
                flex-direction: column;
            }

            .custom-heading-widget h1 {
                font-size: 26px;
                line-height: 1.4;
                text-align: center;
                margin-bottom: 12px;
                padding: 0 10px;
                overflow-wrap: break-word;
                hyphens: none;
            }

            .custom-text-widget p {
                font-size: 16px;
                line-height: 22px;
                text-align: center;
                margin-left: 0;
            }

            .custom-image-widget img {
                max-width: 70%;
                margin: 0 auto;
                display: block;
                border-radius: 20px;
            }

            @media (max-width: 480px) {
                .custom-image-widget img {
                    max-width: 65%;
                    height: auto;
                    margin: 0 auto;
                }
            }
        }

        /* Additional mobile breakpoints for h1 */
        @media (max-width: 576px) {
            .custom-heading-widget h1 {
                font-size: 22px;
                line-height: 1.4;
                padding: 0 15px;
                margin-bottom: 15px;
                hyphens: none;
                overflow-wrap: break-word;
            }
        }

        @media (max-width: 480px) {
            .custom-heading-widget h1 {
                font-size: 20px;
                line-height: 1.5;
                padding: 0 10px;
                margin-bottom: 12px;
                hyphens: none;
                overflow-wrap: break-word;
            }
        }

        @media (max-width: 375px) {
            .custom-heading-widget h1 {
                font-size: 18px;
                line-height: 1.5;
                padding: 0 8px;
                margin-bottom: 10px;
                hyphens: none;
                overflow-wrap: break-word;
            }
        }
        </style>


        <!-- SSL Section Styles -->
        <style>
        /* SSL Section Styles */
        .ssl-section {
            background: #f8fafc;
            border-radius: 25px;
            padding: 40px 30px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.08);
            border: 1px solid #e2e8f0;
            position: relative;
            overflow: hidden;
        }

        .ssl-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: #667eea;
        }

        .ssl-title {
            text-align: center;
            font-size: 32px;
            font-weight: 700;
            color: #2d3748;
            margin-bottom: 35px;
            position: relative;
        }

        .ssl-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 3px;
            background: #667eea;
            border-radius: 2px;
        }

        .ssl-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 25px;
            max-width: 1400px;
            margin: 0 auto;
        }

        .ssl-category {
            background: white;
            border-radius: 15px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.06);
            overflow: hidden;
            transition: all 0.3s ease;
            border: 1px solid #f1f5f9;
            position: relative;
            isolation: isolate;
        }

        .ssl-category:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 35px rgba(0, 0, 0, 0.1);
        }

        .ssl-header {
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px 25px;
            background: #667eea;
            transition: all 0.3s ease;
            position: relative;
            z-index: 1;
            user-select: none;
            border-radius: 15px 15px 0 0;
        }

        .ssl-header:hover {
            background: #5a67d8;
        }

        .ssl-category-title {
            color: white;
            font-size: 18px;
            font-weight: 600;
            margin: 0;
        }

        .ssl-content {
            background: white;
            padding: 20px 25px;
            border-radius: 0 0 15px 15px;
        }

        .ssl-item {
            display: flex;
            align-items: center;
            padding: 8px 0;
            color: #4a5568;
            font-size: 15px;
            transition: all 0.2s ease;
        }

        .ssl-item:hover {
            color: #667eea;
            transform: translateX(5px);
        }

        .ssl-item i {
            color: #48bb78;
            margin-right: 12px;
            font-size: 14px;
        }

        /* SSL Responsive Styles */
        @media (max-width: 991px) {
            .ssl-section {
                padding: 30px 20px;
            }

            .ssl-title {
                font-size: 28px;
                margin-bottom: 30px;
            }

            .ssl-grid {
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                gap: 20px;
            }

            .ssl-header {
                padding: 18px 20px;
            }

            .ssl-category-title {
                font-size: 16px;
            }

            .ssl-content {
                padding: 18px 20px;
            }
        }

        @media (max-width: 767px) {
            .ssl-section {
                padding: 25px 15px;
                border-radius: 20px;
            }

            .ssl-title {
                font-size: 24px;
                margin-bottom: 25px;
            }

            .ssl-grid {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .ssl-header {
                padding: 15px 18px;
            }

            .ssl-category-title {
                font-size: 15px;
            }

            .ssl-content {
                padding: 15px 18px;
            }

            .ssl-item {
                font-size: 14px;
                padding: 6px 0;
            }
        }

        @media (max-width: 480px) {
            .ssl-section {
                padding: 20px 12px;
            }

            .ssl-title {
                font-size: 22px;
                margin-bottom: 20px;
            }

            .ssl-header {
                padding: 12px 15px;
            }

            .ssl-category-title {
                font-size: 14px;
            }

            .ssl-content {
                padding: 12px 15px;
            }

            .ssl-item {
                font-size: 13px;
            }
        }
        </style>

        <!-- Supported Software List Section -->
        <div class="container-fluid px-4 pt-13 pt-lg-18 pb-13 pb-lg-18 bg-default-6">
            <div class="row justify-content-center mb-15" data-aos="fade-up" data-aos-duration="1100"
                data-aos-once="true">
                <div class="col-12">
                    <h3 class="ssl-title">Supported Software List (SSL)</h3>
                    <div class="ssl-grid">
                        <!-- Productivity Category -->
                        <div class="ssl-category">
                            <div class="ssl-header">
                                <h4 class="ssl-category-title">Productivity</h4>
                            </div>
                            <div class="ssl-content">
                                <div class="ssl-item"><i class="fas fa-check-circle"></i> ACDSee</div>
                                <div class="ssl-item"><i class="fas fa-check-circle"></i> Adobe Creative Cloud</div>
                                <div class="ssl-item"><i class="fas fa-check-circle"></i> Adobe Creative Suite</div>
                                <div class="ssl-item"><i class="fas fa-check-circle"></i> Autodesk</div>
                                <div class="ssl-item"><i class="fas fa-check-circle"></i> Evernote</div>
                                <div class="ssl-item"><i class="fas fa-check-circle"></i> Foxit PDF</div>
                                <div class="ssl-item"><i class="fas fa-check-circle"></i> Google Workspace</div>
                                <div class="ssl-item"><i class="fas fa-check-circle"></i> LibreOffice</div>
                                <div class="ssl-item"><i class="fas fa-check-circle"></i> Microsoft Office 365</div>
                                <div class="ssl-item"><i class="fas fa-check-circle"></i> Open Office</div>
                                <div class="ssl-item"><i class="fas fa-check-circle"></i> Picasa</div>
                            </div>
                        </div>

                        <!-- Web Browsers Category -->
                        <div class="ssl-category">
                            <div class="ssl-header">
                                <h4 class="ssl-category-title">Web Browsers</h4>
                            </div>
                            <div class="ssl-content">
                                <div class="ssl-item"><i class="fas fa-check-circle"></i> Chrome</div>
                                <div class="ssl-item"><i class="fas fa-check-circle"></i> Firefox</div>
                                <div class="ssl-item"><i class="fas fa-check-circle"></i> Microsoft Edge / Internet
                                    Explorer</div>
                                <div class="ssl-item"><i class="fas fa-check-circle"></i> Opera</div>
                                <div class="ssl-item"><i class="fas fa-check-circle"></i> Safari</div>
                            </div>
                        </div>

                        <!-- End-point Security Category -->
                        <div class="ssl-category">
                            <div class="ssl-header">
                                <h4 class="ssl-category-title">End-point Security</h4>
                            </div>
                            <div class="ssl-content">
                                <div class="ssl-item"><i class="fas fa-check-circle"></i> Avast</div>
                                <div class="ssl-item"><i class="fas fa-check-circle"></i> Bitdefender</div>
                                <div class="ssl-item"><i class="fas fa-check-circle"></i> ESET</div>
                                <div class="ssl-item"><i class="fas fa-check-circle"></i> Kaspersky</div>
                                <div class="ssl-item"><i class="fas fa-check-circle"></i> Malwarebyte</div>
                                <div class="ssl-item"><i class="fas fa-check-circle"></i> McAfee</div>
                                <div class="ssl-item"><i class="fas fa-check-circle"></i> Microsoft Essential</div>
                                <div class="ssl-item"><i class="fas fa-check-circle"></i> Norton</div>
                                <div class="ssl-item"><i class="fas fa-check-circle"></i> Trend Micro</div>
                            </div>
                        </div>

                        <!-- Framework / Plugin Category -->
                        <div class="ssl-category">
                            <div class="ssl-header">
                                <h4 class="ssl-category-title">Framework / Plugin</h4>
                            </div>
                            <div class="ssl-content">
                                <div class="ssl-item"><i class="fas fa-check-circle"></i> ActiveX</div>
                                <div class="ssl-item"><i class="fas fa-check-circle"></i> Adobe Plugin</div>
                                <div class="ssl-item"><i class="fas fa-check-circle"></i> Autodesk Plugin</div>
                                <div class="ssl-item"><i class="fas fa-check-circle"></i> Firefox Plugin</div>
                            </div>
                        </div>

                        <!-- Collaboration Category -->
                        <div class="ssl-category">
                            <div class="ssl-header">
                                <h4 class="ssl-category-title">Collaboration</h4>
                            </div>
                            <div class="ssl-content">
                                <div class="ssl-item"><i class="fas fa-check-circle"></i> Anydesk</div>
                                <div class="ssl-item"><i class="fas fa-check-circle"></i> Cisco WebX</div>
                                <div class="ssl-item"><i class="fas fa-check-circle"></i> Line</div>
                                <div class="ssl-item"><i class="fas fa-check-circle"></i> Sanook QQ</div>
                                <div class="ssl-item"><i class="fas fa-check-circle"></i> Skype</div>
                                <div class="ssl-item"><i class="fas fa-check-circle"></i> Teamviewer</div>
                                <div class="ssl-item"><i class="fas fa-check-circle"></i> VNC</div>
                                <div class="ssl-item"><i class="fas fa-check-circle"></i> Windows Live MSN</div>
                                <div class="ssl-item"><i class="fas fa-check-circle"></i> Yahoo Messenger</div>
                            </div>
                        </div>

                        <!-- Multimedia Category -->
                        <div class="ssl-category">
                            <div class="ssl-header">
                                <h4 class="ssl-category-title">Multimedia</h4>
                            </div>
                            <div class="ssl-content">
                                <div class="ssl-item"><i class="fas fa-check-circle"></i> AMP</div>
                                <div class="ssl-item"><i class="fas fa-check-circle"></i> Flash Player</div>
                                <div class="ssl-item"><i class="fas fa-check-circle"></i> KM Player</div>
                                <div class="ssl-item"><i class="fas fa-check-circle"></i> Nero</div>
                                <div class="ssl-item"><i class="fas fa-check-circle"></i> Quicktime</div>
                                <div class="ssl-item"><i class="fas fa-check-circle"></i> Real Player</div>
                                <div class="ssl-item"><i class="fas fa-check-circle"></i> VLC Media Player</div>
                                <div class="ssl-item"><i class="fas fa-check-circle"></i> Winamp</div>
                            </div>
                        </div>

                        <!-- Business Applications Category -->
                        <div class="ssl-category">
                            <div class="ssl-header">
                                <h4 class="ssl-category-title">Business Applications</h4>
                            </div>
                            <div class="ssl-content">
                                <div class="ssl-item"><i class="fas fa-check-circle"></i> CD Organizer</div>
                                <div class="ssl-item"><i class="fas fa-check-circle"></i> Express</div>
                                <div class="ssl-item"><i class="fas fa-check-circle"></i> RDNet</div>
                            </div>
                        </div>

                        <!-- Utilities Category -->
                        <div class="ssl-category">
                            <div class="ssl-header">
                                <h4 class="ssl-category-title">Utilities</h4>
                            </div>
                            <div class="ssl-content">
                                <div class="ssl-item"><i class="fas fa-check-circle"></i> 7 zip</div>
                                <div class="ssl-item"><i class="fas fa-check-circle"></i> Winrar</div>
                                <div class="ssl-item"><i class="fas fa-check-circle"></i> Winzip</div>
                                <div class="ssl-item"><i class="fas fa-check-circle"></i> FastCopy</div>
                                <div class="ssl-item"><i class="fas fa-check-circle"></i> TeraCopy</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Footer section -->
        <?php
    include('../header-footer/footer.php');
    ?>
        <!-- Vendor Scripts -->
        <script src="../js/vendor.min.js"></script>
        <!-- Plugin's Scripts -->
        <script src="../plugins/fancybox/jquery.fancybox.min.js"></script>
        <script src="../plugins/nice-select/jquery.nice-select.min.js"></script>
        <script src="../plugins/aos/aos.min.js"></script>
        <script src="../plugins/slick/slick.min.js"></script>
        <script src="../plugins/date-picker/js/gijgo.min.js"></script>
        <script src="../plugins/counter-up/jquery.waypoints.min.js"></script>
        <script src="../plugins/counter-up/jquery.counterup.min.js"></script>
        <script src="../plugins/theme-mode-switcher/gr-theme-mode-switcher.js"></script>
        <!-- Activation Script -->
        <script src="../js/custom.js"></script>
    </div>
</body>

</html>