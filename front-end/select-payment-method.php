<?php
session_start();
// Auto-detect environment for file paths
$is_localhost = (strpos($_SERVER['HTTP_HOST'], 'localhost') !== false || strpos($_SERVER['HTTP_HOST'], '127.0.0.1') !== false);
$base_path = $is_localhost ? '/helloit' : '';
require_once '../vendor/autoload.php';
include('../functions/server.php');

// Set your Stripe API keys
\Stripe\Stripe::setApiKey('sk_test_51ROUofEJRyUMDOj5Op3iqq0gpje1mQqwhINGCYVwVRvmOixzZh1IUmfwArcDt7FtJkN9aszopPP6dyxVWyOZeC8F00gwZtB0ju');

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('location: sign-in.php');
    exit();
}

$user_id = $_SESSION['user_id'];
$cart_id = isset($_GET['cart_id']) ? $_GET['cart_id'] : null;
$selected_payment_method = isset($_GET['payment_method']) ? $_GET['payment_method'] : null;
$save_payment_method = isset($_GET['save_payment_method']) ? $_GET['save_payment_method'] : '1'; // Default to save

// Validate cart_id
if (!$cart_id) {
    header('location: cart.php');
    exit();
}

// If a payment method was selected from cart page, process it directly
if ($selected_payment_method) {
    if ($selected_payment_method === 'new_card') {
        // Redirect to create checkout session with new card
        header("location: {$base_path}/functions/create-cart-checkout-session.php?cart_id=$cart_id&new_card=1&save_payment_method=$save_payment_method");
        exit();
    } else if (strpos($selected_payment_method, 'pm_') === 0) {
        // Use saved payment method (no need to save again)
        header("location: {$base_path}/functions/create-cart-checkout-session.php?cart_id=$cart_id&payment_method=$selected_payment_method");
        exit();
    }
}

// Get user's Stripe customer ID
$stripe_customer_id = '';
$user_query = $conn->prepare("SELECT stripe_customer_id FROM user WHERE id = ?");
$user_query->bind_param("i", $user_id);
$user_query->execute();
$user_query->bind_result($stripe_customer_id);
$user_query->fetch();
$user_query->close();

// Get saved payment methods
$payment_methods = null;
$payment_methods_data = []; // Array to store payment methods data
if ($stripe_customer_id) {
    try {
        $payment_methods = \Stripe\PaymentMethod::all([
            'customer' => $stripe_customer_id,
            'type' => 'card',
        ]);

        // Store payment methods data in an array for easier access
        if (isset($payment_methods->data)) {
            $payment_methods_data = $payment_methods->data;
        }
    } catch (Exception $e) {
        error_log("Error retrieving payment methods: " . $e->getMessage());
    }
}

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $payment_option = $_POST['payment_option'] ?? '';

    if ($payment_option === 'new_card') {
        // Get make_default value and save_payment_method
        $make_default = isset($_POST['make_default']) ? 1 : 0;
        $save_payment_method = isset($_POST['save_payment_method']) ? 1 : 0;

        // Redirect to create checkout session with new card
        header("location: {$base_path}/functions/create-cart-checkout-session.php?cart_id=$cart_id&new_card=1&make_default=$make_default&save_payment_method=$save_payment_method");
        exit();
    } else if (strpos($payment_option, 'pm_') === 0) {
        // Use saved payment method (no need to save again)
        header("location: {$base_path}/functions/create-cart-checkout-session.php?cart_id=$cart_id&payment_method=$payment_option");
        exit();
    }
}

?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>Select Payment - HelloIT</title>
    <link rel="shortcut icon" href="../image/png/favicon.png" type="image/x-icon">
    <!-- Bootstrap , fonts & icons  -->
    <link rel="stylesheet" href="../css/bootstrap.css">
    <link rel="stylesheet" href="../fonts/icon-font/css/style.css">
    <link rel="stylesheet" href="../fonts/typography-font/typo.css">
    <link rel="stylesheet" href="../fonts/fontawesome-5/css/all.css">
    <!-- Plugin'stylesheets  -->
    <link rel="stylesheet" href="../plugins/aos/aos.min.css">
    <link rel="stylesheet" href="../plugins/fancybox/jquery.fancybox.min.css">
    <link rel="stylesheet" href="../plugins/nice-select/nice-select.min.css">
    <link rel="stylesheet" href="../plugins/slick/slick.min.css">
    <link rel="stylesheet" href="../plugins/date-picker/css/gijgo.min.css" type="text/css" />
    <!-- Vendor stylesheets  -->
    <link rel="stylesheet" href="../plugins/theme-mode-switcher/switcher-panel.css">
    <link rel="stylesheet" href="../css/main.css">
    <link rel="stylesheet" href="../css/theme-mode-custom.css">
    <!-- Custom stylesheet -->
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
    body {
        background-color: #f8f9fa;
        font-family: 'Circular Std', sans-serif;
    }

    .navbar {
        background-color: #6B62F3;
        padding: 15px 0;
    }

    .alert-danger {
        background-color: #f8d7da;
        color: #721c24;
        border-color: #f5c6cb;
        padding: 12px;
        border-radius: 4px;
        margin-bottom: 20px;
    }

    .navbar-brand {
        color: white;
        font-weight: bold;
    }

    .back-button {
        color: white;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        font-weight: 500;
    }

    .back-button:hover {
        color: #f0f0f0;
    }

    .card {
        border: none;
        border-radius: 10px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .card-header {
        background-color: #473BF0;
        border-radius: 10px 10px 0 0 !important;
        padding: 15px 20px;
    }

    .btn-primary {
        background-color: #473BF0;
        border-color: #473BF0;
    }

    .btn-primary:hover {
        background-color: #3730c0;
        border-color: #3730c0;
    }

    .form-check-input:checked {
        background-color: #473BF0;
        border-color: #473BF0;
    }

    .form-check {
        transition: all 0.3s ease;
    }

    .form-check:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    .form-check-input:checked+.form-check-label {
        font-weight: 500;
    }

    .form-check-input:checked+.form-check-label .text-muted {
        color: #473BF0 !important;
    }

    .form-check.border {
        border-color: #e0e0e0 !important;
    }

    .form-check-input:checked~.form-check.border {
        border-color: #473BF0 !important;
    }

    .card-header.bg-primary.text-white {
        background-color: #6B62F3 !important;
    }
    </style>

</head>



<body>
    <nav class="navbar">
        <div class="container">
            <a href="cart.php" class="back-button">
                <i class="fas fa-arrow-left me-2"></i> Back to Cart
            </a>
            <span class="navbar-brand mb-0 h1">HelloIT</span>
        </div>
    </nav>

    <div class="container mt-5 mb-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0">Select Payment Method</h4>
                    </div>
                    <div class="card-body">
                        <?php if (isset($_GET['error'])): ?>
                        <div class="alert alert-danger" role="alert">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-exclamation-triangle me-3"></i>
                                <div>
                                    <strong>Error:</strong> <?php echo htmlspecialchars($_GET['error']); ?>
                                    <?php if (strpos($_GET['error'], 'maximum number of saved cards') !== false): ?>
                                    <div class="mt-2">
                                        <a href="../front-end/payment-methods.php" class="btn btn-sm btn-primary">
                                            <i class="fas fa-credit-card me-1"></i> Manage Payment Methods
                                        </a>
                                    </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                        <?php endif; ?>

                        <form method="post" action="">
                            <input type="hidden" name="cart_id" value="<?php echo htmlspecialchars($cart_id); ?>">
                            <input type="hidden" name="save_payment_method"
                                value="<?php echo htmlspecialchars($save_payment_method); ?>">

                            <?php if (count($payment_methods_data) > 0): ?>
                            <div class="mb-4">
                                <h5>Saved Payment Methods</h5>
                                <?php foreach ($payment_methods_data as $method): ?>
                                <?php
                                        $card = $method->card;
                                        $last4 = $card->last4;
                                        $brand = ucfirst($card->brand);
                                        $exp_month = $card->exp_month;
                                        $exp_year = $card->exp_year;
                                    ?>
                                <div class="form-check mb-3 p-3 border rounded">
                                    <input class="form-check-input" type="radio" name="payment_option"
                                        id="<?php echo $method->id; ?>" value="<?php echo $method->id; ?>">
                                    <label class="form-check-label d-flex justify-content-between"
                                        for="<?php echo $method->id; ?>">
                                        <div>
                                            <strong><?php echo $brand; ?> •••• <?php echo $last4; ?></strong>
                                            <div class="text-muted">Expires
                                                <?php echo $exp_month; ?>/<?php echo $exp_year; ?></div>
                                            <?php
                                            // Check if this is the default payment method
                                            $is_default = false;
                                            $check_default_query = "SELECT is_default FROM payment_methods WHERE payment_method_id = '$method->id' AND user_id = $user_id";
                                            $check_default_result = mysqli_query($conn, $check_default_query);
                                            if ($check_default_result && mysqli_num_rows($check_default_result) > 0) {
                                                $check_default_row = mysqli_fetch_assoc($check_default_result);
                                                $is_default = (bool)$check_default_row['is_default'];
                                            }
                                            if ($is_default): ?>
                                            <div class="mt-1"><span class="badge bg-success">Default</span></div>
                                            <?php endif; ?>
                                        </div>
                                        <div>
                                            <?php if ($brand == 'Visa'): ?>
                                            <img src="../image/card-logo/visa.webp" alt="Visa"
                                                style="height: 32px; width: auto;">
                                            <?php elseif ($brand == 'Mastercard'): ?>
                                            <img src="../image/card-logo/mastercard.svg" alt="Mastercard"
                                                style="height: 32px; width: auto;">
                                            <?php elseif ($brand == 'Amex' || $brand == 'American Express'): ?>
                                            <img src="../image/card-logo/AE.svg" alt="American Express"
                                                style="height: 32px; width: auto;">
                                            <?php elseif ($brand == 'Discover'): ?>
                                            <img src="../image/card-logo/discover.png" alt="Discover"
                                                style="height: 32px; width: auto;">
                                            <?php elseif ($brand == 'Diners Club'): ?>
                                            <img src="../image/card-logo/diners.svg" alt="Diners Club"
                                                style="height: 32px; width: auto;">
                                            <?php elseif ($brand == 'Jcb'): ?>
                                            <img src="../image/card-logo/jcb.svg" alt="JCB"
                                                style="height: 32px; width: auto;">
                                            <?php elseif ($brand == 'Maestro'): ?>
                                            <img src="../image/card-logo/Maestro.svg" alt="Maestro"
                                                style="height: 32px; width: auto;">
                                            <?php elseif ($brand == 'UnionPay'): ?>
                                            <img src="../image/card-logo/unionpay.svg" alt="UnionPay"
                                                style="height: 32px; width: auto;">
                                            <?php elseif ($brand == 'BCcard'): ?>
                                            <img src="../image/card-logo/bccard.png" alt="BCcard"
                                                style="height: 32px; width: auto;">
                                            <?php elseif ($brand == 'DinaCard'): ?>
                                            <img src="../image/card-logo/dinacard.svg" alt="DinaCard"
                                                style="height: 32px; width: auto;">
                                            <?php else: ?>
                                            <img src="../image/card-logo/generic-card.svg" alt="Credit Card"
                                                style="height: 32px; width: auto;">
                                            <?php endif; ?>
                                        </div>
                                    </label>
                                </div>
                                <?php endforeach; ?>
                            </div>
                            <div class="mb-4">
                                <div class="d-flex align-items-center">
                                    <hr class="flex-grow-1">
                                    <span class="mx-3">OR</span>
                                    <hr class="flex-grow-1">
                                </div>
                            </div>
                            <?php endif; ?>

                            <?php if (count($payment_methods_data) < 2): ?>
                            <!-- Show "Add a new card" option only if user has less than 2 cards -->
                            <div class="form-check mb-3 p-3 border rounded">
                                <input class="form-check-input" type="radio" name="payment_option" id="new_card"
                                    value="new_card" <?php echo count($payment_methods_data) === 0 ? 'checked' : ''; ?>>
                                <label class="form-check-label d-flex justify-content-between" for="new_card">
                                    <div>
                                        <strong>Add a new card</strong>
                                        <div class="text-muted">Enter your card details securely</div>
                                        <div class="form-check mt-2">
                                            <input class="form-check-input" type="checkbox" id="makeDefaultCard"
                                                name="make_default" value="1" checked>
                                            <label class="form-check-label small" for="makeDefaultCard">
                                                Make this my default payment method
                                            </label>
                                        </div>
                                    </div>
                                    <div>
                                        <i class="fas fa-plus-circle fa-2x text-success"></i>
                                    </div>
                                </label>


                            </div>
                            <?php else: ?>
                            <!-- Show notification if user already has 2 or more cards -->
                            <div class="alert alert-warning mb-3" role="alert">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-exclamation-triangle me-3 fa-2x"></i>
                                    <div>
                                        <h5 class="mb-1">Maximum Cards Reached</h5>
                                        <p class="mb-2">You already have 2 payment methods saved. To add a new card,
                                            please remove an existing one first.</p>
                                        <a href="../front-end/payment-methods.php" class="btn btn-sm btn-primary">
                                            <i class="fas fa-credit-card me-1"></i> Manage Payment Methods
                                        </a>
                                    </div>
                                </div>
                            </div>
                            <?php endif; ?>

                            <div class="d-grid gap-2 mt-4">
                                <button type="button" id="continueBtn" class="btn btn-primary">Continue to
                                    Payment</button>
                                <a href="cart.php" class="btn btn-outline-secondary">Back to Cart</a>
                            </div>
                            <br>
                            <div class="d-flex justify-content-center align-items-center flex-wrap gap-3 mb-3">
                                <img src="../image/card-logo/secure-stripe-payment-logo.png" alt="Stripe Secure"
                                    height="50">
                            </div>
                            <!-- Trust Badges Section -->
                            <!-- <div class="mt-4 mb-4 p-3 border rounded bg-light">
                                <div class="text-center mb-3">

                                </div>
                            </div> -->



                            <!-- Confirmation Modal -->
                            <div class="modal fade" id="paymentConfirmModal" tabindex="-1"
                                aria-labelledby="paymentConfirmModalLabel" aria-hidden="true">
                                <div class="modal-dialog modal-dialog-centered">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <h5 class="modal-title" id="paymentConfirmModalLabel">Confirm Payment</h5>

                                        </div>
                                        <div class="modal-body">
                                            <div id="newCardConfirm" style="display: none;">
                                                <p>You are about to add a new payment method.</p>
                                                <p>You will be redirected to Stripe's secure payment page to enter your
                                                    card details.</p>
                                                <div class="alert alert-success mt-3">
                                                    <div class="d-flex">
                                                        <div class="me-3">
                                                            <i class="fas fa-shield-alt fa-2x text-success"></i>
                                                        </div>
                                                        <div>
                                                            <h6 class="mb-1">Stripe Secure Payment</h6>
                                                            <p class="small mb-0">Your payment will be processed on
                                                                Stripe's secure servers. Your card details are encrypted
                                                                with industry-standard SSL technology and never stored
                                                                on our servers.</p>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="d-flex justify-content-center mt-3">

                                                </div>
                                            </div>
                                            <div id="savedCardConfirm" style="display: none;">
                                                <p>You are about to make a payment with your saved card:</p>
                                                <div class="saved-card-details p-3 my-3 border rounded bg-light">
                                                    <div class="d-flex align-items-center">
                                                        <div id="cardIcon" class="me-3"></div>
                                                        <div>
                                                            <div id="cardBrand" class="fw-bold"></div>
                                                            <div id="cardLast4" class="text-muted"></div>
                                                            <div id="cardExpiry" class="text-muted"></div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="alert alert-info mt-3">
                                                    <div class="d-flex">
                                                        <div class="me-3">
                                                            <i class="fas fa-lock fa-2x text-primary"></i>
                                                        </div>
                                                        <div>
                                                            <h6 class="mb-1">Secure Transaction</h6>
                                                            <p class="small mb-0">Your payment is secure and your card
                                                                details are protected. We use Stripe's secure payment
                                                                processing to ensure your information is safe.</p>
                                                        </div>
                                                    </div>
                                                </div>
                                                <p>Your card will be charged immediately.</p>
                                                <div class="d-flex justify-content-center mt-3">
                                                    <!-- <img src="../image/card-logo/secure-stripe-payment-logo.png"
                                                        alt="Stripe Secure" height="50"> -->
                                                </div>
                                            </div>
                                        </div>
                                        <div class="modal-footer">
                                            <button type="button" class="btn btn-secondary"
                                                data-bs-dismiss="modal">Cancel</button>
                                            <button type="submit" class="btn btn-primary" id="confirmPaymentBtn">Confirm
                                                Payment</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Payment Method Required Modal -->
    <div class="modal fade" id="paymentRequiredModal" tabindex="-1" aria-labelledby="paymentRequiredModalLabel"
        aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header" style="background-color: #f8d7da; color: #721c24;">
                    <h5 class="modal-title" id="paymentRequiredModalLabel">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        Payment Method Required
                    </h5>
                </div>
                <div class="modal-body">
                    <div class="d-flex align-items-center mb-3">
                        <div class="me-3 text-danger">
                            <i class="fas fa-credit-card fa-2x"></i>
                        </div>
                        <div>
                            <p class="mb-0">Please select a payment method to continue with your purchase.</p>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary" data-bs-dismiss="modal">OK</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
    document.addEventListener('DOMContentLoaded', function() {
        const radioButtons = document.querySelectorAll('input[name="payment_option"]');
        const continueBtn = document.getElementById('continueBtn');
        const confirmPaymentBtn = document.getElementById('confirmPaymentBtn');
        const paymentForm = document.querySelector('form');
        const paymentConfirmModal = new bootstrap.Modal(document.getElementById('paymentConfirmModal'));
        const paymentRequiredModal = new bootstrap.Modal(document.getElementById('paymentRequiredModal'));

        // Highlight selected payment method
        radioButtons.forEach(function(radio) {
            radio.addEventListener('change', function() {
                // Remove highlight from all payment options
                document.querySelectorAll('.form-check.border').forEach(function(el) {
                    el.style.borderColor = '#e0e0e0';
                    el.style.backgroundColor = '';
                });

                // Add highlight to selected option
                if (this.checked) {
                    this.closest('.form-check').style.borderColor = '#473BF0';
                    this.closest('.form-check').style.backgroundColor = '#f8f9ff';
                }
            });

            // Initialize with the checked option highlighted
            if (radio.checked) {
                radio.closest('.form-check').style.borderColor = '#473BF0';
                radio.closest('.form-check').style.backgroundColor = '#f8f9ff';
            }
        });

        // Handle continue button click
        continueBtn.addEventListener('click', function() {
            // Get selected payment option
            const selectedOption = document.querySelector('input[name="payment_option"]:checked');

            if (!selectedOption) {
                // Show the payment required modal instead of basic alert
                paymentRequiredModal.show();
                return;
            }

            const paymentOption = selectedOption.value;
            const newCardConfirm = document.getElementById('newCardConfirm');
            const savedCardConfirm = document.getElementById('savedCardConfirm');

            // Show appropriate confirmation message
            if (paymentOption === 'new_card') {
                newCardConfirm.style.display = 'block';
                savedCardConfirm.style.display = 'none';
            } else {
                // It's a saved card
                newCardConfirm.style.display = 'none';
                savedCardConfirm.style.display = 'block';

                // Get card details from the selected option
                const cardLabel = selectedOption.nextElementSibling;
                const cardBrand = cardLabel.querySelector('strong').textContent.split(' ')[0];
                const cardLast4 = cardLabel.querySelector('strong').textContent.split('••••')[1].trim();
                const cardExpiry = cardLabel.querySelector('.text-muted').textContent.replace('Expires',
                    '').trim();

                // Update the confirmation modal with card details
                document.getElementById('cardBrand').textContent = cardBrand;
                document.getElementById('cardLast4').textContent = '•••• ' + cardLast4;
                document.getElementById('cardExpiry').textContent = 'Expires ' + cardExpiry;

                // Set card icon
                const cardIcon = document.getElementById('cardIcon');
                cardIcon.innerHTML = '';

                if (cardBrand.toLowerCase() === 'visa') {
                    cardIcon.innerHTML =
                        '<img src="../image/card-logo/visa.webp" alt="Visa" style="height: 32px; width: auto;">';
                } else if (cardBrand.toLowerCase() === 'mastercard') {
                    cardIcon.innerHTML =
                        '<img src="../image/card-logo/mastercard.svg" alt="Mastercard" style="height: 32px; width: auto;">';
                } else if (cardBrand.toLowerCase() === 'amex' || cardBrand.toLowerCase() ===
                    'american express') {
                    cardIcon.innerHTML =
                        '<img src="../image/card-logo/AE.svg" alt="American Express" style="height: 32px; width: auto;">';
                } else if (cardBrand.toLowerCase() === 'discover') {
                    cardIcon.innerHTML =
                        '<img src="../image/card-logo/discover.png" alt="Discover" style="height: 32px; width: auto;">';
                } else if (cardBrand.toLowerCase() === 'diners club') {
                    cardIcon.innerHTML =
                        '<img src="../image/card-logo/diners.svg" alt="Diners Club" style="height: 32px; width: auto;">';
                } else if (cardBrand.toLowerCase() === 'jcb') {
                    cardIcon.innerHTML =
                        '<img src="../image/card-logo/jcb.svg" alt="JCB" style="height: 32px; width: auto;">';
                } else if (cardBrand.toLowerCase() === 'maestro') {
                    cardIcon.innerHTML =
                        '<img src="../image/card-logo/Maestro.svg" alt="Maestro" style="height: 32px; width: auto;">';
                } else if (cardBrand.toLowerCase() === 'unionpay') {
                    cardIcon.innerHTML =
                        '<img src="../image/card-logo/unionpay.svg" alt="UnionPay" style="height: 32px; width: auto;">';
                } else if (cardBrand.toLowerCase() === 'bccard') {
                    cardIcon.innerHTML =
                        '<img src="../image/card-logo/bccard.png" alt="BCcard" style="height: 32px; width: auto;">';
                } else if (cardBrand.toLowerCase() === 'dinacard') {
                    cardIcon.innerHTML =
                        '<img src="../image/card-logo/dinacard.svg" alt="DinaCard" style="height: 32px; width: auto;">';
                } else {
                    cardIcon.innerHTML =
                        '<img src="../image/card-logo/generic-card.svg" alt="Credit Card" style="height: 32px; width: auto;">';
                }
            }

            // Show the confirmation modal
            paymentConfirmModal.show();
        });

        // Handle confirm payment button click
        confirmPaymentBtn.addEventListener('click', function() {
            // Submit the form
            paymentForm.submit();
        });
    });
    </script>
</body>

</html>