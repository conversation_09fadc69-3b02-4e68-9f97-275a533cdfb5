<?php
// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Debug: Log that this file is being accessed
error_log("=== CREATE PRE-CHECKOUT PAYMENT STARTED ===");
error_log("Request method: " . $_SERVER['REQUEST_METHOD']);
error_log("Request URI: " . $_SERVER['REQUEST_URI']);
error_log("GET parameters: " . json_encode($_GET));
error_log("POST parameters: " . json_encode($_POST));

// Auto-detect environment for file paths
$is_localhost = (strpos($_SERVER['HTTP_HOST'], 'localhost') !== false || strpos($_SERVER['HTTP_HOST'], '127.0.0.1') !== false);
$file_base_path = $is_localhost ? '/helloit' : '';

try {
    require_once $_SERVER['DOCUMENT_ROOT'] . $file_base_path . '/vendor/autoload.php';
    include($_SERVER['DOCUMENT_ROOT'] . $file_base_path . '/functions/server.php');
    session_start();
} catch (Exception $e) {
    die("Error loading files: " . $e->getMessage());
}

// Set your Stripe API keys
\Stripe\Stripe::setApiKey('sk_test_51ROUofEJRyUMDOj5Op3iqq0gpje1mQqwhINGCYVwVRvmOixzZh1IUmfwArcDt7FtJkN9aszopPP6dyxVWyOZeC8F00gwZtB0ju');

// Check if user is logged in
error_log("Session check - user_id: " . ($_SESSION['user_id'] ?? 'NOT SET'));
error_log("Session check - username: " . ($_SESSION['username'] ?? 'NOT SET'));
error_log("Session data: " . json_encode($_SESSION));

if (!isset($_SESSION['user_id'])) {
    error_log("User not logged in, redirecting to sign-in.php");
    header('location: ' . $file_base_path . '/front-end/sign-in.php');
    exit();
}

error_log("User is logged in, proceeding with payment processing");

$user_id = $_SESSION['user_id'];
$payment_method = isset($_GET['payment_method']) ? $_GET['payment_method'] : null;

// Validate payment method
if (!$payment_method || strpos($payment_method, 'pm_') !== 0) {
    header('location: ' . $file_base_path . '/front-end/pre-checkout.php?error=' . urlencode('Invalid payment method'));
    exit();
}

// Get cart items (same logic as pre-checkout.php)
$cart_items = [];

// Fetch cart items from database for logged-in user (same query as pre-checkout.php)
$query = "SELECT ci.ticket_id, ci.quantity, t.ticket_type, t.dollar_price_per_package, t.package_size, t.numbers_per_package
          FROM cart_items ci
          JOIN tickets t ON ci.ticket_id = t.ticketid
          WHERE ci.cart_id IN (SELECT cart_id FROM cart WHERE user_id = ? AND status = 'active')";
$stmt = $conn->prepare($query);
$stmt->bind_param("i", $user_id);
$stmt->execute();
$result = $stmt->get_result();
$cart_items = $result->fetch_all(MYSQLI_ASSOC);

// Debug logging
error_log("Pre-checkout payment - User ID: " . $user_id);
error_log("Pre-checkout payment - Cart items found: " . count($cart_items));
error_log("Pre-checkout payment - Cart items: " . json_encode($cart_items));

if (empty($cart_items)) {
    error_log("Pre-checkout payment - No cart items found, redirecting to pre-checkout with error");
    header('location: ' . $file_base_path . '/front-end/pre-checkout.php?error=' . urlencode('Your cart is empty. Please add items to your cart first.'));
    exit();
}

// Prepare line items and metadata
$line_items = [];
$cart_metadata = [];

foreach ($cart_items as $item) {
    // Create descriptive product name that includes package size
    $product_name = $item['ticket_type'];
    if (!empty($item['package_size'])) {
        $product_name .= ' ' . $item['package_size'];
    }

    $product_data = [
        'name' => $product_name
    ];
    if (!empty($item['package_size'])) {
        $tickets_text = ($item['numbers_per_package'] ?? 1) == 1 ? 'ticket' : 'tickets';
        $product_data['description'] = ($item['numbers_per_package'] ?? 1) . ' ' . $tickets_text;
    }
    
    $line_items[] = [
        'price_data' => [
            'currency' => 'usd',
            'product_data' => $product_data,
            'unit_amount' => $item['dollar_price_per_package'] * 100,
        ],
        'quantity' => $item['quantity'],
    ];

    // Store cart item data for webhook processing
    $cart_metadata[] = [
        'ticket_type' => $item['ticket_type'],
        'package_size' => $item['package_size'] ?? '',
        'numbers_per_package' => $item['numbers_per_package'] ?? 1,
        'dollar_price_per_package' => $item['dollar_price_per_package'],
        'quantity' => $item['quantity']
    ];
}

// Store cart data in database to avoid Stripe 500 character limit
$cart_session_id = 'cart_' . uniqid() . '_' . time();
$cart_data_json = json_encode($cart_metadata);

// Insert cart data into database
$insert_cart_query = "INSERT INTO cart_sessions (session_id, cart_data, user_id) VALUES (?, ?, ?)";
$cart_stmt = $conn->prepare($insert_cart_query);
$cart_stmt->bind_param("ssi", $cart_session_id, $cart_data_json, $user_id);
$cart_stmt->execute();

// Get cart_id for metadata (needed by cart-payment-success.php)
$cart_id = '';
$cart_query = "SELECT cart_id FROM cart WHERE user_id = ? AND status = 'active' LIMIT 1";
$cart_stmt = $conn->prepare($cart_query);
$cart_stmt->bind_param("i", $user_id);
$cart_stmt->execute();
$cart_result = $cart_stmt->get_result();
if ($cart_row = $cart_result->fetch_assoc()) {
    $cart_id = $cart_row['cart_id'];
}

// Create minimal metadata (same format as old system)
$metadata = [
    'user_id' => $user_id,
    'username' => isset($_SESSION['username']) ? $_SESSION['username'] : '',
    'total_items' => count($cart_metadata),
    'cart_id' => $cart_id, // Required by cart-payment-success.php
    'cart_session_id' => $cart_session_id,
    'total_amount' => array_sum(array_column($cart_metadata, 'dollar_price_per_package')),
    'save_payment_method' => '0', // Already saved
    'source' => 'pre_checkout'
];

// Get user email and stripe_customer_id from database
$user_email = '';
$stripe_customer_id = '';
$user_query = $conn->prepare("SELECT email, stripe_customer_id FROM user WHERE id = ?");
$user_query->bind_param("i", $user_id);
$user_query->execute();
$user_query->bind_result($user_email, $stripe_customer_id);
$user_query->fetch();
$user_query->close();

if (!$stripe_customer_id) {
    header('location: ' . $file_base_path . '/front-end/pre-checkout.php?error=' . urlencode('No Stripe customer ID found'));
    exit();
}

try {
    // Calculate total amount
    $amount = 0;
    foreach ($line_items as $item) {
        $amount += $item['price_data']['unit_amount'] * $item['quantity'];
    }

    // Create a PaymentIntent with the saved payment method
    $payment_intent = \Stripe\PaymentIntent::create([
        'amount' => $amount,
        'currency' => 'usd',
        'customer' => $stripe_customer_id,
        'payment_method' => $payment_method,
        'off_session' => false, // This is an on-session payment
        'confirm' => true, // Confirm the payment immediately
        'metadata' => $metadata,
        'return_url' => (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://$_SERVER[HTTP_HOST]{$file_base_path}/functions/cart-payment-success.php?payment_intent_id={PAYMENT_INTENT_ID}",
        'confirmation_method' => 'automatic',
    ]);

    // Check if payment requires additional action
    if ($payment_intent->status === 'requires_action' && $payment_intent->next_action->type === 'redirect_to_url') {
        // Redirect to the URL provided by Stripe for 3D Secure authentication
        header('location: ' . $payment_intent->next_action->redirect_to_url->url);
        exit();
    } else if ($payment_intent->status === 'succeeded') {
        // Payment succeeded, redirect to cart payment success page (same as old system)
        header('location: ' . $file_base_path . '/functions/cart-payment-success.php?payment_intent_id=' . $payment_intent->id);
        exit();
    } else {
        // Payment failed or requires further action
        header('location: ' . $file_base_path . '/front-end/pre-checkout.php?error=' . urlencode('Payment failed or requires further action. Status: ' . $payment_intent->status));
        exit();
    }
} catch (Exception $e) {
    header('location: ' . $file_base_path . '/front-end/pre-checkout.php?error=' . urlencode('Payment error: ' . $e->getMessage()));
    exit();
}
?>
