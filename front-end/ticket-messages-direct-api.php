<?php
session_start();
include('../functions/server.php');
include('../functions/graphql_functions.php');

// Set JSON header
header('Content-Type: application/json');

// Check if user is logged in
if (!isset($_SESSION['username'])) {
    echo json_encode(['success' => false, 'error' => 'Not logged in']);
    exit();
}

$username = $_SESSION['username'];
$user_id = $_SESSION['user_id'];

// Get action
$action = $_GET['action'] ?? $_POST['action'] ?? '';
$ticket_id = intval($_GET['ticket_id'] ?? $_POST['ticket_id'] ?? 0);

if ($ticket_id <= 0) {
    echo json_encode(['success' => false, 'error' => 'Invalid ticket ID']);
    exit();
}

// Verify user has access to this ticket
$user_query = "SELECT email FROM user WHERE username = ?";
$stmt = mysqli_prepare($conn, $user_query);
mysqli_stmt_bind_param($stmt, 's', $username);
mysqli_stmt_execute($stmt);
$user_result = mysqli_stmt_get_result($stmt);
$user_data = mysqli_fetch_assoc($user_result);
mysqli_stmt_close($stmt);

if (!$user_data) {
    echo json_encode(['success' => false, 'error' => 'User not found']);
    exit();
}

$user_email = $user_data['email'];

switch ($action) {
    case 'get_messages':
        // Get messages from Appika API (same as working ticket-chat-api.php)
        $query = '
        query GetTicketWithMessages($id: Int!) {
            getTicket(id: $id) {
                id
                req_email
                status
                ticketMsg {
                    message
                    creator_by_contact
                    creator_by_agent
                }
            }
        }';

        $variables = ['id' => $ticket_id];
        $result = makeGraphQLRequest($query, $variables);

        if (!$result['success']) {
            echo json_encode(['success' => false, 'error' => $result['error'] ?? 'API error']);
            exit();
        }

        $ticketData = $result['data']['data']['getTicket'] ?? null;

        if (!$ticketData) {
            echo json_encode(['success' => false, 'error' => 'Ticket not found']);
            exit();
        }

        // Verify user access
        if ($ticketData['req_email'] !== $user_email) {
            echo json_encode(['success' => false, 'error' => 'Access denied']);
            exit();
        }

        // Process messages (same logic as working ticket-chat-api.php)
        $messages = [];
        if (isset($ticketData['ticketMsg']) && is_array($ticketData['ticketMsg'])) {
            foreach ($ticketData['ticketMsg'] as $index => $msg) {
                $messageText = $msg['message'] ?? '';

                // Skip empty messages
                if (empty(trim($messageText))) {
                    continue;
                }

                // Determine sender based on Appika's creator fields
                $creatorByContact = $msg['creator_by_contact'] ?? null;
                $creatorByAgent = $msg['creator_by_agent'] ?? null;

                if (!empty($creatorByContact)) {
                    // Message sent by contact/user
                    $senderType = 'user';
                    $senderName = 'You';
                } elseif (!empty($creatorByAgent)) {
                    // Message sent by agent/support
                    $senderType = 'admin';
                    $senderName = 'Support';
                } else {
                    // First message is usually description, others default to user
                    $senderType = $index === 0 ? 'system' : 'user';
                    $senderName = $index === 0 ? 'System' : 'You';
                }

                $messages[] = [
                    'id' => $index,
                    'message' => $messageText,
                    'sender_type' => $senderType,
                    'sender_name' => $senderName,
                    'is_description' => $index === 0,
                    'creator_by_contact' => $creatorByContact,
                    'creator_by_agent' => $creatorByAgent
                ];
            }
        }

        echo json_encode(['success' => true, 'messages' => $messages]);
        break;
        
    case 'send_message':
        $message = isset($_POST['message']) ? trim($_POST['message']) : '';

        if (empty($message)) {
            echo json_encode(['success' => false, 'error' => 'Message cannot be empty']);
            exit();
        }

        // First, get the current ticket data to preserve existing values
        $verifyQuery = '
        query GetTicket($id: Int!) {
            getTicket(id: $id) {
                id
                req_email
                subject
                type
                type_name
                priority
                status
                contact_id
                agent_id
            }
        }';

        $verifyResult = makeGraphQLRequest($verifyQuery, ['id' => $ticket_id]);

        if (!$verifyResult['success']) {
            echo json_encode(['success' => false, 'error' => 'Failed to verify ticket: ' . ($verifyResult['error'] ?? 'Unknown error')]);
            exit();
        }

        $currentTicket = $verifyResult['data']['data']['getTicket'] ?? null;

        if (!$currentTicket) {
            echo json_encode(['success' => false, 'error' => 'Ticket not found in API response']);
            exit();
        }

        // Verify user access
        if ($currentTicket['req_email'] !== $user_email) {
            echo json_encode(['success' => false, 'error' => 'Access denied - email mismatch']);
            exit();
        }

        // Check if ticket is closed
        if (strtoupper($currentTicket['status'] ?? '') === 'CLOSED') {
            echo json_encode(['success' => false, 'error' => 'Cannot send messages to closed tickets']);
            exit();
        }

        // DIRECT GraphQL mutation (as requested - no $updateData)
        $mutation = '
        mutation updateTicket(
          $id: Int!,
          $subject: String!,
          $type: Int!,
          $priority: String!,
          $status: String!,
          $time_track: String!,
          $reply_msg: String!,
          $reply_type: String!,
          $tags: String
        ) {
          updateTicket(
            id: $id,
            subject: $subject,
            type: $type,
            priority: $priority,
            status: $status,
            time_track: $time_track,
            reply_msg: $reply_msg,
            reply_type: $reply_type,
            tags: $tags
          ) {
            id
            ticket_no
            subject
            status
            updated
          }
        }';

        // Prepare variables with preserved values + new message
        $variables = [
            'id' => $ticket_id,
            'subject' => $currentTicket['subject'],
            'type' => (int)$currentTicket['type'],
            'priority' => strtoupper($currentTicket['priority']),
            'status' => strtoupper($currentTicket['status']),
            'time_track' => '00:00:00',
            'reply_msg' => $message,
            'reply_type' => 'user',
            'tags' => ''
        ];

        // Log what we're sending for debugging
        error_log("DIRECT MUTATION - Sending message for ticket $ticket_id: " . json_encode($variables));

        $updateResult = makeGraphQLRequest($mutation, $variables);

        // Log the response for debugging
        error_log("DIRECT MUTATION - Update result for ticket $ticket_id: " . json_encode($updateResult));

        if ($updateResult['success'] && isset($updateResult['data']['data']['updateTicket'])) {
            echo json_encode([
                'success' => true,
                'message' => [
                    'id' => time(), // Use timestamp as ID
                    'message' => $message,
                    'sender_type' => 'user',
                    'sender_name' => 'You'
                ]
            ]);
        } else {
            // Check if it's a status 200 response (sometimes API returns errors but still processes)
            if ($updateResult['status'] == 200 && isset($updateResult['data']['data']['updateTicket'])) {
                echo json_encode([
                    'success' => true,
                    'message' => [
                        'id' => time(),
                        'message' => $message,
                        'sender_type' => 'user',
                        'sender_name' => 'You'
                    ]
                ]);
            } else {
                echo json_encode([
                    'success' => false,
                    'error' => 'Failed to send message: ' . ($updateResult['error'] ?? 'Unknown error')
                ]);
            }
        }
        break;

    default:
        echo json_encode(['success' => false, 'error' => 'Invalid action']);
        break;
}
?>
