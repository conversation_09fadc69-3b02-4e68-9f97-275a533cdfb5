<?php
session_start();
include('../functions/server.php');
include('../functions/graphql_functions.php');

// Set JSON header
header('Content-Type: application/json');

// Check if user is logged in
if (!isset($_SESSION['username'])) {
    echo json_encode(['success' => false, 'error' => 'Not logged in']);
    exit();
}

$username = $_SESSION['username'];
$user_id = $_SESSION['user_id'];

// Get action
$action = $_GET['action'] ?? $_POST['action'] ?? '';
$ticket_id = intval($_GET['ticket_id'] ?? $_POST['ticket_id'] ?? 0);

if ($ticket_id <= 0) {
    echo json_encode(['success' => false, 'error' => 'Invalid ticket ID']);
    exit();
}

// Verify user has access to this ticket via Appika API
$user_query = "SELECT email FROM user WHERE username = ?";
$stmt = mysqli_prepare($conn, $user_query);
mysqli_stmt_bind_param($stmt, 's', $username);
mysqli_stmt_execute($stmt);
$user_result = mysqli_stmt_get_result($stmt);
$user_data = mysqli_fetch_assoc($user_result);
mysqli_stmt_close($stmt);

if (!$user_data) {
    echo json_encode(['success' => false, 'error' => 'User not found']);
    exit();
}

$user_email = $user_data['email'];

// Verify ticket access via Appika API
$verifyQuery = '
query GetTicket($id: Int!) {
    getTicket(id: $id) {
        id
        req_email
        status
        subject
        type
        priority
    }
}';

$verifyResult = makeGraphQLRequest($verifyQuery, ['id' => $ticket_id]);

if (!$verifyResult['success']) {
    echo json_encode(['success' => false, 'error' => 'Failed to verify ticket access']);
    exit();
}

$ticketData = $verifyResult['data']['data']['getTicket'] ?? null;

if (!$ticketData || $ticketData['req_email'] !== $user_email) {
    echo json_encode(['success' => false, 'error' => 'Access denied']);
    exit();
}

switch ($action) {
    case 'get_messages':
        // Get messages from chat_messages table (like HelloITOld)
        $messages_query = "SELECT cm.*,
                          CASE
                              WHEN cm.sender_type = 'admin' THEN a.username
                              WHEN cm.sender_type = 'user' THEN u.username
                          END as sender_name
                          FROM chat_messages cm
                          LEFT JOIN admin_users a ON cm.sender_id = a.id AND cm.sender_type = 'admin'
                          LEFT JOIN user u ON cm.sender_id = u.id AND cm.sender_type = 'user'
                          WHERE cm.ticket_id = ?
                          ORDER BY cm.created_at ASC";
        $stmt = mysqli_prepare($conn, $messages_query);
        mysqli_stmt_bind_param($stmt, 'i', $ticket_id);
        mysqli_stmt_execute($stmt);
        $messages_result = mysqli_stmt_get_result($stmt);
        
        $messages = [];
        while ($message = mysqli_fetch_assoc($messages_result)) {
            $messages[] = [
                'id' => $message['id'],
                'message' => $message['message'],
                'message_type' => $message['sender_type'], // 'user' or 'admin'
                'sender_name' => $message['sender_name'] ?? 'Unknown',
                'created_at' => $message['created_at'],
                'is_read' => $message['is_read']
            ];
        }
        mysqli_stmt_close($stmt);

        echo json_encode(['success' => true, 'messages' => $messages]);
        break;
        
    case 'send_message':
        $message = isset($_POST['message']) ? trim($_POST['message']) : '';

        if (empty($message)) {
            echo json_encode(['success' => false, 'error' => 'Message cannot be empty']);
            exit();
        }

        // Check if ticket is closed
        if (strtoupper($ticketData['status'] ?? '') === 'CLOSED') {
            echo json_encode(['success' => false, 'error' => 'Cannot send messages to closed tickets']);
            exit();
        }

        // Insert user message into chat_messages table (like HelloITOld)
        $insert_query = "INSERT INTO chat_messages (ticket_id, sender_id, sender_type, message)
                        VALUES (?, ?, 'user', ?)";

        $stmt = mysqli_prepare($conn, $insert_query);
        mysqli_stmt_bind_param($stmt, 'iis',
            $ticket_id,
            $user_id,
            $message
        );
        
        if (mysqli_stmt_execute($stmt)) {
            $message_id = mysqli_insert_id($conn);
            mysqli_stmt_close($stmt);
            
            // Try to sync to Appika API in background (optional)
            syncMessageToAppika($ticket_id, $message, $ticketData);
            
            echo json_encode([
                'success' => true,
                'message' => [
                    'id' => $message_id,
                    'message' => $message,
                    'message_type' => 'user',
                    'sender_name' => $username,
                    'created_at' => date('Y-m-d H:i:s')
                ]
            ]);
        } else {
            mysqli_stmt_close($stmt);
            echo json_encode(['success' => false, 'error' => 'Failed to save message']);
        }
        break;

    default:
        echo json_encode(['success' => false, 'error' => 'Invalid action']);
        break;
}

// Function to sync message to Appika API (background operation)
function syncMessageToAppika($ticket_id, $message, $ticketData) {
    try {
        // Direct GraphQL mutation to Appika
        $mutation = '
        mutation updateTicket(
          $id: Int!,
          $subject: String!,
          $type: Int!,
          $priority: String!,
          $status: String!,
          $time_track: String!,
          $reply_msg: String!,
          $reply_type: String!,
          $tags: String
        ) {
          updateTicket(
            id: $id,
            subject: $subject,
            type: $type,
            priority: $priority,
            status: $status,
            time_track: $time_track,
            reply_msg: $reply_msg,
            reply_type: $reply_type,
            tags: $tags
          ) {
            id
            updated
          }
        }';

        $variables = [
            'id' => $ticket_id,
            'subject' => $ticketData['subject'],
            'type' => (int)$ticketData['type'],
            'priority' => strtoupper($ticketData['priority']),
            'status' => strtoupper($ticketData['status']),
            'time_track' => '00:00:00',
            'reply_msg' => $message,
            'reply_type' => 'user',
            'tags' => ''
        ];

        $result = makeGraphQLRequest($mutation, $variables);
        
        // Update sync status in database
        global $conn;
        if ($result['success']) {
            $update_sync = "UPDATE ticket_messages 
                           SET appika_synced = 1, appika_sync_at = NOW() 
                           WHERE appika_ticket_id = ? AND message = ? AND appika_synced = 0 
                           ORDER BY created_at DESC LIMIT 1";
            $stmt = mysqli_prepare($conn, $update_sync);
            mysqli_stmt_bind_param($stmt, 'is', $ticket_id, $message);
            mysqli_stmt_execute($stmt);
            mysqli_stmt_close($stmt);
            
            error_log("Message synced to Appika successfully for ticket $ticket_id");
        } else {
            error_log("Failed to sync message to Appika for ticket $ticket_id: " . ($result['error'] ?? 'Unknown error'));
        }
    } catch (Exception $e) {
        error_log("Exception syncing message to Appika: " . $e->getMessage());
    }
}
?>
