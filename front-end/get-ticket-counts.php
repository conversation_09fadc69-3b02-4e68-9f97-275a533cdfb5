<?php
session_start();
include_once('../functions/server.php');
include_once('../functions/ticket-expiration-functions.php');
header('Content-Type: application/json');

$user_id = isset($_SESSION['user_id']) ? intval($_SESSION['user_id']) : 0;
$username = isset($_SESSION['username']) ? $_SESSION['username'] : '';

if ($user_id > 0 && $username) {
    // First, sync user tickets to handle any expired tickets
    // TEMPORARILY DISABLED FOR DEBUGGING - This was overwriting admin-added tickets every 5 seconds
    // syncUserTableTickets($username);
    
    // Get updated user data
    $userQuery = "SELECT starter_tickets, premium_tickets, ultimate_tickets FROM user WHERE username = ?";
    $stmt = mysqli_prepare($conn, $userQuery);
    mysqli_stmt_bind_param($stmt, 's', $username);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    $user = mysqli_fetch_assoc($result);
    mysqli_stmt_close($stmt);
    
    // Count tickets expiring soon
    $expiring_tickets = getTicketsExpiringSoon($username);
    $expiring_tickets_count = count($expiring_tickets);
    
    echo json_encode([
        'success' => true,
        'starter_tickets' => (int)($user['starter_tickets'] ?? 0),
        'premium_tickets' => (int)($user['premium_tickets'] ?? 0),
        'ultimate_tickets' => (int)($user['ultimate_tickets'] ?? 0),
        'expiring_tickets_count' => $expiring_tickets_count
    ]);
} else {
    echo json_encode([
        'success' => false,
        'error' => 'User not authenticated'
    ]);
}
?>
