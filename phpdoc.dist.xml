<?xml version="1.0" encoding="UTF-8" ?>
<phpdocumentor
        configVersion="3"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xmlns="http://www.phpdoc.org"
        xsi:noNamespaceSchemaLocation="data/xsd/phpdoc.xsd"
>
    <paths>
        <output>build/phpdoc</output>
    </paths>
    <version number="3.0.0">
        <folder>latest</folder>
        <api>
            <source dsn=".">
                <path>lib</path>
            </source>
            <output>api</output>
            <ignore hidden="true" symlinks="true">
                <path>build/**/*</path>
                <path>examples/**/*</path>
                <path>tests/**/*</path>
                <path>vendor/**/*</path>
            </ignore>
            <extensions>
                <extension>php</extension>
            </extensions>
            <default-package-name>stripe-php</default-package-name>
        </api>
    </version>
    <template name="default"/>
</phpdocumentor>
