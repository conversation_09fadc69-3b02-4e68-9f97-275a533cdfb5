<?php
/**
 * Server Environment Test
 * Tests basic PHP functionality and autoloader on the server
 */

echo "<h1>Server Environment Test</h1>";
echo "<p>Testing basic PHP functionality and autoloader...</p>";

// Test 1: Basic PHP Info
echo "<h2>1. PHP Environment</h2>";
echo "PHP Version: " . PHP_VERSION . "<br>";
echo "Server Software: " . ($_SERVER['SERVER_SOFTWARE'] ?? 'Unknown') . "<br>";
echo "Document Root: " . ($_SERVER['DOCUMENT_ROOT'] ?? 'Unknown') . "<br>";
echo "Current Directory: " . getcwd() . "<br>";
echo "Script Filename: " . __FILE__ . "<br>";

// Test 2: File Existence Check
echo "<h2>2. File Existence Check</h2>";
$files_to_check = [
    'vendor/autoload.php',
    'vendor/composer/autoload_real.php',
    'vendor/guzzlehttp/guzzle/src/Client.php',
    'composer.json'
];

foreach ($files_to_check as $file) {
    if (file_exists($file)) {
        echo "✅ $file exists<br>";
    } else {
        echo "❌ $file missing<br>";
    }
}

// Test 3: Try to include autoloader
echo "<h2>3. Autoloader Test</h2>";
try {
    require_once 'vendor/autoload.php';
    echo "✅ Autoloader included successfully<br>";
} catch (Exception $e) {
    echo "❌ Error including autoloader: " . $e->getMessage() . "<br>";
} catch (Error $e) {
    echo "❌ Fatal error including autoloader: " . $e->getMessage() . "<br>";
}

// Test 4: Check if Guzzle class exists after autoloader
echo "<h2>4. Guzzle Class Test</h2>";
if (class_exists('GuzzleHttp\Client')) {
    echo "✅ GuzzleHttp\Client class exists<br>";
    
    // Test creating a client
    try {
        $client = new GuzzleHttp\Client(['timeout' => 10]);
        echo "✅ GuzzleHttp\Client can be instantiated<br>";
    } catch (Exception $e) {
        echo "❌ Error creating Guzzle client: " . $e->getMessage() . "<br>";
    }
} else {
    echo "❌ GuzzleHttp\Client class does not exist<br>";
}

// Test 5: Check loaded classes
echo "<h2>5. Loaded Classes Check</h2>";
$loaded_classes = get_declared_classes();
$guzzle_classes = array_filter($loaded_classes, function($class) {
    return strpos($class, 'GuzzleHttp') !== false;
});

if (!empty($guzzle_classes)) {
    echo "✅ Guzzle classes loaded: " . count($guzzle_classes) . "<br>";
    echo "First few: " . implode(', ', array_slice($guzzle_classes, 0, 3)) . "<br>";
} else {
    echo "❌ No Guzzle classes found in loaded classes<br>";
}

// Test 6: Check include path
echo "<h2>6. Include Path Check</h2>";
echo "Include Path: " . get_include_path() . "<br>";

// Test 7: Check if composer.json is valid
echo "<h2>7. Composer Configuration Check</h2>";
if (file_exists('composer.json')) {
    $composer_content = file_get_contents('composer.json');
    $composer_data = json_decode($composer_content, true);
    
    if ($composer_data) {
        echo "✅ composer.json is valid JSON<br>";
        if (isset($composer_data['require']['guzzlehttp/guzzle'])) {
            echo "✅ Guzzle is listed in requirements: " . $composer_data['require']['guzzlehttp/guzzle'] . "<br>";
        } else {
            echo "❌ Guzzle not found in composer.json requirements<br>";
        }
    } else {
        echo "❌ composer.json is not valid JSON<br>";
    }
} else {
    echo "❌ composer.json not found<br>";
}

// Test 8: Check vendor directory permissions
echo "<h2>8. Vendor Directory Permissions</h2>";
if (is_dir('vendor')) {
    $perms = fileperms('vendor');
    $perms_octal = substr(sprintf('%o', $perms), -4);
    echo "✅ vendor directory exists (permissions: $perms_octal)<br>";
    
    if (is_readable('vendor')) {
        echo "✅ vendor directory is readable<br>";
    } else {
        echo "❌ vendor directory is not readable<br>";
    }
} else {
    echo "❌ vendor directory does not exist<br>";
}

// Test 9: Try manual class loading
echo "<h2>9. Manual Class Loading Test</h2>";
$guzzle_client_file = 'vendor/guzzlehttp/guzzle/src/Client.php';
if (file_exists($guzzle_client_file)) {
    echo "✅ Guzzle Client.php file exists<br>";
    
    // Check if we can read the file
    $content = file_get_contents($guzzle_client_file, false, null, 0, 100);
    if ($content !== false) {
        echo "✅ Can read Guzzle Client.php file<br>";
        echo "First 100 chars: " . htmlspecialchars(substr($content, 0, 100)) . "...<br>";
    } else {
        echo "❌ Cannot read Guzzle Client.php file<br>";
    }
} else {
    echo "❌ Guzzle Client.php file does not exist<br>";
}

echo "<h2>Summary</h2>";
echo "<p>This test checks if the server environment can properly load Guzzle. Look for any ❌ marks above to identify issues.</p>";
?>
