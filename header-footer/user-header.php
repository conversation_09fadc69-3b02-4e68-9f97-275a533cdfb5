<!DOCTYPE html>
<html lang="en">

<head>

</head>

<style>
  .brand-logo img {
    width: 120px;
    height: 85px;
    object-fit: cover;
    /* Ensures the image fits within the dimensions */
    margin-left: -40px;
  }

  /* Hamburger Menu Styles */
  .hamburger-menu {
    cursor: pointer;
    z-index: 1001;
    margin-left: auto;
    margin-right: 15px;
  }

  .hamburger-icon {
    width: 30px;
    height: 20px;
    position: relative;
  }

  .hamburger-icon span {
    display: block;
    position: absolute;
    height: 3px;
    width: 100%;
    background: white;
    border-radius: 3px;
    opacity: 1;
    left: 0;
    transform: rotate(0deg);
    transition: .25s ease-in-out;
  }

  .hamburger-icon span:nth-child(1) {
    top: 0px;
  }

  .hamburger-icon span:nth-child(2) {
    top: 8px;
  }

  .hamburger-icon span:nth-child(3) {
    top: 16px;
  }

  .hamburger-menu.active .hamburger-icon span:nth-child(1) {
    top: 8px;
    transform: rotate(135deg);
  }

  .hamburger-menu.active .hamburger-icon span:nth-child(2) {
    opacity: 0;
    left: -60px;
  }

  .hamburger-menu.active .hamburger-icon span:nth-child(3) {
    top: 8px;
    transform: rotate(-135deg);
  }

  /* Menu overlay */
  .menu-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 999;
  }

  .menu-overlay.active {
    display: block;
  }

  body.menu-open {
    overflow: hidden;
  }

  /* Responsive styles */
  @media (max-width: 991px) {
    .navbar-collapse {
      position: fixed;
      top: 0;
      left: -300px;
      width: 300px;
      height: 100vh;
      background-color: #473BF0;
      z-index: 1000;
      transition: left 0.3s ease;
      padding: 20px;
      overflow-y: auto;
    }

    .navbar-collapse.show {
      left: 0;
    }

    .navbar-nav-wrapper {
      width: 100%;
    }

    .navbar-nav {
      flex-direction: column !important;
      width: 100%;
    }

    .nav-item {
      width: 100%;
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }

    .nav-link {
      padding: 15px 0 !important;
      display: block;
    }

    .dropdown-menu {
      position: static !important;
      width: 100%;
      background-color: rgba(255, 255, 255, 0.1) !important;
      border: none !important;
      border-radius: 0 !important;
    }

    .brand-logo img {
      width: 100px;
      height: 70px;
    }
  }

  @media (max-width: 576px) {
    .brand-logo img {
      width: 80px;
      height: 60px;
      margin-left: -20px;
    }

    .site-header {
      padding: 10px 0 !important;
    }
  }
</style>

<body data-theme="light">
  <div class="site-wrapper overflow-hidden">
    <!-- Header Area -->
    <header class="site-header site-header--menu-left site-header--absolute" style="background-color: #473BF0; padding-bottom: 15px; padding-top: 10px;">
      <div class="container" style="max-width: 1100px;">
        <nav class="navbar site-navbar offcanvas-active navbar-expand-lg  px-0">
          <!-- Brand Logo-->
          <div class="brand-logo"><a href="index.php">
              <!-- light version logo (logo must be black)-->
              <img src="../image/wp/HelloIT-new.png" alt="" class="light-version-logo ">
              <!-- Dark version logo (logo must be White)-->
              <img src="../image/HelloIT-new.png" alt="" class="dark-version-logo">
            </a></div>
          <style>
            .header-btn .btn {
              width: 105px !important;
              height: 51px !important;
              padding: 0 !important;
              display: flex;
              align-items: center;
              justify-content: center;
              font-size: 14px;
              font-weight: 500;
              background-color: #ffffff;
              color: #000000;
              border-radius: 10px;
            }

            .header-btn .btn:hover {
              background-color: #F64B4B;
              color: #ffffff;
            }
          </style>


          <!-- Hamburger Menu Button (visible only on mobile) -->
          <div class="hamburger-menu d-lg-none">
            <div class="hamburger-icon">
              <span></span>
              <span></span>
              <span></span>
            </div>
          </div>

          <div class="collapse navbar-collapse" id="mobile-menu">
            <div class="navbar-nav-wrapper">
              <ul class="navbar-nav main-menu" style="display: flex; justify-content: center; list-style: none; padding: 0; margin: 0;">

              </ul>
            </div>

            <!--
            <button class="d-block d-lg-none offcanvas-btn-close" type="button" data-toggle="collapse" data-target="#mobile-menu" aria-controls="mobile-menu" aria-expanded="true" aria-label="Toggle navigation">
              <i class="gr-cross-icon">55555</i>
            </button>
            -->
          </div>
          <!-- <div class="header-btn ml-auto ml-lg-5 mr-6 mr-lg-0 d-none d-xs-block">
            <a class="btn btn-primary gr-text-9" href="../front-end/buy-now.php">
              Buy Now
            </a>
          </div> -->

          <!-- loged in user information-->
          <?php if (isset($_SESSION['username'])) : ?>
            <!-- <div class="header-btn  ml-auto ml-lg-5 mr-6 mr-lg-0 d-none d-xs-block">
              <a class="btn btn-primary gr-text-9" href="../front-end/open-tickets.php">
                Open Ticket
              </a>
            </div> -->
            <div class="header-btn  ml-auto ml-lg-5 mr-6 mr-lg-0 d-none d-xs-block">
              <div class="dropdown " style="display: flex; align-items: center; margin-left: 20px; ">
                <a class="dropdown-toggle" href="#" role="button" id="dropdownMenuLink" data-bs-toggle="dropdown" aria-expanded="false" style="display: flex; align-items: center; color: white;">
                  <i class="fa fa-user-circle" style="font-size: 20px; margin-right: 8px;"></i>
                  <i><?php echo $_SESSION['username']; ?></i>
                </a>
                <ul class="dropdown-menu">
                  <li><a class="dropdown-item" href="../front-end/open-tickets.php">Open Ticket</a></li>
                  <li><a class="dropdown-item" href="../front-end/profile.php">Profile</a></li>
                  <li><a class="dropdown-item" href="../front-end/user-panel.php">My panel</a></li>
                  <li><a class="dropdown-item" href="../front-end/purchase-history.php">Purchase History</a></li>
                  <li><a class="dropdown-item" href="<?php
                    $is_localhost = (strpos($_SERVER['HTTP_HOST'], 'localhost') !== false || strpos($_SERVER['HTTP_HOST'], '127.0.0.1') !== false);
                    $logout_url = $is_localhost ? '/helloit/index?logout=1' : '/index?logout=1';
                    echo $logout_url;
                  ?>">Logout</a></li>
                </ul>
              </div>
            <?php else : ?>
              <div class="header-btn ml-auto ml-lg-5 mr-6 mr-lg-0 d-none d-xs-block">
                <a class="btn btn-primary gr-text-9" href="../front-end/sign-in.php">
                  Sign In
                </a>
              </div>
            <?php endif; ?>
            </div>
            <!-- Mobile Menu Hamburger-->
            <!-- <button class="navbar-toggler btn-close-off-canvas  hamburger-icon border-0" type="button" data-toggle="collapse" data-target="#mobile-menu" aria-controls="mobile-menu" aria-expanded="false" aria-label="Toggle navigation">
            <i class="icon icon-simple-remove icon-close"></i> -->
            <!-- <span class="hamburger hamburger--squeeze js-hamburger">
              <span class="hamburger-box">
                <span class="hamburger-inner"></span>
              </span>
            </span>
            </button> -->
            <!--/.Mobile Menu Hamburger Ends-->
        </nav>
      </div>
    </header>

    <!-- Overlay for mobile menu -->
    <div class="menu-overlay"></div>

    <!-- navbar-dark -->

    <script>
      document.addEventListener('DOMContentLoaded', function() {
        const hamburgerMenu = document.querySelector('.hamburger-menu');
        const mobileMenu = document.getElementById('mobile-menu');
        const menuOverlay = document.querySelector('.menu-overlay');
        const body = document.body;

        // Toggle mobile menu
        hamburgerMenu.addEventListener('click', function() {
          hamburgerMenu.classList.toggle('active');
          mobileMenu.classList.toggle('show');
          menuOverlay.classList.toggle('active');
          body.classList.toggle('menu-open');
        });

        // Close menu when clicking on overlay
        menuOverlay.addEventListener('click', function() {
          hamburgerMenu.classList.remove('active');
          mobileMenu.classList.remove('show');
          menuOverlay.classList.remove('active');
          body.classList.remove('menu-open');
        });

        // Close menu when clicking on a link (for mobile)
        const menuLinks = document.querySelectorAll('.nav-link');
        menuLinks.forEach(link => {
          link.addEventListener('click', function() {
            if (window.innerWidth <= 991) {
              hamburgerMenu.classList.remove('active');
              mobileMenu.classList.remove('show');
              menuOverlay.classList.remove('active');
              body.classList.remove('menu-open');
            }
          });
        });
      });
    </script>

    <!--
    <script src="../js/vendor.min.js"></script>

    <script src="../plugins/fancybox/jquery.fancybox.min.js"></script>
    <script src="../plugins/nice-select/jquery.nice-select.min.js"></script>
    <script src="../plugins/aos/aos.min.js"></script>
    <script src="../plugins/slick/slick.min.js"></script>
    <script src="../plugins/date-picker/js/gijgo.min.js"></script>
    <script src="../plugins/counter-up/jquery.waypoints.min.js"></script>
    <script src="../plugins/counter-up/jquery.counterup.min.js"></script>
    <script src="../plugins/theme-mode-switcher/gr-theme-mode-switcher.js"></script>

    <script src="../js/custom.js"></script>
    -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

</body>

</html>