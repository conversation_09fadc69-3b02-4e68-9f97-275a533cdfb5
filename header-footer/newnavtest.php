<!-- Localization APi: ip-api.com -->
<?php
// Include tracking codes
include('tracking-codes.php');
include('tracking-body-start.php');
include('tracking-noscript.php');

//ip setting here
$use_manual_ip = false; // true = use manual ip, false = use user ip
$manual_ip     = '*******'; //insert manual ip here

// default
$country = '';
$city = '';

// check if page is index or sign-up this function will work
$current_page = basename($_SERVER['PHP_SELF']);
if ($current_page === 'index.php' || $current_page === 'sign-up.php') {

    // check what ip type should use
    if ($use_manual_ip) {
        $ip = $manual_ip;
    } else {
        //real user IP
        $ip = $_SERVER['REMOTE_ADDR'];

        // If localhost, use ipify for showing real ip
        if ($ip === '127.0.0.1' || $ip === '::1') {
            $ip = @file_get_contents("https://api.ipify.org");
        }
    }

    //get localtion from ip-api
    if ($ip) {
        $response = @file_get_contents("http://ip-api.com/json/{$ip}");
        if ($response) {
            $data = json_decode($response, true);
            if ($data && isset($data['country'])) {
                $country = $data['country'];
                $city    = $data['city'] ?? '';
            }
        }
    }
}

// Get user email if logged in
$user_email = '';
if (isset($_SESSION['username'])) {
    // Include database connection if not already included
    if (!isset($conn)) {
        include_once(dirname(__FILE__) . '/../functions/server.php');
    }

    $username = $_SESSION['username'];
    $stmt = $conn->prepare("SELECT email FROM user WHERE username = ? LIMIT 1");
    $stmt->bind_param("s", $username);
    $stmt->execute();
    $stmt->bind_result($user_email);
    $stmt->fetch();
    $stmt->close();
}
?>



<header class="top-navbar-container">
    <nav class="top-navbar">
        <div class="logo">
            <a href="<?php
                $is_localhost = (strpos($_SERVER['HTTP_HOST'], 'localhost') !== false || strpos($_SERVER['HTTP_HOST'], '127.0.0.1') !== false);
                if ($is_localhost) {
                    echo (basename($_SERVER['PHP_SELF']) === 'index.php') ? '/helloit/' : '/helloit/';
                } else {
                    echo '/';
                }
            ?>" class="logo" aria-label="HelloIT - Go to homepage" title="HelloIT - Go to homepage">
                <picture>
                    <source srcset="<?php echo (basename($_SERVER['PHP_SELF']) === 'index.php') ? 'image/wp/HelloIT-new.webp' : '../image/wp/HelloIT-new.webp'; ?>" type="image/webp">
                    <img src="<?php echo (basename($_SERVER['PHP_SELF']) === 'index.php') ? 'image/wp/HelloIT-new.png' : '../image/wp/HelloIT-new.png'; ?>"
                        alt="HelloIT Professional IT Support Services Logo" style="height: 50px;">
                </picture>
            </a>
        </div>
        <div class="nav-center">
            <ul class="nav-links">
                <li><a href="<?php
                            $is_localhost = (strpos($_SERVER['HTTP_HOST'], 'localhost') !== false || strpos($_SERVER['HTTP_HOST'], '127.0.0.1') !== false);
                            if ($is_localhost) {
                                echo '/helloit/';
                            } else {
                                echo '/';
                            }
                        ?>">Home</a>
                </li>
                <!-- Issues We Solve Dropdown (Desktop) -->
                <li class="dropdown desktop-dropdown">
                    <a class="dropdown-toggle" href="#" role="button" id="issuesDropdown" data-bs-toggle="dropdown"
                        aria-expanded="false">Issues We Solve</a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item"
                                href="<?php echo (basename($_SERVER['PHP_SELF']) === 'index.php') ? 'support-ticket/issues-we-solve' : '../support-ticket/issues-we-solve'; ?>">Issues
                                We Solve</a></li>
                        <li><a class="dropdown-item"
                                href="<?php echo (basename($_SERVER['PHP_SELF']) === 'index.php') ? 'support-ticket/ssl' : '../support-ticket/ssl'; ?>">Supported
                                Software List (SSL)</a></li>
                    </ul>
                </li>
                <!-- Mobile Dropdown for Issues We Solve -->
                <li class="dropdown mobile-dropdown" style="display:none; position:relative;">
                    <button class="dropdown-toggle-parent" aria-label="Toggle Issues We Solve submenu"
                        style="width:100%; background:none; border:none; color:white; font-size:24px; padding:15px 0; display:flex; align-items:center; justify-content:center; position:relative;">
                        <span style="text-align:center;">Issues We Solve</span>
                        <span class="dropdown-arrow"
                            style="display:inline-block; transition:transform 0.3s; font-size:22px; margin-left:8px;">&#9660;</span>
                    </button>
                    <ul class="dropdown-menu" style="margin-top:0; padding-left:0;">
                        <li><a class="dropdown-item" style="padding-left:0; text-align:center;"
                                href="<?php echo (basename($_SERVER['PHP_SELF']) === 'index.php') ? 'support-ticket/issues-we-solve' : '../support-ticket/issues-we-solve'; ?>">Issues
                                We Solve</a></li>
                        <li><a class="dropdown-item" style="padding-left:0; text-align:center;"
                                href="<?php echo (basename($_SERVER['PHP_SELF']) === 'index.php') ? 'support-ticket/ssl' : '../support-ticket/ssl'; ?>">Supported
                                Software List (SSL)</a></li>
                    </ul>
                </li>
                <!-- About Us as direct link (desktop & mobile) -->
                <li><a
                        href="<?php echo (basename($_SERVER['PHP_SELF']) === 'index.php') ? 'support-ticket/about-us' : '../support-ticket/about-us'; ?>">About
                        Us</a></li>
                <li><a
                        href="<?php echo (basename($_SERVER['PHP_SELF']) === 'index.php') ? 'support-ticket/buy-now' : '../support-ticket/buy-now'; ?>">Pricing</a>
                </li>
                <li><a
                        href="<?php echo (basename($_SERVER['PHP_SELF']) === 'index.php') ? 'support-ticket/faq' : '../support-ticket/faq'; ?>">FAQ</a>
                </li>
                <li><a
                        href="<?php echo (basename($_SERVER['PHP_SELF']) === 'index.php') ? 'support-ticket/contact' : '../support-ticket/contact'; ?>">Contact
                        Us</a></li>



                <?php if (isset($_SESSION['username'])) : ?>
                <!-- Desktop/Tablet View for logged-in users -->
                <li class="dropdown desktop-dropdown" style="display: flex; align-items: center;">
                    <a class="dropdown-toggle" href="#" role="button" id="dropdownMenuLink" data-bs-toggle="dropdown"
                        aria-expanded="false" style="display: flex; align-items: center; color: white;">
                        <i class="fa fa-user-circle" style="font-size: 20px; margin-right: 8px; text-align:left"></i>
                        <i
                            style="text-align:left"><?php echo !empty($user_email) ? $user_email : $_SESSION['username']; ?></i>
                    </a>
                    <ul class="dropdown-menu">
                        <!-- create-ticket -->
                        <li><a class="dropdown-item"
                                href="<?php echo (basename($_SERVER['PHP_SELF']) === 'index.php') ? './front-end/create-ticket.php' : '../front-end/create-ticket.php'; ?>"
                                style="color: black; font-weight:400;">Open Ticket</a></li>
                        <li><a class="dropdown-item"
                                href="<?php echo (basename($_SERVER['PHP_SELF']) === 'index.php') ? './front-end/./profile.php' : '../front-end/profile.php'; ?>"
                                style="color: black; font-weight:400;">Profile</a></li>
                        <li><a class="dropdown-item"
                                href="<?php echo (basename($_SERVER['PHP_SELF']) === 'index.php') ? './front-end/purchase-history.php' : '../front-end/purchase-history.php'; ?>"
                                style="color: black; font-weight:400;">Purchase History</a></li>
                        <li><a class="dropdown-item" href="<?php
                            $is_localhost = (strpos($_SERVER['HTTP_HOST'], 'localhost') !== false || strpos($_SERVER['HTTP_HOST'], '127.0.0.1') !== false);
                            $logout_url = $is_localhost ? '/helloit/index?logout=1' : '/index?logout=1';
                            echo $logout_url;
                        ?>" style="color: black; font-weight:400;">Logout</a></li>
                    </ul>
                </li>
                <!-- Mobile View for logged-in users -->
                <li class="mobile-profile-link">
                    <!-- profile.php -->
                    <a
                        href="<?php echo (basename($_SERVER['PHP_SELF']) === 'index.php') ? './front-end/profile.php' : '../front-end/profile.php'; ?>">My
                        profile</a>
                </li>
                <?php else : ?>
                <!-- Desktop/Tablet View for guests -->
                <li class="login-item desktop-login">
                    <a href="<?php echo (basename($_SERVER['PHP_SELF']) === 'index.php') ? 'support-ticket/sign-in' : '../support-ticket/sign-in'; ?>"
                        class="login">Log in</a>
                </li>
                <!-- Mobile View for guests -->
                <li class="mobile-profile-link">
                    <!-- sign-in.php -->
                    <a
                        href="<?php echo (basename($_SERVER['PHP_SELF']) === 'index.php') ? 'support-ticket/sign-in' : '../support-ticket/sign-in'; ?>">Login</a>
                </li>
                <?php endif; ?>
            </ul>
        </div>

        <div class="nav-right">
            <div class="actions">
                <?php
                // Hide Buy Now button if user is on buy-now.php page
                $current_page = basename($_SERVER['PHP_SELF']);
                if ($current_page !== 'buy-now.php'):
                ?>
                <a href="<?php echo (basename($_SERVER['PHP_SELF']) === 'index.php') ? 'support-ticket/buy-now' : '../support-ticket/buy-now'; ?>"
                    class="buynow button always-visible">Buy
                    Now</a>
                <?php endif; ?>
                <!-- Hamburger Menu Button (visible only on mobile) -->
                <div class="hamburger-menu">
                    <div class="hamburger-icon">
                        <span></span>
                        <span></span>
                        <span></span>
                    </div>
                </div>
            </div>
        </div>
    </nav>
</header>

<!-- Overlay for mobile menu -->
<div class="menu-overlay"></div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const hamburgerMenu = document.querySelector('.hamburger-menu');
    const navCenter = document.querySelector('.nav-center');
    const menuOverlay = document.querySelector('.menu-overlay');
    const body = document.body;
    const navbar = document.querySelector('.top-navbar-container');

    // Navbar scroll behavior
    let lastScrollTop = 0;
    let scrollThreshold = 10; // Minimum scroll distance to trigger hide/show

    window.addEventListener('scroll', function() {
        let scrollTop = window.pageYOffset || document.documentElement.scrollTop;

        if (Math.abs(scrollTop - lastScrollTop) < scrollThreshold) {
            return;
        }

        if (scrollTop > lastScrollTop && scrollTop > 100) {
            // Scrolling down & past 100px
            navbar.classList.add('hidden');
        } else {
            // Scrolling up
            navbar.classList.remove('hidden');
        }

        lastScrollTop = scrollTop;
    });

    // Toggle mobile menu
    hamburgerMenu.addEventListener('click', function() {
        hamburgerMenu.classList.toggle('active');
        navCenter.classList.toggle('active');
        menuOverlay.classList.toggle('active');
        body.classList.toggle('menu-open');
    });

    // Close menu when clicking on overlay
    menuOverlay.addEventListener('click', function() {
        document.querySelector('.hamburger-menu').classList.remove('active');
        navCenter.classList.remove('active');
        menuOverlay.classList.remove('active');
        body.classList.remove('menu-open');
    });

    // Close menu when clicking on a link (for mobile), but exclude dropdown toggles
    const menuLinks = document.querySelectorAll('.nav-links a:not(.dropdown-toggle)');
    menuLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            // Don't close menu if this is a dropdown item
            if (this.classList.contains('dropdown-item') && window.innerWidth <= 767) {
                // Just let the click go through without closing the menu
                return;
            }

            if (window.innerWidth <= 767) {
                document.querySelector('.hamburger-menu').classList.remove('active');
                navCenter.classList.remove('active');
                menuOverlay.classList.remove('active');
                body.classList.remove('menu-open');
            }
        });
    });

    // Handle dropdown toggles separately
    const dropdownToggles = document.querySelectorAll('.dropdown-toggle');
    dropdownToggles.forEach(toggle => {
        toggle.addEventListener('click', function(e) {
            if (window.innerWidth <= 767) {
                // Prevent the default action (which would be to close the menu)
                e.stopPropagation();

                // Find the dropdown menu associated with this toggle
                const dropdownMenu = this.nextElementSibling;

                // Toggle the 'show' class manually
                if (dropdownMenu && dropdownMenu.classList.contains('dropdown-menu')) {
                    if (dropdownMenu.classList.contains('show')) {
                        dropdownMenu.classList.remove('show');
                    } else {
                        dropdownMenu.classList.add('show');
                    }
                }
            }
        });
    });

    // Prevent dropdown items from closing the mobile menu
    const dropdownItems = document.querySelectorAll('.dropdown-item');
    dropdownItems.forEach(item => {
        item.addEventListener('click', function(e) {
            if (window.innerWidth <= 767) {
                // Only stop propagation, don't prevent default
                // This allows the link to work but prevents the menu from closing
                e.stopPropagation();
            }
        });
    });

    // Mobile About Us dropdown toggle (parent button only)
    document.querySelectorAll('.mobile-dropdown .dropdown-toggle-parent').forEach(function(toggle) {
        toggle.addEventListener('click', function(e) {
            e.preventDefault();
            var menu = toggle.closest('.mobile-dropdown').querySelector('.dropdown-menu');
            // Close other open dropdowns (optional)
            document.querySelectorAll('.mobile-dropdown .dropdown-menu').forEach(function(
                otherMenu) {
                if (otherMenu !== menu) {
                    otherMenu.classList.remove('show');
                    var otherArrow = otherMenu.closest('.mobile-dropdown')
                        .querySelector('.dropdown-arrow');
                    if (otherArrow) otherArrow.style.transform = 'rotate(0deg)';
                }
            });
            // Toggle this dropdown
            menu.classList.toggle('show');
            // Rotate arrow
            var arrow = toggle.querySelector('.dropdown-arrow');
            if (menu.classList.contains('show')) {
                arrow.style.transform = 'rotate(180deg)';
            } else {
                arrow.style.transform = 'rotate(0deg)';
            }
        });
    });
});

// Hero Carousel Auto-Slide Functionality
let currentSlideIndex = 1;
let slideInterval;
let isManualNavigation = false;

function showSlide(n) {
    const slides = document.querySelectorAll('.hero-slide');
    const dots = document.querySelectorAll('.dot');

    if (n > slides.length) { currentSlideIndex = 1; }
    if (n < 1) { currentSlideIndex = slides.length; }

    // Hide all slides
    slides.forEach(slide => slide.classList.remove('active'));
    dots.forEach(dot => dot.classList.remove('active'));

    // Show current slide
    if (slides[currentSlideIndex - 1]) {
        slides[currentSlideIndex - 1].classList.add('active');
    }
    if (dots[currentSlideIndex - 1]) {
        dots[currentSlideIndex - 1].classList.add('active');
    }
}

function nextSlide() {
    // Only auto-advance if not in manual navigation mode
    if (!isManualNavigation) {
        currentSlideIndex++;
        showSlide(currentSlideIndex);
    }
}

function currentSlide(n) {
    // Stop auto-slide and set manual navigation flag
    clearInterval(slideInterval);
    isManualNavigation = true;

    currentSlideIndex = n;
    showSlide(currentSlideIndex);

    // Resume auto-slide after 8 seconds (longer delay after manual navigation)
    setTimeout(function() {
        isManualNavigation = false;
        startAutoSlide();
    }, 8000);
}

function startAutoSlide() {
    // Clear any existing interval first
    clearInterval(slideInterval);
    slideInterval = setInterval(nextSlide, 5000); // 5 seconds
}

// Initialize carousel when page loads
document.addEventListener('DOMContentLoaded', function() {
    // Only start carousel if we're on index page and carousel exists
    if (document.querySelector('.hero-carousel-container')) {
        showSlide(currentSlideIndex);
        startAutoSlide();

        // Pause auto-slide when user hovers over the carousel
        const carousel = document.querySelector('.hero-carousel-container');
        if (carousel) {
            carousel.addEventListener('mouseenter', function() {
                clearInterval(slideInterval);
                isManualNavigation = true; // Treat hover as manual interaction
            });

            carousel.addEventListener('mouseleave', function() {
                // Resume auto-slide after a short delay when mouse leaves
                setTimeout(function() {
                    isManualNavigation = false;
                    startAutoSlide();
                }, 2000); // 2 second delay after mouse leaves
            });

            // Add swipe functionality for banner carousel
            let bannerTouchStartX = 0;
            let bannerTouchEndX = 0;
            let bannerTouchStartY = 0;
            let bannerTouchEndY = 0;
            const bannerMinSwipeDistance = 50;
            const bannerMaxVerticalDistance = 100;

            function handleBannerTouchStart(e) {
                bannerTouchStartX = e.changedTouches[0].screenX;
                bannerTouchStartY = e.changedTouches[0].screenY;
            }

            function handleBannerTouchEnd(e) {
                bannerTouchEndX = e.changedTouches[0].screenX;
                bannerTouchEndY = e.changedTouches[0].screenY;

                const horizontalDistance = bannerTouchEndX - bannerTouchStartX;
                const verticalDistance = Math.abs(bannerTouchEndY - bannerTouchStartY);

                // Check if this is a valid horizontal swipe
                if (Math.abs(horizontalDistance) > bannerMinSwipeDistance && verticalDistance < bannerMaxVerticalDistance) {
                    // Stop auto-slide temporarily
                    clearInterval(slideInterval);
                    isManualNavigation = true;

                    if (horizontalDistance > 0) {
                        // Swipe right - go to previous slide
                        currentSlideIndex--;
                        if (currentSlideIndex < 1) {
                            currentSlideIndex = document.querySelectorAll('.hero-slide').length;
                        }
                    } else {
                        // Swipe left - go to next slide
                        currentSlideIndex++;
                        if (currentSlideIndex > document.querySelectorAll('.hero-slide').length) {
                            currentSlideIndex = 1;
                        }
                    }

                    showSlide(currentSlideIndex);

                    // Resume auto-slide after delay
                    setTimeout(function() {
                        isManualNavigation = false;
                        startAutoSlide();
                    }, 8000);
                }
            }

            // Add touch event listeners to the carousel
            carousel.addEventListener('touchstart', handleBannerTouchStart, { passive: true });
            carousel.addEventListener('touchend', handleBannerTouchEnd, { passive: true });

            // Add invisible navigation buttons
            const leftNavButton = document.createElement('div');
            leftNavButton.className = 'carousel-nav-button carousel-nav-left';
            leftNavButton.innerHTML = '<i class="fa fa-chevron-left"></i>';
            leftNavButton.addEventListener('click', function() {
                clearInterval(slideInterval);
                isManualNavigation = true;

                currentSlideIndex--;
                if (currentSlideIndex < 1) {
                    currentSlideIndex = document.querySelectorAll('.hero-slide').length;
                }
                showSlide(currentSlideIndex);

                setTimeout(function() {
                    isManualNavigation = false;
                    startAutoSlide();
                }, 8000);
            });

            const rightNavButton = document.createElement('div');
            rightNavButton.className = 'carousel-nav-button carousel-nav-right';
            rightNavButton.innerHTML = '<i class="fa fa-chevron-right"></i>';
            rightNavButton.addEventListener('click', function() {
                clearInterval(slideInterval);
                isManualNavigation = true;

                currentSlideIndex++;
                if (currentSlideIndex > document.querySelectorAll('.hero-slide').length) {
                    currentSlideIndex = 1;
                }
                showSlide(currentSlideIndex);

                setTimeout(function() {
                    isManualNavigation = false;
                    startAutoSlide();
                }, 8000);
            });

            // Append navigation buttons to carousel
            carousel.appendChild(leftNavButton);
            carousel.appendChild(rightNavButton);
        }
    }
});
</script>

<style>
.top-navbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: rgba(71, 59, 240, 0.95);
    backdrop-filter: blur(10px);
    padding: 15px 30px;
    border-radius: 0;
    height: 90px;
    box-sizing: border-box;
    transition: transform 0.3s ease;
}

.nav-center {
    flex-grow: 1;
    display: flex;
    justify-content: center;
}



.nav-right {
    display: flex;
    align-items: center;
    gap: 15px;
}

.bg-default-2 {
    background: #5045F1 !important;
    height: 750px !important;
}

.title.gr-text-6 {
    font-family: 'Circular Std', sans-serif;
    font-weight: 700;
    font-size: 21px;
    line-height: 44px;
    color: black;
}

body {
    margin: 0;
    padding-top: 80px;
    /* Account for fixed navbar height */
    /* Ensure no margin around the body */
}

.hero {
    margin-top: -70px;
}



.top-navbar-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 1000;
    transition: transform 0.3s ease;
}

.top-navbar-container.hidden {
    transform: translateY(-100%);
}


.logo {
    font-size: 1.5em;
    font-weight: bold;
    margin-right: 10px;
}

.nav-links {
    list-style: none;
    padding: 0;
    margin: 0;
    display: flex;
    gap: 30px;
}

.nav-links a {
    text-decoration: none;
    /* color: #333; */
    color: white;
    font-weight: 700;
}

.actions {
    display: flex;
    gap: 15px;
    align-items: center;
    position: relative;
}

/* Hamburger Menu Styles */
.hamburger-menu {
    display: none;
    cursor: pointer;
    margin-left: 15px;
    z-index: 1001;
}

.hamburger-icon {
    width: 30px;
    height: 20px;
    position: relative;
    transition: all 0.3s ease;
}

.hamburger-icon span {
    display: block;
    position: absolute;
    height: 3px;
    width: 100%;
    background: white;
    border-radius: 3px;
    opacity: 1;
    left: 0;
    transform: rotate(0deg);
    transition: .25s ease-in-out;
}

.hamburger-icon span:nth-child(1) {
    top: 0px;
}

.hamburger-icon span:nth-child(2) {
    top: 8px;
}

.hamburger-icon span:nth-child(3) {
    top: 16px;
}

.hamburger-menu.active .hamburger-icon span:nth-child(1) {
    top: 8px;
    transform: rotate(135deg);
    background-color: white;
}

.hamburger-menu.active .hamburger-icon span:nth-child(2) {
    opacity: 0;
    left: -60px;
    background-color: white;
}

.hamburger-menu.active .hamburger-icon span:nth-child(3) {
    top: 8px;
    transform: rotate(-135deg);
    background-color: white;
}

.hamburger-menu.active .hamburger-icon {
    transform: scale(1.2);
}

/* Hide/show elements based on screen size */
.mobile-profile-link {
    display: none;
}

/* Style for mobile profile link */
.mobile-profile-link a {
    display: block;
    padding: 15px 20px;
    font-size: 20px;
    color: white;
    text-align: center;
    margin-top: 15px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.mobile-profile-link a:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

/* Responsive Styles */
@media (max-width: 1200px) {
    .nav-links {
        gap: 20px;
    }
}

@media (max-width: 991px) {
    body {
        padding-top: 70px;
    }

    .top-navbar {
        padding: 12px 20px;
        height: 70px;
    }

    .nav-links {
        gap: 15px;
    }

    .nav-links a {
        font-size: 14px;
    }

    .logo img {
        height: 45px !important;
    }
}

@media (max-width: 767px) {
    .hamburger-menu {
        display: block;
    }

    .buynow.button {
        display: none !important;
    }

    /* Show mobile profile link and hide desktop dropdown */
    .mobile-profile-link {
        display: block;
    }

    .desktop-dropdown,
    .desktop-login {
        display: none !important;
    }

    .nav-center {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100vh;
        background-color: rgba(71, 59, 240, 0.98);
        flex-direction: column;
        justify-content: center;
        align-items: center;
        padding-top: 0;
        z-index: 1000;
        overflow-y: auto;
        opacity: 0;
        visibility: hidden;
        transition: opacity 0.4s ease, visibility 0.4s ease;
    }

    .nav-center.active {
        opacity: 1;
        visibility: visible;
    }

    .nav-links {
        flex-direction: column;
        gap: 20px;
        width: 100%;
        text-align: center;
    }

    .nav-links li {
        width: 100%;
        border-bottom: none;
    }

    .nav-links li a {
        display: block;
        padding: 15px 20px;
        font-size: 24px;
        font-weight: 400;
        letter-spacing: 1px;
        transition: transform 0.3s ease;
    }

    .nav-links li a:hover {
        transform: scale(1.1);
    }

    /* Mobile dropdown styles */
    .dropdown {
        width: 100%;
        text-align: center;
    }

    .dropdown-toggle {
        font-size: 24px !important;
        padding: 15px 20px !important;
        width: 100%;
        display: block;
        position: relative;
    }

    .dropdown-toggle::after {
        content: '';
        display: inline-block;
        margin-left: 8px;
        vertical-align: middle;
        border-top: 6px solid white;
        border-right: 6px solid transparent;
        border-left: 6px solid transparent;
        transition: transform 0.3s ease;
    }

    .dropdown-toggle[aria-expanded="true"]::after {
        transform: rotate(180deg);
    }

    .dropdown-menu {
        position: static !important;
        width: 100%;
        background-color: rgba(255, 255, 255, 0.1) !important;
        border: none !important;
        border-radius: 8px !important;
        margin: 0 !important;
        padding: 10px 0 !important;
        text-align: center;
        display: none;
        max-height: 0;
        overflow: hidden;
        transition: max-height 0.3s ease-out;
    }

    .dropdown-menu.show {
        display: block !important;
        max-height: 500px;
        transition: max-height 0.5s ease-in;
    }

    .dropdown-item {
        padding: 10px 20px !important;
        color: white !important;
        font-size: 20px !important;
        transition: all 0.3s ease;
        margin: 5px 10px !important;
        border-radius: 5px !important;
        display: block;
        width: calc(100% - 20px);
    }

    .dropdown-item:hover {
        background-color: rgba(255, 255, 255, 0.2) !important;
        transform: scale(1.05);
    }

    .login-item {
        margin-top: 15px;
    }

    .nav-right {
        margin-left: auto;
    }

    .buynow.button {
        padding: 8px 15px;
        font-size: 14px;
    }

    /* Overlay for mobile menu */
    .menu-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.7);
        z-index: 999;
        opacity: 0;
        visibility: hidden;
        transition: opacity 0.3s ease, visibility 0.3s ease;
    }

    .menu-overlay.active {
        opacity: 1;
        visibility: visible;
    }

    body.menu-open {
        overflow: hidden;
    }

    .mobile-dropdown {
        display: block !important;
    }

    .desktop-dropdown {
        display: none !important;
    }

    .mobile-dropdown .dropdown-menu {
        background-color: rgba(255, 255, 255, 0.12) !important;
        box-shadow: none !important;
        border-radius: 0 0 0px 0px !important;
        margin: 0 !important;
        padding: 0 !important;
        text-align: center !important;
        width: 100% !important;
    }

    .mobile-dropdown .dropdown-item {
        color: white !important;
        font-size: 20px !important;
        border-radius: 0 !important;
        margin: 0 !important;
        padding: 12px 0 !important;
        text-align: center !important;
        width: 100% !important;
        display: block !important;
    }

    .hero-img img[alt="Additional Tech Support"] {
        right: -80px !important;
        bottom: 30px !important;
        width: 250px !important;
        height: 250px !important;
        min-width: unset !important;
        max-width: unset !important;
    }

    .hero-img img[alt="Tech Support"] {
        max-width: 70vw !important;
        height: auto !important;
        margin: 0 auto !important;
    }

    .hero-img {
        min-height: 0 !important;
        margin-bottom: 0 !important;
        text-align: center;
    }
}

@media (min-width: 768px) {
    .mobile-dropdown {
        display: none !important;
    }

    .mb-md-8,
    .my-md-8 {
        text-align: left;
    }

    p.gr-text-8.mb-5.mb-md-11.hero-subtitle {
        text-align: left;
        margin-left: 3px;
    }

    .hero-content.text-center.text-lg-start {
        text-align: left !important;
        margin-top: -70px;
    }

    .hero-btn {
        text-align: left !important;
    }

    .hero-btn .hero-button {
        margin-left: 0 !important;
        margin-right: 0 !important;
        display: inline-block !important;
    }
}

@media (max-width: 480px) {
    body {
        padding-top: 60px;
    }

    .top-navbar {
        padding: 10px 15px;
        height: 60px;
    }

    .logo img {
        height: 40px !important;
    }

    .buynow.button {
        padding: 6px 12px;
        font-size: 12px;
    }
}

.actions a {
    text-decoration: none;
    /* color: #333; */
    color: white;
    font-weight: 700;
    transition: transform 0.3s ease, opacity 0.3s ease;
}

.actions .button {
    background-color: #007bff;
    color: white;
    padding: 10px 15px;
    border-radius: 20px;
}

.actions .buynow.hidden {
    opacity: 0;
    transform: translateY(10px);
    pointer-events: none;
}

.actions .buynow.visible {
    opacity: 1;
    transform: translateY(0);
}



/* .hero {
        height: 100px;
        background-image: linear-gradient(to bottom right, #f0f0f0, #e0e0e0);
        display: flex;
        justify-content: center;
        align-items: center;
        text-align: center;
    } */

.content section {
    padding: 50px 0px;
}

/* Hero Section Responsive Styles */
.hero {
    overflow: hidden;
    position: relative;
}

.hero-title {
    color: white;
    font-family: 'Circular Std', sans-serif;
    font-weight: 700;
    font-size: 2.8rem;
    line-height: 1.2;
}

.hero-subtitle {
    color: #D7D5FC;
    font-family: 'Circular Std', sans-serif;
    font-weight: 400;
    font-size: 1.1rem;
    line-height: 1.5;
}

.hero-button {
    background-color: #FFC84B;
    color: #000;
    font-weight: 600;
    padding: 12px 24px;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.hero-button:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.hero-image-container {
    position: relative;
    z-index: 1;
}

.hero-img img {
    max-width: 100%;
    height: auto;
}

/* Media Queries for Hero Section */
@media (max-width: 1200px) {
    .hero-title {
        font-size: 2.5rem;
    }

    .hero-subtitle {
        font-size: 1rem;
    }
}

@media (max-width: 991px) {
    .hero {
        padding-top: 120px !important;
        padding-bottom: 60px !important;
    }

    .hero-title {
        font-size: 2.2rem;
    }

    .hero-img img {
        max-width: 90%;
        margin: 0 auto;
    }
}

@media (max-width: 767px) {
    .hero {
        padding-top: 100px !important;
        padding-bottom: 50px !important;
        text-align: center;
    }

    .hero-title {
        font-size: 1.8rem;
        margin-bottom: 15px !important;
    }

    .hero-subtitle {
        font-size: 0.95rem;
        margin-bottom: 20px !important;
    }

    /* Remove top margin since image is now first */
    .hero-img {
        margin-top: 0;
        margin-bottom: 20px;
    }

    .hero-img img {
        max-width: 80%;
        margin: 0 auto;
    }

    /* Add spacing between image and text */
    .hero-content {
        padding-top: 15px;
    }
}

@media (max-width: 576px) {

    .hero {
        padding-top: 150px !important;
        padding-bottom: 40px !important;
    }

    .hero-title {
        font-size: 1.5rem;
    }

    .hero-subtitle {
        font-size: 0.9rem;
        padding: 0 15px;
    }

    .hero-button {
        padding: 10px 20px;
        font-size: 0.9rem;
    }

    .hero-img img {
        max-width: 90%;
    }
}

.nav-links .dropdown-menu .dropdown-item {
    color: #222 !important;
    background: #fff !important;
    font-weight: 400 !important;
    transition: none !important;
}

.nav-links .dropdown-menu .dropdown-item:hover,
.nav-links .dropdown-menu .dropdown-item:focus {
    color: #222 !important;
    background: #fff !important;
    font-weight: 400 !important;
    text-decoration: underline !important;
    transition: none !important;
}
</style>
<?php if (basename($_SERVER['PHP_SELF']) === 'index.php'): ?>
<div class="content">
    <section class="hero position-relative bg-default-2 pt-32 pt-lg-37 pb-15 pb-lg-27">
        <div class="hero-carousel-container">
            <!-- Banner Slide 1 -->
            <div class="hero-slide active" id="slide1">
                <div class="container">
                    <div class="row justify-content-center align-items-center">
                        <!-- Hero image - First on mobile, second on desktop -->
                        <div
                            class="col-12 col-sm-10 col-md-7 col-lg-5 offset-xl-1 align-self-center order-1 order-lg-2 text-center text-lg-start hero-image-container">
                            <div class="hero-img position-relative" data-aos="fade-left" data-aos-duration="1100"
                                data-aos-once="true" style="min-height: 500px;">
                                <div style="position: relative; display: inline-block;">
                                    <img fetchpriority="high" decoding="sync" src="image/wp/image.webp" width="500" height="500"
                                        class="attachment-full size-full wp-image-13629 img-fluid" alt="Tech Support"
                                        sizes="(max-width: 480px) 280px, (max-width: 768px) 350px, (max-width: 992px) 400px, 500px"
                                        style="position: relative; z-index: 2;" />
                                    <picture>
                                        <source srcset="image/wp/pic-1.webp" type="image/webp"
                                                sizes="(max-width: 480px) 250px, (max-width: 768px) 320px, 520px">
                                        <img src="image/wp/pic-1.png" alt="Additional Tech Support Professional" loading="lazy"
                                            class="hero-background-woman" width="320" height="250" />
                                            <!-- width="520" height="550" -->
                                    </picture>
                                </div>
                                <div class="gr-abs-tl gr-z-index-n1 d-none d-md-block" data-aos="zoom-in" data-aos-delay="600"
                                    data-aos-duration="800" data-aos-once="true">
                                    <img src="image/l5/png/l5-dot-shape.png" alt="" class="img-fluid" loading="lazy" decoding="async" width="80" height="80">
                                </div>
                            </div>
                        </div>

                        <!-- Hero content - Second on mobile, first on desktop -->
                        <div class="col-12 col-md-10 col-lg-7 col-xl-6 order-2 order-lg-1 mt-4 mt-lg-0 mb-0 mb-lg-0"
                            data-aos="fade-right" data-aos-duration="500" data-aos-once="true">
                            <div class="hero-content text-center text-lg-start">
                                <h1 class="title gr-text-2 mb-4 mb-md-8 hero-title">A Better Tech Support Experience From Start
                                    To Finish.</h1>
                                <p class="gr-text-8 mb-5 mb-md-11 hero-subtitle">Get instant help from tech experts so you can
                                    focus on what is truly important.</p>
                                <div class="hero-btn">
                                    <a href="./front-end/buy-now.php" class="btn gr-hover-y hero-button"
                                       aria-label="Get started with HelloIT tech support services">Let's Get Started</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Banner Slide 2 -->
            <div class="hero-slide" id="slide2">
                <div class="container">
                    <div class="row justify-content-center align-items-center">
                        <!-- Hero image - First on mobile, second on desktop -->
                        <div
                            class="col-12 col-sm-10 col-md-7 col-lg-5 offset-xl-1 align-self-center order-1 order-lg-2 text-center text-lg-start hero-image-container">
                            <div class="hero-img position-relative" data-aos="fade-left" data-aos-duration="1100"
                                data-aos-once="true" style="min-height: 500px;">
                                <div style="position: relative; display: inline-block;">
                                    <img fetchpriority="high" decoding="sync" src="image/wp/save-up-to-80-cost.png" width="500" height="500"
                                        class="attachment-full size-full wp-image-13629 img-fluid" alt="Save up to 80% on IT costs"
                                        sizes="(max-width: 480px) 280px, (max-width: 768px) 350px, (max-width: 992px) 400px, 500px"
                                        style="position: relative; z-index: 2;" />
                                </div>
                                <!-- <div class="gr-abs-tl gr-z-index-n1 d-none d-md-block" data-aos="zoom-in" data-aos-delay="600"
                                    data-aos-duration="800" data-aos-once="true">
                                    <img src="image/l5/png/l5-dot-shape.png" alt="" class="img-fluid" loading="lazy" decoding="async" width="80" height="80">
                                </div> -->
                            </div>
                        </div>

                        <!-- Hero content - Second on mobile, first on desktop -->
                        <div class="col-12 col-md-10 col-lg-7 col-xl-6 order-2 order-lg-1 mt-4 mt-lg-0 mb-0 mb-lg-0"
                            data-aos="fade-right" data-aos-duration="500" data-aos-once="true">
                            <div class="hero-content text-center text-lg-start">
                                <h1 class="title gr-text-2 mb-4 mb-md-8 hero-title">Save up to 80% on IT costs</h1>
                                <p class="gr-text-8 mb-5 mb-md-11 hero-subtitle">With full support from HelloIT, you can focus on growing your business without the high costs of in-house IT.</p>
                                <div class="hero-btn">
                                    <a href="./front-end/buy-now.php" class="btn gr-hover-y hero-button"
                                       aria-label="Start your IT support journey with HelloIT">Get Expert Help Now</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

           
        </div>
         <!-- Carousel Navigation Dots -->
            <div class="hero-carousel-dots">
                <span class="dot active" onclick="currentSlide(1)"></span>
                <span class="dot" onclick="currentSlide(2)"></span>
            </div>
    </section>
</div>
<?php endif; ?>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>


<div class="floating-cart">
    <a
        href="<?php echo (basename($_SERVER['PHP_SELF']) === 'index.php') ? 'support-ticket/cart' : '../support-ticket/cart'; ?>"
        aria-label="View shopping cart"
        title="View shopping cart">
        <i class="fa fa-shopping-cart" aria-hidden="true"></i>
        <span class="sr-only">View shopping cart</span>
    </a>
</div>
<style>
.floating-cart {
    position: fixed;
    bottom: 20px;
    /* Distance from the bottom of the screen */
    right: 20px;
    /* Distance from the right of the screen */
    width: 60px;
    height: 60px;
    background-color: #007bff;
    /* Button background color */
    border-radius: 50%;
    /* Makes the button circular */
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.2);
    /* Adds a shadow for better visibility */
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    /* Ensures it stays on top of other elements */
    transition: transform 0.3s ease, background-color 0.3s ease;
}

.floating-cart a {
    color: white;
    /* Icon color */
    font-size: 24px;
    /* Icon size */
    text-decoration: none;
}

.floating-cart:hover {
    transform: scale(1.1);
    /* Slightly enlarges the button on hover */
    background-color: #0056b3;
    /* Darker shade on hover */
}

/* Screen reader only text for accessibility */
.sr-only {
    position: absolute !important;
    width: 1px !important;
    height: 1px !important;
    padding: 0 !important;
    margin: -1px !important;
    overflow: hidden !important;
    clip: rect(0, 0, 0, 0) !important;
    white-space: nowrap !important;
    border: 0 !important;
}

/* Hero Carousel Styles */
.hero-carousel-container {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
}

/* Carousel Navigation Buttons */
.carousel-nav-button {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 60px;
    height: 60px;
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    z-index: 10;
    opacity: 0;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.carousel-nav-button:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.6);
    opacity: 1 !important;
    transform: translateY(-50%) scale(1.1);
}

.carousel-nav-button i {
    color: white;
    font-size: 20px;
    font-weight: bold;
}

.carousel-nav-left {
    left: 30px;
}

.carousel-nav-right {
    right: 30px;
}

/* Show navigation buttons on hover */
.hero-carousel-container:hover .carousel-nav-button {
    opacity: 0.7;
}

.hero-slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    transition: opacity 0.8s ease-in-out;
    z-index: 1;
}

.hero-slide.active {
    opacity: 1;
    z-index: 2;
}

.hero-carousel-dots {
    position: absolute;
    bottom: 50px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 12px;
    z-index: 10;
}

.dot {
    height: 12px;
    width: 12px;
    background-color: rgba(255, 255, 255, 0.4);
    border-radius: 50%;
    display: inline-block;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.dot.active,
.dot:hover {
    background-color: #FFC84B;
    border-color: rgba(255, 255, 255, 0.8);
    transform: scale(1.2);
}

/* Hero background woman image responsive styling */
.hero-background-woman {
    position: absolute;
    right: -200px;
    bottom: 0;
    transform: translateY(-20px);
    z-index: 1;
    height: 550px;
    min-width: 520px;
}

/* Tablet responsive adjustments for woman's photo - broader range */
@media (max-width: 1200px) and (min-width: 769px) {
    .hero-background-woman {
        height: 450px !important;
        width: 400px !important;
        min-width: unset !important;
        max-width: 400px !important;
        right: -120px !important;
        transform: translateY(-15px) !important;
        object-fit: cover !important;
    }
}

/* Mobile responsive adjustments for woman's photo */
@media (max-width: 768px) {
    .hero-background-woman {
        height: 350px !important;
        width: auto !important;
        min-width: 320px !important;
        right: -150px !important;
        transform: translateY(-10px) !important;
    }

    /* Optimize hero image for tablets */
    .hero-img img[alt="Tech Support"],
    .hero-img img[alt="Professional IT Support"] {
        max-width: 350px !important;
        height: auto !important;
    }

    /* Carousel dots for tablets */
    .hero-carousel-dots {
        bottom: 45px;
        gap: 10px;
    }

    /* Mobile navigation buttons */
    .carousel-nav-button {
        width: 50px;
        height: 50px;
        opacity: 0.5;
    }

    .carousel-nav-left {
        left: 15px;
    }

    .carousel-nav-right {
        right: 15px;
    }

    .carousel-nav-button i {
        font-size: 18px;
    }

    /* Always show nav buttons on mobile for better accessibility */
    .hero-carousel-container .carousel-nav-button {
        opacity: 0.6;
    }
}

@media (max-width: 480px) {
    .hero-background-woman {
        height: 280px !important;
        min-width: 250px !important;
        right: -120px !important;
        transform: translateY(-5px) !important;
    }

    /* Optimize hero image for mobile */
    .hero-img img[alt="Tech Support"],
    .hero-img img[alt="Professional IT Support"] {
        max-width: 280px !important;
        height: auto !important;
    }

    /* Reduce hero section height on mobile */
    .hero-img {
        min-height: 300px !important;
    }

    /* Carousel dots responsive */
    .hero-carousel-dots {
        bottom: 40px;
        gap: 8px;
    }

    .dot {
        height: 10px;
        width: 10px;
    }

    /* Smaller navigation buttons for mobile */
    .carousel-nav-button {
        width: 40px;
        height: 40px;
    }

    .carousel-nav-left {
        left: 10px;
    }

    .carousel-nav-right {
        right: 10px;
    }

    .carousel-nav-button i {
        font-size: 16px;
    }
}
</style>
<?php //include_once('./includes/tawk-widget.php'); ?>