<?php
// test-appika-send-message.php
// Test sending a message to <PERSON><PERSON><PERSON> as user or admin (no DB storage)

require_once __DIR__ . '/functions/graphql_functions.php';

// Helper: Pretty print message result
function renderMessage($msg) {
    $sender = $msg['creator_by_contact'] ? 'User' : ($msg['creator_by_agent'] ? 'Admin' : 'System');
    $senderLabel = $sender === 'User' ? '<span style="color:#007bff">User</span>' : ($sender === 'Admin' ? '<span style="color:#28a745">Admin</span>' : 'System');
    echo '<div style="border:1px solid #ccc; border-radius:5px; margin:10px 0; padding:10px;">';
    echo '<strong>Sender:</strong> ' . $senderLabel . '<br>';
    echo '<strong>Message:</strong> ' . nl2br(htmlspecialchars($msg['message'])) . '<br>';
    echo '<strong>Type:</strong> ' . htmlspecialchars($msg['msg_type'] ?? '-') . '<br>';
    echo '<strong>ID:</strong> ' . htmlspecialchars($msg['id'] ?? '-') . '<br>';
    echo '</div>';
}

// Handle form submit
$response = null;
$error = null;
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $ticketId = intval($_POST['ticket_id'] ?? 0);
    $message = trim($_POST['message'] ?? '');
    $role = $_POST['role'] ?? 'user';

    if ($ticketId <= 0 || $message === '') {
        $error = 'Ticket ID and message are required.';
    } else {
        // Build mutation
        $mutation = '
        mutation updateTicketMessageByContact($id: Int!, $reply_msg: String!) {
            updateTicketMessageByContact(id: $id, reply_msg: $reply_msg) {
                id
                message
                msg_type
                creator_by_contact
                creator_by_agent
            }
        }';
        $variables = [
            'id' => $ticketId,
            'reply_msg' => $message
        ];

        // Optionally, set a header or context for admin (if API supports it)
        // For now, just send as-is (Appika will determine sender by auth key)
        $result = makeGraphQLRequest($mutation, $variables);
        if ($result['success'] && isset($result['data']['data']['updateTicketMessageByContact'])) {
            $response = $result['data']['data']['updateTicketMessageByContact'];
        } else {
            $error = $result['error'] ?? 'Unknown error';
        }
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Test Send Message to Appika</title>
    <link rel="stylesheet" href="css/bootstrap.css">
    <style>
        body { background: #f8f9fa; padding: 30px; }
        .container { max-width: 600px; margin: auto; background: #fff; border-radius: 8px; box-shadow: 0 2px 8px #0001; padding: 30px; }
        h1 { font-size: 1.5rem; margin-bottom: 1.5rem; }
    </style>
</head>
<body>
<div class="container">
    <h1>Test Send Message to Appika (No DB)</h1>
    <form method="post">
        <div class="mb-3">
            <label for="ticket_id" class="form-label">Ticket ID</label>
            <input type="number" class="form-control" id="ticket_id" name="ticket_id" required>
        </div>
        <div class="mb-3">
            <label for="message" class="form-label">Message</label>
            <textarea class="form-control" id="message" name="message" rows="3" required></textarea>
        </div>
        <div class="mb-3">
            <label class="form-label">Role (for test only)</label><br>
            <select name="role" class="form-select" style="width:auto;display:inline-block;">
                <option value="user">User</option>
                <option value="admin">Admin</option>
            </select>
            <span style="color:#888;font-size:0.9em;">(API will use your current API key role)</span>
        </div>
        <button type="submit" class="btn btn-primary">Send Message</button>
    </form>
    <hr>
    <?php if ($error): ?>
        <div class="alert alert-danger"><?php echo htmlspecialchars($error); ?></div>
    <?php endif; ?>
    <?php if ($response): ?>
        <h3>API Response:</h3>
        <?php renderMessage($response); ?>
    <?php endif; ?>
</div>
</body>
</html> 