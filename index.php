<?php
session_start();
include('functions/server.php');
if (isset($_GET['logout'])) {
  // Only unset front-end user session variables, not admin variables
  unset($_SESSION['username']);
  unset($_SESSION['user_id']);
  unset($_SESSION['success']);
  // Preserve admin session if it exists
  $admin_username = isset($_SESSION['admin_username']) ? $_SESSION['admin_username'] : null;
  $admin_id = isset($_SESSION['admin_id']) ? $_SESSION['admin_id'] : null;
  $admin_role = isset($_SESSION['admin_role']) ? $_SESSION['admin_role'] : null;

  // If no admin session exists, destroy the entire session
  if (!$admin_username) {
    session_destroy();
    session_start();
  }

  // Restore admin session variables if they existed
  if ($admin_username) {
    $_SESSION['admin_username'] = $admin_username;
    $_SESSION['admin_id'] = $admin_id;
    $_SESSION['admin_role'] = $admin_role;
  }

  header('location:index.php');
}
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <meta name="description" content="Get expert IT helpdesk support with HelloIT. Professional tech support services with 15+ years experience. Pay-as-you-go pricing, no monthly fees. Trusted by businesses worldwide.">
    <meta name="keywords" content="IT support, helpdesk, tech support, IT services, computer support, technical assistance, IT helpdesk, business IT support">
    <meta name="author" content="HelloIT">
    <meta name="robots" content="index, follow">
    <meta property="og:title" content="HelloIT - Professional IT Helpdesk Support & Tech Services">
    <meta property="og:description" content="Get expert IT helpdesk support with HelloIT. Professional tech support services with 15+ years experience. Pay-as-you-go pricing, no monthly fees.">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://helloit.io">
    <meta property="og:image" content="https://helloit.io/image/wp/pc.jpg">
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="HelloIT - Professional IT Helpdesk Support & Tech Services">
    <meta name="twitter:description" content="Get expert IT helpdesk support with HelloIT. Professional tech support services with 15+ years experience.">
    <meta name="twitter:image" content="https://helloit.io/image/wp/pc.jpg">
    <title>HelloIT - Professional IT Helpdesk Support & Tech Services</title>
    <link rel="shortcut icon" href="image/png/favicon.png" type="image/x-icon">
    <link rel="canonical" href="https://helloit.io">

    <!-- Resource Hints for Performance -->
    <link rel="dns-prefetch" href="//fonts.googleapis.com">
    <link rel="dns-prefetch" href="//cdnjs.cloudflare.com">
    <link rel="preconnect" href="https://fonts.googleapis.com" crossorigin>

    <!-- Preload critical resources -->
    <link rel="preload" href="image/wp/image.png" as="image" type="image/png">
    <link rel="preload" href="image/l5/png/l5-dot-shape.png" as="image" type="image/png">
    <link rel="preload" href="fonts/typography-font/CircularStd-Bold.woff2" as="font" type="font/woff2" crossorigin>
    <link rel="preload" href="fonts/typography-font/CircularStd-Book.woff2" as="font" type="font/woff2" crossorigin>

    <!-- Critical CSS loaded synchronously -->
    <link rel="stylesheet" href="css/bootstrap.css">
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="fonts/typography-font/typo.css">

    <!-- Non-critical CSS loaded asynchronously -->
    <link rel="preload" href="fonts/icon-font/css/style.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <noscript><link rel="stylesheet" href="fonts/icon-font/css/style.css"></noscript>
    <link rel="preload" href="fonts/fontawesome-5/css/all.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <noscript><link rel="stylesheet" href="fonts/fontawesome-5/css/all.css"></noscript>
    <!-- Plugin'stylesheets  -->
    <link rel="stylesheet" href="plugins/aos/aos.min.css">
    <link rel="stylesheet" href="plugins/fancybox/jquery.fancybox.min.css">
    <link rel="stylesheet" href="plugins/nice-select/nice-select.min.css">
    <link rel="stylesheet" href="plugins/slick/slick.min.css">
    <link rel="stylesheet" href="plugins/date-picker/css/gijgo.min.css" type="text/css" />
    <!-- Vendor stylesheets  -->
    <link rel="stylesheet" href="plugins/theme-mode-switcher/switcher-panel.css">
    <link rel="stylesheet" href="css/theme-mode-custom.css">
    <!-- Custom stylesheet -->

    <!-- Critical CSS for LCP optimization -->
    <style>
    /* Critical styles for above-the-fold content */
    .hero-img img { display: block; max-width: 100%; height: auto; }
    .gr-abs-tl { position: absolute; top: 0; left: 0; }
    .gr-z-index-n1 { z-index: -1; }
    .hero-content { padding: 2rem 0; }
    .title { font-size: 2.5rem; line-height: 1.2; margin-bottom: 1rem; }
    .container { max-width: 1200px; margin: 0 auto; padding: 0 15px; }
    .row { display: flex; flex-wrap: wrap; margin: 0 -15px; }
    .col-9, .col-md-7, .col-lg-5 { flex: 0 0 auto; padding: 0 15px; }
    @media (min-width: 768px) { .col-md-7 { width: 58.333333%; } }
    @media (min-width: 992px) { .col-lg-5 { width: 41.666667%; } }
    </style>

    <!-- CSS Loading Fallback Script -->
    <script>
    // Fallback for CSS preload
    !function(e){"use strict";var t=function(t,n,r){function o(e){return i.body?e():void setTimeout(function(){o(e)})}function a(){d.addEventListener&&d.removeEventListener("load",a),d.media=r||"all"}var i=e.document,d=i.createElement("link");if(n)d.href=n;else{var s=(i.body||i.getElementsByTagName("head")[0]).childNodes;d.href=s[s.length-1].href}var l=i.styleSheets;d.rel="stylesheet",d.media="only x",o(function(){i.head.appendChild(d)});var u=function(e){for(var t=d.href,n=l.length;n--;)if(l[n].href===t)return e();setTimeout(function(){u(e)})};return d.addEventListener&&d.addEventListener("load",a),d.onloadcssdefined=u,u(a),d};"undefined"!=typeof exports?exports.loadCSS=t:e.loadCSS=t}("undefined"!=typeof global?global:this);
    </script>
</head>

<body data-theme="light">
    <div class="site-wrapper overflow-hidden">
        <!-- Header Area -->
        <?php
    // header.php
    include('header-footer/newnavtest.php');
    // include('header-footer/header.php');
    ?>
        <!-- navbar-dark -->
        <!-- notification message-->
        <?php if (isset($_SESSION['success'])) : ?>
        <div>
            <h3>
                <?php
          echo $_SESSION['success'];
          unset($_SESSION['success']);
          ?>
            </h3>
        </div>
        <?php endif ?>
        <!-- Hero Area -->

        <style>
        .bg-default-2 {
            background: #473BF0 !important;
            height: 700px;
            /* Adjusted height to make it smaller */
        }

        .title.gr-text-6 {
            font-family: 'Circular Std', sans-serif;
            font-weight: 700;
            font-size: 21px;
            line-height: 44px;
            color: black;
        }

        /* Global Responsive Styles */
        @media (max-width: 1200px) {
            .container {
                max-width: 960px;
            }
        }

        @media (max-width: 992px) {
            .container {
                max-width: 720px;
            }
        }

        @media (max-width: 768px) {
            .container {
                max-width: 540px;
            }

            h1,
            h2 {
                font-size: 28px !important;
                line-height: 1.3 !important;
            }

            h3 {
                font-size: 22px !important;
            }

            p {
                font-size: 16px !important;
            }
        }

        @media (max-width: 576px) {
            .container {
                max-width: 100%;
                padding-left: 15px;
                padding-right: 15px;
            }

            h1,
            h2 {
                font-size: 24px !important;
                line-height: 1.3 !important;
            }
        }
        </style>



        <!-- <div class="position-relative bg-default-2 bg-pattern pattern-2 pt-27 pt-lg-32 pb-15 pb-lg-27">
      <div class="container">
        <div class="row justify-content-center align-items-center">
          <div class="col-9 col-md-7 col-lg-5 offset-xl-1 align-self-sm-end order-lg-2">
            <div class="hero-img position-relative" data-aos="fade-left" data-aos-duration="1100" data-aos-once="true">
              <picture>
                <source srcset="
                  image/wp/image.webp 500w,
                  image/wp/image-292x292.webp 292w,
                  image/wp/image-492x492.webp 492w
                  " type="image/webp" sizes="(max-width: 500px) 100vw, 500px">
                <img
                  fetchpriority="high"
                  decoding="sync"
                  width="525"
                  height="525"
                  src="image/wp/image.png"
                  class="attachment-full size-full wp-image-13629"
                  alt="HelloIT Professional IT Support Services - Expert Technical Assistance"
                  srcset="
                    image/wp/image.png 500w,
                    image/wp/image.png 292w,
                    image/wp/image.png 492w
                    "
                  sizes="(max-width: 500px) 100vw, 500px" />
              </picture>

              <div class="gr-abs-tl gr-z-index-n1" data-aos="zoom-in" data-aos-delay="600" data-aos-duration="800" data-aos-once="true">
                <picture>
                  <source srcset="image/l5/png/l5-dot-shape.webp" type="image/webp">
                  <img src="image/l5/png/l5-dot-shape.png" alt="Decorative blue dot pattern background" loading="lazy" decoding="async" width="200" height="200">
                </picture>
              </div>
            </div>
          </div>
          <div class="col-11 col-md-9 col-lg-7 col-xl-6 order-lg-1" data-aos="fade-right" data-aos-duration="500" data-aos-once="true">
            <div class="hero-content mt-11 mt-lg-0">
              <h1 class="title gr-text-2 mb-8" style="color: white; font-family: 'Circular Std', sans-serif; font-weight: 700;">A Better Tech Support Experience From Start To Finish.</h1>
              <p class="gr-text-8 mb-11 pr-md-12" style="color: #D7D5FC; font-family: 'Circular Std', sans-serif; font-weight: 400;">Get instant help from tech experts so you can focus on what is truly important.</p>
              <div class="hero-btn">
                <a href="#" class="btn gr-hover-y" style="background-color: #FFC84B; color: #000;">Let's Get Started</a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div> -->
        <!-- Feature section | section 1 -->
        <div class="feature-section pt-14 pt-lg-21 pb-7 bg-default-6 ">
            <div class="container">
                <div style="padding:0px 0px 40px 0px;" data-aos="fade-down" data-aos-duration="800" data-aos-once="true"
                    class="text-center">
                    <h2>The Benefits Of An Outsourced Helpdesk Support</h2>
                </div>
                <div class="row align-items-center justify-content-center">
                    <div class="col-lg-4 col-md-6 mb-11 mb-lg-19 px-xs-6 px-md-6 px-lg-0 px-xl-8" data-aos="fade-up"
                        data-aos-delay="400" data-aos-duration="800" data-aos-once="true">
                        <div class="feature-widget text-center">
                            <div class="widget-icon square-80 rounded-15 mx-auto mb-9 mb-lg-12 bg-blue shadow-blue" style="background-color: #007BFF !important;">
                                <picture>
                                    <source srcset="image/png/enterprise.webp" type="image/webp">
                                    <img src="image/png/enterprise.png" alt="Expertise Icon - Professional IT Support" class="img-fluid" width="50" height="50"
                                        style="width: 50px; height: 50px; filter: brightness(0) invert(1);">
                                </picture>
                            </div>
                            <div class="widget-text">
                                <h3 class="title gr-text-6 mb-7">Expertise</h3>
                                <p class="gr-text-11 mb-0">Highly skilled multi-disciplinary team with in depth IT
                                    knowledge to provide best-in-class support so you can focus on your business.</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-4 col-md-6 mb-11 mb-lg-19 px-xs-6 px-md-6 px-lg-0 px-xl-8" data-aos="fade-up"
                        data-aos-duration="800" data-aos-delay="600" data-aos-once="true">
                        <div class="feature-widget text-center">
                            <div class="widget-icon square-80 rounded-15 mx-auto mb-9 mb-lg-12 bg-red shadow-red" style="background-color: #007BFF !important;">
                                <picture>
                                    <source srcset="image/png/save-time.webp" type="image/webp">
                                    <img src="image/png/save-time.png" alt="Save Time Icon - Efficient IT Support" width="50" height="50"
                                        style="width: 50px; height: 50px; filter: brightness(0) invert(1);">
                                </picture>
                            </div>
                            <div class="widget-text">
                                <h3 class="title gr-text-6 mb-7">Saves Time</h3>
                                <p class="gr-text-11 mb-0">Responsive IT support with proven processes that helps you
                                    shave off time and frustrations when dealing with IT issues.</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-4 col-md-6 mb-11 mb-lg-19 px-xs-6 px-md-6 px-lg-0 px-xl-8" data-aos="fade-up"
                        data-aos-duration="800" data-aos-delay="800" data-aos-once="true">
                        <div class="feature-widget text-center">
                            <div class="widget-icon square-80 rounded-15 mx-auto mb-9 mb-lg-12 bg-green shadow-green" style="background-color: #007BFF !important;">
                                <picture>
                                    <source srcset="image/png/save-money.webp" type="image/webp">
                                    <img src="image/png/save-money.png" alt="Save Money Icon - Cost-Effective IT Support" width="50" height="50"
                                        style="width: 50px; height: 50px; filter: brightness(0) invert(1);">
                                </picture>
                            </div>
                            <div class="widget-text">
                                <h3 class="title gr-text-6 mb-7">Save Money</h3>
                                <p class="gr-text-11 mb-0">We have no fixed monthly fee - it’s a pay as you go pricing
                                    model! You only get charged once an issue is being resolved.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- Section 3 -->
        <!-- Helpdesk support youcan trust section-->
        <style>
        .custom-top-section {
            background-color: #f44336;
            /* Match the red background */
            padding-top: 80px;
            /* Adjust as needed */
            padding-bottom: 80px;
            /* Adjust as needed */
        }

        .custom-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1200px;
            /* Adjust as needed for your desired width */
            margin-left: auto;
            margin-right: auto;
            padding-left: 20px;
            padding-right: 20px;
        }

        .custom-col-50:first-child {
            width: 45%;
            /* Adjust width for the left column */
            position: relative;
            /* For absolute positioning of the image */
        }

        .custom-col-50:last-child {
            width: 50%;
            /* Adjust width for the right column */
        }

        /* Responsive styles for custom sections */
        @media (max-width: 992px) {
            .custom-container {
                max-width: 720px;
            }
        }

        @media (max-width: 768px) {
            .custom-container {
                flex-direction: column;
                max-width: 540px;
            }

            .custom-col-50:first-child,
            .custom-col-50:last-child {
                width: 100%;
                margin-bottom: 30px;
            }

            .custom-top-section {
                padding-top: 50px;
                padding-bottom: 50px;
            }
        }

        @media (max-width: 576px) {
            .custom-container {
                padding-left: 15px;
                padding-right: 15px;
            }
        }

        .custom-element-8671b6e {
            top: 0;
            left: 0;
            z-index: 1;
            /* Ensure it's behind the ticket list */
        }

        .custom-element-d992ec6 {
            position: absolute !important;
            /* Position it absolutely */
            top: 50%;
            /* Adjust to center vertically */
            left: 55%;
            /* Adjust to center horizontally */
            transform: translate(-50%, -50%);
            /* Center the element */
            z-index: 2;
        }

        .custom-element-d992ec6 img {
            border-radius: 20px;
        }


        .custom-heading-title {
            color: #ffffff;
            /* White heading text */
            font-family: "Circular Std", sans-serif;
            /* Use Circular Std font */
            font-weight: 700;
            /* Bold font weight */
            font-size: 36px;
            /* Adjust font size */
            line-height: 1.2;
            /* Adjust line height */
            margin-bottom: 20px;
            /* Adjust spacing */
        }

        .custom-widget-text-editor p {
            color: #ffffff;
            /* White paragraph text */
            font-family: sans-serif;
            /* Use a readable sans-serif font */
            font-size: 16px;
            /* Adjust font size */
            line-height: 1.6;
            /* Adjust line height for readability */
            margin-bottom: 15px;
            /* Adjust spacing between paragraphs */
        }

        .shadepro-btn-wrapper a.shadepro-btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-family: "Circular Std", sans-serif !important;
            /* Use Circular Std font */
            font-weight: 700 !important;
            /* Bold font weight */
            font-size: 21px !important;
            /* Adjust font size */
            color: #ffffff !important;
            /* White text color */
            background-color: transparent;
            /* No background color for the button itself */
            border: none;
            padding: 0;
            text-decoration: none;
        }

        .shadepro-btn-wrapper .icon-after {
            margin-left: 10px;
            /* Space between text and arrow */
        }

        .shadepro-btn-wrapper .icon-after i {
            font-size: 14px;
            /* Adjust arrow size */
        }
        </style>
        <section
            class="custom-section custom-top-section custom-element custom-element-ad556f2 custom-section-content-middle custom-section-boxed custom-section-height-default custom-section-height-default shadepro-adv-gradient-no shadepro-sticky-no"
            data-id="ad556f2" data-element_type="section"
            data-settings='{"background_background":"classic","shadepro_sticky":"no"}'>
            <div class="custom-container custom-column-gap-default">
                <div class="custom-column custom-col-50 custom-top-column custom-element custom-element-5a5200b"
                    data-id="5a5200b" data-element_type="column">
                    <div class="custom-widget-wrap custom-element-populated">
                        <!-- element icons 8671b6e -->
                        <div class="custom-element custom-element-8671b6e custom-absolute shadepro-sticky-no custom-widget custom-widget-image"
                            data-id="8671b6e" data-element_type="widget"
                            data-settings='{"_position":"absolute","shadepro_sticky":"no"}'
                            data-widget_type="image.default">
                            <div class="custom-widget-container " data-aos="fade-right" data-aos-duration="1100"
                                data-aos-once="true">
                                <img decoding="async" width="400" height="300" src="image/wp/digit-merk-shape-3.svg"
                                    class="attachment-full size-full wp-image-5686" alt="Digital technology background shape" />
                            </div>
                        </div>
                        <!-- ticket listing d992ec6-->
                        <div class="custom-element custom-element-d992ec6 custom-widget__width-auto shadepro-sticky-no custom-invisible custom-widget custom-widget-image"
                            data-id="d992ec6" data-element_type="widget"
                            data-settings='{"_animation":"pulse","shadepro_sticky":"no"}'
                            data-widget_type="image.default">
                            <div class="custom-widget-container" data-aos="fade-right" data-aos-duration="1100"
                                data-aos-once="true">
                                <picture>
                                  <source srcset="
                                    image/wp/Hello-IT-Tickets-zoom.webp 834w,
                                    image/wp/Hello-IT-Tickets-zoom-300x117.webp 300w,
                                    image/wp/Hello-IT-Tickets-zoom-768x300.webp 768w,
                                    image/wp/Hello-IT-Tickets-zoom-492x192.webp 492w
                                    " type="image/webp" sizes="(max-width: 834px) 100vw, 834px">
                                  <img loading="lazy" decoding="async" width="400" height="157"
                                      src="image/wp/Hello-IT-Tickets-zoom.png"
                                      class="attachment-full size-full wp-image-16333"
                                      alt="HelloIT ticket management system interface showing helpdesk support dashboard"
                                      srcset="
                                        image/wp/Hello-IT-Tickets-zoom.png 834w,
                                        image/wp/Hello-IT-Tickets-zoom.png 300w,
                                        image/wp/Hello-IT-Tickets-zoom.png 768w,
                                        image/wp/Hello-IT-Tickets-zoom.png 492w
                                        " sizes="(max-width: 400px) 100vw, 400px" />
                                </picture>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="custom-column custom-col-50 custom-top-column custom-element custom-element-bf9f5e8"
                    data-id="bf9f5e8" data-element_type="column">
                    <div class="custom-widget-wrap custom-element-populated">
                        <div class="custom-element custom-element-904a7a1 shadepro-sticky-no custom-widget custom-widget-heading"
                            data-id="904a7a1" data-element_type="widget" data-settings='{"shadepro_sticky":"no"}'
                            data-widget_type="heading.default">
                            <div class="custom-widget-container" data-aos="fade-left" data-aos-duration="1100"
                                data-aos-once="true">
                                <h2 class="custom-heading-title custom-size-default">
                                    Helpdesk Support You Can Trust
                                </h2>
                            </div>
                        </div>
                        <div class="custom-element custom-element-6fad3b0 shadepro-sticky-no custom-widget custom-widget-text-editor"
                            data-id="6fad3b0" data-element_type="widget" data-settings='{"shadepro_sticky":"no"}'
                            data-widget_type="text-editor.default">
                            <div class="custom-widget-container" data-aos="fade-left" data-aos-duration="1100"
                                data-aos-once="true">
                                <p
                                    style="color: rgba(255, 255, 255, 0.65); font-family: 'Circular Std', sans-serif; font-weight: 400;">
                                    We have been solving technical issues and providing IT
                                    helpdesk support to our customer for over 15 years.
                                    HelloIT provides you a reliable solutions for your
                                    business needs.
                                </p>
                                <p
                                    style="color: rgba(255, 255, 255, 0.65); font-family: 'Circular Std', sans-serif; font-weight: 400;">
                                    Our award-winning help desk services are delivered from
                                    North America, Asia and by virtual teams. No matter where
                                    you and your employees work, our teams are ready to
                                    support you.
                                </p>
                            </div>
                        </div>
                        <div class="custom-element custom-element-84671af shadepro-sticky-no custom-widget custom-widget-shadepro-btn"
                            data-id="84671af" data-element_type="widget" data-settings='{"shadepro_sticky":"no"}'
                            data-widget_type="shadepro-btn.default">
                            <div class="custom-widget-container">
                                <div class="shadepro-btn-wrapper enable-icon-box-no" data-aos="fade-left"
                                    data-aos-duration="1100" data-aos-once="true">
                                    <!-- Circular Std - 700 21px -->
                                    <a class="shadepro-btn btn-type-inline custom-animation-"
                                        href="front-end/buy-now.php"
                                        style="font-family: 'Circular Std', sans-serif; font-weight: 700; font-size: 21px;">
                                        Get Started Now
                                        <span class="icon-after btn-icon"><i aria-hidden="true"
                                                class="fas fa-arrow-right"></i></span>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
    </div>
    </section>

    <!-- section 4-->
    <style>
    /* photo */
    .pcHome {
        width: 600px !important;
        height: 400px !important;
        object-fit: cover;
        margin-left: 40px;
        border-radius: 20px !important;
        /* Ensures the image fits within the dimensions */
    }

    .content-section {
        display: flex;
        justify-content: center;
        align-items: center;
        background-color: #ffffff;
        /* White background color */
        padding: 80px 20px;
        /* Adjust padding as needed */
        height: 80vh;
        /* Full viewport height */
        box-sizing: border-box;
        /* Include padding in height */
    }

    .elementor-container {
        max-width: 1200px;
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .elementor-text-content {
        max-width: 50%;
        text-align: left;
        margin-left: 20px;
    }

    .elementor-heading-title {
        font-family: "Circular Std", sans-serif;
        font-weight: 700;
        font-size: 36px;
        color: #212529;
        /* Dark text color */
        margin-bottom: 20px;
    }

    .elementor-widget-container p {
        font-family: sans-serif;
        font-size: 16px;
        color: #6c757d;
        /* Gray text color */
        line-height: 1.6;
        margin-bottom: 15px;
    }

    .shadepro-btn-wrapper a.shadepro-btn {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        font-family: "Circular Std", sans-serif;
        font-weight: 700;
        font-size: 21px;
        color: #ffffff;
        background-color: #663399;
        /* Purple button background */
        padding: 10px 20px;
        border-radius: 5px;
        text-decoration: none;
        margin-top: 20px;
    }

    .shadepro-btn-wrapper a.shadepro-btn:hover {
        background-color: #5a2d85;
        /* Darker purple on hover */
    }

    .elementor-image-container {
        max-width: 50%;
        text-align: right;
    }

    .elementor-image-container img {
        max-width: 100%;
        height: auto;
    }

    /* Responsive styles for mobile */
    @media (max-width: 768px) {
        .elementor-container {
            flex-direction: column-reverse;
            /* Reverse order on mobile */
        }

        .elementor-text-content,
        .elementor-image-container2 {
            max-width: 100%;
            margin: 0 auto;
            text-align: center;
        }

        .elementor-image-container2 {
            margin-bottom: 30px;
            display: flex;
            justify-content: center;
        }

        /* Make pcHome image smaller on mobile */
        .pcHome {
            width: 390px !important;
            height: 340px !important;
            object-fit: contain;
            margin-left: 0 !important;
            border-radius: 20px !important;
        }

        /* Helpdesk support youcan trust section */
        .wp-image-16333 {
            width: 390px !important;
            height: 340px !important;
            object-fit: contain;
            margin-left: -40px !important;
            border-radius: 20px !important;
        }

        .elementor-heading-title {
            font-size: 28px;
            text-align: center;
        }

        .elementor-widget-container p {
            text-align: center;
        }

        .shadepro-btn-wrapper {
            text-align: center;
        }
    }
    </style>
    <section class="content-section">
        <div class="elementor-container">
            <div class="elementor-text-content" data-aos="fade-right" data-aos-duration="800" data-aos-once="true">
                <h2 class="elementor-heading-title">Flexible Support Options That Fits Your Needs</h2>
                <p>Whether it's occasional assistance or ongoing IT support, our flexible plans are designed to provide
                    the right level of support, exactly when you need it.</p>
                <div class="shadepro-btn-wrapper">
                    <a class="shadepro-btn" href="front-end/buy-now.php">
                        Compare Plans
                        <span class="icon-after btn-icon"><i aria-hidden="true" class="fas fa-arrow-right"></i></span>
                    </a>
                </div>
            </div>
            <div class="elementor-image-container2" data-aos="fade-up" data-aos-duration="800" data-aos-delay="600"
                data-aos-once="true">
                <picture>
                    <source srcset="image/wp/pc.webp" type="image/webp" sizes="(max-width: 884px) 100vw, 884px">
                    <img class="pcHome" loading="lazy" decoding="async" width="884" height="445" src="image/wp/pc.jpg"
                        alt="Professional IT support services - Computer and technology solutions by HelloIT"
                        sizes="(max-width: 884px) 100vw, 884px" />
                </picture>
            </div>
        </div>
    </section>


    <!-- Section 5 -->
    <style>
    .content-section2 {
        background-color: #68D585;
        /* Yellow background color */
    }

    .text-primary--light-only.gr-text-9 {
        font-family: 'Circular Std', sans-serif;
        font-weight: 700;
        /* Bold font */
        font-size: 29px;
        /* Font size */
    }

    .gr-text-7 {
        font-family: 'Circular Std', sans-serif;
        font-weight: 700;
        /* Bold font */
        font-size: 24px;
        color: #ffffff;
    }

    .gr-text-9 {
        font-family: 'Circular Std', sans-serif;
        font-weight: 400;
        font-size: 19px;
        color: #ffffff;
    }

    .count.circle-sm.gr-bg-blue-opacity-1.mr-8 {
        border: 2px solid #ffffff;
        /* ขอบสีขาว */
        background-color: transparent;
        /* พื้นหลังโปร่งใส */
        border-radius: 50%;
        /* ทำให้ขอบเป็นวงกลม */
        width: 50px;
        /* กำหนดความกว้าง */
        height: 50px;
        /* กำหนดความสูง */
        display: flex;
        align-items: center;
        justify-content: center;
    }
    </style>
    <style>
    .yellow-support-section {
        background-color: #FFDD3C;
        padding: 80px 0;
        position: relative;
        overflow: hidden;
    }

    .yellow-support-section::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: radial-gradient(circle at 30% 50%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
        pointer-events: none;
    }

    .support-image-container {
        position: relative;
        z-index: 2;
    }

    .support-image-container img {
        width: 100%;
        height: auto;
        max-width: 500px;
        border-radius: 15px;
        /*box-shadow: 0 30px 60px rgba(0, 0, 0, 0.15);*/
        transition: all 0.4s ease;
        transform: translateY(-15px);
        animation: float 6s ease-in-out infinite;
    }

    .support-image-container img:hover {
        transform: translateY(-25px);
        /*box-shadow: 0 40px 80px rgba(0, 0, 0, 0.2);*/
    }

    @keyframes float {

        0%,
        100% {
            transform: translateY(-15px);
        }

        50% {
            transform: translateY(-25px);
        }
    }

    .support-features {
        padding-left: 40px;
    }

    .feature-item {
        display: flex;
        align-items: flex-start;
        margin-bottom: 35px;
        opacity: 0;
        transform: translateX(30px);
        transition: all 0.6s ease;
    }

    .feature-item.aos-animate {
        opacity: 1;
        transform: translateX(0);
    }

    .feature-number {
        width: 50px;
        height: 50px;
        background: transparent;
        color: #333;
        border: 3px solid #333;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        font-size: 18px;
        margin-right: 20px;
        flex-shrink: 0;
        transition: all 0.3s ease;
    }

    .feature-number:hover {
        transform: scale(1.1);
        border-color: #000;
        color: #000;
    }

    .feature-content h3 {
        color: #333;
        font-size: 22px;
        font-weight: 700;
        margin-bottom: 8px;
        font-family: "Circular Std", sans-serif;
    }

    .feature-content p {
        color: #555;
        font-size: 16px;
        line-height: 1.6;
        margin: 0;
        font-family: "Circular Std", sans-serif;
    }

    @media (max-width: 991px) {
        .yellow-support-section {
            padding: 60px 0;
        }

        .support-features {
            padding-left: 0;
            margin-top: 40px;
        }

        .support-image-container {
            text-align: center;
        }

        .support-image-container img {
            max-width: 400px;
        }
    }

    @media (max-width: 767px) {
        .yellow-support-section {
            padding: 40px 0;
        }

        .feature-item {
            margin-bottom: 25px;
        }

        .feature-number {
            width: 40px;
            height: 40px;
            font-size: 16px;
            margin-right: 15px;
            border-width: 2px;
        }

        .feature-content h3 {
            font-size: 20px;
        }

        .feature-content p {
            font-size: 15px;
        }

        .support-image-container img {
            max-width: 300px;
        }
    }
    </style>

    <div class="yellow-support-section">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-6 col-md-6 mb-4 mb-lg-0" data-aos="fade-right" data-aos-duration="800"
                    data-aos-once="true">
                    <div class="support-image-container">
                        <img src="image/wp/job-man-loon.png" alt="Professional IT Support Expert" class="img-fluid" width="400" height="400">
                    </div>
                </div>
                <div class="col-lg-6 col-md-6">
                    <div class="support-features">
                        <div class="feature-item" data-aos="fade-up" data-aos-duration="800" data-aos-delay="200"
                            data-aos-once="true">
                            <div class="feature-number">1</div>
                            <div class="feature-content">
                                <h3>Get Instant Support</h3>
                                <p>Connect one-on-one with an expert and get your issue resolved in minutes.</p>
                            </div>
                        </div>

                        <div class="feature-item" data-aos="fade-up" data-aos-duration="800" data-aos-delay="400"
                            data-aos-once="true">
                            <div class="feature-number">2</div>
                            <div class="feature-content">
                                <h3>Guaranteed Response Time</h3>
                                <p>We do not charge ticket that are not responded within our advertised response time.
                                </p>
                            </div>
                        </div>

                        <div class="feature-item" data-aos="fade-up" data-aos-duration="800" data-aos-delay="600"
                            data-aos-once="true">
                            <div class="feature-number">3</div>
                            <div class="feature-content">
                                <h3>Secure Remote Support</h3>
                                <p>Secure communication channels are used to protect your data and privacy.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- section 6  -->
    <style>
    .content-section {
        padding-top: 60px;
        /* Adjust as needed */
        padding-bottom: 60px;
        /* Adjust as needed */
        background-color: #f9f9f9;
        /* Optional: Light gray background */
        min-height: 400px;
        /* Ensure minimum height */
    }

    .elementor-container {
        display: flex;
        justify-content: space-around;
        /* Space out the two testimonials */
        max-width: 1200px;
        /* Adjust as needed */
        margin-left: auto;
        margin-right: auto;
        padding-left: 20px;
        padding-right: 20px;
    }

    .elementor-heading-title6.elementor-size-default {
        font-family: 'Circular Std', sans-serif;
        font-weight: 700;
        font-size: 32px;
        line-height: 44px;
    }

    .elementor-column {
        width: 40%;
        /* Adjust width for each testimonial block */
        text-align: center;
        /* Center content within each block */
    }

    .elementor-widget-image {
        margin-bottom: 20px;
        /* Space between image and quote */
    }

    .elementor-widget-image img {
        border-radius: 50%;
        /* Make the images circular */
        max-width: 96px;
        /* Ensure consistent image size */
        height: auto;
    }

    .elementor-widget-container6 p {
        color: #6c757d;
        /* Gray for the testimonial text */
        font-family: sans-serif;
        /* Readable sans-serif font */
        font-size: 16px;
        /* Adjust text size */
        line-height: 32px;
        margin-bottom: 15px;
    }

    .elementor-widget-container6 .elementor-widget-heading h4.elementor-heading-title {
        color: #343a40;
        /* Darker gray for the name */
        font-family: "Circular Std", sans-serif;
        /* Use Circular Std font */
        font-weight: 700;
        font-size: 18px;
        margin-bottom: 5px;
    }

    .elementor-heading-title6-name .elementor-size-default {
        font-family: "Circular Std, sans-serif";
        font-weight: 700;
        font-size: 16px;
    }

    /* Hide the spacer element */
    .elementor-element-5f47279 {
        display: none;
    }

    /* Responsive styles for mobile */
    @media (max-width: 768px) {
        .content-section {
            padding-top: 40px;
            padding-bottom: 40px;
            min-height: 800px;
            /* Increased height for mobile */
        }

        .elementor-container {
            flex-direction: column;
            align-items: center;
        }

        .elementor-column {
            width: 90%;
            margin-bottom: 40px;
        }

        .elementor-heading-title6.elementor-size-default {
            font-size: 28px;
            line-height: 36px;
        }

        .elementor-widget-container6 p {
            font-size: 16px;
            line-height: 28px;
        }
    }
    </style>
    <section class="content-section" data-id="30fe555" data-element_type="section"
        data-settings="{&quot;stretch_section&quot;:&quot;section-stretched&quot;:&quot;no&quot;}">
        <div class="elementor-container elementor-column-gap-default" data-aos="zoom-in-up" data-aos-duration="1100"
            data-aos-once="true">
            <div class="elementor-column elementor-col-50 elementor-top-column elementor-element elementor-element-a04f350"
                data-id="a04f350" data-element_type="column">
                <div class="elementor-widget-wrap elementor-element-populated">

                    <div class="elementor-element elementor-element-5f47279 elementor-widget__width-initial elementor-absolute shadepro-sticky-no elementor-widget elementor-widget-spacer"
                        data-id="5f47279" data-element_type="widget"
                        data-settings="{&quot;_position&quot;:&quot;absolute&quot;,&quot;shadepro_sticky&quot;:&quot;no&quot;}"
                        data-widget_type="spacer.default">
                        <div class="elementor-widget-container">
                            <div class="elementor-spacer">
                                <div class="elementor-spacer-inner"></div>
                            </div>
                        </div>
                    </div>

                    <div class="elementor-element elementor-element-92f0eed shadepro-sticky-no elementor-widget elementor-widget-image"
                        data-id="92f0eed" data-element_type="widget"
                        data-settings="{&quot;shadepro_sticky&quot;:&quot;no&quot;}" data-widget_type="image.default">
                        <div class="elementor-widget-container">
                            <img loading="lazy" decoding="async" width="96" height="96" src="image/wp/doctor-5.png"
                                class=" attachment-large size-large wp-image-4121" alt="customer pic">
                        </div>
                    </div>

                    <div class="elementor-element elementor-element-a45390d shadepro-sticky-no elementor-widget elementor-widget-heading"
                        data-id="a45390d" data-element_type="widget"
                        data-settings="{&quot;shadepro_sticky&quot;:&quot;no&quot;}" data-widget_type="heading.default">
                        <div class="elementor-widget-container">
                            <h2 class="elementor-heading-title6 elementor-size-default">“Support Made Easy"</h2>
                        </div>
                    </div>

                    <div class="elementor-element elementor-element-ca25b7f shadepro-sticky-no elementor-widget"
                        data-id="ca25b7f" data-element_type="widget"
                        data-settings="{&quot;shadepro_sticky&quot;:&quot;no&quot;}"
                        data-widget_type="text-editor.default">
                        <div class="elementor-widget-container6">
                            <p>HelloIT fast support has helped us improve staff productivity and reduce IT downtime.
                                Highly recommend them.</p>
                        </div>
                    </div>

                    <div class="elementor-element elementor-element-984643e shadepro-sticky-no elementor-widget elementor-widget-heading"
                        data-id="984643e" data-element_type="widget"
                        data-settings="{&quot;shadepro_sticky&quot;:&quot;no&quot;}" data-widget_type="heading.default">
                        <div class="elementor-widget-container">
                            <h3 class="elementor-heading-title6-name elementor-size-default">Corey Valdez</h3>
                        </div>
                    </div>

                    <div class="elementor-element elementor-element-1cf5d49 shadepro-sticky-no elementor-widget"
                        data-id="1cf5d49" data-element_type="widget"
                        data-settings="{&quot;shadepro_sticky&quot;:&quot;no&quot;}"
                        data-widget_type="text-editor.default">
                        <div class="elementor-widget-container">
                            <p>IT Director at Pickabox</p>
                        </div>
                    </div>

                </div>
            </div>

            <div class="elementor-column elementor-col-50 elementor-top-column elementor-element elementor-element-818b7e0"
                data-id="818b7e0" data-element_type="column">
                <div class="elementor-widget-wrap elementor-element-populated">

                    <div class="elementor-element elementor-element-2d66eb6 shadepro-sticky-no elementor-widget elementor-widget-image"
                        data-id="2d66eb6" data-element_type="widget"
                        data-settings="{&quot;shadepro_sticky&quot;:&quot;no&quot;}" data-widget_type="image.default">
                        <div class="elementor-widget-container">
                            <img loading="lazy" decoding="async" width="96" height="96" src="image/wp/doctor-10.png"
                                class="attachment-large size-large wp-image-4125" alt="customer pic">
                        </div>
                    </div>

                    <div class="elementor-element elementor-element-48f6bfc shadepro-sticky-no elementor-widget elementor-widget-heading"
                        data-id="48f6bfc" data-element_type="widget"
                        data-settings="{&quot;shadepro_sticky&quot;:&quot;no&quot;}" data-widget_type="heading.default">
                        <div class="elementor-widget-container">
                            <h2 class="elementor-heading-title6 elementor-size-default">“Fast and Responsive"</h2>
                        </div>
                    </div>

                    <div class="elementor-element elementor-element-3ac01eb shadepro-sticky-no elementor-widget elementor-widget-text-editor"
                        data-id="3ac01eb" data-element_type="widget"
                        data-settings="{&quot;shadepro_sticky&quot;:&quot;no&quot;}"
                        data-widget_type="text-editor.default">
                        <div class="elementor-widget-container">
                            <p>We are very impressed by their professionalism and the proactive&nbsp; services they
                                provide.</p>
                        </div>
                    </div>

                    <div class="elementor-element elementor-element-cc9dc77 shadepro-sticky-no elementor-widget elementor-widget-heading"
                        data-id="cc9dc77" data-element_type="widget"
                        data-settings="{&quot;shadepro_sticky&quot;:&quot;no&quot;}" data-widget_type="heading.default">
                        <div class="elementor-widget-container">
                            <h3 class="elementor-heading-title6-name elementor-size-default">Kathy Bohen</h3>
                        </div>
                    </div>

                    <div class="elementor-element elementor-element-c7c8ca6 shadepro-sticky-no elementor-widget elementor-widget-text-editor"
                        data-id="c7c8ca6" data-element_type="widget"
                        data-settings="{&quot;shadepro_sticky&quot;:&quot;no&quot;}"
                        data-widget_type="text-editor.default">
                        <div class="elementor-widget-container">
                            <p>Operations Manager at Curtin &amp; Son</p>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </section>

    <!-- Section 7 -->
    <style>
    .cta-section {
        padding-top: 200px;
        /* Increased top padding */
        padding-bottom: 200px;
        /* Increased bottom padding */
        background-color: #663399;
        height: auto;
        /* Background color remains the same */
        background-image: url('image/wp/digit-merk-svg-7.svg'), url('image/wp/digit-merk-svg-8.svg');
        background-repeat: no-repeat, no-repeat;
        background-size: auto 100%, auto 100%;
        /* Keeps the background photos the same size */
        background-position: left center, right center;
    }

    /* Responsive styles for mobile */
    @media (max-width: 768px) {
        .cta-section {
            padding-top: 100px;
            padding-bottom: 100px;
            background-image: none !important;
            /* Remove background images on mobile */
            background-color: #663399;
            /* Keep only the background color */
        }

        .gr-text-2.mb-7 {
            font-size: 36px;
            line-height: 1.3;
        }

        .gr-text-6 {
            font-size: 16px;
            line-height: 1.5;
        }

        .col-md-9 {
            width: 90%;
        }
    }

    .container {
        max-width: 1200px;
        margin-left: auto;
        margin-right: auto;
        padding-left: 20px;
        padding-right: 20px;
    }

    .row {
        display: flex;
        justify-content: center;
    }

    .col-md-9 {
        width: 75%;
    }

    .text-center {
        text-align: center;
        color: #ffffff;
    }

    .gr-text-2.mb-7 {
        font-size: 60px;
        font-weight: 700;
        line-height: 65px;
        margin-bottom: 30px;
        font-family: "Circular Std", sans-serif;
        font-weight: 700;
    }

    .gr-text-6 {
        font-size: 19px;
        font-weight: 400;
        line-height: 32px;
        margin-bottom: 40px;
        font-family: "Circular Std", sans-serif;
        color: #f0f0f0;
    }

    .btn-green {
        display: inline-block;
        background-color: #28a745;
        color: #ffffff;
        font-family: "Circular Std", sans-serif;
        font-weight: 700;
        font-size: 18px;
        padding: 15px 30px;
        border-radius: 5px;
        text-decoration: none;
        transition: background-color 0.3s ease;
    }

    .btn-green:hover {
        background-color: #218838;
    }
    </style>

    <div class="cta-section bg-pattern pattern-7">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-md-9" data-aos="fade-up" data-aos-duration="800" data-aos-once="true">
                    <div class="text-center dark-mode-texts">
                        <h2 class="gr-text-2 mb-7">Don't let IT issues from hindering <br> your work!</h2>
                        <p class="gr-text-6 mb-11 px-xl-7 gr-text-color">Join in with hundreds of other businesses that
                            uses HelloIT technical support services to solve their IT issues and make their business
                            more efficient than before.</p>
                        <a href="front-end/buy-now.php" class="btn btn-green text-white gr-hover-y px-lg-9">Get started
                            today!</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Footer section -->
    <?php
  include('header-footer/footer.php');
  ?>
    </div>
    <!-- Vendor Scripts -->
    <script src="js/vendor.min.js"></script>
    <!-- Plugin's Scripts -->
    <script src="plugins/fancybox/jquery.fancybox.min.js"></script>
    <script src="plugins/nice-select/jquery.nice-select.min.js"></script>
    <script src="plugins/aos/aos.min.js"></script>
    <script src="plugins/slick/slick.min.js"></script>
    <script src="plugins/date-picker/js/gijgo.min.js"></script>
    <script src="plugins/counter-up/jquery.waypoints.min.js"></script>
    <script src="plugins/counter-up/jquery.counterup.min.js"></script>
    <script src="plugins/theme-mode-switcher/gr-theme-mode-switcher.js"></script>
    <!-- Activation Script -->
    <script src="js/custom.js"></script>
</body>

</html>