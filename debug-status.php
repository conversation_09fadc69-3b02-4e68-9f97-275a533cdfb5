<?php
// Debug the actual API status
include('functions/appika-status-checker.php');

echo "<h2>🔍 Real-time API Status Debug</h2>";
echo "<p><strong>Current time:</strong> " . date('Y-m-d H:i:s') . "</p>";
echo "<p><em>This page tests the automatic server detection that triggers maintenance pages.</em></p>";

// Test the actual API status
$status = checkAppikaStatus();
echo "<h3>Detailed API Status:</h3>";
echo "<pre>";
print_r($status);
echo "</pre>";

// Test if system thinks Appika is online
$isOnline = isAppikaOnline();
echo "<h3>System Decision:</h3>";
echo "<p><strong>Is Appika Online?</strong> " . ($isOnline ? "<span style='color: green;'>YES</span>" : "<span style='color: red;'>NO</span>") . "</p>";

// Test status message
$message = getAppikaStatusMessage();
echo "<p><strong>Status Message:</strong> $message</p>";

// Test what maintenance check would do
echo "<h3>Maintenance Check Simulation:</h3>";
if (!$isOnline) {
    echo "<p style='color: red; font-weight: bold;'>🚨 MAINTENANCE PAGE SHOULD SHOW</p>";
    echo "<p><strong>When you visit my-ticket.php, it should automatically redirect to server-down.php</strong></p>";
} else {
    echo "<p style='color: green; font-weight: bold;'>✅ NO MAINTENANCE NEEDED</p>";
    echo "<p>my-ticket.php should load normally</p>";
}

// Test individual API endpoints manually
echo "<h3>Manual API Tests:</h3>";
$endpoints = [
    'Ticket API' => 'https://dev-sgsg-tktapi.appika.com/graphql',
    'Customer API' => 'https://dev-api-pooh-sgsg.appika.com/contact/customers'
];

foreach ($endpoints as $name => $url) {
    echo "<p><strong>$name:</strong> ";
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 5);
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 3);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_NOBODY, true); // HEAD request only
    
    $result = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($error) {
        echo "<span style='color: red;'>ERROR: $error</span>";
    } else {
        echo "<span style='color: " . ($httpCode >= 200 && $httpCode < 400 ? 'green' : 'red') . ";'>HTTP $httpCode</span>";
    }
    echo "</p>";
}

echo "<hr>";
echo "<h3>📋 Test Links:</h3>";
echo "<ul>";
echo "<li><a href='front-end/my-ticket.php' target='_blank'>My Tickets Page</a> (should redirect to maintenance if APIs are down)</li>";
echo "<li><a href='front-end/profile.php' target='_blank'>Profile Page</a> (should show maintenance warning and disable edit button if APIs are down)</li>";
echo "<li><a href='front-end/payment-methods.php' target='_blank'>Payment Methods</a> (should redirect to maintenance if APIs are down)</li>";
echo "<li><a href='front-end/purchase-history.php' target='_blank'>Purchase History</a> (should redirect to maintenance if APIs are down)</li>";
echo "<li><a href='front-end/my-ratings.php' target='_blank'>My Ratings</a> (should redirect to maintenance if APIs are down)</li>";
echo "<li><a href='front-end/create-ticket.php' target='_blank'>Create Ticket</a> (should redirect to maintenance if APIs are down)</li>";
echo "<li><a href='front-end/server-down.php' target='_blank'>Maintenance Page</a> (direct access)</li>";
echo "</ul>";
?>
