<?php
/**
 * Setup Company Name and Tax ID Fields
 * This script ensures company_name and tax_id columns exist in the user table
 */

require_once 'functions/server.php';

echo "<h2>Setting up Company Name and Tax ID Fields</h2>\n";

// Check if company_name column exists
echo "<h3>Checking company_name column...</h3>\n";
$check_company_name = "SHOW COLUMNS FROM user LIKE 'company_name'";
$result_company = mysqli_query($conn, $check_company_name);

if (mysqli_num_rows($result_company) == 0) {
    echo "<p>Adding company_name column to user table...</p>\n";
    $add_company_name = "ALTER TABLE user ADD COLUMN company_name VARCHAR(255) NULL AFTER tell";
    
    if (mysqli_query($conn, $add_company_name)) {
        echo "<p style='color: green;'>✓ company_name column added successfully!</p>\n";
    } else {
        echo "<p style='color: red;'>✗ Error adding company_name column: " . mysqli_error($conn) . "</p>\n";
    }
} else {
    echo "<p style='color: blue;'>ℹ company_name column already exists in user table.</p>\n";
}

// Check if tax_id column exists
echo "<h3>Checking tax_id column...</h3>\n";
$check_tax_id = "SHOW COLUMNS FROM user LIKE 'tax_id'";
$result_tax = mysqli_query($conn, $check_tax_id);

if (mysqli_num_rows($result_tax) == 0) {
    echo "<p>Adding tax_id column to user table...</p>\n";
    $add_tax_id = "ALTER TABLE user ADD COLUMN tax_id VARCHAR(100) NULL AFTER company_name";
    
    if (mysqli_query($conn, $add_tax_id)) {
        echo "<p style='color: green;'>✓ tax_id column added successfully!</p>\n";
    } else {
        echo "<p style='color: red;'>✗ Error adding tax_id column: " . mysqli_error($conn) . "</p>\n";
    }
} else {
    echo "<p style='color: blue;'>ℹ tax_id column already exists in user table.</p>\n";
}

echo "<h3>Setup Complete!</h3>\n";
echo "<p>Both company_name and tax_id fields are now available for:</p>\n";
echo "<ul>\n";
echo "<li>✓ Profile editing (front-end/profile.php)</li>\n";
echo "<li>✓ Admin user creation (merlion/admin-users.php)</li>\n";
echo "<li>✓ Database storage (local database only)</li>\n";
echo "</ul>\n";

mysqli_close($conn);
?>
