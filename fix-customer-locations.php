<?php
/**
 * Fix Customer Locations Script
 * This script fixes customers who have data in Location 2 instead of Location 1
 * by moving the data from Location 2 to Location 1 and removing Location 2
 */

require_once 'functions/server.php';
require_once 'config/api-config.php';
require_once 'vendor/autoload.php';

// Get API configuration
$apiConfig = getCustomerApiConfig();
$apiEndpoint = $apiConfig['endpoint'];
$apiPath = $apiConfig['path'];
$apiKey = $apiConfig['key'];

// Create Guzzle client
$client = new \GuzzleHttp\Client([
    'base_uri' => $apiEndpoint,
    'timeout' => 30,
    'http_errors' => false,
]);

// Get all users with Appika customer IDs
$query = "SELECT id, appika_customer_id, appika_id, first_name FROM user WHERE appika_customer_id IS NOT NULL AND appika_customer_id != ''";
$result = mysqli_query($conn, $query);

$totalProcessed = 0;
$totalFixed = 0;
$errors = [];

echo "<h2>Customer Location Fix Script</h2>\n";
echo "<p>Starting to process customers...</p>\n";

while ($user = mysqli_fetch_assoc($result)) {
    $totalProcessed++;
    $userId = $user['id'];
    $appikaCustomerId = $user['appika_customer_id'];
    $customerName = $user['first_name'];
    
    echo "<p>Processing User ID: $userId, Appika Customer ID: $appikaCustomerId, Name: $customerName</p>\n";
    
    try {
        // Get customer locations
        $locationsResponse = $client->request('GET', $apiPath . '/' . $appikaCustomerId . '/locations', [
            'headers' => [
                'X-api-key' => "{$apiKey}",
                'Accept' => 'application/json',
            ]
        ]);
        
        if ($locationsResponse->getStatusCode() >= 200 && $locationsResponse->getStatusCode() < 300) {
            $locationsData = json_decode($locationsResponse->getBody()->getContents(), true);
            $locations = $locationsData['items'] ?? [];
            
            if (count($locations) >= 2) {
                $location1 = $locations[0];
                $location2 = $locations[1];
                
                // Check if Location 1 is empty and Location 2 has data
                $location1HasData = !empty($location1['add1']) || !empty($location1['city']) || !empty($location1['tel_work']);
                $location2HasData = !empty($location2['add1']) || !empty($location2['city']) || !empty($location2['tel_work']);
                
                if (!$location1HasData && $location2HasData) {
                    echo "<p style='color: orange;'>  → Found customer with empty Location 1 and data in Location 2. Fixing...</p>\n";
                    
                    // Update Location 1 with data from Location 2
                    $updateData = [
                        'loc_code' => $location2['loc_code'],
                        'loc_name' => $location2['loc_name'],
                        'add1' => $location2['add1'],
                        'add2' => $location2['add2'],
                        'ccode' => $location2['ccode'],
                        'state_code' => $location2['state_code'],
                        'city' => $location2['city'],
                        'status' => 'a',
                        'is_primary_loc' => 'y',
                        'zip' => $location2['zip'],
                        'parent_id' => 0,
                        'tel_work' => $location2['tel_work'],
                        'contact_pax' => $location2['contact_pax']
                    ];
                    
                    // Update Location 1
                    $updateResponse = $client->request('PUT', $apiPath . '/' . $appikaCustomerId . '/locations/' . $location1['id'], [
                        'headers' => [
                            'X-api-key' => "{$apiKey}",
                            'Accept' => 'application/json',
                            'Content-Type' => 'application/json',
                        ],
                        'json' => $updateData
                    ]);
                    
                    if ($updateResponse->getStatusCode() >= 200 && $updateResponse->getStatusCode() < 300) {
                        echo "<p style='color: green;'>  → Successfully updated Location 1 with data</p>\n";
                        
                        // Delete Location 2
                        $deleteResponse = $client->request('DELETE', $apiPath . '/' . $appikaCustomerId . '/locations/' . $location2['id'], [
                            'headers' => [
                                'X-api-key' => "{$apiKey}",
                                'Accept' => 'application/json',
                            ]
                        ]);
                        
                        if ($deleteResponse->getStatusCode() >= 200 && $deleteResponse->getStatusCode() < 300) {
                            echo "<p style='color: green;'>  → Successfully deleted Location 2</p>\n";
                            $totalFixed++;
                        } else {
                            echo "<p style='color: red;'>  → Failed to delete Location 2</p>\n";
                            $errors[] = "User $userId: Failed to delete Location 2";
                        }
                    } else {
                        echo "<p style='color: red;'>  → Failed to update Location 1</p>\n";
                        $errors[] = "User $userId: Failed to update Location 1";
                    }
                } else {
                    echo "<p style='color: blue;'>  → Customer locations are already correct</p>\n";
                }
            } else {
                echo "<p style='color: gray;'>  → Customer has less than 2 locations, skipping</p>\n";
            }
        } else {
            echo "<p style='color: red;'>  → Failed to get customer locations</p>\n";
            $errors[] = "User $userId: Failed to get locations";
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>  → Exception: " . $e->getMessage() . "</p>\n";
        $errors[] = "User $userId: Exception - " . $e->getMessage();
    }
    
    // Add a small delay to avoid overwhelming the API
    usleep(500000); // 0.5 seconds
}

echo "<h3>Summary</h3>\n";
echo "<p>Total customers processed: $totalProcessed</p>\n";
echo "<p>Total customers fixed: $totalFixed</p>\n";

if (!empty($errors)) {
    echo "<h4>Errors:</h4>\n";
    echo "<ul>\n";
    foreach ($errors as $error) {
        echo "<li>$error</li>\n";
    }
    echo "</ul>\n";
}

echo "<p><strong>Script completed!</strong></p>\n";
?>
