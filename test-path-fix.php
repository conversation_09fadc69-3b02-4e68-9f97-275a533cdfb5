<?php
/**
 * Test Path Fix
 * Verify that the path detection fix works correctly
 */

echo "<h1>Test Path Fix</h1>";
echo "<p>Testing if the path detection fix resolves the Guzzle issue...</p>";

// Test 1: Environment Detection
echo "<h2>1. Environment Detection</h2>";
$is_localhost = (strpos($_SERVER['HTTP_HOST'], 'localhost') !== false || strpos($_SERVER['HTTP_HOST'], '127.0.0.1') !== false);
$file_base_path = $is_localhost ? '/helloit' : '';

echo "Server Host: " . $_SERVER['HTTP_HOST'] . "<br>";
echo "Is Localhost: " . ($is_localhost ? 'Yes' : 'No') . "<br>";
echo "File Base Path: '" . $file_base_path . "'<br>";
echo "Document Root: " . $_SERVER['DOCUMENT_ROOT'] . "<br>";

// Test 2: Path Construction
echo "<h2>2. Path Construction</h2>";
$autoload_path = $_SERVER['DOCUMENT_ROOT'] . $file_base_path . '/vendor/autoload.php';
$server_path = $_SERVER['DOCUMENT_ROOT'] . $file_base_path . '/functions/server.php';
$config_path = $_SERVER['DOCUMENT_ROOT'] . $file_base_path . '/config/api-config.php';

echo "Autoload Path: " . $autoload_path . "<br>";
echo "Server Path: " . $server_path . "<br>";
echo "Config Path: " . $config_path . "<br>";

// Test 3: File Existence Check
echo "<h2>3. File Existence Check</h2>";
$files_to_check = [
    'vendor/autoload.php' => $autoload_path,
    'functions/server.php' => $server_path,
    'config/api-config.php' => $config_path
];

foreach ($files_to_check as $name => $path) {
    if (file_exists($path)) {
        echo "✅ $name exists at: $path<br>";
    } else {
        echo "❌ $name NOT found at: $path<br>";
    }
}

// Test 4: Include Files Test
echo "<h2>4. Include Files Test</h2>";
try {
    // Include autoloader
    require_once $autoload_path;
    echo "✅ Autoloader included successfully<br>";
    
    // Include server.php
    include($server_path);
    echo "✅ Server.php included successfully<br>";
    
    // Include config
    require_once $config_path;
    echo "✅ Config included successfully<br>";
    
} catch (Exception $e) {
    echo "❌ Error including files: " . $e->getMessage() . "<br>";
}

// Test 5: Guzzle Test (Same as Working Files)
echo "<h2>5. Guzzle Test (Same as Working Files)</h2>";
if (class_exists('GuzzleHttp\Client')) {
    echo "✅ GuzzleHttp\Client class exists<br>";
    
    try {
        // Create client exactly like working front-end files
        $client = new \GuzzleHttp\Client([
            'timeout' => 8,
            'connect_timeout' => 5,
            'http_errors' => false,
        ]);
        echo "✅ Guzzle client created successfully<br>";
        
        // Test API config
        if (function_exists('getCustomerApiConfig')) {
            $apiConfig = getCustomerApiConfig();
            echo "✅ API config loaded: " . $apiConfig['endpoint'] . "<br>";
            
            // Test simple request
            $response = $client->request('GET', $apiConfig['endpoint'] . $apiConfig['path'], [
                'headers' => [
                    'X-api-key' => $apiConfig['key'],
                    'Accept' => 'application/json',
                ],
                'query' => ['limit' => 1],
                'timeout' => 5
            ]);
            
            echo "✅ Test API request completed<br>";
            echo "Status: " . $response->getStatusCode() . "<br>";
            
        } else {
            echo "⚠️ getCustomerApiConfig function not available<br>";
        }
        
    } catch (Exception $e) {
        echo "❌ Error testing Guzzle: " . $e->getMessage() . "<br>";
    }
} else {
    echo "❌ GuzzleHttp\Client class does not exist<br>";
}

// Test 6: Compare Old vs New Paths
echo "<h2>6. Path Comparison</h2>";
echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th>File</th><th>Old Path (Relative)</th><th>New Path (Absolute)</th><th>Status</th></tr>";

$comparisons = [
    'vendor/autoload.php' => ['../vendor/autoload.php', $autoload_path],
    'functions/server.php' => ['../functions/server.php', $server_path],
    'config/api-config.php' => ['../config/api-config.php', $config_path]
];

foreach ($comparisons as $file => $paths) {
    $old_exists = file_exists($paths[0]);
    $new_exists = file_exists($paths[1]);
    
    $status = '';
    if (!$old_exists && $new_exists) {
        $status = '✅ Fixed!';
    } elseif ($old_exists && $new_exists) {
        $status = '✅ Both work';
    } elseif (!$old_exists && !$new_exists) {
        $status = '❌ Both fail';
    } else {
        $status = '⚠️ Old works, new fails';
    }
    
    echo "<tr>";
    echo "<td>$file</td>";
    echo "<td>" . $paths[0] . " (" . ($old_exists ? 'exists' : 'missing') . ")</td>";
    echo "<td>" . $paths[1] . " (" . ($new_exists ? 'exists' : 'missing') . ")</td>";
    echo "<td>$status</td>";
    echo "</tr>";
}
echo "</table>";

echo "<h2>Summary</h2>";
echo "<p>This test verifies that using absolute paths with environment detection (like working front-end files) fixes the Guzzle issue.</p>";

echo "<h2>Next Steps</h2>";
echo "<p>If this test shows ✅ results:</p>";
echo "<ol>";
echo "<li>The path fix should resolve the Guzzle issue</li>";
echo "<li>Test admin-users.php user creation</li>";
echo "<li>Test debug-appika-detailed.php</li>";
echo "<li>Apply the same fix to other admin files if needed</li>";
echo "</ol>";
?>
