<?php
/**
 * Test API Authentication
 * Compare working GraphQL vs failing Customer API authentication
 */

// Auto-detect environment for file paths
$is_localhost = (strpos($_SERVER['HTTP_HOST'], 'localhost') !== false || strpos($_SERVER['HTTP_HOST'], '127.0.0.1') !== false);
$file_base_path = $is_localhost ? '/helloit' : '';

require_once $_SERVER['DOCUMENT_ROOT'] . $file_base_path . '/vendor/autoload.php';
require_once $_SERVER['DOCUMENT_ROOT'] . $file_base_path . '/config/api-config.php';
require_once $_SERVER['DOCUMENT_ROOT'] . $file_base_path . '/functions/graphql_functions.php';

echo "<h1>API Authentication Test</h1>";
echo "<p>Comparing working GraphQL vs failing Customer API authentication...</p>";

// Test 1: GraphQL API (Working)
echo "<h2>1. GraphQL API Test (Working)</h2>";
try {
    $graphqlConfig = getGraphqlApiConfig();
    echo "GraphQL Endpoint: " . $graphqlConfig['endpoint'] . "<br>";
    echo "GraphQL Key: " . substr($graphqlConfig['key'], 0, 10) . "...<br>";
    
    // Test with a simple GraphQL query
    $query = '
    query {
        getTickets {
            id
            subject
        }
    }';
    
    $result = makeGraphQLRequest($query, []);
    
    if ($result['success']) {
        echo "✅ GraphQL API authentication successful<br>";
        echo "Status: " . $result['status'] . "<br>";
        echo "Data: " . (isset($result['data']['data']['getTickets']) ? count($result['data']['data']['getTickets']) . " tickets" : "No tickets") . "<br>";
    } else {
        echo "❌ GraphQL API failed: " . $result['error'] . "<br>";
        echo "Status: " . $result['status'] . "<br>";
    }
    
} catch (Exception $e) {
    echo "❌ GraphQL test error: " . $e->getMessage() . "<br>";
}

// Test 2: Customer API (Failing)
echo "<h2>2. Customer API Test (Failing)</h2>";
try {
    $customerConfig = getCustomerApiConfig();
    echo "Customer Endpoint: " . $customerConfig['endpoint'] . "<br>";
    echo "Customer Path: " . $customerConfig['path'] . "<br>";
    echo "Customer Key: " . substr($customerConfig['key'], 0, 10) . "...<br>";
    
    $client = new \GuzzleHttp\Client([
        'base_uri' => $customerConfig['endpoint'],
        'timeout' => 8,
        'http_errors' => false,
    ]);
    
    // Test with same headers as debug script
    $response = $client->request('GET', $customerConfig['path'], [
        'headers' => [
            'X-api-key' => "{$customerConfig['key']}",
            'Accept' => 'application/json',
            'Content-Type' => 'application/json',
        ]
    ]);
    
    $statusCode = $response->getStatusCode();
    $body = $response->getBody()->getContents();
    
    echo "Status Code: " . $statusCode . "<br>";
    echo "Response: " . htmlspecialchars(substr($body, 0, 200)) . "<br>";
    
    if ($statusCode == 200) {
        echo "✅ Customer API authentication successful<br>";
    } else {
        echo "❌ Customer API authentication failed<br>";
    }
    
} catch (Exception $e) {
    echo "❌ Customer API test error: " . $e->getMessage() . "<br>";
}

// Test 3: Try Different Authentication Methods
echo "<h2>3. Try Different Authentication Methods</h2>";

$customerConfig = getCustomerApiConfig();
$client = new \GuzzleHttp\Client([
    'base_uri' => $customerConfig['endpoint'],
    'timeout' => 8,
    'http_errors' => false,
]);

$auth_methods = [
    'X-api-key only' => [
        'X-api-key' => $customerConfig['key'],
        'Accept' => 'application/json'
    ],
    'X-api-key with Content-Type' => [
        'X-api-key' => $customerConfig['key'],
        'Accept' => 'application/json',
        'Content-Type' => 'application/json'
    ],
    'Bearer token' => [
        'Authorization' => 'Bearer ' . $customerConfig['key'],
        'Accept' => 'application/json'
    ],
    'X-api-key with quotes' => [
        'X-api-key' => '"' . $customerConfig['key'] . '"',
        'Accept' => 'application/json'
    ],
    'X-API-KEY (uppercase)' => [
        'X-API-KEY' => $customerConfig['key'],
        'Accept' => 'application/json'
    ]
];

foreach ($auth_methods as $method_name => $headers) {
    try {
        $response = $client->request('GET', $customerConfig['path'], [
            'headers' => $headers,
            'query' => ['limit' => 1]
        ]);
        
        $statusCode = $response->getStatusCode();
        $body = $response->getBody()->getContents();
        
        echo "<strong>$method_name:</strong> ";
        if ($statusCode == 200) {
            echo "✅ Success (200)<br>";
        } elseif ($statusCode == 401) {
            echo "❌ Unauthorized (401)<br>";
        } else {
            echo "⚠️ Status $statusCode<br>";
        }
        
        // Show response for debugging
        if ($statusCode != 200) {
            echo "&nbsp;&nbsp;Response: " . htmlspecialchars(substr($body, 0, 100)) . "<br>";
        }
        
    } catch (Exception $e) {
        echo "<strong>$method_name:</strong> ❌ Error: " . $e->getMessage() . "<br>";
    }
}

// Test 4: Check API Key Format
echo "<h2>4. API Key Analysis</h2>";
$key = $customerConfig['key'];
echo "Key Length: " . strlen($key) . " characters<br>";
echo "Key Format: " . (ctype_alnum($key) ? "Alphanumeric" : "Contains special characters") . "<br>";
echo "Key Preview: " . substr($key, 0, 10) . "..." . substr($key, -10) . "<br>";

// Test 5: Compare with Working Core API Example
echo "<h2>5. Check Core API Example</h2>";
if (file_exists($_SERVER['DOCUMENT_ROOT'] . $file_base_path . '/api/core_api_example.php')) {
    echo "✅ Core API example file exists<br>";
    echo "<a href='/api/core_api_example.php' target='_blank'>Test Core API Example</a><br>";
} else {
    echo "❌ Core API example file not found<br>";
}

echo "<h2>Recommendations</h2>";
echo "<ol>";
echo "<li>If GraphQL works but Customer API doesn't, the Customer API might require different authentication</li>";
echo "<li>Check if the Customer API key is different from GraphQL API key</li>";
echo "<li>Try the Core API example to see if it works</li>";
echo "<li>Contact Appika support to verify Customer API authentication requirements</li>";
echo "</ol>";

echo "<h2>Summary</h2>";
echo "<p><strong>Good news:</strong> Guzzle is now working perfectly! ✅</p>";
echo "<p><strong>Issue:</strong> Customer API authentication (401 error)</p>";
echo "<p><strong>Next step:</strong> Verify Customer API key or authentication method</p>";
?>
