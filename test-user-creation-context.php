<?php
/**
 * Test User Creation Context
 * Test the exact same context as admin-users.php user creation
 */

echo "<h1>Test User Creation Context</h1>";
echo "<p>Testing the exact same context as admin-users.php user creation...</p>";

// Test 1: Simulate admin-users.php context
echo "<h2>1. Simulate Admin-Users Context</h2>";
try {
    // Include files in the same order as admin-users.php
    include('functions/server.php');
    echo "✅ server.php included<br>";
    
    require_once 'vendor/autoload.php';
    echo "✅ vendor/autoload.php included<br>";
    
    require_once 'config/api-config.php';
    echo "✅ config/api-config.php included<br>";
    
} catch (Exception $e) {
    echo "❌ Error including files: " . $e->getMessage() . "<br>";
    exit;
}

// Test 2: Test Guzzle in admin context
echo "<h2>2. Test Guzzle in Admin Context</h2>";
if (class_exists('GuzzleHttp\Client')) {
    echo "✅ GuzzleHttp\Client class exists<br>";
    
    try {
        // Test the exact same way as admin-users.php sendToAppikaAPI function
        $apiConfig = getCustomerApiConfig();
        $apiEndpoint = $apiConfig['endpoint'];
        $apiPath = $apiConfig['path'];
        $apiKey = $apiConfig['key'];
        
        echo "✅ API config loaded successfully<br>";
        
        // Create a Guzzle HTTP client (exact same as admin-users.php)
        $client = new \GuzzleHttp\Client([
            'base_uri' => $apiEndpoint,
            'timeout' => 30,
            'http_errors' => false, // Don't throw exceptions for 4xx/5xx responses
        ]);
        echo "✅ Guzzle client created (admin-users.php style)<br>";
        
        // Test a simple request
        $response = $client->request('GET', $apiPath, [
            'headers' => [
                'X-api-key' => "{$apiKey}",
                'Accept' => 'application/json',
                'Content-Type' => 'application/json',
            ],
            'query' => [
                'limit' => 1
            ]
        ]);
        
        echo "✅ Test request completed<br>";
        echo "Status: " . $response->getStatusCode() . "<br>";
        
    } catch (Exception $e) {
        echo "❌ Error in admin context: " . $e->getMessage() . "<br>";
    }
} else {
    echo "❌ GuzzleHttp\Client class does not exist in admin context<br>";
}

// Test 3: Test createCustomerMinimal function
echo "<h2>3. Test createCustomerMinimal Function</h2>";
try {
    require_once 'functions/create-customer-minimal.php';
    echo "✅ create-customer-minimal.php included<br>";
    
    if (function_exists('createCustomerMinimal')) {
        echo "✅ createCustomerMinimal function exists<br>";
        
        // Test with dummy data (don't actually create)
        $testData = [
            'username' => 'test_user_' . time(),
            'email' => '<EMAIL>',
            'password' => 'testpass123',
            'full_name' => 'Test User',
            'address' => 'Test Address',
            'address2' => '',
            'city' => 'Test City',
            'state' => 'Test State',
            'country' => 'TH',
            'postal_code' => '12345',
            'phone' => '1234567890',
            'company_name' => 'Test Company',
            'tax_id' => '',
            'district' => '',
            'timezone' => 'Asia/Bangkok',
            'stripe_customer_id' => null
        ];
        
        echo "✅ Test data prepared<br>";
        echo "📝 Test data: " . json_encode($testData, JSON_PRETTY_PRINT) . "<br>";
        
        // Don't actually run the function to avoid creating test users
        echo "⚠️ Skipping actual function call to avoid creating test users<br>";
        
    } else {
        echo "❌ createCustomerMinimal function does not exist<br>";
    }
    
} catch (Exception $e) {
    echo "❌ Error testing createCustomerMinimal: " . $e->getMessage() . "<br>";
}

// Test 4: Test createCustomerInAppika function directly
echo "<h2>4. Test createCustomerInAppika Function</h2>";
if (function_exists('createCustomerInAppika')) {
    echo "✅ createCustomerInAppika function exists<br>";
    
    // Test if the function can be called without errors (dry run)
    echo "⚠️ Function exists but skipping actual call to avoid creating test data<br>";
    
} else {
    echo "❌ createCustomerInAppika function does not exist<br>";
}

// Test 5: Check error logs for user creation issues
echo "<h2>5. Check Error Logs</h2>";
$log_files = [
    'logs/appika_debug.log',
    'logs/appika_api.log',
    'logs/error.log',
    'logs/php_errors.log'
];

foreach ($log_files as $log_file) {
    if (file_exists($log_file)) {
        echo "📄 Found $log_file<br>";
        $lines = file($log_file);
        $recent_lines = array_slice($lines, -5); // Last 5 lines
        
        echo "<details><summary>Recent entries from $log_file</summary>";
        echo "<pre style='background: #f5f5f5; padding: 10px; max-height: 150px; overflow-y: auto;'>";
        foreach ($recent_lines as $line) {
            echo htmlspecialchars($line);
        }
        echo "</pre></details>";
    } else {
        echo "❌ $log_file not found<br>";
    }
}

// Test 6: Compare working vs non-working contexts
echo "<h2>6. Context Comparison</h2>";
echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th>Aspect</th><th>GraphQL (Working)</th><th>User Creation (Failing?)</th></tr>";

echo "<tr><td>Autoloader Path</td>";
echo "<td>__DIR__ . '/../vendor/autoload.php'</td>";
echo "<td>vendor/autoload.php (relative)</td></tr>";

echo "<tr><td>API Endpoint</td>";
echo "<td>GraphQL: " . (defined('GRAPHQL_API_ENDPOINT') ? GRAPHQL_API_ENDPOINT : 'Not defined') . "</td>";
echo "<td>Customer: " . (defined('CUSTOMER_API_ENDPOINT') ? CUSTOMER_API_ENDPOINT : 'Not defined') . "</td></tr>";

echo "<tr><td>Timeout</td>";
echo "<td>8 seconds</td>";
echo "<td>30 seconds</td></tr>";

echo "<tr><td>Error Handling</td>";
echo "<td>Comprehensive try-catch</td>";
echo "<td>Basic error handling</td></tr>";

echo "</table>";

echo "<h2>Recommendations</h2>";
echo "<p>Based on this test:</p>";
echo "<ol>";
echo "<li>If Guzzle works here, the issue might be in the specific admin-users.php execution context</li>";
echo "<li>Check if there are any PHP errors in the logs during user creation</li>";
echo "<li>The Customer API might be different from GraphQL API in terms of availability</li>";
echo "<li>Try using the same error handling pattern as GraphQL functions</li>";
echo "</ol>";

echo "<h2>Next Steps</h2>";
echo "<p>If this test shows Guzzle working:</p>";
echo "<ul>";
echo "<li>The issue is likely in the specific execution context of admin-users.php</li>";
echo "<li>Check the server error logs during actual user creation attempts</li>";
echo "<li>Test the Customer API endpoint specifically</li>";
echo "<li>Consider using the same timeout and error handling as GraphQL</li>";
echo "</ul>";
?>
