// This script generates a notification sound and saves it as an MP3 file
// Run this in a browser console to generate the sound

function generateNotificationSound() {
  // Create audio context
  const audioContext = new (window.AudioContext || window.webkitAudioContext)();
  const duration = 0.5; // Duration in seconds

  // Create oscillator
  const oscillator = audioContext.createOscillator();
  oscillator.type = "sine";
  oscillator.frequency.setValueAtTime(880, audioContext.currentTime); // A5
  oscillator.frequency.exponentialRampToValueAtTime(
    1320,
    audioContext.currentTime + 0.1
  ); // E6

  // Create gain node for volume control
  const gainNode = audioContext.createGain();
  gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
  gainNode.gain.exponentialRampToValueAtTime(
    0.01,
    audioContext.currentTime + duration
  );

  // Connect nodes
  oscillator.connect(gainNode);
  gainNode.connect(audioContext.destination);

  // Start and stop
  oscillator.start();
  oscillator.stop(audioContext.currentTime + duration);

  console.log(
    "Notification sound played. Use browser recording tools to capture this sound."
  );
}

// Call the function to generate the sound
generateNotificationSound();
