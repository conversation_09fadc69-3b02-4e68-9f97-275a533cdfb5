<?php
/**
 * Core API POST & PUT Example - Using Guzzle to connect to the API
 *
 * This example demonstrates how to use POST and PUT methods with the API endpoint:
 * https://dev-api-pooh-sgsg.appika.com/contact/customers
 * using the Core API (REST) approach with Guzzle HTTP client.
 */

// Include Composer autoloader
require_once __DIR__ . '/../vendor/autoload.php';

// Load centralized API configuration
require_once __DIR__ . '/../config/api-config.php';

// Start session to persist data across requests
session_start();

// Get API configuration
$apiConfig = getCustomerApiConfig();
$apiEndpoint = $apiConfig['endpoint'];
$apiPath = $apiConfig['path'];

// Store API key in session for persistence
$tokenMessage = '';
$tokenMessageType = '';
if (isset($_POST['api_token']) && !empty($_POST['api_token'])) {
    $_SESSION['apiKey'] = $_POST['api_token'];
    // Validate token by making a simple API call
    $testApiKey = $_POST['api_token'];
    $testClient = new \GuzzleHttp\Client([
        'base_uri' => $apiEndpoint,
        'timeout' => 10,
        'http_errors' => false,
    ]);
    try {
        $testResponse = $testClient->request('GET', $apiPath, [
            'headers' => [
                'X-api-key' => "{$testApiKey}",
                'Accept' => 'application/json',
            ],
            'query' => ['limit' => 1]
        ]);
        $testStatus = $testResponse->getStatusCode();
        if ($testStatus == 401 || $testStatus == 403) {
            $tokenMessage = 'Token expired or incorrect. Please check your token.';
            $tokenMessageType = 'danger';
            unset($_SESSION['apiKey']);
        } elseif ($testStatus >= 200 && $testStatus < 300) {
            $tokenMessage = 'Token updated successfully.';
            $tokenMessageType = 'success';
        } else {
            $tokenMessage = 'Token validation failed (HTTP ' . $testStatus . ').';
            $tokenMessageType = 'warning';
        }
    } catch (\Exception $e) {
        $tokenMessage = 'Token validation error: ' . $e->getMessage();
        $tokenMessageType = 'danger';
        unset($_SESSION['apiKey']);
    }
}
if (isset($_SESSION['apiKey']) && !empty($_SESSION['apiKey'])) {
    $apiKey = $_SESSION['apiKey'];
} else {
    // Use centralized API key as default
    $apiKey = $apiConfig['key'];
}

// Create a Guzzle HTTP client
$client = new \GuzzleHttp\Client([
    'base_uri' => $apiEndpoint,
    'timeout' => 30,
    'http_errors' => false, // Don't throw exceptions for 4xx/5xx responses
]);

// Function to display results in a readable format
function displayResults($title, $data) {
    echo "<h2>{$title}</h2>";
    echo "<pre>";
    print_r($data);
    echo "</pre>";
}

// Function to handle API requests and display results
function makeApiRequest($client, $method, $endpoint, $options = [], $title = '') {
    try {
        // Display request URL and headers for debugging
        echo "<h3>Request URL (Debug):</h3>";
        echo "<pre>{$method} {$endpoint}</pre>";

        if (isset($options['headers'])) {
            echo "<h3>Request Headers (Debug):</h3>";
            echo "<pre>";
            print_r($options['headers']);
            echo "</pre>";
        }

        if (isset($options['json'])) {
            echo "<h3>Request Data (Debug):</h3>";
            echo "<pre>";
            print_r($options['json']);
            echo "</pre>";
        }

        // Send the request
        $response = $client->request($method, $endpoint, $options);

        // Get status code
        $statusCode = $response->getStatusCode();
        echo "<p>Status Code: {$statusCode}</p>";

        // Get response body
        $body = $response->getBody()->getContents();

        // Parse JSON if the response is JSON
        if (strpos($response->getHeaderLine('Content-Type'), 'application/json') !== false) {
            $data = json_decode($body, true);
            displayResults($title . ' Response (JSON):', $data);
        } else {
            displayResults($title . ' Response:', $body);
        }

        return [
            'status' => $statusCode,
            'data' => $data ?? $body
        ];
    } catch (\Exception $e) {
        echo "<h2>Error Occurred</h2>";
        echo "<p>Error Message: " . $e->getMessage() . "</p>";
        return [
            'status' => 500,
            'error' => $e->getMessage()
        ];
    }
}

// Process form submission
$message = '';
$messageType = '';
$customerId = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        $action = $_POST['action'];

        // Common headers for all requests
        $headers = [
            'X-api-key' => "{$apiKey}",
            'Accept' => 'application/json',
            'Content-Type' => 'application/json',
        ];

        switch ($action) {
            case 'create':
                // Create a new customer
                $customerNo = !empty($_POST['customerNo']) ? $_POST['customerNo'] : 'CUST' . rand(1000, 9999);
                $customerName = !empty($_POST['customerName']) ? $_POST['customerName'] : 'New Customer';
                $customerEntityType = !empty($_POST['customerEntityType']) ? $_POST['customerEntityType'] : 'COMPANY';
                $customerGroup = !empty($_POST['customerGroup']) ? $_POST['customerGroup'] : '10'; // Use numeric group id
                $customerStartDate = !empty($_POST['customerStartDate']) ? $_POST['customerStartDate'] : date('Y-m-d');
                $status = !empty($_POST['status']) ? $_POST['status'] : 'a';

                // Add required fields: grp_id, ofc_id, assign2, creator
                $ofcId = '511';
                $assignTo = '1';
                $creator = '1';

                $customerData = [
                    'no' => $customerNo,
                    'name' => $customerName,
                    'entity_type' => $customerEntityType === 'COMPANY' ? '1' : '2',
                    'grp_id' => $customerGroup, // Use grp_id and set to a valid numeric group id
                    'ofc_id' => $ofcId,
                    'assign2' => $assignTo,
                    'creator' => $creator,
                    'start_date' => $customerStartDate,
                    'status' => $status
                ];

                $result = makeApiRequest(
                    $client,
                    'POST',
                    $apiPath,
                    [
                        'headers' => $headers,
                        'json' => $customerData
                    ],
                    'Create Customer'
                );

                if ($result['status'] >= 200 && $result['status'] < 300) {
                    $message = 'Customer created successfully!';
                    $messageType = 'success';
                    $customerId = $result['data']['id'] ?? '';
                } else {
                    $message = 'Failed to create customer. ' . ($result['error'] ?? '');
                    $messageType = 'danger';
                }
                break;

            case 'update':
                // Update an existing customer
                if (empty($_POST['customerId'])) {
                    $message = 'Customer ID is required for update.';
                    $messageType = 'danger';
                    break;
                }

                $customerId = $_POST['customerId'];
                $customerName = !empty($_POST['customerName']) ? $_POST['customerName'] : 'Updated Customer';
                $customerEntityType = !empty($_POST['customerEntityType']) ? $_POST['customerEntityType'] : 'COMPANY';
                $customerGroup = !empty($_POST['customerGroup']) ? $_POST['customerGroup'] : '10';
                $customerStartDate = !empty($_POST['customerStartDate']) ? $_POST['customerStartDate'] : date('Y-m-d');
                $status = !empty($_POST['status']) ? $_POST['status'] : 'a';

                // Add required fields: grp_id, ofc_id, assign2, creator (like Create)
                $ofcId = '511';
                $assignTo = '1';
                $creator = '1';

                // Get the current customer data
                echo "<h3>Getting current customer data for update...</h3>";
                $getResult = makeApiRequest(
                    $client,
                    'GET',
                    $apiPath,
                    [
                        'headers' => [
                            'X-api-key' => "{$apiKey}",
                            'Accept' => 'application/json',
                        ],
                        'query' => [
                            'no' => $customerId
                        ]
                    ],
                    'Get Customer for Update'
                );

                if ($getResult['status'] >= 200 && $getResult['status'] < 300 &&
                    isset($getResult['data']['items']) && !empty($getResult['data']['items'])) {

                    $currentCustomer = $getResult['data']['items'][0];
                    echo "<p>Current customer data retrieved successfully.</p>";

                    // Use the same structure as Create (POST)
                    $customerData = [
                        'no' => $currentCustomer['no'],
                        'name' => $customerName,
                        'entity_type' => $customerEntityType === 'COMPANY' ? '1' : '2',
                        'grp_id' => $customerGroup,
                        'ofc_id' => $ofcId,
                        'assign2' => $assignTo,
                        'creator' => $creator,
                        'start_date' => $customerStartDate,
                        'status' => $status
                    ];

                    echo "<h3>Update data:</h3>";
                    echo "<pre>";
                    print_r($customerData);
                    echo "</pre>";

                } else {
                    $message = 'Failed to get current customer data for update.';
                    $messageType = 'danger';
                    break;
                }

                $customerId = $currentCustomer['id'];

                echo "<h3>Update URL:</h3>";
                echo "<pre>{$apiPath}/{$customerId}</pre>";

                $result = makeApiRequest(
                    $client,
                    'PUT',
                    $apiPath . '/' . $customerId,
                    [
                        'headers' => $headers,
                        'json' => $customerData
                    ],
                    'Update Customer'
                );

                if ($result['status'] >= 200 && $result['status'] < 300) {
                    $message = 'Customer updated successfully!';
                    $messageType = 'success';
                } else {
                    $message = 'Failed to update customer. ' . ($result['error'] ?? '');
                    $messageType = 'danger';
                }
                break;



            case 'search_for_update':
                // Search for a customer to update
                if (empty($_POST['customerId'])) {
                    $message = 'Customer ID is required to search.';
                    $messageType = 'danger';
                    break;
                }

                $customerId = $_POST['customerId'];

                // Display the customer ID being searched
                echo "<h3>Searching for Customer to Update:</h3>";
                echo "<pre>{$customerId}</pre>";

                $result = makeApiRequest(
                    $client,
                    'GET',
                    $apiPath,
                    [
                        'headers' => [
                            'X-api-key' => "{$apiKey}",
                            'Accept' => 'application/json',
                        ],
                        'query' => [
                            // Search specifically by the 'no' field (customer number)
                            'no' => $customerId
                        ]
                    ],
                    'Search Customer for Update'
                );

                if ($result['status'] >= 200 && $result['status'] < 300) {
                    // Check if we got any results
                    if (isset($result['data']['items']) && !empty($result['data']['items'])) {
                        $message = 'Customer found! You can now update the details.';
                        $messageType = 'success';

                        // Store the customer data for the update form
                        $customerForUpdate = $result['data']['items'][0];

                        // Use actual data if available, fallback to empty/defaults
                        $primary = isset($customerForUpdate['primary_location']) && is_array($customerForUpdate['primary_location'])
                            ? $customerForUpdate['primary_location']
                            : [];

                        $customerForUpdate['primary_location'] = [
                            'id' => $primary['id'] ?? ($customerForUpdate['id'] ?? ''),
                            'status' => $primary['status'] ?? ($customerForUpdate['status'] ?? 'a'),
                            'is_leaf' => $primary['is_leaf'] ?? 'y',
                            'loc_code' => $primary['loc_code'] ?? ('CUST-' . ($customerForUpdate['id'] ?? rand(100, 999))),
                            'loc_name' => $primary['loc_name'] ?? (($customerForUpdate['name'] ?? 'Location') . ' Location'),
                            'is_primary_loc' => $primary['is_primary_loc'] ?? 'y',
                            'add1' => $primary['add1'] ?? '',
                            'add2' => $primary['add2'] ?? '',
                            'state_name' => $primary['state_name'] ?? 'Bangkok',
                            'city' => $primary['city'] ?? 'กรุงเทพมหานคร',
                            'zip' => $primary['zip'] ?? '10230',
                            'ccode' => $primary['ccode'] ?? 'TH',
                            'contact_pax' => $primary['contact_pax'] ?? '',
                            'email' => $primary['email'] ?? '',
                            'tel_work' => $primary['tel_work'] ?? '',
                            'tel_alt' => $primary['tel_alt'] ?? '',
                            'fax' => $primary['fax'] ?? '',
                        ];

                        // Debug the customer data
                        echo "<h3>Customer Data for Update Form:</h3>";
                        echo "<pre>";
                        print_r($customerForUpdate);
                        echo "</pre>";

                        // Redirect to the update tab
                        echo '<script>
                            document.addEventListener("DOMContentLoaded", function() {
                                document.getElementById("update-tab").click();
                            });
                        </script>';
                    } else {
                        $message = 'No customer found with the specified customer number.';
                        $messageType = 'warning';
                    }
                } else {
                    $message = 'Failed to search for customer. ' . ($result['error'] ?? '');
                    $messageType = 'danger';
                }
                break;

            case 'get':
                // Get a specific customer
                if (empty($_POST['customerId'])) {
                    $message = 'Customer ID is required to get details.';
                    $messageType = 'danger';
                    break;
                }

                $customerId = $_POST['customerId'];

                // Display the customer ID being searched
                echo "<h3>Searching for Customer ID:</h3>";
                echo "<pre>{$customerId}</pre>";

                $result = makeApiRequest(
                    $client,
                    'GET',
                    $apiPath,
                    [
                        'headers' => [
                            'X-api-key' => "{$apiKey}",
                            'Accept' => 'application/json',
                        ],
                        'query' => [
                            // Search specifically by the 'no' field (customer number)
                            'no' => $customerId
                        ]
                    ],
                    'Get Customer'
                );

                if ($result['status'] >= 200 && $result['status'] < 300) {
                    // Check if we got any results
                    if (isset($result['data']['items']) && !empty($result['data']['items'])) {
                        $message = 'Customer details retrieved successfully!';
                        $messageType = 'success';

                        // Fetch and display addresses for this customer
                        $customerDbId = $result['data']['items'][0]['id'];
                        $addressResult = makeApiRequest(
                            $client,
                            'GET',
                            $apiPath . '/' . $customerDbId . '/locations',
                            [
                                'headers' => [
                                    'X-api-key' => "{$apiKey}",
                                    'Accept' => 'application/json',
                                ]
                            ],
                            'Get Customer Address'
                        );
                        // Merge address data into customer data for display
                        if (isset($addressResult['data']['items'])) {
                            $result['data']['items'][0]['addresses'] = $addressResult['data']['items'];
                        }
                        // Show merged data in the response section
                        displayResults('Get Customer Response (JSON):', $result['data']['items'][0]);
                        return;
                    } else {
                        $message = 'No customer found with the specified customer number.';
                        $messageType = 'warning';
                    }
                } else {
                    $message = 'Failed to get customer details. ' . ($result['error'] ?? '');
                    $messageType = 'danger';
                }
                break;

            case 'search_for_address':
                // Search for a customer to add address
                if (empty($_POST['customerId'])) {
                    $message = 'Customer ID is required to search.';
                    $messageType = 'danger';
                    break;
                }

                $customerId = $_POST['customerId'];

                echo "<h3>Searching for Customer to Add Address:</h3>";
                echo "<pre>{$customerId}</pre>";

                $result = makeApiRequest(
                    $client,
                    'GET',
                    $apiPath,
                    [
                        'headers' => [
                            'X-api-key' => "{$apiKey}",
                            'Accept' => 'application/json',
                        ],
                        'query' => [
                            'no' => $customerId
                        ]
                    ],
                    'Search Customer for Address'
                );

                if ($result['status'] >= 200 && $result['status'] < 300) {
                    if (isset($result['data']['items']) && !empty($result['data']['items'])) {
                        $message = 'Customer found! You can now add an address.';
                        $messageType = 'success';
                        $customerForAddress = $result['data']['items'][0];
                        // Store in session for next request
                        $_SESSION['customerForAddress'] = $customerForAddress;
                        echo '<script>
                            document.addEventListener("DOMContentLoaded", function() {
                                document.getElementById("add-address-tab").click();
                            });
                        </script>';
                    } else {
                        $message = 'No customer found with the specified customer number.';
                        $messageType = 'warning';
                        unset($_SESSION['customerForAddress']);
                    }
                } else {
                    $message = 'Failed to search for customer. ' . ($result['error'] ?? '');
                    $messageType = 'danger';
                    unset($_SESSION['customerForAddress']);
                }
                break;

            case 'add_address':
                // Add address to selected customer
                // Always resolve numeric customerDbId from the latest customer search result
                $customerId = $_POST['customerId'] ?? '';
                $customerDbId = '';

                // Try to get numeric ID from session (from last search)
                if (isset($_SESSION['customerForAddress']['id']) && is_numeric($_SESSION['customerForAddress']['id'])) {
                    $customerDbId = $_SESSION['customerForAddress']['id'];
                } else {
                    // Fallback: search again by customer number to get numeric ID
                    $findResult = makeApiRequest(
                        $client,
                        'GET',
                        $apiPath,
                        [
                            'headers' => [
                                'X-api-key' => "{$apiKey}",
                                'Accept' => 'application/json',
                            ],
                            'query' => [
                                'no' => $customerId
                            ]
                        ],
                        'Find Customer by Number'
                    );
                    if (
                        $findResult['status'] === 200 &&
                        isset($findResult['data']['items'][0]['id'])
                    ) {
                        $customerDbId = $findResult['data']['items'][0]['id'];
                        // Update session for consistency
                        $_SESSION['customerForAddress'] = $findResult['data']['items'][0];
                    } else {
                        $message = 'Could not resolve numeric customer ID from customer number.';
                        $messageType = 'danger';
                        break;
                    }
                }

                // Debug: show which ID is being used and its type
                echo "<h3>POST Address to: {$apiPath}/{$customerDbId}/locations</h3>";
                echo "<pre>customerDbId: {$customerDbId} (type: " . gettype($customerDbId) . ")</pre>";

                $addressData = [
                    'loc_code' => $_POST['loc_code'] ?? '',
                    'loc_name' => $_POST['loc_name'] ?? '',
                    'add1' => $_POST['add1'] ?? '',
                    'ccode' => $_POST['ccode'] ?? '',
                    'state_code' => $_POST['state_code'] ?? '',
                    'city' => $_POST['city'] ?? '',
                    'status' => $_POST['status'] ?? 'a',
                    'is_primary_loc' => 'n',
                    'zip' => $_POST['zip'] ?? '',
                    'parent_id'=> 0,
                ];

                $result = makeApiRequest(
                    $client,
                    'POST',
                    $apiPath . '/' . $customerDbId . '/locations',
                    [
                        'headers' => [
                            'X-api-key' => "{$apiKey}",
                            'Accept' => 'application/json',
                            'Content-Type' => 'application/json',
                        ],
                        'json' => $addressData
                    ],
                    'Add Address'
                );

                // After successful add, clear session
                if ($result['status'] >= 200 && $result['status'] < 300) {
                    $message = 'Address added successfully!';
                    $messageType = 'success';
                    unset($_SESSION['customerForAddress']);
                } else {
                    $message = 'Failed to add address. ' . ($result['error'] ?? '');
                    $messageType = 'danger';
                }
                break;

            case 'search_for_update_location':
                // Search for a customer and list locations for update
                if (empty($_POST['customerId'])) {
                    $message = 'Customer ID is required to search.';
                    $messageType = 'danger';
                    break;
                }
                $customerId = $_POST['customerId'];
                $result = makeApiRequest(
                    $client,
                    'GET',
                    $apiPath,
                    [
                        'headers' => [
                            'X-api-key' => "{$apiKey}",
                            'Accept' => 'application/json',
                        ],
                        'query' => [
                            'no' => $customerId
                        ]
                    ],
                    'Search Customer for Update Location'
                );
                if ($result['status'] >= 200 && $result['status'] < 300) {
                    if (isset($result['data']['items']) && !empty($result['data']['items'])) {
                        $customerForUpdateLocation = $result['data']['items'][0];
                        $_SESSION['customerForUpdateLocation'] = $customerForUpdateLocation;
                        // Get locations for this customer
                        $customerDbId = $customerForUpdateLocation['id'];
                        $locResult = makeApiRequest(
                            $client,
                            'GET',
                            $apiPath . '/' . $customerDbId . '/locations',
                            [
                                'headers' => [
                                    'X-api-key' => "{$apiKey}",
                                    'Accept' => 'application/json',
                                ]
                            ],
                            'Get Locations for Customer'
                        );
                        if ($locResult['status'] >= 200 && $locResult['status'] < 300 && isset($locResult['data']['items'])) {
                            $_SESSION['locationsForUpdate'] = $locResult['data']['items'];
                            $message = 'Customer and locations found! Select a location to update.';
                            $messageType = 'success';
                        } else {
                            $message = 'No locations found for this customer.';
                            $messageType = 'warning';
                            unset($_SESSION['locationsForUpdate']);
                        }
                        // Switch to update-location tab
                        echo '<script>
                            document.addEventListener("DOMContentLoaded", function() {
                                document.getElementById("update-location-tab").click();
                            });
                        </script>';
                    } else {
                        $message = 'No customer found with the specified customer number.';
                        $messageType = 'warning';
                        unset($_SESSION['customerForUpdateLocation'], $_SESSION['locationsForUpdate']);
                    }
                } else {
                    $message = 'Failed to search for customer. ' . ($result['error'] ?? '');
                    $messageType = 'danger';
                    unset($_SESSION['customerForUpdateLocation'], $_SESSION['locationsForUpdate']);
                }
                break;

            case 'select_location_for_update':
                // Store selected location in session for update form
                if (!empty($_POST['locationId']) && isset($_SESSION['locationsForUpdate'])) {
                    foreach ($_SESSION['locationsForUpdate'] as $loc) {
                        if ($loc['id'] == $_POST['locationId']) {
                            $_SESSION['locationForUpdate'] = $loc;
                            break;
                        }
                    }
                    // Switch to update-location tab
                    echo '<script>
                        document.addEventListener("DOMContentLoaded", function() {
                            document.getElementById("update-location-tab").click();
                        });
                    </script>';
                }
                break;

            case 'update_address':
                // Update address for selected location
                $customerDbId = $_POST['customerDbId'] ?? '';
                $locationId = $_POST['locationId'] ?? '';
                if (!$customerDbId || !$locationId) {
                    $message = 'Customer and Location ID are required.';
                    $messageType = 'danger';
                    break;
                }
                $addressData = [
                    'loc_code' => $_POST['loc_code'] ?? '',
                    'loc_name' => $_POST['loc_name'] ?? '',
                    'add1' => $_POST['add1'] ?? '',
                    'ccode' => $_POST['ccode'] ?? '',
                    'state_code' => $_POST['state_code'] ?? '',
                    'city' => $_POST['city'] ?? '',
                    'status' => $_POST['status'] ?? 'a',
                    'is_primary_loc' => $_POST['is_primary_loc'] ?? 'n',
                    'zip' => $_POST['zip'] ?? '',
                    'parent_id'=> 0,
                ];
                $result = makeApiRequest(
                    $client,
                    'PUT',
                    $apiPath . '/' . $customerDbId . '/locations/' . $locationId,
                    [
                        'headers' => [
                            'X-api-key' => "{$apiKey}",
                            'Accept' => 'application/json',
                            'Content-Type' => 'application/json',
                        ],
                        'json' => $addressData
                    ],
                    'Update Address'
                );
                if ($result['status'] >= 200 && $result['status'] < 300) {
                    $message = 'Address updated successfully!';
                    $messageType = 'success';
                    unset($_SESSION['locationForUpdate']);
                } else {
                    $message = 'Failed to update address. ' . ($result['error'] ?? '');
                    $messageType = 'danger';
                }
                break;
        }
    }
}

// Always use session value for customerForAddress if available
if (isset($_SESSION['customerForAddress'])) {
    $customerForAddress = $_SESSION['customerForAddress'];
}

// Always use session value for customerForUpdateLocation and locationsForUpdate if available
if (isset($_SESSION['customerForUpdateLocation'])) {
    $customerForUpdateLocation = $_SESSION['customerForUpdateLocation'];
}
if (isset($_SESSION['locationsForUpdate'])) {
    $locationsForUpdate = $_SESSION['locationsForUpdate'];
}
if (isset($_SESSION['locationForUpdate'])) {
    $locationForUpdate = $_SESSION['locationForUpdate'];
}
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Core API POST & PUT Example</title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <style>
    .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
    }

    .card {
        margin-bottom: 20px;
    }

    .form-group {
        margin-bottom: 15px;
    }

    .btn-action {
        margin-right: 10px;
    }

    pre {
        background-color: #f8f9fa;
        padding: 15px;
        border-radius: 5px;
        overflow-x: auto;
    }

    .nav-tabs {
        margin-bottom: 20px;
    }

    .tab-content {
        padding: 20px;
        border: 1px solid #dee2e6;
        border-top: none;
        border-radius: 0 0 5px 5px;
    }
    </style>
</head>

<body>
    <div class="container">
        <h1 class="mb-4">Core API POST & PUT Examples</h1>
        <!-- Show token validation message -->
        <?php if (!empty($tokenMessage)): ?>
        <div class="alert alert-<?php echo $tokenMessageType; ?>" role="alert">
            <?php echo $tokenMessage; ?>
        </div>
        <?php endif; ?>
        <!-- Add API Token Input Form -->
        <form method="post" action="" class="mb-4">
            <div class="form-group">
                <label for="api_token"><strong>API Token</strong></label>
                <input type="text" class="form-control" id="api_token" name="api_token"
                    value="<?php echo htmlspecialchars($apiKey); ?>" required>
                <small class="form-text text-muted">Paste your API token here. This will be used for all API requests
                    below.</small>
            </div>
            <button type="submit" class="btn btn-secondary mb-2">Set Token</button>
        </form>

        <?php if (!empty($message)): ?>
        <div class="alert alert-<?php echo $messageType; ?>" role="alert">
            <?php echo $message; ?>
        </div>
        <?php endif; ?>

        <div class="card">
            <div class="card-header">
                <ul class="nav nav-tabs card-header-tabs" id="apiTabs" role="tablist">
                    <li class="nav-item">
                        <a class="nav-link active" id="create-tab" data-toggle="tab" href="#create" role="tab">Create
                            (POST)</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" id="update-tab" data-toggle="tab" href="#update" role="tab">Update (PUT)</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" id="get-tab" data-toggle="tab" href="#get" role="tab">Get (GET)</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" id="add-address-tab" data-toggle="tab" href="#add-address" role="tab">Add
                            Address (POST)</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" id="update-location-tab" data-toggle="tab" href="#update-location"
                            role="tab">Update Address (PUT)</a>
                    </li>
                </ul>
            </div>
            <div class="card-body">
                <div class="tab-content" id="apiTabsContent">
                    <!-- Create Customer Form (POST) -->
                    <div class="tab-pane fade show active" id="create" role="tabpanel">
                        <h3>Create New Customer</h3>
                        <form method="post" action="">
                            <input type="hidden" name="action" value="create">

                            <div class="form-group">
                                <label for="customerNo">Customer No *</label>
                                <input type="text" class="form-control" id="customerNo" name="customerNo"
                                    placeholder="CUST1234" required value="CUST<?php echo rand(1000, 9999); ?>">
                                <small class="form-text text-muted">Unique customer identifier</small>
                            </div>

                            <div class="form-group">
                                <label for="customerName">Customer Name *</label>
                                <input type="text" class="form-control" id="customerName" name="customerName" required>
                            </div>

                            <div class="form-group">
                                <label for="customerEntityType">Entity Type *</label>
                                <select class="form-control" id="customerEntityType" name="customerEntityType" required>
                                    <option value="COMPANY">Company</option>
                                    <option value="INDIVIDUAL">Individual</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label for="customerGroup">Customer Group *</label>
                                <input type="text" class="form-control" id="customerGroup" name="customerGroup"
                                    value="1" required>
                            </div>

                            <div class="form-group">
                                <label for="customerStartDate">Start Date *</label>
                                <input type="date" class="form-control" id="customerStartDate" name="customerStartDate"
                                    value="<?php echo date('Y-m-d'); ?>" required>
                            </div>

                            <div class="form-group">
                                <label for="status">Status</label>
                                <input type="text" class="form-control" id="status" name="status" value="a">
                                <small class="form-text text-muted">a = active</small>
                            </div>

                            <button type="submit" class="btn btn-primary">Create Customer</button>
                        </form>
                    </div>

                    <!-- Update Customer Form (PUT) -->
                    <div class="tab-pane fade" id="update" role="tabpanel">
                        <h3>Update Existing Customer</h3>

                        <!-- Customer Search Form -->
                        <div class="card mb-4">
                            <div class="card-header bg-light">
                                <h5 class="mb-0">Search for Customer</h5>
                            </div>
                            <div class="card-body">
                                <form method="post" action="" id="searchCustomerForm">
                                    <input type="hidden" name="action" value="search_for_update">

                                    <div class="form-group">
                                        <label for="searchCustomerId">Customer Number *</label>
                                        <input type="text" class="form-control" id="searchCustomerId" name="customerId"
                                            placeholder="Enter customer number (e.g., HI003)" required>
                                    </div>

                                    <button type="submit" class="btn btn-info">Search Customer</button>
                                </form>
                            </div>
                        </div>

                        <?php
                        // Display customer data if available from search
                        if (isset($customerForUpdate) && !empty($customerForUpdate)) {
                            $updateCustomerId = $customerForUpdate['no'] ?? '';
                            $updateCustomerName = $customerForUpdate['name'] ?? '';
                            $updateEntityType = $customerForUpdate['entity_type'] == '1' ? 'COMPANY' : 'INDIVIDUAL';
                            $updateCustomerGroup = $customerForUpdate['grp_id'] ?? '1';
                            $updateStartDate = $customerForUpdate['start_date'] ?? date('Y-m-d');
                            $updateStatus = $customerForUpdate['status'] ?? 'a';
                        } else {
                            $updateCustomerId = '';
                            $updateCustomerName = '';
                            $updateEntityType = 'COMPANY';
                            $updateCustomerGroup = '1';
                            $updateStartDate = date('Y-m-d');
                            $updateStatus = 'a';
                        }
                        ?>

                        <!-- Update Form -->
                        <form method="post" action="">
                            <input type="hidden" name="action" value="update">

                            <div class="form-group">
                                <label for="updateCustomerId">Customer No *</label>
                                <input type="text" class="form-control" id="updateCustomerId" name="customerId" required
                                    value="<?php echo htmlspecialchars($updateCustomerId); ?>"
                                    <?php echo !empty($updateCustomerId) ? 'readonly' : ''; ?>>
                                <small class="form-text text-muted">Customer number cannot be changed</small>
                            </div>

                            <div class="form-group">
                                <label for="updateCustomerName">Customer Name *</label>
                                <input type="text" class="form-control" id="updateCustomerName" name="customerName"
                                    value="<?php echo htmlspecialchars($updateCustomerName); ?>" required>
                            </div>

                            <div class="form-group">
                                <label for="updateCustomerEntityType">Entity Type *</label>
                                <select class="form-control" id="updateCustomerEntityType" name="customerEntityType"
                                    required>
                                    <option value="COMPANY"
                                        <?php echo $updateEntityType == 'COMPANY' ? 'selected' : ''; ?>>Company</option>
                                    <option value="INDIVIDUAL"
                                        <?php echo $updateEntityType == 'INDIVIDUAL' ? 'selected' : ''; ?>>Individual
                                    </option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label for="updateCustomerGroup">Customer Group *</label>
                                <input type="text" class="form-control" id="updateCustomerGroup" name="customerGroup"
                                    value="<?php echo htmlspecialchars($updateCustomerGroup); ?>" required>
                            </div>

                            <div class="form-group">
                                <label for="updateCustomerStartDate">Start Date *</label>
                                <input type="date" class="form-control" id="updateCustomerStartDate"
                                    name="customerStartDate" value="<?php echo htmlspecialchars($updateStartDate); ?>"
                                    required>
                            </div>

                            <div class="form-group">
                                <label for="updateStatus">Status</label>
                                <input type="text" class="form-control" id="updateStatus" name="status"
                                    value="<?php echo htmlspecialchars($updateStatus); ?>">
                                <small class="form-text text-muted">a = active</small>
                            </div>

                            <button type="submit" class="btn btn-warning"
                                <?php echo empty($updateCustomerId) ? 'disabled' : ''; ?>>
                                <?php echo empty($updateCustomerId) ? 'Search for a Customer First' : 'Update Customer'; ?>
                            </button>
                        </form>
                    </div>



                    <!-- Get Customer Form (GET) -->
                    <div class="tab-pane fade" id="get" role="tabpanel">
                        <h3>Get Customer Details</h3>
                        <form method="post" action="">
                            <input type="hidden" name="action" value="get">

                            <div class="form-group">
                                <label for="getCustomerId">Customer ID *</label>
                                <input type="text" class="form-control" id="getCustomerId" name="customerId" required
                                    value="<?php echo htmlspecialchars($customerId); ?>">
                            </div>

                            <button type="submit" class="btn btn-info">Get Customer</button>
                        </form>
                    </div>

                    <!-- Add Address Tab -->
                    <div class="tab-pane fade" id="add-address" role="tabpanel">
                        <h3>Add Address to Customer</h3>
                        <!-- Search Customer for Address -->
                        <div class="card mb-4">
                            <div class="card-header bg-light">
                                <h5 class="mb-0">Search for Customer</h5>
                            </div>
                            <div class="card-body">
                                <form method="post" action="" id="searchCustomerForAddressForm">
                                    <input type="hidden" name="action" value="search_for_address">
                                    <div class="form-group">
                                        <label for="searchCustomerIdForAddress">Customer Number *</label>
                                        <input type="text" class="form-control" id="searchCustomerIdForAddress"
                                            name="customerId" placeholder="Enter customer number (e.g., HI003)"
                                            required>
                                    </div>
                                    <button type="submit" class="btn btn-info">Search Customer</button>
                                </form>
                            </div>
                        </div>
                        <?php
                        if (isset($customerForAddress) && !empty($customerForAddress)) {
                            $addressCustomerId = $customerForAddress['no'] ?? '';
                            $addressCustomerDbId = $customerForAddress['id'] ?? '';
                        ?>
                        <!-- Show Customer Number -->
                        <div class="alert alert-secondary">
                            <strong>Customer Number:</strong> <?php echo htmlspecialchars($addressCustomerId); ?>
                        </div>
                        <!-- Address Form -->
                        <form method="post" action="">
                            <input type="hidden" name="action" value="add_address">
                            <input type="hidden" name="customerId"
                                value="<?php echo htmlspecialchars($addressCustomerId); ?>">
                            <input type="hidden" name="customerDbId"
                                value="<?php echo htmlspecialchars($addressCustomerDbId); ?>">

                            <div class="form-group">
                                <label for="loc_code">Location Code *</label>
                                <input type="text" class="form-control" id="loc_code" name="loc_code" required
                                    value="LOC<?php echo rand(1000,9999); ?>">
                            </div>
                            <div class="form-group">
                                <label for="loc_name">Location Name *</label>
                                <input type="text" class="form-control" id="loc_name" name="loc_name" required>
                            </div>
                            <div class="form-group">
                                <label for="add1">Address *</label>
                                <input type="text" class="form-control" id="add1" name="add1" required>
                            </div>
                            <div class="form-group">
                                <label for="ccode">Country Code *</label>
                                <input type="text" class="form-control" id="ccode" name="ccode" value="TH" required>
                            </div>
                            <div class="form-group">
                                <label for="state_code">State Code *</label>
                                <input type="text" class="form-control" id="state_code" name="state_code" value="10"
                                    required>
                            </div>
                            <div class="form-group">
                                <label for="city">City *</label>
                                <input type="text" class="form-control" id="city" name="city" value="Bangkok" required>
                            </div>
                            <div class="form-group">
                                <label for="zip">ZIP *</label>
                                <input type="text" class="form-control" id="zip" name="zip" value="10230" required>
                            </div>
                            <div class="form-group">
                                <label for="status">Status</label>
                                <input type="text" class="form-control" id="status" name="status" value="a">
                            </div>
                            <div class="form-group">
                                <label for="is_primary_loc">Is Primary Location</label>
                                <input type="text" class="form-control" id="is_primary_loc" name="is_primary_loc"
                                    value="n" readonly>
                                <small class="form-text text-muted">Always "n" for this form</small>
                            </div>
                            <button type="submit" class="btn btn-success">Add Address</button>
                        </form>
                        <?php } ?>
                    </div>
                    <!-- Update Address Tab -->
                    <div class="tab-pane fade" id="update-location" role="tabpanel">
                        <h3>Update Address for Customer</h3>
                        <!-- Search Customer for Address Update -->
                        <div class="card mb-4">
                            <div class="card-header bg-light">
                                <h5 class="mb-0">Search for Customer</h5>
                            </div>
                            <div class="card-body">
                                <form method="post" action="" id="searchCustomerForUpdateLocationForm">
                                    <input type="hidden" name="action" value="search_for_update_location">
                                    <div class="form-group">
                                        <label for="searchCustomerIdForUpdateLocation">Customer Number *</label>
                                        <input type="text" class="form-control" id="searchCustomerIdForUpdateLocation"
                                            name="customerId" placeholder="Enter customer number (e.g., HI003)"
                                            required>
                                    </div>
                                    <button type="submit" class="btn btn-info">Search Customer</button>
                                </form>
                            </div>
                        </div>
                        <?php
                        // Only show locations dropdown if a customer has been found and locations are available
                        if (
                            isset($customerForUpdateLocation) && !empty($customerForUpdateLocation)
                            && isset($locationsForUpdate) && !empty($locationsForUpdate)
                        ) {
                        ?>
                        <div class="card mb-4">
                            <div class="card-header bg-light">
                                <h5 class="mb-0">Select Location to Update</h5>
                            </div>
                            <div class="card-body">
                                <form method="post" action="">
                                    <input type="hidden" name="action" value="select_location_for_update">
                                    <div class="form-group">
                                        <label for="locationId">Location *</label>
                                        <select class="form-control" id="locationId" name="locationId" required>
                                            <option value="">-- Select Location --</option>
                                            <?php foreach ($locationsForUpdate as $loc): ?>
                                            <option value="<?php echo htmlspecialchars($loc['id']); ?>"
                                                <?php if (isset($locationForUpdate) && $locationForUpdate['id'] == $loc['id']) echo 'selected'; ?>>
                                                <?php echo htmlspecialchars($loc['loc_code'] . ' - ' . $loc['loc_name']); ?>
                                            </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                    <button type="submit" class="btn btn-primary">Edit Location</button>
                                </form>
                            </div>
                        </div>
                        <?php } ?>

                        <?php
                        // If a location is selected for update, show the update form
                        if (isset($locationForUpdate) && !empty($locationForUpdate) && isset($customerForUpdateLocation)) {
                        ?>
                        <!-- Show Customer Number -->
                        <div class="alert alert-secondary">
                            <strong>Customer Number:</strong>
                            <?php echo htmlspecialchars($customerForUpdateLocation['no'] ?? ''); ?>
                        </div>
                        <form method="post" action="">
                            <input type="hidden" name="action" value="update_address">
                            <input type="hidden" name="customerDbId"
                                value="<?php echo htmlspecialchars($customerForUpdateLocation['id']); ?>">
                            <input type="hidden" name="locationId"
                                value="<?php echo htmlspecialchars($locationForUpdate['id']); ?>">

                            <div class="form-group">
                                <label for="loc_code">Location Code *</label>
                                <input type="text" class="form-control" id="loc_code" name="loc_code" required
                                    value="<?php echo htmlspecialchars($locationForUpdate['loc_code'] ?? ''); ?>">
                            </div>
                            <div class="form-group">
                                <label for="loc_name">Location Name *</label>
                                <input type="text" class="form-control" id="loc_name" name="loc_name" required
                                    value="<?php echo htmlspecialchars($locationForUpdate['loc_name'] ?? ''); ?>">
                            </div>
                            <div class="form-group">
                                <label for="add1">Address *</label>
                                <input type="text" class="form-control" id="add1" name="add1" required
                                    value="<?php echo htmlspecialchars($locationForUpdate['add1'] ?? ''); ?>">
                            </div>
                            <div class="form-group">
                                <label for="ccode">Country Code *</label>
                                <input type="text" class="form-control" id="ccode" name="ccode" required
                                    value="<?php echo htmlspecialchars($locationForUpdate['ccode'] ?? 'TH'); ?>">
                            </div>
                            <div class="form-group">
                                <label for="state_code">State Code *</label>
                                <input type="text" class="form-control" id="state_code" name="state_code" required
                                    value="<?php echo htmlspecialchars($locationForUpdate['state_code'] ?? '10'); ?>">
                            </div>
                            <div class="form-group">
                                <label for="city">City *</label>
                                <input type="text" class="form-control" id="city" name="city" required
                                    value="<?php echo htmlspecialchars($locationForUpdate['city'] ?? 'Bangkok'); ?>">
                            </div>
                            <div class="form-group">
                                <label for="zip">ZIP *</label>
                                <input type="text" class="form-control" id="zip" name="zip" required
                                    value="<?php echo htmlspecialchars($locationForUpdate['zip'] ?? '10230'); ?>">
                            </div>
                            <div class="form-group">
                                <label for="status">Status</label>
                                <input type="text" class="form-control" id="status" name="status"
                                    value="<?php echo htmlspecialchars($locationForUpdate['status'] ?? 'a'); ?>">
                            </div>
                            <div class="form-group">
                                <label for="is_primary_loc">Is Primary Location</label>
                                <input type="text" class="form-control" id="is_primary_loc" name="is_primary_loc"
                                    value="<?php echo htmlspecialchars($locationForUpdate['is_primary_loc'] ?? 'n'); ?>">
                            </div>
                            <button type="submit" class="btn btn-warning">Update Address</button>
                        </form>
                        <?php } ?>
                    </div>
                </div>
            </div>
        </div>

        <div class="mt-4">
            <a href="core_api_example.php" class="btn btn-secondary">View GET Examples</a>
            <a href="../merlion/admin-tickets.php" class="btn btn-secondary">Back to Dashboard</a>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/popper.js@1.16.1/dist/umd/popper.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
</body>

</html>

<!-- Explanation for "Invalid parent id" error:
This error means the API endpoint /contact/customers/{id}/locations expects {id} to be the numeric internal customer ID (not the customer number).
If you use the customer number (e.g., "CUST1234") instead of the numeric ID (e.g., 123), the API will return "Invalid parent id".
Always use the numeric "id" field from the customer search result, not the "no" field, in the URL path for adding an address. -->