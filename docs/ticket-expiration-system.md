# Ticket Expiration System Documentation

## Overview
The Ticket Expiration System manages the lifecycle of support tickets with configurable expiration periods and automatic cleanup functionality. It implements a FIFO (First In, First Out) system to ensure older tickets are used before newer ones.

## Key Features

### 1. FIFO Ticket Usage
- When users create support tickets, the system automatically uses the oldest available tickets first
- Prevents newer tickets from being consumed while older ones expire unused
- Maintains purchase history with detailed tracking

### 2. Configurable Expiration
- **Default Lifetime**: 12 months (configurable)
- **Warning Period**: 30 days before expiration (configurable)
- **Cleanup Frequency**: Weekly automatic cleanup (configurable)
- **Admin Controls**: Full configuration through admin panel

### 3. Automatic Cleanup
- Scheduled cleanup removes expired tickets automatically
- Manual cleanup available through admin panel
- Detailed logging of all expiration actions
- Email notifications to administrators

## File Structure

### Core Files
- `config/ticket-expiration-config.php` - Configuration management
- `functions/ticket-expiration-functions.php` - Core FIFO and expiration logic
- `database/setup-ticket-expiration.php` - Database initialization script

### Admin Interface
- `merlion/ticket-expiration-manager.php` - Main admin panel
- `merlion/test-ticket-expiration.php` - Testing interface
- `merlion/admin-menu.php` - Updated to include expiration manager

### Automation
- `cron/ticket-expiration-cleanup.php` - Automatic cleanup cron job

### Components
- `components/ticket-expiration-widget.php` - User dashboard widget

## Database Schema

### New Tables
1. **ticket_expiration_settings** - System configuration
2. **ticket_expiration_log** - Audit trail of expired tickets
3. **ticket_usage_log** - Track ticket consumption

### Modified Tables
1. **purchasetickets** - Added `expiration_date` and `expired_at` columns

## Installation & Setup

### 1. Database Setup
Run the initialization script:
```
http://your-domain/database/setup-ticket-expiration.php
```

### 2. Configuration
Access the admin panel:
```
http://your-domain/merlion/ticket-expiration-manager.php
```

### 3. Cron Job Setup
Add to your server's crontab for daily cleanup at 2 AM:
```bash
0 2 * * * /usr/bin/php /path/to/your/project/cron/ticket-expiration-cleanup.php
```

## Configuration Options

### System Settings
- **Ticket Lifetime**: How long tickets remain valid (months)
- **Warning Period**: Days before expiration to warn users
- **Cleanup Frequency**: How often to run automatic cleanup
- **Auto Cleanup**: Enable/disable automatic cleanup
- **User Notifications**: Enable/disable user warnings
- **Admin Email**: Email for cleanup reports

### Example Configuration
```php
$config = [
    'ticket_lifetime_months' => 12,    // 1 year lifetime
    'warning_period_days' => 30,       // 30-day warning
    'cleanup_frequency_days' => 7,     // Weekly cleanup
    'auto_cleanup_enabled' => true,    // Auto cleanup on
    'user_notifications_enabled' => true
];
```

## Usage Examples

### Your Scenario
**User Purchases:**
- June 22, 2024: 10 starter + 5 ultimate tickets
- August 22, 2024: 5 starter + 5 ultimate tickets
- **Total**: 15 starter + 10 ultimate tickets

**User Creates 3 Tickets:**
- System uses 3 oldest starter tickets (from June 2024 batch)
- **Remaining**: 12 starter + 10 ultimate tickets

**After 1 Year (June 24, 2025):**
- System removes 7 expired starter + 5 expired ultimate (June 2024)
- System removes 5 expired starter + 5 expired ultimate (August 2024)
- **Final**: Only tickets purchased after expiration dates remain

## API Functions

### Core Functions
```php
// Get user tickets in FIFO order
getUserTicketsFIFO($username, $ticket_type = null)

// Use tickets with FIFO logic
useTicketsFIFO($username, $ticket_type, $quantity = 1)

// Get expired tickets
getExpiredTickets($username = null)

// Remove expired tickets
removeExpiredTickets($username = null)

// Get tickets expiring soon
getTicketsExpiringSoon($username = null, $days_ahead = null)
```

### Configuration Functions
```php
// Get current configuration
getTicketExpirationConfig()

// Update configuration
updateTicketExpirationConfig($key, $value)

// Check if tickets are expired
areTicketsExpired($purchase_date)

// Get days until expiration
getDaysUntilExpiration($purchase_date)
```

## Admin Panel Features

### Statistics Dashboard
- Total active tickets
- Expired tickets count
- Tickets expiring soon
- Current lifetime settings

### Management Tools
- Update expiration settings
- Run manual cleanup
- View expiration logs
- Update existing ticket dates

### Testing Interface
- Create test data
- Test FIFO logic
- Test cleanup process
- View current ticket status

## Logging & Monitoring

### Expiration Log
Tracks all expired tickets with:
- Username
- Ticket type
- Expired quantity
- Purchase date
- Expiration date
- Cleanup timestamp

### Usage Log
Tracks ticket consumption with:
- Username
- Ticket type
- Quantity used
- Purchase record ID
- Usage timestamp

### Cleanup Log
Automated cleanup generates:
- Detailed cleanup reports
- Email notifications to admin
- Error logging for troubleshooting

## Troubleshooting

### Common Issues
1. **Class Conflict Error**: Ensure `server.php` is included only once
2. **Database Connection**: Verify database credentials and connection
3. **Cron Job Not Running**: Check server cron configuration
4. **Email Notifications**: Verify SMTP settings and admin email

### Debug Mode
Enable debug mode in configuration for detailed logging:
```php
updateTicketExpirationConfig('debug_mode', true);
```

## Security Considerations

### Data Protection
- All database queries use prepared statements
- Input validation on all user inputs
- CSRF protection on admin forms
- Secure session management

### Access Control
- Admin-only access to configuration
- Role-based permissions
- Secure logout functionality

## Performance Optimization

### Database Indexes
The system creates optimized indexes for:
- Expiration date queries
- Username lookups
- Purchase time sorting
- Remaining ticket counts

### Efficient Queries
- Batch operations for cleanup
- Optimized FIFO queries
- Minimal database calls

## Future Enhancements

### Potential Features
- User email notifications before expiration
- Different expiration periods per ticket type
- Ticket transfer between users
- Advanced reporting dashboard
- API endpoints for external integration

## Support

For issues or questions about the Ticket Expiration System:
1. Check the admin panel logs
2. Review the test interface results
3. Verify configuration settings
4. Check server error logs
5. Contact system administrator

---

**Version**: 1.0  
**Last Updated**: June 2025  
**Compatibility**: PHP 7.4+, MySQL 5.7+
