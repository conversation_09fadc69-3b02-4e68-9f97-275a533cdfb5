<?php
/**
 * Reinstall Dependencies Script
 * This script will help reinstall Composer dependencies properly
 */

echo "<h1>Reinstall Dependencies</h1>";
echo "<p>This script will help you reinstall Composer dependencies properly.</p>";

// Check if we're running this via web or CLI
$is_cli = php_sapi_name() === 'cli';

if (!$is_cli) {
    echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 10px; margin: 10px 0; border-radius: 5px;'>";
    echo "<strong>⚠️ Warning:</strong> This script is better run via command line for proper output.";
    echo "</div>";
}

// Test 1: Check current state
echo "<h2>1. Current State Check</h2>";

$composer_exists = file_exists('composer.json');
$vendor_exists = is_dir('vendor');
$autoload_exists = file_exists('vendor/autoload.php');

echo "composer.json: " . ($composer_exists ? "✅ Exists" : "❌ Missing") . "<br>";
echo "vendor directory: " . ($vendor_exists ? "✅ Exists" : "❌ Missing") . "<br>";
echo "vendor/autoload.php: " . ($autoload_exists ? "✅ Exists" : "❌ Missing") . "<br>";

// Test 2: Check if Composer is available
echo "<h2>2. Composer Availability</h2>";

$composer_commands = ['composer', 'php composer.phar'];
$composer_available = false;
$composer_command = '';

foreach ($composer_commands as $cmd) {
    $output = [];
    $return_var = 0;
    
    // Try to run composer --version
    exec("$cmd --version 2>&1", $output, $return_var);
    
    if ($return_var === 0) {
        $composer_available = true;
        $composer_command = $cmd;
        echo "✅ Composer available via: $cmd<br>";
        echo "Version: " . implode(' ', $output) . "<br>";
        break;
    }
}

if (!$composer_available) {
    echo "❌ Composer not available<br>";
    echo "<p><strong>To install Composer:</strong></p>";
    echo "<ol>";
    echo "<li>Download composer.phar from <a href='https://getcomposer.org/download/' target='_blank'>https://getcomposer.org/download/</a></li>";
    echo "<li>Place it in your project directory</li>";
    echo "<li>Run: <code>php composer.phar install</code></li>";
    echo "</ol>";
}

// Test 3: Backup current vendor (if exists)
echo "<h2>3. Backup Current Installation</h2>";

if ($vendor_exists) {
    $backup_name = 'vendor_backup_' . date('Y-m-d_H-i-s');
    
    if (rename('vendor', $backup_name)) {
        echo "✅ Backed up vendor directory to: $backup_name<br>";
    } else {
        echo "⚠️ Could not backup vendor directory<br>";
    }
} else {
    echo "ℹ️ No vendor directory to backup<br>";
}

// Test 4: Show commands to run
echo "<h2>4. Commands to Run</h2>";

if ($composer_available) {
    echo "<p>Run these commands in your terminal:</p>";
    echo "<div style='background: #f8f9fa; border: 1px solid #dee2e6; padding: 15px; font-family: monospace;'>";
    echo "cd " . getcwd() . "<br>";
    echo "$composer_command install --no-dev<br>";
    echo "</div>";
    
    echo "<p>Or if you want development dependencies:</p>";
    echo "<div style='background: #f8f9fa; border: 1px solid #dee2e6; padding: 15px; font-family: monospace;'>";
    echo "$composer_command install<br>";
    echo "</div>";
    
    // Try to run composer install automatically (if safe to do so)
    if (isset($_GET['auto_install']) && $_GET['auto_install'] === 'yes') {
        echo "<h2>5. Automatic Installation</h2>";
        echo "<p>Running composer install...</p>";
        
        $output = [];
        $return_var = 0;
        
        // Change to the correct directory and run composer install
        $command = "cd " . escapeshellarg(getcwd()) . " && $composer_command install --no-dev 2>&1";
        exec($command, $output, $return_var);
        
        echo "<div style='background: #f8f9fa; border: 1px solid #dee2e6; padding: 15px; font-family: monospace; white-space: pre-wrap;'>";
        echo implode("\n", $output);
        echo "</div>";
        
        if ($return_var === 0) {
            echo "<p>✅ Composer install completed successfully!</p>";
            echo "<p><a href='test-composer-installation.php'>Test the installation</a></p>";
        } else {
            echo "<p>❌ Composer install failed with return code: $return_var</p>";
        }
    } else {
        echo "<p><a href='?auto_install=yes' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Run Automatic Installation</a></p>";
        echo "<p><small>⚠️ This will run composer install automatically. Make sure you have a backup.</small></p>";
    }
} else {
    echo "<p>❌ Cannot run automatic installation - Composer not available</p>";
}

// Test 5: Manual installation instructions
echo "<h2>5. Manual Installation (Alternative)</h2>";
echo "<p>If automatic installation doesn't work, you can:</p>";
echo "<ol>";
echo "<li>Download the vendor directory from your localhost</li>";
echo "<li>Upload it to your server via FTP/SFTP</li>";
echo "<li>Make sure file permissions are correct (755 for directories, 644 for files)</li>";
echo "</ol>";

// Test 6: Verify installation
echo "<h2>6. Verify Installation</h2>";
echo "<p>After installation, check:</p>";
echo "<ul>";
echo "<li><a href='test-composer-installation.php'>Test Composer Installation</a></li>";
echo "<li><a href='debug-appika-detailed.php'>Test Appika Integration</a></li>";
echo "</ul>";

echo "<h2>Troubleshooting</h2>";
echo "<p>Common issues and solutions:</p>";
echo "<ul>";
echo "<li><strong>Permission denied:</strong> Check file permissions (chmod 755 vendor)</li>";
echo "<li><strong>Memory limit:</strong> Increase PHP memory_limit in php.ini</li>";
echo "<li><strong>Timeout:</strong> Increase max_execution_time in php.ini</li>";
echo "<li><strong>Missing extensions:</strong> Install php-curl, php-json, php-mbstring</li>";
echo "</ul>";
?>
