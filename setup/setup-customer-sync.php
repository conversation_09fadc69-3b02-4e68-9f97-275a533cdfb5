<?php
/**
 * Setup script for Appika Customer Sync
 * This script helps configure the automatic customer sync functionality
 */

require_once '../functions/server.php';

echo "<h1>Appika Customer Sync Setup</h1>";

// Check if required files exist
echo "<h2>1. File Check</h2>";
$required_files = [
    '../functions/appika_customer_sync.php' => 'Customer sync functions',
    '../merlion/sync-customers-from-appika.php' => 'Scheduled sync script',
    '../merlion/appika-customer-updates-widget.php' => 'Customer updates widget',
    '../config/api-config.php' => 'API configuration'
];

$all_files_exist = true;
foreach ($required_files as $file => $description) {
    if (file_exists($file)) {
        echo "✅ $description: <code>$file</code><br>";
    } else {
        echo "❌ $description: <code>$file</code> - FILE MISSING<br>";
        $all_files_exist = false;
    }
}

if (!$all_files_exist) {
    echo "<p style='color: red;'><strong>Some required files are missing. Please ensure all files are uploaded.</strong></p>";
    exit;
}

// Check database columns
echo "<h2>2. Database Column Check</h2>";
$required_columns = [
    'updated_at' => 'TIMESTAMP NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP',
    'last_appika_sync' => 'TIMESTAMP NULL DEFAULT NULL',
    'appika_updated_at' => 'TIMESTAMP NULL DEFAULT NULL',
    'appika_update_source' => 'VARCHAR(50) NULL DEFAULT NULL'
];

foreach ($required_columns as $column => $definition) {
    $check_column = mysqli_query($conn, "SHOW COLUMNS FROM user LIKE '$column'");
    if (mysqli_num_rows($check_column) > 0) {
        echo "✅ Column <code>user.$column</code> exists<br>";
    } else {
        echo "❌ Column <code>user.$column</code> does NOT exist<br>";
        echo "<strong>Adding column now...</strong><br>";
        
        $add_column = mysqli_query($conn, "ALTER TABLE user ADD COLUMN $column $definition");
        if ($add_column) {
            echo "✅ Column <code>$column</code> added successfully<br>";
        } else {
            echo "❌ Failed to add column <code>$column</code>: " . mysqli_error($conn) . "<br>";
        }
    }
}

// Check if customer_sync_logs table exists
echo "<h2>3. Sync Logs Table Check</h2>";
$check_table = mysqli_query($conn, "SHOW TABLES LIKE 'customer_sync_logs'");
if (mysqli_num_rows($check_table) > 0) {
    echo "✅ Table <code>customer_sync_logs</code> exists<br>";
} else {
    echo "❌ Table <code>customer_sync_logs</code> does NOT exist<br>";
    echo "<strong>Creating table now...</strong><br>";
    
    $create_table = "CREATE TABLE customer_sync_logs (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        operation VARCHAR(50) NOT NULL,
        success TINYINT(1) NOT NULL,
        message TEXT,
        changes JSON,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_user_id (user_id),
        INDEX idx_created_at (created_at)
    )";
    
    if (mysqli_query($conn, $create_table)) {
        echo "✅ Table <code>customer_sync_logs</code> created successfully<br>";
    } else {
        echo "❌ Failed to create table: " . mysqli_error($conn) . "<br>";
    }
}

// Test API connection
echo "<h2>4. API Connection Test</h2>";
try {
    require_once '../config/api-config.php';
    $apiConfig = getCustomerApiConfig();
    
    if (empty($apiConfig['key'])) {
        echo "❌ API key not configured in api-config.php<br>";
    } else {
        echo "✅ API configuration found<br>";
        echo "- Endpoint: " . htmlspecialchars($apiConfig['endpoint']) . "<br>";
        echo "- Path: " . htmlspecialchars($apiConfig['path']) . "<br>";
        echo "- Key: " . str_repeat('*', strlen($apiConfig['key']) - 4) . substr($apiConfig['key'], -4) . "<br>";
    }
} catch (Exception $e) {
    echo "❌ API configuration error: " . htmlspecialchars($e->getMessage()) . "<br>";
}

// Test sync functionality
echo "<h2>5. Sync Function Test</h2>";
try {
    require_once '../functions/appika_customer_sync.php';
    
    // Get a user with Appika customer ID for testing
    $test_user_query = "SELECT id, username, appika_customer_id FROM user WHERE appika_customer_id IS NOT NULL AND appika_customer_id != '' LIMIT 1";
    $test_user_result = mysqli_query($conn, $test_user_query);
    
    if ($test_user = mysqli_fetch_assoc($test_user_result)) {
        echo "✅ Found test user: " . htmlspecialchars($test_user['username']) . " (ID: {$test_user['id']}, Appika ID: {$test_user['appika_customer_id']})<br>";
        
        // Test sync function
        $sync_result = syncSingleCustomerFromAppika($test_user['id']);
        if ($sync_result['success']) {
            echo "✅ Sync test successful: " . htmlspecialchars($sync_result['message']) . "<br>";
        } else {
            echo "⚠️ Sync test completed with message: " . htmlspecialchars($sync_result['message']) . "<br>";
        }
    } else {
        echo "⚠️ No users with Appika customer IDs found for testing<br>";
    }
} catch (Exception $e) {
    echo "❌ Sync function test failed: " . htmlspecialchars($e->getMessage()) . "<br>";
}

// Cron job setup instructions
echo "<h2>6. Cron Job Setup Instructions</h2>";
echo "<p>To enable automatic customer sync every 10 minutes, add this cron job:</p>";

$php_path = '/usr/bin/php'; // Default PHP path
$script_path = realpath('../merlion/sync-customers-from-appika.php');

echo "<div style='background-color: #f8f9fa; padding: 15px; border-radius: 5px; font-family: monospace;'>";
echo "*/10 * * * * $php_path $script_path >> /path/to/logs/customer_sync_cron.log 2>&1";
echo "</div>";

echo "<p><strong>Steps to add cron job:</strong></p>";
echo "<ol>";
echo "<li>Open terminal/SSH to your server</li>";
echo "<li>Run: <code>crontab -e</code></li>";
echo "<li>Add the line above (adjust paths as needed)</li>";
echo "<li>Save and exit</li>";
echo "<li>Verify with: <code>crontab -l</code></li>";
echo "</ol>";

echo "<p><strong>Alternative: Manual sync</strong></p>";
echo "<p>You can also run manual sync by visiting: <a href='../merlion/appika-customer-updates-widget.php' target='_blank'>Customer Updates Widget</a></p>";

// Test manual sync
echo "<h2>7. Manual Sync Test</h2>";
echo "<p>You can test the bulk sync function here:</p>";

if (isset($_POST['test_sync'])) {
    echo "<div style='background-color: #e7f3ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<strong>Running manual sync test...</strong><br><br>";
    
    try {
        $sync_results = bulkSyncCustomersFromAppika(10); // Test with 10 customers
        
        echo "✅ Sync completed successfully!<br>";
        echo "- Customers checked: " . $sync_results['total_checked'] . "<br>";
        echo "- Customers updated: " . $sync_results['total_updated'] . "<br>";
        
        if (!empty($sync_results['errors'])) {
            echo "- Errors: " . count($sync_results['errors']) . "<br>";
            foreach ($sync_results['errors'] as $error) {
                echo "&nbsp;&nbsp;• " . htmlspecialchars($error) . "<br>";
            }
        }
    } catch (Exception $e) {
        echo "❌ Sync test failed: " . htmlspecialchars($e->getMessage()) . "<br>";
    }
    
    echo "</div>";
}

echo "<form method='POST'>";
echo "<button type='submit' name='test_sync' class='btn btn-primary' style='background-color: #473BF0; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;'>Test Manual Sync (10 customers)</button>";
echo "</form>";

echo "<h2>8. Summary</h2>";
echo "<p>✅ <strong>Setup Complete!</strong> Your Appika customer sync system is ready.</p>";
echo "<p><strong>Features available:</strong></p>";
echo "<ul>";
echo "<li>Real-time sync when viewing admin-users.php (every 5 minutes)</li>";
echo "<li>Scheduled bulk sync (every 10 minutes via cron)</li>";
echo "<li>Manual sync via Customer Updates Widget</li>";
echo "<li>Conflict prevention (10-minute protection window)</li>";
echo "<li>Comprehensive logging and monitoring</li>";
echo "</ul>";

echo "<p><strong>Next steps:</strong></p>";
echo "<ol>";
echo "<li>Set up the cron job for automatic sync</li>";
echo "<li>Visit <a href='../merlion/admin-users.php'>Admin Users</a> to see the sync in action</li>";
echo "<li>Monitor sync activity in <a href='../merlion/appika-customer-updates-widget.php'>Customer Updates Widget</a></li>";
echo "</ol>";

echo "<style>";
echo "body { font-family: Arial, sans-serif; margin: 20px; }";
echo "h1, h2 { color: #473BF0; }";
echo "code { background-color: #f8f9fa; padding: 2px 4px; border-radius: 3px; }";
echo ".btn { display: inline-block; text-decoration: none; }";
echo ".btn:hover { opacity: 0.9; }";
echo "</style>";
?>
