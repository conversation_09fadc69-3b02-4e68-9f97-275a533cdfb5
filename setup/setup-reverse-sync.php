<?php
/**
 * Setup Script for Appika Reverse Sync
 * Run this once to set up the automatic sync system
 */

include('../functions/server.php');

echo "<h2>Setting up Appika Reverse Sync System</h2>\n";

// Step 1: Add database columns
echo "<h3>Step 1: Adding database columns...</h3>\n";

$columns = [
    "ALTER TABLE support_tickets
     ADD COLUMN IF NOT EXISTS last_appika_sync TIMESTAMP NULL DEFAULT NULL
     COMMENT 'Last time this ticket was synced from <PERSON>pp<PERSON>'",

    "ALTER TABLE support_tickets
     ADD COLUMN IF NOT EXISTS appika_updated_at TIMESTAMP NULL DEFAULT NULL
     COMMENT 'When this ticket was last updated FROM Appika'",

    "ALTER TABLE support_tickets
     ADD COLUMN IF NOT EXISTS appika_update_source VARCHAR(50) NULL DEFAULT NULL
     COMMENT 'Source of last Appika update (admin_name, system, etc)'"
];

foreach ($columns as $sql) {
    if (mysqli_query($conn, $sql)) {
        echo "✅ Database column added successfully<br>\n";
    } else {
        echo "❌ Error adding column: " . mysqli_error($conn) . "<br>\n";
    }
}

// Step 2: Add indexes
echo "<h3>Step 2: Adding database indexes...</h3>\n";

$indexes = [
    "CREATE INDEX IF NOT EXISTS idx_last_appika_sync ON support_tickets(last_appika_sync)",
    "CREATE INDEX IF NOT EXISTS idx_appika_id_sync ON support_tickets(appika_id, last_appika_sync)"
];

foreach ($indexes as $index) {
    if (mysqli_query($conn, $index)) {
        echo "✅ Index created successfully<br>\n";
    } else {
        echo "❌ Error creating index: " . mysqli_error($conn) . "<br>\n";
    }
}

// Step 3: Create logs directory
echo "<h3>Step 3: Creating logs directory...</h3>\n";

$logsDir = '../logs';
if (!file_exists($logsDir)) {
    if (mkdir($logsDir, 0755, true)) {
        echo "✅ Created logs directory<br>\n";
    } else {
        echo "❌ Failed to create logs directory<br>\n";
    }
} else {
    echo "✅ Logs directory already exists<br>\n";
}

// Step 4: Test sync functionality
echo "<h3>Step 4: Testing sync functionality...</h3>\n";

require_once '../functions/appika_reverse_sync.php';

// Find a ticket with Appika ID for testing
$testQuery = "SELECT id, appika_id FROM support_tickets WHERE appika_id IS NOT NULL AND appika_id != '' LIMIT 1";
$testResult = mysqli_query($conn, $testQuery);

if ($testTicket = mysqli_fetch_assoc($testResult)) {
    echo "Testing with ticket ID: {$testTicket['id']} (Appika ID: {$testTicket['appika_id']})<br>\n";
    
    $syncResult = syncFromAppika($testTicket['id']);
    
    if ($syncResult['success']) {
        echo "✅ Sync test successful: " . $syncResult['message'] . "<br>\n";
        if ($syncResult['updated']) {
            echo "🔄 Ticket was updated during test<br>\n";
        }
    } else {
        echo "❌ Sync test failed: " . $syncResult['message'] . "<br>\n";
    }
} else {
    echo "⚠️ No tickets with Appika ID found for testing<br>\n";
}

// Step 5: Display cron job instructions
echo "<h3>Step 5: Cron Job Setup Instructions</h3>\n";
echo "<div style='background: #f5f5f5; padding: 15px; border-radius: 5px; margin: 10px 0;'>\n";
echo "<strong>To enable automatic sync, add this to your crontab:</strong><br><br>\n";

$scriptPath = realpath('../merlion/sync-from-appika.php');
echo "<code># Run Appika sync every 10 minutes<br>\n";
echo "*/10 * * * * /usr/bin/php $scriptPath >> /var/log/appika-sync.log 2>&1</code><br><br>\n";

echo "<strong>To add this cron job:</strong><br>\n";
echo "1. Run: <code>crontab -e</code><br>\n";
echo "2. Add the line above<br>\n";
echo "3. Save and exit<br><br>\n";

echo "<strong>Alternative frequencies:</strong><br>\n";
echo "• Every 5 minutes: <code>*/5 * * * *</code><br>\n";
echo "• Every 15 minutes: <code>*/15 * * * *</code><br>\n";
echo "• Every hour: <code>0 * * * *</code><br>\n";
echo "</div>\n";

// Step 6: Manual sync test
echo "<h3>Step 6: Manual Sync Test</h3>\n";
echo "<div style='background: #e8f4fd; padding: 15px; border-radius: 5px; margin: 10px 0;'>\n";
echo "<strong>To test the sync manually, run:</strong><br>\n";
echo "<code>php $scriptPath</code><br><br>\n";
echo "This will check all recent tickets and sync any updates from Appika.<br>\n";
echo "</div>\n";

// Step 7: Summary
echo "<h3>✅ Setup Complete!</h3>\n";
echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>\n";
echo "<strong>Your Appika Reverse Sync system is now ready!</strong><br><br>\n";
echo "<strong>What happens now:</strong><br>\n";
echo "• When admins view tickets, the system checks for Appika updates<br>\n";
echo "• Every 10 minutes (once cron is set up), all tickets are checked<br>\n";
echo "• Updates from Appika are automatically applied to your database<br>\n";
echo "• Notifications show when tickets are updated from Appika<br><br>\n";
echo "<strong>Files created/modified:</strong><br>\n";
echo "• functions/appika_reverse_sync.php - Core sync functions<br>\n";
echo "• merlion/sync-from-appika.php - Scheduled sync script<br>\n";
echo "• merlion/admin-ticket-detail.php - Added real-time sync checks<br>\n";
echo "• logs/ directory - For sync operation logs<br>\n";
echo "</div>\n";

echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h2 { color: #333; border-bottom: 2px solid #007bff; padding-bottom: 10px; }
h3 { color: #555; margin-top: 25px; }
code { background: #f8f9fa; padding: 2px 5px; border-radius: 3px; font-family: monospace; }
</style>";
?>
