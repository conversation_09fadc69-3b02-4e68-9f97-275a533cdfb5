<?php
/**
 * Automatic Ticket Expiration Cleanup Cron Job
 * This script should be run periodically (e.g., daily) to clean up expired tickets
 * 
 * Add to crontab:
 * 0 2 * * * /usr/bin/php /path/to/your/project/cron/ticket-expiration-cleanup.php
 * (Runs daily at 2:00 AM)
 */

// Set script execution time limit
set_time_limit(300); // 5 minutes

// Include required files
require_once('../functions/server.php');
require_once('../config/ticket-expiration-config.php');
require_once('../functions/ticket-expiration-functions.php');

// Log file for cron job output
$log_file = '../logs/ticket-expiration-cleanup.log';

/**
 * Log message with timestamp
 */
function logMessage($message, $level = 'INFO') {
    global $log_file;
    $timestamp = date('Y-m-d H:i:s');
    $log_entry = "[$timestamp] [$level] $message\n";
    
    // Write to log file
    file_put_contents($log_file, $log_entry, FILE_APPEND | LOCK_EX);
    
    // Also output to console if running from command line
    if (php_sapi_name() === 'cli') {
        echo $log_entry;
    }
}

/**
 * Send email notification to admin
 */
function sendAdminNotification($subject, $message) {
    $config = getTicketExpirationConfig();
    $admin_email = $config['admin_notification_email'];
    
    if (empty($admin_email)) {
        return false;
    }
    
    $headers = [
        'From: HelloIT System <<EMAIL>>',
        'Reply-To: <EMAIL>',
        'Content-Type: text/html; charset=UTF-8',
        'X-Mailer: PHP/' . phpversion()
    ];
    
    $html_message = "
    <html>
    <head>
        <title>$subject</title>
    </head>
    <body>
        <h2>$subject</h2>
        <div style='font-family: Arial, sans-serif; line-height: 1.6;'>
            $message
        </div>
        <hr>
        <p style='color: #666; font-size: 12px;'>
            This is an automated message from the HelloIT Ticket Expiration System.<br>
            Generated at: " . date('Y-m-d H:i:s') . "
        </p>
    </body>
    </html>";
    
    return mail($admin_email, $subject, $html_message, implode("\r\n", $headers));
}

// Start cleanup process
logMessage("Starting ticket expiration cleanup process");

try {
    // Check if system is enabled
    $config = getTicketExpirationConfig();
    
    if (!$config['system_enabled']) {
        logMessage("Ticket expiration system is disabled. Skipping cleanup.", 'WARNING');
        exit(0);
    }
    
    if (!$config['auto_cleanup_enabled']) {
        logMessage("Automatic cleanup is disabled. Skipping cleanup.", 'INFO');
        exit(0);
    }
    
    // Check if cleanup is needed based on frequency
    if (!isCleanupNeeded()) {
        logMessage("Cleanup not needed yet based on frequency settings.", 'INFO');
        exit(0);
    }
    
    logMessage("Cleanup is needed. Starting process...");
    
    // Get expired tickets before cleanup for reporting
    $expired_tickets = getExpiredTickets();
    $expired_count = count($expired_tickets);
    
    if ($expired_count === 0) {
        logMessage("No expired tickets found. Nothing to clean up.");
        updateTicketExpirationConfig('last_cleanup_timestamp', time());
        exit(0);
    }
    
    logMessage("Found $expired_count expired ticket records to process");
    
    // Group expired tickets by user for reporting
    $user_summary = [];
    $total_expired_tickets = 0;
    
    foreach ($expired_tickets as $ticket) {
        $username = $ticket['username'];
        $ticket_type = $ticket['ticket_type'];
        $expired_qty = $ticket['remaining_tickets'];
        
        if (!isset($user_summary[$username])) {
            $user_summary[$username] = [];
        }
        
        if (!isset($user_summary[$username][$ticket_type])) {
            $user_summary[$username][$ticket_type] = 0;
        }
        
        $user_summary[$username][$ticket_type] += $expired_qty;
        $total_expired_tickets += $expired_qty;
    }
    
    // Perform the cleanup
    $cleanup_summary = removeExpiredTickets();
    
    if (empty($cleanup_summary)) {
        logMessage("Cleanup completed but no tickets were removed.", 'WARNING');
    } else {
        logMessage("Cleanup completed successfully. Removed $total_expired_tickets expired tickets from " . count($user_summary) . " user(s).");
        
        // Log details for each user
        foreach ($user_summary as $username => $types) {
            $user_details = [];
            foreach ($types as $type => $count) {
                $user_details[] = "$count $type";
            }
            logMessage("  - $username: " . implode(', ', $user_details));
        }
    }
    
    // Send admin notification if enabled
    if ($config['user_notifications_enabled'] && !empty($user_summary)) {
        $notification_subject = "Ticket Expiration Cleanup Report - $total_expired_tickets tickets removed";
        
        $notification_message = "<h3>Cleanup Summary</h3>";
        $notification_message .= "<p><strong>Total expired tickets removed:</strong> $total_expired_tickets</p>";
        $notification_message .= "<p><strong>Users affected:</strong> " . count($user_summary) . "</p>";
        
        $notification_message .= "<h4>Details by User:</h4>";
        $notification_message .= "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        $notification_message .= "<tr><th>Username</th><th>Ticket Type</th><th>Expired Quantity</th></tr>";
        
        foreach ($user_summary as $username => $types) {
            foreach ($types as $type => $count) {
                $notification_message .= "<tr>";
                $notification_message .= "<td>$username</td>";
                $notification_message .= "<td>" . ucfirst($type) . "</td>";
                $notification_message .= "<td>$count</td>";
                $notification_message .= "</tr>";
            }
        }
        
        $notification_message .= "</table>";
        
        if (sendAdminNotification($notification_subject, $notification_message)) {
            logMessage("Admin notification sent successfully");
        } else {
            logMessage("Failed to send admin notification", 'WARNING');
        }
    }
    
    // Check for tickets expiring soon and warn
    $expiring_soon = getTicketsExpiringSoon();
    if (!empty($expiring_soon)) {
        $expiring_count = count($expiring_soon);
        logMessage("Found $expiring_count ticket records expiring within {$config['warning_period_days']} days", 'WARNING');
        
        // Group by user for warning notification
        $warning_summary = [];
        foreach ($expiring_soon as $ticket) {
            $username = $ticket['username'];
            $days_left = getDaysUntilExpiration($ticket['purchase_time']);
            
            if (!isset($warning_summary[$username])) {
                $warning_summary[$username] = [
                    'tickets' => [],
                    'min_days' => $days_left
                ];
            }
            
            $warning_summary[$username]['tickets'][] = [
                'type' => $ticket['ticket_type'],
                'quantity' => $ticket['remaining_tickets'],
                'days_left' => $days_left
            ];
            
            $warning_summary[$username]['min_days'] = min($warning_summary[$username]['min_days'], $days_left);
        }
        
        // Send warning notification
        if ($config['user_notifications_enabled'] && !empty($warning_summary)) {
            $warning_subject = "Tickets Expiring Soon - " . count($warning_summary) . " users affected";
            
            $warning_message = "<h3>Tickets Expiring Soon</h3>";
            $warning_message .= "<p>The following users have tickets expiring within {$config['warning_period_days']} days:</p>";
            
            $warning_message .= "<table border='1' style='border-collapse: collapse; width: 100%;'>";
            $warning_message .= "<tr><th>Username</th><th>Ticket Type</th><th>Quantity</th><th>Days Until Expiration</th></tr>";
            
            foreach ($warning_summary as $username => $data) {
                foreach ($data['tickets'] as $ticket) {
                    $warning_message .= "<tr>";
                    $warning_message .= "<td>$username</td>";
                    $warning_message .= "<td>" . ucfirst($ticket['type']) . "</td>";
                    $warning_message .= "<td>{$ticket['quantity']}</td>";
                    $warning_message .= "<td>{$ticket['days_left']}</td>";
                    $warning_message .= "</tr>";
                }
            }
            
            $warning_message .= "</table>";
            
            if (sendAdminNotification($warning_subject, $warning_message)) {
                logMessage("Expiration warning notification sent successfully");
            } else {
                logMessage("Failed to send expiration warning notification", 'WARNING');
            }
        }
    }
    
    logMessage("Ticket expiration cleanup process completed successfully");
    
} catch (Exception $e) {
    logMessage("Error during cleanup process: " . $e->getMessage(), 'ERROR');
    
    // Send error notification to admin
    $error_subject = "Ticket Expiration Cleanup Error";
    $error_message = "<h3>Cleanup Process Failed</h3>";
    $error_message .= "<p><strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    $error_message .= "<p><strong>File:</strong> " . $e->getFile() . "</p>";
    $error_message .= "<p><strong>Line:</strong> " . $e->getLine() . "</p>";
    
    sendAdminNotification($error_subject, $error_message);
    
    exit(1); // Exit with error code
}

// Close database connection
if (isset($conn)) {
    mysqli_close($conn);
}

exit(0); // Exit successfully
?>
