<?php
/**
 * Test Script: Manually trigger expiration process for HC185
 */

// Include required files
require_once('../functions/server.php');
require_once('../functions/ticket-expiration-functions.php');

// Set content type for proper display
header('Content-Type: text/html; charset=UTF-8');

echo "<!DOCTYPE html>";
echo "<html><head><title>Test: Expiration Process</title>";
echo "<style>body{font-family:Arial,sans-serif;margin:40px;} .success{color:green;} .error{color:red;} .info{color:blue;} .warning{color:orange;} table{border-collapse:collapse;margin:10px 0;} th,td{border:1px solid #ccc;padding:8px;}</style>";
echo "</head><body>";

echo "<h1>Test: Expiration Process for HC185</h1>";
echo "<hr>";

$username = 'HC185';

// 1. Check current tickets before processing
echo "<h3>1. Current tickets for $username (before processing):</h3>";
$current_tickets_query = "SELECT * FROM purchasetickets WHERE username = '$username' ORDER BY purchase_time DESC";
$current_result = mysqli_query($conn, $current_tickets_query);

if ($current_result && mysqli_num_rows($current_result) > 0) {
    echo "<table>";
    echo "<tr><th>Type</th><th>Remaining</th><th>Purchase Date</th><th>Expiration Date</th><th>Status</th><th>Transaction ID</th></tr>";
    
    while ($ticket = mysqli_fetch_assoc($current_result)) {
        $expiration = new DateTime($ticket['expiration_date']);
        $now = new DateTime();
        $is_expired = $expiration < $now;
        $status = $is_expired ? 'EXPIRED' : 'ACTIVE';
        $status_class = $is_expired ? 'error' : 'success';
        
        echo "<tr>";
        echo "<td>{$ticket['ticket_type']}</td>";
        echo "<td>{$ticket['remaining_tickets']}</td>";
        echo "<td>{$ticket['purchase_time']}</td>";
        echo "<td>{$ticket['expiration_date']}</td>";
        echo "<td class='$status_class'><strong>$status</strong></td>";
        echo "<td>{$ticket['transactionid']}</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p class='warning'>No tickets found for $username</p>";
}

// 2. Check current expiration logs
echo "<h3>2. Current expiration logs for $username:</h3>";
$logs_query = "SELECT * FROM ticket_expiration_log WHERE username = '$username' ORDER BY cleanup_date DESC";
$logs_result = mysqli_query($conn, $logs_query);

if ($logs_result && mysqli_num_rows($logs_result) > 0) {
    echo "<p class='info'>Found " . mysqli_num_rows($logs_result) . " expiration log entries:</p>";
    echo "<table>";
    echo "<tr><th>Type</th><th>Expired Qty</th><th>Purchase Date</th><th>Expiration Date</th><th>Cleanup Date</th><th>Transaction ID</th></tr>";
    
    while ($log = mysqli_fetch_assoc($logs_result)) {
        echo "<tr>";
        echo "<td>{$log['ticket_type']}</td>";
        echo "<td>{$log['expired_quantity']}</td>";
        echo "<td>{$log['purchase_date']}</td>";
        echo "<td>{$log['expiration_date']}</td>";
        echo "<td>{$log['cleanup_date']}</td>";
        echo "<td>{$log['transaction_id']}</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p class='warning'>No expiration logs found for $username</p>";
}

// 3. Test the fixed getUserTicketCounts function
echo "<h3>3. Testing getUserTicketCounts function (this should create expiration logs):</h3>";

if (isset($_GET['run_process']) && $_GET['run_process'] === 'yes') {
    echo "<p class='info'>Running getUserTicketCounts for $username...</p>";
    
    try {
        $success = getUserTicketCounts($username);
        
        if ($success) {
            echo "<p class='success'>✅ getUserTicketCounts executed successfully</p>";
        } else {
            echo "<p class='error'>❌ getUserTicketCounts failed</p>";
        }
        
        // Check if new expiration logs were created
        echo "<h4>Checking for new expiration logs:</h4>";
        $new_logs_result = mysqli_query($conn, $logs_query);
        $new_log_count = mysqli_num_rows($new_logs_result);
        
        if ($new_log_count > 0) {
            echo "<p class='success'>✅ Found $new_log_count expiration log entries (some may be new)</p>";
            echo "<table>";
            echo "<tr><th>Type</th><th>Expired Qty</th><th>Purchase Date</th><th>Expiration Date</th><th>Cleanup Date</th><th>Transaction ID</th></tr>";
            
            while ($log = mysqli_fetch_assoc($new_logs_result)) {
                echo "<tr>";
                echo "<td>{$log['ticket_type']}</td>";
                echo "<td>{$log['expired_quantity']}</td>";
                echo "<td>{$log['purchase_date']}</td>";
                echo "<td>{$log['expiration_date']}</td>";
                echo "<td>{$log['cleanup_date']}</td>";
                echo "<td>{$log['transaction_id']}</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p class='warning'>⚠️ No expiration logs found after processing</p>";
        }
        
        // Check updated tickets
        echo "<h4>Updated tickets after processing:</h4>";
        $updated_result = mysqli_query($conn, $current_tickets_query);
        
        if ($updated_result && mysqli_num_rows($updated_result) > 0) {
            echo "<table>";
            echo "<tr><th>Type</th><th>Remaining</th><th>Purchase Date</th><th>Expiration Date</th><th>Expired At</th><th>Status</th></tr>";
            
            while ($ticket = mysqli_fetch_assoc($updated_result)) {
                $expiration = new DateTime($ticket['expiration_date']);
                $now = new DateTime();
                $is_expired = $expiration < $now;
                $status = $ticket['remaining_tickets'] == 0 ? 'PROCESSED' : ($is_expired ? 'EXPIRED' : 'ACTIVE');
                $status_class = $ticket['remaining_tickets'] == 0 ? 'info' : ($is_expired ? 'error' : 'success');
                
                echo "<tr>";
                echo "<td>{$ticket['ticket_type']}</td>";
                echo "<td>{$ticket['remaining_tickets']}</td>";
                echo "<td>{$ticket['purchase_time']}</td>";
                echo "<td>{$ticket['expiration_date']}</td>";
                echo "<td>" . ($ticket['expired_at'] ?? 'NULL') . "</td>";
                echo "<td class='$status_class'><strong>$status</strong></td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        
    } catch (Exception $e) {
        echo "<p class='error'>❌ Error: " . $e->getMessage() . "</p>";
    }
    
} else {
    echo "<p><a href='?run_process=yes' onclick='return confirm(\"Run expiration process for $username?\")'>Click here to run expiration process</a></p>";
    echo "<p class='info'>This will call getUserTicketCounts() which should automatically expire tickets and create expiration logs.</p>";
}

// 4. Manual cleanup option
echo "<h3>4. Manual cleanup option:</h3>";
if (isset($_GET['manual_cleanup']) && $_GET['manual_cleanup'] === 'yes') {
    echo "<p class='info'>Running manual cleanup...</p>";
    
    try {
        $cleanup_summary = removeExpiredTickets($username);
        
        if (!empty($cleanup_summary)) {
            echo "<p class='success'>✅ Manual cleanup completed</p>";
            echo "<p class='info'>Cleanup summary:</p>";
            echo "<ul>";
            foreach ($cleanup_summary as $summary) {
                echo "<li>{$summary['username']}: {$summary['total_expired']} {$summary['ticket_type']} tickets expired</li>";
            }
            echo "</ul>";
        } else {
            echo "<p class='warning'>⚠️ No expired tickets found to clean up</p>";
        }
        
    } catch (Exception $e) {
        echo "<p class='error'>❌ Error during manual cleanup: " . $e->getMessage() . "</p>";
    }
} else {
    echo "<p><a href='?manual_cleanup=yes' onclick='return confirm(\"Run manual cleanup for $username?\")'>Click here to run manual cleanup</a></p>";
    echo "<p class='info'>This will call removeExpiredTickets() to manually process expired tickets.</p>";
}

// Close database connection
mysqli_close($conn);

echo "</body></html>";
?>
