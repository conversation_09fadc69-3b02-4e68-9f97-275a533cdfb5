<?php
include('../functions/server.php');

echo "<h2>Test Expiration Logs</h2>";

// Insert some test expiration logs for HC185
$test_logs = [
    [
        'username' => 'HC185',
        'ticket_type' => 'starter',
        'expired_quantity' => 5,
        'purchase_date' => '2023-06-01 10:00:00',
        'expiration_date' => '2024-06-01 10:00:00',
        'cleanup_date' => '2025-06-23 12:00:00',
        'transaction_id' => 'TEST_EXP_001'
    ],
    [
        'username' => 'HC185',
        'ticket_type' => 'ultimate',
        'expired_quantity' => 3,
        'purchase_date' => '2023-08-15 14:30:00',
        'expiration_date' => '2024-08-15 14:30:00',
        'cleanup_date' => '2025-06-23 11:30:00',
        'transaction_id' => 'TEST_EXP_002'
    ],
    [
        'username' => 'HC185',
        'ticket_type' => 'premium',
        'expired_quantity' => 2,
        'purchase_date' => '2023-09-10 16:45:00',
        'expiration_date' => '2024-09-10 16:45:00',
        'cleanup_date' => '2025-06-23 10:15:00',
        'transaction_id' => 'TEST_EXP_003'
    ]
];

foreach ($test_logs as $log) {
    $insert_query = "INSERT INTO ticket_expiration_log 
                    (username, ticket_type, expired_quantity, purchase_date, expiration_date, cleanup_date, transaction_id) 
                    VALUES (?, ?, ?, ?, ?, ?, ?)";
    
    $stmt = mysqli_prepare($conn, $insert_query);
    mysqli_stmt_bind_param($stmt, 'ssissss', 
        $log['username'],
        $log['ticket_type'],
        $log['expired_quantity'],
        $log['purchase_date'],
        $log['expiration_date'],
        $log['cleanup_date'],
        $log['transaction_id']
    );
    
    if (mysqli_stmt_execute($stmt)) {
        echo "<p>✅ Inserted expiration log: {$log['expired_quantity']} {$log['ticket_type']} tickets expired for {$log['username']}</p>";
    } else {
        echo "<p>❌ Failed to insert log: " . mysqli_stmt_error($stmt) . "</p>";
    }
    mysqli_stmt_close($stmt);
}

echo "<hr>";
echo "<h3>Next Steps:</h3>";
echo "<ol>";
echo "<li><a href='../front-end/my-ticket-log.php'>Check My Ticket Logs</a> - Should now show expiration entries</li>";
echo "<li>Search for 'expired' to filter only expiration logs</li>";
echo "</ol>";

mysqli_close($conn);
?>
