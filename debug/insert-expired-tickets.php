<?php
include('../functions/server.php');

echo "<h2>Insert Expired Test Tickets</h2>";

// Insert tickets with past dates for testing
$test_tickets = [
    [
        'username' => 'HC185',
        'ticket_type' => 'starter',
        'quantity' => 5,
        'purchase_date' => '2023-06-01 10:00:00', // Over 1 year ago
        'expiration_date' => '2024-06-01 10:00:00' // Already expired
    ],
    [
        'username' => 'HC185', 
        'ticket_type' => 'ultimate',
        'quantity' => 3,
        'purchase_date' => '2023-08-15 14:30:00', // Over 1 year ago
        'expiration_date' => '2024-08-15 14:30:00' // Already expired
    ],
    [
        'username' => 'HC185',
        'ticket_type' => 'starter', 
        'quantity' => 2,
        'purchase_date' => '2024-12-01 09:00:00', // Recent but set to expire soon
        'expiration_date' => '2025-06-20 09:00:00' // Expires in a few days
    ]
];

foreach ($test_tickets as $ticket) {
    $insert_query = "INSERT INTO purchasetickets 
                    (username, ticket_type, package_size, numbers_per_package, dollar_price_per_package, 
                     purchase_time, transactionid, remaining_tickets, expiration_date) 
                    VALUES (?, ?, 'Test Package', ?, 99.99, ?, ?, ?, ?)";
    
    $stmt = mysqli_prepare($conn, $insert_query);
    $transaction_id = 'MANUAL_TEST_' . time() . '_' . rand(1000, 9999);
    
    mysqli_stmt_bind_param($stmt, 'ssissis', 
        $ticket['username'],
        $ticket['ticket_type'],
        $ticket['quantity'],
        $ticket['purchase_date'],
        $transaction_id,
        $ticket['quantity'],
        $ticket['expiration_date']
    );
    
    if (mysqli_stmt_execute($stmt)) {
        echo "<p>✅ Inserted {$ticket['quantity']} {$ticket['ticket_type']} tickets for {$ticket['username']} (Purchase: {$ticket['purchase_date']}, Expires: {$ticket['expiration_date']})</p>";
    } else {
        echo "<p>❌ Failed to insert ticket: " . mysqli_stmt_error($stmt) . "</p>";
    }
    mysqli_stmt_close($stmt);
}

echo "<hr>";
echo "<h3>Next Steps:</h3>";
echo "<ol>";
echo "<li><a href='../merlion/ticket-expiration-manager.php'>Go to Expiration Manager</a></li>";
echo "<li>Click 'Run Cleanup Now' to remove expired tickets</li>";
echo "<li>Check the expiration log to see what was removed</li>";
echo "</ol>";

mysqli_close($conn);
?>
