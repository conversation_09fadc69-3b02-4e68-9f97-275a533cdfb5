<?php
include('../functions/server.php');

echo "<h2>Checking purchasetickets Table Structure</h2>";

// Show table structure
$describe_query = "DESCRIBE purchasetickets";
$result = mysqli_query($conn, $describe_query);

if ($result) {
    echo "<h3>Table Columns:</h3>";
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    
    while ($row = mysqli_fetch_assoc($result)) {
        echo "<tr>";
        echo "<td>" . $row['Field'] . "</td>";
        echo "<td>" . $row['Type'] . "</td>";
        echo "<td>" . $row['Null'] . "</td>";
        echo "<td>" . $row['Key'] . "</td>";
        echo "<td>" . ($row['Default'] ?? 'NULL') . "</td>";
        echo "<td>" . $row['Extra'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "Error: " . mysqli_error($conn);
}

// Show sample data
echo "<h3>Sample Data (first 5 rows):</h3>";
$sample_query = "SELECT * FROM purchasetickets LIMIT 5";
$sample_result = mysqli_query($conn, $sample_query);

if ($sample_result && mysqli_num_rows($sample_result) > 0) {
    echo "<table border='1' style='border-collapse: collapse;'>";
    
    // Get column names
    $fields = mysqli_fetch_fields($sample_result);
    echo "<tr>";
    foreach ($fields as $field) {
        echo "<th>" . $field->name . "</th>";
    }
    echo "</tr>";
    
    // Show data
    while ($row = mysqli_fetch_assoc($sample_result)) {
        echo "<tr>";
        foreach ($row as $value) {
            echo "<td>" . htmlspecialchars($value ?? 'NULL') . "</td>";
        }
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>No data found in purchasetickets table.</p>";
}

mysqli_close($conn);
?>
