<?php
/**
 * Test Admin User Creation
 * Debug why admin user creation might not be adding users to Appika
 */

// Auto-detect environment for file paths
$is_localhost = (strpos($_SERVER['HTTP_HOST'], 'localhost') !== false || strpos($_SERVER['HTTP_HOST'], '127.0.0.1') !== false);
$file_base_path = $is_localhost ? '/helloit' : '';

require_once $_SERVER['DOCUMENT_ROOT'] . $file_base_path . '/vendor/autoload.php';
require_once $_SERVER['DOCUMENT_ROOT'] . $file_base_path . '/functions/server.php';
require_once $_SERVER['DOCUMENT_ROOT'] . $file_base_path . '/config/api-config.php';
require_once $_SERVER['DOCUMENT_ROOT'] . $file_base_path . '/functions/create-customer-minimal.php';

echo "<h1>Test Admin User Creation</h1>";
echo "<p>Testing why admin user creation might not be adding users to Appika...</p>";

// Test 1: Check if createCustomerMinimal function exists
echo "<h2>1. Function Availability Test</h2>";
if (function_exists('createCustomerMinimal')) {
    echo "✅ createCustomerMinimal function exists<br>";
} else {
    echo "❌ createCustomerMinimal function does not exist<br>";
    exit;
}

if (function_exists('createCustomerInAppika')) {
    echo "✅ createCustomerInAppika function exists<br>";
} else {
    echo "❌ createCustomerInAppika function does not exist<br>";
}

// Test 2: Test API Configuration
echo "<h2>2. API Configuration Test</h2>";
try {
    $apiConfig = getCustomerApiConfig();
    echo "✅ API config loaded successfully<br>";
    echo "Endpoint: " . $apiConfig['endpoint'] . "<br>";
    echo "Path: " . $apiConfig['path'] . "<br>";
    echo "Key: " . substr($apiConfig['key'], 0, 10) . "...<br>";
} catch (Exception $e) {
    echo "❌ Error loading API config: " . $e->getMessage() . "<br>";
}

// Test 3: Test Guzzle Availability
echo "<h2>3. Guzzle Availability Test</h2>";
if (class_exists('GuzzleHttp\Client')) {
    echo "✅ GuzzleHttp\Client class exists<br>";
    
    try {
        $client = new \GuzzleHttp\Client([
            'timeout' => 8,
            'http_errors' => false,
        ]);
        echo "✅ Guzzle client can be instantiated<br>";
    } catch (Exception $e) {
        echo "❌ Error creating Guzzle client: " . $e->getMessage() . "<br>";
    }
} else {
    echo "❌ GuzzleHttp\Client class does not exist<br>";
}

// Test 4: Test Database Connection
echo "<h2>4. Database Connection Test</h2>";
if (isset($conn) && $conn) {
    echo "✅ Database connection available<br>";
} else {
    echo "❌ Database connection not available<br>";
}

// Test 5: Simulate Admin User Creation (DRY RUN)
echo "<h2>5. Simulate Admin User Creation (Dry Run)</h2>";

// Prepare test data (same structure as admin-users.php)
$customerData = [
    'username' => 'temp_test_' . uniqid(),
    'email' => '<EMAIL>',
    'password' => 'testpass123',
    'full_name' => 'Test User Admin',
    'address' => 'Test Address',
    'address2' => '',
    'city' => 'Test City',
    'state' => 'Test State',
    'country' => 'TH',
    'postal_code' => '12345',
    'phone' => '1234567890',
    'company_name' => 'Test Company',
    'tax_id' => '',
    'district' => '',
    'timezone' => 'Asia/Bangkok',
    'stripe_customer_id' => null
];

echo "Test data prepared:<br>";
echo "<pre>" . json_encode($customerData, JSON_PRETTY_PRINT) . "</pre>";

// Test 6: Check what happens in createCustomerMinimal
echo "<h2>6. Test createCustomerMinimal Function</h2>";

// Don't actually create a user, just test the function logic
echo "⚠️ Skipping actual user creation to avoid test data<br>";
echo "But we can test the Appika API connection part...<br>";

// Test 7: Test Appika API Connection Directly
echo "<h2>7. Test Appika API Connection</h2>";
try {
    $apiConfig = getCustomerApiConfig();
    $client = new \GuzzleHttp\Client([
        'base_uri' => $apiConfig['endpoint'],
        'timeout' => 8,
        'http_errors' => false,
    ]);
    
    // Test GET request to see if API is accessible
    $response = $client->request('GET', $apiConfig['path'], [
        'headers' => [
            'X-api-key' => $apiConfig['key'],
            'Accept' => 'application/json',
        ],
        'query' => ['limit' => 1]
    ]);
    
    $statusCode = $response->getStatusCode();
    echo "API Test Status: " . $statusCode . "<br>";
    
    if ($statusCode == 200) {
        echo "✅ Appika API is accessible<br>";
    } elseif ($statusCode == 401) {
        echo "❌ Appika API authentication failed (401)<br>";
    } else {
        echo "⚠️ Appika API returned status: " . $statusCode . "<br>";
    }
    
} catch (Exception $e) {
    echo "❌ Error testing Appika API: " . $e->getMessage() . "<br>";
}

// Test 8: Check Recent Error Logs
echo "<h2>8. Check Recent Error Logs</h2>";
$log_files = [
    'logs/appika_debug.log',
    'logs/appika_api.log',
    'logs/error.log'
];

foreach ($log_files as $log_file) {
    $full_path = $_SERVER['DOCUMENT_ROOT'] . $file_base_path . '/' . $log_file;
    if (file_exists($full_path)) {
        echo "📄 Found $log_file<br>";
        $lines = file($full_path);
        $recent_lines = array_slice($lines, -5); // Last 5 lines
        
        echo "<details><summary>Recent entries from $log_file</summary>";
        echo "<pre style='background: #f5f5f5; padding: 10px; max-height: 150px; overflow-y: auto;'>";
        foreach ($recent_lines as $line) {
            echo htmlspecialchars($line);
        }
        echo "</pre></details>";
    } else {
        echo "❌ $log_file not found at $full_path<br>";
    }
}

// Test 9: Check PHP Error Log
echo "<h2>9. Check PHP Error Log</h2>";
$php_error_log = ini_get('error_log');
if ($php_error_log && file_exists($php_error_log)) {
    echo "📄 PHP Error Log: $php_error_log<br>";
    $lines = file($php_error_log);
    $recent_lines = array_slice($lines, -10); // Last 10 lines
    
    echo "<details><summary>Recent PHP errors</summary>";
    echo "<pre style='background: #f5f5f5; padding: 10px; max-height: 200px; overflow-y: auto;'>";
    foreach ($recent_lines as $line) {
        if (stripos($line, 'admin') !== false || stripos($line, 'appika') !== false || stripos($line, 'guzzle') !== false) {
            echo "<strong>" . htmlspecialchars($line) . "</strong>";
        } else {
            echo htmlspecialchars($line);
        }
    }
    echo "</pre></details>";
} else {
    echo "❌ PHP Error Log not found or not accessible<br>";
}

echo "<h2>Possible Issues</h2>";
echo "<ol>";
echo "<li><strong>Path Issues:</strong> If createCustomerMinimal can't be loaded properly</li>";
echo "<li><strong>API Authentication:</strong> If Appika API returns 401 errors</li>";
echo "<li><strong>Silent Failures:</strong> If errors are not being logged properly</li>";
echo "<li><strong>Database Issues:</strong> If local user creation fails, Appika creation won't happen</li>";
echo "<li><strong>Function Logic:</strong> If createCustomerMinimal has bugs in the Appika integration part</li>";
echo "</ol>";

echo "<h2>Next Steps</h2>";
echo "<ol>";
echo "<li>Try creating a user in admin-users.php and check the browser console for errors</li>";
echo "<li>Check the PHP error logs immediately after attempting user creation</li>";
echo "<li>Look for specific error messages in the admin interface</li>";
echo "<li>Test if the same issue happens with post-purchase user creation</li>";
echo "</ol>";

echo "<h2>Debug Commands</h2>";
echo "<p>To debug admin user creation:</p>";
echo "<ol>";
echo "<li>Open browser developer tools (F12)</li>";
echo "<li>Go to Network tab</li>";
echo "<li>Try creating a user in admin-users.php</li>";
echo "<li>Check the AJAX response for error messages</li>";
echo "<li>Look for any failed network requests</li>";
echo "</ol>";
?>
