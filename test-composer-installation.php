<?php
/**
 * Test Composer Installation
 * Simple test to verify if Composer dependencies are properly installed
 */

echo "<h1>Composer Installation Test</h1>";
echo "<p>Testing if Composer dependencies are properly installed...</p>";

// Test 1: Check if composer.json exists
echo "<h2>1. Composer Configuration</h2>";
if (file_exists('composer.json')) {
    echo "✅ composer.json exists<br>";
    $composer_content = file_get_contents('composer.json');
    $composer_data = json_decode($composer_content, true);
    
    if ($composer_data && isset($composer_data['require'])) {
        echo "✅ composer.json is valid<br>";
        echo "Required packages:<br>";
        foreach ($composer_data['require'] as $package => $version) {
            echo "&nbsp;&nbsp;- $package: $version<br>";
        }
    } else {
        echo "❌ composer.json is invalid or missing requirements<br>";
    }
} else {
    echo "❌ composer.json not found<br>";
}

// Test 2: Check vendor directory
echo "<h2>2. Vendor Directory</h2>";
if (is_dir('vendor')) {
    echo "✅ vendor directory exists<br>";
    
    // Check key files
    $key_files = [
        'vendor/autoload.php',
        'vendor/composer/installed.json',
        'vendor/guzzlehttp/guzzle/composer.json'
    ];
    
    foreach ($key_files as $file) {
        if (file_exists($file)) {
            echo "✅ $file exists<br>";
        } else {
            echo "❌ $file missing<br>";
        }
    }
} else {
    echo "❌ vendor directory not found<br>";
}

// Test 3: Try to include autoloader
echo "<h2>3. Autoloader Test</h2>";
if (file_exists('vendor/autoload.php')) {
    try {
        require_once 'vendor/autoload.php';
        echo "✅ Autoloader included successfully<br>";
        
        // Test if Guzzle class exists
        if (class_exists('GuzzleHttp\Client')) {
            echo "✅ GuzzleHttp\Client class is available<br>";
            
            // Try to create an instance
            try {
                $client = new GuzzleHttp\Client(['timeout' => 10]);
                echo "✅ Successfully created Guzzle client instance<br>";
            } catch (Exception $e) {
                echo "❌ Error creating Guzzle client: " . $e->getMessage() . "<br>";
            }
        } else {
            echo "❌ GuzzleHttp\Client class not found<br>";
        }
    } catch (Exception $e) {
        echo "❌ Error including autoloader: " . $e->getMessage() . "<br>";
    }
} else {
    echo "❌ vendor/autoload.php not found<br>";
}

// Test 4: Check installed packages
echo "<h2>4. Installed Packages</h2>";
if (file_exists('vendor/composer/installed.json')) {
    $installed_content = file_get_contents('vendor/composer/installed.json');
    $installed_data = json_decode($installed_content, true);
    
    if ($installed_data) {
        echo "✅ installed.json is valid<br>";
        
        // Look for Guzzle
        $guzzle_found = false;
        $packages = $installed_data['packages'] ?? $installed_data;
        
        foreach ($packages as $package) {
            if (isset($package['name']) && $package['name'] === 'guzzlehttp/guzzle') {
                $guzzle_found = true;
                echo "✅ Guzzle package found: version " . ($package['version'] ?? 'unknown') . "<br>";
                break;
            }
        }
        
        if (!$guzzle_found) {
            echo "❌ Guzzle package not found in installed packages<br>";
        }
        
        echo "Total packages installed: " . count($packages) . "<br>";
    } else {
        echo "❌ installed.json is invalid<br>";
    }
} else {
    echo "❌ installed.json not found<br>";
}

// Test 5: PHP Environment
echo "<h2>5. PHP Environment</h2>";
echo "PHP Version: " . PHP_VERSION . "<br>";
echo "Operating System: " . PHP_OS . "<br>";

// Check required extensions
$required_extensions = ['curl', 'json', 'mbstring', 'openssl'];
foreach ($required_extensions as $ext) {
    if (extension_loaded($ext)) {
        echo "✅ $ext extension loaded<br>";
    } else {
        echo "❌ $ext extension NOT loaded<br>";
    }
}

// Test 6: Simple HTTP test (if Guzzle is available)
echo "<h2>6. HTTP Test</h2>";
if (class_exists('GuzzleHttp\Client')) {
    try {
        $client = new GuzzleHttp\Client(['timeout' => 5]);
        $response = $client->request('GET', 'https://httpbin.org/get', [
            'http_errors' => false
        ]);
        
        echo "✅ HTTP test successful (status: " . $response->getStatusCode() . ")<br>";
    } catch (Exception $e) {
        echo "⚠️ HTTP test failed: " . $e->getMessage() . "<br>";
    }
} else {
    echo "❌ Cannot test HTTP - Guzzle not available<br>";
}

echo "<h2>Recommendations</h2>";
echo "<p>If you see any ❌ marks above, try the following:</p>";
echo "<ol>";
echo "<li>Run <code>composer install</code> in your project directory</li>";
echo "<li>Make sure all required PHP extensions are installed</li>";
echo "<li>Check file permissions on the vendor directory</li>";
echo "<li>Verify your PHP version is compatible with Guzzle</li>";
echo "</ol>";
?>
