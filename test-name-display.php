<?php
/**
 * Test script to verify name display functionality
 * This script tests how names are fetched and displayed using the customer data service
 */

require_once 'functions/server.php';
require_once 'functions/customer-data-service.php';

echo "<h1>Name Display Test</h1>";

// Test 1: Get a sample user
$testQuery = "SELECT username FROM user LIMIT 1";
$testResult = mysqli_query($conn, $testQuery);

if ($testResult && mysqli_num_rows($testResult) > 0) {
    $testUser = mysqli_fetch_assoc($testResult);
    $username = $testUser['username'];
    
    echo "<h2>Testing for user: " . htmlspecialchars($username) . "</h2>";
    
    // Test 2: Get complete customer data
    $completeData = getCompleteCustomerData($username);
    
    if ($completeData) {
        echo "<h3>✅ Complete Customer Data Retrieved</h3>";
        echo "<div style='background: #f0f8ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<p><strong>Username:</strong> " . htmlspecialchars($completeData['username']) . "</p>";
        echo "<p><strong>Email:</strong> " . htmlspecialchars($completeData['email']) . "</p>";
        echo "<p><strong>Has Appika Data:</strong> " . ($completeData['has_appika_data'] ? 'Yes' : 'No') . "</p>";
        
        if ($completeData['has_appika_data']) {
            echo "<p><strong>Name from Appika:</strong> " . htmlspecialchars($completeData['name'] ?: 'Not set') . "</p>";
        }
        
        echo "<p><strong>Display Name (getCustomerDisplayName):</strong> <span style='color: green; font-weight: bold;'>" . htmlspecialchars(getCustomerDisplayName($completeData)) . "</span></p>";
        echo "</div>";
        
        // Test 3: Simulate ticket creation variables
        echo "<h3>🎫 Ticket Creation Test</h3>";
        echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<p><strong>Name that would be sent to Appika:</strong> " . htmlspecialchars(getCustomerDisplayName($completeData)) . "</p>";
        echo "<p><strong>Email that would be sent to Appika:</strong> " . htmlspecialchars($completeData['email']) . "</p>";
        echo "</div>";
        
    } else {
        echo "<h3>❌ Failed to retrieve complete customer data</h3>";
    }
    
    // Test 4: Test getLocalUserByEmail function (admin function)
    echo "<h3>👨‍💼 Admin Function Test</h3>";
    if ($completeData && !empty($completeData['email'])) {
        $adminUserData = getLocalUserByEmail($completeData['email']);
        
        if ($adminUserData) {
            echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
            echo "<p><strong>Admin Function Result:</strong></p>";
            echo "<p><strong>Display Name:</strong> " . htmlspecialchars(getCustomerDisplayName($adminUserData)) . "</p>";
            echo "<p><strong>Has Appika Data:</strong> " . ($adminUserData['has_appika_data'] ? 'Yes' : 'No') . "</p>";
            echo "</div>";
        } else {
            echo "<p>❌ Admin function failed to retrieve user data</p>";
        }
    } else {
        echo "<p>⚠️ Cannot test admin function - no email available</p>";
    }
    
} else {
    echo "<h2>❌ No users found in database</h2>";
    echo "<p>Please ensure there are users in the database to test with.</p>";
}

echo "<hr>";
echo "<h2>📋 Summary</h2>";
echo "<p>This test verifies that:</p>";
echo "<ul>";
echo "<li>✅ Customer data service is working</li>";
echo "<li>✅ Names are properly fetched from Appika when available</li>";
echo "<li>✅ getCustomerDisplayName function returns the correct name</li>";
echo "<li>✅ Admin functions can retrieve complete customer data</li>";
echo "<li>✅ Ticket creation will use the proper name from Appika</li>";
echo "</ul>";

echo "<p><strong>Note:</strong> If the name shows as username or email, it means either:</p>";
echo "<ul>";
echo "<li>The user doesn't have Appika data yet</li>";
echo "<li>The name field in Appika is empty</li>";
echo "<li>There's an issue with the Appika API connection</li>";
echo "</ul>";
?> 