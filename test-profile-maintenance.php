<?php
// Test profile maintenance behavior
include('functions/appika-status-checker.php');

echo "<h2>🧪 Profile Maintenance Test</h2>";
echo "<p><strong>Current time:</strong> " . date('Y-m-d H:i:s') . "</p>";

// Test the status checker
$isOnline = isAppikaOnline();
echo "<h3>API Status Check:</h3>";
echo "<p><strong>Is Appika Online?</strong> " . ($isOnline ? "<span style='color: green;'>YES</span>" : "<span style='color: red;'>NO</span>") . "</p>";

if (!$isOnline) {
    echo "<div style='background: #ffebee; border: 1px solid #f44336; color: #c62828; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h4>🚨 Expected Profile Behavior (Maintenance Mode):</h4>";
    echo "<ul>";
    echo "<li>✅ Orange warning banner should appear at top</li>";
    echo "<li>✅ Edit Profile button should be disabled (grey with lock icon)</li>";
    echo "<li>✅ Profile data should show from local database only</li>";
    echo "<li>✅ Warning about data loss prevention</li>";
    echo "</ul>";
    echo "</div>";
} else {
    echo "<div style='background: #e8f5e8; border: 1px solid #4caf50; color: #2e7d32; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h4>✅ Expected Profile Behavior (Normal Mode):</h4>";
    echo "<ul>";
    echo "<li>✅ No warning banner</li>";
    echo "<li>✅ Edit Profile button should be enabled (blue)</li>";
    echo "<li>✅ Profile data should show from Appika API</li>";
    echo "<li>✅ Full functionality available</li>";
    echo "</ul>";
    echo "</div>";
}

echo "<h3>🔗 Test Links:</h3>";
echo "<ul>";
echo "<li><a href='front-end/profile.php' target='_blank' style='color: #007bff; text-decoration: none; padding: 5px 10px; background: #e7f3ff; border-radius: 4px;'>👤 Open Profile Page</a></li>";
echo "<li><a href='test-maintenance-toggle.php' target='_blank' style='color: #007bff; text-decoration: none; padding: 5px 10px; background: #e7f3ff; border-radius: 4px;'>🔧 Toggle Maintenance Mode</a></li>";
echo "</ul>";

echo "<hr>";
echo "<h3>🔧 Quick Toggle:</h3>";
echo "<p>";
if (!$isOnline) {
    echo "<a href='?action=disable' style='background: #4caf50; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>✅ Disable Maintenance Mode</a>";
} else {
    echo "<a href='?action=enable' style='background: #f44336; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🚨 Enable Maintenance Mode</a>";
}
echo "</p>";

// Handle toggle actions
if (isset($_GET['action'])) {
    $statusFile = 'functions/appika-status-checker.php';
    $content = file_get_contents($statusFile);
    
    if ($_GET['action'] === 'enable') {
        $content = str_replace(
            '// Auto-detect server status - no manual override needed',
            '// TESTING: Force maintenance mode
    return false; // Remove this line to restore normal operation
    // Auto-detect server status - no manual override needed',
            $content
        );
        file_put_contents($statusFile, $content);
        echo "<script>setTimeout(function(){ window.location.href = window.location.href.split('?')[0]; }, 1000);</script>";
        echo "<p style='color: green;'>✅ Maintenance mode enabled! Refreshing...</p>";
    } elseif ($_GET['action'] === 'disable') {
        $content = str_replace(
            '// TESTING: Force maintenance mode
    return false; // Remove this line to restore normal operation
    // Auto-detect server status - no manual override needed',
            '// Auto-detect server status - no manual override needed',
            $content
        );
        file_put_contents($statusFile, $content);
        echo "<script>setTimeout(function(){ window.location.href = window.location.href.split('?')[0]; }, 1000);</script>";
        echo "<p style='color: green;'>✅ Maintenance mode disabled! Refreshing...</p>";
    }
}
?>
