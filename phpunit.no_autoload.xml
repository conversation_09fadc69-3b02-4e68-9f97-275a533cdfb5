<phpunit
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:noNamespaceSchemaLocation="vendor/phpunit/phpunit/phpunit.xsd"
    bootstrap="tests/bootstrap.no_autoload.php"
    colors="true"
>
    <php>
      <env name="IS_RUNNING_PHPUNIT" value="true"/>
    </php>
    <testsuites>
        <testsuite name="Stripe PHP Test Suite">
            <directory suffix="Test.php">tests</directory>
        </testsuite>
    </testsuites>
    <filter>
        <whitelist>
            <directory>lib</directory>
        </whitelist>
    </filter>
    <logging>
        <log type="coverage-clover" target="clover.xml"/>
    </logging>
</phpunit>
