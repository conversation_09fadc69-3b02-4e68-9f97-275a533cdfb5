<?php
/**
 * Test if Guzzle is actually working by using the same method as GraphQL functions
 */

echo "<h1>Test Guzzle Working (Same as GraphQL)</h1>";
echo "<p>Testing Guzzle using the exact same method as the working GraphQL functions...</p>";

// Test 1: Include the same files as GraphQL functions
echo "<h2>1. Include Files Test</h2>";
try {
    require_once __DIR__ . '/vendor/autoload.php';
    echo "✅ Autoloader included successfully<br>";
    
    require_once __DIR__ . '/config/api-config.php';
    echo "✅ API config included successfully<br>";
    
} catch (Exception $e) {
    echo "❌ Error including files: " . $e->getMessage() . "<br>";
    exit;
}

// Test 2: Test Guzzle class (same as GraphQL functions)
echo "<h2>2. Guzzle Class Test (GraphQL Method)</h2>";
if (class_exists('GuzzleHttp\Client')) {
    echo "✅ GuzzleHttp\Client class exists<br>";
    
    try {
        // Create client exactly like GraphQL functions do
        $client = new \GuzzleHttp\Client([
            'timeout' => 8,
            'connect_timeout' => 5,
            'http_errors' => false,
        ]);
        echo "✅ Guzzle client created successfully (GraphQL style)<br>";
        
    } catch (Exception $e) {
        echo "❌ Error creating Guzzle client: " . $e->getMessage() . "<br>";
    }
} else {
    echo "❌ GuzzleHttp\Client class does not exist<br>";
}

// Test 3: Test GraphQL function directly
echo "<h2>3. Test GraphQL Function Directly</h2>";
try {
    require_once __DIR__ . '/functions/graphql_functions.php';
    echo "✅ GraphQL functions included successfully<br>";
    
    // Test a simple GraphQL query
    $query = '
    query {
        getTickets {
            id
            subject
        }
    }';
    
    echo "🧪 Testing GraphQL request...<br>";
    $result = makeGraphQLRequest($query, []);
    
    if ($result['success']) {
        echo "✅ GraphQL request successful!<br>";
        echo "Status: " . $result['status'] . "<br>";
        echo "Data count: " . (isset($result['data']['data']['getTickets']) ? count($result['data']['data']['getTickets']) : 'N/A') . "<br>";
    } else {
        echo "⚠️ GraphQL request failed: " . $result['error'] . "<br>";
        echo "Status: " . $result['status'] . "<br>";
    }
    
} catch (Exception $e) {
    echo "❌ Error testing GraphQL: " . $e->getMessage() . "<br>";
}

// Test 4: Test Customer API (same as user creation)
echo "<h2>4. Test Customer API (User Creation Method)</h2>";
try {
    // Get API configuration
    $apiConfig = getCustomerApiConfig();
    $apiEndpoint = $apiConfig['endpoint'];
    $apiPath = $apiConfig['path'];
    $apiKey = $apiConfig['key'];
    
    echo "✅ Customer API config loaded<br>";
    echo "Endpoint: " . $apiEndpoint . "<br>";
    echo "Path: " . $apiPath . "<br>";
    echo "Key: " . substr($apiKey, 0, 10) . "...<br>";
    
    // Create Guzzle client (same as user creation)
    $client = new \GuzzleHttp\Client([
        'base_uri' => $apiEndpoint,
        'timeout' => 30,
        'http_errors' => false,
    ]);
    echo "✅ Customer API Guzzle client created successfully<br>";
    
    // Test a simple GET request to the customer API
    $response = $client->request('GET', $apiPath, [
        'headers' => [
            'X-api-key' => "{$apiKey}",
            'Accept' => 'application/json',
            'Content-Type' => 'application/json',
        ],
        'query' => [
            'limit' => 1
        ]
    ]);
    
    echo "✅ Customer API request completed<br>";
    echo "Status Code: " . $response->getStatusCode() . "<br>";
    
    if ($response->getStatusCode() == 200) {
        echo "✅ Customer API connection successful!<br>";
    } else {
        echo "⚠️ Customer API returned status " . $response->getStatusCode() . "<br>";
    }
    
} catch (Exception $e) {
    echo "❌ Error testing Customer API: " . $e->getMessage() . "<br>";
}

// Test 5: Test createCustomerMinimal function
echo "<h2>5. Test createCustomerMinimal Function</h2>";
try {
    require_once __DIR__ . '/functions/create-customer-minimal.php';
    echo "✅ createCustomerMinimal function included<br>";
    
    if (function_exists('createCustomerMinimal')) {
        echo "✅ createCustomerMinimal function exists<br>";
    } else {
        echo "❌ createCustomerMinimal function does not exist<br>";
    }
    
    if (function_exists('createCustomerInAppika')) {
        echo "✅ createCustomerInAppika function exists<br>";
    } else {
        echo "❌ createCustomerInAppika function does not exist<br>";
    }
    
} catch (Exception $e) {
    echo "❌ Error including createCustomerMinimal: " . $e->getMessage() . "<br>";
}

echo "<h2>Summary</h2>";
echo "<p>This test uses the exact same methods as your working GraphQL functions.</p>";
echo "<p>If GraphQL works but user creation doesn't, the issue is likely:</p>";
echo "<ul>";
echo "<li>Different file paths or include contexts</li>";
echo "<li>Different error handling</li>";
echo "<li>API endpoint differences</li>";
echo "<li>Timing or server load issues</li>";
echo "</ul>";
?>
