-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: 127.0.0.1
-- Generation Time: Jun 29, 2025 at 06:07 PM
-- Server version: 10.4.32-MariaDB
-- PHP Version: 8.2.12

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `helloit88_db`
--

-- --------------------------------------------------------

--
-- Table structure for table `admin_logs`
--

CREATE TABLE `admin_logs` (
  `id` int(11) NOT NULL,
  `admin_id` int(11) NOT NULL,
  `action` varchar(50) NOT NULL,
  `description` text NOT NULL,
  `created_at` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `admin_logs`
--

INSERT INTO `admin_logs` (`id`, `admin_id`, `action`, `description`, `created_at`) VALUES
(1, 1, 'delete_user', 'Admin deleted user: dev01 (ID: 12)', '2025-05-06 15:52:43'),
(2, 1, 'delete_user', 'Admin deleted user: watcharapolAdmin (ID: 10)', '2025-05-06 15:52:57'),
(3, 1, 'delete_user', 'Admin deleted user: watcharapol (ID: 5)', '2025-05-06 15:53:08'),
(4, 1, 'delete_user', 'Admin deleted user: watcahrapol (ID: 11)', '2025-05-06 15:58:27'),
(5, 1, 'delete_user', 'Admin deleted user: dev03 (ID: 15)', '2025-05-06 17:57:01'),
(6, 1, 'delete_user', 'Admin deleted user: dev02 (ID: 14)', '2025-05-06 17:57:07'),
(7, 1, 'delete_user', 'Admin deleted user: test5 (ID: 3)', '2025-05-06 17:58:14'),
(8, 1, 'delete_user', 'Admin deleted user: dev02 (ID: 16)', '2025-05-06 17:59:02'),
(9, 1, 'delete_user', 'Admin deleted user: dev03 (ID: 18)', '2025-05-06 18:03:07'),
(10, 1, 'delete_user', 'Admin deleted user: dev02 (ID: 17)', '2025-05-06 18:03:12'),
(11, 1, 'delete_user', 'Admin deleted user: watcahrapolVRY (ID: 23)', '2025-05-21 16:32:55'),
(12, 1, 'delete_user', 'Admin deleted user: dev99 (ID: 22)', '2025-05-21 16:32:59'),
(13, 1, 'delete_user', 'Admin deleted user: dev04 (ID: 21)', '2025-05-21 16:33:01'),
(14, 1, 'delete_user', 'Admin deleted user: dev02 (ID: 20)', '2025-05-21 16:33:03'),
(15, 1, 'delete_user', 'Admin deleted user: dev03 (ID: 19)', '2025-05-21 16:33:06');

-- --------------------------------------------------------

--
-- Table structure for table `admin_users`
--

CREATE TABLE `admin_users` (
  `id` int(11) NOT NULL,
  `username` varchar(100) NOT NULL,
  `email` varchar(255) NOT NULL,
  `password` varchar(255) NOT NULL,
  `role` enum('admin','support','manager') DEFAULT 'support',
  `created_at` datetime DEFAULT current_timestamp(),
  `last_login` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

--
-- Dumping data for table `admin_users`
--

INSERT INTO `admin_users` (`id`, `username`, `email`, `password`, `role`, `created_at`, `last_login`) VALUES
(1, 'superadmin', '<EMAIL>', '81dc9bdb52d04dc20036dbd8313ed055', 'admin', '2025-04-10 16:08:17', '2025-06-29 16:04:30'),
(2, 'loon', '<EMAIL>', 'e10adc3949ba59abbe56e057f20f883e', 'manager', '2025-05-06 15:24:04', NULL),
(4, 'test', '<EMAIL>', 'e10adc3949ba59abbe56e057f20f883e', 'support', '2025-05-06 16:04:50', '2025-06-06 07:33:28'),
(8, 'admin', '<EMAIL>', 'e10adc3949ba59abbe56e057f20f883e', 'admin', '2025-06-12 06:36:37', '2025-06-12 06:36:58');

-- --------------------------------------------------------

--
-- Table structure for table `appika_pending_sync`
--

CREATE TABLE `appika_pending_sync` (
  `id` int(11) NOT NULL,
  `sync_type` varchar(50) NOT NULL,
  `local_id` int(11) NOT NULL,
  `sync_data` text NOT NULL,
  `status` enum('pending','completed','failed') DEFAULT 'pending',
  `appika_id` varchar(100) DEFAULT NULL,
  `error_message` text DEFAULT NULL,
  `retry_count` int(11) DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `last_attempt` timestamp NULL DEFAULT NULL,
  `synced_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `backup_config`
--

CREATE TABLE `backup_config` (
  `id` int(11) NOT NULL,
  `config_key` varchar(100) NOT NULL,
  `config_value` text NOT NULL,
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `backup_config`
--

INSERT INTO `backup_config` (`id`, `config_key`, `config_value`, `updated_at`, `created_at`) VALUES
(1, 'auto_sync_enabled', '1', '2025-06-27 18:49:00', '2025-06-27 18:49:00'),
(2, 'sync_batch_size', '10', '2025-06-27 18:49:00', '2025-06-27 18:49:00'),
(3, 'sync_retry_limit', '3', '2025-06-27 18:49:00', '2025-06-27 18:49:00'),
(4, 'cleanup_days', '30', '2025-06-27 18:49:00', '2025-06-27 18:49:00');

-- --------------------------------------------------------

--
-- Table structure for table `cart`
--

CREATE TABLE `cart` (
  `cart_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `status` varchar(20) DEFAULT 'active',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

--
-- Dumping data for table `cart`
--

INSERT INTO `cart` (`cart_id`, `user_id`, `status`, `created_at`, `updated_at`) VALUES
(1, 1, 'open', '2025-04-08 09:36:37', '2025-04-09 04:10:02'),
(2, 1, 'completed', '2025-04-09 04:43:06', '2025-05-14 10:08:24'),
(5, 1, 'completed', '2025-05-14 10:12:55', '2025-05-15 02:25:15'),
(6, 13, 'completed', '2025-05-15 02:26:59', '2025-05-15 02:27:48'),
(14, 1, 'completed', '2025-05-19 03:58:56', '2025-05-19 04:00:03'),
(15, 1, 'completed', '2025-05-19 04:00:36', '2025-05-21 02:21:53'),
(16, 1, 'completed', '2025-05-21 02:44:31', '2025-05-21 02:46:08'),
(17, 1, 'completed', '2025-05-21 02:46:36', '2025-05-21 03:55:47'),
(18, 1, 'completed', '2025-05-21 03:56:13', '2025-05-21 03:56:57'),
(19, 1, 'completed', '2025-05-21 03:58:25', '2025-05-21 04:15:34'),
(20, 1, 'completed', '2025-05-21 04:15:47', '2025-05-21 04:29:34'),
(21, 1, 'completed', '2025-05-21 04:30:33', '2025-05-21 04:37:25'),
(22, 1, 'completed', '2025-05-21 04:37:37', '2025-05-21 04:42:35'),
(23, 1, 'completed', '2025-05-21 04:42:54', '2025-05-21 04:51:55'),
(27, 24, 'completed', '2025-05-21 05:02:53', '2025-05-21 05:03:23'),
(28, 24, 'completed', '2025-05-21 05:06:32', '2025-05-21 05:07:08'),
(29, 24, 'completed', '2025-05-21 05:28:40', '2025-05-21 05:29:41'),
(30, 24, 'completed', '2025-05-21 05:43:03', '2025-05-21 05:43:51'),
(31, 24, 'completed', '2025-05-21 05:44:44', '2025-05-21 05:45:38'),
(32, 24, 'completed', '2025-05-21 05:47:53', '2025-05-21 05:56:07'),
(33, 24, 'completed', '2025-05-21 06:00:34', '2025-05-21 06:03:03'),
(34, 24, 'completed', '2025-05-21 06:03:24', '2025-05-21 06:27:21'),
(35, 24, 'completed', '2025-05-21 06:28:29', '2025-05-21 06:28:43'),
(36, 24, 'completed', '2025-05-21 06:29:02', '2025-05-21 06:34:59'),
(37, 24, 'completed', '2025-05-21 06:35:29', '2025-05-21 06:55:10'),
(38, 24, 'completed', '2025-05-21 06:55:58', '2025-05-21 06:57:27'),
(39, 24, 'completed', '2025-05-21 07:10:59', '2025-05-21 07:20:58'),
(40, 24, 'completed', '2025-05-21 07:39:04', '2025-05-21 08:00:16'),
(41, 24, 'completed', '2025-05-21 08:00:41', '2025-05-21 08:03:31'),
(42, 24, 'active', '2025-05-21 08:10:16', '2025-05-21 08:10:16'),
(43, 25, 'completed', '2025-05-21 09:37:34', '2025-05-21 09:51:37'),
(44, 25, 'completed', '2025-05-21 09:52:07', '2025-05-21 09:56:43'),
(45, 25, 'completed', '2025-05-21 09:56:55', '2025-05-21 10:37:40'),
(46, 25, 'completed', '2025-05-21 10:40:17', '2025-05-21 10:56:38'),
(47, 25, 'completed', '2025-05-21 10:57:33', '2025-05-21 11:00:38'),
(48, 1, 'completed', '2025-05-23 05:32:06', '2025-05-23 07:38:13'),
(49, 1, 'completed', '2025-05-23 07:38:42', '2025-05-23 10:43:22'),
(50, 40, 'completed', '2025-05-28 10:30:00', '2025-05-28 10:36:09'),
(51, 1, 'completed', '2025-05-29 04:43:08', '2025-05-29 04:48:36'),
(52, 1, 'completed', '2025-05-29 04:49:45', '2025-05-30 10:45:51'),
(53, 49, 'active', '2025-05-30 07:37:22', '2025-05-30 07:37:22'),
(54, 52, 'active', '2025-05-30 08:26:17', '2025-05-30 08:26:17'),
(55, 1, 'completed', '2025-05-30 10:48:43', '2025-05-30 10:52:42'),
(56, 1, 'completed', '2025-05-30 10:53:09', '2025-05-30 11:03:16'),
(57, 1, 'completed', '2025-05-31 04:18:38', '2025-05-31 04:19:36'),
(58, 1, 'completed', '2025-05-31 04:20:47', '2025-05-31 04:21:25'),
(66, 1, 'completed', '2025-05-31 05:42:41', '2025-05-31 07:44:04'),
(67, 1, 'completed', '2025-05-31 07:44:45', '2025-05-31 08:45:33'),
(68, 1, 'completed', '2025-05-31 08:47:03', '2025-06-04 08:50:00'),
(69, 76, 'active', '2025-05-31 11:07:54', '2025-05-31 11:07:54'),
(70, 98, 'active', '2025-05-31 15:52:11', '2025-05-31 15:52:11'),
(71, 100, 'completed', '2025-05-31 16:26:52', '2025-05-31 16:27:32'),
(72, 101, 'active', '2025-05-31 16:34:10', '2025-05-31 16:34:10'),
(73, 102, 'completed', '2025-05-31 16:40:54', '2025-05-31 16:41:06'),
(74, 102, 'active', '2025-05-31 16:41:35', '2025-05-31 16:41:35'),
(75, 106, 'completed', '2025-05-31 17:54:31', '2025-05-31 18:13:36'),
(76, 106, 'completed', '2025-05-31 18:14:13', '2025-05-31 18:24:26'),
(77, 107, 'completed', '2025-05-31 20:19:19', '2025-05-31 20:19:53'),
(78, 111, 'completed', '2025-05-31 20:57:04', '2025-05-31 20:57:49'),
(79, 111, 'completed', '2025-05-31 21:15:31', '2025-05-31 21:47:46'),
(80, 111, 'completed', '2025-05-31 21:55:45', '2025-05-31 21:59:09'),
(81, 111, 'completed', '2025-05-31 22:00:06', '2025-05-31 22:13:54'),
(82, 112, 'completed', '2025-06-01 08:38:43', '2025-06-01 08:39:27'),
(83, 112, 'completed', '2025-06-01 08:39:53', '2025-06-01 08:40:39'),
(84, 112, 'completed', '2025-06-01 08:43:57', '2025-06-01 08:44:14'),
(85, 112, 'completed', '2025-06-01 08:44:52', '2025-06-01 08:45:33'),
(86, 112, 'completed', '2025-06-01 08:45:53', '2025-06-01 08:46:03'),
(87, 112, 'completed', '2025-06-01 08:48:22', '2025-06-01 08:48:32'),
(88, 112, 'completed', '2025-06-01 08:48:45', '2025-06-01 08:49:10'),
(89, 120, 'completed', '2025-06-02 03:28:58', '2025-06-02 03:29:11'),
(90, 129, 'completed', '2025-06-02 04:11:56', '2025-06-02 04:12:10'),
(91, 129, 'completed', '2025-06-02 04:12:20', '2025-06-02 04:13:05'),
(92, 129, 'completed', '2025-06-02 04:13:14', '2025-06-02 04:13:51'),
(93, 132, 'completed', '2025-06-02 04:34:22', '2025-06-02 04:34:58'),
(94, 1, 'completed', '2025-06-04 08:51:44', '2025-06-04 08:52:36'),
(95, 143, 'completed', '2025-06-05 03:00:48', '2025-06-05 03:01:08'),
(96, 1, 'completed', '2025-06-05 05:27:00', '2025-06-05 07:05:49'),
(97, 1, 'completed', '2025-06-05 07:07:06', '2025-06-05 07:08:05'),
(98, 1, 'completed', '2025-06-05 07:09:13', '2025-06-05 07:10:10'),
(99, 1, 'completed', '2025-06-05 07:14:48', '2025-06-05 08:27:10'),
(100, 1, 'completed', '2025-06-05 08:52:08', '2025-06-06 07:00:48'),
(101, 144, 'completed', '2025-06-05 09:12:52', '2025-06-05 09:13:51'),
(102, 150, 'completed', '2025-06-05 10:17:07', '2025-06-05 10:18:04'),
(103, 150, 'completed', '2025-06-05 11:21:39', '2025-06-05 11:22:07'),
(104, 151, 'completed', '2025-06-06 05:52:58', '2025-06-06 05:54:20'),
(105, 158, 'completed', '2025-06-09 03:34:20', '2025-06-09 03:44:09'),
(106, 1, 'completed', '2025-06-09 10:33:42', '2025-06-10 04:14:59'),
(107, 1, 'completed', '2025-06-10 04:23:53', '2025-06-10 04:24:53'),
(108, 1, 'completed', '2025-06-10 04:27:16', '2025-06-10 04:28:11'),
(109, 1, 'completed', '2025-06-10 04:28:52', '2025-06-10 04:30:08'),
(110, 1, 'completed', '2025-06-10 04:37:32', '2025-06-10 04:38:07'),
(111, 1, 'completed', '2025-06-10 04:40:10', '2025-06-10 04:41:12'),
(112, 1, 'completed', '2025-06-11 04:46:28', '2025-06-11 10:32:08'),
(113, 1, 'completed', '2025-06-11 10:34:01', '2025-06-11 10:34:15'),
(114, 1, 'completed', '2025-06-11 10:35:58', '2025-06-11 10:36:21'),
(115, 1, 'completed', '2025-06-11 10:40:25', '2025-06-11 10:40:39'),
(116, 1, 'completed', '2025-06-11 10:42:46', '2025-06-11 10:43:23'),
(117, 1, 'completed', '2025-06-11 10:45:37', '2025-06-11 10:47:44'),
(118, 1, 'completed', '2025-06-11 10:53:07', '2025-06-11 11:18:47'),
(119, 1, 'completed', '2025-06-11 11:20:51', '2025-06-11 11:22:28'),
(120, 1, 'completed', '2025-06-11 11:25:39', '2025-06-11 11:29:01'),
(121, 1, 'completed', '2025-06-11 11:35:07', '2025-06-11 11:37:04'),
(122, 1, 'completed', '2025-06-12 02:16:37', '2025-06-12 02:45:05'),
(123, 1, 'completed', '2025-06-12 02:45:39', '2025-06-12 02:52:30'),
(124, 1, 'completed', '2025-06-12 02:53:04', '2025-06-12 02:53:21'),
(125, 1, 'completed', '2025-06-12 02:54:15', '2025-06-12 03:00:29'),
(126, 1, 'completed', '2025-06-12 03:00:49', '2025-06-12 03:01:12'),
(127, 1, 'completed', '2025-06-12 03:01:53', '2025-06-12 03:26:02'),
(128, 1, 'completed', '2025-06-12 03:45:38', '2025-06-12 03:45:46'),
(129, 175, 'completed', '2025-06-12 03:59:16', '2025-06-12 03:59:37'),
(130, 175, 'completed', '2025-06-12 03:59:53', '2025-06-12 04:00:31'),
(131, 175, 'completed', '2025-06-12 04:02:08', '2025-06-12 04:07:38'),
(132, 175, 'completed', '2025-06-12 04:07:57', '2025-06-12 04:08:24'),
(133, 175, 'completed', '2025-06-12 04:33:47', '2025-06-12 05:00:28'),
(134, 1, 'completed', '2025-06-12 05:34:46', '2025-06-12 05:35:16'),
(135, 1, 'completed', '2025-06-12 07:04:27', '2025-06-13 03:29:21'),
(136, 176, 'completed', '2025-06-12 10:55:26', '2025-06-12 10:55:36'),
(137, 185, 'completed', '2025-06-23 11:19:23', '2025-06-23 11:19:34'),
(138, 186, 'completed', '2025-06-24 02:19:03', '2025-06-24 02:19:18'),
(139, 186, 'completed', '2025-06-24 03:03:04', '2025-06-24 03:03:29'),
(140, 186, 'completed', '2025-06-24 03:29:58', '2025-06-24 03:30:19'),
(141, 186, 'completed', '2025-06-24 03:47:08', '2025-06-24 03:47:24'),
(142, 186, 'completed', '2025-06-24 03:53:42', '2025-06-24 03:53:50'),
(143, 186, 'completed', '2025-06-24 04:05:19', '2025-06-24 04:05:34'),
(144, 186, 'completed', '2025-06-24 04:12:05', '2025-06-24 04:12:15'),
(145, 186, 'completed', '2025-06-24 04:19:47', '2025-06-24 04:19:59'),
(146, 186, 'completed', '2025-06-24 04:23:21', '2025-06-24 04:23:29'),
(147, 186, 'completed', '2025-06-24 04:26:15', '2025-06-24 04:26:27'),
(148, 186, 'completed', '2025-06-24 04:30:16', '2025-06-24 04:30:27'),
(149, 186, 'completed', '2025-06-24 04:30:50', '2025-06-24 04:31:00'),
(150, 186, 'completed', '2025-06-24 04:38:01', '2025-06-24 04:38:15'),
(151, 186, 'completed', '2025-06-24 04:53:04', '2025-06-24 04:53:16'),
(152, 186, 'completed', '2025-06-24 05:08:43', '2025-06-24 05:09:00'),
(153, 186, 'completed', '2025-06-24 05:53:33', '2025-06-24 05:53:41'),
(154, 186, 'completed', '2025-06-24 06:16:07', '2025-06-24 06:16:17'),
(155, 186, 'completed', '2025-06-24 06:25:07', '2025-06-24 06:25:24'),
(156, 186, 'completed', '2025-06-24 06:30:00', '2025-06-24 06:30:08'),
(157, 186, 'completed', '2025-06-24 06:32:45', '2025-06-24 06:32:53'),
(158, 186, 'completed', '2025-06-24 06:43:20', '2025-06-24 06:43:33'),
(159, 186, 'completed', '2025-06-24 06:48:21', '2025-06-24 06:48:28'),
(160, 186, 'completed', '2025-06-24 06:59:08', '2025-06-24 06:59:19'),
(161, 1, 'completed', '2025-06-24 11:09:50', '2025-06-25 04:53:33'),
(162, 177, 'completed', '2025-06-25 04:56:33', '2025-06-25 04:56:42'),
(163, 1, 'completed', '2025-06-25 06:45:00', '2025-06-25 06:45:08'),
(164, 1, 'completed', '2025-06-25 07:05:27', '2025-06-25 07:05:36'),
(165, 1, 'completed', '2025-06-25 07:15:41', '2025-06-25 07:15:49'),
(166, 1, 'completed', '2025-06-25 11:42:56', '2025-06-25 11:43:03'),
(167, 202, 'completed', '2025-06-27 16:54:47', '2025-06-27 16:54:56'),
(168, 200, 'active', '2025-06-28 18:37:43', '2025-06-28 18:37:43'),
(169, 209, 'completed', '2025-06-29 13:32:10', '2025-06-29 13:32:19');

-- --------------------------------------------------------

--
-- Table structure for table `cart_items`
--

CREATE TABLE `cart_items` (
  `id` int(11) NOT NULL,
  `cart_id` int(11) NOT NULL,
  `ticket_id` int(11) NOT NULL,
  `quantity` int(11) NOT NULL DEFAULT 1
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

--
-- Dumping data for table `cart_items`
--

INSERT INTO `cart_items` (`id`, `cart_id`, `ticket_id`, `quantity`) VALUES
(32, 1, 1004, 2),
(54, 1, 1006, 1),
(56, 1, 1003, 1),
(57, 1, 2006, 1),
(79, 2, 1001, 1),
(91, 5, 1001, 1),
(92, 5, 2001, 1),
(98, 6, 1001, 1),
(99, 6, 2001, 1),
(100, 6, 3001, 1),
(133, 14, 1001, 1),
(135, 15, 2001, 1),
(136, 16, 1001, 1),
(137, 17, 1001, 1),
(139, 18, 2001, 2),
(140, 19, 1001, 1),
(141, 20, 1001, 1),
(142, 21, 2001, 1),
(143, 22, 2001, 1),
(144, 23, 3001, 2),
(148, 27, 1001, 1),
(149, 28, 1001, 1),
(150, 29, 3001, 1),
(151, 30, 2001, 1),
(152, 31, 2001, 1),
(153, 32, 1001, 2),
(154, 33, 3001, 1),
(155, 34, 3001, 1),
(156, 35, 2001, 1),
(157, 36, 3001, 1),
(158, 37, 1001, 1),
(159, 38, 2001, 1),
(160, 39, 1001, 1),
(161, 40, 1001, 1),
(162, 41, 1001, 1),
(163, 41, 2001, 1),
(164, 41, 3001, 1),
(165, 42, 1001, 1),
(166, 43, 1002, 1),
(167, 43, 2002, 1),
(168, 43, 3002, 1),
(169, 44, 1001, 1),
(170, 45, 2001, 1),
(172, 46, 3001, 1),
(173, 47, 1001, 1),
(174, 48, 1001, 1),
(175, 49, 1001, 2),
(178, 50, 1001, 1),
(180, 51, 3001, 1),
(181, 52, 3001, 2),
(183, 53, 2002, 1),
(184, 54, 1001, 1),
(185, 54, 1003, 1),
(186, 55, 1001, 2),
(187, 56, 1001, 2),
(188, 56, 2001, 2),
(189, 56, 3001, 2),
(191, 57, 1001, 1),
(192, 57, 2001, 1),
(193, 57, 3001, 1),
(194, 58, 1001, 1),
(195, 58, 2001, 1),
(196, 58, 3001, 1),
(200, 66, 1001, 1),
(201, 66, 1002, 1),
(203, 67, 1002, 1),
(204, 67, 2002, 1),
(205, 67, 3002, 1),
(208, 68, 1002, 1),
(209, 68, 1001, 1),
(210, 69, 1001, 1),
(211, 69, 1002, 1),
(212, 69, 1003, 1),
(213, 69, 2001, 1),
(214, 69, 3001, 1),
(215, 69, 2005, 1),
(216, 70, 1001, 1),
(217, 70, 2001, 1),
(218, 70, 2002, 1),
(219, 70, 3001, 1),
(220, 70, 3002, 1),
(221, 70, 1002, 1),
(222, 71, 1001, 1),
(223, 72, 2001, 1),
(224, 73, 2002, 1),
(225, 74, 3001, 1),
(226, 75, 1001, 1),
(250, 80, 2001, 2),
(251, 80, 3001, 2),
(252, 80, 1001, 2),
(253, 81, 1001, 1),
(254, 81, 2001, 1),
(255, 81, 3001, 1),
(258, 84, 2001, 1),
(259, 85, 3001, 1),
(260, 86, 1001, 1),
(261, 87, 2001, 1),
(262, 88, 3001, 1),
(263, 89, 1001, 1),
(264, 90, 1001, 1),
(265, 90, 1002, 1),
(266, 91, 2001, 1),
(267, 91, 2002, 1),
(268, 92, 3001, 1),
(269, 92, 3002, 1),
(270, 93, 2001, 1),
(271, 93, 2002, 1),
(272, 94, 2001, 1),
(273, 95, 1001, 1),
(277, 96, 2001, 1),
(278, 97, 2001, 2),
(279, 98, 1001, 1),
(286, 99, 3004, 1),
(287, 100, 1001, 1),
(288, 101, 3001, 1),
(289, 102, 2001, 1),
(290, 103, 3001, 1),
(291, 104, 1001, 1),
(292, 105, 2001, 1),
(293, 106, 1001, 1),
(294, 106, 1002, 1),
(295, 106, 2006, 1),
(296, 107, 2001, 1),
(297, 108, 1001, 1),
(298, 109, 1001, 1),
(299, 110, 2001, 1),
(300, 111, 1001, 1),
(302, 112, 1001, 3),
(303, 113, 1001, 1),
(304, 114, 1001, 1),
(305, 114, 1002, 1),
(306, 114, 2001, 1),
(307, 115, 1001, 1),
(308, 116, 1002, 1),
(309, 116, 2002, 1),
(310, 116, 3002, 1),
(311, 116, 3001, 1),
(312, 116, 2001, 1),
(313, 116, 1001, 1),
(314, 117, 1001, 1),
(315, 117, 1002, 1),
(316, 117, 2001, 1),
(317, 117, 2002, 1),
(318, 117, 3001, 1),
(319, 117, 3002, 1),
(320, 118, 1001, 5),
(321, 118, 2001, 1),
(322, 118, 3001, 1),
(323, 119, 1001, 2),
(324, 120, 1001, 1),
(325, 121, 1001, 2),
(327, 122, 1001, 1),
(328, 123, 2001, 1),
(329, 124, 3001, 1),
(330, 125, 1001, 1),
(331, 126, 2001, 1),
(332, 126, 2002, 1),
(333, 126, 3001, 1),
(334, 127, 1001, 1),
(335, 128, 1001, 1),
(336, 129, 1002, 1),
(337, 129, 2001, 1),
(338, 129, 3001, 1),
(339, 130, 1001, 1),
(340, 131, 2001, 1),
(341, 132, 3001, 1),
(342, 133, 2001, 1),
(343, 133, 1001, 1),
(344, 133, 3001, 1),
(345, 133, 3002, 1),
(346, 133, 2002, 1),
(347, 133, 1002, 1),
(348, 134, 1001, 1),
(350, 135, 1001, 1),
(351, 136, 1001, 1),
(352, 135, 3001, 1),
(353, 137, 2001, 1),
(354, 138, 1001, 1),
(355, 138, 2001, 1),
(356, 138, 3001, 1),
(357, 139, 1001, 1),
(358, 139, 2001, 1),
(359, 139, 3001, 1),
(360, 139, 1002, 1),
(361, 139, 2002, 1),
(362, 139, 3002, 1),
(363, 140, 1001, 1),
(364, 140, 2001, 1),
(365, 140, 3001, 1),
(366, 141, 1001, 1),
(367, 141, 2001, 1),
(368, 141, 3001, 1),
(369, 142, 1001, 1),
(370, 143, 1001, 1),
(371, 143, 2001, 1),
(372, 144, 1001, 1),
(373, 145, 1001, 1),
(374, 146, 3001, 1),
(375, 147, 2001, 1),
(376, 148, 1001, 1),
(377, 148, 2001, 1),
(378, 149, 3001, 1),
(379, 150, 1001, 1),
(380, 150, 2001, 1),
(381, 150, 3001, 1),
(382, 151, 1001, 1),
(383, 151, 2001, 1),
(384, 152, 1001, 1),
(385, 153, 1001, 1),
(386, 154, 1001, 1),
(387, 154, 1002, 1),
(388, 155, 1001, 1),
(389, 155, 2001, 1),
(390, 155, 3001, 1),
(391, 156, 2001, 1),
(392, 157, 2001, 1),
(393, 158, 1001, 1),
(394, 158, 2001, 1),
(395, 158, 3001, 1),
(396, 159, 1001, 1),
(397, 160, 1001, 1),
(398, 160, 2001, 1),
(401, 161, 1001, 1),
(404, 162, 1001, 1),
(405, 163, 1001, 1),
(406, 164, 2001, 1),
(407, 165, 1001, 1),
(408, 166, 3001, 1),
(409, 167, 1001, 1),
(410, 168, 1001, 1),
(411, 169, 1001, 1);

-- --------------------------------------------------------

--
-- Table structure for table `cart_sessions`
--

CREATE TABLE `cart_sessions` (
  `id` int(11) NOT NULL,
  `session_id` varchar(255) NOT NULL,
  `cart_data` text NOT NULL,
  `user_id` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `processed_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `cart_sessions`
--

INSERT INTO `cart_sessions` (`id`, `session_id`, `cart_data`, `user_id`, `created_at`, `processed_at`) VALUES
(2, 'cart_683b23a9e9069_1748706217', '[{\"ticket_type\":\"STARTER\",\"package_size\":\"XS\",\"numbers_per_package\":\"1\",\"dollar_price_per_package\":\"20\",\"quantity\":1},{\"ticket_type\":\"STARTER\",\"package_size\":\"S\",\"numbers_per_package\":\"15\",\"dollar_price_per_package\":\"150\",\"quantity\":\"1\"},{\"ticket_type\":\"BUSINESS\",\"package_size\":\"XS\",\"numbers_per_package\":\"1\",\"dollar_price_per_package\":\"25\",\"quantity\":\"1\"},{\"ticket_type\":\"BUSINESS\",\"package_size\":\"S\",\"numbers_per_package\":\"10\",\"dollar_price_per_package\":\"200\",\"quantity\":\"1\"},{\"ticket_type\":\"ULTIMATE\",\"package_size\":\"XS\",\"numbers_per_package\":\"1\",\"dollar_price_per_package\":\"30\",\"quantity\":\"1\"},{\"ticket_type\":\"ULTIMATE\",\"package_size\":\"S\",\"numbers_per_package\":\"10\",\"dollar_price_per_package\":\"250\",\"quantity\":\"1\"},{\"ticket_type\":\"STARTER\",\"package_size\":\"M\",\"numbers_per_package\":\"25\",\"dollar_price_per_package\":\"325\",\"quantity\":\"1\"}]', NULL, '2025-05-31 15:43:37', '2025-05-31 15:44:59'),
(3, 'cart_683b24c9cb043_1748706505', '[{\"ticket_type\":\"STARTER\",\"package_size\":\"XS\",\"numbers_per_package\":\"1\",\"dollar_price_per_package\":\"20\",\"quantity\":\"1\"},{\"ticket_type\":\"STARTER\",\"package_size\":\"S\",\"numbers_per_package\":\"15\",\"dollar_price_per_package\":\"150\",\"quantity\":\"1\"},{\"ticket_type\":\"BUSINESS\",\"package_size\":\"XS\",\"numbers_per_package\":\"1\",\"dollar_price_per_package\":\"25\",\"quantity\":\"1\"},{\"ticket_type\":\"BUSINESS\",\"package_size\":\"S\",\"numbers_per_package\":\"10\",\"dollar_price_per_package\":\"200\",\"quantity\":\"1\"},{\"ticket_type\":\"ULTIMATE\",\"package_size\":\"XS\",\"numbers_per_package\":\"1\",\"dollar_price_per_package\":\"30\",\"quantity\":\"1\"},{\"ticket_type\":\"ULTIMATE\",\"package_size\":\"S\",\"numbers_per_package\":\"10\",\"dollar_price_per_package\":\"250\",\"quantity\":\"1\"}]', NULL, '2025-05-31 15:48:25', '2025-05-31 15:49:04'),
(4, 'cart_683b2aafe1956_1748708015', '[{\"ticket_type\":\"STARTER\",\"package_size\":\"XS\",\"numbers_per_package\":\"1\",\"dollar_price_per_package\":\"20\",\"quantity\":\"1\"},{\"ticket_type\":\"STARTER\",\"package_size\":\"S\",\"numbers_per_package\":\"15\",\"dollar_price_per_package\":\"150\",\"quantity\":\"1\"},{\"ticket_type\":\"BUSINESS\",\"package_size\":\"XS\",\"numbers_per_package\":\"1\",\"dollar_price_per_package\":\"25\",\"quantity\":\"1\"},{\"ticket_type\":\"BUSINESS\",\"package_size\":\"S\",\"numbers_per_package\":\"10\",\"dollar_price_per_package\":\"200\",\"quantity\":\"1\"},{\"ticket_type\":\"ULTIMATE\",\"package_size\":\"XS\",\"numbers_per_package\":\"1\",\"dollar_price_per_package\":\"30\",\"quantity\":\"1\"},{\"ticket_type\":\"ULTIMATE\",\"package_size\":\"S\",\"numbers_per_package\":\"10\",\"dollar_price_per_package\":\"250\",\"quantity\":\"1\"}]', NULL, '2025-05-31 16:13:35', '2025-05-31 16:14:30'),
(5, 'cart_683b2ca12b8d8_1748708513', '[{\"ticket_type\":\"STARTER\",\"package_size\":\"XS\",\"numbers_per_package\":\"1\",\"dollar_price_per_package\":\"20\",\"quantity\":\"1\"},{\"ticket_type\":\"STARTER\",\"package_size\":\"S\",\"numbers_per_package\":\"15\",\"dollar_price_per_package\":\"150\",\"quantity\":\"1\"},{\"ticket_type\":\"BUSINESS\",\"package_size\":\"XS\",\"numbers_per_package\":\"1\",\"dollar_price_per_package\":\"25\",\"quantity\":\"1\"},{\"ticket_type\":\"BUSINESS\",\"package_size\":\"S\",\"numbers_per_package\":\"10\",\"dollar_price_per_package\":\"200\",\"quantity\":\"1\"},{\"ticket_type\":\"ULTIMATE\",\"package_size\":\"XS\",\"numbers_per_package\":\"1\",\"dollar_price_per_package\":\"30\",\"quantity\":\"1\"},{\"ticket_type\":\"ULTIMATE\",\"package_size\":\"S\",\"numbers_per_package\":\"10\",\"dollar_price_per_package\":\"250\",\"quantity\":\"1\"}]', NULL, '2025-05-31 16:21:53', '2025-05-31 16:23:39'),
(8, 'cart_683b2ece6c253_1748709070', '[{\"ticket_type\":\"STARTER\",\"package_size\":\"XS\",\"numbers_per_package\":\"1\",\"dollar_price_per_package\":\"20\",\"quantity\":\"1\"},{\"ticket_type\":\"STARTER\",\"package_size\":\"S\",\"numbers_per_package\":\"15\",\"dollar_price_per_package\":\"150\",\"quantity\":\"1\"},{\"ticket_type\":\"BUSINESS\",\"package_size\":\"XS\",\"numbers_per_package\":\"1\",\"dollar_price_per_package\":\"25\",\"quantity\":\"1\"},{\"ticket_type\":\"BUSINESS\",\"package_size\":\"S\",\"numbers_per_package\":\"10\",\"dollar_price_per_package\":\"200\",\"quantity\":\"1\"},{\"ticket_type\":\"ULTIMATE\",\"package_size\":\"XS\",\"numbers_per_package\":\"1\",\"dollar_price_per_package\":\"30\",\"quantity\":\"1\"},{\"ticket_type\":\"ULTIMATE\",\"package_size\":\"S\",\"numbers_per_package\":\"10\",\"dollar_price_per_package\":\"250\",\"quantity\":\"1\"}]', NULL, '2025-05-31 16:31:10', '2025-05-31 16:31:46'),
(9, 'cart_683b2f865b868_1748709254', '[{\"ticket_type\":\"BUSINESS\",\"package_size\":\"XS\",\"numbers_per_package\":1,\"dollar_price_per_package\":25,\"quantity\":1}]', 101, '2025-05-31 16:34:14', '2025-05-31 16:34:41'),
(10, 'cart_683b309cda8bd_1748709532', '[{\"ticket_type\":\"STARTER\",\"package_size\":\"XS\",\"numbers_per_package\":\"1\",\"dollar_price_per_package\":\"20\",\"quantity\":\"1\"},{\"ticket_type\":\"STARTER\",\"package_size\":\"S\",\"numbers_per_package\":\"15\",\"dollar_price_per_package\":\"150\",\"quantity\":\"1\"}]', NULL, '2025-05-31 16:38:52', '2025-05-31 16:39:32'),
(12, 'cart_683b314527b03_1748709701', '[{\"ticket_type\":\"ULTIMATE\",\"package_size\":\"XS\",\"numbers_per_package\":1,\"dollar_price_per_package\":30,\"quantity\":1}]', 102, '2025-05-31 16:41:41', '2025-05-31 16:42:09'),
(13, 'cart_683b31c079d9e_1748709824', '[{\"ticket_type\":\"ULTIMATE\",\"package_size\":\"XS\",\"numbers_per_package\":1,\"dollar_price_per_package\":30,\"quantity\":2}]', 102, '2025-05-31 16:43:44', '2025-05-31 16:44:11'),
(14, 'cart_683b330fb4470_1748710159', '[{\"ticket_type\":\"ULTIMATE\",\"package_size\":\"XS\",\"numbers_per_package\":1,\"dollar_price_per_package\":30,\"quantity\":1}]', 102, '2025-05-31 16:49:19', '2025-05-31 16:49:47'),
(15, 'cart_683b33a961b9e_1748710313', '[{\"ticket_type\":\"STARTER\",\"package_size\":\"XS\",\"numbers_per_package\":\"1\",\"dollar_price_per_package\":\"20\",\"quantity\":\"1\"},{\"ticket_type\":\"BUSINESS\",\"package_size\":\"XS\",\"numbers_per_package\":\"1\",\"dollar_price_per_package\":\"25\",\"quantity\":\"1\"},{\"ticket_type\":\"ULTIMATE\",\"package_size\":\"XS\",\"numbers_per_package\":\"1\",\"dollar_price_per_package\":\"30\",\"quantity\":\"1\"}]', NULL, '2025-05-31 16:51:53', '2025-05-31 16:52:35'),
(16, 'cart_683b357cdb72f_1748710780', '[{\"ticket_type\":\"STARTER\",\"package_size\":\"XS\",\"numbers_per_package\":\"1\",\"dollar_price_per_package\":\"20\",\"quantity\":\"1\"},{\"ticket_type\":\"BUSINESS\",\"package_size\":\"XS\",\"numbers_per_package\":\"1\",\"dollar_price_per_package\":\"25\",\"quantity\":\"1\"},{\"ticket_type\":\"ULTIMATE\",\"package_size\":\"XS\",\"numbers_per_package\":\"1\",\"dollar_price_per_package\":\"30\",\"quantity\":\"1\"}]', NULL, '2025-05-31 16:59:40', '2025-05-31 17:00:24'),
(17, 'cart_683b360415685_1748710916', '[{\"ticket_type\":\"STARTER\",\"package_size\":\"XS\",\"numbers_per_package\":\"1\",\"dollar_price_per_package\":\"20\",\"quantity\":\"1\"},{\"ticket_type\":\"BUSINESS\",\"package_size\":\"XS\",\"numbers_per_package\":\"1\",\"dollar_price_per_package\":\"25\",\"quantity\":\"1\"},{\"ticket_type\":\"ULTIMATE\",\"package_size\":\"XS\",\"numbers_per_package\":\"1\",\"dollar_price_per_package\":\"30\",\"quantity\":\"1\"}]', NULL, '2025-05-31 17:01:56', '2025-05-31 17:02:43'),
(18, 'cart_683b419e74288_1748713886', '[{\"ticket_type\":\"STARTER\",\"package_size\":\"XS\",\"numbers_per_package\":\"1\",\"dollar_price_per_package\":\"20\",\"quantity\":\"1\"},{\"ticket_type\":\"STARTER\",\"package_size\":\"S\",\"numbers_per_package\":\"15\",\"dollar_price_per_package\":\"150\",\"quantity\":\"1\"},{\"ticket_type\":\"BUSINESS\",\"package_size\":\"XS\",\"numbers_per_package\":\"1\",\"dollar_price_per_package\":\"25\",\"quantity\":\"1\"},{\"ticket_type\":\"BUSINESS\",\"package_size\":\"S\",\"numbers_per_package\":\"10\",\"dollar_price_per_package\":\"200\",\"quantity\":\"1\"},{\"ticket_type\":\"ULTIMATE\",\"package_size\":\"XS\",\"numbers_per_package\":\"1\",\"dollar_price_per_package\":\"30\",\"quantity\":\"1\"},{\"ticket_type\":\"ULTIMATE\",\"package_size\":\"S\",\"numbers_per_package\":\"10\",\"dollar_price_per_package\":\"250\",\"quantity\":\"1\"}]', NULL, '2025-05-31 17:51:26', '2025-05-31 17:52:35'),
(19, 'cart_683b441c908cb_1748714524', '[{\"ticket_type\":\"STARTER\",\"package_size\":\"XS\",\"numbers_per_package\":1,\"dollar_price_per_package\":20,\"quantity\":1}]', 106, '2025-05-31 18:02:04', '2025-05-31 18:02:52'),
(20, 'cart_683b46112aac4_1748715025', '[{\"ticket_type\":\"STARTER\",\"package_size\":\"XS\",\"numbers_per_package\":1,\"dollar_price_per_package\":20,\"quantity\":1}]', 106, '2025-05-31 18:10:25', '2025-05-31 18:10:58'),
(21, 'cart_683b465e57f6c_1748715102', '[{\"ticket_type\":\"STARTER\",\"package_size\":\"XS\",\"numbers_per_package\":1,\"dollar_price_per_package\":20,\"quantity\":1}]', 106, '2025-05-31 18:11:42', '2025-05-31 18:12:52'),
(23, 'cart_683b4708cfb5e_1748715272', '[{\"ticket_type\":\"BUSINESS\",\"package_size\":\"XS\",\"numbers_per_package\":1,\"dollar_price_per_package\":25,\"quantity\":1}]', 106, '2025-05-31 18:14:32', '2025-05-31 18:15:00'),
(24, 'cart_683b493fe9f97_1748715839', '[{\"ticket_type\":\"ULTIMATE\",\"package_size\":\"XS\",\"numbers_per_package\":1,\"dollar_price_per_package\":30,\"quantity\":1}]', 106, '2025-05-31 18:23:59', '2025-05-31 18:24:25'),
(25, 'cart_683b52513a429_1748718161', '[{\"ticket_type\":\"STARTER\",\"package_size\":\"XS\",\"numbers_per_package\":\"1\",\"dollar_price_per_package\":\"20\",\"quantity\":\"1\"},{\"ticket_type\":\"BUSINESS\",\"package_size\":\"XS\",\"numbers_per_package\":\"1\",\"dollar_price_per_package\":\"25\",\"quantity\":\"1\"},{\"ticket_type\":\"ULTIMATE\",\"package_size\":\"XS\",\"numbers_per_package\":\"1\",\"dollar_price_per_package\":\"30\",\"quantity\":\"1\"}]', NULL, '2025-05-31 19:02:41', '2025-05-31 19:03:35'),
(27, 'cart_683b657e37dca_1748723070', '[{\"ticket_type\":\"STARTER\",\"package_size\":\"XS\",\"numbers_per_package\":\"1\",\"dollar_price_per_package\":\"20\",\"quantity\":1},{\"ticket_type\":\"BUSINESS\",\"package_size\":\"XS\",\"numbers_per_package\":\"1\",\"dollar_price_per_package\":\"25\",\"quantity\":1},{\"ticket_type\":\"ULTIMATE\",\"package_size\":\"XS\",\"numbers_per_package\":\"1\",\"dollar_price_per_package\":\"30\",\"quantity\":1},{\"ticket_type\":\"STARTER\",\"package_size\":\"S\",\"numbers_per_package\":\"15\",\"dollar_price_per_package\":\"150\",\"quantity\":\"1\"},{\"ticket_type\":\"BUSINESS\",\"package_size\":\"S\",\"numbers_per_package\":\"10\",\"dollar_price_per_package\":\"200\",\"quantity\":\"1\"},{\"ticket_type\":\"ULTIMATE\",\"package_size\":\"S\",\"numbers_per_package\":\"10\",\"dollar_price_per_package\":\"250\",\"quantity\":\"1\"}]', NULL, '2025-05-31 20:24:30', '2025-05-31 20:25:31'),
(301, 'cart_686140608423d_1751203936', '[{\"ticket_type\":\"STARTER\",\"package_size\":\"XS\",\"numbers_per_package\":5,\"dollar_price_per_package\":90,\"quantity\":1}]', 209, '2025-06-29 13:32:16', NULL);

-- --------------------------------------------------------

--
-- Table structure for table `chat_messages`
--

CREATE TABLE `chat_messages` (
  `id` int(11) NOT NULL,
  `ticket_id` int(11) DEFAULT NULL,
  `user_id` int(11) DEFAULT NULL,
  `sender_id` int(11) NOT NULL,
  `sender_type` enum('user','admin') NOT NULL,
  `message` text NOT NULL,
  `is_read` tinyint(1) DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

--
-- Dumping data for table `chat_messages`
--

INSERT INTO `chat_messages` (`id`, `ticket_id`, `user_id`, `sender_id`, `sender_type`, `message`, `is_read`, `created_at`) VALUES
(1, 104, NULL, 1, 'user', 'Hello', 1, '2025-04-21 11:00:43'),
(2, 104, NULL, 1, 'user', 'can you help me fix my app?', 1, '2025-04-21 11:02:59'),
(3, 99, NULL, 1, 'admin', 'hello', 1, '2025-04-21 11:08:53'),
(4, 104, NULL, 1, 'admin', 'hiiii', 1, '2025-04-21 11:09:20'),
(5, 103, NULL, 1, 'admin', 'can you check', 1, '2025-04-21 11:09:39'),
(6, 104, NULL, 1, 'admin', 'check please', 1, '2025-04-21 11:09:54'),
(7, 104, NULL, 1, 'admin', 'yes sure', 1, '2025-04-21 11:10:08'),
(8, 104, NULL, 1, 'user', 'Hello', 1, '2025-04-22 02:27:38'),
(9, 104, NULL, 1, 'admin', 'hi, mr.test', 1, '2025-04-22 02:27:53'),
(10, 104, NULL, 1, 'user', 'how are you?', 1, '2025-04-22 02:28:12'),
(11, 104, NULL, 1, 'user', 'my app still freeze when im opening app', 1, '2025-04-22 02:34:13'),
(12, 103, NULL, 1, 'admin', 'Hello, May i help you?', 1, '2025-04-22 02:34:27'),
(13, 99, NULL, 1, 'admin', 'test 1', 1, '2025-04-22 02:40:16'),
(14, 104, NULL, 1, 'admin', 'hello can you try to re-install again?', 1, '2025-04-22 02:40:44'),
(15, 100, NULL, 1, 'admin', 'hellooooo', 1, '2025-04-22 02:41:11'),
(16, 100, NULL, 1, 'admin', 'test', 1, '2025-04-22 02:41:38'),
(17, 100, NULL, 1, 'admin', 'hello', 1, '2025-04-22 02:52:36'),
(18, 105, NULL, 1, 'user', 'hello', 1, '2025-04-22 07:55:56'),
(19, 105, NULL, 1, 'admin', 'how are you', 1, '2025-04-22 07:56:28'),
(20, 106, NULL, 1, 'user', 'Hi, Admin', 1, '2025-04-25 09:28:13'),
(21, 106, NULL, 1, 'admin', 'Hello', 1, '2025-05-02 06:00:53'),
(22, 106, NULL, 1, 'admin', 'Hello', 1, '2025-05-02 06:00:56'),
(23, 106, NULL, 1, 'user', 'test is this on quickdesk?', 1, '2025-05-02 10:48:29'),
(24, 106, NULL, 1, 'user', 'Hello', 1, '2025-05-06 02:16:21'),
(25, 106, NULL, 1, 'admin', 'HI user', 1, '2025-05-06 05:05:05'),
(26, 99, NULL, 1, 'user', 'test', 1, '2025-05-06 07:16:32'),
(27, 108, NULL, 19, 'user', 'Hello', 1, '2025-05-08 09:00:18'),
(28, 108, NULL, 19, 'user', 'how are you', 1, '2025-05-08 09:02:10'),
(29, 108, NULL, 1, 'admin', 'hello user', 1, '2025-05-08 09:02:40'),
(30, 108, NULL, 1, 'admin', 'try to uninstall', 1, '2025-05-08 09:02:57'),
(31, 108, NULL, 1, 'admin', 'test', 1, '2025-05-08 09:12:35'),
(32, 108, NULL, 1, 'admin', 'hello', 1, '2025-05-08 10:36:51'),
(33, 110, NULL, 22, 'user', 'Hello Admin', 1, '2025-05-15 08:35:42'),
(34, 110, NULL, 1, 'admin', 'Oh hello', 1, '2025-05-15 08:36:00'),
(35, 110, NULL, 1, 'admin', 'Oh hello', 1, '2025-05-15 08:36:02'),
(36, 110, NULL, 1, 'admin', 'Oh hello', 1, '2025-05-15 08:36:03'),
(37, 110, NULL, 1, 'admin', 'Test', 1, '2025-05-15 08:36:13'),
(38, 110, NULL, 22, 'user', 'help me fix', 1, '2025-05-15 08:36:24'),
(39, 110, NULL, 1, 'admin', 'test hello', 1, '2025-05-15 08:43:01'),
(40, 110, NULL, 22, 'user', 'hello', 1, '2025-05-15 09:06:38'),
(41, 110, NULL, 22, 'user', 'test', 1, '2025-05-15 09:12:14'),
(42, 110, NULL, 1, 'admin', 'hello', 1, '2025-05-15 09:12:26'),
(43, 110, NULL, 1, 'admin', 'test', 1, '2025-05-15 09:14:06'),
(44, 110, NULL, 22, 'user', 'hello', 1, '2025-05-15 09:28:25'),
(45, 113, NULL, 25, 'user', 'hello', 1, '2025-05-21 10:08:40'),
(46, 113, NULL, 1, 'admin', 'Hi', 1, '2025-05-21 10:08:54'),
(47, 113, NULL, 1, 'admin', 'Hello', 1, '2025-05-21 10:09:03'),
(48, 113, NULL, 25, 'user', 'how r u', 1, '2025-05-21 10:09:18'),
(49, 113, NULL, 1, 'admin', 'hello', 1, '2025-05-21 10:09:24'),
(50, 113, NULL, 25, 'user', 'Hello', 1, '2025-05-21 10:10:42'),
(51, 124, NULL, 1, 'user', 'Hello', 1, '2025-05-27 10:31:42'),
(52, 124, NULL, 1, 'user', 'hello admin', 1, '2025-05-27 10:32:35'),
(53, 124, NULL, 1, 'user', 'eyyy', 1, '2025-05-27 10:32:49'),
(54, 124, NULL, 1, 'user', 'hello admin', 1, '2025-05-27 10:35:53'),
(55, 123, NULL, 1, 'user', 'Hello', 1, '2025-05-27 10:36:48'),
(56, 123, NULL, 1, 'user', 'hey', 1, '2025-05-27 10:39:49'),
(57, 123, NULL, 1, 'user', 'test', 1, '2025-05-27 10:43:31'),
(58, 123, NULL, 1, 'user', 'test', 1, '2025-05-27 10:44:07'),
(59, 112, NULL, 1, 'user', 'hello', 1, '2025-05-27 10:53:22'),
(60, 112, NULL, 1, 'user', 'Hi', 1, '2025-05-27 10:54:10'),
(61, 112, NULL, 1, 'admin', 'hello', 1, '2025-05-27 10:54:35'),
(62, 112, NULL, 1, 'admin', 'hello', 1, '2025-05-27 10:54:49'),
(63, 112, NULL, 1, 'admin', 'hello', 1, '2025-05-27 10:55:05'),
(64, 112, NULL, 1, 'admin', 'hello', 1, '2025-05-27 10:55:51'),
(65, 112, NULL, 1, 'admin', 'hello', 1, '2025-05-27 10:55:55'),
(66, 112, NULL, 1, 'admin', 'hi', 1, '2025-05-27 10:56:10'),
(67, 112, NULL, 1, 'admin', 'hi2', 1, '2025-05-27 10:56:18'),
(68, 112, NULL, 1, 'admin', 'test', 1, '2025-05-27 10:59:48'),
(69, 112, NULL, 1, 'admin', 'hello', 1, '2025-05-27 11:00:35'),
(70, 123, NULL, 1, 'user', 'hiiii', 1, '2025-05-27 11:00:59'),
(71, 123, NULL, 1, 'user', 'test', 1, '2025-05-27 11:01:28'),
(72, 123, NULL, 1, 'user', 'test2', 1, '2025-05-27 11:03:31'),
(73, 123, NULL, 1, 'user', 'test3', 1, '2025-05-27 11:03:44'),
(74, 123, NULL, 1, 'user', 'test4', 1, '2025-05-27 11:04:06'),
(75, 123, NULL, 1, 'user', 'hi', 1, '2025-05-27 11:05:21'),
(76, 123, NULL, 1, 'admin', 'hi', 1, '2025-05-27 11:06:04'),
(77, 123, NULL, 1, 'admin', 'hello', 1, '2025-05-27 11:06:23'),
(78, 123, NULL, 1, 'user', 'test', 1, '2025-05-27 11:07:06'),
(79, 127, NULL, 1, 'admin', 'hi', 1, '2025-05-28 02:36:22'),
(80, 126, NULL, 1, 'admin', 'hi', 1, '2025-05-28 02:36:32'),
(81, 127, NULL, 1, 'admin', 'hello', 1, '2025-05-28 02:36:43'),
(82, 125, NULL, 1, 'admin', 'hello', 1, '2025-05-28 02:38:09'),
(83, 111, NULL, 1, 'user', 'hello', 1, '2025-05-28 02:39:22'),
(84, 111, NULL, 1, 'admin', 'hi', 1, '2025-05-28 02:39:49'),
(85, 101, NULL, 1, 'admin', 'hi', 1, '2025-05-28 02:40:31'),
(86, 128, NULL, 1, 'admin', 'hi', 1, '2025-05-28 02:44:02'),
(87, 128, NULL, 1, 'user', 'hello', 1, '2025-05-28 02:45:46'),
(88, 128, NULL, 1, 'user', 'hih', 1, '2025-05-28 02:45:53'),
(89, 128, NULL, 1, 'user', 'hi', 1, '2025-05-28 02:46:04'),
(90, 128, NULL, 1, 'admin', 'oh hello', 1, '2025-05-28 02:47:20'),
(91, 128, NULL, 1, 'user', 'hi', 1, '2025-05-28 02:48:46'),
(92, 128, NULL, 1, 'user', 'hi', 1, '2025-05-28 02:51:06'),
(93, 128, NULL, 1, 'user', 'hello', 1, '2025-05-28 02:51:14'),
(94, 128, NULL, 1, 'user', 'hi', 1, '2025-05-28 02:51:28'),
(95, 128, NULL, 1, 'user', 'hi', 1, '2025-05-28 02:52:10'),
(96, 128, NULL, 1, 'user', 'hello', 1, '2025-05-28 02:52:23'),
(97, 128, NULL, 1, 'user', 'hi', 1, '2025-05-28 02:53:27'),
(98, 128, NULL, 1, 'user', 'hi', 1, '2025-05-28 02:55:21'),
(99, 128, NULL, 1, 'user', '9999', 1, '2025-05-28 02:55:29'),
(100, 128, NULL, 1, 'user', '10', 1, '2025-05-28 02:55:45'),
(101, 128, NULL, 1, 'admin', '11', 1, '2025-05-28 02:58:11'),
(102, 128, NULL, 1, 'admin', '7', 1, '2025-05-28 02:58:20'),
(103, 125, NULL, 1, 'user', 'hi', 1, '2025-05-28 03:03:12'),
(104, 125, NULL, 1, 'user', 'hello', 1, '2025-05-28 03:03:25'),
(105, 128, NULL, 1, 'admin', 'hi', 1, '2025-05-28 03:03:33'),
(106, 128, NULL, 1, 'admin', 'test', 1, '2025-05-28 03:03:58'),
(107, 128, NULL, 1, 'admin', 'hi', 1, '2025-05-28 03:04:12'),
(108, 128, NULL, 1, 'admin', 'a', 1, '2025-05-28 03:04:41'),
(109, 128, NULL, 1, 'admin', 'a', 1, '2025-05-28 03:04:49'),
(110, 128, NULL, 1, 'admin', 'a', 1, '2025-05-28 03:04:53'),
(111, 128, NULL, 1, 'admin', 'aaaaaa', 1, '2025-05-28 03:05:38'),
(112, 128, NULL, 1, 'admin', 'aaaaaaaaa', 1, '2025-05-28 03:06:19'),
(113, 128, NULL, 1, 'user', '??', 1, '2025-05-28 03:10:04'),
(114, 128, NULL, 1, 'admin', 'hhii', 1, '2025-05-28 03:10:31'),
(115, 128, NULL, 1, 'admin', 'hi', 1, '2025-05-28 03:19:45'),
(116, 128, NULL, 1, 'admin', 's', 1, '2025-05-28 03:21:21'),
(117, 128, NULL, 1, 'admin', 'h', 1, '2025-05-28 03:33:22'),
(118, 128, NULL, 1, 'user', 'i', 1, '2025-05-28 03:34:44'),
(119, 128, NULL, 1, 'admin', '2', 1, '2025-05-28 03:36:41'),
(120, 128, NULL, 1, 'admin', '2', 1, '2025-05-28 03:38:12'),
(121, 128, NULL, 1, 'admin', '1', 1, '2025-05-28 03:38:17'),
(122, 128, NULL, 1, 'admin', 'hello', 1, '2025-05-28 03:38:26'),
(123, 128, NULL, 1, 'admin', '1', 1, '2025-05-28 03:40:49'),
(124, 128, NULL, 1, 'admin', 's', 1, '2025-05-28 03:42:35'),
(125, 128, NULL, 1, 'admin', 's', 1, '2025-05-28 03:49:13'),
(126, 128, NULL, 1, 'admin', 'Test', 1, '2025-05-28 03:49:25'),
(127, 128, NULL, 1, 'admin', 'hello', 1, '2025-05-28 03:49:38'),
(128, 128, NULL, 1, 'admin', 'hello', 1, '2025-05-28 03:50:00'),
(129, 128, NULL, 1, 'admin', 'test', 1, '2025-05-28 03:50:40'),
(130, 128, NULL, 1, 'admin', 's', 1, '2025-05-28 03:53:00'),
(131, 128, NULL, 1, 'user', 'hu', 1, '2025-05-28 03:58:36'),
(132, 128, NULL, 1, 'admin', 'hi', 1, '2025-05-28 04:08:56'),
(133, 128, NULL, 1, 'admin', 'hello', 1, '2025-05-28 04:09:10'),
(134, 128, NULL, 1, 'admin', 'e', 1, '2025-05-28 04:12:05'),
(135, 128, NULL, 1, 'user', 'hello', 1, '2025-05-28 04:12:18'),
(136, 128, NULL, 1, 'user', 'hi', 1, '2025-05-28 04:12:44'),
(137, 128, NULL, 1, 'user', 'hiii', 1, '2025-05-28 04:19:41'),
(138, 128, NULL, 1, 'user', 'eyy', 1, '2025-05-28 04:19:52'),
(139, 126, NULL, 1, 'user', 'Hello admin, I need help with my issue!', 1, '2025-05-28 04:22:12'),
(140, 127, NULL, 1, 'user', 'This is urgent, please respond ASAP', 1, '2025-05-28 04:22:12'),
(141, 128, NULL, 1, 'user', 'Can you check my previous message?', 1, '2025-05-28 04:22:12'),
(142, 111, NULL, 1, 'user', 'Hello admin, I need help with my starter ticket issue!', 1, '2025-05-28 04:22:45'),
(143, 112, NULL, 1, 'user', 'This is urgent, please respond ASAP for my business ticket', 1, '2025-05-28 04:22:45'),
(144, 104, NULL, 1, 'user', 'Can you check my previous message about the app freezing?', 1, '2025-05-28 04:22:45'),
(145, 128, NULL, 1, 'user', 'etst', 1, '2025-05-28 04:27:52'),
(146, 128, NULL, 1, 'user', 'Hello', 1, '2025-05-28 04:29:00'),
(147, 128, NULL, 1, 'user', 'Hello', 1, '2025-05-28 04:29:57'),
(148, 128, NULL, 1, 'user', 'Test unread message #1 for ticket #128', 1, '2025-05-28 04:34:20'),
(149, 127, NULL, 1, 'user', 'Test unread message #2 for ticket #127', 1, '2025-05-28 04:34:20'),
(150, 126, NULL, 1, 'user', 'Test unread message #3 for ticket #126', 1, '2025-05-28 04:34:20'),
(151, 128, NULL, 1, 'user', '2', 1, '2025-05-28 04:35:41'),
(152, 128, NULL, 1, 'user', 'Helooooooo', 1, '2025-05-28 04:36:19'),
(153, 132, NULL, 40, 'user', 'hi', 1, '2025-05-28 10:57:25'),
(154, 132, NULL, 1, 'admin', 'oo', 1, '2025-05-28 10:59:54'),
(155, 131, NULL, 1, 'admin', 'hello', 1, '2025-05-28 11:00:11'),
(156, 131, NULL, 1, 'admin', 'hi', 1, '2025-05-28 11:00:38'),
(157, 131, NULL, 1, 'admin', 'jjjj', 1, '2025-05-28 11:00:41'),
(158, 130, NULL, 1, 'admin', 'd', 1, '2025-05-28 11:01:25'),
(159, 129, NULL, 1, 'admin', 's', 1, '2025-05-28 11:02:09'),
(160, 133, NULL, 1, 'admin', 'hi', 0, '2025-05-28 11:06:03'),
(161, 131, NULL, 1, 'user', 'test', 1, '2025-05-29 02:15:58'),
(162, 131, NULL, 1, 'user', 'test', 1, '2025-05-29 02:18:10'),
(163, 131, NULL, 1, 'user', 'hellloo', 1, '2025-05-29 02:18:18'),
(164, 131, NULL, 1, 'user', 'test', 1, '2025-05-29 02:19:14'),
(165, 131, NULL, 1, 'user', 'test', 1, '2025-05-29 02:20:38'),
(166, 128, NULL, 1, 'admin', 'hi', 1, '2025-05-29 02:22:22'),
(167, 128, NULL, 1, 'admin', '.', 1, '2025-05-29 02:22:31'),
(168, 128, NULL, 1, 'admin', 'test', 1, '2025-05-29 02:25:08'),
(169, 128, NULL, 1, 'admin', '.', 1, '2025-05-29 02:25:49'),
(170, 131, NULL, 1, 'user', 'hi admin', 1, '2025-05-29 02:26:11'),
(171, 131, NULL, 1, 'user', 'test', 1, '2025-05-29 02:26:40'),
(172, 131, NULL, 1, 'user', 'test', 1, '2025-05-29 02:28:20'),
(173, 131, NULL, 1, 'user', 'hi', 1, '2025-05-29 02:33:38'),
(174, 104, NULL, 1, 'admin', 'test', 1, '2025-05-29 02:36:12'),
(175, 131, NULL, 1, 'user', 'test', 1, '2025-05-29 02:38:46'),
(176, 103, NULL, 1, 'admin', '.', 1, '2025-05-29 02:43:07'),
(177, 131, NULL, 1, 'user', '.', 1, '2025-05-29 02:46:13'),
(178, 131, NULL, 1, 'user', 'test', 1, '2025-05-29 02:46:27'),
(179, 102, NULL, 1, 'admin', '.', 1, '2025-05-29 02:46:42'),
(180, 102, NULL, 1, 'admin', '.', 1, '2025-05-29 02:47:30'),
(181, 102, NULL, 1, 'admin', 'he', 1, '2025-05-29 02:48:14'),
(182, 131, NULL, 1, 'admin', 'hello', 1, '2025-05-29 02:48:25'),
(183, 130, NULL, 1, 'admin', '.', 1, '2025-05-29 02:48:34'),
(184, 130, NULL, 1, 'admin', '.', 1, '2025-05-29 02:48:42'),
(185, 131, NULL, 1, 'admin', 'test', 1, '2025-05-29 02:48:46'),
(186, 99, NULL, 1, 'admin', '.', 1, '2025-05-29 02:48:52'),
(187, 99, NULL, 1, 'admin', '.', 1, '2025-05-29 02:48:55'),
(188, 134, NULL, 1, 'admin', 'test', 1, '2025-05-29 02:50:39'),
(189, 134, NULL, 1, 'admin', 'hello', 1, '2025-05-29 02:50:47'),
(190, 134, NULL, 1, 'admin', 'hi', 1, '2025-05-29 02:54:23'),
(191, 135, NULL, 1, 'user', 'hi', 1, '2025-05-29 03:02:34'),
(192, 135, NULL, 1, 'admin', 'oh hello', 1, '2025-05-29 03:02:48'),
(193, 135, NULL, 1, 'admin', 'hi', 1, '2025-05-29 03:02:57'),
(194, 135, NULL, 1, 'admin', 'test', 1, '2025-05-29 03:07:19'),
(195, 136, NULL, 1, 'admin', 'hi', 1, '2025-05-29 03:08:42'),
(196, 137, NULL, 1, 'admin', 'hi', 1, '2025-05-29 03:15:27'),
(197, 138, NULL, 1, 'admin', 'hi', 1, '2025-05-29 03:29:44'),
(198, 138, NULL, 1, 'admin', '.', 1, '2025-05-29 03:30:10'),
(199, 138, NULL, 1, 'admin', 'hello', 1, '2025-05-29 03:30:27'),
(200, 139, NULL, 1, 'admin', 'hi', 1, '2025-05-29 03:37:05'),
(201, 139, NULL, 1, 'user', 'test', 1, '2025-05-29 03:37:35'),
(202, 139, NULL, 1, 'user', 'test', 1, '2025-05-29 03:37:44'),
(203, 140, NULL, 1, 'admin', 'hi', 1, '2025-05-29 03:41:23'),
(204, 140, NULL, 1, 'admin', 'test', 1, '2025-05-29 03:41:42'),
(205, 140, NULL, 1, 'admin', 'hi', 1, '2025-05-29 03:44:18'),
(206, 141, NULL, 1, 'admin', 'hi', 1, '2025-05-29 03:46:31'),
(207, 141, NULL, 1, 'admin', 'aaaaaaaaa', 1, '2025-05-29 03:46:48'),
(208, 142, NULL, 1, 'admin', 'j', 1, '2025-05-29 03:49:29'),
(209, 142, NULL, 1, 'admin', '.', 1, '2025-05-29 03:49:42'),
(210, 142, NULL, 1, 'user', 'hi adin', 1, '2025-05-29 04:02:11'),
(211, 143, NULL, 1, 'admin', 'hello', 1, '2025-05-29 04:03:29'),
(212, 143, NULL, 1, 'admin', '.', 1, '2025-05-29 04:03:47'),
(213, 144, NULL, 1, 'admin', 'hi', 1, '2025-05-29 04:08:17'),
(214, 145, NULL, 1, 'admin', 'hello', 1, '2025-05-29 04:11:29'),
(215, 145, NULL, 1, 'admin', '.', 1, '2025-05-29 04:11:42'),
(216, 145, NULL, 1, 'admin', '.', 1, '2025-05-29 04:13:03'),
(217, 145, NULL, 1, 'admin', 'helloo', 1, '2025-05-29 04:13:23'),
(218, 146, NULL, 1, 'admin', 'hi', 1, '2025-05-29 04:15:25'),
(219, 146, NULL, 1, 'admin', 'hiiiiiii', 1, '2025-05-29 04:16:00'),
(220, 146, NULL, 1, 'admin', 'test', 1, '2025-05-29 04:17:01'),
(221, 146, NULL, 1, 'admin', 'h', 1, '2025-05-29 04:19:32'),
(222, 146, NULL, 1, 'admin', 'etst', 1, '2025-05-29 04:19:35'),
(223, 147, NULL, 1, 'admin', 'hi', 1, '2025-05-29 04:20:01'),
(224, 147, NULL, 1, 'user', 'hello', 1, '2025-05-29 04:20:22'),
(225, 148, NULL, 1, 'admin', 'hiiiiiiii', 1, '2025-05-29 04:23:44'),
(226, 148, NULL, 1, 'admin', 'yesss finally auto update', 1, '2025-05-29 04:23:58'),
(227, 148, NULL, 1, 'admin', 's', 1, '2025-05-29 04:27:56'),
(228, 148, NULL, 1, 'user', 'sssssssss', 1, '2025-05-29 04:28:19'),
(229, 133, NULL, 1, 'admin', 'hello', 0, '2025-05-29 04:36:25'),
(230, 151, NULL, 105, 'user', 'test', 1, '2025-05-31 19:00:05'),
(231, 151, NULL, 1, 'admin', 'oh hi', 1, '2025-05-31 19:00:19'),
(232, 151, NULL, 105, 'user', 'how r u', 1, '2025-05-31 19:00:31'),
(233, 154, NULL, 1, 'admin', 'hello', 1, '2025-06-02 04:36:08'),
(234, 154, NULL, 1, 'admin', 'how can i help you?', 1, '2025-06-02 04:36:14'),
(235, 154, NULL, 132, 'user', 'oh hello admin', 1, '2025-06-02 04:36:25'),
(236, 165, NULL, 1, 'admin', 'hello', 1, '2025-06-04 10:52:33'),
(237, 166, 142, 142, 'user', 'hi admin', 1, '2025-06-04 11:19:34'),
(238, 177, 150, 150, 'user', 'hihihi', 1, '2025-06-05 10:41:20'),
(239, 177, 150, 150, 'user', 'hello', 1, '2025-06-05 10:51:28'),
(240, 177, 150, 1, 'admin', 'hello', 1, '2025-06-05 10:51:55'),
(241, 177, 150, 1, 'admin', 'hello', 1, '2025-06-05 10:53:31'),
(242, 177, 150, 150, 'user', 'hi', 1, '2025-06-05 10:54:07'),
(243, 177, 150, 1, 'admin', 'how are you', 1, '2025-06-05 10:56:13'),
(244, 177, 150, 150, 'user', 'im fine', 1, '2025-06-05 10:56:28'),
(245, 177, 150, 1, 'admin', 'good', 1, '2025-06-05 10:56:34'),
(246, 177, 150, 150, 'user', 'hi', 1, '2025-06-05 10:59:36'),
(247, 177, 150, 1, 'admin', 'oh helloooooooooooooooooooooooooooooooooooooooooo', 1, '2025-06-05 10:59:46'),
(248, 177, 150, 150, 'user', 'hey', 1, '2025-06-05 11:00:47'),
(249, 177, 150, 1, 'admin', '???', 1, '2025-06-05 11:00:56'),
(250, 177, 150, 150, 'user', 'hi', 1, '2025-06-05 11:03:26'),
(251, 177, 150, 1, 'admin', '.', 1, '2025-06-05 11:03:36'),
(252, 177, 150, 150, 'user', '.', 1, '2025-06-05 11:03:48'),
(253, 177, 150, 150, 'user', 'test', 1, '2025-06-05 11:04:16'),
(254, 177, 150, 1, 'admin', 'hello', 1, '2025-06-05 11:04:45'),
(255, 177, 150, 150, 'user', 'hi', 1, '2025-06-05 11:06:10'),
(256, 177, 150, 1, 'admin', 'hello', 1, '2025-06-05 11:06:15'),
(257, 177, 150, 1, 'admin', 'test', 1, '2025-06-05 11:07:34'),
(258, 177, 150, 150, 'user', 'test', 1, '2025-06-05 11:07:59'),
(259, 177, 150, 150, 'user', 'hi', 1, '2025-06-05 11:09:58'),
(260, 177, 150, 150, 'user', 'tst', 1, '2025-06-05 11:10:19'),
(261, 177, 150, 150, 'user', 'hello', 1, '2025-06-05 11:10:35'),
(262, 177, 150, 1, 'admin', 'hello', 1, '2025-06-05 11:10:51'),
(263, 177, 150, 150, 'user', 'hello', 1, '2025-06-05 11:11:18'),
(264, 177, 150, 150, 'user', 'test', 1, '2025-06-05 11:13:22'),
(265, 177, 150, 150, 'user', 'hey', 1, '2025-06-05 11:13:34'),
(266, 177, 150, 1, 'admin', 'yes', 1, '2025-06-05 11:13:40'),
(267, 177, 150, 150, 'user', 'hi', 1, '2025-06-05 11:14:00'),
(268, 178, 150, 150, 'user', 'hello', 1, '2025-06-05 11:38:19'),
(269, 178, 150, 4, 'admin', 'hi', 0, '2025-06-06 07:33:47'),
(270, 165, NULL, 4, 'admin', 'hi', 1, '2025-06-06 07:34:03'),
(271, 165, NULL, 4, 'admin', 'hello user', 1, '2025-06-06 09:33:26'),
(272, 165, NULL, 1, 'user', 'hi', 1, '2025-06-09 10:05:18'),
(273, 182, 168, 1, 'admin', 'hello', 1, '2025-06-10 03:44:54'),
(277, 155, NULL, 1, 'admin', 'hi', 1, '2025-06-11 09:20:17'),
(278, 155, NULL, 1, 'admin', 'hello', 1, '2025-06-11 09:23:34'),
(279, 184, NULL, 1, 'admin', 'Hi', 1, '2025-06-12 06:23:31'),
(280, 185, NULL, 1, 'admin', 'oh hello user', 1, '2025-06-12 06:34:51'),
(281, 185, NULL, 8, 'admin', 'hi user', 1, '2025-06-12 06:37:19'),
(282, 185, NULL, 1, 'user', 'hi admin', 1, '2025-06-12 06:46:29'),
(283, 185, NULL, 1, 'user', 'how are you?', 1, '2025-06-12 06:46:37'),
(284, 186, NULL, 1, 'user', 'hello admin', 1, '2025-06-12 10:19:23'),
(285, 186, NULL, 1, 'admin', 'hihi', 1, '2025-06-12 10:19:31'),
(286, 187, NULL, 1, 'user', 'Hello\r\nhow are you\r\nare you good\r\nhihi\r\na\r\na\r\na\r\na\r\na', 1, '2025-06-13 02:27:13'),
(287, 187, NULL, 1, 'admin', 'hii', 1, '2025-06-13 02:30:05'),
(288, 188, NULL, 1, 'user', 'hihi', 1, '2025-06-13 03:02:42'),
(289, 189, NULL, 1, 'user', 'Hi admin', 1, '2025-06-13 03:45:12'),
(290, 189, NULL, 1, 'user', 'i have problem with my screen', 1, '2025-06-13 03:45:22'),
(291, 189, NULL, 1, 'user', 'can you please tell me how to fix it', 1, '2025-06-13 03:45:33'),
(292, 190, NULL, 1, 'user', 'hi admin this is testing', 1, '2025-06-13 03:49:02'),
(293, 190, NULL, 1, 'user', 'test', 1, '2025-06-13 03:49:05'),
(294, 190, NULL, 1, 'user', 'test', 1, '2025-06-13 03:49:05'),
(295, 190, NULL, 1, 'user', 'hello', 1, '2025-06-13 03:49:06'),
(296, 190, NULL, 1, 'user', 'hello', 1, '2025-06-13 03:49:06'),
(297, 190, NULL, 1, 'admin', 'hello user', 1, '2025-06-13 03:53:37'),
(298, 191, NULL, 1, 'user', 'hello', 1, '2025-06-13 03:55:12'),
(299, 191, NULL, 1, 'admin', 'hi', 1, '2025-06-13 03:56:10'),
(300, 189, NULL, 1, 'admin', 'hello', 1, '2025-06-13 03:56:28'),
(301, 188, NULL, 1, 'admin', 'test', 1, '2025-06-13 04:01:37'),
(302, 188, NULL, 1, 'admin', 'etst', 1, '2025-06-13 04:03:04'),
(303, 191, NULL, 1, 'user', 'hi admin', 1, '2025-06-13 04:03:49'),
(304, 191, NULL, 1, 'admin', 'hello', 1, '2025-06-13 04:12:35'),
(305, 191, NULL, 1, 'admin', 'hhhh', 1, '2025-06-13 04:12:45'),
(306, 191, NULL, 1, 'user', 'hi', 1, '2025-06-13 04:13:34'),
(307, 191, NULL, 1, 'admin', 'Hi user', 1, '2025-06-13 04:17:59'),
(308, 191, NULL, 1, 'admin', 'hihihihi', 1, '2025-06-13 04:18:19'),
(309, 191, NULL, 1, 'admin', 'hi', 1, '2025-06-13 04:21:26'),
(310, 191, NULL, 1, 'admin', 'hihi', 1, '2025-06-13 04:22:10'),
(311, 191, NULL, 1, 'admin', 'hello', 1, '2025-06-13 04:22:24'),
(312, 159, NULL, 1, 'admin', 'hi', 1, '2025-06-13 04:27:10'),
(313, 159, NULL, 1, 'admin', 'h', 1, '2025-06-13 04:27:24'),
(314, 159, NULL, 1, 'admin', 'hi', 1, '2025-06-13 04:27:43'),
(315, 159, NULL, 1, 'admin', 'ye', 1, '2025-06-13 04:28:08'),
(316, 159, NULL, 1, 'admin', 'hi', 1, '2025-06-13 04:29:09'),
(317, 191, NULL, 1, 'admin', 'hihih', 1, '2025-06-13 04:29:17'),
(318, 191, NULL, 1, 'admin', 'test', 1, '2025-06-13 04:30:39'),
(319, 158, NULL, 1, 'admin', 'hello', 1, '2025-06-13 04:31:12'),
(320, 191, NULL, 1, 'user', 'test', 1, '2025-06-13 04:31:34'),
(321, 191, NULL, 1, 'user', 's', 1, '2025-06-13 04:32:41'),
(322, 191, NULL, 1, 'user', 'hello', 1, '2025-06-13 04:36:25'),
(323, 191, NULL, 1, 'user', 'test', 1, '2025-06-13 04:36:30'),
(324, 191, NULL, 1, 'admin', 'Hi user', 1, '2025-06-13 04:37:20'),
(325, 191, NULL, 1, 'user', 'hello', 1, '2025-06-13 04:37:35'),
(326, 191, NULL, 1, 'user', 'r', 1, '2025-06-13 04:37:43'),
(327, 157, NULL, 1, 'admin', 'hi', 1, '2025-06-13 04:38:22'),
(328, 191, NULL, 1, 'admin', 'hi', 1, '2025-06-13 04:38:29'),
(329, 191, NULL, 1, 'admin', 'test', 1, '2025-06-13 04:38:34'),
(330, 192, NULL, 1, 'user', 'hello', 1, '2025-06-13 05:02:12'),
(331, 192, NULL, 1, 'admin', 'hi', 1, '2025-06-13 05:02:17'),
(332, 196, NULL, 1, 'admin', 'hello', 1, '2025-06-16 08:33:37'),
(333, 197, NULL, 1, 'admin', 'hi', 1, '2025-06-16 08:39:33'),
(334, 198, NULL, 1, 'admin', 'hi', 1, '2025-06-16 08:48:46'),
(335, 199, NULL, 1, 'admin', 'hihi', 1, '2025-06-16 09:27:37'),
(336, 200, NULL, 1, 'admin', 'test', 1, '2025-06-16 09:33:44'),
(337, 199, NULL, 1, 'admin', 'Test admin message for sync', 1, '2025-06-16 09:39:36'),
(338, 201, NULL, 1, 'admin', 'hi', 1, '2025-06-16 09:42:14'),
(339, 202, NULL, 1, 'admin', 'hi', 1, '2025-06-16 09:54:41'),
(340, 203, NULL, 1, 'admin', 'hi admin', 1, '2025-06-16 10:08:47'),
(341, 204, NULL, 1, 'admin', 'hello', 1, '2025-06-16 10:12:51'),
(342, 252, 185, 185, 'user', 'hi admin', 1, '2025-06-23 07:41:22'),
(343, 252, 185, 185, 'user', 'pls help me\r\nto send message', 1, '2025-06-23 07:41:33'),
(344, 251, 185, 185, 'user', 'hihihi', 1, '2025-06-23 07:42:13'),
(345, 251, 185, 185, 'user', 'helloooo', 1, '2025-06-23 07:50:05'),
(346, 251, 185, 185, 'user', 'yyy', 1, '2025-06-23 07:50:30'),
(347, 251, 185, 185, 'user', 'hello', 1, '2025-06-23 07:51:02'),
(348, 251, 185, 185, 'user', 'hello', 1, '2025-06-23 07:55:53'),
(349, 255, 185, 185, 'user', 'Test customer message sent at 2025-06-23 07:58:44 - This should trigger a notification!', 1, '2025-06-23 07:58:44'),
(350, 251, 185, 185, 'user', 'hi', 1, '2025-06-23 08:02:00'),
(351, 251, 185, 185, 'user', 'hellooo', 1, '2025-06-23 08:02:07'),
(352, 255, 185, 185, 'user', 'Test customer message sent at 2025-06-23 08:02:26 - This should trigger a notification!', 1, '2025-06-23 08:02:26'),
(353, 251, 185, 185, 'user', 'test', 1, '2025-06-23 08:02:45'),
(354, 251, 185, 185, 'user', 'a', 1, '2025-06-23 08:02:46'),
(355, 251, 185, 185, 'user', 'b', 1, '2025-06-23 08:02:49'),
(356, 251, 185, 185, 'user', 'c', 1, '2025-06-23 08:02:49'),
(357, 251, 185, 185, 'user', 'd', 1, '2025-06-23 08:02:50'),
(358, 251, 185, 185, 'user', 'd', 1, '2025-06-23 08:02:59'),
(359, 251, 185, 185, 'user', 'f', 1, '2025-06-23 08:02:59'),
(360, 251, 185, 185, 'user', 'a', 1, '2025-06-23 08:03:01'),
(361, 255, 185, 185, 'user', 'Global notification test message from user ID 185 at 2025-06-23 08:10:31', 1, '2025-06-23 08:10:31'),
(362, 251, 185, 185, 'user', 'hello', 1, '2025-06-23 08:10:56'),
(363, 251, 185, 185, 'user', 'test', 1, '2025-06-23 08:13:59'),
(364, NULL, 194, 194, 'user', 'hello', 1, '2025-06-26 10:23:28'),
(365, NULL, 194, 194, 'user', 'hihihi', 1, '2025-06-26 10:27:52'),
(366, 0, NULL, 1, 'admin', 'oh hello', 0, '2025-06-26 10:28:16'),
(367, 0, NULL, 1, 'admin', 'hello', 0, '2025-06-26 10:28:36'),
(368, NULL, 194, 194, 'user', 'hi', 1, '2025-06-26 10:33:14'),
(369, NULL, 194, 194, 'user', 'test', 1, '2025-06-26 11:00:38'),
(370, NULL, 194, 194, 'user', 'ssss', 1, '2025-06-26 11:00:42'),
(371, NULL, 191, 191, 'user', 'GG', 1, '2025-06-26 11:10:39'),
(372, NULL, 191, 1, 'admin', 'oh hi user', 1, '2025-06-26 11:17:00'),
(373, NULL, 191, 191, 'user', 'haloo', 1, '2025-06-26 11:17:05'),
(374, NULL, 198, 198, 'user', 'hello', 1, '2025-06-27 02:27:10'),
(375, NULL, 198, 1, 'admin', 'hello\\r\\n', 0, '2025-06-27 03:46:21'),
(376, NULL, 200, 200, 'user', 'hello', 1, '2025-06-27 09:15:12'),
(377, NULL, 200, 1, 'admin', 'oh hi user ', 1, '2025-06-27 09:15:23'),
(378, NULL, 200, 200, 'user', 'how are you', 1, '2025-06-27 09:15:32'),
(379, NULL, 200, 200, 'user', 'hi', 1, '2025-06-27 09:35:02'),
(380, NULL, 200, 200, 'user', 'helo', 1, '2025-06-27 09:35:06'),
(381, NULL, 200, 1, 'admin', 'hiii\\r\\n', 1, '2025-06-27 09:35:16'),
(382, NULL, 200, 1, 'admin', 'hi', 1, '2025-06-27 09:35:33'),
(383, NULL, 200, 200, 'user', 'hello', 1, '2025-06-27 09:35:42'),
(384, NULL, 200, 1, 'admin', 'hi\\r\\n', 1, '2025-06-27 09:35:53'),
(385, NULL, 200, 200, 'user', 'Hello', 0, '2025-06-28 14:15:40'),
(386, 286, NULL, 1, 'admin', 'hello', 0, '2025-06-28 14:43:15'),
(387, 286, NULL, 200, 'user', 'oh hi', 0, '2025-06-28 14:43:42'),
(388, 286, NULL, 1, 'admin', 'hihi', 0, '2025-06-28 14:45:26'),
(389, 287, NULL, 200, 'user', 'Hello', 0, '2025-06-29 07:52:01'),
(390, 300, NULL, 209, 'user', 'Hi i have issue', 0, '2025-06-29 10:23:47'),
(391, 300, NULL, 1, 'admin', 'Hi user how can i help??', 0, '2025-06-29 10:24:03');

-- --------------------------------------------------------

--
-- Table structure for table `customer_sync_logs`
--

CREATE TABLE `customer_sync_logs` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `operation` varchar(50) NOT NULL,
  `success` tinyint(1) NOT NULL,
  `message` text DEFAULT NULL,
  `changes` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`changes`)),
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `customer_sync_logs`
--

INSERT INTO `customer_sync_logs` (`id`, `user_id`, `operation`, `success`, `message`, `changes`, `created_at`) VALUES
(1, 179, 'bulk_sync', 1, 'Customer updated from Appika: first_name, last_name', '{\"first_name\":{\"old\":\"dev03 (Updated)\",\"new\":\"dev03\",\"appika_value\":\"dev03\"},\"last_name\":{\"old\":\" (Sim)\",\"new\":\"\",\"appika_value\":\"dev03\"}}', '2025-06-19 06:43:10'),
(2, 179, 'bulk_sync', 1, 'Customer updated from Appika: first_name, last_name', '{\"first_name\":{\"old\":\"dev03 (Updated)\",\"new\":\"dev03\",\"appika_value\":\"dev03\"},\"last_name\":{\"old\":\" (Sim)\",\"new\":\"\",\"appika_value\":\"dev03\"}}', '2025-06-19 06:53:50'),
(3, 180, 'simulation', 1, 'Simulated Appika customer update', '{\"first_name\":{\"old\":\"wat\",\"new\":\"wat (Updated)\"},\"last_name\":{\"old\":\"\",\"new\":\" (Sim)\"},\"tell\":{\"old\":\"\",\"new\":\"999-\"},\"company_name\":{\"old\":\"\",\"new\":\" (Simulated)\"},\"tax_id\":{\"old\":\"\",\"new\":\"TAX_SIM\"},\"address\":{\"old\":\"add2\",\"new\":\"add2 (Updated)\"},\"address2\":{\"old\":\"add1\",\"new\":\"add1 (Simulated)\"},\"district\":{\"old\":\"onnut\",\"new\":\"onnut Sim\"},\"city\":{\"old\":\"bangkok\",\"new\":\"bangkok (Updated)\"},\"state\":{\"old\":\"\\u0e01\\u0e23\\u0e38\\u0e07\\u0e40\\u0e17\\u0e1e\\u0e21\\u0e2b\\u0e32\\u0e19\\u0e04\\u0e23\",\"new\":\"\\u0e01\\u0e23\\u0e38\\u0e07\\u0e40\\u0e17\\u0e1e\\u0e21\\u0e2b\\u0e32\\u0e19\\u0e04\\u0e23 (Sim)\"},\"postal_code\":{\"old\":\"10110\",\"new\":\"101109\"},\"country\":{\"old\":\"TH\",\"new\":\"TH_SIM\"}}', '2025-06-19 07:48:21'),
(4, 182, 'simulation', 1, 'Simulated Appika customer update', '{\"first_name\":{\"old\":\"Editwathelloit\",\"new\":\"Editwathelloit (Updated)\"},\"last_name\":{\"old\":\"\",\"new\":\" (Sim)\"},\"tell\":{\"old\":\"\",\"new\":\"999-\"},\"company_name\":{\"old\":\"\",\"new\":\" (Simulated)\"},\"username\":{\"old\":\"HC182\",\"new\":\"HC182_sim\"},\"tax_id\":{\"old\":\"\",\"new\":\"TAX_SIM\"},\"address\":{\"old\":\"Edadd2\",\"new\":\"Edadd2 (Updated)\"},\"address2\":{\"old\":\"Edadd1\",\"new\":\"Edadd1 (Simulated)\"},\"district\":{\"old\":\"Edonnut\",\"new\":\"Edonnut Sim\"},\"city\":{\"old\":\"Edbangkok\",\"new\":\"Edbangkok (Updated)\"},\"state\":{\"old\":\"Ed\\u0e01\\u0e23\\u0e38\\u0e07\\u0e40\\u0e17\\u0e1e\\u0e21\\u0e2b\\u0e32\\u0e19\\u0e04\\u0e23\",\"new\":\"Ed\\u0e01\\u0e23\\u0e38\\u0e07\\u0e40\\u0e17\\u0e1e\\u0e21\\u0e2b\\u0e32\\u0e19\\u0e04\\u0e23 (Sim)\"},\"postal_code\":{\"old\":\"10110\",\"new\":\"101109\"},\"country\":{\"old\":\"TH\",\"new\":\"TH_SIM\"}}', '2025-06-19 08:00:57'),
(5, 185, 'simulation', 1, 'Simulated Appika customer update', '{\"first_name\":{\"old\":\"developer full name\",\"new\":\"developer full name (Updated)\"},\"last_name\":{\"old\":\"\",\"new\":\" (Sim)\"},\"tell\":{\"old\":\"\",\"new\":\"999-\"},\"company_name\":{\"old\":\"\",\"new\":\" (Simulated)\"},\"username\":{\"old\":\"HC185\",\"new\":\"HC185_sim\"},\"tax_id\":{\"old\":\"\",\"new\":\"TAX_SIM\"},\"address\":{\"old\":\"412 Soi bankeng\",\"new\":\"412 Soi bankeng (Updated)\"},\"address2\":{\"old\":\"ban tab chang bangkok 10240\",\"new\":\"ban tab chang bangkok 10240 (Simulated)\"},\"district\":{\"old\":\"ban tab chang\",\"new\":\"ban tab chang Sim\"},\"city\":{\"old\":\"bangkok\",\"new\":\"bangkok (Updated)\"},\"state\":{\"old\":\"\\u0e01\\u0e23\\u0e38\\u0e07\\u0e40\\u0e17\\u0e1e\\u0e21\\u0e2b\\u0e32\\u0e19\\u0e04\\u0e23\",\"new\":\"\\u0e01\\u0e23\\u0e38\\u0e07\\u0e40\\u0e17\\u0e1e\\u0e21\\u0e2b\\u0e32\\u0e19\\u0e04\\u0e23 (Sim)\"},\"postal_code\":{\"old\":\"10110\",\"new\":\"101109\"},\"country\":{\"old\":\"TH\",\"new\":\"TH_SIM\"}}', '2025-06-23 07:12:21'),
(6, 185, 'bulk_sync', 1, 'Customer updated from Appika: first_name, last_name', '{\"first_name\":{\"old\":\"developer full name\",\"new\":\"developer\",\"appika_value\":\"developer full name TTTTT\"},\"last_name\":{\"old\":\" \",\"new\":\"full name TTTTT\",\"appika_value\":\"developer full name TTTTT\"}}', '2025-06-24 09:42:13'),
(7, 185, 'location_sync', 1, 'Location updated from Appika: address, city', '{\"address\":{\"old\":\"412 Soi bankeng\",\"new\":\"412 Soi bankeng sukhumvit 48\",\"appika_value\":\"412 Soi bankeng sukhumvit 48\"},\"city\":{\"old\":\"bangkok\",\"new\":\"bangkok-th\",\"appika_value\":\"bangkok-th\"}}', '2025-06-24 09:57:48'),
(8, 185, 'location_sync', 1, 'Location updated from Appika: tell', '{\"tell\":{\"old\":\"\",\"new\":\"02 999 9999\",\"appika_value\":\"02 999 9999\"}}', '2025-06-24 10:08:12'),
(9, 184, 'location_sync', 1, 'Location updated from Appika: state', '{\"state\":{\"old\":\"\\u0e01\\u0e23\\u0e38\\u0e07\\u0e40\\u0e17\\u0e1e\\u0e21\\u0e2b\\u0e32\\u0e19\\u0e04\\u0e23\",\"new\":\"\\u0e01\\u0e23\\u0e38\\u0e07\\u0e40\\u0e17\\u0e1e\\u0e21\\u0e2b\\u0e32\",\"appika_value\":\"\\u0e01\\u0e23\\u0e38\\u0e07\\u0e40\\u0e17\\u0e1e\\u0e21\\u0e2b\\u0e32\"}}', '2025-06-24 10:08:13'),
(10, 180, 'location_sync', 1, 'Location updated from Appika: address, address2, city, state, postal_code, country', '{\"address\":{\"old\":\"add2 (Updated)\",\"new\":\"add2\",\"appika_value\":\"add2\"},\"address2\":{\"old\":\"add1 (Simulated)\",\"new\":\"add1\",\"appika_value\":\"add1\"},\"city\":{\"old\":\"bangkok (Updated)\",\"new\":\"bangkok\",\"appika_value\":\"bangkok\"},\"state\":{\"old\":\"\\u0e01\\u0e23\\u0e38\\u0e07\\u0e40\\u0e17\\u0e1e\\u0e21\\u0e2b\\u0e32\\u0e19\\u0e04\\u0e23 (Sim)\",\"new\":\"\\u0e01\\u0e23\\u0e38\\u0e07\\u0e40\\u0e17\\u0e1e\\u0e21\\u0e2b\\u0e32\",\"appika_value\":\"\\u0e01\\u0e23\\u0e38\\u0e07\\u0e40\\u0e17\\u0e1e\\u0e21\\u0e2b\\u0e32\"},\"postal_code\":{\"old\":\"101109\",\"new\":\"10110\",\"appika_value\":\"10110\"},\"country\":{\"old\":\"TH_SIM\",\"new\":\"TH\",\"appika_value\":\"TH\"}}', '2025-06-24 10:08:15'),
(11, 179, 'location_sync', 1, 'Location updated from Appika: address, address2, city, state, postal_code', '{\"address\":{\"old\":\"add1 (Updated)\",\"new\":\"add1\",\"appika_value\":\"add1\"},\"address2\":{\"old\":\"add2 (Simulated)\",\"new\":\"add2\",\"appika_value\":\"add2\"},\"city\":{\"old\":\"Bangkok (Updated)\",\"new\":\"Bangkok\",\"appika_value\":\"Bangkok\"},\"state\":{\"old\":\"\\u0e01\\u0e23\\u0e38\\u0e07\\u0e40\\u0e17\\u0e1e\\u0e21\\u0e2b\\u0e32\\u0e19\\u0e04\\u0e23 (Sim)\",\"new\":\"\\u0e01\\u0e23\\u0e38\\u0e07\\u0e40\\u0e17\\u0e1e\\u0e21\\u0e2b\\u0e32\",\"appika_value\":\"\\u0e01\\u0e23\\u0e38\\u0e07\\u0e40\\u0e17\\u0e1e\\u0e21\\u0e2b\\u0e32\"},\"postal_code\":{\"old\":\"101109\",\"new\":\"10110\",\"appika_value\":\"10110\"}}', '2025-06-24 10:08:15'),
(12, 185, 'bulk_sync', 1, 'Customer updated from Appika: first_name', '{\"first_name\":{\"old\":\"developer\",\"new\":\"developer full name TTTTT\",\"appika_value\":\"developer full name TTTTT\"}}', '2025-06-24 10:37:23'),
(13, 180, 'bulk_sync', 1, 'Customer updated from Appika: first_name', '{\"first_name\":{\"old\":\"wat (Updated)\",\"new\":\"wat (Updated)  (Sim)\",\"appika_value\":\"wat (Updated)  (Sim)\"}}', '2025-06-24 10:37:26'),
(14, 188, 'location_sync', 1, 'Location updated from Appika: state', '{\"state\":{\"old\":\"\\u0e01\\u0e23\\u0e38\\u0e07\\u0e40\\u0e17\\u0e1e\\u0e21\\u0e2b\\u0e32\\u0e19\\u0e04\\u0e23\",\"new\":\"\\u0e01\\u0e23\\u0e38\\u0e07\\u0e40\\u0e17\\u0e1e\\u0e21\\u0e2b\\u0e32\",\"appika_value\":\"\\u0e01\\u0e23\\u0e38\\u0e07\\u0e40\\u0e17\\u0e1e\\u0e21\\u0e2b\\u0e32\"}}', '2025-06-25 09:50:28'),
(15, 186, 'location_sync', 1, 'Location updated from Appika: state', '{\"state\":{\"old\":\"\\u0e01\\u0e23\\u0e38\\u0e07\\u0e40\\u0e17\\u0e1e\\u0e21\\u0e2b\\u0e32\\u0e19\\u0e04\\u0e23\",\"new\":\"\\u0e01\\u0e23\\u0e38\\u0e07\\u0e40\\u0e17\\u0e1e\\u0e21\\u0e2b\\u0e32\",\"appika_value\":\"\\u0e01\\u0e23\\u0e38\\u0e07\\u0e40\\u0e17\\u0e1e\\u0e21\\u0e2b\\u0e32\"}}', '2025-06-26 03:58:39'),
(16, 193, 'bulk_sync', 1, 'Customer updated from Appika: first_name', '{\"first_name\":{\"old\":\"\",\"new\":\"bonus japanese\",\"appika_value\":\"bonus japanese\"}}', '2025-06-26 06:41:03'),
(17, 192, 'bulk_sync', 1, 'Customer updated from Appika: first_name', '{\"first_name\":{\"old\":\"\",\"new\":\"bonus lastname\",\"appika_value\":\"bonus lastname\"}}', '2025-06-26 06:41:04'),
(18, 190, 'bulk_sync', 1, 'Customer updated from Appika: first_name', '{\"first_name\":{\"old\":\"\",\"new\":\"helloitrework sohandsome\",\"appika_value\":\"helloitrework sohandsome\"}}', '2025-06-26 06:41:06'),
(19, 188, 'bulk_sync', 1, 'Customer updated from Appika: first_name', '{\"first_name\":{\"old\":\"\",\"new\":\"watcharapol vry\",\"appika_value\":\"watcharapol vry\"}}', '2025-06-26 06:41:06'),
(20, 188, 'location_sync', 1, 'Location updated from Appika: address, address2, city, state, postal_code, tell', '{\"address\":{\"old\":\"\",\"new\":\"123 Moo 5, Sukhumvit Road\",\"appika_value\":\"123 Moo 5, Sukhumvit Road\"},\"address2\":{\"old\":\"\",\"new\":\"Bangna Nuea Sub-district, Bangna District\",\"appika_value\":\"Bangna Nuea Sub-district, Bangna District\"},\"city\":{\"old\":\"\",\"new\":\"Bangkok\",\"appika_value\":\"Bangkok\"},\"state\":{\"old\":\"\",\"new\":\"Bangkok\",\"appika_value\":\"Bangkok\"},\"postal_code\":{\"old\":\"\",\"new\":\"10250\",\"appika_value\":\"10250\"},\"tell\":{\"old\":\"\",\"new\":\"0611111111\",\"appika_value\":\"0611111111\"}}', '2025-06-26 06:41:07'),
(21, 194, 'bulk_sync', 1, 'Customer updated from Appika: first_name', '{\"first_name\":{\"old\":\"\",\"new\":\"bonus USA\",\"appika_value\":\"bonus USA\"}}', '2025-06-26 07:00:05');

-- --------------------------------------------------------

--
-- Table structure for table `payment_methods`
--

CREATE TABLE `payment_methods` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `payment_method_id` varchar(255) NOT NULL,
  `card_last4` varchar(4) NOT NULL,
  `card_brand` varchar(50) NOT NULL,
  `card_exp_month` int(2) NOT NULL,
  `card_exp_year` int(4) NOT NULL,
  `is_default` tinyint(1) DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `payment_methods`
--

INSERT INTO `payment_methods` (`id`, `user_id`, `payment_method_id`, `card_last4`, `card_brand`, `card_exp_month`, `card_exp_year`, `is_default`, `created_at`) VALUES
(1, 25, 'pm_1RRA1iEJRyUMDOj53aZOwXLy', '3222', 'mastercard', 12, 2027, 0, '2025-05-21 10:37:40'),
(3, 25, 'pm_1RRANwEJRyUMDOj5616erqoS', '4242', 'visa', 12, 2028, 1, '2025-05-21 11:00:38'),
(5, 40, 'pm_1RThL5EJRyUMDOj5LumSatJg', '4242', 'visa', 12, 2026, 1, '2025-05-28 10:36:09'),
(11, 100, 'pm_1RUsFjEJRyUMDOj5mFpt8yZZ', '4444', 'mastercard', 12, 2034, 1, '2025-05-31 16:27:31'),
(12, 101, 'pm_1RUsMiEJRyUMDOj5KqnOJtHh', '4242', 'visa', 12, 2034, 1, '2025-05-31 16:34:41'),
(13, 102, 'pm_1RUsR4EJRyUMDOj5M5Wfwcea', '4242', 'visa', 12, 2034, 1, '2025-05-31 16:39:33'),
(16, 103, 'pm_1RUsdgEJRyUMDOj5hyJwxN9e', '4242', 'visa', 12, 2034, 1, '2025-05-31 16:52:36'),
(17, 105, 'pm_1RUsnVEJRyUMDOj55UsqHltZ', '4444', 'mastercard', 12, 2034, 1, '2025-05-31 17:02:44'),
(18, 106, 'pm_1RUtk3EJRyUMDOj5I0GAoNVu', '4242', 'visa', 12, 2034, 1, '2025-05-31 18:02:53'),
(21, 106, 'pm_1RUu4uEJRyUMDOj5koopsDph', '4444', 'mastercard', 12, 2031, 0, '2025-05-31 18:24:26'),
(22, 107, 'pm_1RUvsdEJRyUMDOj5aeojaRun', '4242', 'visa', 12, 2031, 1, '2025-05-31 20:19:53'),
(23, 108, 'pm_1RUvxlEJRyUMDOj5r8D3hAqx', '4444', 'mastercard', 12, 2031, 1, '2025-05-31 20:25:32'),
(24, 109, 'pm_1RUw69EJRyUMDOj5T9ushSQR', '4444', 'mastercard', 12, 2031, 1, '2025-05-31 20:34:13'),
(25, 110, 'pm_1RUwFmEJRyUMDOj5BZp1UKkg', '4444', 'mastercard', 12, 2031, 1, '2025-05-31 20:44:09'),
(26, 111, 'pm_1RUwPLEJRyUMDOj5h56BnV64', '4444', 'mastercard', 6, 2027, 1, '2025-05-31 20:54:03'),
(27, 111, 'pm_1RUxFfEJRyUMDOj5j6hh0BMt', '4242', 'visa', 12, 2026, 0, '2025-05-31 21:47:45'),
(28, 112, 'pm_1RV7OwEJRyUMDOj5lgh0O3Bl', '4242', 'visa', 12, 2034, 1, '2025-06-01 08:38:20'),
(29, 112, 'pm_1RV7RWEJRyUMDOj5QVenMuYt', '4444', 'mastercard', 12, 2031, 0, '2025-06-01 08:40:39'),
(30, 113, 'pm_1RVDUfEJRyUMDOj5ztfzpUdV', '4242', 'visa', 12, 2028, 1, '2025-06-01 15:08:40'),
(31, 114, 'pm_1RVDkMEJRyUMDOj5fBQxA22C', '4242', 'visa', 12, 2034, 1, '2025-06-01 15:24:53'),
(32, 115, 'pm_1RVOMEEJRyUMDOj5UWZBpAMS', '4242', 'visa', 12, 2034, 1, '2025-06-02 02:44:23'),
(33, 117, 'pm_1RVOmvEJRyUMDOj55CzJCEbD', '4444', 'mastercard', 12, 2028, 1, '2025-06-02 03:11:58'),
(34, 118, 'pm_1RVOvvEJRyUMDOj5b7CLm3Hz', '4242', 'visa', 12, 2028, 1, '2025-06-02 03:21:15'),
(35, 119, 'pm_1RVP0yEJRyUMDOj5OAcWhV8B', '4242', 'visa', 12, 2028, 1, '2025-06-02 03:26:29'),
(36, 120, 'pm_1RVP2FEJRyUMDOj5WwYjZ53t', '4242', 'visa', 12, 2028, 1, '2025-06-02 03:27:47'),
(37, 121, 'pm_1RVP4QEJRyUMDOj5893E2IlZ', '4242', 'visa', 12, 2028, 1, '2025-06-02 03:30:02'),
(38, 122, 'pm_1RVP7IEJRyUMDOj5hyvp1IQ0', '4242', 'visa', 12, 2034, 1, '2025-06-02 03:33:00'),
(39, 123, 'pm_1RVPCIEJRyUMDOj58RKxHc01', '4242', 'visa', 12, 2034, 1, '2025-06-02 03:38:10'),
(40, 129, 'pm_1RVPgnEJRyUMDOj5lN1hJLM2', '4242', 'visa', 12, 2034, 0, '2025-06-02 04:12:10'),
(41, 129, 'pm_1RVPkrEJRyUMDOj5RJxzzPry', '4444', 'mastercard', 12, 2035, 1, '2025-06-02 04:13:51'),
(42, 143, 'pm_1RWTQFEJRyUMDOj5M8Yzs3rB', '4242', 'visa', 12, 2034, 1, '2025-06-05 03:01:08'),
(45, 144, 'pm_1RWZrpEJRyUMDOj5t2D71J7K', '4444', 'mastercard', 12, 2034, 1, '2025-06-05 09:13:51'),
(46, 150, 'pm_1RWaryEJRyUMDOj5uac5GnJA', '4444', 'mastercard', 12, 2034, 1, '2025-06-05 10:18:04'),
(47, 150, 'pm_1RWb3gEJRyUMDOj5Rs5vvshN', '4242', 'visa', 12, 2035, 0, '2025-06-05 11:22:07'),
(49, 158, 'pm_1RXwcxEJRyUMDOj56omU5iPF', '4444', 'mastercard', 12, 2034, 1, '2025-06-09 03:44:09');

-- --------------------------------------------------------

--
-- Table structure for table `payment_temp`
--

CREATE TABLE `payment_temp` (
  `id` int(11) NOT NULL,
  `session_id` varchar(255) NOT NULL,
  `username` varchar(255) NOT NULL,
  `email` varchar(255) NOT NULL,
  `password` varchar(255) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `stripe_customer_id` varchar(255) NOT NULL,
  `user_created` tinyint(1) DEFAULT 0,
  `processed_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `payment_temp`
--

INSERT INTO `payment_temp` (`id`, `session_id`, `username`, `email`, `password`, `created_at`, `stripe_customer_id`, `user_created`, `processed_at`) VALUES
(102, 'cs_test_a1qQK0UvFMt2jhpbPfaiLkGCnh9HfCpoN9zx5d5xpP8uhnYVX2IS7PyVZZ', 'user17688', '<EMAIL>', '37b53a30', '2025-06-02 02:45:50', '', 1, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `purchasetickets`
--

CREATE TABLE `purchasetickets` (
  `purchaseid` int(11) NOT NULL,
  `username` varchar(100) NOT NULL,
  `ticket_type` varchar(20) NOT NULL,
  `package_size` varchar(10) NOT NULL,
  `numbers_per_package` int(11) NOT NULL,
  `dollar_price_per_package` int(11) NOT NULL,
  `purchase_time` datetime NOT NULL,
  `expiration_date` datetime DEFAULT NULL,
  `expired_at` timestamp NULL DEFAULT NULL,
  `transactionid` varchar(100) NOT NULL,
  `remaining_tickets` int(11) NOT NULL DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

--
-- Dumping data for table `purchasetickets`
--

INSERT INTO `purchasetickets` (`purchaseid`, `username`, `ticket_type`, `package_size`, `numbers_per_package`, `dollar_price_per_package`, `purchase_time`, `expiration_date`, `expired_at`, `transactionid`, `remaining_tickets`) VALUES
(0, 'test5', 'BASIC', 'XS', 1, 20, '2025-01-07 10:42:43', '2025-01-07 10:44:43', NULL, 'cs_test_a1pAVaBf92GccNgkwkbv8hb4C78CNHm9LICx4YHAcbOkvc6xb9RIB63ER8', 0),
(1, 'test', 'STARTER', 'XS', 1, 10, '2024-07-15 16:07:48', '2024-07-15 16:09:48', '2025-06-24 02:45:43', '', 0),
(40, 'root', 'STARTER', 'M', 30, 225, '2024-07-19 11:13:50', '2024-07-19 11:15:50', '2025-06-24 02:45:43', 'not set', 0),
(45, 'test2', 'STARTER', 'L', 50, 400, '2024-07-19 11:46:19', '2024-07-19 11:48:19', '2025-06-24 02:45:43', 'cs_test_a1kllMwHzxx6eR1o081FnZxJL6NfaRWLZvcBzDgPfFLnRNlP1K13GwVolx', 0),
(49, 'test2', 'STARTER', 'S', 10, 90, '2024-07-19 11:49:07', '2024-07-19 11:51:07', '2025-06-24 02:45:43', 'cs_test_a1KVBDJlsGWQ6uwKpfuf5wrtT7QS3shpaTMOKjarfz6mAf47iJVUHdcsUr', 0),
(52, 'test2', 'STARTER', 'M', 30, 225, '2024-07-19 11:52:26', '2024-07-19 11:54:26', '2025-06-24 02:45:43', 'cs_test_a1vkbG7baIdiQyr2EY08KL3aDSNsMhmsZx0DiCrdcMgUeN99n3ZjeY14fs', 0),
(53, 'test2', 'STARTER', 'M', 30, 225, '2024-07-19 12:04:48', '2024-07-19 12:06:48', '2025-06-24 02:45:43', 'cs_test_a1O7kwjdVL5H7FqQpDytj3n1fAX6UM7nMXFwXxlWFkaZXLj80i3VnitxSx', 0),
(54, 'test2', 'STARTER', 'L', 50, 400, '2024-07-19 12:15:04', '2024-07-19 12:17:04', '2025-06-24 02:45:43', 'cs_test_a1P5dEOvoon7gnltDxq4xI49aQxkt7P84N5N7yL40Po8rubffa0yPmBBED', 0),
(55, 'test2', 'STARTER', 'S', 10, 90, '2024-07-19 15:54:41', '2024-07-19 15:56:41', '2025-06-24 02:45:43', 'cs_test_a1YYY5x2Yq6I2SHN0rohu3MILrUUt6cDdwnnBDxEKOW69SJzrw91WwWl5e', 0),
(57, 'test', 'STARTER', 'S', 10, 90, '2024-07-19 16:23:09', '2024-07-19 16:25:09', '2025-06-24 02:45:43', 'cs_test_a10qiW5vgClkfux98q46ALQjlJS59n6zmDQbYPNrn9XPgKHJqeBJ1WnU53', 0),
(77, 'test', 'STARTER', 'S', 10, 90, '2024-07-19 16:47:22', '2024-07-19 16:49:22', '2025-06-24 02:45:43', 'cs_test_a1NyC3T2lxmpZIAkwh6UlkqGpS3UddqIHlGI2XGJgmkbCutvEx0KUTU2cp', 0),
(82, 'test', 'STARTER', 'S', 10, 90, '2024-07-23 11:15:38', '2024-07-23 11:17:38', '2025-06-24 02:45:43', 'cs_test_a12k2KnCMTnqZNK3kVtLjDAzCAOWrLmT8DxWbVjHhVI3wjiELgsD69O9pd', 0),
(103, 'test', 'STARTER', 'XS', 1, 10, '2024-07-23 11:45:56', '2024-07-23 11:47:56', '2025-06-24 02:45:43', 'cs_test_a1wThR010E9hFWuYXvpjEcnVc7gPCSPVqvRC0Vjg6PEfCPDQePLWjseHKC', 0),
(106, 'test', 'STARTER', 'XS', 1, 10, '2024-07-23 14:22:12', '2024-07-23 14:24:12', '2025-06-24 02:45:43', 'cs_test_a17oYtppjlYd5ljKPGCIbifFd9RPY45V28Pdwy12KSxb1fsT9cfsU1JDAr', 0),
(109, 'test', 'STARTER', 'XS', 1, 10, '2024-07-23 14:34:53', '2024-07-23 14:36:53', '2025-06-24 02:45:43', 'cs_test_a1RQXE8c22NNwQdNz2drwIGaoS8ujAlgruJa5IsKOope7grxMytHbMDT7F', 0),
(119, 'test', 'STARTER', 'XS', 1, 10, '2024-07-23 14:55:20', '2024-07-23 14:57:20', '2025-06-24 02:45:43', 'cs_test_a17kHnsJICcDPzeR6kqGYthgIN42iyzcxC6cT5P7wJpaAIwKHoaXfaHQMa', 0),
(121, 'test', 'STARTER', 'XS', 1, 10, '2024-07-31 16:24:24', '2024-07-31 16:26:24', '2025-06-24 02:45:43', 'cs_test_a1AMTYYnoaiEhIRxi1TjWrGhTyVItiYJmEm5wknle428xgkGBPX24fgDds', 0),
(123, 'test', 'STARTER', 'XS', 1, 20, '2024-09-11 15:05:19', '2024-09-11 15:07:19', '2025-06-24 02:45:43', 'cs_test_a1SiCTlfaUDaoNFQHj7GKUZJ9Lu9rTBHsaGfNAOuCFbXLiFqTYKmuDejEV', 0),
(127, 'test', 'STARTER', 'XS', 1, 20, '2024-09-11 15:17:20', '2024-09-11 15:19:20', '2025-06-24 02:45:43', 'cs_test_a1R9Vkw4e5rGAISdq6MAIzRDQtO5LGBPWjgGSYwcudPCoczlnBXs5m4yPB', 0),
(133, 'test', 'PREMIUM', 'XS', 1, 20, '2024-09-11 16:08:23', '2024-09-11 16:10:23', '2025-06-24 02:45:43', 'cs_test_a1vPE5sGy6KbRPaEkzXbXW3XBfpJg6aSQOCU12YPuKre9iyFWJs8A96vax', 0),
(536, 'user64324', 'STARTER', 'XS', 5, 90, '2025-06-02 11:11:50', '2025-06-02 11:13:50', '2025-06-24 02:45:43', 'cs_test_a14o9wEKddaq7hZFyB7t4H5SWqx42CpaJX14okHcJ9JOOYcSXeb0sbSjbk', 0),
(537, 'user72354', 'STARTER', 'XS', 5, 90, '2025-06-04 02:40:40', '2025-06-04 02:42:40', '2025-06-24 02:45:43', 'cs_test_b12auZUv27DhkUJHbBpa9lLilej6r4xLl9Oo2YIhc4W3OyBLJ1egZ9JDdv', 0),
(538, 'user72354', 'BUSINESS', 'XS', 5, 115, '2025-06-04 02:40:40', '2025-06-04 02:42:40', '2025-06-24 02:45:43', 'cs_test_b12auZUv27DhkUJHbBpa9lLilej6r4xLl9Oo2YIhc4W3OyBLJ1egZ9JDdv', 0),
(539, 'user34839', 'STARTER', 'XS', 5, 90, '2025-06-04 02:46:47', '2025-06-04 02:48:47', '2025-06-24 02:45:43', 'cs_test_b1kvkCFP0UQAuvEq93rmAemOuZQZvNqc180SW4QA4efHFljXNoKWkkjmi5', 0),
(540, 'user34839', 'BUSINESS', 'XS', 5, 115, '2025-06-04 02:46:47', '2025-06-04 02:48:47', '2025-06-24 02:45:43', 'cs_test_b1kvkCFP0UQAuvEq93rmAemOuZQZvNqc180SW4QA4efHFljXNoKWkkjmi5', 0),
(541, 'user34839', 'BUSINESS', 'S', 10, 200, '2025-06-04 02:46:47', '2025-06-04 02:48:47', '2025-06-24 02:45:43', 'cs_test_b1kvkCFP0UQAuvEq93rmAemOuZQZvNqc180SW4QA4efHFljXNoKWkkjmi5', 0),
(542, 'user43076', 'STARTER', 'XS', 5, 90, '2025-06-04 02:56:58', '2025-06-04 02:58:58', '2025-06-24 02:45:43', 'cs_test_a1XHHDjHnjrmDdgzvEShBdMfDqv7HaIujgPG61GXOzwoFyM9rGBXW6ub19', 0),
(543, 'user61778', 'STARTER', 'XS', 5, 90, '2025-06-04 03:09:16', '2025-06-04 03:11:16', '2025-06-24 02:45:43', 'cs_test_b1Ko1kSU6yIwxHHuyg9qNC6SRyl203dAfTfcuduStZFDHO7IoeLGdPuM2w', 0),
(544, 'user61778', 'ULTIMATE', 'XS', 5, 165, '2025-06-04 03:09:16', '2025-06-04 03:11:16', '2025-06-24 02:45:43', 'cs_test_b1Ko1kSU6yIwxHHuyg9qNC6SRyl203dAfTfcuduStZFDHO7IoeLGdPuM2w', 0),
(545, 'user61778', 'BUSINESS', 'XS', 5, 115, '2025-06-04 03:09:16', '2025-06-04 03:11:16', '2025-06-24 02:45:43', 'cs_test_b1Ko1kSU6yIwxHHuyg9qNC6SRyl203dAfTfcuduStZFDHO7IoeLGdPuM2w', 0),
(546, 'user70412', 'STARTER', 'XS', 5, 90, '2025-06-04 04:56:28', '2025-06-04 04:58:28', '2025-06-24 02:45:43', 'cs_test_a1KDs2WmDHARvGE6ZR0NQ7qolQlgSqg5ABIQHcGX4iYejHUOU7WfCz0MlV', 0),
(547, 'test', 'STARTER', 'XS', 5, 90, '2025-06-04 08:49:59', '2025-06-04 08:51:59', '2025-06-24 02:45:43', 'cs_test_a1HyR97BugptYWeyJLIRPMbfySMyq7354dOeqN9xJXJ0rp8VNi9evCGhrb', 0),
(548, 'test', 'BUSINESS', 'XS', 5, 115, '2025-06-04 08:52:36', '2025-06-04 08:54:36', '2025-06-24 02:45:43', 'cs_test_a1i2BDs1thqaaJDdvZkDa9QCExx6zdP0Xzm7Ik2af6AES3kYCkjrBEijV1', 0),
(549, 'user40554', 'STARTER', 'XS', 5, 90, '2025-06-04 08:57:32', '2025-06-04 08:59:32', '2025-06-24 02:45:43', 'cs_test_b1844etB9AAODvxiBc5coOmg2rtJB2JEVOjIdHDlkAOZDYn9CFyl3xnLcL', 0),
(550, 'user40554', 'STARTER', 'S', 10, 150, '2025-06-04 08:57:32', '2025-06-04 08:59:32', '2025-06-24 02:45:43', 'cs_test_b1844etB9AAODvxiBc5coOmg2rtJB2JEVOjIdHDlkAOZDYn9CFyl3xnLcL', 0),
(551, 'user40554', 'BUSINESS', 'S', 10, 200, '2025-06-04 08:57:32', '2025-06-04 08:59:32', '2025-06-24 02:45:43', 'cs_test_b1844etB9AAODvxiBc5coOmg2rtJB2JEVOjIdHDlkAOZDYn9CFyl3xnLcL', 0),
(552, 'test', 'STARTER', 'XS', 5, 90, '2025-06-04 09:48:56', '2025-06-04 09:50:56', '2025-06-24 02:45:43', 'cs_test_a1vCL6vwuWo9GVbuTtTThbHalE6s0MQrgQAA4rB1excSOsLKiqjNzZKXho', 0),
(553, 'user10455', 'STARTER', 'XS', 5, 90, '2025-06-04 10:00:32', '2025-06-04 10:02:32', '2025-06-24 02:45:43', 'cs_test_a1ya2nsgJJVkthaUgx2ZYCeqB1wNWkjDG3abBYaZh2P64jlWQhOzWsTGba', 0),
(554, 'user95311', 'STARTER', 'XS', 5, 90, '2025-06-05 02:20:55', '2025-06-05 02:22:55', '2025-06-24 02:45:43', 'cs_test_b13A3WhhIISq9WSoJzdMMSg6cXjvZAE2yhqo6R3Fv7JqOvJLbgyml6gQej', 0),
(555, 'user95311', 'STARTER', 'S', 10, 150, '2025-06-05 02:20:55', '2025-06-05 02:22:55', '2025-06-24 02:45:43', 'cs_test_b13A3WhhIISq9WSoJzdMMSg6cXjvZAE2yhqo6R3Fv7JqOvJLbgyml6gQej', 0),
(556, 'user95311', 'BUSINESS', 'XS', 5, 115, '2025-06-05 02:20:55', '2025-06-05 02:22:55', '2025-06-24 02:45:43', 'cs_test_b13A3WhhIISq9WSoJzdMMSg6cXjvZAE2yhqo6R3Fv7JqOvJLbgyml6gQej', 0),
(557, 'user95311', 'STARTER', 'XS', 5, 90, '2025-06-05 03:01:08', '2025-06-05 03:03:08', '2025-06-24 02:45:43', 'pi_3RWU3BEJRyUMDOj50tuSrVcJ', 0),
(558, 'test', 'BUSINESS', 'XS', 5, 115, '2025-06-05 07:05:49', '2025-06-05 07:07:49', '2025-06-24 02:45:43', 'cs_test_a1WxZFvxzxSTizSneKCBHSB14w26bwcR8w4JxrHAnCl3l3ckqLZ7FWPLD9', 0),
(559, 'test', 'BUSINESS', 'XS', 5, 115, '2025-06-05 07:08:05', '2025-06-05 07:10:05', '2025-06-24 02:45:43', 'pi_3RWXtsEJRyUMDOj51SAKlui0', 0),
(560, 'test', 'STARTER', 'XS', 5, 90, '2025-06-05 07:10:10', '2025-06-05 07:12:10', '2025-06-24 02:45:43', 'cs_test_a1JFQAOXK7ZMWXnhv5tC95JWaJjvnaV4RhdqUUWxvXlssq6IrLyxwUFfxx', 0),
(561, 'test', 'BUSINESS', 'S', 10, 200, '2025-06-05 08:27:09', '2025-06-05 08:29:09', '2025-06-24 02:45:43', 'cs_test_a1k8o2dn26ko8VBp6FH8Cbr8PHYOaxBzY9E20jKrS50ne0WGvfjRHbolGl', 0),
(562, 'test', 'BUSINESS', 'S', 10, 200, '2025-06-05 08:36:32', '2025-06-05 08:38:32', '2025-06-24 02:45:43', 'cs_test_a1uZmhyrX2zujW9hP0veUckUZSHDTf96AdGm9RVqzZgS5ex3cqOtYWYumc', 0),
(563, 'test', 'STARTER', 'XS', 5, 90, '2025-06-05 08:51:32', '2025-06-05 08:53:32', '2025-06-24 02:45:43', 'cs_test_a1mmX5NlTAVqye5u9iSBhIQcPZb1evaFBForB7Mc2v3w4WupBy0IQuNQO6', 0),
(564, 'user92053', 'ULTIMATE', 'S', 10, 300, '2025-06-05 09:11:54', '2025-06-05 09:13:54', '2025-06-24 02:45:43', 'cs_test_b1CJ722RY6oS8WRuQegFsUBagjFz8syhYpMjMjBQI9aB1YL5OqV75L9qbu', 0),
(565, 'user92053', 'BUSINESS', 'S', 10, 200, '2025-06-05 09:11:54', '2025-06-05 09:13:54', '2025-06-24 02:45:43', 'cs_test_b1CJ722RY6oS8WRuQegFsUBagjFz8syhYpMjMjBQI9aB1YL5OqV75L9qbu', 0),
(566, 'user92053', 'STARTER', 'S', 10, 150, '2025-06-05 09:11:54', '2025-06-05 09:13:54', '2025-06-24 02:45:43', 'cs_test_b1CJ722RY6oS8WRuQegFsUBagjFz8syhYpMjMjBQI9aB1YL5OqV75L9qbu', 0),
(567, 'user92053', 'ULTIMATE', 'XS', 5, 165, '2025-06-05 09:13:51', '2025-06-05 09:15:51', '2025-06-24 02:45:43', 'cs_test_a106SdtGwT3y2OoyXpxkSeQtuRma1BauZBYc4PzyoZKB5LufRhvsPIsw9e', 0),
(568, 'user92053', 'BUSINESS', 'XS', 5, 115, '2025-06-05 09:15:57', '2025-06-05 09:17:57', '2025-06-24 02:45:43', 'cs_test_a1tmTdvUczPgDLGAtanZyUWTD9kMuEkgIhyvmNTSZLYFafil9tPSyvSsNw', 0),
(569, 'user90502', 'BUSINESS', 'XS', 5, 115, '2025-06-05 09:19:06', '2025-06-05 09:21:06', '2025-06-24 02:45:43', 'cs_test_b1GnAQ2JQ0UOsgBqOuuCjyyd7URDluA4yhsRcB3sSuBK4iomL72G3IWQMx', 0),
(570, 'user90502', 'STARTER', 'XS', 5, 90, '2025-06-05 09:19:06', '2025-06-05 09:21:06', '2025-06-24 02:45:43', 'cs_test_b1GnAQ2JQ0UOsgBqOuuCjyyd7URDluA4yhsRcB3sSuBK4iomL72G3IWQMx', 0),
(571, 'india', 'STARTER', 'XS', 5, 90, '2025-06-05 09:28:54', '2025-06-05 09:30:54', '2025-06-24 02:45:43', 'cs_test_a11dSpbwZhhjipAtGGwQH3XhGdk9l7gkOg9tNN26mbVJ0WSKF6tOFzRL5u', 0),
(572, 'SG2e', 'BUSINESS', 'XS', 5, 115, '2025-06-05 09:46:54', '2025-06-05 09:48:54', '2025-06-24 02:45:43', 'cs_test_b1ngfogofwCxe20HLynYxbv9hPyO1qRWwTGt8wx0SIS4lWQIDGH5YSYKrv', 0),
(573, 'SG2e', 'BUSINESS', 'S', 10, 200, '2025-06-05 09:46:54', '2025-06-05 09:48:54', '2025-06-24 02:45:43', 'cs_test_b1ngfogofwCxe20HLynYxbv9hPyO1qRWwTGt8wx0SIS4lWQIDGH5YSYKrv', 0),
(574, 'Testlaos1', 'STARTER', 'XS', 5, 90, '2025-06-05 09:54:39', '2025-06-05 09:56:39', '2025-06-24 02:45:43', 'cs_test_b1kd7uSFWOabK1GpHZ0tBEf7EpRHmTMhpZNGegTxlAOnpWYR3IMgf179hL', 0),
(575, 'Testlaos1', 'BUSINESS', 'XS', 5, 115, '2025-06-05 09:54:39', '2025-06-05 09:56:39', '2025-06-24 02:45:43', 'cs_test_b1kd7uSFWOabK1GpHZ0tBEf7EpRHmTMhpZNGegTxlAOnpWYR3IMgf179hL', 0),
(576, 'testVietnam', 'ULTIMATE', 'XS', 5, 165, '2025-06-05 10:07:26', '2025-06-05 10:09:26', '2025-06-24 02:45:43', 'cs_test_b1PvlT87m8UszXbJTQbZiYoQ0GXgq2fME7Rfy5iGGT7v7pUM2uyEMyJ4PW', 0),
(577, 'testVietnam', 'BUSINESS', 'XS', 5, 115, '2025-06-05 10:07:26', '2025-06-05 10:09:26', '2025-06-24 02:45:43', 'cs_test_b1PvlT87m8UszXbJTQbZiYoQ0GXgq2fME7Rfy5iGGT7v7pUM2uyEMyJ4PW', 0),
(578, 'TestMyanMar', 'STARTER', 'XS', 5, 90, '2025-06-05 10:13:43', '2025-06-05 10:15:43', '2025-06-24 02:45:43', 'cs_test_a1ksW0FOQrFfVrC9Nn67faig06MqiWPqQY28aIwYEkhtDVmkvH9RFb4tvH', 0),
(579, 'TestMyanMar', 'BUSINESS', 'XS', 5, 115, '2025-06-05 10:18:04', '2025-06-05 10:20:04', '2025-06-24 02:45:43', 'cs_test_a12ZOIR7iFO1pJCW9M2ksc5OIZSq9YaOu6QBz8NKucXy1u95GzgsZu4YiF', 0),
(580, 'TestMyanMar', 'ULTIMATE', 'XS', 5, 165, '2025-06-05 10:19:26', '2025-06-05 10:21:26', '2025-06-24 02:45:43', 'cs_test_a1lpcXjmXvWJkBTDIe2XU5FpdfgyJ3kp7kgGAngTGKpMo6vmr9f1Wlnd5R', 0),
(581, 'TestMyanMar', 'STARTER', 'XS', 5, 90, '2025-06-05 10:23:30', '2025-06-05 10:25:30', '2025-06-24 02:45:43', 'cs_test_a1RuLVs1OdZcIsgXGbKc9PlF6NRpOy0KBiSlX2I7HaH89BaLGkLgEfv8YT', 0),
(582, 'TestMyanMar', 'ULTIMATE', 'XS', 5, 165, '2025-06-05 10:30:08', '2025-06-05 10:32:08', '2025-06-24 02:45:43', 'cs_test_a1vDD6Hy6UX8Vf4rgqhvsubNKk1k3tJ25I4sXXfFwhpHiRFhidGFynUyAm', 0),
(583, 'TestMyanMar', 'STARTER', 'XS', 5, 90, '2025-06-05 10:37:57', '2025-06-05 10:39:57', '2025-06-24 02:45:43', 'cs_test_a1MKxRg6WAPJtAkk3lJFeWEJ8VuQIri9KtaZLVV8NeYgaaRWYb3FBIDBjK', 0),
(584, 'TestMyanMar', 'BUSINESS', 'S', 10, 200, '2025-06-05 11:21:07', '2025-06-05 11:23:07', '2025-06-24 02:45:43', 'cs_test_a1wbyZNsW21xueLh1XtgWzRHG0NI7HiLa1kdjTu7HAcRSPziKlMIZyfkov', 0),
(585, 'TestMyanMar', 'ULTIMATE', 'XS', 5, 165, '2025-06-05 11:22:07', '2025-06-05 11:24:07', '2025-06-24 02:45:43', 'pi_3RWbs0EJRyUMDOj50OlABeaz', 0),
(586, 'maffy', 'STARTER', 'XS', 5, 90, '2025-06-06 05:21:55', '2025-06-06 05:23:55', '2025-06-24 02:45:43', 'cs_test_b1KnlaCvBN3095Ig1nxwXSVoqfI6NonUXKJeTGJSTzScqI49BCX0YFsiWN', 0),
(587, 'maffy', 'BUSINESS', 'XS', 5, 115, '2025-06-06 05:21:55', '2025-06-06 05:23:55', '2025-06-24 02:45:43', 'cs_test_b1KnlaCvBN3095Ig1nxwXSVoqfI6NonUXKJeTGJSTzScqI49BCX0YFsiWN', 0),
(588, 'maffy', 'ULTIMATE', 'XS', 5, 165, '2025-06-06 05:21:55', '2025-06-06 05:23:55', '2025-06-24 02:45:43', 'cs_test_b1KnlaCvBN3095Ig1nxwXSVoqfI6NonUXKJeTGJSTzScqI49BCX0YFsiWN', 0),
(589, 'maffy', 'STARTER', 'XS', 5, 90, '2025-06-06 05:54:20', '2025-06-06 05:56:20', '2025-06-24 02:45:43', 'cs_test_a1YFsQgMR3B2VqPUrWyd0HVcLsw5vLSmz5TBxZ0Q9bojpZikdnkexw9FdB', 0),
(590, 'test', 'STARTER', 'XS', 5, 90, '2025-06-06 07:00:48', '2025-06-06 07:02:48', '2025-06-24 02:45:43', 'pi_3RWuGdEJRyUMDOj51yIQKYuu', 0),
(591, 'laos', 'STARTER', 'XS', 5, 90, '2025-06-06 07:04:18', '2025-06-06 07:06:18', '2025-06-24 02:45:43', 'cs_test_a1UGMtDeFkRct3p7JpLLb9FKucPpAnB8sEsW3H7aJZ8HGBkV6djcOeuykf', 0),
(592, 'kafkaTest', 'STARTER', 'M', 25, 350, '2025-06-06 10:04:20', '2025-06-06 10:06:20', '2025-06-24 02:45:43', 'cs_test_a15MoWemNbC5thjHVsSYWfBcforv0tAuZPd7a79yz19ADU2pfyDmB1JoyP', 0),
(593, 'user57006', 'STARTER', 'XS', 5, 90, '2025-06-06 10:41:01', '2025-06-06 10:43:01', '2025-06-24 02:45:43', 'cs_test_a1IgxtNlwtQd16MfnyqQMYdfYIqq9gnGPD2HbmnUTgPDmqzRdbmOBql9aS', 0),
(594, 'kafka3', 'STARTER', 'XS', 5, 90, '2025-06-06 10:45:53', '2025-06-06 10:47:53', '2025-06-24 02:45:43', 'cs_test_a1JLHRFB9ty9jZ1lF3OcCcvpd3EnAvqf7iNbCjrRLfYFux3YgsETjDD764', 0),
(595, 'testmalaysia', 'STARTER', 'M', 25, 350, '2025-06-09 02:26:19', '2025-06-09 02:28:19', '2025-06-24 02:45:43', 'cs_test_b1c715X9tAvnlW0koOgTU50DsckWhRO22diMYHALbenrQMDOoU7qa1YTVk', 0),
(596, 'testmalaysia', 'STARTER', 'XS', 5, 90, '2025-06-09 02:26:19', '2025-06-09 02:28:19', '2025-06-24 02:45:43', 'cs_test_b1c715X9tAvnlW0koOgTU50DsckWhRO22diMYHALbenrQMDOoU7qa1YTVk', 0),
(597, 'testmalaysia2', 'STARTER', 'M', 25, 350, '2025-06-09 02:30:03', '2025-06-09 02:32:03', '2025-06-24 02:45:43', 'cs_test_b1GFwGJ7xPDWkrBlOcsVfdio8lATWFd19vXlFL8oLTLoh1uBIYtio164pP', 0),
(598, 'testmalaysia2', 'STARTER', 'S', 10, 150, '2025-06-09 02:30:03', '2025-06-09 02:32:03', '2025-06-24 02:45:43', 'cs_test_b1GFwGJ7xPDWkrBlOcsVfdio8lATWFd19vXlFL8oLTLoh1uBIYtio164pP', 0),
(599, 'dev01', 'STARTER', 'M', 25, 350, '2025-06-09 03:29:34', '2025-06-09 03:31:34', '2025-06-24 02:45:43', 'cs_test_b1SkkkTgGVmPWJBeUDBuakRJ7GQbsesoHimr2vDIgqjnZ7H2tgm9Tkg4MQ', 0),
(600, 'dev01', 'STARTER', 'S', 10, 150, '2025-06-09 03:29:34', '2025-06-09 03:31:34', '2025-06-24 02:45:43', 'cs_test_b1SkkkTgGVmPWJBeUDBuakRJ7GQbsesoHimr2vDIgqjnZ7H2tgm9Tkg4MQ', 0),
(601, 'dev01', 'BUSINESS', 'XS', 5, 115, '2025-06-09 03:44:09', '2025-06-09 03:46:09', '2025-06-24 02:45:43', 'cs_test_a161EJJiGcttPSlh4BLB6dV7OCrtYcMf32YC6ivSbyiMQFT70qzsh5ZcYb', 0),
(602, 'dev01', 'ULTIMATE', 'XS', 5, 165, '2025-06-09 03:47:15', '2025-06-09 03:49:15', '2025-06-24 02:45:43', 'cs_test_a1HjM8pTjFyMM5DBX7xy5zsLcLTgKVAzmO1USyzRYdwkQFislMVJokdvNK', 0),
(603, 'dev01', 'ULTIMATE', 'XS', 5, 165, '2025-06-09 03:52:58', '2025-06-09 03:54:58', '2025-06-24 02:45:43', 'cs_test_a1c50fHcJJjcLFAG5qhmBMVSX20uYpFnXVhHqdb0xFVL5xzoBuu6Yq4bKB', 0),
(604, 'testnonlogin', 'STARTER', 'XS', 5, 90, '2025-06-09 03:54:32', '2025-06-09 03:56:32', '2025-06-24 02:45:43', 'cs_test_a1JZAZTBc9kh2EgP1gX9mSydxt55CU3DjNoJHIaFq4Gg66OMueXmsGnIH2', 0),
(605, 'testcheckout2', 'STARTER', 'XS', 5, 90, '2025-06-09 03:57:21', '2025-06-09 03:59:21', '2025-06-24 02:45:43', 'cs_test_a14hshrxiVQYOOtk4R1tfMMEyIJpuVIN2cnuFx41ovDzDjreqJIQAgIdeX', 0),
(606, 'testcheckout3', 'STARTER', 'XS', 5, 90, '2025-06-09 03:59:40', '2025-06-09 04:01:40', '2025-06-24 02:45:43', 'cs_test_a1h3KS2waaxj0zufcfG9kYt0uRf43g1SnSBP3qYg7YVs86s9QbMB4zPSJ6', 0),
(607, 'dev01', 'STARTER', 'XS', 5, 90, '2025-06-09 04:03:20', '2025-06-09 04:05:20', '2025-06-24 02:45:43', 'cs_test_a1wznvzDhBqqPJe7tah3w2C3JAmN3A3OJ9zuSzG4tits49RUpoqrzSEaWf', 0),
(608, 'dev01', 'STARTER', 'XS', 5, 90, '2025-06-09 04:28:21', '2025-06-09 04:30:21', '2025-06-24 02:45:43', 'cs_test_b12wiDhKHFDFHpYonJzgb7oDAZNskTVbIRYwEnObZhoWO9ObZtIiZ3DBSO', 0),
(609, 'dev01', 'STARTER', 'S', 10, 150, '2025-06-09 04:28:21', '2025-06-09 04:30:21', '2025-06-24 02:45:43', 'cs_test_b12wiDhKHFDFHpYonJzgb7oDAZNskTVbIRYwEnObZhoWO9ObZtIiZ3DBSO', 0),
(610, 'dev02', 'STARTER', 'XS', 5, 90, '2025-06-09 04:38:27', '2025-06-09 04:40:27', '2025-06-24 02:45:43', 'cs_test_b1nWQNqohkvPYkLXt91ZJ4nMKGWmCeBadZ79wnJ3kdDLLSIRAxITBg9L0v', 0),
(611, 'dev02', 'BUSINESS', 'XS', 5, 115, '2025-06-09 04:38:27', '2025-06-09 04:40:27', '2025-06-24 02:45:43', 'cs_test_b1nWQNqohkvPYkLXt91ZJ4nMKGWmCeBadZ79wnJ3kdDLLSIRAxITBg9L0v', 0),
(612, 'MrMalay', 'STARTER', 'XS', 5, 90, '2025-06-09 04:57:12', '2025-06-09 04:59:12', '2025-06-24 02:45:43', 'cs_test_a12Fjyh8AJelYs4yIxxYSqbNlHwFbrHxH1OQ4uWimIpkKCloWbo56K9C7U', 0),
(613, 'user49913', 'STARTER', 'XS', 5, 90, '2025-06-09 05:06:41', '2025-06-09 05:08:41', '2025-06-24 02:45:43', 'cs_test_a1TjOij2aHnbwBX4mqGzdLhiQXtN7KyHkrra72aYH1Q7ERFiAJ2hWiZjTI', 0),
(614, 'test21', 'STARTER', 'XS', 5, 90, '2025-06-09 05:53:54', '2025-06-09 05:55:54', '2025-06-24 02:45:43', 'cs_test_b1WAywqDe5tYNat2k6j7bQRrPaTnCuC0FWwhm0Ek67NZjm6GuriztVTZLf', 0),
(615, 'test21', 'BUSINESS', 'XS', 5, 115, '2025-06-09 05:53:54', '2025-06-09 05:55:54', '2025-06-24 02:45:43', 'cs_test_b1WAywqDe5tYNat2k6j7bQRrPaTnCuC0FWwhm0Ek67NZjm6GuriztVTZLf', 0),
(616, 'thisIsAmerica', 'STARTER', 'XS', 5, 90, '2025-06-10 03:24:26', '2025-06-10 03:26:26', '2025-06-24 02:45:43', 'cs_test_a1qQENOkMXFclunCGpvVfqov3fSwLWOnPQHIfSFzLNVxmgH15u9mGMr4Av', 0),
(617, 'test', 'STARTER', 'XS', 5, 90, '2025-06-10 04:14:56', '2025-06-10 04:16:56', '2025-06-24 02:45:43', 'cs_test_a1fuzlzXzuMTyInpmdM4BUpfPITqIbTzgVfGBWqlm3OIUdss1iBnT65kFe', 0),
(618, 'test', 'STARTER', 'XS', 5, 90, '2025-06-10 04:16:34', '2025-06-10 04:18:34', '2025-06-24 02:45:43', 'cs_test_a1EwgIESAo87bmlXY9mI0ytcC8WpvzoF0c6CqoAolGvbOdoAqAYa7Q0JG1', 0),
(619, 'test', 'BUSINESS', 'XS', 5, 115, '2025-06-10 04:24:50', '2025-06-10 04:26:50', '2025-06-24 02:45:43', 'cs_test_a1FXX1Q9s1Mt7OwZoWTXFd4Rw6uwxMtYSoHo4pxH9eMlqBoxsvuYqNnBMz', 0),
(620, 'test', 'STARTER', 'XS', 5, 90, '2025-06-10 04:28:08', '2025-06-10 04:30:08', '2025-06-24 02:45:43', 'cs_test_a1P2P62Acslp60tV5x2z9TguSCLWLXZjKJfyPJZDKVVulDtWyE5yjhRVyl', 0),
(621, 'test', 'STARTER', 'XS', 5, 90, '2025-06-10 04:30:08', '2025-06-10 04:32:08', '2025-06-24 02:45:43', 'cs_test_a1BlaxgVAQw1A2R40DldkrU22PKdXHiUlskqN3jBvM1xXtPCY7T5SppmK0', 0),
(622, 'test', 'BUSINESS', 'XS', 5, 115, '2025-06-10 04:38:07', '2025-06-10 04:40:07', '2025-06-24 02:45:43', 'cs_test_a1yE1j7ObLPtPhGfghMBpTPFTXSMnG8wbdzjVOipIf8leN4ejze9wUJirh', 0),
(623, 'test', 'STARTER', 'XS', 5, 90, '2025-06-10 04:41:12', '2025-06-10 04:43:12', '2025-06-24 02:45:43', 'cs_test_a1Y1o3WFydVrU5rWvrRoXKmFMO30JPD3F0wqfa8RC8dhRoUxtVhESMr1mK', 0),
(624, 'test', 'BUSINESS', 'XS', 5, 115, '2025-06-10 04:42:10', '2025-06-10 04:44:10', '2025-06-24 02:45:43', 'cs_test_a1TWefodYIhvUVB3ODAZbqHYoSZ1EvwQx03JHNSbGErmArmrge9w83PgTB', 0),
(625, 'test', 'STARTER', 'XS', 5, 90, '2025-06-10 04:45:57', '2025-06-10 04:47:57', '2025-06-24 02:45:43', 'cs_test_a1Q3GyLT8BA0vfIuocxc95FwFABxAg2IOVhRVfgRYCnDoR2b9XvKag4AZO', 0),
(626, 'test', 'STARTER', 'XS', 5, 90, '2025-06-11 02:28:15', '2025-06-11 02:30:15', '2025-06-24 02:45:43', 'cs_test_a1znPNQvgsGr2FPVxX6qcECsDNLm0ZQsGOhlhy4QV4Z0vQ0S7r9JZGaknT', 0),
(627, 'user92931', 'STARTER', 'XS', 5, 90, '2025-06-11 04:58:35', '2025-06-11 05:00:35', '2025-06-24 02:45:43', 'cs_test_a1uocSrvIy3sLk1DXLBq701Tp31uIRj77s3BjZzzh5B5p0VAm8wMxhjUVr', 0),
(628, 'HC170', 'STARTER', 'XS', 5, 90, '2025-06-11 07:05:05', '2025-06-11 07:07:05', '2025-06-24 02:45:43', 'cs_test_a1QXC42cxcSP83RKBfJT1wsGDHmwaPyBqqmBf3qmoa9cyY4TFuJMpOkH04', 0),
(629, 'HC171', 'STARTER', 'XS', 5, 90, '2025-06-11 07:24:57', '2025-06-11 07:26:57', '2025-06-24 02:45:43', 'cs_test_a16QT5xIYily22sSLHX98TU7uAlWFpUtI9vn9eXVRBVopEJuhvZ1SKveZA', 0),
(630, 'HC172', 'STARTER', 'XS', 5, 90, '2025-06-11 07:31:10', '2025-06-11 07:33:10', '2025-06-24 02:45:43', 'cs_test_a1LcdO5h5NMxD1mAocFwAZ8lRvjToVLhokaUBhyO0jbMYa2raggTiUdpOV', 0),
(631, 'HC173', 'STARTER', 'XS', 5, 90, '2025-06-11 07:36:01', '2025-06-11 07:38:01', '2025-06-24 02:45:43', 'cs_test_a1FHhxQi4r6jumv26DQQeouz28GgBqmIUKTCVQK5VEWEE0blvkGSFyarq5', 0),
(632, 'HC174', 'STARTER', 'XS', 5, 90, '2025-06-11 08:18:25', '2025-06-11 08:20:25', '2025-06-24 02:45:43', 'cs_test_a1Ag9j2AvOWEyThfRDO9DgpiQVGgxq7bwzHg90961xXMya5Vt1oHVYvv93', 0),
(633, 'test', 'STARTER', 'XS', 5, 90, '2025-06-11 10:32:08', '2025-06-11 10:34:08', '2025-06-24 02:45:43', 'pi_3RYlwjEJRyUMDOj50hOoUoKr', 0),
(634, 'test', 'STARTER', 'XS', 5, 90, '2025-06-11 10:34:15', '2025-06-11 10:36:15', '2025-06-24 02:45:43', 'pi_3RYlyxEJRyUMDOj511f4VrP1', 0),
(635, 'test', 'STARTER', 'XS', 5, 90, '2025-06-11 10:36:21', '2025-06-11 10:38:21', '2025-06-24 02:45:43', 'pi_3RYm0qEJRyUMDOj51aO5ZRLH', 0),
(636, 'test', 'STARTER', 'S', 10, 150, '2025-06-11 10:36:21', '2025-06-11 10:38:21', '2025-06-24 02:45:43', 'pi_3RYm0qEJRyUMDOj51aO5ZRLH', 0),
(637, 'test', 'BUSINESS', 'XS', 5, 115, '2025-06-11 10:36:21', '2025-06-11 10:38:21', '2025-06-24 02:45:43', 'pi_3RYm0qEJRyUMDOj51aO5ZRLH', 0),
(638, 'test', 'STARTER', 'XS', 5, 90, '2025-06-11 10:40:39', '2025-06-11 10:42:39', '2025-06-24 02:45:43', 'pi_3RYm59EJRyUMDOj50SYps07I', 0),
(639, 'test', 'STARTER', 'XS', 5, 90, '2025-06-11 10:47:44', '2025-06-11 10:49:44', '2025-06-24 02:45:43', 'pi_3RYmBzEJRyUMDOj51GtzgbSi', 0),
(640, 'test', 'STARTER', 'S', 10, 150, '2025-06-11 10:47:44', '2025-06-11 10:49:44', '2025-06-24 02:45:43', 'pi_3RYmBzEJRyUMDOj51GtzgbSi', 0),
(641, 'test', 'BUSINESS', 'XS', 5, 115, '2025-06-11 10:47:44', '2025-06-11 10:49:44', '2025-06-24 02:45:43', 'pi_3RYmBzEJRyUMDOj51GtzgbSi', 0),
(642, 'test', 'BUSINESS', 'S', 10, 200, '2025-06-11 10:47:44', '2025-06-11 10:49:44', '2025-06-24 02:45:43', 'pi_3RYmBzEJRyUMDOj51GtzgbSi', 0),
(643, 'test', 'ULTIMATE', 'XS', 5, 165, '2025-06-11 10:47:44', '2025-06-11 10:49:44', '2025-06-24 02:45:43', 'pi_3RYmBzEJRyUMDOj51GtzgbSi', 0),
(644, 'test', 'ULTIMATE', 'S', 10, 300, '2025-06-11 10:47:44', '2025-06-11 10:49:44', '2025-06-24 02:45:43', 'pi_3RYmBzEJRyUMDOj51GtzgbSi', 0),
(645, 'test', 'STARTER', 'XS', 5, 90, '2025-06-11 11:22:28', '2025-06-11 11:24:28', '2025-06-24 02:45:43', 'pi_3RYmjUEJRyUMDOj50ZO6mAJS', 0),
(646, 'test', 'STARTER', 'XS', 5, 90, '2025-06-11 11:29:01', '2025-06-11 11:31:01', '2025-06-24 02:45:43', 'pi_3RYmpqEJRyUMDOj51lJiRy8t', 0),
(647, 'test', 'STARTER', 'XS', 5, 90, '2025-06-11 11:37:04', '2025-06-11 11:39:04', '2025-06-24 02:45:43', 'cs_test_a1rc7Me1aczD1HMalumxhHgsIFn3rSIe7QtqFv5xZCrpEpMQCYw1clIVP1', 0),
(648, 'test', 'STARTER', 'XS', 5, 90, '2025-06-12 02:45:05', '2025-06-12 02:47:05', '2025-06-24 02:45:43', 'pi_3RZ18LEJRyUMDOj50gYCN8Hr', 0),
(649, 'test', 'BUSINESS', 'XS', 5, 115, '2025-06-12 02:52:30', '2025-06-12 02:54:30', '2025-06-24 02:45:43', 'cs_test_a1g21YgztjpaeUbAPKjRRSDTCUA37V3FCTjfACJIZdQLWxL2OtsWxJaGhb', 0),
(650, 'test', 'ULTIMATE', 'XS', 5, 165, '2025-06-12 02:53:21', '2025-06-12 02:55:21', '2025-06-24 02:45:43', 'pi_3RZ1GTEJRyUMDOj51Q2anfTU', 0),
(651, 'test', 'STARTER', 'XS', 5, 90, '2025-06-12 03:00:29', '2025-06-12 03:02:29', '2025-06-24 02:45:43', 'pi_3RZ1NOEJRyUMDOj50FKx8hyT', 0),
(652, 'test', 'BUSINESS', 'XS', 5, 115, '2025-06-12 03:01:12', '2025-06-12 03:03:12', '2025-06-24 02:45:43', 'pi_3RZ1O5EJRyUMDOj50FRrjgSQ', 0),
(653, 'test', 'BUSINESS', 'S', 10, 200, '2025-06-12 03:01:12', '2025-06-12 03:03:12', '2025-06-24 02:45:43', 'pi_3RZ1O5EJRyUMDOj50FRrjgSQ', 0),
(654, 'test', 'ULTIMATE', 'XS', 5, 165, '2025-06-12 03:01:12', '2025-06-12 03:03:12', '2025-06-24 02:45:43', 'pi_3RZ1O5EJRyUMDOj50FRrjgSQ', 0),
(655, 'test', 'STARTER', 'XS', 5, 90, '2025-06-12 03:26:02', '2025-06-12 03:28:02', '2025-06-24 02:45:43', 'pi_3RZ1m7EJRyUMDOj50AUoRwqe', 0),
(656, 'test', 'STARTER', 'XS', 5, 90, '2025-06-12 03:45:46', '2025-06-12 03:47:46', '2025-06-24 02:45:43', 'pi_3RZ25DEJRyUMDOj51PxVitqC', 0),
(657, 'HC175', 'STARTER', 'XS', 5, 90, '2025-06-12 03:58:18', '2025-06-12 04:00:18', '2025-06-24 02:45:43', 'cs_test_a1RPwpLMoV6OmZjNYYGQ7lJZTY6iPWkwXvC1LAguGbdurMPoXR8Dyzq87m', 0),
(658, 'HC175', 'STARTER', 'S', 10, 150, '2025-06-12 03:59:37', '2025-06-12 04:01:37', '2025-06-24 02:45:43', 'pi_3RZ2IcEJRyUMDOj50ytFOFC7', 0),
(659, 'HC175', 'BUSINESS', 'XS', 5, 115, '2025-06-12 03:59:37', '2025-06-12 04:01:37', '2025-06-24 02:45:43', 'pi_3RZ2IcEJRyUMDOj50ytFOFC7', 0),
(660, 'HC175', 'ULTIMATE', 'XS', 5, 165, '2025-06-12 03:59:37', '2025-06-12 04:01:37', '2025-06-24 02:45:43', 'pi_3RZ2IcEJRyUMDOj50ytFOFC7', 0),
(661, 'HC175', 'STARTER', 'XS', 5, 90, '2025-06-12 04:00:31', '2025-06-12 04:02:31', '2025-06-24 02:45:43', 'cs_test_a1M3fL84Nnpd3F8ROVQcRlWPxD7FDUDClcZA1i9UlPBAcyLSyqBODPElbh', 0),
(662, 'HC175', 'BUSINESS', 'XS', 5, 115, '2025-06-12 04:07:38', '2025-06-12 04:09:38', '2025-06-24 02:45:43', 'cs_test_a1nBegfg09YUbCqbVwVKJE6WRWkry6YT3LBvhuil1ORIjpeayqKKqq7Hro', 0),
(663, 'HC175', 'ULTIMATE', 'XS', 5, 165, '2025-06-12 04:08:24', '2025-06-12 04:10:24', '2025-06-24 02:45:43', 'cs_test_a1GrxJZnmIO2qBa7wafd6sTN1n6WhTdWCD4G7QgfZZxeCuHK8hvUznOtbt', 0),
(664, 'HC175', 'BUSINESS', 'XS', 5, 115, '2025-06-12 05:00:28', '2025-06-12 05:02:28', '2025-06-24 02:45:43', 'pi_3RZ3FVEJRyUMDOj51cJi6lPk', 0),
(665, 'HC175', 'STARTER', 'XS', 5, 90, '2025-06-12 05:00:28', '2025-06-12 05:02:28', '2025-06-24 02:45:43', 'pi_3RZ3FVEJRyUMDOj51cJi6lPk', 0),
(666, 'HC175', 'ULTIMATE', 'XS', 5, 165, '2025-06-12 05:00:28', '2025-06-12 05:02:28', '2025-06-24 02:45:43', 'pi_3RZ3FVEJRyUMDOj51cJi6lPk', 0),
(667, 'HC175', 'ULTIMATE', 'S', 10, 300, '2025-06-12 05:00:28', '2025-06-12 05:02:28', '2025-06-24 02:45:43', 'pi_3RZ3FVEJRyUMDOj51cJi6lPk', 0),
(668, 'HC175', 'BUSINESS', 'S', 10, 200, '2025-06-12 05:00:28', '2025-06-12 05:02:28', '2025-06-24 02:45:43', 'pi_3RZ3FVEJRyUMDOj51cJi6lPk', 0),
(669, 'HC175', 'STARTER', 'S', 10, 150, '2025-06-12 05:00:28', '2025-06-12 05:02:28', '2025-06-24 02:45:43', 'pi_3RZ3FVEJRyUMDOj51cJi6lPk', 0),
(670, 'test', 'STARTER', 'XS', 5, 90, '2025-06-12 05:35:16', '2025-06-12 05:37:16', '2025-06-24 02:45:43', 'pi_3RZ3nBEJRyUMDOj50TPBOWJk', 0),
(671, 'HC176', 'STARTER', 'XS', 5, 90, '2025-06-12 10:47:32', '2025-06-12 10:49:32', '2025-06-24 02:45:43', 'cs_test_a1pUidK1zOQpRdaG95xQp8S19EcBbJ1NwZzNwubnBuhf9ZCgeCKwkQgAwT', 0),
(672, 'HC176', 'STARTER', 'XS', 5, 90, '2025-06-12 10:55:36', '2025-06-12 10:57:36', '2025-06-24 02:45:43', 'pi_3RZ8nAEJRyUMDOj51hRFm2H5', 0),
(673, 'test', 'STARTER', 'XS', 5, 90, '2025-06-13 03:29:21', '2025-06-13 03:31:21', '2025-06-24 02:45:43', 'pi_3RZOIsEJRyUMDOj51D3Rau77', 0),
(674, 'test', 'ULTIMATE', 'XS', 5, 165, '2025-06-13 03:29:21', '2025-06-13 03:31:21', '2025-06-24 02:45:43', 'pi_3RZOIsEJRyUMDOj51D3Rau77', 0),
(675, 'HC177', 'STARTER', 'XS', 5, 90, '2025-06-13 08:18:45', '2025-06-13 08:20:45', '2025-06-24 02:45:43', 'cs_test_a1IUAYROMpoEdAizta9OpDqk0RFs5BkApqaUF0JTeB9WwXC9ROIQCmejY6', 0),
(676, 'HC178', 'STARTER', 'XS', 5, 90, '2025-06-13 08:34:23', '2025-06-13 08:36:23', '2025-06-24 02:45:43', 'cs_test_a141x1idIBUGIg4xGIaymY5Zynu6zmIJZm6WKBTuC28mYDZK77jv0SnUh5', 0),
(677, 'HC179', 'STARTER', 'XS', 5, 90, '2025-06-13 08:43:48', '2025-06-13 08:45:48', '2025-06-24 02:45:43', 'cs_test_a1sYWypUqFNVTz1Nq8X3eyOYOgh0pnxSdnWLJnjpAbFZqxU755BIxidH3z', 0),
(678, 'HC180', 'STARTER', 'XS', 5, 90, '2025-06-18 03:56:03', '2025-06-18 03:58:03', '2025-06-24 02:45:43', 'cs_test_a1osXCBlP1gSqrLK7NCz7N135i9CNxjltNY4FqDMrO7sMr7KgGl5WaSONC', 0),
(679, 'HC181', 'STARTER', 'XS', 5, 90, '2025-06-18 06:43:07', '2025-06-18 06:45:07', '2025-06-24 02:45:43', 'cs_test_a1XpUm5VuUOLt3moqGDQCRZQGDj34okFVHLwQUniKeA2QnTPu0JXAbWL3D', 0),
(680, 'HC182', 'STARTER', 'XS', 5, 90, '2025-06-18 10:15:46', '2025-06-18 10:17:46', '2025-06-24 02:45:43', 'cs_test_a1OM9rnvXejU1GiuIIK1Jt7qWpNzD3Z4PYTRLfSEMEFNpXTOsLdDordQcp', 0),
(681, 'HC183', 'STARTER', 'XS', 5, 90, '2025-06-18 10:28:44', '2025-06-18 10:30:44', '2025-06-24 02:45:43', 'cs_test_a1BMPcCoaZRnUGwkZoHxnluO01vGzF86ggTIa8K9GkuJnQkBtCZWpiNjgB', 0),
(682, 'HC184', 'BUSINESS', 'S', 10, 200, '2025-06-20 03:39:54', '2025-06-20 03:41:54', '2025-06-24 02:45:43', 'cs_test_b16w4rlJgSIdDNoP8W6UWbWuYKae3MpHAImPjmM2JbfpLh97SDdnZ6EYHF', 0),
(683, 'HC184', 'STARTER', 'XS', 5, 90, '2025-06-20 03:39:54', '2025-06-20 03:41:54', '2025-06-24 02:45:43', 'cs_test_b16w4rlJgSIdDNoP8W6UWbWuYKae3MpHAImPjmM2JbfpLh97SDdnZ6EYHF', 0),
(684, 'HC184', 'ULTIMATE', 'XS', 5, 165, '2025-06-20 03:39:54', '2025-06-20 03:41:54', '2025-06-24 02:45:43', 'cs_test_b16w4rlJgSIdDNoP8W6UWbWuYKae3MpHAImPjmM2JbfpLh97SDdnZ6EYHF', 0),
(685, 'HC185', 'ULTIMATE', 'S', 10, 300, '2025-06-23 03:42:57', '2025-06-23 03:44:57', '2025-06-24 02:45:43', 'cs_test_b1IdPP6DGQSej9LERdD41kORnjHfOAXtdE08j2JFj8zHYcuPeYlfDbKUtk', 0),
(686, 'HC185', 'STARTER', 'XS', 5, 90, '2025-06-23 03:42:57', '2025-06-23 03:44:57', '2025-06-24 02:45:43', 'cs_test_b1IdPP6DGQSej9LERdD41kORnjHfOAXtdE08j2JFj8zHYcuPeYlfDbKUtk', 0),
(687, 'HC185', 'starter', '0', 5, 100, '2023-12-23 09:47:19', '2023-12-23 09:49:19', '2025-06-23 09:48:48', '0', 0),
(688, 'HC185', 'ultimate', '0', 3, 100, '2024-05-23 09:47:19', '2024-05-23 09:49:19', '2025-06-23 09:48:48', '0', 0),
(689, 'HC185', 'starter', '0', 2, 100, '2024-07-02 09:47:19', '2024-07-02 09:49:19', '2025-06-24 02:45:43', '0', 0),
(690, 'HC185', 'ultimate', '0', 4, 100, '2024-07-23 09:47:19', '2024-07-23 09:49:19', '2025-06-24 02:45:43', '0', 0),
(691, 'HC185', 'starter', '0', 10, 100, '2024-12-23 09:47:19', '2024-12-23 09:49:19', '2025-06-24 02:45:43', '0', 0),
(692, 'HC185', 'BUSINESS', 'XS', 5, 115, '2025-06-23 11:19:34', '2025-06-23 11:21:34', '2025-06-24 02:45:43', 'pi_3Rd8PQEJRyUMDOj50drfLy3v', 0),
(693, 'HC186', 'STARTER', 'XS', 5, 90, '2025-06-24 02:08:45', '2025-06-24 02:10:45', '2025-06-24 02:45:43', 'cs_test_b17XOnY9tsF3bqWrO6YXvF4XGPO3tbaTqlzsyaf45bCruEelg6ANcCWjG2', 0),
(694, 'HC186', 'BUSINESS', 'XS', 5, 115, '2025-06-24 02:08:45', '2025-06-24 02:10:45', '2025-06-24 02:45:43', 'cs_test_b17XOnY9tsF3bqWrO6YXvF4XGPO3tbaTqlzsyaf45bCruEelg6ANcCWjG2', 0),
(695, 'HC186', 'ULTIMATE', 'XS', 5, 165, '2025-06-24 02:08:45', '2025-06-24 02:10:45', '2025-06-24 02:45:43', 'cs_test_b17XOnY9tsF3bqWrO6YXvF4XGPO3tbaTqlzsyaf45bCruEelg6ANcCWjG2', 0),
(696, 'HC186', 'STARTER', 'XS', 5, 90, '2025-06-24 02:19:18', '2025-06-24 02:21:18', '2025-06-24 02:45:43', 'pi_3RdMS8EJRyUMDOj508Q3vDt7', 0),
(697, 'HC186', 'BUSINESS', 'XS', 5, 115, '2025-06-24 02:19:18', '2025-06-24 02:21:18', '2025-06-24 02:45:43', 'pi_3RdMS8EJRyUMDOj508Q3vDt7', 0),
(698, 'HC186', 'ULTIMATE', 'XS', 5, 165, '2025-06-24 02:19:18', '2025-06-24 02:21:18', '2025-06-24 02:45:43', 'pi_3RdMS8EJRyUMDOj508Q3vDt7', 0),
(699, 'HC186', 'STARTER', 'XS', 5, 90, '2025-06-24 03:03:29', '2025-06-24 03:05:29', '2025-06-24 03:22:06', 'pi_3RdN8tEJRyUMDOj50gG71Map', 0),
(700, 'HC186', 'BUSINESS', 'XS', 5, 115, '2025-06-24 03:03:29', '2025-06-24 03:05:29', '2025-06-24 03:22:06', 'pi_3RdN8tEJRyUMDOj50gG71Map', 0),
(701, 'HC186', 'ULTIMATE', 'XS', 5, 165, '2025-06-24 03:03:29', '2025-06-24 03:05:29', '2025-06-24 03:22:06', 'pi_3RdN8tEJRyUMDOj50gG71Map', 0),
(702, 'HC186', 'STARTER', 'S', 10, 150, '2025-06-24 03:03:29', '2025-06-24 03:05:29', '2025-06-24 03:22:06', 'pi_3RdN8tEJRyUMDOj50gG71Map', 0),
(703, 'HC186', 'BUSINESS', 'S', 10, 200, '2025-06-24 03:03:29', '2025-06-24 03:05:29', '2025-06-24 03:22:06', 'pi_3RdN8tEJRyUMDOj50gG71Map', 0),
(704, 'HC186', 'ULTIMATE', 'S', 10, 300, '2025-06-24 03:03:29', '2025-06-24 03:05:29', '2025-06-24 03:22:06', 'pi_3RdN8tEJRyUMDOj50gG71Map', 0),
(705, 'HC186', 'STARTER', 'XS', 5, 90, '2025-06-24 03:30:19', '2025-06-24 03:32:19', '2025-06-24 03:46:11', 'pi_3RdNYrEJRyUMDOj51vAK9Isx', 0),
(706, 'HC186', 'BUSINESS', 'XS', 5, 115, '2025-06-24 03:30:19', '2025-06-24 03:32:19', '2025-06-24 03:46:11', 'pi_3RdNYrEJRyUMDOj51vAK9Isx', 0),
(707, 'HC186', 'ULTIMATE', 'XS', 5, 165, '2025-06-24 03:30:19', '2025-06-24 03:32:19', '2025-06-24 03:46:11', 'pi_3RdNYrEJRyUMDOj51vAK9Isx', 0),
(708, 'HC186', 'STARTER', 'XS', 5, 90, '2025-06-24 03:47:24', '2025-06-24 03:49:24', '2025-06-24 03:49:34', 'pi_3RdNpOEJRyUMDOj504bcaj0S', 0),
(709, 'HC186', 'BUSINESS', 'XS', 5, 115, '2025-06-24 03:47:24', '2025-06-24 03:49:24', '2025-06-24 03:49:34', 'pi_3RdNpOEJRyUMDOj504bcaj0S', 0),
(710, 'HC186', 'ULTIMATE', 'XS', 5, 165, '2025-06-24 03:47:24', '2025-06-24 03:49:24', '2025-06-24 03:49:34', 'pi_3RdNpOEJRyUMDOj504bcaj0S', 0),
(711, 'HC186', 'STARTER', 'XS', 5, 90, '2025-06-24 03:53:50', '2025-06-24 03:55:50', '2025-06-24 04:00:20', 'pi_3RdNvcEJRyUMDOj50S1gr1lC', 0),
(712, 'HC186', 'STARTER', 'XS', 5, 90, '2025-06-24 04:05:34', '2025-06-24 04:07:34', '2025-06-24 04:10:51', 'pi_3RdO6zEJRyUMDOj51aVe0UIz', 0),
(713, 'HC186', 'BUSINESS', 'XS', 5, 115, '2025-06-24 04:05:34', '2025-06-24 04:07:34', '2025-06-24 04:10:51', 'pi_3RdO6zEJRyUMDOj51aVe0UIz', 0),
(714, 'HC186', 'STARTER', 'XS', 5, 90, '2025-06-24 04:12:15', '2025-06-24 04:14:15', '2025-06-24 04:17:26', 'pi_3RdODREJRyUMDOj51GGUJqxn', 0),
(715, 'HC186', 'STARTER', 'XS', 5, 90, '2025-06-24 04:19:59', '2025-06-24 04:21:59', '2025-06-24 04:21:59', 'pi_3RdOKvEJRyUMDOj51Y5VdZhy', 0),
(716, 'HC186', 'ULTIMATE', 'XS', 5, 165, '2025-06-24 04:23:29', '2025-06-24 04:25:29', '2025-06-24 04:25:30', 'pi_3RdOOJEJRyUMDOj50bKb8TEN', 0),
(717, 'HC186', 'BUSINESS', 'XS', 5, 115, '2025-06-24 04:26:27', '2025-06-24 04:28:27', '2025-06-24 04:28:27', 'pi_3RdORAEJRyUMDOj507rpRVkp', 0),
(718, 'HC186', 'STARTER', 'XS', 5, 90, '2025-06-24 04:30:27', '2025-06-24 04:32:27', '2025-06-24 04:32:27', 'pi_3RdOV4EJRyUMDOj51UTLsVdg', 0),
(719, 'HC186', 'BUSINESS', 'XS', 5, 115, '2025-06-24 04:30:27', '2025-06-24 04:32:27', '2025-06-24 04:32:27', 'pi_3RdOV4EJRyUMDOj51UTLsVdg', 0),
(720, 'HC186', 'ULTIMATE', 'XS', 5, 165, '2025-06-24 04:31:00', '2025-06-24 04:33:00', '2025-06-24 04:33:02', 'pi_3RdOVaEJRyUMDOj5063IpavJ', 0),
(721, 'HC186', 'STARTER', 'XS', 5, 90, '2025-06-24 04:38:15', '2025-06-24 04:40:15', '2025-06-24 04:40:15', 'pi_3RdOcbEJRyUMDOj5044dJr57', 0),
(722, 'HC186', 'BUSINESS', 'XS', 5, 115, '2025-06-24 04:38:15', '2025-06-24 04:40:15', '2025-06-24 04:40:15', 'pi_3RdOcbEJRyUMDOj5044dJr57', 0),
(723, 'HC186', 'ULTIMATE', 'XS', 5, 165, '2025-06-24 04:38:15', '2025-06-24 04:40:15', '2025-06-24 04:40:15', 'pi_3RdOcbEJRyUMDOj5044dJr57', 0),
(724, 'HC186', 'STARTER', 'XS', 5, 90, '2025-06-24 04:53:16', '2025-06-24 04:55:16', '2025-06-24 04:55:16', 'pi_3RdOr8EJRyUMDOj51iyQsTxv', 0),
(725, 'HC186', 'BUSINESS', 'XS', 5, 115, '2025-06-24 04:53:16', '2025-06-24 04:55:16', '2025-06-24 04:55:16', 'pi_3RdOr8EJRyUMDOj51iyQsTxv', 0),
(726, 'HC186', 'STARTER', 'XS', 5, 90, '2025-06-24 05:09:00', '2025-06-24 05:11:00', '2025-06-24 05:11:00', 'pi_3RdP6MEJRyUMDOj51iCKG6aP', 0),
(727, 'HC186', 'STARTER', 'XS', 5, 90, '2025-06-24 05:53:41', '2025-06-24 05:55:41', '2025-06-24 05:55:41', 'pi_3RdPncEJRyUMDOj51mAQg9k3', 0),
(728, 'HC185', 'starter', '0', 5, 100, '2023-12-24 05:57:57', '2023-12-24 05:59:57', '2025-06-24 06:16:00', '0', 0),
(729, 'HC185', 'ultimate', '0', 3, 100, '2024-05-24 05:57:57', '2024-05-24 05:59:57', '2025-06-24 06:16:00', '0', 0),
(730, 'HC185', 'starter', '0', 2, 100, '2024-07-03 05:57:57', '2024-07-03 05:59:57', '2025-06-24 06:16:00', '0', 0),
(731, 'HC185', 'ultimate', '0', 4, 100, '2024-07-24 05:57:57', '2024-07-24 05:59:57', '2025-06-24 06:16:00', '0', 0),
(732, 'HC185', 'starter', '0', 10, 100, '2024-12-24 05:57:57', '2024-12-24 05:59:57', '2025-06-24 06:16:00', '0', 0),
(733, 'HC185', 'starter', '0', 5, 100, '2023-12-24 05:58:58', '2023-12-24 06:00:58', '2025-06-24 06:16:00', '0', 0),
(734, 'HC185', 'ultimate', '0', 3, 100, '2024-05-24 05:58:58', '2024-05-24 06:00:58', '2025-06-24 06:16:00', '0', 0),
(735, 'HC185', 'starter', '0', 2, 100, '2024-07-03 05:58:58', '2024-07-03 06:00:58', '2025-06-24 06:16:00', '0', 0),
(736, 'HC185', 'ultimate', '0', 4, 100, '2024-07-24 05:58:58', '2024-07-24 06:00:58', '2025-06-24 06:16:00', '0', 0),
(737, 'HC185', 'starter', '0', 10, 100, '2024-12-24 05:58:58', '2024-12-24 06:00:58', '2025-06-24 06:16:00', '0', 0),
(738, 'HC186', 'STARTER', 'XS', 5, 90, '2025-06-24 06:16:17', '2025-06-24 06:18:17', '2025-06-24 06:18:27', 'pi_3RdQ9UEJRyUMDOj50Jf3Qqt8', 0),
(739, 'HC186', 'STARTER', 'S', 10, 150, '2025-06-24 06:16:17', '2025-06-24 06:18:17', '2025-06-24 06:18:27', 'pi_3RdQ9UEJRyUMDOj50Jf3Qqt8', 0),
(740, 'HC186', 'STARTER', 'XS', 5, 90, '2025-06-24 06:25:24', '2025-06-24 06:27:24', '2025-06-24 06:27:24', 'pi_3RdQIIEJRyUMDOj50mASPH1X', 0),
(741, 'HC186', 'BUSINESS', 'XS', 5, 115, '2025-06-24 06:25:24', '2025-06-24 06:27:24', '2025-06-24 06:27:24', 'pi_3RdQIIEJRyUMDOj50mASPH1X', 0),
(742, 'HC186', 'ULTIMATE', 'XS', 5, 165, '2025-06-24 06:25:24', '2025-06-24 06:27:24', '2025-06-24 06:27:24', 'pi_3RdQIIEJRyUMDOj50mASPH1X', 0),
(743, 'HC186', 'BUSINESS', 'XS', 5, 115, '2025-06-24 06:30:08', '2025-06-24 06:32:08', '2025-06-24 06:32:08', 'pi_3RdQMtEJRyUMDOj50Y5Tr0wa', 0),
(744, 'HC186', 'BUSINESS', 'XS', 5, 115, '2025-06-24 06:32:53', '2025-06-24 06:34:53', '2025-06-24 06:34:53', 'pi_3RdQPXEJRyUMDOj50YwroIch', 0),
(745, 'HC186', 'STARTER', 'XS', 5, 90, '2025-06-24 06:43:33', '2025-06-24 06:45:33', '2025-06-24 06:45:33', 'pi_3RdQZrEJRyUMDOj50Fr1NboH', 0),
(746, 'HC186', 'BUSINESS', 'XS', 5, 115, '2025-06-24 06:43:33', '2025-06-24 06:45:33', '2025-06-24 06:45:33', 'pi_3RdQZrEJRyUMDOj50Fr1NboH', 0),
(747, 'HC186', 'ULTIMATE', 'XS', 5, 165, '2025-06-24 06:43:33', '2025-06-24 06:45:33', '2025-06-24 06:45:33', 'pi_3RdQZrEJRyUMDOj50Fr1NboH', 0),
(748, 'HC186', 'STARTER', 'XS', 5, 90, '2025-06-24 06:48:28', '2025-06-24 06:50:28', '2025-06-24 06:50:28', 'pi_3RdQedEJRyUMDOj51qPjzfBq', 0),
(749, 'HC186', 'STARTER', 'XS', 5, 90, '2025-06-24 06:59:19', '2025-06-24 07:01:19', '2025-06-24 07:01:27', 'pi_3RdQp7EJRyUMDOj50P85GMRU', 0),
(750, 'HC186', 'BUSINESS', 'XS', 5, 115, '2025-06-24 06:59:19', '2025-06-24 07:01:19', '2025-06-24 07:01:27', 'pi_3RdQp7EJRyUMDOj50P85GMRU', 0),
(751, 'HC187', 'STARTER', 'XS', 5, 90, '2025-06-25 04:43:02', '2025-06-25 04:45:02', NULL, 'cs_test_a1AF09V2lY7tdymgdcbMQhid3YzTU5UilNzCLPEcpjJdeKjwoBcrvfAQtE', 5),
(752, 'test', 'STARTER', 'XS', 5, 90, '2025-06-25 04:53:33', '2025-06-25 04:55:33', NULL, 'pi_3RdlKwEJRyUMDOj50qgBh5sV', 0),
(753, 'HC177', 'STARTER', 'XS', 5, 90, '2025-06-25 04:56:42', '2025-06-25 04:58:42', NULL, 'pi_3RdlO0EJRyUMDOj50WdzqKdE', 5),
(754, 'HC188', 'STARTER', 'XS', 5, 90, '2025-06-25 05:08:48', '2025-06-25 05:10:48', NULL, 'cs_test_a1PZVQYmS5qX0qcaSFYWOPKkmTYmj2uUMj5FnzmZeEXp7RTi88ktcB6Kpr', 5),
(755, 'test', 'STARTER', 'XS', 5, 90, '2025-06-25 06:45:08', '2025-06-25 06:47:08', NULL, 'pi_3Rdn4wEJRyUMDOj502LSwUIv', 0),
(756, 'test', 'BUSINESS', 'XS', 5, 115, '2025-06-25 07:05:36', '2025-06-25 07:07:36', NULL, 'pi_3RdnOkEJRyUMDOj51YxBgwNP', 1),
(757, 'test', 'STARTER', 'XS', 5, 90, '2025-06-25 07:15:49', '2025-06-25 07:17:49', NULL, 'pi_3RdnYdEJRyUMDOj51FdTupOp', 2),
(758, 'test', 'ULTIMATE', 'XS', 5, 165, '2025-06-25 11:43:03', '2025-06-25 11:45:03', NULL, 'pi_3RdrjFEJRyUMDOj51NwFa123', 2),
(759, 'HC189', 'STARTER', 'XS', 5, 90, '2025-06-26 03:31:25', '2025-06-26 03:33:25', NULL, 'cs_test_a1rpXFiEGAt4gCN4OkWuxw9Pic37kwRyJcztolhQfUPYSJ2ddGxXRJkeZL', 5),
(760, 'HC190', 'STARTER', 'XS', 5, 90, '2025-06-26 05:55:57', '2025-06-26 05:57:57', NULL, 'cs_test_b1Aewy5kR9gWm2hRucsbK7Hne6CN0cdFS4CppuFJ4uSO5LgLAvavistsDf', 5),
(761, 'HC190', 'BUSINESS', 'XS', 5, 115, '2025-06-26 05:55:57', '2025-06-26 05:57:57', NULL, 'cs_test_b1Aewy5kR9gWm2hRucsbK7Hne6CN0cdFS4CppuFJ4uSO5LgLAvavistsDf', 5),
(762, 'HC191', 'STARTER', 'XS', 5, 90, '2025-06-26 06:12:38', '2025-06-26 06:14:38', NULL, 'cs_test_b1KALVKV0RdKZubV82wiZDYY2WAwJbVYG7QJ4mg0zm2HMV2ovJZOwNIT5R', 5),
(763, 'HC191', 'BUSINESS', 'XS', 5, 115, '2025-06-26 06:12:38', '2025-06-26 06:14:38', NULL, 'cs_test_b1KALVKV0RdKZubV82wiZDYY2WAwJbVYG7QJ4mg0zm2HMV2ovJZOwNIT5R', 5),
(764, 'HC191', 'ULTIMATE', 'XS', 5, 165, '2025-06-26 06:12:38', '2025-06-26 06:14:38', NULL, 'cs_test_b1KALVKV0RdKZubV82wiZDYY2WAwJbVYG7QJ4mg0zm2HMV2ovJZOwNIT5R', 5),
(765, 'HC192', 'STARTER', 'XS', 5, 90, '2025-06-26 06:24:59', '2025-06-26 06:26:59', NULL, 'cs_test_a1X5GrjzN1supa6nUW12ZUeCbdMm9BdLVSNwKlufwuw5kf4gSK3iEzQ4E3', 5),
(766, 'HC193', 'BUSINESS', 'XS', 5, 115, '2025-06-26 06:27:44', '2025-06-26 06:29:44', NULL, 'cs_test_a1taLW7dy6udlQOoZNicx4oZKA3bzCNSIVpfNaTRhEZWij8DfKkDhPGBIR', 5),
(767, 'HC194', 'STARTER', 'XS', 5, 90, '2025-06-26 06:36:47', '2025-06-26 06:38:47', NULL, 'cs_test_a1qeFHXL2mjE5NBAHTmi6XRkJ6nD0cYZgG1fwwKFOYJY7d1DQzTe23enpR', 2),
(768, 'HC195', 'STARTER', 'XS', 5, 90, '2025-06-26 11:27:04', '2025-06-26 11:29:04', NULL, 'cs_test_a1MuMQqr0VPe3RT8yBLWiFBJmjirvv9IHPdloasLpUp8A1uyLgCoaERYM4', 5),
(769, 'HC196', 'BUSINESS', 'XS', 5, 115, '2025-06-26 11:35:28', '2025-06-26 11:37:28', NULL, 'cs_test_a15b7lcTqqMSlWdPINvWhAE7LBktWp0YwZ4cyNBAx5OcDbLmzkx5ijRMSV', 10),
(770, 'HC197', 'STARTER', 'XS', 5, 90, '2025-06-26 11:46:10', '2025-06-26 11:48:10', NULL, 'cs_test_a1ZdOt2opuFKmofhkuvZctWeeB7Kx0zeSekhSuHekbd21LyPNrE5qZ1CGx', 5),
(771, 'HC199', 'STARTER', 'XS', 5, 90, '2025-06-27 02:30:35', '2025-06-27 02:32:35', NULL, 'cs_test_b1xgKIXrJSOtnO1SHSCQaIprcWEi2pz5ZXbsn1nho7cobwDzFFHjLtXvWs', 2),
(772, 'HC199', 'BUSINESS', 'XS', 5, 115, '2025-06-27 02:30:35', '2025-06-27 02:32:35', NULL, 'cs_test_b1xgKIXrJSOtnO1SHSCQaIprcWEi2pz5ZXbsn1nho7cobwDzFFHjLtXvWs', 2),
(773, 'HC199', 'ULTIMATE', 'XS', 5, 165, '2025-06-27 02:30:35', '2025-06-27 02:32:35', NULL, 'cs_test_b1xgKIXrJSOtnO1SHSCQaIprcWEi2pz5ZXbsn1nho7cobwDzFFHjLtXvWs', 2),
(774, 'HC200', 'STARTER', 'XS', 5, 90, '2025-06-27 06:25:28', '2025-06-27 06:27:28', NULL, 'cs_test_a1HZQeo2DtWx5S2RkAOawi39GMDkjXA2BMsr2iXnOsPOVBbxB1qfdhJMPh', 2),
(775, 'HC201', 'STARTER', 'XS', 5, 90, '2025-06-27 16:45:51', '2025-06-27 16:47:51', NULL, 'cs_test_b1sLApydd4iw0bHChI4k1BrZcrv0WWWHz9ySxwWprCzKJVl4CbrJmvVRL2', 5),
(776, 'HC201', 'BUSINESS', 'XS', 5, 115, '2025-06-27 16:45:51', '2025-06-27 16:47:51', NULL, 'cs_test_b1sLApydd4iw0bHChI4k1BrZcrv0WWWHz9ySxwWprCzKJVl4CbrJmvVRL2', 5),
(777, 'HC201', 'ULTIMATE', 'XS', 5, 165, '2025-06-27 16:45:51', '2025-06-27 16:47:51', NULL, 'cs_test_b1sLApydd4iw0bHChI4k1BrZcrv0WWWHz9ySxwWprCzKJVl4CbrJmvVRL2', 5),
(778, 'HC202', 'STARTER', 'XS', 5, 90, '2025-06-27 16:53:38', '2025-06-27 16:55:38', NULL, 'cs_test_b1CZYVo2BG5VWncwe1XCVXJrqn9HBZVluikwyMGRNjaGvgJdbdIGfVEsTN', 5),
(779, 'HC202', 'BUSINESS', 'XS', 5, 115, '2025-06-27 16:53:38', '2025-06-27 16:55:38', NULL, 'cs_test_b1CZYVo2BG5VWncwe1XCVXJrqn9HBZVluikwyMGRNjaGvgJdbdIGfVEsTN', 5),
(780, 'HC202', 'ULTIMATE', 'XS', 5, 165, '2025-06-27 16:53:38', '2025-06-27 16:55:38', NULL, 'cs_test_b1CZYVo2BG5VWncwe1XCVXJrqn9HBZVluikwyMGRNjaGvgJdbdIGfVEsTN', 5),
(781, 'HC202', 'STARTER', 'XS', 5, 90, '2025-06-27 16:54:56', '2025-06-27 16:56:56', NULL, 'pi_3RefYAEJRyUMDOj50RAx5lVd', 5),
(782, 'HC203', 'STARTER', 'XS', 5, 90, '2025-06-27 17:30:17', '2025-06-27 17:32:17', NULL, 'cs_test_b18VxaLIMwjDUokoiGXcISx5frIZgTnMZ4QH2mx8bt786DFFfk7bAsqM1C', 5),
(783, 'HC203', 'BUSINESS', 'XS', 5, 115, '2025-06-27 17:30:17', '2025-06-27 17:32:17', NULL, 'cs_test_b18VxaLIMwjDUokoiGXcISx5frIZgTnMZ4QH2mx8bt786DFFfk7bAsqM1C', 5),
(784, 'HC203', 'ULTIMATE', 'XS', 5, 165, '2025-06-27 17:30:17', '2025-06-27 17:32:17', NULL, 'cs_test_b18VxaLIMwjDUokoiGXcISx5frIZgTnMZ4QH2mx8bt786DFFfk7bAsqM1C', 5),
(785, 'HC203', 'ULTIMATE', 'S', 10, 300, '2025-06-27 17:30:17', '2025-06-27 17:32:17', NULL, 'cs_test_b18VxaLIMwjDUokoiGXcISx5frIZgTnMZ4QH2mx8bt786DFFfk7bAsqM1C', 10),
(786, 'HC204', 'STARTER', 'XS', 5, 90, '2025-06-29 07:53:42', '2025-06-29 07:55:42', NULL, 'cs_test_b1e7r7CFacI0XRT7RYbOSkoxdBvuysmgvlFbb403dJUBQewTN3J3VdSYxg', 2),
(787, 'HC204', 'BUSINESS', 'XS', 5, 115, '2025-06-29 07:53:42', '2025-06-29 07:55:42', NULL, 'cs_test_b1e7r7CFacI0XRT7RYbOSkoxdBvuysmgvlFbb403dJUBQewTN3J3VdSYxg', 4),
(788, 'HC205', 'STARTER', 'XS', 5, 90, '2025-06-29 08:26:33', '2025-06-29 08:28:33', NULL, 'cs_test_b1wC8zJYSIVaSx1QO2AjVvndl577yDojUKzo9sc29PijNljvqwcdszLkq7', 3),
(789, 'HC205', 'BUSINESS', 'XS', 5, 115, '2025-06-29 08:26:33', '2025-06-29 08:28:33', NULL, 'cs_test_b1wC8zJYSIVaSx1QO2AjVvndl577yDojUKzo9sc29PijNljvqwcdszLkq7', 5),
(790, 'HC206', 'STARTER', 'XS', 5, 90, '2025-06-29 08:47:15', '2025-06-29 08:49:15', NULL, 'cs_test_a1Xyv7IPv6zPFXBv4y5JPsnM96I8yeU6I6iI5WRQbdQK7o0n5SX3yJVkm7', 4),
(791, 'HC207', 'STARTER', 'XS', 5, 90, '2025-06-29 08:58:11', '2025-06-29 09:00:11', NULL, 'cs_test_b1WTiSX9wQywEEFUunsc7J23BQnobc7FqqtR9oNLCGzFjbi4zlUAVFf7VS', 4),
(792, 'HC207', 'BUSINESS', 'XS', 5, 115, '2025-06-29 08:58:11', '2025-06-29 09:00:11', NULL, 'cs_test_b1WTiSX9wQywEEFUunsc7J23BQnobc7FqqtR9oNLCGzFjbi4zlUAVFf7VS', 5),
(793, 'HC207', 'ULTIMATE', 'XS', 5, 165, '2025-06-29 08:58:11', '2025-06-29 09:00:11', NULL, 'cs_test_b1WTiSX9wQywEEFUunsc7J23BQnobc7FqqtR9oNLCGzFjbi4zlUAVFf7VS', 5),
(794, 'HC208', 'STARTER', 'XS', 5, 90, '2025-06-29 09:10:42', '2025-06-29 09:12:42', NULL, 'cs_test_b1YaEJFSII0aeuLaofNYIVa0ariliBWBTIIqx2JDqSLQX3GlQ9ykIUS1uw', 5),
(795, 'HC208', 'BUSINESS', 'XS', 5, 115, '2025-06-29 09:10:42', '2025-06-29 09:12:42', NULL, 'cs_test_b1YaEJFSII0aeuLaofNYIVa0ariliBWBTIIqx2JDqSLQX3GlQ9ykIUS1uw', 5),
(796, 'HC208', 'ULTIMATE', 'XS', 5, 165, '2025-06-29 09:10:42', '2025-06-29 09:12:42', NULL, 'cs_test_b1YaEJFSII0aeuLaofNYIVa0ariliBWBTIIqx2JDqSLQX3GlQ9ykIUS1uw', 5),
(797, 'HC209', 'STARTER', 'XS', 5, 90, '2025-06-29 09:15:26', '2025-06-29 09:17:26', '2025-06-29 13:30:53', 'cs_test_a1jetgbMVXOE6dRgRNNyP05rh7T1pNb2jrS5DxdEkdjj2ZwjV5oH63ZUOY', 0),
(798, 'HC209', 'STARTER', 'XS', 5, 90, '2025-06-29 13:32:19', '2026-06-29 13:32:19', NULL, 'pi_3RfLLCEJRyUMDOj50F3AyQ6C', 5),
(799, 'HC210', 'STARTER', 'XS', 5, 90, '2025-06-29 16:03:53', '2026-06-29 16:03:53', NULL, 'cs_test_b1o9td5UfVOnaXs1XO76rNCoiJvOj3vd27MKZzXvHVEu5R4L9KOxYKStQ9', 5),
(800, 'HC210', 'BUSINESS', 'XS', 5, 115, '2025-06-29 16:03:53', '2026-06-29 16:03:53', NULL, 'cs_test_b1o9td5UfVOnaXs1XO76rNCoiJvOj3vd27MKZzXvHVEu5R4L9KOxYKStQ9', 5),
(801, 'HC210', 'ULTIMATE', 'XS', 5, 165, '2025-06-29 16:03:53', '2026-06-29 16:03:53', NULL, 'cs_test_b1o9td5UfVOnaXs1XO76rNCoiJvOj3vd27MKZzXvHVEu5R4L9KOxYKStQ9', 5);

-- --------------------------------------------------------

--
-- Table structure for table `support_tickets`
--

CREATE TABLE `support_tickets` (
  `id` int(11) NOT NULL,
  `appika_id` varchar(10) NOT NULL,
  `user_id` int(11) NOT NULL,
  `subject` varchar(255) NOT NULL,
  `severity` varchar(100) NOT NULL,
  `problem_type` varchar(100) DEFAULT NULL,
  `description` text NOT NULL,
  `ticket_type` enum('starter','premium','ultimate') NOT NULL,
  `status` enum('open','in_progress','resolved','closed') DEFAULT 'open',
  `priority` varchar(100) DEFAULT 'medium',
  `created_at` datetime DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `assigned_admin_id` int(11) DEFAULT NULL,
  `is_seen_by_admin` tinyint(1) NOT NULL DEFAULT 0,
  `attachments` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'JSON array of attachment file paths' CHECK (json_valid(`attachments`)),
  `last_appika_sync` timestamp NULL DEFAULT NULL COMMENT 'Last time this ticket was synced from Appika',
  `appika_updated_at` timestamp NULL DEFAULT NULL COMMENT 'When this ticket was last updated FROM Appika',
  `appika_update_source` varchar(50) DEFAULT NULL COMMENT 'Source of last Appika update (admin_name, system, etc)',
  `needs_appika_sync` tinyint(1) DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

--
-- Dumping data for table `support_tickets`
--

INSERT INTO `support_tickets` (`id`, `appika_id`, `user_id`, `subject`, `severity`, `problem_type`, `description`, `ticket_type`, `status`, `priority`, `created_at`, `updated_at`, `assigned_admin_id`, `is_seen_by_admin`, `attachments`, `last_appika_sync`, `appika_updated_at`, `appika_update_source`, `needs_appika_sync`) VALUES
(160, '', 136, 'Test SG time', 'Normal', 'Slow performance', 'test', 'starter', 'open', 'Normal', '2025-06-04 02:43:20', '2025-06-04 02:43:20', NULL, 0, NULL, NULL, NULL, NULL, 0),
(161, '', 137, 'Test japanese time', 'Normal', 'Antivirus or malware infection', 'hello admin', 'starter', 'open', 'Normal', '2025-06-04 02:48:20', '2025-06-04 02:48:20', NULL, 0, NULL, NULL, NULL, NULL, 0),
(162, '', 138, 'Test china time', 'Normal', 'Slow performance', 'test', 'starter', 'open', 'Normal', '2025-06-04 02:57:37', '2025-06-04 02:57:37', NULL, 0, NULL, NULL, NULL, NULL, 0),
(163, '', 138, 'Test china time2', 'Normal', 'Antivirus or malware infection', 'test China time', 'starter', 'open', 'Normal', '2025-06-04 03:06:05', '2025-06-04 03:06:05', NULL, 0, NULL, NULL, NULL, NULL, 0),
(164, '', 139, 'this is brazil', 'Normal', 'Login issues (Windows/Mac)', 'hello admin this is brazil', 'starter', 'open', 'Normal', '2025-06-04 03:11:23', '2025-06-04 03:11:23', NULL, 0, NULL, NULL, NULL, NULL, 0),
(166, '', 142, 'Test chat time', 'Normal', 'Other', 'test', 'starter', 'open', 'Normal', '2025-06-04 11:19:26', '2025-06-04 11:19:26', NULL, 0, NULL, NULL, NULL, NULL, 0),
(167, '', 143, 'This is testing send message', 'Normal', 'OS boot failure', 'hihihi', 'premium', 'open', 'Normal', '2025-06-05 02:23:02', '2025-06-05 02:23:02', NULL, 0, NULL, NULL, NULL, NULL, 0),
(168, '', 143, 'Test notify', 'Normal', 'OS boot failure', 'hi admin', 'starter', 'open', 'Normal', '2025-06-05 02:27:38', '2025-06-05 02:27:38', NULL, 0, NULL, NULL, NULL, NULL, 0),
(169, '', 143, 'Test Notify2', 'Normal', 'OS boot failure', 'hihi', 'starter', 'open', 'Normal', '2025-06-05 02:28:53', '2025-06-05 02:28:53', NULL, 0, NULL, NULL, NULL, NULL, 0),
(170, '', 143, 'test3 notify', 'Normal', 'Email Problem', 'test', 'starter', 'open', 'Normal', '2025-06-05 02:30:05', '2025-06-05 02:30:05', NULL, 0, NULL, NULL, NULL, NULL, 0),
(171, '', 143, 'This is testing send message4', 'Normal', 'Billing/Finance', 'test', 'starter', 'open', 'Normal', '2025-06-05 02:30:37', '2025-06-05 02:30:37', NULL, 0, NULL, NULL, NULL, NULL, 0),
(172, '', 143, 'notify test2', 'Normal', 'Slow performance', 'test', 'starter', 'open', 'Normal', '2025-06-05 02:34:35', '2025-06-05 02:34:35', NULL, 0, NULL, NULL, NULL, NULL, 0),
(173, '', 143, 'notify test2', 'Normal', 'Slow performance', 'test', 'starter', 'open', 'Normal', '2025-06-05 02:34:47', '2025-06-05 02:34:47', NULL, 0, NULL, NULL, NULL, NULL, 0),
(174, '', 143, 'This is testing send message', 'Normal', 'Blue screen / system crash', 'kk', 'starter', 'open', 'Normal', '2025-06-05 02:34:59', '2025-06-05 02:34:59', NULL, 0, NULL, NULL, NULL, NULL, 0),
(175, '', 143, 'This is testing send message', 'Normal', 'Slow performance', 'hihi', 'starter', 'open', 'Normal', '2025-06-05 02:36:26', '2025-06-05 02:36:26', NULL, 0, NULL, NULL, NULL, NULL, 0),
(176, '', 143, 'This is testing send message', 'Normal', 'Other', 'test', 'starter', 'open', 'Normal', '2025-06-05 02:38:24', '2025-06-05 02:38:24', NULL, 0, NULL, NULL, NULL, NULL, 0),
(177, '', 150, 'Test chat MyanMar time', 'Normal', 'Website Error', 'hihihi', 'starter', 'in_progress', 'Normal', '2025-06-05 10:39:08', '2025-06-05 10:51:55', NULL, 0, NULL, NULL, NULL, NULL, 0),
(178, '', 150, 'Test Color', 'Normal', 'Other', 'Hello admin', 'premium', 'in_progress', 'Normal', '2025-06-05 11:25:09', '2025-06-06 10:33:13', NULL, 1, NULL, NULL, NULL, NULL, 0),
(179, '', 148, 'test', 'Normal', 'Slow performance', 'Hello Admin', 'starter', 'open', 'Normal', '2025-06-06 07:23:56', '2025-06-06 11:09:42', NULL, 1, NULL, NULL, NULL, NULL, 0),
(180, '', 168, 'Has Login Issue to Microsoft Account', 'Normal', 'Account Access', 'Hello Support Team,\r\nI recently tried to reset my password using the \"Forgot Password\" option. I received the reset email, successfully changed the password, but now when I try to log in, I keep getting the error message: \"Invalid credentials.\"\r\nI’ve already tried:\r\n1. Clearing my browser cache\r\n2. Trying a different browser\r\n3. Resetting the password again\r\nCould you please assist me in regaining access to my account as soon as possible?\r\nThank you for your help.', 'starter', 'open', 'Normal', '2025-06-10 03:36:48', '2025-06-10 03:36:48', NULL, 0, NULL, NULL, NULL, NULL, 0),
(181, '', 168, 'App Crashes on Launch (Android)', 'Normal', 'App crashes on mobile', 'Hi Support Team,\r\nEvery time I try to open the app, it crashes immediately after the splash screen. I’ve already tried restarting my phone and reinstalling the app, but the problem persists.\r\nCould you please help me troubleshoot this?\r\nThank you.', 'premium', 'closed', 'Normal', '2025-06-10 03:42:06', '2025-06-10 03:45:41', NULL, 0, NULL, NULL, NULL, NULL, 0),
(182, '', 168, 'Website Down - 500 Internal Server Error', 'Normal', 'Server Down', 'Hello Support,\r\nOur website (globalitpartner.com) has been showing a 500 Internal Server Error since this morning (June 10, 2025, ~8:30 AM EST). We rely on this site for client access, so this is a critical issue for us.\r\nSteps already taken:\r\nChecked server resource usage (CPU/memory looks normal)\r\nRestarted the web server (Apache)\r\nNo recent code deployments on our end\r\nCould you please investigate the issue and advise on next steps or provide logs for further debugging?\r\nWe’d appreciate urgent assistance on this matter.\r\nThank you', 'ultimate', 'resolved', 'Normal', '2025-06-10 03:44:05', '2025-06-10 03:45:10', NULL, 1, NULL, NULL, NULL, NULL, 0),
(208, '', 34, 'Test Appika', 'Normal', NULL, 'Hi, This is testing Appika', 'premium', 'open', 'Normal', '2025-06-17 06:59:05', '2025-06-17 06:59:05', NULL, 0, NULL, NULL, NULL, NULL, 0),
(209, '138', 34, 'This is Appika testing', 'Normal', NULL, 'hello this is test', 'ultimate', 'open', 'Normal', '2025-06-17 07:10:45', '2025-06-17 07:10:45', NULL, 0, NULL, NULL, NULL, NULL, 0),
(210, '139', 34, 'Appika contact id and desc is missing', 'Normal', NULL, 'hihihihihihihi', 'ultimate', 'open', 'Normal', '2025-06-17 07:16:53', '2025-06-17 07:16:53', NULL, 0, NULL, NULL, NULL, NULL, 0),
(211, '140', 34, 'Test hello it appika ', 'Normal', NULL, 'hihihihi this is testing', 'ultimate', 'open', 'low', '2025-06-17 07:23:28', '2025-06-17 07:57:31', NULL, 1, NULL, NULL, '2025-06-17 07:57:31', 'appika_admin', 0),
(212, '141', 34, 'This is testing appika description', 'Normal', NULL, 'hihihi this is testing', 'starter', 'open', 'low', '2025-06-17 07:58:34', '2025-06-17 08:53:12', NULL, 1, NULL, NULL, '2025-06-17 08:53:12', 'appika_admin', 0),
(213, '142', 34, 'HelloIT Appika test update', 'Normal', NULL, 'Hi this is testing', 'premium', 'in_progress', 'critical', '2025-06-17 08:54:11', '2025-06-17 09:23:36', NULL, 1, NULL, NULL, '2025-06-17 09:23:36', 'appika_admin', 0),
(214, '143', 34, 'test desc', 'Normal', NULL, 'hello this is test', 'premium', 'open', 'low', '2025-06-17 09:00:29', '2025-06-17 09:11:51', NULL, 1, NULL, NULL, '2025-06-17 09:11:51', 'appika_admin', 0),
(215, '144', 34, 'Show Desc please ', 'Normal', NULL, 'HelloIT desc show ', 'premium', 'open', 'low', '2025-06-17 09:28:34', '2025-06-17 10:15:24', NULL, 1, NULL, NULL, '2025-06-17 10:15:24', 'appika_admin', 0),
(216, '153', 34, 'Pls add desc to Appika', 'Normal', NULL, 'im begginggggg', 'premium', 'open', 'Normal', '2025-06-17 10:17:31', '2025-06-17 10:17:38', NULL, 1, NULL, NULL, NULL, NULL, 0),
(217, '157', 34, '5:52 test create ticket', 'Normal', NULL, 'hello this is description', 'starter', 'open', 'Normal', '2025-06-17 10:52:52', '2025-06-17 10:53:04', NULL, 1, NULL, NULL, NULL, NULL, 0),
(218, '', 34, 'Test show description appika1', 'Normal', NULL, 'hello this is testing ', 'ultimate', 'open', 'Normal', '2025-06-17 11:23:24', '2025-06-17 11:23:24', NULL, 0, NULL, NULL, NULL, NULL, 0),
(219, '', 34, 'test ticket desc2', 'Normal', NULL, 'hi pls add to desc plss', 'ultimate', 'open', 'Normal', '2025-06-17 11:31:45', '2025-06-17 11:31:45', NULL, 0, NULL, NULL, NULL, NULL, 0),
(220, '161', 180, 'HelloIT', 'Normal', NULL, 'hihihihihi', 'starter', 'open', 'Normal', '2025-06-18 03:59:31', '2025-06-18 03:59:56', NULL, 1, NULL, NULL, NULL, NULL, 0),
(221, '163', 180, 'HelloIT desc', 'Normal', NULL, 'this is desc of appika ', 'starter', 'open', 'Normal', '2025-06-18 04:12:24', '2025-06-18 04:12:32', NULL, 1, NULL, NULL, NULL, NULL, 0),
(222, '165', 180, 'desc', 'Normal', NULL, 'hihihihi this is desc', 'starter', 'open', 'Normal', '2025-06-18 04:24:14', '2025-06-18 04:24:26', NULL, 1, NULL, NULL, NULL, NULL, 0),
(223, '166', 180, 'HelloIT desc2', 'Normal', NULL, 'hello this is ', 'starter', 'open', 'Normal', '2025-06-18 04:33:58', '2025-06-18 04:34:20', NULL, 1, NULL, NULL, NULL, NULL, 0),
(224, '167', 180, 'HelloIT desc3', 'Normal', NULL, 'Hihi this is testing helloit', 'ultimate', 'open', 'Normal', '2025-06-18 04:48:11', '2025-06-18 04:48:22', NULL, 1, NULL, NULL, NULL, NULL, 0),
(225, '169', 180, 'This is test3', 'Normal', NULL, 'bla blabla helloit desc', 'ultimate', 'open', 'low', '2025-06-18 04:58:12', '2025-06-18 07:05:44', NULL, 1, NULL, NULL, '2025-06-18 07:05:44', 'appika_admin', 0),
(227, '185', 181, 'Test GraphQL Ticket', 'Normal', NULL, 'hihihi', 'starter', 'open', 'Normal', '2025-06-18 07:04:10', '2025-06-18 07:04:10', NULL, 0, NULL, NULL, NULL, NULL, 0),
(228, '186', 181, 'Test GraphQL Ticket2', 'Normal', NULL, 'hihihihi this is desc', 'starter', 'open', 'Normal', '2025-06-18 07:12:35', '2025-06-18 07:12:35', NULL, 0, NULL, NULL, NULL, NULL, 0),
(229, '', 181, 'Test GraphQL Ticket3', 'Normal', NULL, 'helloit test', 'starter', 'open', 'Normal', '2025-06-18 07:16:45', '2025-06-18 07:16:45', NULL, 0, NULL, NULL, NULL, NULL, 0),
(230, '187', 181, 'Test GraphQL Ticket4', 'Normal', NULL, 'hihihi this is tst', 'starter', 'open', 'Normal', '2025-06-18 07:36:37', '2025-06-18 07:37:51', NULL, 0, NULL, NULL, NULL, NULL, 0),
(231, '189', 181, 'Test GraphQL Ticket5', 'Normal', NULL, 'hihihi pls show desc', 'starter', 'open', 'Normal', '2025-06-18 07:44:54', '2025-06-18 07:45:08', NULL, 1, NULL, NULL, NULL, NULL, 0),
(232, '191', 181, 'HelloIT desc', 'Normal', NULL, 'test test this is desc', 'ultimate', 'open', 'Normal', '2025-06-18 07:52:00', '2025-06-18 07:52:00', NULL, 0, NULL, NULL, NULL, NULL, 0),
(233, '192', 181, 'hello IT pls show desc', 'Normal', NULL, 'pls have desc and contact id', 'premium', 'open', 'Normal', '2025-06-18 07:57:25', '2025-06-18 07:57:25', NULL, 0, NULL, NULL, NULL, NULL, 0),
(234, '193', 181, 'Test GraphQL Ticket', 'Normal', NULL, 'hi', 'premium', 'open', 'Normal', '2025-06-18 08:01:19', '2025-06-18 08:01:19', NULL, 0, NULL, NULL, NULL, NULL, 0),
(235, '', 181, 'Test GraphQL Ticket2', 'Normal', NULL, 'hihi', 'premium', 'open', 'Normal', '2025-06-18 08:06:08', '2025-06-18 08:06:37', NULL, 1, NULL, NULL, NULL, NULL, 0),
(236, '', 181, 'Test GraphQL Ticket99', 'Normal', NULL, 'hello', 'ultimate', 'open', 'Normal', '2025-06-18 08:15:10', '2025-06-18 08:15:10', NULL, 0, NULL, NULL, NULL, NULL, 0),
(237, '', 181, 'HelloIT 3:32PM', 'Normal', NULL, 'HiHi this is desc', 'ultimate', 'open', 'Normal', '2025-06-18 08:23:45', '2025-06-18 08:23:56', NULL, 1, NULL, NULL, NULL, NULL, 0),
(238, '', 181, 'HelloIT desc 3.28pm', 'Normal', NULL, 'hello is desc', 'starter', 'open', 'Normal', '2025-06-18 08:28:44', '2025-06-18 08:29:10', NULL, 1, NULL, NULL, NULL, NULL, 0),
(239, '', 181, 'ggggg', 'Normal', NULL, 'hello it test', 'premium', 'open', 'Normal', '2025-06-18 08:32:30', '2025-06-18 08:32:30', NULL, 0, NULL, NULL, NULL, NULL, 0),
(240, '196', 181, 'Test GraphQL Ticket', 'Information', 'Website Error', 'hhh', 'premium', 'open', 'Information', '2025-06-18 08:36:16', '2025-06-18 08:37:10', NULL, 1, NULL, NULL, NULL, NULL, 0),
(241, '197', 181, 'HelloIT help me', 'Normal', NULL, 'hihihihihi', 'starter', 'open', 'low', '2025-06-18 08:38:29', '2025-06-18 08:53:56', NULL, 1, NULL, NULL, '2025-06-18 08:53:56', 'appika_admin', 0),
(242, '201', 181, 'HelloIT graphql999 last ', 'Normal', NULL, 'test this is desc', 'ultimate', 'in_progress', 'high', '2025-06-18 09:43:42', '2025-06-19 07:37:23', NULL, 1, NULL, NULL, '2025-06-19 07:37:23', 'appika_admin', 0),
(243, '200', 181, 'helloIT revert', 'Normal', NULL, 'hello it desc', 'ultimate', 'open', 'low', '2025-06-18 09:49:19', '2025-06-18 11:09:46', NULL, 1, NULL, NULL, '2025-06-18 11:09:46', 'appika_admin', 0),
(248, '210', 185, 'HelloIT dev69 ticket1', 'Normal', NULL, 'Hi i have problem with my pc \r\npls help me', 'ultimate', 'open', 'Normal', '2025-06-23 03:43:41', '2025-06-23 03:44:01', NULL, 1, NULL, NULL, NULL, NULL, 0),
(249, '211', 185, 'HelloIT Appika 211', 'Normal', NULL, 'HelloIT this is Appika ticket\r\npls help me \r\ni have so many problem \r\nwith my ticket\r\nand IT issues', 'ultimate', 'open', 'Normal', '2025-06-23 03:53:04', '2025-06-23 03:53:04', NULL, 0, NULL, NULL, NULL, NULL, 0),
(250, '212', 185, 'HelloIT text fix desc', 'Normal', NULL, 'hi\r\nhiiii\r\nhiiiiiiii\r\nthis is space\r\nand break to new line\r\nyayyy', 'ultimate', 'open', 'Normal', '2025-06-23 03:56:31', '2025-06-23 03:56:31', NULL, 0, NULL, NULL, NULL, NULL, 0),
(251, '213', 185, 'helloit desc2', 'Normal', NULL, 'aaaaaa\r\nbbbbb\r\ncccccc\r\nddddd\r\nfffffffff\r\nggggg\r\nhhhh\r\niiiiiii\r\njjjjjjj', 'ultimate', 'open', 'Normal', '2025-06-23 03:58:56', '2025-06-23 03:58:56', NULL, 0, NULL, NULL, NULL, NULL, 0),
(252, '214', 185, 'test priority', 'Normal', NULL, 'hihihihi\r\nheyyyyyy\r\nhelloit\r\nhalooo', 'ultimate', 'open', 'Normal', '2025-06-23 04:18:29', '2025-06-23 06:28:26', NULL, 1, NULL, NULL, NULL, NULL, 0),
(253, '215', 185, 'Starter show priority ', 'Normal', NULL, 'testtt\r\nhelloit\r\nyeyy\r\nmafff\r\nthis is description', 'starter', 'closed', 'Important', '2025-06-23 04:22:39', '2025-06-23 06:37:34', NULL, 1, NULL, NULL, '2025-06-23 06:37:34', 'appika_admin', 0),
(254, '217', 185, 'helloIT test auto snyc', 'Normal', NULL, 'hihihihi\r\naaaa\r\nssss\r\nddddd\r\nffff\r\nfggg', 'ultimate', 'closed', 'Important', '2025-06-23 06:42:01', '2025-06-23 06:45:16', NULL, 1, NULL, NULL, '2025-06-23 06:45:16', 'appika_admin', 0),
(255, '218', 185, 'Test sync auto update WIP', 'Critical', NULL, 'hihihihihi', 'ultimate', 'closed', 'Critical', '2025-06-23 06:48:28', '2025-06-23 07:33:14', NULL, 1, NULL, '2025-06-23 07:09:31', '2025-06-23 07:33:14', 'appika_admin', 0),
(256, '220', 185, 'helloIT test 23/6-1', 'Normal', NULL, 'hihihihihihi\r\nthis is test hello it\r\nio', 'starter', 'open', 'Normal', '2025-06-23 08:50:49', '2025-06-23 08:50:49', NULL, 0, NULL, NULL, NULL, NULL, 0),
(257, '221', 185, 'HelloIT 23-6-2', 'Normal', NULL, 'HelloIT desc', 'ultimate', 'open', 'Normal', '2025-06-23 09:35:01', '2025-06-23 09:35:01', NULL, 0, NULL, NULL, NULL, NULL, 0),
(258, '222', 185, 'HelloIT 23-6-3', 'Normal', NULL, 'Test \r\nexp\r\nticket', 'starter', 'open', 'Normal', '2025-06-23 09:40:18', '2025-06-23 09:40:18', NULL, 0, NULL, NULL, NULL, NULL, 0),
(259, '223', 186, 'Test GraphQL Ticket', 'Normal', NULL, 'hihihi', 'starter', 'open', 'Normal', '2025-06-24 03:54:06', '2025-06-24 03:54:06', NULL, 0, NULL, NULL, NULL, NULL, 0),
(260, '224', 186, 'Test GraphQL Ticket', 'Normal', NULL, 'hihihihi', 'starter', 'open', 'Normal', '2025-06-24 04:02:48', '2025-06-24 04:02:48', NULL, 0, NULL, NULL, NULL, NULL, 0),
(261, '225', 186, 'Test GraphQL Ticket3', 'Normal', NULL, 'helloit\r\ntest\r\nhehehe\r\ntesttttt', 'starter', 'open', 'Normal', '2025-06-24 04:06:08', '2025-06-24 04:06:08', NULL, 0, NULL, NULL, NULL, NULL, 0),
(262, '226', 186, 'Test GraphQL Ticket4', 'Normal', NULL, 'hihihihi', 'starter', 'open', 'Normal', '2025-06-24 04:14:54', '2025-06-24 04:14:54', NULL, 0, NULL, NULL, NULL, NULL, 0),
(263, '227', 186, 'HelloIT desc', 'Normal', NULL, 'helloit', 'starter', 'open', 'Normal', '2025-06-24 04:20:22', '2025-06-24 04:20:22', NULL, 0, NULL, NULL, NULL, NULL, 0),
(264, '228', 186, 'Test GraphQL Ticket2', 'Normal', NULL, 'hihihihi', 'ultimate', 'open', 'Normal', '2025-06-24 04:23:52', '2025-06-24 04:23:52', NULL, 0, NULL, NULL, NULL, NULL, 0),
(265, '229', 186, 'Test GraphQL Ticket', 'Normal', NULL, 'hi\r\nhi\r\nhi', 'premium', 'open', 'Normal', '2025-06-24 06:30:26', '2025-06-24 06:30:26', NULL, 0, NULL, NULL, NULL, NULL, 0),
(266, '230', 186, 'Test GraphQL Ticket', 'Normal', NULL, 'hi', 'starter', 'open', 'Normal', '2025-06-24 06:50:23', '2025-06-24 06:50:23', NULL, 0, NULL, NULL, NULL, NULL, 0);

-- --------------------------------------------------------

--
-- Table structure for table `sync_log`
--

CREATE TABLE `sync_log` (
  `id` int(11) NOT NULL,
  `sync_id` int(11) NOT NULL,
  `action` varchar(100) NOT NULL,
  `message` text NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `tickets`
--

CREATE TABLE `tickets` (
  `ticketid` int(11) NOT NULL,
  `ticket_type` varchar(20) NOT NULL,
  `info` text NOT NULL,
  `package_size` varchar(10) NOT NULL,
  `numbers_per_package` int(11) NOT NULL,
  `dollar_price_per_package` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

--
-- Dumping data for table `tickets`
--

INSERT INTO `tickets` (`ticketid`, `ticket_type`, `info`, `package_size`, `numbers_per_package`, `dollar_price_per_package`) VALUES
(1001, 'STARTER', '', 'XS', 5, 90),
(1002, 'STARTER', '', 'S', 10, 150),
(1003, 'STARTER', '', 'M', 25, 350),
(1004, 'STARTER', '', 'L', 50, 650),
(1005, 'STARTER', '', 'XL', 100, 1200),
(1006, 'STARTER', '', '2XL', 250, 2750),
(2001, 'BUSINESS', '', 'XS', 5, 115),
(2002, 'BUSINESS', '', 'S', 10, 200),
(2003, 'BUSINESS', '', 'M', 25, 450),
(2004, 'BUSINESS', '', 'L', 50, 800),
(2005, 'BUSINESS', '', 'XL', 100, 1500),
(2006, 'BUSINESS', '', '2XL', 250, 3500),
(3001, 'ULTIMATE', '', 'XS', 5, 165),
(3002, 'ULTIMATE', '', 'S', 10, 300),
(3003, 'ULTIMATE', '', 'M', 25, 700),
(3004, 'ULTIMATE', '', 'L', 50, 1300),
(3005, 'ULTIMATE', '', 'XL', 100, 2400),
(3006, 'ULTIMATE', '', '2XL', 250, 5500);

-- --------------------------------------------------------

--
-- Table structure for table `ticket_expiration_log`
--

CREATE TABLE `ticket_expiration_log` (
  `id` int(11) NOT NULL,
  `username` varchar(100) NOT NULL,
  `ticket_type` varchar(50) NOT NULL,
  `expired_quantity` int(11) NOT NULL,
  `purchase_date` datetime NOT NULL,
  `expiration_date` datetime NOT NULL,
  `cleanup_date` timestamp NOT NULL DEFAULT current_timestamp(),
  `transaction_id` varchar(100) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `ticket_expiration_log`
--

INSERT INTO `ticket_expiration_log` (`id`, `username`, `ticket_type`, `expired_quantity`, `purchase_date`, `expiration_date`, `cleanup_date`, `transaction_id`) VALUES
(1, 'HC185', 'starter', 5, '2023-12-23 09:47:19', '2024-12-23 09:47:19', '2025-06-23 09:48:48', '0'),
(2, 'HC185', 'ultimate', 3, '2024-05-23 09:47:19', '2025-05-23 09:47:19', '2025-06-23 09:48:48', '0'),
(3, 'HC185', 'starter', 5, '2023-06-01 10:00:00', '2024-06-01 10:00:00', '2025-06-23 12:00:00', 'TEST_EXP_001'),
(4, 'HC185', 'ultimate', 3, '2023-08-15 14:30:00', '2024-08-15 14:30:00', '2025-06-23 11:30:00', 'TEST_EXP_002'),
(5, 'HC185', 'starter', 5, '2023-06-01 10:00:00', '2024-06-01 10:00:00', '2025-06-23 12:00:00', 'TEST_EXP_001'),
(6, 'HC185', 'ultimate', 3, '2023-08-15 14:30:00', '2024-08-15 14:30:00', '2025-06-23 11:30:00', 'TEST_EXP_002'),
(7, 'HC185', 'premium', 2, '2023-09-10 16:45:00', '2024-09-10 16:45:00', '2025-06-23 10:15:00', 'TEST_EXP_003'),
(8, 'dev01', 'BUSINESS', 5, '2025-06-09 03:44:09', '2025-06-09 03:46:09', '2025-06-24 02:45:43', 'cs_test_a161EJJiGcttPSlh4BLB6dV7OCrtYcMf32YC6ivSbyiMQFT70qzsh5ZcYb'),
(9, 'dev01', 'STARTER', 25, '2025-06-09 03:29:34', '2025-06-09 03:31:34', '2025-06-24 02:45:43', 'cs_test_b1SkkkTgGVmPWJBeUDBuakRJ7GQbsesoHimr2vDIgqjnZ7H2tgm9Tkg4MQ'),
(10, 'dev01', 'STARTER', 10, '2025-06-09 03:29:34', '2025-06-09 03:31:34', '2025-06-24 02:45:43', 'cs_test_b1SkkkTgGVmPWJBeUDBuakRJ7GQbsesoHimr2vDIgqjnZ7H2tgm9Tkg4MQ'),
(11, 'dev01', 'STARTER', 5, '2025-06-09 04:03:20', '2025-06-09 04:05:20', '2025-06-24 02:45:43', 'cs_test_a1wznvzDhBqqPJe7tah3w2C3JAmN3A3OJ9zuSzG4tits49RUpoqrzSEaWf'),
(12, 'dev01', 'STARTER', 10, '2025-06-09 04:28:21', '2025-06-09 04:30:21', '2025-06-24 02:45:43', 'cs_test_b12wiDhKHFDFHpYonJzgb7oDAZNskTVbIRYwEnObZhoWO9ObZtIiZ3DBSO'),
(13, 'dev01', 'STARTER', 5, '2025-06-09 04:28:21', '2025-06-09 04:30:21', '2025-06-24 02:45:43', 'cs_test_b12wiDhKHFDFHpYonJzgb7oDAZNskTVbIRYwEnObZhoWO9ObZtIiZ3DBSO'),
(14, 'dev01', 'ULTIMATE', 5, '2025-06-09 03:47:15', '2025-06-09 03:49:15', '2025-06-24 02:45:43', 'cs_test_a1HjM8pTjFyMM5DBX7xy5zsLcLTgKVAzmO1USyzRYdwkQFislMVJokdvNK'),
(15, 'dev01', 'ULTIMATE', 5, '2025-06-09 03:52:58', '2025-06-09 03:54:58', '2025-06-24 02:45:43', 'cs_test_a1c50fHcJJjcLFAG5qhmBMVSX20uYpFnXVhHqdb0xFVL5xzoBuu6Yq4bKB'),
(16, 'dev02', 'BUSINESS', 5, '2025-06-09 04:38:27', '2025-06-09 04:40:27', '2025-06-24 02:45:43', 'cs_test_b1nWQNqohkvPYkLXt91ZJ4nMKGWmCeBadZ79wnJ3kdDLLSIRAxITBg9L0v'),
(17, 'dev02', 'STARTER', 5, '2025-06-09 04:38:27', '2025-06-09 04:40:27', '2025-06-24 02:45:43', 'cs_test_b1nWQNqohkvPYkLXt91ZJ4nMKGWmCeBadZ79wnJ3kdDLLSIRAxITBg9L0v'),
(18, 'HC170', 'STARTER', 5, '2025-06-11 07:05:05', '2025-06-11 07:07:05', '2025-06-24 02:45:43', 'cs_test_a1QXC42cxcSP83RKBfJT1wsGDHmwaPyBqqmBf3qmoa9cyY4TFuJMpOkH04'),
(19, 'HC171', 'STARTER', 5, '2025-06-11 07:24:57', '2025-06-11 07:26:57', '2025-06-24 02:45:43', 'cs_test_a16QT5xIYily22sSLHX98TU7uAlWFpUtI9vn9eXVRBVopEJuhvZ1SKveZA'),
(20, 'HC172', 'STARTER', 5, '2025-06-11 07:31:10', '2025-06-11 07:33:10', '2025-06-24 02:45:43', 'cs_test_a1LcdO5h5NMxD1mAocFwAZ8lRvjToVLhokaUBhyO0jbMYa2raggTiUdpOV'),
(21, 'HC173', 'STARTER', 5, '2025-06-11 07:36:01', '2025-06-11 07:38:01', '2025-06-24 02:45:43', 'cs_test_a1FHhxQi4r6jumv26DQQeouz28GgBqmIUKTCVQK5VEWEE0blvkGSFyarq5'),
(22, 'HC174', 'STARTER', 5, '2025-06-11 08:18:25', '2025-06-11 08:20:25', '2025-06-24 02:45:43', 'cs_test_a1Ag9j2AvOWEyThfRDO9DgpiQVGgxq7bwzHg90961xXMya5Vt1oHVYvv93'),
(23, 'HC175', 'BUSINESS', 5, '2025-06-12 03:59:37', '2025-06-12 04:01:37', '2025-06-24 02:45:43', 'pi_3RZ2IcEJRyUMDOj50ytFOFC7'),
(24, 'HC175', 'BUSINESS', 5, '2025-06-12 04:07:38', '2025-06-12 04:09:38', '2025-06-24 02:45:43', 'cs_test_a1nBegfg09YUbCqbVwVKJE6WRWkry6YT3LBvhuil1ORIjpeayqKKqq7Hro'),
(25, 'HC175', 'BUSINESS', 5, '2025-06-12 05:00:28', '2025-06-12 05:02:28', '2025-06-24 02:45:43', 'pi_3RZ3FVEJRyUMDOj51cJi6lPk'),
(26, 'HC175', 'BUSINESS', 10, '2025-06-12 05:00:28', '2025-06-12 05:02:28', '2025-06-24 02:45:43', 'pi_3RZ3FVEJRyUMDOj51cJi6lPk'),
(27, 'HC175', 'STARTER', 5, '2025-06-12 03:58:18', '2025-06-12 04:00:18', '2025-06-24 02:45:43', 'cs_test_a1RPwpLMoV6OmZjNYYGQ7lJZTY6iPWkwXvC1LAguGbdurMPoXR8Dyzq87m'),
(28, 'HC175', 'STARTER', 10, '2025-06-12 03:59:37', '2025-06-12 04:01:37', '2025-06-24 02:45:43', 'pi_3RZ2IcEJRyUMDOj50ytFOFC7'),
(29, 'HC175', 'STARTER', 5, '2025-06-12 04:00:31', '2025-06-12 04:02:31', '2025-06-24 02:45:43', 'cs_test_a1M3fL84Nnpd3F8ROVQcRlWPxD7FDUDClcZA1i9UlPBAcyLSyqBODPElbh'),
(30, 'HC175', 'STARTER', 10, '2025-06-12 05:00:28', '2025-06-12 05:02:28', '2025-06-24 02:45:43', 'pi_3RZ3FVEJRyUMDOj51cJi6lPk'),
(31, 'HC175', 'STARTER', 5, '2025-06-12 05:00:28', '2025-06-12 05:02:28', '2025-06-24 02:45:43', 'pi_3RZ3FVEJRyUMDOj51cJi6lPk'),
(32, 'HC175', 'ULTIMATE', 5, '2025-06-12 03:59:37', '2025-06-12 04:01:37', '2025-06-24 02:45:43', 'pi_3RZ2IcEJRyUMDOj50ytFOFC7'),
(33, 'HC175', 'ULTIMATE', 5, '2025-06-12 04:08:24', '2025-06-12 04:10:24', '2025-06-24 02:45:43', 'cs_test_a1GrxJZnmIO2qBa7wafd6sTN1n6WhTdWCD4G7QgfZZxeCuHK8hvUznOtbt'),
(34, 'HC175', 'ULTIMATE', 10, '2025-06-12 05:00:28', '2025-06-12 05:02:28', '2025-06-24 02:45:43', 'pi_3RZ3FVEJRyUMDOj51cJi6lPk'),
(35, 'HC175', 'ULTIMATE', 5, '2025-06-12 05:00:28', '2025-06-12 05:02:28', '2025-06-24 02:45:43', 'pi_3RZ3FVEJRyUMDOj51cJi6lPk'),
(36, 'HC176', 'STARTER', 5, '2025-06-12 10:47:32', '2025-06-12 10:49:32', '2025-06-24 02:45:43', 'cs_test_a1pUidK1zOQpRdaG95xQp8S19EcBbJ1NwZzNwubnBuhf9ZCgeCKwkQgAwT'),
(37, 'HC176', 'STARTER', 5, '2025-06-12 10:55:36', '2025-06-12 10:57:36', '2025-06-24 02:45:43', 'pi_3RZ8nAEJRyUMDOj51hRFm2H5'),
(38, 'HC177', 'STARTER', 5, '2025-06-13 08:18:45', '2025-06-13 08:20:45', '2025-06-24 02:45:43', 'cs_test_a1IUAYROMpoEdAizta9OpDqk0RFs5BkApqaUF0JTeB9WwXC9ROIQCmejY6'),
(39, 'HC178', 'STARTER', 5, '2025-06-13 08:34:23', '2025-06-13 08:36:23', '2025-06-24 02:45:43', 'cs_test_a141x1idIBUGIg4xGIaymY5Zynu6zmIJZm6WKBTuC28mYDZK77jv0SnUh5'),
(40, 'HC179', 'STARTER', 5, '2025-06-13 08:43:48', '2025-06-13 08:45:48', '2025-06-24 02:45:43', 'cs_test_a1sYWypUqFNVTz1Nq8X3eyOYOgh0pnxSdnWLJnjpAbFZqxU755BIxidH3z'),
(41, 'HC180', 'STARTER', 5, '2025-06-18 03:56:03', '2025-06-18 03:58:03', '2025-06-24 02:45:43', 'cs_test_a1osXCBlP1gSqrLK7NCz7N135i9CNxjltNY4FqDMrO7sMr7KgGl5WaSONC'),
(42, 'HC181', 'STARTER', 5, '2025-06-18 06:43:07', '2025-06-18 06:45:07', '2025-06-24 02:45:43', 'cs_test_a1XpUm5VuUOLt3moqGDQCRZQGDj34okFVHLwQUniKeA2QnTPu0JXAbWL3D'),
(43, 'HC182', 'STARTER', 5, '2025-06-18 10:15:46', '2025-06-18 10:17:46', '2025-06-24 02:45:43', 'cs_test_a1OM9rnvXejU1GiuIIK1Jt7qWpNzD3Z4PYTRLfSEMEFNpXTOsLdDordQcp'),
(44, 'HC183', 'STARTER', 5, '2025-06-18 10:28:44', '2025-06-18 10:30:44', '2025-06-24 02:45:43', 'cs_test_a1BMPcCoaZRnUGwkZoHxnluO01vGzF86ggTIa8K9GkuJnQkBtCZWpiNjgB'),
(45, 'HC184', 'BUSINESS', 10, '2025-06-20 03:39:54', '2025-06-20 03:41:54', '2025-06-24 02:45:43', 'cs_test_b16w4rlJgSIdDNoP8W6UWbWuYKae3MpHAImPjmM2JbfpLh97SDdnZ6EYHF'),
(46, 'HC184', 'STARTER', 5, '2025-06-20 03:39:54', '2025-06-20 03:41:54', '2025-06-24 02:45:43', 'cs_test_b16w4rlJgSIdDNoP8W6UWbWuYKae3MpHAImPjmM2JbfpLh97SDdnZ6EYHF'),
(47, 'HC184', 'ULTIMATE', 5, '2025-06-20 03:39:54', '2025-06-20 03:41:54', '2025-06-24 02:45:43', 'cs_test_b16w4rlJgSIdDNoP8W6UWbWuYKae3MpHAImPjmM2JbfpLh97SDdnZ6EYHF'),
(48, 'HC185', 'BUSINESS', 5, '2025-06-23 11:19:34', '2025-06-23 11:21:34', '2025-06-24 02:45:43', 'pi_3Rd8PQEJRyUMDOj50drfLy3v'),
(49, 'HC185', 'starter', 2, '2024-07-02 09:47:19', '2024-07-02 09:49:19', '2025-06-24 02:45:43', '0'),
(50, 'HC185', 'starter', 10, '2024-12-23 09:47:19', '2024-12-23 09:49:19', '2025-06-24 02:45:43', '0'),
(51, 'HC185', 'STARTER', 4, '2025-06-23 03:42:57', '2025-06-23 03:44:57', '2025-06-24 02:45:43', 'cs_test_b1IdPP6DGQSej9LERdD41kORnjHfOAXtdE08j2JFj8zHYcuPeYlfDbKUtk'),
(52, 'HC185', 'ultimate', 4, '2024-07-23 09:47:19', '2024-07-23 09:49:19', '2025-06-24 02:45:43', '0'),
(53, 'HC185', 'ULTIMATE', 9, '2025-06-23 03:42:57', '2025-06-23 03:44:57', '2025-06-24 02:45:43', 'cs_test_b1IdPP6DGQSej9LERdD41kORnjHfOAXtdE08j2JFj8zHYcuPeYlfDbKUtk'),
(54, 'HC186', 'BUSINESS', 5, '2025-06-24 02:08:45', '2025-06-24 02:10:45', '2025-06-24 02:45:43', 'cs_test_b17XOnY9tsF3bqWrO6YXvF4XGPO3tbaTqlzsyaf45bCruEelg6ANcCWjG2'),
(55, 'HC186', 'BUSINESS', 5, '2025-06-24 02:19:18', '2025-06-24 02:21:18', '2025-06-24 02:45:43', 'pi_3RdMS8EJRyUMDOj508Q3vDt7'),
(56, 'HC186', 'STARTER', 5, '2025-06-24 02:08:45', '2025-06-24 02:10:45', '2025-06-24 02:45:43', 'cs_test_b17XOnY9tsF3bqWrO6YXvF4XGPO3tbaTqlzsyaf45bCruEelg6ANcCWjG2'),
(57, 'HC186', 'STARTER', 5, '2025-06-24 02:19:18', '2025-06-24 02:21:18', '2025-06-24 02:45:43', 'pi_3RdMS8EJRyUMDOj508Q3vDt7'),
(58, 'HC186', 'ULTIMATE', 5, '2025-06-24 02:08:45', '2025-06-24 02:10:45', '2025-06-24 02:45:43', 'cs_test_b17XOnY9tsF3bqWrO6YXvF4XGPO3tbaTqlzsyaf45bCruEelg6ANcCWjG2'),
(59, 'HC186', 'ULTIMATE', 5, '2025-06-24 02:19:18', '2025-06-24 02:21:18', '2025-06-24 02:45:43', 'pi_3RdMS8EJRyUMDOj508Q3vDt7'),
(60, 'india', 'STARTER', 5, '2025-06-05 09:28:54', '2025-06-05 09:30:54', '2025-06-24 02:45:43', 'cs_test_a11dSpbwZhhjipAtGGwQH3XhGdk9l7gkOg9tNN26mbVJ0WSKF6tOFzRL5u'),
(61, 'kafka3', 'STARTER', 5, '2025-06-06 10:45:53', '2025-06-06 10:47:53', '2025-06-24 02:45:43', 'cs_test_a1JLHRFB9ty9jZ1lF3OcCcvpd3EnAvqf7iNbCjrRLfYFux3YgsETjDD764'),
(62, 'kafkaTest', 'STARTER', 25, '2025-06-06 10:04:20', '2025-06-06 10:06:20', '2025-06-24 02:45:43', 'cs_test_a15MoWemNbC5thjHVsSYWfBcforv0tAuZPd7a79yz19ADU2pfyDmB1JoyP'),
(63, 'laos', 'STARTER', 5, '2025-06-06 07:04:18', '2025-06-06 07:06:18', '2025-06-24 02:45:43', 'cs_test_a1UGMtDeFkRct3p7JpLLb9FKucPpAnB8sEsW3H7aJZ8HGBkV6djcOeuykf'),
(64, 'maffy', 'BUSINESS', 5, '2025-06-06 05:21:55', '2025-06-06 05:23:55', '2025-06-24 02:45:43', 'cs_test_b1KnlaCvBN3095Ig1nxwXSVoqfI6NonUXKJeTGJSTzScqI49BCX0YFsiWN'),
(65, 'maffy', 'STARTER', 5, '2025-06-06 05:21:55', '2025-06-06 05:23:55', '2025-06-24 02:45:43', 'cs_test_b1KnlaCvBN3095Ig1nxwXSVoqfI6NonUXKJeTGJSTzScqI49BCX0YFsiWN'),
(66, 'maffy', 'STARTER', 5, '2025-06-06 05:54:20', '2025-06-06 05:56:20', '2025-06-24 02:45:43', 'cs_test_a1YFsQgMR3B2VqPUrWyd0HVcLsw5vLSmz5TBxZ0Q9bojpZikdnkexw9FdB'),
(67, 'maffy', 'ULTIMATE', 5, '2025-06-06 05:21:55', '2025-06-06 05:23:55', '2025-06-24 02:45:43', 'cs_test_b1KnlaCvBN3095Ig1nxwXSVoqfI6NonUXKJeTGJSTzScqI49BCX0YFsiWN'),
(68, 'MrMalay', 'STARTER', 5, '2025-06-09 04:57:12', '2025-06-09 04:59:12', '2025-06-24 02:45:43', 'cs_test_a12Fjyh8AJelYs4yIxxYSqbNlHwFbrHxH1OQ4uWimIpkKCloWbo56K9C7U'),
(69, 'root', 'STARTER', 30, '2024-07-19 11:13:50', '2024-07-19 11:15:50', '2025-06-24 02:45:43', 'not set'),
(70, 'SG2e', 'BUSINESS', 10, '2025-06-05 09:46:54', '2025-06-05 09:48:54', '2025-06-24 02:45:43', 'cs_test_b1ngfogofwCxe20HLynYxbv9hPyO1qRWwTGt8wx0SIS4lWQIDGH5YSYKrv'),
(71, 'SG2e', 'BUSINESS', 5, '2025-06-05 09:46:54', '2025-06-05 09:48:54', '2025-06-24 02:45:43', 'cs_test_b1ngfogofwCxe20HLynYxbv9hPyO1qRWwTGt8wx0SIS4lWQIDGH5YSYKrv'),
(72, 'test', 'BUSINESS', 5, '2025-06-04 08:52:36', '2025-06-04 08:54:36', '2025-06-24 02:45:43', 'cs_test_a1i2BDs1thqaaJDdvZkDa9QCExx6zdP0Xzm7Ik2af6AES3kYCkjrBEijV1'),
(73, 'test', 'BUSINESS', 5, '2025-06-05 07:05:49', '2025-06-05 07:07:49', '2025-06-24 02:45:43', 'cs_test_a1WxZFvxzxSTizSneKCBHSB14w26bwcR8w4JxrHAnCl3l3ckqLZ7FWPLD9'),
(74, 'test', 'BUSINESS', 10, '2025-06-05 07:08:05', '2025-06-05 07:10:05', '2025-06-24 02:45:43', 'pi_3RWXtsEJRyUMDOj51SAKlui0'),
(75, 'test', 'BUSINESS', 10, '2025-06-05 08:27:09', '2025-06-05 08:29:09', '2025-06-24 02:45:43', 'cs_test_a1k8o2dn26ko8VBp6FH8Cbr8PHYOaxBzY9E20jKrS50ne0WGvfjRHbolGl'),
(76, 'test', 'BUSINESS', 10, '2025-06-05 08:36:32', '2025-06-05 08:38:32', '2025-06-24 02:45:43', 'cs_test_a1uZmhyrX2zujW9hP0veUckUZSHDTf96AdGm9RVqzZgS5ex3cqOtYWYumc'),
(77, 'test', 'BUSINESS', 5, '2025-06-10 04:24:50', '2025-06-10 04:26:50', '2025-06-24 02:45:43', 'cs_test_a1FXX1Q9s1Mt7OwZoWTXFd4Rw6uwxMtYSoHo4pxH9eMlqBoxsvuYqNnBMz'),
(78, 'test', 'BUSINESS', 5, '2025-06-10 04:38:07', '2025-06-10 04:40:07', '2025-06-24 02:45:43', 'cs_test_a1yE1j7ObLPtPhGfghMBpTPFTXSMnG8wbdzjVOipIf8leN4ejze9wUJirh'),
(79, 'test', 'BUSINESS', 5, '2025-06-10 04:42:10', '2025-06-10 04:44:10', '2025-06-24 02:45:43', 'cs_test_a1TWefodYIhvUVB3ODAZbqHYoSZ1EvwQx03JHNSbGErmArmrge9w83PgTB'),
(80, 'test', 'BUSINESS', 5, '2025-06-11 10:36:21', '2025-06-11 10:38:21', '2025-06-24 02:45:43', 'pi_3RYm0qEJRyUMDOj51aO5ZRLH'),
(81, 'test', 'BUSINESS', 5, '2025-06-11 10:47:44', '2025-06-11 10:49:44', '2025-06-24 02:45:43', 'pi_3RYmBzEJRyUMDOj51GtzgbSi'),
(82, 'test', 'BUSINESS', 10, '2025-06-11 10:47:44', '2025-06-11 10:49:44', '2025-06-24 02:45:43', 'pi_3RYmBzEJRyUMDOj51GtzgbSi'),
(83, 'test', 'BUSINESS', 5, '2025-06-12 02:52:30', '2025-06-12 02:54:30', '2025-06-24 02:45:43', 'cs_test_a1g21YgztjpaeUbAPKjRRSDTCUA37V3FCTjfACJIZdQLWxL2OtsWxJaGhb'),
(84, 'test', 'BUSINESS', 10, '2025-06-12 03:01:12', '2025-06-12 03:03:12', '2025-06-24 02:45:43', 'pi_3RZ1O5EJRyUMDOj50FRrjgSQ'),
(85, 'test', 'BUSINESS', 5, '2025-06-12 03:01:12', '2025-06-12 03:03:12', '2025-06-24 02:45:43', 'pi_3RZ1O5EJRyUMDOj50FRrjgSQ'),
(86, 'test', 'PREMIUM', 1, '2024-09-11 16:08:23', '2024-09-11 16:10:23', '2025-06-24 02:45:43', 'cs_test_a1vPE5sGy6KbRPaEkzXbXW3XBfpJg6aSQOCU12YPuKre9iyFWJs8A96vax'),
(87, 'test', 'STARTER', 1, '2024-07-15 16:07:48', '2024-07-15 16:09:48', '2025-06-24 02:45:43', ''),
(88, 'test', 'STARTER', 10, '2024-07-19 16:23:09', '2024-07-19 16:25:09', '2025-06-24 02:45:43', 'cs_test_a10qiW5vgClkfux98q46ALQjlJS59n6zmDQbYPNrn9XPgKHJqeBJ1WnU53'),
(89, 'test', 'STARTER', 10, '2024-07-19 16:47:22', '2024-07-19 16:49:22', '2025-06-24 02:45:43', 'cs_test_a1NyC3T2lxmpZIAkwh6UlkqGpS3UddqIHlGI2XGJgmkbCutvEx0KUTU2cp'),
(90, 'test', 'STARTER', 10, '2024-07-23 11:15:38', '2024-07-23 11:17:38', '2025-06-24 02:45:43', 'cs_test_a12k2KnCMTnqZNK3kVtLjDAzCAOWrLmT8DxWbVjHhVI3wjiELgsD69O9pd'),
(91, 'test', 'STARTER', 1, '2024-07-23 11:45:56', '2024-07-23 11:47:56', '2025-06-24 02:45:43', 'cs_test_a1wThR010E9hFWuYXvpjEcnVc7gPCSPVqvRC0Vjg6PEfCPDQePLWjseHKC'),
(92, 'test', 'STARTER', 1, '2024-07-23 14:22:12', '2024-07-23 14:24:12', '2025-06-24 02:45:43', 'cs_test_a17oYtppjlYd5ljKPGCIbifFd9RPY45V28Pdwy12KSxb1fsT9cfsU1JDAr'),
(93, 'test', 'STARTER', 1, '2024-07-23 14:34:53', '2024-07-23 14:36:53', '2025-06-24 02:45:43', 'cs_test_a1RQXE8c22NNwQdNz2drwIGaoS8ujAlgruJa5IsKOope7grxMytHbMDT7F'),
(94, 'test', 'STARTER', 1, '2024-07-23 14:55:20', '2024-07-23 14:57:20', '2025-06-24 02:45:43', 'cs_test_a17kHnsJICcDPzeR6kqGYthgIN42iyzcxC6cT5P7wJpaAIwKHoaXfaHQMa'),
(95, 'test', 'STARTER', 1, '2024-07-31 16:24:24', '2024-07-31 16:26:24', '2025-06-24 02:45:43', 'cs_test_a1AMTYYnoaiEhIRxi1TjWrGhTyVItiYJmEm5wknle428xgkGBPX24fgDds'),
(96, 'test', 'STARTER', 1, '2024-09-11 15:05:19', '2024-09-11 15:07:19', '2025-06-24 02:45:43', 'cs_test_a1SiCTlfaUDaoNFQHj7GKUZJ9Lu9rTBHsaGfNAOuCFbXLiFqTYKmuDejEV'),
(97, 'test', 'STARTER', 1, '2024-09-11 15:17:20', '2024-09-11 15:19:20', '2025-06-24 02:45:43', 'cs_test_a1R9Vkw4e5rGAISdq6MAIzRDQtO5LGBPWjgGSYwcudPCoczlnBXs5m4yPB'),
(98, 'test', 'STARTER', 5, '2025-06-04 08:49:59', '2025-06-04 08:51:59', '2025-06-24 02:45:43', 'cs_test_a1HyR97BugptYWeyJLIRPMbfySMyq7354dOeqN9xJXJ0rp8VNi9evCGhrb'),
(99, 'test', 'STARTER', 5, '2025-06-04 09:48:56', '2025-06-04 09:50:56', '2025-06-24 02:45:43', 'cs_test_a1vCL6vwuWo9GVbuTtTThbHalE6s0MQrgQAA4rB1excSOsLKiqjNzZKXho'),
(100, 'test', 'STARTER', 5, '2025-06-05 07:10:10', '2025-06-05 07:12:10', '2025-06-24 02:45:43', 'cs_test_a1JFQAOXK7ZMWXnhv5tC95JWaJjvnaV4RhdqUUWxvXlssq6IrLyxwUFfxx'),
(101, 'test', 'STARTER', 5, '2025-06-05 08:51:32', '2025-06-05 08:53:32', '2025-06-24 02:45:43', 'cs_test_a1mmX5NlTAVqye5u9iSBhIQcPZb1evaFBForB7Mc2v3w4WupBy0IQuNQO6'),
(102, 'test', 'STARTER', 5, '2025-06-06 07:00:48', '2025-06-06 07:02:48', '2025-06-24 02:45:43', 'pi_3RWuGdEJRyUMDOj51yIQKYuu'),
(103, 'test', 'STARTER', 5, '2025-06-10 04:14:56', '2025-06-10 04:16:56', '2025-06-24 02:45:43', 'cs_test_a1fuzlzXzuMTyInpmdM4BUpfPITqIbTzgVfGBWqlm3OIUdss1iBnT65kFe'),
(104, 'test', 'STARTER', 5, '2025-06-10 04:16:34', '2025-06-10 04:18:34', '2025-06-24 02:45:43', 'cs_test_a1EwgIESAo87bmlXY9mI0ytcC8WpvzoF0c6CqoAolGvbOdoAqAYa7Q0JG1'),
(105, 'test', 'STARTER', 5, '2025-06-10 04:28:08', '2025-06-10 04:30:08', '2025-06-24 02:45:43', 'cs_test_a1P2P62Acslp60tV5x2z9TguSCLWLXZjKJfyPJZDKVVulDtWyE5yjhRVyl'),
(106, 'test', 'STARTER', 5, '2025-06-10 04:30:08', '2025-06-10 04:32:08', '2025-06-24 02:45:43', 'cs_test_a1BlaxgVAQw1A2R40DldkrU22PKdXHiUlskqN3jBvM1xXtPCY7T5SppmK0'),
(107, 'test', 'STARTER', 5, '2025-06-10 04:41:12', '2025-06-10 04:43:12', '2025-06-24 02:45:43', 'cs_test_a1Y1o3WFydVrU5rWvrRoXKmFMO30JPD3F0wqfa8RC8dhRoUxtVhESMr1mK'),
(108, 'test', 'STARTER', 5, '2025-06-10 04:45:57', '2025-06-10 04:47:57', '2025-06-24 02:45:43', 'cs_test_a1Q3GyLT8BA0vfIuocxc95FwFABxAg2IOVhRVfgRYCnDoR2b9XvKag4AZO'),
(109, 'test', 'STARTER', 5, '2025-06-11 02:28:15', '2025-06-11 02:30:15', '2025-06-24 02:45:43', 'cs_test_a1znPNQvgsGr2FPVxX6qcECsDNLm0ZQsGOhlhy4QV4Z0vQ0S7r9JZGaknT'),
(110, 'test', 'STARTER', 15, '2025-06-11 10:32:08', '2025-06-11 10:34:08', '2025-06-24 02:45:43', 'pi_3RYlwjEJRyUMDOj50hOoUoKr'),
(111, 'test', 'STARTER', 5, '2025-06-11 10:34:15', '2025-06-11 10:36:15', '2025-06-24 02:45:43', 'pi_3RYlyxEJRyUMDOj511f4VrP1'),
(112, 'test', 'STARTER', 5, '2025-06-11 10:36:21', '2025-06-11 10:38:21', '2025-06-24 02:45:43', 'pi_3RYm0qEJRyUMDOj51aO5ZRLH'),
(113, 'test', 'STARTER', 10, '2025-06-11 10:36:21', '2025-06-11 10:38:21', '2025-06-24 02:45:43', 'pi_3RYm0qEJRyUMDOj51aO5ZRLH'),
(114, 'test', 'STARTER', 5, '2025-06-11 10:40:39', '2025-06-11 10:42:39', '2025-06-24 02:45:43', 'pi_3RYm59EJRyUMDOj50SYps07I'),
(115, 'test', 'STARTER', 10, '2025-06-11 10:47:44', '2025-06-11 10:49:44', '2025-06-24 02:45:43', 'pi_3RYmBzEJRyUMDOj51GtzgbSi'),
(116, 'test', 'STARTER', 5, '2025-06-11 10:47:44', '2025-06-11 10:49:44', '2025-06-24 02:45:43', 'pi_3RYmBzEJRyUMDOj51GtzgbSi'),
(117, 'test', 'STARTER', 10, '2025-06-11 11:22:28', '2025-06-11 11:24:28', '2025-06-24 02:45:43', 'pi_3RYmjUEJRyUMDOj50ZO6mAJS'),
(118, 'test', 'STARTER', 5, '2025-06-11 11:29:01', '2025-06-11 11:31:01', '2025-06-24 02:45:43', 'pi_3RYmpqEJRyUMDOj51lJiRy8t'),
(119, 'test', 'STARTER', 10, '2025-06-11 11:37:04', '2025-06-11 11:39:04', '2025-06-24 02:45:43', 'cs_test_a1rc7Me1aczD1HMalumxhHgsIFn3rSIe7QtqFv5xZCrpEpMQCYw1clIVP1'),
(120, 'test', 'STARTER', 5, '2025-06-12 02:45:05', '2025-06-12 02:47:05', '2025-06-24 02:45:43', 'pi_3RZ18LEJRyUMDOj50gYCN8Hr'),
(121, 'test', 'STARTER', 5, '2025-06-12 03:00:29', '2025-06-12 03:02:29', '2025-06-24 02:45:43', 'pi_3RZ1NOEJRyUMDOj50FKx8hyT'),
(122, 'test', 'STARTER', 5, '2025-06-12 03:26:02', '2025-06-12 03:28:02', '2025-06-24 02:45:43', 'pi_3RZ1m7EJRyUMDOj50AUoRwqe'),
(123, 'test', 'STARTER', 5, '2025-06-12 03:45:46', '2025-06-12 03:47:46', '2025-06-24 02:45:43', 'pi_3RZ25DEJRyUMDOj51PxVitqC'),
(124, 'test', 'STARTER', 5, '2025-06-12 05:35:16', '2025-06-12 05:37:16', '2025-06-24 02:45:43', 'pi_3RZ3nBEJRyUMDOj50TPBOWJk'),
(125, 'test', 'STARTER', 5, '2025-06-13 03:29:21', '2025-06-13 03:31:21', '2025-06-24 02:45:43', 'pi_3RZOIsEJRyUMDOj51D3Rau77'),
(126, 'test', 'ULTIMATE', 5, '2025-06-11 10:47:44', '2025-06-11 10:49:44', '2025-06-24 02:45:43', 'pi_3RYmBzEJRyUMDOj51GtzgbSi'),
(127, 'test', 'ULTIMATE', 10, '2025-06-11 10:47:44', '2025-06-11 10:49:44', '2025-06-24 02:45:43', 'pi_3RYmBzEJRyUMDOj51GtzgbSi'),
(128, 'test', 'ULTIMATE', 5, '2025-06-12 02:53:21', '2025-06-12 02:55:21', '2025-06-24 02:45:43', 'pi_3RZ1GTEJRyUMDOj51Q2anfTU'),
(129, 'test', 'ULTIMATE', 5, '2025-06-12 03:01:12', '2025-06-12 03:03:12', '2025-06-24 02:45:43', 'pi_3RZ1O5EJRyUMDOj50FRrjgSQ'),
(130, 'test', 'ULTIMATE', 5, '2025-06-13 03:29:21', '2025-06-13 03:31:21', '2025-06-24 02:45:43', 'pi_3RZOIsEJRyUMDOj51D3Rau77'),
(131, 'test2', 'STARTER', 50, '2024-07-19 11:46:19', '2024-07-19 11:48:19', '2025-06-24 02:45:43', 'cs_test_a1kllMwHzxx6eR1o081FnZxJL6NfaRWLZvcBzDgPfFLnRNlP1K13GwVolx'),
(132, 'test2', 'STARTER', 10, '2024-07-19 11:49:07', '2024-07-19 11:51:07', '2025-06-24 02:45:43', 'cs_test_a1KVBDJlsGWQ6uwKpfuf5wrtT7QS3shpaTMOKjarfz6mAf47iJVUHdcsUr'),
(133, 'test2', 'STARTER', 30, '2024-07-19 11:52:26', '2024-07-19 11:54:26', '2025-06-24 02:45:43', 'cs_test_a1vkbG7baIdiQyr2EY08KL3aDSNsMhmsZx0DiCrdcMgUeN99n3ZjeY14fs'),
(134, 'test2', 'STARTER', 30, '2024-07-19 12:04:48', '2024-07-19 12:06:48', '2025-06-24 02:45:43', 'cs_test_a1O7kwjdVL5H7FqQpDytj3n1fAX6UM7nMXFwXxlWFkaZXLj80i3VnitxSx'),
(135, 'test2', 'STARTER', 50, '2024-07-19 12:15:04', '2024-07-19 12:17:04', '2025-06-24 02:45:43', 'cs_test_a1P5dEOvoon7gnltDxq4xI49aQxkt7P84N5N7yL40Po8rubffa0yPmBBED'),
(136, 'test2', 'STARTER', 10, '2024-07-19 15:54:41', '2024-07-19 15:56:41', '2025-06-24 02:45:43', 'cs_test_a1YYY5x2Yq6I2SHN0rohu3MILrUUt6cDdwnnBDxEKOW69SJzrw91WwWl5e'),
(137, 'test21', 'BUSINESS', 5, '2025-06-09 05:53:54', '2025-06-09 05:55:54', '2025-06-24 02:45:43', 'cs_test_b1WAywqDe5tYNat2k6j7bQRrPaTnCuC0FWwhm0Ek67NZjm6GuriztVTZLf'),
(138, 'test21', 'STARTER', 5, '2025-06-09 05:53:54', '2025-06-09 05:55:54', '2025-06-24 02:45:43', 'cs_test_b1WAywqDe5tYNat2k6j7bQRrPaTnCuC0FWwhm0Ek67NZjm6GuriztVTZLf'),
(139, 'testcheckout2', 'STARTER', 5, '2025-06-09 03:57:21', '2025-06-09 03:59:21', '2025-06-24 02:45:43', 'cs_test_a14hshrxiVQYOOtk4R1tfMMEyIJpuVIN2cnuFx41ovDzDjreqJIQAgIdeX'),
(140, 'testcheckout3', 'STARTER', 5, '2025-06-09 03:59:40', '2025-06-09 04:01:40', '2025-06-24 02:45:43', 'cs_test_a1h3KS2waaxj0zufcfG9kYt0uRf43g1SnSBP3qYg7YVs86s9QbMB4zPSJ6'),
(141, 'Testlaos1', 'BUSINESS', 5, '2025-06-05 09:54:39', '2025-06-05 09:56:39', '2025-06-24 02:45:43', 'cs_test_b1kd7uSFWOabK1GpHZ0tBEf7EpRHmTMhpZNGegTxlAOnpWYR3IMgf179hL'),
(142, 'Testlaos1', 'STARTER', 5, '2025-06-05 09:54:39', '2025-06-05 09:56:39', '2025-06-24 02:45:43', 'cs_test_b1kd7uSFWOabK1GpHZ0tBEf7EpRHmTMhpZNGegTxlAOnpWYR3IMgf179hL'),
(143, 'testmalaysia', 'STARTER', 25, '2025-06-09 02:26:19', '2025-06-09 02:28:19', '2025-06-24 02:45:43', 'cs_test_b1c715X9tAvnlW0koOgTU50DsckWhRO22diMYHALbenrQMDOoU7qa1YTVk'),
(144, 'testmalaysia', 'STARTER', 5, '2025-06-09 02:26:19', '2025-06-09 02:28:19', '2025-06-24 02:45:43', 'cs_test_b1c715X9tAvnlW0koOgTU50DsckWhRO22diMYHALbenrQMDOoU7qa1YTVk'),
(145, 'testmalaysia2', 'STARTER', 25, '2025-06-09 02:30:03', '2025-06-09 02:32:03', '2025-06-24 02:45:43', 'cs_test_b1GFwGJ7xPDWkrBlOcsVfdio8lATWFd19vXlFL8oLTLoh1uBIYtio164pP'),
(146, 'testmalaysia2', 'STARTER', 10, '2025-06-09 02:30:03', '2025-06-09 02:32:03', '2025-06-24 02:45:43', 'cs_test_b1GFwGJ7xPDWkrBlOcsVfdio8lATWFd19vXlFL8oLTLoh1uBIYtio164pP'),
(147, 'TestMyanMar', 'BUSINESS', 5, '2025-06-05 10:18:04', '2025-06-05 10:20:04', '2025-06-24 02:45:43', 'cs_test_a12ZOIR7iFO1pJCW9M2ksc5OIZSq9YaOu6QBz8NKucXy1u95GzgsZu4YiF'),
(148, 'TestMyanMar', 'BUSINESS', 10, '2025-06-05 11:21:07', '2025-06-05 11:23:07', '2025-06-24 02:45:43', 'cs_test_a1wbyZNsW21xueLh1XtgWzRHG0NI7HiLa1kdjTu7HAcRSPziKlMIZyfkov'),
(149, 'TestMyanMar', 'STARTER', 5, '2025-06-05 10:13:43', '2025-06-05 10:15:43', '2025-06-24 02:45:43', 'cs_test_a1ksW0FOQrFfVrC9Nn67faig06MqiWPqQY28aIwYEkhtDVmkvH9RFb4tvH'),
(150, 'TestMyanMar', 'STARTER', 5, '2025-06-05 10:23:30', '2025-06-05 10:25:30', '2025-06-24 02:45:43', 'cs_test_a1RuLVs1OdZcIsgXGbKc9PlF6NRpOy0KBiSlX2I7HaH89BaLGkLgEfv8YT'),
(151, 'TestMyanMar', 'STARTER', 5, '2025-06-05 10:37:57', '2025-06-05 10:39:57', '2025-06-24 02:45:43', 'cs_test_a1MKxRg6WAPJtAkk3lJFeWEJ8VuQIri9KtaZLVV8NeYgaaRWYb3FBIDBjK'),
(152, 'TestMyanMar', 'ULTIMATE', 5, '2025-06-05 10:19:26', '2025-06-05 10:21:26', '2025-06-24 02:45:43', 'cs_test_a1lpcXjmXvWJkBTDIe2XU5FpdfgyJ3kp7kgGAngTGKpMo6vmr9f1Wlnd5R'),
(153, 'TestMyanMar', 'ULTIMATE', 5, '2025-06-05 10:30:08', '2025-06-05 10:32:08', '2025-06-24 02:45:43', 'cs_test_a1vDD6Hy6UX8Vf4rgqhvsubNKk1k3tJ25I4sXXfFwhpHiRFhidGFynUyAm'),
(154, 'TestMyanMar', 'ULTIMATE', 5, '2025-06-05 11:22:07', '2025-06-05 11:24:07', '2025-06-24 02:45:43', 'pi_3RWbs0EJRyUMDOj50OlABeaz'),
(155, 'testnonlogin', 'STARTER', 5, '2025-06-09 03:54:32', '2025-06-09 03:56:32', '2025-06-24 02:45:43', 'cs_test_a1JZAZTBc9kh2EgP1gX9mSydxt55CU3DjNoJHIaFq4Gg66OMueXmsGnIH2'),
(156, 'testVietnam', 'BUSINESS', 5, '2025-06-05 10:07:26', '2025-06-05 10:09:26', '2025-06-24 02:45:43', 'cs_test_b1PvlT87m8UszXbJTQbZiYoQ0GXgq2fME7Rfy5iGGT7v7pUM2uyEMyJ4PW'),
(157, 'testVietnam', 'ULTIMATE', 5, '2025-06-05 10:07:26', '2025-06-05 10:09:26', '2025-06-24 02:45:43', 'cs_test_b1PvlT87m8UszXbJTQbZiYoQ0GXgq2fME7Rfy5iGGT7v7pUM2uyEMyJ4PW'),
(158, 'thisIsAmerica', 'STARTER', 5, '2025-06-10 03:24:26', '2025-06-10 03:26:26', '2025-06-24 02:45:43', 'cs_test_a1qQENOkMXFclunCGpvVfqov3fSwLWOnPQHIfSFzLNVxmgH15u9mGMr4Av'),
(159, 'user10455', 'STARTER', 5, '2025-06-04 10:00:32', '2025-06-04 10:02:32', '2025-06-24 02:45:43', 'cs_test_a1ya2nsgJJVkthaUgx2ZYCeqB1wNWkjDG3abBYaZh2P64jlWQhOzWsTGba'),
(160, 'user34839', 'BUSINESS', 10, '2025-06-04 02:46:47', '2025-06-04 02:48:47', '2025-06-24 02:45:43', 'cs_test_b1kvkCFP0UQAuvEq93rmAemOuZQZvNqc180SW4QA4efHFljXNoKWkkjmi5'),
(161, 'user34839', 'BUSINESS', 5, '2025-06-04 02:46:47', '2025-06-04 02:48:47', '2025-06-24 02:45:43', 'cs_test_b1kvkCFP0UQAuvEq93rmAemOuZQZvNqc180SW4QA4efHFljXNoKWkkjmi5'),
(162, 'user34839', 'STARTER', 5, '2025-06-04 02:46:47', '2025-06-04 02:48:47', '2025-06-24 02:45:43', 'cs_test_b1kvkCFP0UQAuvEq93rmAemOuZQZvNqc180SW4QA4efHFljXNoKWkkjmi5'),
(163, 'user40554', 'BUSINESS', 10, '2025-06-04 08:57:32', '2025-06-04 08:59:32', '2025-06-24 02:45:43', 'cs_test_b1844etB9AAODvxiBc5coOmg2rtJB2JEVOjIdHDlkAOZDYn9CFyl3xnLcL'),
(164, 'user40554', 'STARTER', 10, '2025-06-04 08:57:32', '2025-06-04 08:59:32', '2025-06-24 02:45:43', 'cs_test_b1844etB9AAODvxiBc5coOmg2rtJB2JEVOjIdHDlkAOZDYn9CFyl3xnLcL'),
(165, 'user40554', 'STARTER', 5, '2025-06-04 08:57:32', '2025-06-04 08:59:32', '2025-06-24 02:45:43', 'cs_test_b1844etB9AAODvxiBc5coOmg2rtJB2JEVOjIdHDlkAOZDYn9CFyl3xnLcL'),
(166, 'user43076', 'STARTER', 5, '2025-06-04 02:56:58', '2025-06-04 02:58:58', '2025-06-24 02:45:43', 'cs_test_a1XHHDjHnjrmDdgzvEShBdMfDqv7HaIujgPG61GXOzwoFyM9rGBXW6ub19'),
(167, 'user49913', 'STARTER', 5, '2025-06-09 05:06:41', '2025-06-09 05:08:41', '2025-06-24 02:45:43', 'cs_test_a1TjOij2aHnbwBX4mqGzdLhiQXtN7KyHkrra72aYH1Q7ERFiAJ2hWiZjTI'),
(168, 'user57006', 'STARTER', 5, '2025-06-06 10:41:01', '2025-06-06 10:43:01', '2025-06-24 02:45:43', 'cs_test_a1IgxtNlwtQd16MfnyqQMYdfYIqq9gnGPD2HbmnUTgPDmqzRdbmOBql9aS'),
(169, 'user61778', 'BUSINESS', 5, '2025-06-04 03:09:16', '2025-06-04 03:11:16', '2025-06-24 02:45:43', 'cs_test_b1Ko1kSU6yIwxHHuyg9qNC6SRyl203dAfTfcuduStZFDHO7IoeLGdPuM2w'),
(170, 'user61778', 'STARTER', 5, '2025-06-04 03:09:16', '2025-06-04 03:11:16', '2025-06-24 02:45:43', 'cs_test_b1Ko1kSU6yIwxHHuyg9qNC6SRyl203dAfTfcuduStZFDHO7IoeLGdPuM2w'),
(171, 'user61778', 'ULTIMATE', 5, '2025-06-04 03:09:16', '2025-06-04 03:11:16', '2025-06-24 02:45:43', 'cs_test_b1Ko1kSU6yIwxHHuyg9qNC6SRyl203dAfTfcuduStZFDHO7IoeLGdPuM2w'),
(172, 'user64324', 'STARTER', 5, '2025-06-02 11:11:50', '2025-06-02 11:13:50', '2025-06-24 02:45:43', 'cs_test_a14o9wEKddaq7hZFyB7t4H5SWqx42CpaJX14okHcJ9JOOYcSXeb0sbSjbk'),
(173, 'user70412', 'STARTER', 5, '2025-06-04 04:56:28', '2025-06-04 04:58:28', '2025-06-24 02:45:43', 'cs_test_a1KDs2WmDHARvGE6ZR0NQ7qolQlgSqg5ABIQHcGX4iYejHUOU7WfCz0MlV'),
(174, 'user72354', 'BUSINESS', 5, '2025-06-04 02:40:40', '2025-06-04 02:42:40', '2025-06-24 02:45:43', 'cs_test_b12auZUv27DhkUJHbBpa9lLilej6r4xLl9Oo2YIhc4W3OyBLJ1egZ9JDdv'),
(175, 'user72354', 'STARTER', 5, '2025-06-04 02:40:40', '2025-06-04 02:42:40', '2025-06-24 02:45:43', 'cs_test_b12auZUv27DhkUJHbBpa9lLilej6r4xLl9Oo2YIhc4W3OyBLJ1egZ9JDdv'),
(176, 'user90502', 'BUSINESS', 5, '2025-06-05 09:19:06', '2025-06-05 09:21:06', '2025-06-24 02:45:43', 'cs_test_b1GnAQ2JQ0UOsgBqOuuCjyyd7URDluA4yhsRcB3sSuBK4iomL72G3IWQMx'),
(177, 'user90502', 'STARTER', 5, '2025-06-05 09:19:06', '2025-06-05 09:21:06', '2025-06-24 02:45:43', 'cs_test_b1GnAQ2JQ0UOsgBqOuuCjyyd7URDluA4yhsRcB3sSuBK4iomL72G3IWQMx'),
(178, 'user92053', 'BUSINESS', 10, '2025-06-05 09:11:54', '2025-06-05 09:13:54', '2025-06-24 02:45:43', 'cs_test_b1CJ722RY6oS8WRuQegFsUBagjFz8syhYpMjMjBQI9aB1YL5OqV75L9qbu'),
(179, 'user92053', 'BUSINESS', 5, '2025-06-05 09:15:57', '2025-06-05 09:17:57', '2025-06-24 02:45:43', 'cs_test_a1tmTdvUczPgDLGAtanZyUWTD9kMuEkgIhyvmNTSZLYFafil9tPSyvSsNw'),
(180, 'user92053', 'STARTER', 10, '2025-06-05 09:11:54', '2025-06-05 09:13:54', '2025-06-24 02:45:43', 'cs_test_b1CJ722RY6oS8WRuQegFsUBagjFz8syhYpMjMjBQI9aB1YL5OqV75L9qbu'),
(181, 'user92053', 'ULTIMATE', 10, '2025-06-05 09:11:54', '2025-06-05 09:13:54', '2025-06-24 02:45:43', 'cs_test_b1CJ722RY6oS8WRuQegFsUBagjFz8syhYpMjMjBQI9aB1YL5OqV75L9qbu'),
(182, 'user92053', 'ULTIMATE', 5, '2025-06-05 09:13:51', '2025-06-05 09:15:51', '2025-06-24 02:45:43', 'cs_test_a106SdtGwT3y2OoyXpxkSeQtuRma1BauZBYc4PzyoZKB5LufRhvsPIsw9e'),
(183, 'user92931', 'STARTER', 5, '2025-06-11 04:58:35', '2025-06-11 05:00:35', '2025-06-24 02:45:43', 'cs_test_a1uocSrvIy3sLk1DXLBq701Tp31uIRj77s3BjZzzh5B5p0VAm8wMxhjUVr'),
(184, 'user95311', 'BUSINESS', 5, '2025-06-05 02:20:55', '2025-06-05 02:22:55', '2025-06-24 02:45:43', 'cs_test_b13A3WhhIISq9WSoJzdMMSg6cXjvZAE2yhqo6R3Fv7JqOvJLbgyml6gQej'),
(185, 'user95311', 'STARTER', 5, '2025-06-05 02:20:55', '2025-06-05 02:22:55', '2025-06-24 02:45:43', 'cs_test_b13A3WhhIISq9WSoJzdMMSg6cXjvZAE2yhqo6R3Fv7JqOvJLbgyml6gQej'),
(186, 'user95311', 'STARTER', 10, '2025-06-05 02:20:55', '2025-06-05 02:22:55', '2025-06-24 02:45:43', 'cs_test_b13A3WhhIISq9WSoJzdMMSg6cXjvZAE2yhqo6R3Fv7JqOvJLbgyml6gQej'),
(187, 'user95311', 'STARTER', 5, '2025-06-05 03:01:08', '2025-06-05 03:03:08', '2025-06-24 02:45:43', 'pi_3RWU3BEJRyUMDOj50tuSrVcJ'),
(188, 'HC186', 'BUSINESS', 5, '2025-06-24 03:03:29', '2025-06-24 03:05:29', '2025-06-24 03:22:06', 'pi_3RdN8tEJRyUMDOj50gG71Map'),
(189, 'HC186', 'BUSINESS', 10, '2025-06-24 03:03:29', '2025-06-24 03:05:29', '2025-06-24 03:22:06', 'pi_3RdN8tEJRyUMDOj50gG71Map'),
(190, 'HC186', 'STARTER', 5, '2025-06-24 03:03:29', '2025-06-24 03:05:29', '2025-06-24 03:22:06', 'pi_3RdN8tEJRyUMDOj50gG71Map'),
(191, 'HC186', 'STARTER', 10, '2025-06-24 03:03:29', '2025-06-24 03:05:29', '2025-06-24 03:22:06', 'pi_3RdN8tEJRyUMDOj50gG71Map'),
(192, 'HC186', 'ULTIMATE', 5, '2025-06-24 03:03:29', '2025-06-24 03:05:29', '2025-06-24 03:22:06', 'pi_3RdN8tEJRyUMDOj50gG71Map'),
(193, 'HC186', 'ULTIMATE', 10, '2025-06-24 03:03:29', '2025-06-24 03:05:29', '2025-06-24 03:22:06', 'pi_3RdN8tEJRyUMDOj50gG71Map'),
(194, 'HC186', 'BUSINESS', 5, '2025-06-24 03:30:19', '2025-06-24 03:32:19', '2025-06-24 03:46:11', 'pi_3RdNYrEJRyUMDOj51vAK9Isx'),
(195, 'HC186', 'STARTER', 5, '2025-06-24 03:30:19', '2025-06-24 03:32:19', '2025-06-24 03:46:11', 'pi_3RdNYrEJRyUMDOj51vAK9Isx'),
(196, 'HC186', 'ULTIMATE', 5, '2025-06-24 03:30:19', '2025-06-24 03:32:19', '2025-06-24 03:46:11', 'pi_3RdNYrEJRyUMDOj51vAK9Isx'),
(197, 'HC186', 'BUSINESS', 5, '2025-06-24 03:47:24', '2025-06-24 03:49:24', '2025-06-24 03:49:34', 'pi_3RdNpOEJRyUMDOj504bcaj0S'),
(198, 'HC186', 'STARTER', 5, '2025-06-24 03:47:24', '2025-06-24 03:49:24', '2025-06-24 03:49:34', 'pi_3RdNpOEJRyUMDOj504bcaj0S'),
(199, 'HC186', 'ULTIMATE', 5, '2025-06-24 03:47:24', '2025-06-24 03:49:24', '2025-06-24 03:49:34', 'pi_3RdNpOEJRyUMDOj504bcaj0S'),
(200, 'HC186', 'STARTER', 4, '2025-06-24 03:53:50', '2025-06-24 03:55:50', '2025-06-24 04:00:20', 'pi_3RdNvcEJRyUMDOj50S1gr1lC'),
(201, 'HC186', 'BUSINESS', 5, '2025-06-24 04:05:34', '2025-06-24 04:07:34', '2025-06-24 04:10:51', 'pi_3RdO6zEJRyUMDOj51aVe0UIz'),
(202, 'HC186', 'STARTER', 4, '2025-06-24 04:05:34', '2025-06-24 04:07:34', '2025-06-24 04:10:51', 'pi_3RdO6zEJRyUMDOj51aVe0UIz'),
(203, 'HC186', 'STARTER', 4, '2025-06-24 04:12:15', '2025-06-24 04:14:15', '2025-06-24 04:17:26', 'pi_3RdODREJRyUMDOj51GGUJqxn'),
(204, 'HC185', 'starter', 5, '2023-12-24 05:57:57', '2023-12-24 05:59:57', '2025-06-24 06:16:00', '0'),
(205, 'HC185', 'starter', 5, '2023-12-24 05:58:58', '2023-12-24 06:00:58', '2025-06-24 06:16:00', '0'),
(206, 'HC185', 'starter', 2, '2024-07-03 05:57:57', '2024-07-03 05:59:57', '2025-06-24 06:16:00', '0'),
(207, 'HC185', 'starter', 2, '2024-07-03 05:58:58', '2024-07-03 06:00:58', '2025-06-24 06:16:00', '0'),
(208, 'HC185', 'starter', 10, '2024-12-24 05:57:57', '2024-12-24 05:59:57', '2025-06-24 06:16:00', '0'),
(209, 'HC185', 'starter', 10, '2024-12-24 05:58:58', '2024-12-24 06:00:58', '2025-06-24 06:16:00', '0'),
(210, 'HC185', 'ultimate', 3, '2024-05-24 05:57:57', '2024-05-24 05:59:57', '2025-06-24 06:16:00', '0'),
(211, 'HC185', 'ultimate', 3, '2024-05-24 05:58:58', '2024-05-24 06:00:58', '2025-06-24 06:16:00', '0'),
(212, 'HC185', 'ultimate', 4, '2024-07-24 05:57:57', '2024-07-24 05:59:57', '2025-06-24 06:16:00', '0'),
(213, 'HC185', 'ultimate', 4, '2024-07-24 05:58:58', '2024-07-24 06:00:58', '2025-06-24 06:16:00', '0'),
(214, 'HC186', 'STARTER', 5, '2025-06-24 06:25:24', '2025-06-24 06:27:24', '2025-06-24 06:27:24', 'pi_3RdQIIEJRyUMDOj50mASPH1X'),
(215, 'HC186', 'BUSINESS', 5, '2025-06-24 06:25:24', '2025-06-24 06:27:24', '2025-06-24 06:27:24', 'pi_3RdQIIEJRyUMDOj50mASPH1X'),
(216, 'HC186', 'ULTIMATE', 5, '2025-06-24 06:25:24', '2025-06-24 06:27:24', '2025-06-24 06:27:24', 'pi_3RdQIIEJRyUMDOj50mASPH1X'),
(217, 'HC186', 'BUSINESS', 5, '2025-06-24 06:30:08', '2025-06-24 06:32:08', '2025-06-24 06:32:08', 'pi_3RdQMtEJRyUMDOj50Y5Tr0wa'),
(218, 'HC186', 'BUSINESS', 5, '2025-06-24 06:32:53', '2025-06-24 06:34:53', '2025-06-24 06:34:53', 'pi_3RdQPXEJRyUMDOj50YwroIch'),
(219, 'HC186', 'STARTER', 5, '2025-06-24 06:43:33', '2025-06-24 06:45:33', '2025-06-24 06:45:33', 'pi_3RdQZrEJRyUMDOj50Fr1NboH'),
(220, 'HC186', 'BUSINESS', 5, '2025-06-24 06:43:33', '2025-06-24 06:45:33', '2025-06-24 06:45:33', 'pi_3RdQZrEJRyUMDOj50Fr1NboH'),
(221, 'HC186', 'ULTIMATE', 5, '2025-06-24 06:43:33', '2025-06-24 06:45:33', '2025-06-24 06:45:33', 'pi_3RdQZrEJRyUMDOj50Fr1NboH'),
(222, 'HC186', 'STARTER', 4, '2025-06-24 06:48:28', '2025-06-24 06:50:28', '2025-06-24 06:50:28', 'pi_3RdQedEJRyUMDOj51qPjzfBq'),
(223, 'HC186', 'STARTER', 5, '2025-06-24 06:59:19', '2025-06-24 07:01:19', '2025-06-24 07:01:27', 'pi_3RdQp7EJRyUMDOj50P85GMRU'),
(224, 'HC186', 'BUSINESS', 5, '2025-06-24 06:59:19', '2025-06-24 07:01:19', '2025-06-24 07:01:27', 'pi_3RdQp7EJRyUMDOj50P85GMRU'),
(225, 'HC209', 'STARTER', 3, '2025-06-29 09:15:26', '2025-06-29 09:17:26', '2025-06-29 13:30:53', 'cs_test_a1jetgbMVXOE6dRgRNNyP05rh7T1pNb2jrS5DxdEkdjj2ZwjV5oH63ZUOY');

-- --------------------------------------------------------

--
-- Table structure for table `ticket_expiration_settings`
--

CREATE TABLE `ticket_expiration_settings` (
  `id` int(11) NOT NULL,
  `config_key` varchar(100) NOT NULL,
  `config_value` text NOT NULL,
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `ticket_expiration_settings`
--

INSERT INTO `ticket_expiration_settings` (`id`, `config_key`, `config_value`, `updated_at`, `created_at`) VALUES
(1, 'ticket_lifetime_months', '12', '2025-06-29 13:31:29', '2025-06-23 08:36:43'),
(2, 'auto_cleanup_enabled', 'true', '2025-06-29 13:31:29', '2025-06-23 08:36:43'),
(3, 'cleanup_frequency_days', '12', '2025-06-29 13:31:29', '2025-06-23 08:36:43'),
(4, 'warning_period_days', '3', '2025-06-29 13:31:29', '2025-06-23 08:36:43'),
(5, 'user_notifications_enabled', 'true', '2025-06-29 13:31:29', '2025-06-23 08:36:43'),
(6, 'admin_notification_email', '<EMAIL>', '2025-06-29 13:31:29', '2025-06-23 08:36:43'),
(7, 'system_enabled', 'true', '2025-06-29 13:31:29', '2025-06-23 08:36:43'),
(8, 'debug_mode', 'false', '2025-06-29 13:31:29', '2025-06-23 08:36:43'),
(113, 'last_cleanup_timestamp', '1750761899', '2025-06-24 10:44:59', '2025-06-23 09:48:48'),
(274, 'ticket_lifetime_unit', 'months', '2025-06-29 13:31:29', '2025-06-23 10:26:10'),
(275, 'cleanup_frequency_unit', 'months', '2025-06-29 13:31:29', '2025-06-23 10:26:10'),
(276, 'warning_period_unit', 'months', '2025-06-29 13:31:29', '2025-06-23 10:26:10');

-- --------------------------------------------------------

--
-- Table structure for table `ticket_logs`
--

CREATE TABLE `ticket_logs` (
  `id` int(11) NOT NULL,
  `ticket_id` int(11) DEFAULT NULL,
  `action` enum('success','pending','fail','cancel','expired') NOT NULL DEFAULT 'pending',
  `description` text DEFAULT NULL,
  `performed_by_admin_id` int(11) DEFAULT NULL,
  `created_at` datetime DEFAULT current_timestamp(),
  `user_id` int(11) NOT NULL,
  `ticket_type` varchar(50) NOT NULL,
  `amount` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

--
-- Dumping data for table `ticket_logs`
--

INSERT INTO `ticket_logs` (`id`, `ticket_id`, `action`, `description`, `performed_by_admin_id`, `created_at`, `user_id`, `ticket_type`, `amount`) VALUES
(1009, NULL, 'fail', 'Failed to create starter ticket - API sync failed: Connection Error: cURL error 6: Could not resolve host: dev-sgsg-tktapi.appika.com (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://dev-sgsg-tktapi.appika.com/graphql', NULL, '2025-06-25 11:21:53', 1, 'starter', 0),
(1010, NULL, 'success', 'Admin added 5 ultimate tickets', 1, '2025-06-27 08:56:27', 200, 'ultimate', 5),
(1011, NULL, 'success', 'Admin added 5 ultimate tickets', 1, '2025-06-27 08:56:42', 200, 'ultimate', 5),
(1012, NULL, 'success', 'Admin added 1 starter tickets', 1, '2025-06-27 08:59:35', 200, 'starter', 1),
(1013, NULL, 'success', 'Admin added 5 Business tickets', 1, '2025-06-27 08:59:51', 200, 'Business', 5),
(1014, NULL, 'success', 'Admin added 10 ultimate tickets', 1, '2025-06-27 09:00:02', 200, 'ultimate', 10),
(1015, NULL, 'success', 'Admin added 1 starter tickets', 1, '2025-06-27 09:01:22', 200, 'starter', 1),
(1016, NULL, 'success', 'Admin added 1 Business tickets', 1, '2025-06-27 09:03:46', 200, 'Business', 1),
(1017, NULL, 'success', 'Admin added 1 ultimate tickets', 1, '2025-06-27 09:04:36', 200, 'ultimate', 1),
(1018, NULL, 'success', 'Admin added 1 starter tickets', 1, '2025-06-27 09:08:59', 200, 'starter', 1),
(1019, NULL, 'success', 'Admin added 1 Business tickets', 1, '2025-06-27 09:09:16', 200, 'Business', 1),
(1020, NULL, 'success', 'Admin added 1 ultimate tickets', 1, '2025-06-27 09:09:20', 200, 'ultimate', 1),
(1021, NULL, 'success', 'Admin added 1 starter tickets', 1, '2025-06-27 09:09:58', 200, 'starter', 1),
(1022, NULL, 'success', 'Admin added 1 starter tickets', 1, '2025-06-27 09:20:46', 200, 'starter', 1),
(1023, NULL, 'success', 'Admin added 1 starter tickets', 1, '2025-06-27 09:20:58', 200, 'starter', 1),
(1024, NULL, 'success', 'Admin added 1 starter tickets', 1, '2025-06-27 10:09:45', 200, 'starter', 1),
(1025, NULL, 'success', 'Admin added 1 starter tickets', 1, '2025-06-27 10:10:19', 200, 'starter', 1),
(1026, NULL, 'success', 'Admin added 1 starter tickets', 1, '2025-06-27 10:15:09', 200, 'starter', 1),
(1027, NULL, 'success', 'Admin added 1 starter tickets', 1, '2025-06-27 10:15:19', 200, 'starter', 1),
(1028, NULL, 'success', 'Admin added 1 Business tickets', 1, '2025-06-27 10:15:28', 200, 'Business', 1),
(1029, NULL, 'success', 'Admin added 1 ultimate tickets', 1, '2025-06-27 10:15:32', 200, 'ultimate', 1),
(1030, NULL, 'success', 'Admin added 1 starter tickets', 1, '2025-06-27 10:17:00', 200, 'starter', 1),
(1031, NULL, 'success', 'Admin added 1 Business tickets', 1, '2025-06-27 10:17:03', 200, 'Business', 1),
(1032, NULL, 'success', 'Admin added 1 ultimate tickets', 1, '2025-06-27 10:17:07', 200, 'ultimate', 1),
(1033, NULL, 'success', 'Admin added 5 starter tickets', 1, '2025-06-27 10:17:47', 200, 'starter', 5),
(1034, NULL, 'success', 'Admin added 1 Business tickets', 1, '2025-06-27 10:18:40', 200, 'Business', 1),
(1035, NULL, 'success', 'Admin added 1 ultimate tickets', 1, '2025-06-27 10:18:43', 200, 'ultimate', 1),
(1036, NULL, 'success', 'Admin added 1 starter tickets', 1, '2025-06-27 10:19:39', 200, 'starter', 1),
(1037, NULL, 'success', 'Admin added 3 Business tickets', 1, '2025-06-27 10:50:37', 200, 'Business', 3),
(1038, NULL, 'success', 'Admin added 1 starter tickets', 1, '2025-06-27 10:52:09', 200, 'starter', 1),
(1039, NULL, 'success', 'Admin added 5 Business tickets', 1, '2025-06-27 10:52:20', 200, 'Business', 5),
(1040, NULL, 'success', 'Admin added 5 Business tickets', 1, '2025-06-27 10:57:07', 200, 'Business', 5),
(1041, NULL, 'success', 'Admin added 5 Business tickets', 1, '2025-06-27 10:57:32', 200, 'Business', 5),
(1042, NULL, 'success', 'Admin added 5 starter tickets', 1, '2025-06-27 11:08:36', 200, 'starter', 5),
(1043, NULL, 'success', 'Admin added 10 Business tickets', 1, '2025-06-27 11:08:46', 200, 'Business', 10),
(1044, NULL, 'success', 'Admin added 5 ultimate tickets', 1, '2025-06-27 11:08:59', 200, 'ultimate', 5),
(1045, NULL, 'success', 'Real-time test: Added 1 ultimate_tickets tickets', 1, '2025-06-27 11:12:18', 200, 'ultimate_tickets', 1),
(1046, NULL, 'success', 'Admin added 5 Business tickets', 1, '2025-06-27 11:13:13', 200, 'Business', 5),
(1047, NULL, 'success', 'Admin added 5 Business tickets', 1, '2025-06-27 11:15:45', 200, 'Business', 5),
(1048, NULL, 'success', 'Admin added 5 ultimate tickets', 1, '2025-06-27 11:15:51', 200, 'ultimate', 5),
(1049, NULL, 'success', 'Admin added 1 Business tickets', 1, '2025-06-27 11:16:46', 200, 'Business', 1),
(1050, NULL, 'success', 'Admin added 10 Business tickets', 1, '2025-06-27 11:21:34', 200, 'Business', 10),
(1051, NULL, 'success', 'Admin added 5 ultimate tickets', 1, '2025-06-27 11:21:41', 200, 'ultimate', 5),
(1052, NULL, 'success', 'Admin added 10 Business tickets', 1, '2025-06-27 11:26:26', 200, 'Business', 10),
(1053, NULL, 'success', 'Admin added 1 ultimate tickets', 1, '2025-06-27 11:26:31', 200, 'ultimate', 1),
(1054, NULL, 'fail', 'Failed to create ultimate ticket - API sync failed: Connection Error: cURL error 28: Operation timed out after 30003 milliseconds with 0 bytes received (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://dev-sgsg-tktapi.appika.com/graphql', NULL, '2025-06-27 17:34:55', 203, 'ultimate', 0),
(1055, NULL, 'fail', 'Failed to create ultimate ticket - API sync failed: Connection Error: cURL error 28: Operation timed out after 30006 milliseconds with 0 bytes received (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://dev-sgsg-tktapi.appika.com/graphql', NULL, '2025-06-27 17:39:40', 203, 'ultimate', 0),
(1056, NULL, 'fail', 'Failed to create ultimate ticket - API sync failed: Connection Error: cURL error 28: Operation timed out after 30002 milliseconds with 0 bytes received (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://dev-sgsg-tktapi.appika.com/graphql', NULL, '2025-06-27 18:48:33', 203, 'ultimate', 0),
(1057, NULL, 'fail', 'Failed to create premium ticket - API sync failed: GraphQL Errors: Field \"createTicketByContact\" argument \"type\" of type \"Int!\" is required but not provided.', NULL, '2025-06-29 07:46:20', 200, 'premium', 0),
(1058, NULL, 'fail', 'Failed to create starter ticket - API sync failed: GraphQL Errors: Field \"createTicketByContact\" argument \"type\" of type \"Int!\" is required but not provided.', NULL, '2025-06-29 07:54:36', 204, 'starter', 0),
(1059, NULL, 'fail', 'Failed to create starter ticket - API sync failed: GraphQL Errors: Unknown argument \"fname\" on field \"createTicketByContact\" of type \"Mutation\". Did you mean \"name\"?, Field \"createTicketByContact\" argument \"name\" of type \"String!\" is required but not provided., Field \"createTicketByContact\" argument \"type\" of type \"Int!\" is required but not provided.', NULL, '2025-06-29 07:57:57', 204, 'starter', 0),
(1060, NULL, 'fail', 'Failed to create starter ticket - API sync failed: GraphQL Errors: Unknown argument \"fname\" on field \"createTicketByContact\" of type \"Mutation\". Did you mean \"name\"?, Unknown argument \"lname\" on field \"createTicketByContact\" of type \"Mutation\". Did you mean \"name\"?, Field \"createTicketByContact\" argument \"name\" of type \"String!\" is required but not provided., Field \"createTicketByContact\" argument \"type\" of type \"Int!\" is required but not provided.', NULL, '2025-06-29 08:01:29', 204, 'starter', 0),
(1061, NULL, 'fail', 'Failed to create starter ticket - API sync failed: GraphQL Errors: Variable \"$name\" of non-null type \"String!\" must not be null.', NULL, '2025-06-29 08:21:45', 204, 'starter', 0),
(1062, NULL, 'fail', 'Failed to create ultimate ticket - API sync failed: GraphQL Errors: Internal server error', NULL, '2025-06-29 09:11:35', 208, 'ultimate', 0),
(1063, NULL, 'fail', 'Failed to create ultimate ticket - API sync failed: GraphQL Errors: Internal server error', NULL, '2025-06-29 09:11:52', 208, 'ultimate', 0),
(1064, NULL, 'fail', 'Failed to create starter ticket - API sync failed: GraphQL Errors: Internal server error', NULL, '2025-06-29 09:12:11', 208, 'starter', 0);

-- --------------------------------------------------------

--
-- Table structure for table `ticket_messages`
--

CREATE TABLE `ticket_messages` (
  `id` int(11) NOT NULL,
  `appika_ticket_id` int(11) NOT NULL COMMENT 'Appika ticket ID (286, 1, etc.)',
  `local_ticket_id` int(11) DEFAULT NULL COMMENT 'Local support_tickets.id if exists',
  `user_id` int(11) NOT NULL COMMENT 'References user.id',
  `admin_id` int(11) DEFAULT NULL COMMENT 'Admin user ID for admin messages',
  `message` text NOT NULL,
  `message_type` enum('description','user','admin','system') NOT NULL DEFAULT 'user',
  `sender_name` varchar(100) DEFAULT NULL,
  `sender_email` varchar(255) DEFAULT NULL,
  `is_internal` tinyint(1) DEFAULT 0 COMMENT 'Internal admin notes',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `appika_synced` tinyint(1) DEFAULT 0 COMMENT 'Sent to Appika API',
  `appika_sync_at` timestamp NULL DEFAULT NULL,
  `status` enum('active','deleted','hidden') DEFAULT 'active'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `ticket_messages`
--

INSERT INTO `ticket_messages` (`id`, `appika_ticket_id`, `local_ticket_id`, `user_id`, `admin_id`, `message`, `message_type`, `sender_name`, `sender_email`, `is_internal`, `created_at`, `updated_at`, `appika_synced`, `appika_sync_at`, `status`) VALUES
(1, 286, NULL, 200, NULL, 'Hi admin', 'user', 'HC200', '<EMAIL>', 0, '2025-06-28 13:26:57', '2025-06-28 13:26:58', 1, '2025-06-28 13:26:58', 'active'),
(7, 286, NULL, 200, NULL, 'Hi', 'user', 'HC200', '<EMAIL>', 0, '2025-06-28 14:25:09', '2025-06-28 14:25:10', 1, '2025-06-28 14:25:10', 'active');

-- --------------------------------------------------------

--
-- Table structure for table `ticket_ratings`
--

CREATE TABLE `ticket_ratings` (
  `id` int(11) NOT NULL,
  `ticket_id` int(11) DEFAULT NULL,
  `user_id` int(11) NOT NULL,
  `rating` int(11) NOT NULL CHECK (`rating` >= 1 and `rating` <= 5),
  `comment` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `appika_ticket_id` int(11) DEFAULT NULL,
  `is_appika_ticket` tinyint(1) DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

--
-- Dumping data for table `ticket_ratings`
--

INSERT INTO `ticket_ratings` (`id`, `ticket_id`, `user_id`, `rating`, `comment`, `created_at`, `updated_at`, `appika_ticket_id`, `is_appika_ticket`) VALUES
(8, NULL, 209, 5, 'This is good!', '2025-06-29 12:29:57', '2025-06-29 12:38:54', 300, 1);

-- --------------------------------------------------------

--
-- Table structure for table `ticket_replies`
--

CREATE TABLE `ticket_replies` (
  `id` int(11) NOT NULL,
  `ticket_id` int(11) NOT NULL,
  `sender_type` enum('user','admin') NOT NULL,
  `sender_id` int(11) NOT NULL,
  `message` text NOT NULL,
  `created_at` datetime DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `ticket_usage`
--

CREATE TABLE `ticket_usage` (
  `id` int(11) NOT NULL,
  `purchase_id` int(11) NOT NULL,
  `support_ticket_id` int(11) NOT NULL,
  `used_at` datetime DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `ticket_usage_log`
--

CREATE TABLE `ticket_usage_log` (
  `id` int(11) NOT NULL,
  `username` varchar(100) NOT NULL,
  `ticket_type` varchar(50) NOT NULL,
  `quantity_used` int(11) NOT NULL,
  `purchase_id` int(11) NOT NULL,
  `transaction_id` varchar(100) DEFAULT NULL,
  `used_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `ticket_usage_log`
--

INSERT INTO `ticket_usage_log` (`id`, `username`, `ticket_type`, `quantity_used`, `purchase_id`, `transaction_id`, `used_at`) VALUES
(1, 'HC185', 'starter', 1, 0, 'cs_test_b1IdPP6DGQSej9LERdD41kORnjHfOAXtdE08j2JFj8zHYcuPeYlfDbKUtk', '2025-06-23 09:40:18'),
(2, 'HC186', 'starter', 1, 0, 'pi_3RdNvcEJRyUMDOj50S1gr1lC', '2025-06-24 03:54:07'),
(3, 'HC186', 'starter', 1, 0, 'pi_3RdO6zEJRyUMDOj51aVe0UIz', '2025-06-24 04:06:08'),
(4, 'HC186', 'starter', 1, 0, 'pi_3RdODREJRyUMDOj51GGUJqxn', '2025-06-24 04:14:54'),
(5, 'HC186', 'starter', 1, 0, 'pi_3RdOKvEJRyUMDOj51Y5VdZhy', '2025-06-24 04:20:22'),
(6, 'HC186', 'ultimate', 1, 0, 'pi_3RdOOJEJRyUMDOj50bKb8TEN', '2025-06-24 04:23:52'),
(7, 'HC186', 'starter', 1, 0, 'pi_3RdQedEJRyUMDOj51qPjzfBq', '2025-06-24 06:50:23'),
(8, 'test', 'starter', 1, 0, 'pi_3RdlKwEJRyUMDOj50qgBh5sV', '2025-06-25 07:05:11'),
(9, 'test', 'starter', 1, 0, 'pi_3RdlKwEJRyUMDOj50qgBh5sV', '2025-06-25 09:34:24'),
(10, 'test', 'starter', 1, 0, 'pi_3RdlKwEJRyUMDOj50qgBh5sV', '2025-06-25 09:34:40'),
(11, 'test', 'starter', 1, 0, 'pi_3RdlKwEJRyUMDOj50qgBh5sV', '2025-06-25 09:38:52'),
(12, 'test', 'starter', 1, 0, 'pi_3RdlKwEJRyUMDOj50qgBh5sV', '2025-06-25 10:09:44'),
(13, 'test', 'starter', 1, 0, 'pi_3Rdn4wEJRyUMDOj502LSwUIv', '2025-06-25 10:30:37'),
(14, 'test', 'starter', 1, 0, 'pi_3Rdn4wEJRyUMDOj502LSwUIv', '2025-06-25 11:22:25'),
(15, 'test', 'starter', 1, 0, 'pi_3Rdn4wEJRyUMDOj502LSwUIv', '2025-06-25 11:28:14'),
(16, 'test', 'starter', 1, 0, 'pi_3Rdn4wEJRyUMDOj502LSwUIv', '2025-06-25 11:28:53'),
(17, 'test', 'starter', 1, 0, 'pi_3Rdn4wEJRyUMDOj502LSwUIv', '2025-06-25 11:32:49'),
(18, 'test', 'starter', 1, 0, 'pi_3RdnYdEJRyUMDOj51FdTupOp', '2025-06-25 11:34:13'),
(19, 'test', 'starter', 1, 0, 'pi_3RdnYdEJRyUMDOj51FdTupOp', '2025-06-25 11:38:49'),
(20, 'test', 'starter', 1, 0, 'pi_3RdnYdEJRyUMDOj51FdTupOp', '2025-06-25 11:41:40'),
(21, 'test', 'ultimate', 1, 0, 'pi_3RdrjFEJRyUMDOj51NwFa123', '2025-06-25 11:43:14'),
(22, 'test', 'premium', 1, 0, 'pi_3RdnOkEJRyUMDOj51YxBgwNP', '2025-06-25 11:49:26'),
(23, 'test', 'ultimate', 1, 0, 'pi_3RdrjFEJRyUMDOj51NwFa123', '2025-06-26 02:02:21'),
(24, 'test', 'premium', 1, 0, 'pi_3RdnOkEJRyUMDOj51YxBgwNP', '2025-06-26 02:40:57'),
(25, 'test', 'ultimate', 1, 0, 'pi_3RdrjFEJRyUMDOj51NwFa123', '2025-06-26 02:43:13'),
(26, 'test', 'premium', 1, 0, 'pi_3RdnOkEJRyUMDOj51YxBgwNP', '2025-06-26 02:46:04'),
(27, 'test', 'premium', 1, 0, 'pi_3RdnOkEJRyUMDOj51YxBgwNP', '2025-06-26 02:54:18'),
(28, 'HC194', 'starter', 1, 0, 'cs_test_a1qeFHXL2mjE5NBAHTmi6XRkJ6nD0cYZgG1fwwKFOYJY7d1DQzTe23enpR', '2025-06-26 06:38:50'),
(29, 'HC194', 'starter', 1, 0, 'cs_test_a1qeFHXL2mjE5NBAHTmi6XRkJ6nD0cYZgG1fwwKFOYJY7d1DQzTe23enpR', '2025-06-26 07:13:16'),
(30, 'HC194', 'starter', 1, 0, 'cs_test_a1qeFHXL2mjE5NBAHTmi6XRkJ6nD0cYZgG1fwwKFOYJY7d1DQzTe23enpR', '2025-06-26 08:30:40'),
(31, 'HC199', 'ultimate', 1, 0, 'cs_test_b1xgKIXrJSOtnO1SHSCQaIprcWEi2pz5ZXbsn1nho7cobwDzFFHjLtXvWs', '2025-06-27 03:12:19'),
(32, 'HC199', 'ultimate', 1, 0, 'cs_test_b1xgKIXrJSOtnO1SHSCQaIprcWEi2pz5ZXbsn1nho7cobwDzFFHjLtXvWs', '2025-06-27 03:37:23'),
(33, 'HC199', 'premium', 1, 0, 'cs_test_b1xgKIXrJSOtnO1SHSCQaIprcWEi2pz5ZXbsn1nho7cobwDzFFHjLtXvWs', '2025-06-27 03:43:55'),
(34, 'HC199', 'premium', 1, 0, 'cs_test_b1xgKIXrJSOtnO1SHSCQaIprcWEi2pz5ZXbsn1nho7cobwDzFFHjLtXvWs', '2025-06-27 03:52:01'),
(35, 'HC199', 'starter', 1, 0, 'cs_test_b1xgKIXrJSOtnO1SHSCQaIprcWEi2pz5ZXbsn1nho7cobwDzFFHjLtXvWs', '2025-06-27 04:32:31'),
(36, 'HC199', 'starter', 1, 0, 'cs_test_b1xgKIXrJSOtnO1SHSCQaIprcWEi2pz5ZXbsn1nho7cobwDzFFHjLtXvWs', '2025-06-27 04:40:49'),
(37, 'HC199', 'starter', 1, 0, 'cs_test_b1xgKIXrJSOtnO1SHSCQaIprcWEi2pz5ZXbsn1nho7cobwDzFFHjLtXvWs', '2025-06-27 06:04:52'),
(38, 'HC199', 'premium', 1, 0, 'cs_test_b1xgKIXrJSOtnO1SHSCQaIprcWEi2pz5ZXbsn1nho7cobwDzFFHjLtXvWs', '2025-06-27 06:12:56'),
(39, 'HC199', 'ultimate', 1, 0, 'cs_test_b1xgKIXrJSOtnO1SHSCQaIprcWEi2pz5ZXbsn1nho7cobwDzFFHjLtXvWs', '2025-06-27 06:13:23'),
(40, 'HC200', 'starter', 1, 0, 'cs_test_a1HZQeo2DtWx5S2RkAOawi39GMDkjXA2BMsr2iXnOsPOVBbxB1qfdhJMPh', '2025-06-27 06:28:21'),
(41, 'HC200', 'starter', 1, 0, 'cs_test_a1HZQeo2DtWx5S2RkAOawi39GMDkjXA2BMsr2iXnOsPOVBbxB1qfdhJMPh', '2025-06-27 07:38:27'),
(42, 'HC200', 'starter', 1, 0, 'cs_test_a1HZQeo2DtWx5S2RkAOawi39GMDkjXA2BMsr2iXnOsPOVBbxB1qfdhJMPh', '2025-06-27 09:30:28'),
(43, 'HC204', 'starter', 1, 0, 'cs_test_b1e7r7CFacI0XRT7RYbOSkoxdBvuysmgvlFbb403dJUBQewTN3J3VdSYxg', '2025-06-29 08:06:14'),
(44, 'HC204', 'premium', 1, 0, 'cs_test_b1e7r7CFacI0XRT7RYbOSkoxdBvuysmgvlFbb403dJUBQewTN3J3VdSYxg', '2025-06-29 08:16:00'),
(45, 'HC204', 'starter', 1, 0, 'cs_test_b1e7r7CFacI0XRT7RYbOSkoxdBvuysmgvlFbb403dJUBQewTN3J3VdSYxg', '2025-06-29 08:17:20'),
(46, 'HC204', 'starter', 1, 0, 'cs_test_b1e7r7CFacI0XRT7RYbOSkoxdBvuysmgvlFbb403dJUBQewTN3J3VdSYxg', '2025-06-29 08:18:13'),
(47, 'HC205', 'starter', 1, 0, 'cs_test_b1wC8zJYSIVaSx1QO2AjVvndl577yDojUKzo9sc29PijNljvqwcdszLkq7', '2025-06-29 08:27:35'),
(48, 'HC205', 'starter', 1, 0, 'cs_test_b1wC8zJYSIVaSx1QO2AjVvndl577yDojUKzo9sc29PijNljvqwcdszLkq7', '2025-06-29 08:42:32'),
(49, 'HC206', 'starter', 1, 0, 'cs_test_a1Xyv7IPv6zPFXBv4y5JPsnM96I8yeU6I6iI5WRQbdQK7o0n5SX3yJVkm7', '2025-06-29 08:47:54'),
(50, 'HC207', 'starter', 1, 0, 'cs_test_b1WTiSX9wQywEEFUunsc7J23BQnobc7FqqtR9oNLCGzFjbi4zlUAVFf7VS', '2025-06-29 08:58:56'),
(51, 'HC209', 'starter', 1, 0, 'cs_test_a1jetgbMVXOE6dRgRNNyP05rh7T1pNb2jrS5DxdEkdjj2ZwjV5oH63ZUOY', '2025-06-29 09:16:11'),
(52, 'HC209', 'starter', 1, 0, 'cs_test_a1jetgbMVXOE6dRgRNNyP05rh7T1pNb2jrS5DxdEkdjj2ZwjV5oH63ZUOY', '2025-06-29 12:49:16');

-- --------------------------------------------------------

--
-- Table structure for table `transactions`
--

CREATE TABLE `transactions` (
  `id` int(11) NOT NULL,
  `customer_name` varchar(50) NOT NULL,
  `customer_email` varchar(50) NOT NULL,
  `item_name` varchar(255) NOT NULL,
  `item_number` varchar(50) NOT NULL,
  `item_price` float(10,2) NOT NULL,
  `item_price_currency` varchar(10) NOT NULL,
  `paid_amount` float(10,2) NOT NULL,
  `paid_amount_currency` varchar(10) NOT NULL,
  `txn_id` varchar(50) NOT NULL,
  `payment_status` varchar(25) NOT NULL,
  `stripe_checkout_session_id` varchar(100) DEFAULT NULL,
  `created` datetime NOT NULL,
  `modified` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

--
-- Dumping data for table `transactions`
--

INSERT INTO `transactions` (`id`, `customer_name`, `customer_email`, `item_name`, `item_number`, `item_price`, `item_price_currency`, `paid_amount`, `paid_amount_currency`, `txn_id`, `payment_status`, `stripe_checkout_session_id`, `created`, `modified`) VALUES
(1, 'demo user', '<EMAIL>', 'Codex Demo Product', '12345', 55.00, 'usd', 55.00, 'usd', 'pi_3PdqWbRwRukogfMz14UkK5zJ', 'succeeded', 'cs_test_a1ghH3KXHBTtIdMM88E5V3Df1cVKrUYMT2uXOZQw1gwUV1XXMmT3Rmg3kh', '2024-07-18 16:22:02', '2024-07-18 16:22:02');

-- --------------------------------------------------------

--
-- Table structure for table `user`
--

CREATE TABLE `user` (
  `id` int(11) NOT NULL,
  `appika_id` varchar(10) NOT NULL,
  `username` varchar(100) NOT NULL,
  `email` varchar(100) NOT NULL,
  `password` varchar(100) NOT NULL,
  `starter_tickets` int(11) NOT NULL DEFAULT 0,
  `premium_tickets` int(11) NOT NULL DEFAULT 0,
  `ultimate_tickets` int(11) NOT NULL,
  `tell` varchar(100) NOT NULL,
  `company_name` varchar(100) NOT NULL DEFAULT '',
  `tax_id` varchar(20) NOT NULL DEFAULT '',
  `first_name` varchar(50) NOT NULL,
  `address` varchar(250) NOT NULL,
  `address2` varchar(255) NOT NULL,
  `district` varchar(100) NOT NULL,
  `city` varchar(100) NOT NULL,
  `state` varchar(30) NOT NULL,
  `postal_code` varchar(10) NOT NULL,
  `registration_time` datetime DEFAULT current_timestamp(),
  `country` varchar(100) NOT NULL,
  `timezone` varchar(50) DEFAULT NULL,
  `stripe_customer_id` varchar(255) DEFAULT NULL,
  `appika_contact_id` int(11) DEFAULT NULL,
  `appika_customer_id` int(11) DEFAULT NULL COMMENT 'Actual Appika customer ID (e.g., 213)',
  `last_appika_sync` timestamp NULL DEFAULT NULL COMMENT 'Last time this user was synced from Appika',
  `appika_updated_at` timestamp NULL DEFAULT NULL COMMENT 'When this user was last updated FROM Appika',
  `appika_update_source` varchar(50) DEFAULT NULL COMMENT 'Source of last Appika update (admin_name, system, etc)',
  `updated_at` timestamp NULL DEFAULT NULL ON UPDATE current_timestamp() COMMENT 'When this user was last updated locally',
  `needs_appika_sync` tinyint(1) DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

--
-- Dumping data for table `user`
--

INSERT INTO `user` (`id`, `appika_id`, `username`, `email`, `password`, `starter_tickets`, `premium_tickets`, `ultimate_tickets`, `tell`, `company_name`, `tax_id`, `first_name`, `address`, `address2`, `district`, `city`, `state`, `postal_code`, `registration_time`, `country`, `timezone`, `stripe_customer_id`, `appika_contact_id`, `appika_customer_id`, `last_appika_sync`, `appika_updated_at`, `appika_update_source`, `updated_at`, `needs_appika_sync`) VALUES
(1, 'HI00001', 'test', '<EMAIL>', '$2y$10$G7YxHDjrulgBMXhb47RBGO0WI4X1rBw9m0T.kgvI4aTV1Oo.HfVx6', 2, 1, 2, '0832456987', '', '', 'wat', 'Onnut72, Bangkok, thailand', 'Onnut72, Bangkok, thailand', 'prawet', 'Bangkok', 'Bangkok', '10250', NULL, 'Thailand', 'Asia/Bangkok', 'cus_SMXaQ7Dq3hiylU', NULL, NULL, NULL, NULL, NULL, '2025-06-26 02:54:18', 0),
(2, 'HI00002', 'Aeyyyy', '<EMAIL>', '81dc9bdb52d04dc20036dbd8313ed055', 0, 0, 0, '', '', '', '', '', '', '', '', '', '', NULL, '', 'Asia/Thailand', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0),
(4, 'HI00004', 'test2', '<EMAIL>', '81dc9bdb52d04dc20036dbd8313ed055', 432, 0, 198, '00000', '', '', '', '5555', '', '55555', '00000', '', '00000', '2024-07-16 10:54:24', '00000', 'Asia/Thailand', NULL, NULL, NULL, NULL, NULL, NULL, '2025-06-24 02:45:43', 0),
(34, 'HI34', 'BonusHelloIT', '<EMAIL>', '81dc9bdb52d04dc20036dbd8313ed055', 98, 106, 118, '0811111111', 'Tech Space', '12345678910', 'Bonus', '1112/110-111 Sukhumvit Road, Phra Khanong, Khlong Toei, Bangkok 10110', '170 Upper Bukit Timah Rd, #02-10, Singapore 588179', 'Phra Khanong', 'Bangkok', 'Bangkok', '10250', '2025-06-17 13:57:34', 'Thailand', 'Asia/Bangkok', NULL, 34, NULL, NULL, NULL, NULL, NULL, 0),
(170, '', 'HC170', '<EMAIL>', '$2y$10$BJs7BtlLwUNlU1T4r.Hdq.cdBwUXJXcrFe0jrF1gQAVnLVkDG2wYO', 0, 0, 0, '', '', '', 'hello1', 'add1', 'add2', 'Phra Khanong', 'Bangkok', 'กรุงเทพมหานคร', '10110', '2025-06-11 07:05:05', 'TH', 'Asia/Bangkok', 'cus_STg4zGRh5gGiae', NULL, NULL, NULL, NULL, NULL, '2025-06-24 02:45:43', 0),
(171, '', 'HC171', '<EMAIL>', '$2y$10$sRDmyHVt1gVkcDM6L9BbPu.hOv2CgX/pChxrQOhFBiPd/Wtur/8Ci', 0, 0, 0, '', '', '', 'test2', 'add1', 'add2', 'Phra Khanong', 'Bangkok', 'กรุงเทพมหานคร', '10110', '2025-06-11 07:24:57', 'TH', 'Asia/Bangkok', 'cus_STgO1zfNpJOYdK', NULL, NULL, NULL, NULL, NULL, '2025-06-24 02:45:43', 0),
(172, '', 'HC172', '<EMAIL>', '$2y$10$ll7biYb1cCyOnudIPauJ0uIhNlaKVmpHnEECdIzI0Ye1PRmuxo4JW', 0, 0, 0, '', '', '', 'test3', '1112/110-111 Sukhumvit Road, Phra Khanong, Khlong Toei, Bangkok 10110', 'Onnut74', 'Bangkok', 'province', 'กรุงเทพมหานคร', '10110', '2025-06-11 07:31:10', 'TH', 'Asia/Bangkok', 'cus_STgUcm32sGz2WN', NULL, NULL, NULL, NULL, NULL, '2025-06-24 02:45:43', 0),
(173, '', 'HC173', '<EMAIL>', '$2y$10$qevXTyAtwLjLOtvgx2IM0OgAu8Dng4JJ65la1u.mEIzOwH0VbWNC2', 0, 0, 0, '', '', '', 'hello3', 'add1', 'add2', 'Phra Khanong', 'Bangkok', 'กรุงเทพมหานคร', '10110', '2025-06-11 07:36:01', 'TH', 'Asia/Bangkok', 'cus_STgZWuNwgdlU6r', NULL, NULL, NULL, NULL, NULL, '2025-06-24 02:45:43', 0),
(174, '', 'HC174', '<EMAIL>', '$2y$10$urrfAASbx/e06NtJY0otveN2KDfXBxnsDcJUUOknjCvXVocbxHKp2', 0, 0, 0, '', '', '', '<EMAIL>', '1112/110-111 Sukhumvit Road, Phra Khanong, Khlong Toei, Bangkok 10110', 'Onnut74', 'Bangkok', 'Bangkok', 'กรุงเทพมหานคร', '10110', '2025-06-11 08:18:25', 'TH', 'Asia/Bangkok', 'cus_SThG9kqR3zcuib', NULL, NULL, NULL, NULL, NULL, '2025-06-24 02:45:43', 0),
(175, '', 'HC175', '<EMAIL>', '$2y$10$3Im1K/Wd6Kgo.p0mHiZdKe09hSDqtoTIh5nxtxBYAgtQd5jHnwXA2', 0, 0, 0, '', '', '', 't30', 'test add1', 'test add2', 'Phra Khanong', 'Bangkok', 'กรุงเทพมหานคร', '10110', '2025-06-12 03:58:18', 'TH', 'Asia/Bangkok', 'cus_SU0HV4XRiChOJU', NULL, NULL, NULL, NULL, NULL, '2025-06-24 02:45:43', 0),
(176, '', 'HC176', '<EMAIL>', '$2y$10$EY0nISp/ZxyoFmH5uHi8F.iLoC2qTNjtM1djnPOyun2GBXIhhcMym', 0, 1, 0, '', '', '', 'Test22', 'add1', 'add2', 'Phra Khanong', 'Bangkok', 'กรุงเทพมหานคร', '10110', '2025-06-12 10:47:32', 'TH', 'Asia/Bangkok', 'cus_SU6t3XWyHf3vjA', NULL, NULL, NULL, NULL, NULL, '2025-06-24 02:45:43', 0),
(177, '', 'HC177', '<EMAIL>', '$2y$10$G7YxHDjrulgBMXhb47RBGO0WI4X1rBw9m0T.kgvI4aTV1Oo.HfVx6', 5, 0, 0, '', '', '', 'dev01', 'add1', 'add2', 'Phra Khanong', 'Bangkok', 'กรุงเทพมหานคร', '10110', '2025-06-13 08:18:45', 'TH', 'Asia/Bangkok', 'cus_SURiyg7sIMgxAq', NULL, NULL, NULL, NULL, NULL, '2025-06-25 04:56:42', 0),
(178, 'HI178', 'HC178', '<EMAIL>', '$2y$10$pLSY6VIGvIm8mSF3a5JGsOh7pi/3udpaYI4mZzuE1jX8.D4X07tRa', 0, 0, 0, '', '', '', 'dev02', 'add1', 'add2', 'Phra Khanong', 'Bangkok', 'กรุงเทพมหานคร', '10110', '2025-06-13 08:34:23', 'TH', NULL, 'cus_SURyLqtDEBeA7g', NULL, 219, '2025-06-24 10:37:28', NULL, NULL, '2025-06-24 10:37:28', 0),
(179, 'HI179', 'HC179_sim', '<EMAIL>', '$2y$10$HCz5ZqEJDw3H2MjfVsmouembffFGB7Tti7ky9kp2uwMOjilrrqzjq', 5, 0, 0, '999-999-', ' (Simulated) (Simulated)', 'TAX_SIM', 'dev03', 'add1', 'add2', 'Phra Khanong Sim', 'Bangkok', 'กรุงเทพมหา', '10110', '2025-06-13 08:43:48', 'TH', NULL, 'cus_SUS7weD8WOvYUf', NULL, 217, '2025-06-24 10:37:27', '2025-06-24 10:08:15', 'appika_location_sync', '2025-06-24 10:37:27', 0),
(180, 'HI180', 'HC180', '<EMAIL>', '$2y$10$sZXUS52SdtKo48H0g230dOeKFNvpcf5ICOQaaMTheT7MJ0RXiOFzy', 93, 99, 97, '999-', ' (Simulated)', 'TAX_SIM', 'wat (Updated)  (Sim)', 'add2', 'add1', 'onnut Sim', 'bangkok', 'กรุงเทพมหา', '10110', '2025-06-18 03:56:03', 'TH', 'Asia/Bangkok', 'cus_SWFbw6a6tXX3xg', NULL, 216, '2025-06-26 03:58:43', '2025-06-24 10:37:26', 'appika_bulk_sync', '2025-06-26 03:58:43', 0),
(181, 'HI181', 'HC181', '<EMAIL>', '$2y$10$pVy8oUdEzeDoAYw9gfKJXeNb9Si3Kyq9vphEPBNRUtVS8oRFyB2za', 91, 94, 94, '', '', '', 'Editedwathelloit', 'Edited add2', 'Edited add1', 'Edited onnut', 'Edited bangkok', 'Edited กรุงเทพมหานคร', '10110', '2025-06-18 06:43:06', 'TH', 'Asia/Bangkok', 'cus_SWIIg9IJX5cpjq', NULL, 213, '2025-06-26 03:58:42', NULL, NULL, '2025-06-26 03:58:42', 0),
(182, 'HI182', 'HC182_sim', '<EMAIL>', '$2y$10$dO/qEjRmwlgLGwmjuJkI3ugk1uWzsO7tu1J9WnGkx1W42ZLyLwYiy', 5, 0, 0, '999-', ' (Simulated)', 'TAX_SIM', 'Editwathelloit (Updated)', 'Edadd2 (Updated)', 'Edadd1 (Simulated)', 'Edonnut Sim', 'Edbangkok (Updated)', 'Edกรุงเทพมหานคร (Sim)', '101109', '2025-06-18 10:15:45', 'TH_SIM', 'Asia/Bangkok', 'cus_SWLjnTGY8gIbfJ', NULL, NULL, NULL, '2025-06-19 08:00:57', 'simulation', '2025-06-19 08:00:57', 0),
(183, 'HI183', 'HC183', '<EMAIL>', '$2y$10$ait3ssKHWh3xqaS6cFHIWuubSlfF.bFw./xWvn9ppXHLuq1u7Lq0.', 0, 0, 0, '', '', '', 'wat3', 'AdE2 113 onnut', 'AdE2 114 onnut', 'AdE2 onnut', 'AdE2 bangkok', 'AdE2 กรุงเทพมหานคร', '810110', '2025-06-18 10:28:43', 'TH', 'Asia/Bangkok', 'cus_SWLwtbI9SEjzII', NULL, NULL, NULL, NULL, NULL, '2025-06-24 02:45:43', 0),
(184, 'HI184', 'HC184', '<EMAIL>', '$2y$10$E5xSNXm3H84i/LjvqMrfNeJ.W9rCItfRCW6EGnoMaofD06QdslHGG', 0, 0, 0, '', '', '', 'prayat januncarn', '412 Soi bankeng', 'ban tab chang bangkok 10240', 'ban tab chang', 'bangkok', 'กรุงเทพมหา', '10110', '2025-06-20 03:39:54', 'TH', 'Asia/Bangkok', 'cus_SWzn2mMnqwxphj', NULL, 220, '2025-06-26 03:58:41', '2025-06-24 10:08:13', 'appika_location_sync', '2025-06-26 03:58:41', 0),
(185, 'HI185', 'HC185', '<EMAIL>', '$2y$10$Fppq8XVJUIMgDLijQN1VV.A7lVFvKWNdYDBZeCuDYU8Jm0kViylsK', 0, 0, 0, '02 999 9999', '', 'TAX', 'developer full name TTTTT', '412 Soi bankeng sukhumvit 48', 'ban tab chang bangkok 10240', 'ban tab chang', 'bangkok-th', 'กรุงเทพมหานคร', '10110', '2025-06-23 03:42:56', 'TH', 'Asia/Bangkok', 'cus_SY7WGdzEykwySY', NULL, 225, '2025-06-26 07:00:13', '2025-06-24 10:37:23', 'appika_bulk_sync', '2025-06-26 07:00:13', 0),
(186, 'HI186', 'HC186', '<EMAIL>', '$2y$10$UmWit.IDtLYnByXi9R.aJOW2dgza3.xTR8xvJpdllZ2hMaceWrWAq', 0, 0, 0, '', '', '', 'developerHelloit full name', '113 onnut', 'ban tab chang bangkok 10240', 'onnut', 'bangkok', 'กรุงเทพมหา', '10110', '2025-06-24 02:08:42', 'TH', 'Asia/Bangkok', 'cus_SYTESPoLS48HeT', NULL, 228, '2025-06-26 07:00:12', '2025-06-26 03:58:39', 'appika_location_sync', '2025-06-26 07:00:12', 0),
(187, '', 'HC187', '<EMAIL>', '$2y$10$D3GyM5JpPZYCmwDrF5EYd.j4e9V5S6e2PYG1FUwlkPuY/bDRncD9.', 5, 0, 0, '', '', '', 'test60 full name', 'Address1', 'Address2', 'onnut', 'bangkok', 'กรุงเทพมหานคร', '10250', '2025-06-25 04:43:02', 'TH', 'Asia/Bangkok', 'cus_SYswEOZxw5kHkn', NULL, NULL, NULL, NULL, NULL, '2025-06-25 04:52:51', 0),
(188, 'HI188', 'HC188', '<EMAIL>', '$2y$10$G7YxHDjrulgBMXhb47RBGO0WI4X1rBw9m0T.kgvI4aTV1Oo.HfVx6', 5, 0, 0, '0611111111', '', '', 'watcharapol vry', '123 Moo 5, Sukhumvit Road', 'Bangna Nuea Sub-district, Bangna District', '', 'Bangkok', 'Bangkok', '10250', '2025-06-25 05:08:47', 'TH', 'Asia/Bangkok', 'cus_SYtMMpJjTZP9de', NULL, 226, '2025-06-26 07:00:11', '2025-06-26 06:41:07', 'appika_location_sync', '2025-06-26 07:00:11', 0),
(189, '', 'HC189', '<EMAIL>', '$2y$10$gBi.mImUxbsx26rtzc9deuM2PJ9yrlNlEmQUvZtomNdsxBOKmT3lW', 5, 0, 0, '', '', '', 'plssendemail', 'Address1', 'Address2', 'Onnut', 'bangkok', 'กรุงเทพมหานคร', '10110', '2025-06-26 03:31:24', 'TH', NULL, 'cus_SZF1EcjZvGJIlg', NULL, NULL, NULL, NULL, NULL, '2025-06-26 03:31:25', 0),
(190, 'HI190', 'HC190', '<EMAIL>', 'fafdaead3c0b786f1dc8d69861292c65', 5, 5, 0, '', '', '', 'helloitrework sohandsome', '', '', '', '', '', '', '2025-06-26 05:55:55', '', 'UTC', 'cus_SZHLZRU3o9rpx5', NULL, 229, '2025-06-26 07:00:10', '2025-06-26 06:41:06', 'appika_bulk_sync', '2025-06-26 07:00:10', 0),
(191, 'HI191', 'HC191', '<EMAIL>', '$2y$10$G7YxHDjrulgBMXhb47RBGO0WI4X1rBw9m0T.kgvI4aTV1Oo.HfVx6', 5, 5, 5, '', '', '', 'bonusHandsome', '', '', '', '', '', '', '2025-06-26 06:12:36', 'TH', 'Asia/Bangkok', 'cus_SZHcoX2RIR37Zx', NULL, 230, '2025-06-27 10:25:21', NULL, NULL, '2025-06-27 10:25:21', 0),
(192, 'HI192', 'HC192', '<EMAIL>', '$2y$10$G7YxHDjrulgBMXhb47RBGO0WI4X1rBw9m0T.kgvI4aTV1Oo.HfVx6', 5, 0, 0, '', '', '', 'bonus lastname', '', '', '', '', '', '', '2025-06-26 06:24:58', '', 'Asia/Bangkok', 'cus_SZHoAjHQnHeabv', NULL, 231, '2025-06-27 10:25:21', '2025-06-26 06:41:04', 'appika_bulk_sync', '2025-06-27 10:25:21', 0),
(193, 'HI193', 'HC193', '<EMAIL>', '$2y$10$G7YxHDjrulgBMXhb47RBGO0WI4X1rBw9m0T.kgvI4aTV1Oo.HfVx6', 0, 5, 0, '', '', '', 'bonus japanese', '', '', '', '', '', '', '2025-06-26 06:27:43', '', 'Asia/Bangkok', 'cus_SZHr013GV4B9Fl', NULL, 232, '2025-06-27 10:25:21', '2025-06-26 06:41:03', 'appika_bulk_sync', '2025-06-27 10:25:21', 0),
(194, 'HI194', 'HC194', '<EMAIL>', '$2y$10$G7YxHDjrulgBMXhb47RBGO0WI4X1rBw9m0T.kgvI4aTV1Oo.HfVx6', 2, 0, 0, '', '', '', 'bonus USA', '', '', '', '', '', '', '2025-06-26 06:36:46', 'US', 'America/New_York', 'cus_SZI0pHmwozhLMW', NULL, 233, '2025-06-27 10:25:21', '2025-06-26 07:00:05', 'appika_bulk_sync', '2025-06-27 10:25:21', 0),
(195, 'HI195', 'HC195', '<EMAIL>', '$2y$10$G7YxHDjrulgBMXhb47RBGO0WI4X1rBw9m0T.kgvI4aTV1Oo.HfVx6', 5, 0, 0, '', '', '', '', '', '', '', '', '', '', '2025-06-26 11:27:03', 'TH', 'Asia/Bangkok', 'cus_SZMgBO1aU1OWkc', NULL, 234, '2025-06-27 10:25:21', NULL, NULL, '2025-06-27 10:25:21', 0),
(196, 'HI196', 'HC196', '<EMAIL>', '$2y$10$G7YxHDjrulgBMXhb47RBGO0WI4X1rBw9m0T.kgvI4aTV1Oo.HfVx6', 0, 10, 0, '', '', '', '', '', '', '', '', '', '', '2025-06-26 11:35:27', 'TH', 'Asia/Bangkok', 'cus_SZMpGRiPjuggcv', NULL, 235, '2025-06-27 10:25:21', NULL, NULL, '2025-06-27 10:25:21', 0),
(197, 'HI197', 'HC197', '<EMAIL>', 'fdc068f431a30372ed83ab91138dc52d', 5, 0, 0, '', '', '', '', '', '', '', '', '', '', '2025-06-26 11:46:09', 'TH', 'Asia/Bangkok', 'cus_SZMzSVsJE6MO4M', NULL, 236, '2025-06-27 10:25:21', NULL, NULL, '2025-06-27 10:25:21', 0),
(198, 'HI198', 'HC198', '<EMAIL>', 'e10adc3949ba59abbe56e057f20f883e', 0, 0, 0, '', '', '', '', '', '', '', '', '', '', '2025-06-27 02:21:46', 'TH', 'Asia/Bangkok', NULL, NULL, 237, '2025-06-27 10:25:21', NULL, NULL, '2025-06-27 10:25:21', 0),
(199, 'HI199', 'HC199', '<EMAIL>', '$2y$10$G7YxHDjrulgBMXhb47RBGO0WI4X1rBw9m0T.kgvI4aTV1Oo.HfVx6', 2, 2, 2, '', 'TS', '1187987784569', '', '', '', '', '', '', '', '2025-06-27 02:30:34', 'TH', 'Asia/Bangkok', 'cus_SZbGz1pxW9Rdnz', NULL, 238, '2025-06-27 10:25:21', NULL, NULL, '2025-06-27 10:25:21', 0),
(200, 'HI200', 'HC200', '<EMAIL>', '$2y$10$G7YxHDjrulgBMXhb47RBGO0WI4X1rBw9m0T.kgvI4aTV1Oo.HfVx6', 2, 9, 1, '', 'TS21', '', '', '', '', '', '', '', '', '2025-06-27 06:25:26', 'TH', 'Asia/Bangkok', 'cus_SZf3dQ9JKCoq1w', NULL, 239, '2025-06-27 10:32:38', NULL, NULL, '2025-06-28 16:58:27', 0),
(201, 'HI201', 'HC201', '<EMAIL>', 'edf49bae1f66d280fd1f7b9900e5670d', 5, 5, 5, '', '', '', '', '', '', '', '', '', '', '2025-06-27 16:45:49', 'TH', 'Asia/Bangkok', 'cus_SZp35SkVpoiXCa', NULL, 240, NULL, NULL, NULL, '2025-06-27 16:45:52', 0),
(202, 'HI202', 'HC202', '<EMAIL>', '$2y$10$G7YxHDjrulgBMXhb47RBGO0WI4X1rBw9m0T.kgvI4aTV1Oo.HfVx6', 10, 5, 5, '', '', '', '', '', '', '', '', '', '', '2025-06-27 16:53:36', 'TH', 'Asia/Bangkok', 'cus_SZpBgEPpAxH9mK', NULL, 241, NULL, NULL, NULL, '2025-06-27 16:54:56', 0),
(204, 'HI204', 'HC204', '<EMAIL>', '$2y$10$G7YxHDjrulgBMXhb47RBGO0WI4X1rBw9m0T.kgvI4aTV1Oo.HfVx6', 3, 4, 0, '', '', '', '', '', '', '', '', '', '', '2025-06-29 07:53:41', 'TH', 'Asia/Bangkok', 'cus_SaQvdqYCYf9UNi', NULL, 242, NULL, NULL, NULL, '2025-06-29 08:24:52', 0),
(205, 'HI205', 'HC205', '<EMAIL>', '$2y$10$G7YxHDjrulgBMXhb47RBGO0WI4X1rBw9m0T.kgvI4aTV1Oo.HfVx6', 5, 4, 0, '', '', '', '', '', '', '', '', '', '', '2025-06-29 08:26:32', 'TH', 'Asia/Bangkok', 'cus_SaRSqlvuzvrlRE', NULL, 243, NULL, NULL, NULL, '2025-06-29 08:39:46', 0),
(206, 'HI206', 'HC206', '<EMAIL>', '$2y$10$G7YxHDjrulgBMXhb47RBGO0WI4X1rBw9m0T.kgvI4aTV1Oo.HfVx6', 5, 0, 0, '', '', '', '', '', '', '', '', '', '', '2025-06-29 08:47:14', 'TH', 'Asia/Bangkok', 'cus_SaRmS7rkKklRLS', NULL, 244, NULL, NULL, NULL, '2025-06-29 08:47:27', 0),
(207, 'HI207', 'HC207', '<EMAIL>', '$2y$10$G7YxHDjrulgBMXhb47RBGO0WI4X1rBw9m0T.kgvI4aTV1Oo.HfVx6', 4, 5, 5, '', '', '', '', '', '', '', '', '', '', '2025-06-29 08:58:09', 'TH', 'Asia/Bangkok', 'cus_SaRxmuLoQPBEpp', NULL, 245, NULL, NULL, NULL, '2025-06-29 09:09:05', 0),
(208, 'HI208', 'HC208', '<EMAIL>', '$2y$10$G7YxHDjrulgBMXhb47RBGO0WI4X1rBw9m0T.kgvI4aTV1Oo.HfVx6', 5, 5, 5, '', '', '', '', '', '', '', '', '', '', '2025-06-29 09:10:41', 'TH', 'Asia/Bangkok', 'cus_SaSAIazEQubpQw', NULL, 246, NULL, NULL, NULL, '2025-06-29 09:10:59', 0),
(209, 'HI209', 'HC209', '<EMAIL>', '$2y$10$G7YxHDjrulgBMXhb47RBGO0WI4X1rBw9m0T.kgvI4aTV1Oo.HfVx6', 5, 0, 0, '', '', '', '', '', '', '', '', '', '', '2025-06-29 09:15:25', 'TH', 'Asia/Bangkok', 'cus_SaSFD9MYfwrqdS', NULL, 247, NULL, NULL, NULL, '2025-06-29 13:32:19', 0),
(210, 'HI210', 'HC210', '<EMAIL>', 'cd328f1351a127d16fed734231f53c26', 5, 5, 5, '', '', '', '', '', '', '', '', '', '', '2025-06-29 16:03:51', 'TH', 'Asia/Bangkok', 'cus_SaYp71FUtivRNO', NULL, 248, NULL, NULL, NULL, '2025-06-29 16:03:53', 0);

--
-- Indexes for dumped tables
--

--
-- Indexes for table `admin_logs`
--
ALTER TABLE `admin_logs`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `admin_users`
--
ALTER TABLE `admin_users`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `username` (`username`),
  ADD UNIQUE KEY `email` (`email`);

--
-- Indexes for table `appika_pending_sync`
--
ALTER TABLE `appika_pending_sync`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_status` (`status`),
  ADD KEY `idx_sync_type` (`sync_type`),
  ADD KEY `idx_created_at` (`created_at`);

--
-- Indexes for table `backup_config`
--
ALTER TABLE `backup_config`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `config_key` (`config_key`);

--
-- Indexes for table `cart`
--
ALTER TABLE `cart`
  ADD PRIMARY KEY (`cart_id`),
  ADD KEY `user_id` (`user_id`);

--
-- Indexes for table `cart_items`
--
ALTER TABLE `cart_items`
  ADD PRIMARY KEY (`id`),
  ADD KEY `cart_id` (`cart_id`),
  ADD KEY `ticket_id` (`ticket_id`);

--
-- Indexes for table `cart_sessions`
--
ALTER TABLE `cart_sessions`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `session_id` (`session_id`),
  ADD KEY `idx_session_id` (`session_id`),
  ADD KEY `idx_created_at` (`created_at`);

--
-- Indexes for table `chat_messages`
--
ALTER TABLE `chat_messages`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `customer_sync_logs`
--
ALTER TABLE `customer_sync_logs`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_created_at` (`created_at`);

--
-- Indexes for table `payment_methods`
--
ALTER TABLE `payment_methods`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `user_id` (`user_id`,`payment_method_id`);

--
-- Indexes for table `payment_temp`
--
ALTER TABLE `payment_temp`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `purchasetickets`
--
ALTER TABLE `purchasetickets`
  ADD PRIMARY KEY (`purchaseid`),
  ADD KEY `idx_purchasetickets_expiration` (`expiration_date`),
  ADD KEY `idx_purchasetickets_username_type` (`username`,`ticket_type`),
  ADD KEY `idx_purchasetickets_remaining` (`remaining_tickets`),
  ADD KEY `idx_purchasetickets_purchase_time` (`purchase_time`);

--
-- Indexes for table `support_tickets`
--
ALTER TABLE `support_tickets`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_id` (`user_id`),
  ADD KEY `assigned_admin_id` (`assigned_admin_id`),
  ADD KEY `idx_last_appika_sync` (`last_appika_sync`),
  ADD KEY `idx_appika_updated` (`appika_updated_at`),
  ADD KEY `idx_appika_id_sync` (`appika_id`,`last_appika_sync`);

--
-- Indexes for table `sync_log`
--
ALTER TABLE `sync_log`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_sync_id` (`sync_id`),
  ADD KEY `idx_created_at` (`created_at`);

--
-- Indexes for table `tickets`
--
ALTER TABLE `tickets`
  ADD PRIMARY KEY (`ticketid`);

--
-- Indexes for table `ticket_expiration_log`
--
ALTER TABLE `ticket_expiration_log`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_username` (`username`),
  ADD KEY `idx_cleanup_date` (`cleanup_date`),
  ADD KEY `idx_ticket_type` (`ticket_type`);

--
-- Indexes for table `ticket_expiration_settings`
--
ALTER TABLE `ticket_expiration_settings`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `config_key` (`config_key`),
  ADD KEY `idx_config_key` (`config_key`);

--
-- Indexes for table `ticket_logs`
--
ALTER TABLE `ticket_logs`
  ADD PRIMARY KEY (`id`),
  ADD KEY `ticket_id` (`ticket_id`),
  ADD KEY `performed_by_admin_id` (`performed_by_admin_id`),
  ADD KEY `fk_ticket_logs_user` (`user_id`);

--
-- Indexes for table `ticket_messages`
--
ALTER TABLE `ticket_messages`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_appika_ticket_id` (`appika_ticket_id`),
  ADD KEY `idx_local_ticket_id` (`local_ticket_id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_created_at` (`created_at`),
  ADD KEY `idx_message_type` (`message_type`),
  ADD KEY `idx_appika_synced` (`appika_synced`);

--
-- Indexes for table `ticket_ratings`
--
ALTER TABLE `ticket_ratings`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_ticket_user` (`ticket_id`,`user_id`),
  ADD KEY `idx_ticket_id` (`ticket_id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_rating` (`rating`),
  ADD KEY `idx_created_at` (`created_at`);

--
-- Indexes for table `ticket_replies`
--
ALTER TABLE `ticket_replies`
  ADD PRIMARY KEY (`id`),
  ADD KEY `ticket_id` (`ticket_id`);

--
-- Indexes for table `ticket_usage`
--
ALTER TABLE `ticket_usage`
  ADD PRIMARY KEY (`id`),
  ADD KEY `purchase_id` (`purchase_id`),
  ADD KEY `support_ticket_id` (`support_ticket_id`);

--
-- Indexes for table `ticket_usage_log`
--
ALTER TABLE `ticket_usage_log`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_username` (`username`),
  ADD KEY `idx_used_at` (`used_at`),
  ADD KEY `idx_purchase_id` (`purchase_id`);

--
-- Indexes for table `transactions`
--
ALTER TABLE `transactions`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `user`
--
ALTER TABLE `user`
  ADD PRIMARY KEY (`id`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `admin_logs`
--
ALTER TABLE `admin_logs`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=16;

--
-- AUTO_INCREMENT for table `admin_users`
--
ALTER TABLE `admin_users`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=9;

--
-- AUTO_INCREMENT for table `appika_pending_sync`
--
ALTER TABLE `appika_pending_sync`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `backup_config`
--
ALTER TABLE `backup_config`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `cart`
--
ALTER TABLE `cart`
  MODIFY `cart_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=170;

--
-- AUTO_INCREMENT for table `cart_items`
--
ALTER TABLE `cart_items`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=412;

--
-- AUTO_INCREMENT for table `cart_sessions`
--
ALTER TABLE `cart_sessions`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=303;

--
-- AUTO_INCREMENT for table `chat_messages`
--
ALTER TABLE `chat_messages`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=392;

--
-- AUTO_INCREMENT for table `customer_sync_logs`
--
ALTER TABLE `customer_sync_logs`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=22;

--
-- AUTO_INCREMENT for table `payment_methods`
--
ALTER TABLE `payment_methods`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=51;

--
-- AUTO_INCREMENT for table `payment_temp`
--
ALTER TABLE `payment_temp`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=117;

--
-- AUTO_INCREMENT for table `purchasetickets`
--
ALTER TABLE `purchasetickets`
  MODIFY `purchaseid` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=802;

--
-- AUTO_INCREMENT for table `support_tickets`
--
ALTER TABLE `support_tickets`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=269;

--
-- AUTO_INCREMENT for table `sync_log`
--
ALTER TABLE `sync_log`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `ticket_expiration_log`
--
ALTER TABLE `ticket_expiration_log`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=226;

--
-- AUTO_INCREMENT for table `ticket_expiration_settings`
--
ALTER TABLE `ticket_expiration_settings`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=1357;

--
-- AUTO_INCREMENT for table `ticket_logs`
--
ALTER TABLE `ticket_logs`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=1065;

--
-- AUTO_INCREMENT for table `ticket_messages`
--
ALTER TABLE `ticket_messages`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=10;

--
-- AUTO_INCREMENT for table `ticket_ratings`
--
ALTER TABLE `ticket_ratings`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=9;

--
-- AUTO_INCREMENT for table `ticket_replies`
--
ALTER TABLE `ticket_replies`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `ticket_usage`
--
ALTER TABLE `ticket_usage`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `ticket_usage_log`
--
ALTER TABLE `ticket_usage_log`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=53;

--
-- AUTO_INCREMENT for table `transactions`
--
ALTER TABLE `transactions`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `user`
--
ALTER TABLE `user`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=211;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `sync_log`
--
ALTER TABLE `sync_log`
  ADD CONSTRAINT `sync_log_ibfk_1` FOREIGN KEY (`sync_id`) REFERENCES `appika_pending_sync` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `ticket_messages`
--
ALTER TABLE `ticket_messages`
  ADD CONSTRAINT `fk_ticket_messages_local_ticket` FOREIGN KEY (`local_ticket_id`) REFERENCES `support_tickets` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fk_ticket_messages_user` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `ticket_ratings`
--
ALTER TABLE `ticket_ratings`
  ADD CONSTRAINT `ticket_ratings_ibfk_1` FOREIGN KEY (`ticket_id`) REFERENCES `support_tickets` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `ticket_ratings_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE CASCADE;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
