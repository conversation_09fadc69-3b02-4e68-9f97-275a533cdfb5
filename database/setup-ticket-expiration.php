<?php
/**
 * Database Setup for Ticket Expiration System
 * Run this script to initialize all necessary database tables and columns
 */

include('../functions/server.php');

echo "<h2>Setting up Ticket Expiration System Database</h2>";

$errors = [];
$success = [];

// 1. Check table structure
echo "<h3>1. Checking purchasetickets table structure...</h3>";

// Check if table has any auto-increment column
$check_auto = "SHOW COLUMNS FROM purchasetickets WHERE Extra LIKE '%auto_increment%'";
$result_auto = mysqli_query($conn, $check_auto);

if (mysqli_num_rows($result_auto) > 0) {
    $auto_column = mysqli_fetch_assoc($result_auto);
    $success[] = "✅ Table already has auto-increment column: " . $auto_column['Field'];
} else {
    $success[] = "✅ Table structure checked - no auto-increment column found";
}

// 2. Add expiration_date column to purchasetickets table
echo "<h3>2. Adding expiration_date column to purchasetickets table...</h3>";

// Check if expiration_date column exists
$check_columns = "SHOW COLUMNS FROM purchasetickets LIKE 'expiration_date'";
$result = mysqli_query($conn, $check_columns);

if (mysqli_num_rows($result) == 0) {
    // Column doesn't exist, add it
    $add_expiration_column = "ALTER TABLE purchasetickets
                             ADD COLUMN expiration_date DATETIME NULL AFTER purchase_time";

    if (mysqli_query($conn, $add_expiration_column)) {
        $success[] = "✅ Added expiration_date column to purchasetickets table";
    } else {
        $errors[] = "❌ Failed to add expiration_date column: " . mysqli_error($conn);
    }
} else {
    $success[] = "✅ expiration_date column already exists in purchasetickets table";
}

// Check if expired_at column exists
$check_expired_at = "SHOW COLUMNS FROM purchasetickets LIKE 'expired_at'";
$result2 = mysqli_query($conn, $check_expired_at);

if (mysqli_num_rows($result2) == 0) {
    // Column doesn't exist, add it
    $add_expired_at_column = "ALTER TABLE purchasetickets
                             ADD COLUMN expired_at TIMESTAMP NULL AFTER expiration_date";

    if (mysqli_query($conn, $add_expired_at_column)) {
        $success[] = "✅ Added expired_at column to purchasetickets table";
    } else {
        $errors[] = "❌ Failed to add expired_at column: " . mysqli_error($conn);
    }
} else {
    $success[] = "✅ expired_at column already exists in purchasetickets table";
}

// 3. Create ticket_expiration_settings table
echo "<h3>3. Creating ticket_expiration_settings table...</h3>";
$create_settings_table = "CREATE TABLE IF NOT EXISTS ticket_expiration_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    config_key VARCHAR(100) UNIQUE NOT NULL,
    config_value TEXT NOT NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_config_key (config_key)
)";

if (mysqli_query($conn, $create_settings_table)) {
    $success[] = "✅ Created ticket_expiration_settings table";
} else {
    $errors[] = "❌ Failed to create settings table: " . mysqli_error($conn);
}

// 4. Create ticket_expiration_log table
echo "<h3>4. Creating ticket_expiration_log table...</h3>";
$create_log_table = "CREATE TABLE IF NOT EXISTS ticket_expiration_log (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(100) NOT NULL,
    ticket_type VARCHAR(50) NOT NULL,
    expired_quantity INT NOT NULL,
    purchase_date DATETIME NOT NULL,
    expiration_date DATETIME NOT NULL,
    cleanup_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    transaction_id VARCHAR(100),
    INDEX idx_username (username),
    INDEX idx_cleanup_date (cleanup_date),
    INDEX idx_ticket_type (ticket_type)
)";

if (mysqli_query($conn, $create_log_table)) {
    $success[] = "✅ Created ticket_expiration_log table";
} else {
    $errors[] = "❌ Failed to create log table: " . mysqli_error($conn);
}

// 5. Create ticket_usage_log table
echo "<h3>5. Creating ticket_usage_log table...</h3>";
$create_usage_table = "CREATE TABLE IF NOT EXISTS ticket_usage_log (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(100) NOT NULL,
    ticket_type VARCHAR(50) NOT NULL,
    quantity_used INT NOT NULL,
    transaction_id VARCHAR(100),
    used_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_username (username),
    INDEX idx_used_at (used_at),
    INDEX idx_transaction_id (transaction_id)
)";

if (mysqli_query($conn, $create_usage_table)) {
    $success[] = "✅ Created ticket_usage_log table";
} else {
    $errors[] = "❌ Failed to create usage log table: " . mysqli_error($conn);
}

// 6. Insert default configuration
echo "<h3>6. Setting up default configuration...</h3>";
$default_config = [
    'ticket_lifetime_months' => '12',
    'auto_cleanup_enabled' => 'true',
    'cleanup_frequency_days' => '7',
    'warning_period_days' => '30',
    'user_notifications_enabled' => 'true',
    'admin_notification_email' => '<EMAIL>',
    'system_enabled' => 'true',
    'debug_mode' => 'false'
];

foreach ($default_config as $key => $value) {
    $insert_config = "INSERT INTO ticket_expiration_settings (config_key, config_value) 
                     VALUES (?, ?) 
                     ON DUPLICATE KEY UPDATE 
                     config_value = IF(config_value IS NULL OR config_value = '', VALUES(config_value), config_value)";
    
    $stmt = mysqli_prepare($conn, $insert_config);
    mysqli_stmt_bind_param($stmt, 'ss', $key, $value);
    
    if (mysqli_stmt_execute($stmt)) {
        $success[] = "✅ Set default configuration for: $key";
    } else {
        $errors[] = "❌ Failed to set configuration for $key: " . mysqli_stmt_error($stmt);
    }
    mysqli_stmt_close($stmt);
}

// 7. Update existing tickets with expiration dates
echo "<h3>7. Updating existing tickets with expiration dates...</h3>";
$update_existing = "UPDATE purchasetickets 
                    SET expiration_date = DATE_ADD(purchase_time, INTERVAL 12 MONTH) 
                    WHERE expiration_date IS NULL";

if (mysqli_query($conn, $update_existing)) {
    $affected = mysqli_affected_rows($conn);
    $success[] = "✅ Updated expiration dates for $affected existing ticket records";
} else {
    $errors[] = "❌ Failed to update existing tickets: " . mysqli_error($conn);
}

// 8. Create indexes for better performance
echo "<h3>8. Creating performance indexes...</h3>";
$indexes = [
    "CREATE INDEX IF NOT EXISTS idx_purchasetickets_expiration ON purchasetickets (expiration_date)",
    "CREATE INDEX IF NOT EXISTS idx_purchasetickets_username_type ON purchasetickets (username, ticket_type)",
    "CREATE INDEX IF NOT EXISTS idx_purchasetickets_remaining ON purchasetickets (remaining_tickets)",
    "CREATE INDEX IF NOT EXISTS idx_purchasetickets_purchase_time ON purchasetickets (purchase_time)"
];

foreach ($indexes as $index_sql) {
    if (mysqli_query($conn, $index_sql)) {
        $success[] = "✅ Created performance index";
    } else {
        if (strpos(mysqli_error($conn), 'Duplicate key name') === false) {
            $errors[] = "❌ Failed to create index: " . mysqli_error($conn);
        }
    }
}

// 9. Test the system
echo "<h3>9. Testing the system...</h3>";
$test_query = "SELECT 
    COUNT(*) as total_tickets,
    SUM(remaining_tickets) as total_remaining,
    COUNT(CASE WHEN expiration_date < NOW() THEN 1 END) as expired_count,
    COUNT(CASE WHEN expiration_date > NOW() THEN 1 END) as active_count
FROM purchasetickets";

$test_result = mysqli_query($conn, $test_query);
if ($test_result) {
    $test_data = mysqli_fetch_assoc($test_result);
    $success[] = "✅ System test passed - Found {$test_data['total_tickets']} total ticket records";
    $success[] = "✅ Active tickets: {$test_data['active_count']}, Expired: {$test_data['expired_count']}";
} else {
    $errors[] = "❌ System test failed: " . mysqli_error($conn);
}

// Display results
echo "<hr>";
echo "<h2>Setup Results</h2>";

if (!empty($success)) {
    echo "<div style='background: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>✅ Successful Operations:</h4>";
    echo "<ul>";
    foreach ($success as $msg) {
        echo "<li>$msg</li>";
    }
    echo "</ul>";
    echo "</div>";
}

if (!empty($errors)) {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>❌ Errors:</h4>";
    echo "<ul>";
    foreach ($errors as $msg) {
        echo "<li>$msg</li>";
    }
    echo "</ul>";
    echo "</div>";
}

if (empty($errors)) {
    echo "<div style='background: #d1ecf1; color: #0c5460; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>🎉 Setup Complete!</h4>";
    echo "<p>The ticket expiration system has been successfully set up. You can now:</p>";
    echo "<ul>";
    echo "<li><a href='../merlion/ticket-expiration-manager.php'>Access the Admin Panel</a></li>";
    echo "<li>Configure expiration settings</li>";
    echo "<li>Monitor expired tickets</li>";
    echo "<li>Run manual cleanups</li>";
    echo "</ul>";
    echo "</div>";
} else {
    echo "<div style='background: #fff3cd; color: #856404; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>⚠️ Setup Incomplete</h4>";
    echo "<p>Please fix the errors above and run the setup again.</p>";
    echo "</div>";
}

echo "<hr>";
echo "<p><a href='../merlion/admin-tickets.php'>← Back to Admin Panel</a></p>";

// Close connection
mysqli_close($conn);
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 1000px;
    margin: 20px auto;
    padding: 20px;
    background-color: #f8f9fa;
}

h2, h3 {
    color: #333;
    border-bottom: 2px solid #007bff;
    padding-bottom: 5px;
}

ul {
    margin: 10px 0;
}

li {
    margin: 5px 0;
}

a {
    color: #007bff;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}

hr {
    margin: 30px 0;
    border: none;
    border-top: 1px solid #dee2e6;
}
</style>
