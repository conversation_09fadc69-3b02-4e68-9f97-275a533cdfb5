<?php
/**
 * Database Update Script: Add Unit Fields to Ticket Expiration Configuration
 * This script adds the new unit fields (ticket_lifetime_unit, cleanup_frequency_unit, warning_period_unit)
 * to the ticket expiration configuration system.
 */

// Include database connection
require_once('../functions/server.php');

// Set content type for proper display
header('Content-Type: text/html; charset=UTF-8');

echo "<!DOCTYPE html>
<html lang='en'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Update Ticket Expiration Units - Database Update</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 5px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); }
        .success { color: #28a745; margin: 10px 0; }
        .error { color: #dc3545; margin: 10px 0; }
        .info { color: #17a2b8; margin: 10px 0; }
        h1 { color: #333; border-bottom: 2px solid #473BF0; padding-bottom: 10px; }
        h2 { color: #473BF0; margin-top: 30px; }
        .code { background: #f8f9fa; padding: 10px; border-left: 4px solid #473BF0; margin: 10px 0; font-family: monospace; }
    </style>
</head>
<body>
    <div class='container'>
        <h1>🔧 Ticket Expiration Units Database Update</h1>
        <p>This script will add the new unit configuration fields to your ticket expiration system.</p>";

$success = [];
$errors = [];

// Check database connection
if (!$conn) {
    $errors[] = "❌ Database connection failed: " . mysqli_connect_error();
    echo "<div class='error'>" . end($errors) . "</div>";
    echo "</div></body></html>";
    exit;
}

echo "<div class='success'>✅ Database connection successful</div>";

// 1. Add unit configuration fields
echo "<h2>1. Adding Unit Configuration Fields...</h2>";

$unit_configs = [
    'ticket_lifetime_unit' => 'months',
    'cleanup_frequency_unit' => 'days', 
    'warning_period_unit' => 'days'
];

foreach ($unit_configs as $key => $default_value) {
    // Check if the configuration already exists
    $check_query = "SELECT config_value FROM ticket_expiration_settings WHERE config_key = ?";
    $check_stmt = mysqli_prepare($conn, $check_query);
    mysqli_stmt_bind_param($check_stmt, 's', $key);
    mysqli_stmt_execute($check_stmt);
    $result = mysqli_stmt_get_result($check_stmt);
    
    if (mysqli_num_rows($result) > 0) {
        $success[] = "✅ Configuration '$key' already exists";
        echo "<div class='info'>ℹ️ Configuration '$key' already exists</div>";
    } else {
        // Insert the new configuration
        $insert_query = "INSERT INTO ticket_expiration_settings (config_key, config_value, created_at, updated_at) 
                        VALUES (?, ?, NOW(), NOW())";
        $insert_stmt = mysqli_prepare($conn, $insert_query);
        mysqli_stmt_bind_param($insert_stmt, 'ss', $key, $default_value);
        
        if (mysqli_stmt_execute($insert_stmt)) {
            $success[] = "✅ Added configuration '$key' with default value '$default_value'";
            echo "<div class='success'>✅ Added configuration '$key' with default value '$default_value'</div>";
        } else {
            $errors[] = "❌ Failed to add configuration '$key': " . mysqli_stmt_error($insert_stmt);
            echo "<div class='error'>❌ Failed to add configuration '$key': " . mysqli_stmt_error($insert_stmt) . "</div>";
        }
        mysqli_stmt_close($insert_stmt);
    }
    mysqli_stmt_close($check_stmt);
}

// 2. Verify all configurations
echo "<h2>2. Verifying Configuration...</h2>";

$verify_query = "SELECT config_key, config_value FROM ticket_expiration_settings ORDER BY config_key";
$verify_result = mysqli_query($conn, $verify_query);

if ($verify_result && mysqli_num_rows($verify_result) > 0) {
    echo "<div class='code'>";
    echo "<strong>Current Ticket Expiration Configuration:</strong><br>";
    while ($row = mysqli_fetch_assoc($verify_result)) {
        echo htmlspecialchars($row['config_key']) . " = " . htmlspecialchars($row['config_value']) . "<br>";
    }
    echo "</div>";
    $success[] = "✅ Configuration verification completed";
} else {
    $errors[] = "❌ Failed to verify configuration";
    echo "<div class='error'>❌ Failed to verify configuration</div>";
}

// 3. Summary
echo "<h2>3. Update Summary</h2>";

if (count($errors) > 0) {
    echo "<div class='error'><strong>❌ Errors encountered:</strong><br>";
    foreach ($errors as $error) {
        echo "• " . htmlspecialchars($error) . "<br>";
    }
    echo "</div>";
} else {
    echo "<div class='success'><strong>✅ Update completed successfully!</strong></div>";
}

if (count($success) > 0) {
    echo "<div class='success'><strong>✅ Successful operations:</strong><br>";
    foreach ($success as $msg) {
        echo "• " . htmlspecialchars($msg) . "<br>";
    }
    echo "</div>";
}

echo "<h2>4. Next Steps</h2>";
echo "<div class='info'>";
echo "• The ticket expiration manager now supports unit selection for all time-based settings<br>";
echo "• You can now select Minutes, Hours, Days, or Months for each setting<br>";
echo "• Visit the <a href='../merlion/ticket-expiration-manager.php'>Ticket Expiration Manager</a> to test the new functionality<br>";
echo "• The dropdown selections will now be saved and restored properly<br>";
echo "</div>";

echo "</div></body></html>";

// Close database connection
mysqli_close($conn);
?>
